{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton } from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport { Dropdown } from 'primereact/dropdown';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  var _filters$searchColumn;\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 搜索和排序状态\n  const [globalFilter, setGlobalFilter] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all');\n  const [filters, setFilters] = useState({});\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map((row, index) => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    // 确保每行都有唯一的id\n    return {\n      ...row,\n      id: row.id !== undefined ? row.id : index,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData - 强制清理版本\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 强制清理函数，确保数据完全干净\n    const forceCleanData = rawData => {\n      if (!Array.isArray(rawData)) {\n        console.warn('Raw data is not an array:', rawData);\n        return [];\n      }\n      const cleanedData = [];\n      rawData.forEach((row, index) => {\n        // 完全跳过无效行\n        if (!row || typeof row !== 'object' || row === null || row === undefined) {\n          console.warn(`Skipping invalid row at index ${index}:`, row);\n          return;\n        }\n\n        // 创建完全新的对象，确保没有引用问题\n        const cleanRow = {\n          id: `clean_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,\n          NO: row.NO !== undefined && row.NO !== null ? row.NO : index + 1,\n          DATE: String(row.DATE || ''),\n          'VEHICLE NO': String(row['VEHICLE NO'] || ''),\n          'RO NO': row['RO NO'] || '',\n          KM: Number(row.KM) || 0,\n          REMARKS: String(row.REMARKS || ''),\n          MAXCHECK: Number(row.MAXCHECK) || 0,\n          COMMISSION: Number(row.COMMISSION) || 0,\n          _removed: Boolean(row._removed),\n          _selected_remarks: String(row._selected_remarks || ''),\n          isTotal: Boolean(row.NO === 'TOTAL')\n        };\n        cleanedData.push(cleanRow);\n      });\n      console.log(`Cleaned ${rawData.length} input rows to ${cleanedData.length} valid rows`);\n      return cleanedData;\n    };\n    let initialData;\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      initialData = forceCleanData(savedGridData);\n    } else {\n      console.log('使用processedData初始化');\n      initialData = forceCleanData(processedData);\n    }\n\n    // 设置原始数据\n    setOriginalData([...initialData]);\n    console.log('初始化完成，最终数据行数:', initialData.length);\n    return initialData;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onCellEditComplete = e => {\n    let {\n      rowData,\n      newValue,\n      field\n    } = e;\n\n    // 阻止总计行被编辑\n    if (rowData.NO === 'TOTAL' || rowData._removed) {\n      return;\n    }\n\n    // 处理数值字段\n    if (field === 'COMMISSION' || field === 'MAXCHECK' || field === 'KM' || field === 'RO NO') {\n      if (typeof newValue === 'string') {\n        newValue = Number(newValue) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowData.id) {\n          return {\n            ...row,\n            [field]: newValue\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n      return updatedData;\n    });\n  };\n  const allowEdit = rowData => {\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"text\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this);\n  };\n  const numberEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"number\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 优化的重新编号函数\n  const renumberRows = useCallback(data => {\n    let newNumber = 1;\n    return data.map(row => {\n      if (row.NO !== 'TOTAL' && !row._removed) {\n        return {\n          ...row,\n          NO: newNumber++\n        };\n      }\n      return row;\n    });\n  }, []);\n\n  // 删除行数据 - 优化版本\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      // 使用批量更新减少渲染次数\n      const updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号和计算总计\n      const renumberedData = renumberRows(updatedData);\n      return recalculateTotal(renumberedData);\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [renumberRows, recalculateTotal, showSnackbar]);\n\n  // 恢复行数据 - 优化版本\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      // 使用批量更新减少渲染次数\n      const updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号和计算总计\n      const renumberedData = renumberRows(updatedData);\n      return recalculateTotal(renumberedData);\n    });\n    showSnackbar('行已恢复并重新编号', 'success');\n  }, [renumberRows, recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  // 强制确保数据唯一性和完整性的函数\n  const ensureUniqueIds = useCallback(data => {\n    if (!Array.isArray(data)) {\n      console.warn('Data is not an array:', data);\n      return [];\n    }\n    const validData = [];\n    const seenIds = new Set();\n    let counter = 0;\n    data.forEach((row, index) => {\n      // 完全跳过null、undefined或无效的行\n      if (!row || typeof row !== 'object' || row === null || row === undefined) {\n        console.warn(`Skipping invalid row at index ${index}:`, row);\n        return;\n      }\n\n      // 强制生成唯一ID，不依赖原有ID\n      let uniqueId;\n      do {\n        uniqueId = `data_row_${counter}_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        counter++;\n      } while (seenIds.has(uniqueId));\n      seenIds.add(uniqueId);\n\n      // 创建完全新的对象，确保没有引用问题\n      const cleanRow = {\n        id: uniqueId,\n        NO: row.NO !== undefined && row.NO !== null ? row.NO : index + 1,\n        DATE: row.DATE || '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': row['RO NO'] || '',\n        KM: row.KM || 0,\n        REMARKS: row.REMARKS || '',\n        MAXCHECK: row.MAXCHECK || 0,\n        COMMISSION: row.COMMISSION || 0,\n        _removed: Boolean(row._removed),\n        _selected_remarks: row._selected_remarks || '',\n        isTotal: Boolean(row.isTotal || row.NO === 'TOTAL')\n      };\n      validData.push(cleanRow);\n    });\n    console.log(`Processed ${data.length} input rows, created ${validData.length} valid rows`);\n    return validData;\n  }, []);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => {\n    console.log('Processing gridData:', gridData === null || gridData === void 0 ? void 0 : gridData.length, 'rows');\n\n    // 直接使用ensureUniqueIds处理数据，它已经包含了所有验证\n    const processedData = ensureUniqueIds(gridData);\n\n    // 超级严格的验证：确保每个对象都是完全有效的\n    const ultraCleanData = processedData.filter(row => {\n      try {\n        // 检查基本有效性\n        if (!row) return false;\n        if (typeof row !== 'object') return false;\n        if (row === null || row === undefined) return false;\n\n        // 检查必要属性\n        if (!row.hasOwnProperty('id')) return false;\n        if (row.id === null || row.id === undefined || row.id === '') return false;\n\n        // 检查id类型\n        if (typeof row.id !== 'string' && typeof row.id !== 'number') return false;\n\n        // 确保所有必要字段都存在\n        const requiredFields = ['NO', 'DATE', 'VEHICLE NO', 'RO NO', 'KM', 'REMARKS', 'MAXCHECK', 'COMMISSION'];\n        for (const field of requiredFields) {\n          if (!row.hasOwnProperty(field)) {\n            console.warn(`Row missing field ${field}:`, row);\n            return false;\n          }\n        }\n        return true;\n      } catch (error) {\n        console.error('Error validating row:', error, row);\n        return false;\n      }\n    });\n    console.log('Ultra clean data for DataTable:', ultraCleanData.length, 'rows');\n\n    // 最终ID唯一性检查\n    const ids = ultraCleanData.map(row => row.id);\n    const uniqueIds = new Set(ids);\n    if (ids.length !== uniqueIds.size) {\n      console.error('DUPLICATE IDs FOUND!', ids.filter((id, index) => ids.indexOf(id) !== index));\n      // 强制修复重复ID\n      ultraCleanData.forEach((row, index) => {\n        if (ids.filter(id => id === row.id).length > 1) {\n          row.id = `fixed_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n        }\n      });\n    }\n    return ultraCleanData;\n  }, [gridData, ensureUniqueIds]);\n\n  // 优化的处理函数，使用useCallback避免不必要的重新渲染\n  const handleRemarksClickMemo = useCallback((rowId, value) => {\n    handleRemarksClick(rowId, value);\n  }, [handleRemarksClick]);\n  const handleRemoveRowMemo = useCallback(id => {\n    handleRemoveRow(id);\n  }, [handleRemoveRow]);\n  const handleUndoRowMemo = useCallback(id => {\n    handleUndoRow(id);\n  }, [handleUndoRow]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        display: 'flex',\n        gap: 2,\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n        value: searchColumn,\n        options: [{\n          label: '全部列',\n          value: 'all'\n        }, {\n          label: 'NO',\n          value: 'NO'\n        }, {\n          label: 'DATE',\n          value: 'DATE'\n        }, {\n          label: 'VEHICLE NO',\n          value: 'VEHICLE NO'\n        }, {\n          label: 'RO NO',\n          value: 'RO NO'\n        }, {\n          label: 'KM',\n          value: 'KM'\n        }, {\n          label: 'REMARKS',\n          value: 'REMARKS'\n        }, {\n          label: 'HOURS',\n          value: 'MAXCHECK'\n        }, {\n          label: 'AMOUNT',\n          value: 'COMMISSION'\n        }],\n        onChange: e => {\n          setSearchColumn(e.value);\n          if (e.value === 'all') {\n            setFilters({});\n          } else {\n            setGlobalFilter('');\n          }\n        },\n        placeholder: \"\\u9009\\u62E9\\u641C\\u7D22\\u5217\",\n        style: {\n          width: '150px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: searchColumn === 'all' ? '搜索所有列' : `搜索 ${searchColumn}`,\n        variant: \"outlined\",\n        size: \"small\",\n        value: searchColumn === 'all' ? globalFilter : ((_filters$searchColumn = filters[searchColumn]) === null || _filters$searchColumn === void 0 ? void 0 : _filters$searchColumn.value) || '',\n        onChange: e => {\n          if (searchColumn === 'all') {\n            setGlobalFilter(e.target.value);\n          } else {\n            setFilters({\n              ...filters,\n              [searchColumn]: {\n                value: e.target.value,\n                matchMode: 'contains'\n              }\n            });\n          }\n        },\n        placeholder: \"\\u8F93\\u5165\\u5173\\u952E\\u8BCD\\u641C\\u7D22...\",\n        sx: {\n          width: '300px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          value: memoGridData.filter(row => row && row.id),\n          editMode: \"cell\",\n          dataKey: \"id\",\n          globalFilter: searchColumn === 'all' ? globalFilter : null,\n          filters: searchColumn !== 'all' ? filters : null,\n          sortMode: \"multiple\",\n          removableSort: true,\n          rowClassName: rowData => {\n            // 完全防御性编程\n            try {\n              if (!rowData) return '';\n              if (typeof rowData !== 'object') return '';\n              if (!rowData.hasOwnProperty('id')) return '';\n              if (rowData.id === null || rowData.id === undefined) return '';\n              if (rowData.NO === 'TOTAL') return 'total-row';\n              if (rowData._removed) return 'removed-row';\n              return '';\n            } catch (error) {\n              console.error('Error in rowClassName:', error, rowData);\n              return '';\n            }\n          },\n          className: \"custom-datatable\",\n          paginator: true,\n          rows: 100,\n          rowsPerPageOptions: [50, 100, 200, 500],\n          paginatorTemplate: \"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\",\n          currentPageReportTemplate: \"\\u663E\\u793A {first} \\u5230 {last} \\u6761\\uFF0C\\u5171 {totalRecords} \\u6761\\u8BB0\\u5F55\",\n          lazy: false,\n          scrollable: true,\n          scrollHeight: \"600px\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"NO\",\n            header: \"NO\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: Math.floor(rowData.NO)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 26\n                }, this);\n              }\n              return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n            },\n            style: {\n              width: '80px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"DATE\",\n            header: \"DATE\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: dateValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 26\n                }, this);\n              }\n              return dateValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"VEHICLE NO\",\n            header: \"VEHICLE NO\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: rowData['VEHICLE NO']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 26\n                }, this);\n              }\n              return rowData['VEHICLE NO'];\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"RO NO\",\n            header: \"RO NO\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: roValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 26\n                }, this);\n              }\n              return roValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"KM\",\n            header: \"KM\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: kmValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 26\n                }, this);\n              }\n              return kmValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"REMARKS\",\n            header: \"REMARKS\",\n            sortable: true,\n            body: rowData => {\n              if (!rowData) return '';\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                const removedRemarkText = rowData._selected_remarks || '无备注';\n                return /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  disabled: true,\n                  sx: {\n                    maxWidth: '100%',\n                    opacity: 0.6,\n                    textTransform: 'none',\n                    fontSize: '12px'\n                  },\n                  children: removedRemarkText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 21\n                }, this);\n              }\n              let remarkText = '点击选择';\n              let isSelected = false;\n              if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                remarkText = rowData._selected_remarks;\n                isSelected = true;\n              }\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: isSelected ? \"contained\" : \"outlined\",\n                size: \"small\",\n                onClick: () => handleRemarksClickMemo(rowData.id, remarkText),\n                sx: {\n                  maxWidth: '100%',\n                  textTransform: 'none',\n                  fontSize: '12px'\n                },\n                children: remarkText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '200px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"MAXCHECK\",\n            header: \"HOURS\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              const hoursValue = typeof rowData.MAXCHECK === 'number' ? rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: hoursValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 26\n                }, this);\n              }\n              return hoursValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"COMMISSION\",\n            header: \"AMOUNT\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (!rowData) return '';\n              if (rowData.NO === 'TOTAL') {\n                const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                return /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  children: totalValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this);\n              }\n              const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: amountValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 26\n                }, this);\n              }\n              return amountValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            header: \"ACTION\",\n            body: rowData => {\n              if (!rowData) return '';\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  color: \"success\",\n                  size: \"small\",\n                  startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => handleUndoRowMemo(rowData.id),\n                  sx: {\n                    fontSize: '12px',\n                    textTransform: 'none'\n                  },\n                  children: \"\\u6062\\u590D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1053,\n                  columnNumber: 21\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"error\",\n                size: \"small\",\n                startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 32\n                }, this),\n                onClick: () => handleRemoveRowMemo(rowData.id),\n                sx: {\n                  fontSize: '12px',\n                  textTransform: 'none'\n                },\n                children: \"\\u79FB\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this)]\n        }, `datatable-${Date.now()}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1122,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1091,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 692,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"PJpBpllji2epoqcOhYkIrroeb0Y=\", false, function () {\n  return [useSnackbar];\n});\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "DataTable", "Column", "InputText", "Dropdown", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "useSnackbar", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "_filters$searchColumn", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "globalFilter", "setGlobalFilter", "searchColumn", "setSearchColumn", "filters", "setFilters", "setItem", "stringify", "processedData", "map", "row", "index", "id", "undefined", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "forceCleanData", "rawData", "Array", "isArray", "warn", "cleanedData", "for<PERSON>ach", "cleanRow", "Date", "now", "Math", "random", "toString", "substring", "NO", "DATE", "String", "KM", "Number", "MAXCHECK", "COMMISSION", "Boolean", "isTotal", "push", "initialData", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "rowId", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "variant", "key", "sx", "borderRadius", "border", "borderColor", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "onCellEditComplete", "e", "rowData", "newValue", "field", "prev", "updatedData", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "allowEdit", "textEditor", "options", "type", "value", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "target", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "numberEditor", "recalculateTotal", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "renumberRows", "newNumber", "handleRemoveRow", "renumberedData", "handleUndoRow", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "ensureUniqueIds", "validData", "seenIds", "Set", "counter", "uniqueId", "has", "add", "memoGridData", "ultraCleanData", "hasOwnProperty", "requiredFields", "ids", "uniqueIds", "size", "indexOf", "handleRemarksClickMemo", "handleRemoveRowMemo", "handleUndoRowMemo", "textAlign", "py", "children", "color", "onClick", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "gap", "label", "placeholder", "matchMode", "overflow", "height", "editMode", "dataKey", "sortMode", "removableSort", "rowClassName", "className", "paginator", "rows", "rowsPerPageOptions", "paginatorTemplate", "currentPageReportTemplate", "lazy", "scrollable", "scrollHeight", "header", "sortable", "editor", "textDecoration", "dateValue", "roValue", "kmValue", "removedRemarkText", "max<PERSON><PERSON><PERSON>", "opacity", "textTransform", "fontSize", "remarkText", "isSelected", "hoursValue", "totalValue", "isNaN", "fontWeight", "amountValue", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton\n} from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport { Dropdown } from 'primereact/dropdown';\n\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n\n\n\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 搜索和排序状态\n  const [globalFilter, setGlobalFilter] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all');\n  const [filters, setFilters] = useState({});\n\n\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map((row, index) => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    // 确保每行都有唯一的id\n    return {\n      ...row,\n      id: row.id !== undefined ? row.id : index,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData - 强制清理版本\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:',\n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 强制清理函数，确保数据完全干净\n    const forceCleanData = (rawData) => {\n      if (!Array.isArray(rawData)) {\n        console.warn('Raw data is not an array:', rawData);\n        return [];\n      }\n\n      const cleanedData = [];\n      rawData.forEach((row, index) => {\n        // 完全跳过无效行\n        if (!row || typeof row !== 'object' || row === null || row === undefined) {\n          console.warn(`Skipping invalid row at index ${index}:`, row);\n          return;\n        }\n\n        // 创建完全新的对象，确保没有引用问题\n        const cleanRow = {\n          id: `clean_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,\n          NO: row.NO !== undefined && row.NO !== null ? row.NO : index + 1,\n          DATE: String(row.DATE || ''),\n          'VEHICLE NO': String(row['VEHICLE NO'] || ''),\n          'RO NO': row['RO NO'] || '',\n          KM: Number(row.KM) || 0,\n          REMARKS: String(row.REMARKS || ''),\n          MAXCHECK: Number(row.MAXCHECK) || 0,\n          COMMISSION: Number(row.COMMISSION) || 0,\n          _removed: Boolean(row._removed),\n          _selected_remarks: String(row._selected_remarks || ''),\n          isTotal: Boolean(row.NO === 'TOTAL')\n        };\n\n        cleanedData.push(cleanRow);\n      });\n\n      console.log(`Cleaned ${rawData.length} input rows to ${cleanedData.length} valid rows`);\n      return cleanedData;\n    };\n\n    let initialData;\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      initialData = forceCleanData(savedGridData);\n    } else {\n      console.log('使用processedData初始化');\n      initialData = forceCleanData(processedData);\n    }\n\n    // 设置原始数据\n    setOriginalData([...initialData]);\n\n    console.log('初始化完成，最终数据行数:', initialData.length);\n    return initialData;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onCellEditComplete = (e) => {\n    let { rowData, newValue, field } = e;\n\n    // 阻止总计行被编辑\n    if (rowData.NO === 'TOTAL' || rowData._removed) {\n      return;\n    }\n\n    // 处理数值字段\n    if (field === 'COMMISSION' || field === 'MAXCHECK' || field === 'KM' || field === 'RO NO') {\n      if (typeof newValue === 'string') {\n        newValue = Number(newValue) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowData.id) {\n          return { ...row, [field]: newValue };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData\n          .filter(row => row.NO !== 'TOTAL' && !row._removed)\n          .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n\n      return updatedData;\n    });\n  };\n\n  const allowEdit = (rowData) => {\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = (options) => {\n    return (\n      <InputText\n        type=\"text\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n  const numberEditor = (options) => {\n    return (\n      <InputText\n        type=\"number\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 优化的重新编号函数\n  const renumberRows = useCallback((data) => {\n    let newNumber = 1;\n    return data.map(row => {\n      if (row.NO !== 'TOTAL' && !row._removed) {\n        return { ...row, NO: newNumber++ };\n      }\n      return row;\n    });\n  }, []);\n\n  // 删除行数据 - 优化版本\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      // 使用批量更新减少渲染次数\n      const updatedData = prev.map(row =>\n        row.id === id ? { ...row, _removed: true } : row\n      );\n\n      // 重新编号和计算总计\n      const renumberedData = renumberRows(updatedData);\n      return recalculateTotal(renumberedData);\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [renumberRows, recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据 - 优化版本\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      // 使用批量更新减少渲染次数\n      const updatedData = prev.map(row =>\n        row.id === id ? { ...row, _removed: false } : row\n      );\n\n      // 重新编号和计算总计\n      const renumberedData = renumberRows(updatedData);\n      return recalculateTotal(renumberedData);\n    });\n\n    showSnackbar('行已恢复并重新编号', 'success');\n  }, [renumberRows, recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n\n  \n  // 强制确保数据唯一性和完整性的函数\n  const ensureUniqueIds = useCallback((data) => {\n    if (!Array.isArray(data)) {\n      console.warn('Data is not an array:', data);\n      return [];\n    }\n\n    const validData = [];\n    const seenIds = new Set();\n    let counter = 0;\n\n    data.forEach((row, index) => {\n      // 完全跳过null、undefined或无效的行\n      if (!row || typeof row !== 'object' || row === null || row === undefined) {\n        console.warn(`Skipping invalid row at index ${index}:`, row);\n        return;\n      }\n\n      // 强制生成唯一ID，不依赖原有ID\n      let uniqueId;\n      do {\n        uniqueId = `data_row_${counter}_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        counter++;\n      } while (seenIds.has(uniqueId));\n\n      seenIds.add(uniqueId);\n\n      // 创建完全新的对象，确保没有引用问题\n      const cleanRow = {\n        id: uniqueId,\n        NO: row.NO !== undefined && row.NO !== null ? row.NO : index + 1,\n        DATE: row.DATE || '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': row['RO NO'] || '',\n        KM: row.KM || 0,\n        REMARKS: row.REMARKS || '',\n        MAXCHECK: row.MAXCHECK || 0,\n        COMMISSION: row.COMMISSION || 0,\n        _removed: Boolean(row._removed),\n        _selected_remarks: row._selected_remarks || '',\n        isTotal: Boolean(row.isTotal || row.NO === 'TOTAL')\n      };\n\n      validData.push(cleanRow);\n    });\n\n    console.log(`Processed ${data.length} input rows, created ${validData.length} valid rows`);\n    return validData;\n  }, []);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => {\n    console.log('Processing gridData:', gridData?.length, 'rows');\n\n    // 直接使用ensureUniqueIds处理数据，它已经包含了所有验证\n    const processedData = ensureUniqueIds(gridData);\n\n    // 超级严格的验证：确保每个对象都是完全有效的\n    const ultraCleanData = processedData.filter(row => {\n      try {\n        // 检查基本有效性\n        if (!row) return false;\n        if (typeof row !== 'object') return false;\n        if (row === null || row === undefined) return false;\n\n        // 检查必要属性\n        if (!row.hasOwnProperty('id')) return false;\n        if (row.id === null || row.id === undefined || row.id === '') return false;\n\n        // 检查id类型\n        if (typeof row.id !== 'string' && typeof row.id !== 'number') return false;\n\n        // 确保所有必要字段都存在\n        const requiredFields = ['NO', 'DATE', 'VEHICLE NO', 'RO NO', 'KM', 'REMARKS', 'MAXCHECK', 'COMMISSION'];\n        for (const field of requiredFields) {\n          if (!row.hasOwnProperty(field)) {\n            console.warn(`Row missing field ${field}:`, row);\n            return false;\n          }\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Error validating row:', error, row);\n        return false;\n      }\n    });\n\n    console.log('Ultra clean data for DataTable:', ultraCleanData.length, 'rows');\n\n    // 最终ID唯一性检查\n    const ids = ultraCleanData.map(row => row.id);\n    const uniqueIds = new Set(ids);\n    if (ids.length !== uniqueIds.size) {\n      console.error('DUPLICATE IDs FOUND!', ids.filter((id, index) => ids.indexOf(id) !== index));\n      // 强制修复重复ID\n      ultraCleanData.forEach((row, index) => {\n        if (ids.filter(id => id === row.id).length > 1) {\n          row.id = `fixed_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n        }\n      });\n    }\n\n    return ultraCleanData;\n  }, [gridData, ensureUniqueIds]);\n\n  // 优化的处理函数，使用useCallback避免不必要的重新渲染\n  const handleRemarksClickMemo = useCallback((rowId, value) => {\n    handleRemarksClick(rowId, value);\n  }, [handleRemarksClick]);\n\n  const handleRemoveRowMemo = useCallback((id) => {\n    handleRemoveRow(id);\n  }, [handleRemoveRow]);\n\n  const handleUndoRowMemo = useCallback((id) => {\n    handleUndoRow(id);\n  }, [handleUndoRow]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n\n        <Box>\n          <Button\n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n\n      {/* 搜索框 */}\n      <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>\n        <Dropdown\n          value={searchColumn}\n          options={[\n            { label: '全部列', value: 'all' },\n            { label: 'NO', value: 'NO' },\n            { label: 'DATE', value: 'DATE' },\n            { label: 'VEHICLE NO', value: 'VEHICLE NO' },\n            { label: 'RO NO', value: 'RO NO' },\n            { label: 'KM', value: 'KM' },\n            { label: 'REMARKS', value: 'REMARKS' },\n            { label: 'HOURS', value: 'MAXCHECK' },\n            { label: 'AMOUNT', value: 'COMMISSION' }\n          ]}\n          onChange={(e) => {\n            setSearchColumn(e.value);\n            if (e.value === 'all') {\n              setFilters({});\n            } else {\n              setGlobalFilter('');\n            }\n          }}\n          placeholder=\"选择搜索列\"\n          style={{ width: '150px' }}\n        />\n        <TextField\n          label={searchColumn === 'all' ? '搜索所有列' : `搜索 ${searchColumn}`}\n          variant=\"outlined\"\n          size=\"small\"\n          value={searchColumn === 'all' ? globalFilter : (filters[searchColumn]?.value || '')}\n          onChange={(e) => {\n            if (searchColumn === 'all') {\n              setGlobalFilter(e.target.value);\n            } else {\n              setFilters({\n                ...filters,\n                [searchColumn]: { value: e.target.value, matchMode: 'contains' }\n              });\n            }\n          }}\n          placeholder=\"输入关键词搜索...\"\n          sx={{ width: '300px' }}\n        />\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <style>\n            {`\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `}\n          </style>\n          <DataTable\n            key={`datatable-${Date.now()}`}\n            value={memoGridData.filter(row => row && row.id)}\n            editMode=\"cell\"\n            dataKey=\"id\"\n            globalFilter={searchColumn === 'all' ? globalFilter : null}\n            filters={searchColumn !== 'all' ? filters : null}\n            sortMode=\"multiple\"\n            removableSort\n            rowClassName={(rowData) => {\n              // 完全防御性编程\n              try {\n                if (!rowData) return '';\n                if (typeof rowData !== 'object') return '';\n                if (!rowData.hasOwnProperty('id')) return '';\n                if (rowData.id === null || rowData.id === undefined) return '';\n\n                if (rowData.NO === 'TOTAL') return 'total-row';\n                if (rowData._removed) return 'removed-row';\n                return '';\n              } catch (error) {\n                console.error('Error in rowClassName:', error, rowData);\n                return '';\n              }\n            }}\n            className=\"custom-datatable\"\n            paginator\n            rows={100}\n            rowsPerPageOptions={[50, 100, 200, 500]}\n            paginatorTemplate=\"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\"\n            currentPageReportTemplate=\"显示 {first} 到 {last} 条，共 {totalRecords} 条记录\"\n            lazy={false}\n            scrollable\n            scrollHeight=\"600px\"\n          >\n            {/* NO Column */}\n            <Column\n              field=\"NO\"\n              header=\"NO\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{Math.floor(rowData.NO)}</span>;\n                }\n                return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n              }}\n              style={{ width: '80px' }}\n            />\n\n            {/* DATE Column */}\n            <Column\n              field=\"DATE\"\n              header=\"DATE\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{dateValue}</span>;\n                }\n                return dateValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* VEHICLE NO Column */}\n            <Column\n              field=\"VEHICLE NO\"\n              header=\"VEHICLE NO\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{rowData['VEHICLE NO']}</span>;\n                }\n                return rowData['VEHICLE NO'];\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* RO NO Column */}\n            <Column\n              field=\"RO NO\"\n              header=\"RO NO\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{roValue}</span>;\n                }\n                return roValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* KM Column */}\n            <Column\n              field=\"KM\"\n              header=\"KM\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{kmValue}</span>;\n                }\n                return kmValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* REMARKS Column */}\n            <Column\n              field=\"REMARKS\"\n              header=\"REMARKS\"\n              sortable\n              body={(rowData) => {\n                if (!rowData) return '';\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  const removedRemarkText = rowData._selected_remarks || '无备注';\n                  return (\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      disabled\n                      sx={{\n                        maxWidth: '100%',\n                        opacity: 0.6,\n                        textTransform: 'none',\n                        fontSize: '12px'\n                      }}\n                    >\n                      {removedRemarkText}\n                    </Button>\n                  );\n                }\n\n                let remarkText = '点击选择';\n                let isSelected = false;\n\n                if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                  remarkText = rowData._selected_remarks;\n                  isSelected = true;\n                }\n\n                return (\n                  <Button\n                    variant={isSelected ? \"contained\" : \"outlined\"}\n                    size=\"small\"\n                    onClick={() => handleRemarksClickMemo(rowData.id, remarkText)}\n                    sx={{\n                      maxWidth: '100%',\n                      textTransform: 'none',\n                      fontSize: '12px'\n                    }}\n                  >\n                    {remarkText}\n                  </Button>\n                );\n              }}\n              style={{ width: '200px' }}\n            />\n\n            {/* MAXCHECK (HOURS) Column */}\n            <Column\n              field=\"MAXCHECK\"\n              header=\"HOURS\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                const hoursValue = typeof rowData.MAXCHECK === 'number' ?\n                  (rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1)) :\n                  rowData.MAXCHECK;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{hoursValue}</span>;\n                }\n                return hoursValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* COMMISSION (AMOUNT) Column */}\n            <Column\n              field=\"COMMISSION\"\n              header=\"AMOUNT\"\n              sortable\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (!rowData) return '';\n                if (rowData.NO === 'TOTAL') {\n                  const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                    typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                  return (\n                    <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n                      {totalValue}\n                    </Typography>\n                  );\n                }\n\n                const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                  typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{amountValue}</span>;\n                }\n                return amountValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* ACTION Column */}\n            <Column\n              header=\"ACTION\"\n              body={(rowData) => {\n                if (!rowData) return '';\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  return (\n                    <Button\n                      variant=\"contained\"\n                      color=\"success\"\n                      size=\"small\"\n                      startIcon={<UndoIcon />}\n                      onClick={() => handleUndoRowMemo(rowData.id)}\n                      sx={{\n                        fontSize: '12px',\n                        textTransform: 'none'\n                      }}\n                    >\n                      恢复\n                    </Button>\n                  );\n                }\n                return (\n                  <Button\n                    variant=\"contained\"\n                    color=\"error\"\n                    size=\"small\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleRemoveRowMemo(rowData.id)}\n                    sx={{\n                      fontSize: '12px',\n                      textTransform: 'none'\n                    }}\n                  >\n                    移除\n                  </Button>\n                );\n              }}\n              style={{ width: '120px' }}\n            />\n          </DataTable>\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,QACL,eAAe;AACtB,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAE9C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AACA,OAAO,yDAAyD;AAChE,OAAO,yCAAyC;AAChD,OAAO,2BAA2B;;AAElC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;AAMD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAEzF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,MAAM;IACzD,MAAM8C,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGZ,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAI1C;EACAC,SAAS,CAAC,MAAM;IACd8C,YAAY,CAACkB,OAAO,CAAC,gBAAgB,EAAEhB,IAAI,CAACiB,SAAS,CAACtB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMuB,aAAa,GAAG/B,IAAI,CAACgC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC7C;IACA;IACA,OAAO;MACL,GAAGD,GAAG;MACNE,EAAE,EAAEF,GAAG,CAACE,EAAE,KAAKC,SAAS,GAAGH,GAAG,CAACE,EAAE,GAAGD,KAAK;MACzCG,OAAO,EAAE,EAAE;MACXC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,MAAM;IAC7C8E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7CvC,aAAa,GAAG,IAAIA,aAAa,CAACwC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,MAAMC,cAAc,GAAIC,OAAO,IAAK;MAClC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC3BJ,OAAO,CAACO,IAAI,CAAC,2BAA2B,EAAEH,OAAO,CAAC;QAClD,OAAO,EAAE;MACX;MAEA,MAAMI,WAAW,GAAG,EAAE;MACtBJ,OAAO,CAACK,OAAO,CAAC,CAAClB,GAAG,EAAEC,KAAK,KAAK;QAC9B;QACA,IAAI,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;UACxEM,OAAO,CAACO,IAAI,CAAC,iCAAiCf,KAAK,GAAG,EAAED,GAAG,CAAC;UAC5D;QACF;;QAEA;QACA,MAAMmB,QAAQ,GAAG;UACfjB,EAAE,EAAE,SAASD,KAAK,IAAImB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAChFC,EAAE,EAAE1B,GAAG,CAAC0B,EAAE,KAAKvB,SAAS,IAAIH,GAAG,CAAC0B,EAAE,KAAK,IAAI,GAAG1B,GAAG,CAAC0B,EAAE,GAAGzB,KAAK,GAAG,CAAC;UAChE0B,IAAI,EAAEC,MAAM,CAAC5B,GAAG,CAAC2B,IAAI,IAAI,EAAE,CAAC;UAC5B,YAAY,EAAEC,MAAM,CAAC5B,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;UAC7C,OAAO,EAAEA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;UAC3B6B,EAAE,EAAEC,MAAM,CAAC9B,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC;UACvBzB,OAAO,EAAEwB,MAAM,CAAC5B,GAAG,CAACI,OAAO,IAAI,EAAE,CAAC;UAClC2B,QAAQ,EAAED,MAAM,CAAC9B,GAAG,CAAC+B,QAAQ,CAAC,IAAI,CAAC;UACnCC,UAAU,EAAEF,MAAM,CAAC9B,GAAG,CAACgC,UAAU,CAAC,IAAI,CAAC;UACvC1B,QAAQ,EAAE2B,OAAO,CAACjC,GAAG,CAACM,QAAQ,CAAC;UAC/BD,iBAAiB,EAAEuB,MAAM,CAAC5B,GAAG,CAACK,iBAAiB,IAAI,EAAE,CAAC;UACtD6B,OAAO,EAAED,OAAO,CAACjC,GAAG,CAAC0B,EAAE,KAAK,OAAO;QACrC,CAAC;QAEDT,WAAW,CAACkB,IAAI,CAAChB,QAAQ,CAAC;MAC5B,CAAC,CAAC;MAEFV,OAAO,CAACC,GAAG,CAAC,WAAWG,OAAO,CAACF,MAAM,kBAAkBM,WAAW,CAACN,MAAM,aAAa,CAAC;MACvF,OAAOM,WAAW;IACpB,CAAC;IAED,IAAImB,WAAW;IACf,IAAIjE,aAAa,IAAIA,aAAa,CAACwC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC0B,WAAW,GAAGxB,cAAc,CAACzC,aAAa,CAAC;IAC7C,CAAC,MAAM;MACLsC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC0B,WAAW,GAAGxB,cAAc,CAACd,aAAa,CAAC;IAC7C;;IAEA;IACAT,eAAe,CAAC,CAAC,GAAG+C,WAAW,CAAC,CAAC;IAEjC3B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE0B,WAAW,CAACzB,MAAM,CAAC;IAChD,OAAOyB,WAAW;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGvG,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwG,iBAAiB,GAAGxG,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMyG,gBAAgB,GAAGzG,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM0G,mBAAmB,GAAG1G,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM2G,UAAU,GAAI1E,IAAI,IAAKA,IAAI,CAACgC,GAAG,CAACC,GAAG,KAAK;IAC5CE,EAAE,EAAEF,GAAG,CAACE,EAAE;IACVwB,EAAE,EAAE1B,GAAG,CAAC0B,EAAE;IACVpB,QAAQ,EAAEN,GAAG,CAACM,QAAQ;IACtBF,OAAO,EAAEJ,GAAG,CAACI,OAAO;IACpBC,iBAAiB,EAAEL,GAAG,CAACK,iBAAiB;IACxC2B,UAAU,EAAEhC,GAAG,CAACgC;EAClB,CAAC,CAAC,CAAC;;EAEH;EACApG,SAAS,CAAC,MAAM;IACd,IAAIwC,YAAY,IAAImC,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAM+B,OAAO,GAAG9D,IAAI,CAACiB,SAAS,CAAC4C,UAAU,CAAClC,QAAQ,CAAC,CAAC;MACpD,MAAMoC,WAAW,GAAGN,mBAAmB,CAACO,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIH,gBAAgB,CAACK,OAAO,EAAE;UAC5BC,YAAY,CAACN,gBAAgB,CAACK,OAAO,CAAC;QACxC;QACAL,gBAAgB,CAACK,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CT,mBAAmB,CAACO,OAAO,GAAGF,OAAO;UACrCJ,iBAAiB,CAACM,OAAO,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC;UACtCjD,YAAY,CAAC,CAAC,GAAGmC,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIgC,gBAAgB,CAACK,OAAO,EAAE;QAC5BC,YAAY,CAACN,gBAAgB,CAACK,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACrC,QAAQ,EAAEnC,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAE2E;EAAgB,CAAC,GAAGrF,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC;IACjDuH,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAxH,SAAS,CAAC,MAAM;IACd,IAAIwD,YAAY,CAACuB,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDtB,eAAe,CAAC,CAAC,GAAGkB,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEnB,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMiE,YAAY,GAAGxH,WAAW,CAAC,CAACyH,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAId,mBAAmB,CAACI,OAAO,EAAE,EAAE;IAE5EG,eAAe,CAACO,OAAO,EAAE;MACvBG,OAAO,EAAEF,QAAQ;MACjB;MACAG,GAAG,EAAEF,SAAS;MACdG,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTP,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMgB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,WAAW,GAAG,GAAGxG,OAAO,aAAaQ,MAAM,EAAE;MACnD,MAAMiG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIjD,IAAI,CAAC,CAAC,CAACkD,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BZ,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdlE,OAAO,CAACkE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BzG,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM0G,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMrH,KAAK,CAACsH,MAAM,CAAC,GAAGrH,OAAO,YAAYQ,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO2G,KAAK,EAAE;MACdlE,OAAO,CAACkE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA1G,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM6G,kBAAkB,GAAIC,CAAC,IAAK;IAChC,IAAI;MAAEC,OAAO;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAGH,CAAC;;IAEpC;IACA,IAAIC,OAAO,CAACtD,EAAE,KAAK,OAAO,IAAIsD,OAAO,CAAC1E,QAAQ,EAAE;MAC9C;IACF;;IAEA;IACA,IAAI4E,KAAK,KAAK,YAAY,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,OAAO,EAAE;MACzF,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAGnD,MAAM,CAACmD,QAAQ,CAAC,IAAI,CAAC;MAClC;IACF;;IAEA;IACAzE,WAAW,CAAC2E,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAGD,IAAI,CAACpF,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACE,EAAE,KAAK8E,OAAO,CAAC9E,EAAE,EAAE;UACzB,OAAO;YAAE,GAAGF,GAAG;YAAE,CAACkF,KAAK,GAAGD;UAAS,CAAC;QACtC;QACA,OAAOjF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMqF,QAAQ,GAAGD,WAAW,CAACE,IAAI,CAACtF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAI2D,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGH,WAAW,CACzBI,MAAM,CAACxF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,IAAI,CAAC1B,GAAG,CAACM,QAAQ,CAAC,CAClDmF,MAAM,CAAC,CAACC,GAAG,EAAE1F,GAAG,KAAK0F,GAAG,IAAI5D,MAAM,CAAC9B,GAAG,CAACgC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/DqD,QAAQ,CAACrD,UAAU,GAAGuD,QAAQ;MAChC;MAEA,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,SAAS,GAAIX,OAAO,IAAK;IAC7B,OAAOA,OAAO,CAACtD,EAAE,KAAK,OAAO,IAAI,CAACsD,OAAO,CAAC1E,QAAQ;EACpD,CAAC;;EAED;EACA,MAAMsF,UAAU,GAAIC,OAAO,IAAK;IAC9B,oBACEjI,OAAA,CAACb,SAAS;MACR+I,IAAI,EAAC,MAAM;MACXC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGjB,CAAC,IAAKc,OAAO,CAACI,cAAc,CAAClB,CAAC,CAACmB,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAED,MAAMC,YAAY,GAAIZ,OAAO,IAAK;IAChC,oBACEjI,OAAA,CAACb,SAAS;MACR+I,IAAI,EAAC,QAAQ;MACbC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGjB,CAAC,IAAKc,OAAO,CAACI,cAAc,CAAClB,CAAC,CAACmB,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;;EAID;EACA,MAAME,gBAAgB,GAAG7K,WAAW,CAAEkC,IAAI,IAAK;IAC7C,MAAMsH,QAAQ,GAAGtH,IAAI,CAACuH,IAAI,CAACtF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,CAAC;IACrD,IAAI2D,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGxH,IAAI,CAClByH,MAAM,CAACxF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,IAAI,CAAC1B,GAAG,CAACM,QAAQ,CAAC,CAClDmF,MAAM,CAAC,CAACC,GAAG,EAAE1F,GAAG,KAAK0F,GAAG,IAAI5D,MAAM,CAAC9B,GAAG,CAACgC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DqD,QAAQ,CAACrD,UAAU,GAAGuD,QAAQ;IAChC;IACA,OAAOxH,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAM4I,iBAAiB,GAAGjL,KAAK,CAACG,WAAW,CAAC,CAACsH,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAH,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwD,kBAAkB,GAAG/K,WAAW,CAAC,MAAM;IAC3CoH,gBAAgB,CAACkC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPjC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2D,kBAAkB,GAAGhL,WAAW,CAAEiL,MAAM,IAAK;IACjD,MAAM;MAAE3D;IAAM,CAAC,GAAGH,aAAa;IAC/B,IAAIG,KAAK,KAAK,IAAI,EAAE;MAClB;MACAyD,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCxG,WAAW,CAACyG,QAAQ,IAAI;UACtB,IAAI7B,WAAW,GAAG6B,QAAQ,CAAClH,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACE,EAAE,KAAKiD,KAAK,EAAE;cACpB,IAAI2D,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAG9G,GAAG;kBAAEI,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGL,GAAG;kBAAEI,OAAO,EAAE0G,MAAM;kBAAEzG,iBAAiB,EAAEyG;gBAAO,CAAC;cAC/D;YACF;YACA,OAAO9G,GAAG;UACZ,CAAC,CAAC;UAEFoF,WAAW,GAAGsB,gBAAgB,CAACtB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAtC,UAAU,CAAC,MAAM;UACfO,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACL,aAAa,EAAE4D,kBAAkB,EAAEF,gBAAgB,EAAErD,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAM6D,mBAAmB,GAAGrL,WAAW,CAAC,MAAM;IAC5CoD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkI,oBAAoB,GAAGtL,WAAW,CAAC,MAAM;IAC7CoD,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqI,YAAY,GAAGvL,WAAW,CAAC,MAAM;IACrC,IAAIiD,SAAS,CAACuI,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC9I,cAAc,CAAC+I,QAAQ,CAACxI,SAAS,CAACuI,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE7I,iBAAiB,CAAC2G,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAErG,SAAS,CAACuI,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDhE,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjC8D,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI5I,cAAc,CAAC+I,QAAQ,CAACxI,SAAS,CAACuI,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDhE,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAACvE,SAAS,EAAEP,cAAc,EAAE4I,oBAAoB,EAAE9D,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAMkE,YAAY,GAAG1L,WAAW,CAAEiL,MAAM,IAAK;IAC3CtI,iBAAiB,CAAC2G,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACgC,IAAI,IAAIA,IAAI,KAAKV,MAAM,CAAC,CAAC;IAC/DzD,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMoE,YAAY,GAAG5L,WAAW,CAAEkC,IAAI,IAAK;IACzC,IAAI2J,SAAS,GAAG,CAAC;IACjB,OAAO3J,IAAI,CAACgC,GAAG,CAACC,GAAG,IAAI;MACrB,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,IAAI,CAAC1B,GAAG,CAACM,QAAQ,EAAE;QACvC,OAAO;UAAE,GAAGN,GAAG;UAAE0B,EAAE,EAAEgG,SAAS;QAAG,CAAC;MACpC;MACA,OAAO1H,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2H,eAAe,GAAG9L,WAAW,CAAEqE,EAAE,IAAK;IAC1CM,WAAW,CAAC2E,IAAI,IAAI;MAClB;MACA,MAAMC,WAAW,GAAGD,IAAI,CAACpF,GAAG,CAACC,GAAG,IAC9BA,GAAG,CAACE,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGF,GAAG;QAAEM,QAAQ,EAAE;MAAK,CAAC,GAAGN,GAC/C,CAAC;;MAED;MACA,MAAM4H,cAAc,GAAGH,YAAY,CAACrC,WAAW,CAAC;MAChD,OAAOsB,gBAAgB,CAACkB,cAAc,CAAC;IACzC,CAAC,CAAC;IAEFvE,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAACoE,YAAY,EAAEf,gBAAgB,EAAErD,YAAY,CAAC,CAAC;;EAElD;EACA,MAAMwE,aAAa,GAAGhM,WAAW,CAAEqE,EAAE,IAAK;IACxCM,WAAW,CAAC2E,IAAI,IAAI;MAClB;MACA,MAAMC,WAAW,GAAGD,IAAI,CAACpF,GAAG,CAACC,GAAG,IAC9BA,GAAG,CAACE,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGF,GAAG;QAAEM,QAAQ,EAAE;MAAM,CAAC,GAAGN,GAChD,CAAC;;MAED;MACA,MAAM4H,cAAc,GAAGH,YAAY,CAACrC,WAAW,CAAC;MAChD,OAAOsB,gBAAgB,CAACkB,cAAc,CAAC;IACzC,CAAC,CAAC;IAEFvE,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;EACtC,CAAC,EAAE,CAACoE,YAAY,EAAEf,gBAAgB,EAAErD,YAAY,CAAC,CAAC;;EAElD;EACA,MAAMyE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF3I,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM4I,YAAY,GAAGxH,QAAQ,CAC1BiF,MAAM,CAACxF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,IAAI,CAAC1B,GAAG,CAACM,QAAQ,CAAC;;MAErD;MACAyH,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACvG,EAAE,KAAK,QAAQ,IAAI,OAAOwG,CAAC,CAACxG,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOuG,CAAC,CAACvG,EAAE,GAAGwG,CAAC,CAACxG,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMyG,OAAO,GAAGJ,YAAY,CAAChI,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QAChD;QACAyB,EAAE,EAAEzB,KAAK,GAAG,CAAC;QACb0B,IAAI,EAAE3B,GAAG,CAAC2B,IAAI,GAAI,OAAO3B,GAAG,CAAC2B,IAAI,KAAK,QAAQ,IAAI3B,GAAG,CAAC2B,IAAI,CAAC2F,QAAQ,CAAC,GAAG,CAAC,GAAGtH,GAAG,CAAC2B,IAAI,CAACyG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpI,GAAG,CAAC2B,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE3B,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGsB,IAAI,CAAC+G,KAAK,CAACrI,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF6B,EAAE,EAAE,OAAO7B,GAAG,CAAC6B,EAAE,KAAK,QAAQ,GAAGP,IAAI,CAAC+G,KAAK,CAACrI,GAAG,CAAC6B,EAAE,CAAC,GAAG7B,GAAG,CAAC6B,EAAE,IAAI,EAAE;QAClEzB,OAAO,EAAGJ,GAAG,CAACK,iBAAiB,IAAIL,GAAG,CAACK,iBAAiB,KAAK,MAAM,GAAIL,GAAG,CAACK,iBAAiB,GAAG,EAAE;QACjGiI,KAAK,EAAE,OAAOtI,GAAG,CAAC+B,QAAQ,KAAK,QAAQ,GACpC/B,GAAG,CAAC+B,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG/B,GAAG,CAAC+B,QAAQ,CAACwG,OAAO,CAAC,CAAC,CAAC,GAAGvI,GAAG,CAAC+B,QAAQ,CAACwG,OAAO,CAAC,CAAC,CAAC,GAC3EvI,GAAG,CAAC+B,QAAQ,IAAI,EAAE;QACpByG,MAAM,EAAE,OAAOxI,GAAG,CAACgC,UAAU,KAAK,QAAQ,GAAGhC,GAAG,CAACgC,UAAU,CAACuG,OAAO,CAAC,CAAC,CAAC,GAAGvI,GAAG,CAACgC,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMyG,WAAW,GAAGlI,QAAQ,CACzBiF,MAAM,CAACxF,GAAG,IAAIA,GAAG,CAAC0B,EAAE,KAAK,OAAO,IAAI,CAAC1B,GAAG,CAACM,QAAQ,IAAIN,GAAG,CAACgC,UAAU,CAAC,CACpEyD,MAAM,CAAC,CAACC,GAAG,EAAE1F,GAAG,KAAK0F,GAAG,IAAI,OAAO1F,GAAG,CAACgC,UAAU,KAAK,QAAQ,GAAGhC,GAAG,CAACgC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM0G,QAAQ,GAAG,MAAMnL,KAAK,CAACoL,IAAI,CAAC,GAAGnL,OAAO,oBAAoB,EAAE;QAChEO,IAAI,EAAEoK,OAAO;QACbM,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCvK,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI0K,QAAQ,CAAC3K,IAAI,IAAI2K,QAAQ,CAAC3K,IAAI,CAAC6K,KAAK,EAAE;QACxC;QACA,MAAM5E,WAAW,GAAG,GAAGxG,OAAO,CAAC4K,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAAC3K,IAAI,CAAC8K,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAxF,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACAP,UAAU,CAAC,MAAM;UACf,MAAMgG,MAAM,GAAG5E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C2E,MAAM,CAAC3C,KAAK,CAAC4C,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAGhF,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACsE,MAAM,CAAC;UACjChG,UAAU,CAAC,MAAM;YACfoB,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACoE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOtE,KAAK,EAAE;MACdlE,OAAO,CAACkE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACRlE,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM+J,kBAAkB,GAAGrN,WAAW,CAAC,CAACsH,KAAK,EAAE4C,KAAK,KAAK;IACvDY,iBAAiB,CAACxD,KAAK,EAAE4C,KAAK,CAAC;EACjC,CAAC,EAAE,CAACY,iBAAiB,CAAC,CAAC;;EAIvB;EACA,MAAMwC,eAAe,GAAGtN,WAAW,CAAEkC,IAAI,IAAK;IAC5C,IAAI,CAAC+C,KAAK,CAACC,OAAO,CAAChD,IAAI,CAAC,EAAE;MACxB0C,OAAO,CAACO,IAAI,CAAC,uBAAuB,EAAEjD,IAAI,CAAC;MAC3C,OAAO,EAAE;IACX;IAEA,MAAMqL,SAAS,GAAG,EAAE;IACpB,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAIC,OAAO,GAAG,CAAC;IAEfxL,IAAI,CAACmD,OAAO,CAAC,CAAClB,GAAG,EAAEC,KAAK,KAAK;MAC3B;MACA,IAAI,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;QACxEM,OAAO,CAACO,IAAI,CAAC,iCAAiCf,KAAK,GAAG,EAAED,GAAG,CAAC;QAC5D;MACF;;MAEA;MACA,IAAIwJ,QAAQ;MACZ,GAAG;QACDA,QAAQ,GAAG,YAAYD,OAAO,IAAItJ,KAAK,IAAImB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACtG8H,OAAO,EAAE;MACX,CAAC,QAAQF,OAAO,CAACI,GAAG,CAACD,QAAQ,CAAC;MAE9BH,OAAO,CAACK,GAAG,CAACF,QAAQ,CAAC;;MAErB;MACA,MAAMrI,QAAQ,GAAG;QACfjB,EAAE,EAAEsJ,QAAQ;QACZ9H,EAAE,EAAE1B,GAAG,CAAC0B,EAAE,KAAKvB,SAAS,IAAIH,GAAG,CAAC0B,EAAE,KAAK,IAAI,GAAG1B,GAAG,CAAC0B,EAAE,GAAGzB,KAAK,GAAG,CAAC;QAChE0B,IAAI,EAAE3B,GAAG,CAAC2B,IAAI,IAAI,EAAE;QACpB,YAAY,EAAE3B,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAEA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QAC3B6B,EAAE,EAAE7B,GAAG,CAAC6B,EAAE,IAAI,CAAC;QACfzB,OAAO,EAAEJ,GAAG,CAACI,OAAO,IAAI,EAAE;QAC1B2B,QAAQ,EAAE/B,GAAG,CAAC+B,QAAQ,IAAI,CAAC;QAC3BC,UAAU,EAAEhC,GAAG,CAACgC,UAAU,IAAI,CAAC;QAC/B1B,QAAQ,EAAE2B,OAAO,CAACjC,GAAG,CAACM,QAAQ,CAAC;QAC/BD,iBAAiB,EAAEL,GAAG,CAACK,iBAAiB,IAAI,EAAE;QAC9C6B,OAAO,EAAED,OAAO,CAACjC,GAAG,CAACkC,OAAO,IAAIlC,GAAG,CAAC0B,EAAE,KAAK,OAAO;MACpD,CAAC;MAED0H,SAAS,CAACjH,IAAI,CAAChB,QAAQ,CAAC;IAC1B,CAAC,CAAC;IAEFV,OAAO,CAACC,GAAG,CAAC,aAAa3C,IAAI,CAAC4C,MAAM,wBAAwByI,SAAS,CAACzI,MAAM,aAAa,CAAC;IAC1F,OAAOyI,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,YAAY,GAAG5N,OAAO,CAAC,MAAM;IACjC0E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM,EAAE,MAAM,CAAC;;IAE7D;IACA,MAAMb,aAAa,GAAGqJ,eAAe,CAAC5I,QAAQ,CAAC;;IAE/C;IACA,MAAMqJ,cAAc,GAAG9J,aAAa,CAAC0F,MAAM,CAACxF,GAAG,IAAI;MACjD,IAAI;QACF;QACA,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;QACtB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,KAAK;QACzC,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE,OAAO,KAAK;;QAEnD;QACA,IAAI,CAACH,GAAG,CAAC6J,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;QAC3C,IAAI7J,GAAG,CAACE,EAAE,KAAK,IAAI,IAAIF,GAAG,CAACE,EAAE,KAAKC,SAAS,IAAIH,GAAG,CAACE,EAAE,KAAK,EAAE,EAAE,OAAO,KAAK;;QAE1E;QACA,IAAI,OAAOF,GAAG,CAACE,EAAE,KAAK,QAAQ,IAAI,OAAOF,GAAG,CAACE,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;;QAE1E;QACA,MAAM4J,cAAc,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC;QACvG,KAAK,MAAM5E,KAAK,IAAI4E,cAAc,EAAE;UAClC,IAAI,CAAC9J,GAAG,CAAC6J,cAAc,CAAC3E,KAAK,CAAC,EAAE;YAC9BzE,OAAO,CAACO,IAAI,CAAC,qBAAqBkE,KAAK,GAAG,EAAElF,GAAG,CAAC;YAChD,OAAO,KAAK;UACd;QACF;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,OAAO2E,KAAK,EAAE;QACdlE,OAAO,CAACkE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,EAAE3E,GAAG,CAAC;QAClD,OAAO,KAAK;MACd;IACF,CAAC,CAAC;IAEFS,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkJ,cAAc,CAACjJ,MAAM,EAAE,MAAM,CAAC;;IAE7E;IACA,MAAMoJ,GAAG,GAAGH,cAAc,CAAC7J,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,CAAC;IAC7C,MAAM8J,SAAS,GAAG,IAAIV,GAAG,CAACS,GAAG,CAAC;IAC9B,IAAIA,GAAG,CAACpJ,MAAM,KAAKqJ,SAAS,CAACC,IAAI,EAAE;MACjCxJ,OAAO,CAACkE,KAAK,CAAC,sBAAsB,EAAEoF,GAAG,CAACvE,MAAM,CAAC,CAACtF,EAAE,EAAED,KAAK,KAAK8J,GAAG,CAACG,OAAO,CAAChK,EAAE,CAAC,KAAKD,KAAK,CAAC,CAAC;MAC3F;MACA2J,cAAc,CAAC1I,OAAO,CAAC,CAAClB,GAAG,EAAEC,KAAK,KAAK;QACrC,IAAI8J,GAAG,CAACvE,MAAM,CAACtF,EAAE,IAAIA,EAAE,KAAKF,GAAG,CAACE,EAAE,CAAC,CAACS,MAAM,GAAG,CAAC,EAAE;UAC9CX,GAAG,CAACE,EAAE,GAAG,SAASD,KAAK,IAAImB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACvF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOmI,cAAc;EACvB,CAAC,EAAE,CAACrJ,QAAQ,EAAE4I,eAAe,CAAC,CAAC;;EAE/B;EACA,MAAMgB,sBAAsB,GAAGtO,WAAW,CAAC,CAACsH,KAAK,EAAE4C,KAAK,KAAK;IAC3DmD,kBAAkB,CAAC/F,KAAK,EAAE4C,KAAK,CAAC;EAClC,CAAC,EAAE,CAACmD,kBAAkB,CAAC,CAAC;EAExB,MAAMkB,mBAAmB,GAAGvO,WAAW,CAAEqE,EAAE,IAAK;IAC9CyH,eAAe,CAACzH,EAAE,CAAC;EACrB,CAAC,EAAE,CAACyH,eAAe,CAAC,CAAC;EAErB,MAAM0C,iBAAiB,GAAGxO,WAAW,CAAEqE,EAAE,IAAK;IAC5C2H,aAAa,CAAC3H,EAAE,CAAC;EACnB,CAAC,EAAE,CAAC2H,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAI,CAACtH,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE/C,OAAA,CAAC5B,GAAG;MAAC2H,EAAE,EAAE;QAAE2G,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtC5M,OAAA,CAAC3B,UAAU;QAACwH,OAAO,EAAC,IAAI;QAACgH,KAAK,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAEhD;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5I,OAAA,CAAC1B,MAAM;QACLuH,OAAO,EAAC,WAAW;QACnBiH,OAAO,EAAEzM,OAAQ;QACjB0F,EAAE,EAAE;UAAEgH,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EACf;MAED;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE5I,OAAA,CAAC5B,GAAG;IAAAwO,QAAA,gBACF5M,OAAA,CAAC5B,GAAG;MAAC2H,EAAE,EAAE;QAAEoF,OAAO,EAAE,MAAM;QAAE6B,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzF5M,OAAA,CAAC3B,UAAU;QAACwH,OAAO,EAAC,IAAI;QAACsH,YAAY;QAAAP,QAAA,EAAC;MAEtC;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5I,OAAA,CAAC5B,GAAG;QAAAwO,QAAA,gBACF5M,OAAA,CAAC1B,MAAM;UACLuH,OAAO,EAAC,WAAW;UACnBgH,KAAK,EAAC,SAAS;UACfO,SAAS,eAAEpN,OAAA,CAACX,YAAY;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BkE,OAAO,EAAE3G,cAAe;UACxBJ,EAAE,EAAE;YAAEsH,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACf;QAED;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5I,OAAA,CAAC1B,MAAM;UACLuH,OAAO,EAAC,WAAW;UACnBgH,KAAK,EAAC,SAAS;UACfO,SAAS,eAAEpN,OAAA,CAACN,gBAAgB;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCkE,OAAO,EAAE5C,gBAAiB;UAC1BoD,QAAQ,EAAEhM,oBAAqB;UAC/ByE,EAAE,EAAE;YAAEsH,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EAEbtL,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAET5I,OAAA,CAAC1B,MAAM;UACLuH,OAAO,EAAC,UAAU;UAClBuH,SAAS,eAAEpN,OAAA,CAACV,cAAc;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BkE,OAAO,EAAE9F,aAAc;UAAA4F,QAAA,EACxB;QAED;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5I,OAAA,CAAC5B,GAAG;MAAC2H,EAAE,EAAE;QAAEmH,EAAE,EAAE,CAAC;QAAE/B,OAAO,EAAE,MAAM;QAAEoC,GAAG,EAAE,CAAC;QAAEN,UAAU,EAAE;MAAS,CAAE;MAAAL,QAAA,gBAChE5M,OAAA,CAACZ,QAAQ;QACP+I,KAAK,EAAEvG,YAAa;QACpBqG,OAAO,EAAE,CACP;UAAEuF,KAAK,EAAE,KAAK;UAAErF,KAAK,EAAE;QAAM,CAAC,EAC9B;UAAEqF,KAAK,EAAE,IAAI;UAAErF,KAAK,EAAE;QAAK,CAAC,EAC5B;UAAEqF,KAAK,EAAE,MAAM;UAAErF,KAAK,EAAE;QAAO,CAAC,EAChC;UAAEqF,KAAK,EAAE,YAAY;UAAErF,KAAK,EAAE;QAAa,CAAC,EAC5C;UAAEqF,KAAK,EAAE,OAAO;UAAErF,KAAK,EAAE;QAAQ,CAAC,EAClC;UAAEqF,KAAK,EAAE,IAAI;UAAErF,KAAK,EAAE;QAAK,CAAC,EAC5B;UAAEqF,KAAK,EAAE,SAAS;UAAErF,KAAK,EAAE;QAAU,CAAC,EACtC;UAAEqF,KAAK,EAAE,OAAO;UAAErF,KAAK,EAAE;QAAW,CAAC,EACrC;UAAEqF,KAAK,EAAE,QAAQ;UAAErF,KAAK,EAAE;QAAa,CAAC,CACxC;QACFC,QAAQ,EAAGjB,CAAC,IAAK;UACftF,eAAe,CAACsF,CAAC,CAACgB,KAAK,CAAC;UACxB,IAAIhB,CAAC,CAACgB,KAAK,KAAK,KAAK,EAAE;YACrBpG,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM;YACLJ,eAAe,CAAC,EAAE,CAAC;UACrB;QACF,CAAE;QACF8L,WAAW,EAAC,gCAAO;QACnBlF,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACF5I,OAAA,CAACjB,SAAS;QACRyO,KAAK,EAAE5L,YAAY,KAAK,KAAK,GAAG,OAAO,GAAG,MAAMA,YAAY,EAAG;QAC/DiE,OAAO,EAAC,UAAU;QAClBwG,IAAI,EAAC,OAAO;QACZlE,KAAK,EAAEvG,YAAY,KAAK,KAAK,GAAGF,YAAY,GAAI,EAAAhB,qBAAA,GAAAoB,OAAO,CAACF,YAAY,CAAC,cAAAlB,qBAAA,uBAArBA,qBAAA,CAAuByH,KAAK,KAAI,EAAI;QACpFC,QAAQ,EAAGjB,CAAC,IAAK;UACf,IAAIvF,YAAY,KAAK,KAAK,EAAE;YAC1BD,eAAe,CAACwF,CAAC,CAACmB,MAAM,CAACH,KAAK,CAAC;UACjC,CAAC,MAAM;YACLpG,UAAU,CAAC;cACT,GAAGD,OAAO;cACV,CAACF,YAAY,GAAG;gBAAEuG,KAAK,EAAEhB,CAAC,CAACmB,MAAM,CAACH,KAAK;gBAAEuF,SAAS,EAAE;cAAW;YACjE,CAAC,CAAC;UACJ;QACF,CAAE;QACFD,WAAW,EAAC,+CAAY;QACxB1H,EAAE,EAAE;UAAEyC,KAAK,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5I,OAAA,CAACzB,KAAK;MAACwH,EAAE,EAAE;QAAEyC,KAAK,EAAE,MAAM;QAAEmF,QAAQ,EAAE;MAAS,CAAE;MAAAf,QAAA,eAC/C5M,OAAA,CAAC5B,GAAG;QAAC2H,EAAE,EAAE;UAAE6H,MAAM,EAAE,MAAM;UAAEpF,KAAK,EAAE;QAAO,CAAE;QAAAoE,QAAA,gBACzC5M,OAAA;UAAA4M,QAAA,EACG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QAAa;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACR5I,OAAA,CAACf,SAAS;UAERkJ,KAAK,EAAE4D,YAAY,CAACnE,MAAM,CAACxF,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACE,EAAE,CAAE;UACjDuL,QAAQ,EAAC,MAAM;UACfC,OAAO,EAAC,IAAI;UACZpM,YAAY,EAAEE,YAAY,KAAK,KAAK,GAAGF,YAAY,GAAG,IAAK;UAC3DI,OAAO,EAAEF,YAAY,KAAK,KAAK,GAAGE,OAAO,GAAG,IAAK;UACjDiM,QAAQ,EAAC,UAAU;UACnBC,aAAa;UACbC,YAAY,EAAG7G,OAAO,IAAK;YACzB;YACA,IAAI;cACF,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE,OAAO,EAAE;cAC1C,IAAI,CAACA,OAAO,CAAC6E,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE;cAC5C,IAAI7E,OAAO,CAAC9E,EAAE,KAAK,IAAI,IAAI8E,OAAO,CAAC9E,EAAE,KAAKC,SAAS,EAAE,OAAO,EAAE;cAE9D,IAAI6E,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,WAAW;cAC9C,IAAIsD,OAAO,CAAC1E,QAAQ,EAAE,OAAO,aAAa;cAC1C,OAAO,EAAE;YACX,CAAC,CAAC,OAAOqE,KAAK,EAAE;cACdlE,OAAO,CAACkE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,EAAEK,OAAO,CAAC;cACvD,OAAO,EAAE;YACX;UACF,CAAE;UACF8G,SAAS,EAAC,kBAAkB;UAC5BC,SAAS;UACTC,IAAI,EAAE,GAAI;UACVC,kBAAkB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACxCC,iBAAiB,EAAC,sGAAsG;UACxHC,yBAAyB,EAAC,yFAA4C;UACtEC,IAAI,EAAE,KAAM;UACZC,UAAU;UACVC,YAAY,EAAC,OAAO;UAAA9B,QAAA,gBAGpB5M,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,IAAI;YACVqH,MAAM,EAAC,IAAI;YACXC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGY,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAIA,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAElJ,IAAI,CAAC+G,KAAK,CAACrD,OAAO,CAACtD,EAAE;gBAAC;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACxG;cACA,OAAO,OAAOxB,OAAO,CAACtD,EAAE,KAAK,QAAQ,GAAGJ,IAAI,CAAC+G,KAAK,CAACrD,OAAO,CAACtD,EAAE,CAAC,GAAGsD,OAAO,CAACtD,EAAE;YAC7E,CAAE;YACFyE,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,MAAM;YACZqH,MAAM,EAAC,MAAM;YACbC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGY,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,MAAM2H,SAAS,GAAG3H,OAAO,CAACrD,IAAI,IAAIqD,OAAO,CAACrD,IAAI,CAAC2F,QAAQ,CAAC,GAAG,CAAC,GAAGtC,OAAO,CAACrD,IAAI,CAACyG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,OAAO,CAACrD,IAAI;cACxG,IAAIqD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEmC;gBAAS;kBAAAtG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC3F;cACA,OAAOmG,SAAS;YAClB,CAAE;YACFxG,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,YAAY;YAClBqH,MAAM,EAAC,YAAY;YACnBC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGY,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAIA,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAExF,OAAO,CAAC,YAAY;gBAAC;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACvG;cACA,OAAOxB,OAAO,CAAC,YAAY,CAAC;YAC9B,CAAE;YACFmB,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,OAAO;YACbqH,MAAM,EAAC,OAAO;YACdC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGyB,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC/Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,MAAM4H,OAAO,GAAG,OAAO5H,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG1D,IAAI,CAAC+G,KAAK,CAACrD,OAAO,CAAC,OAAO,CAAC,CAAC,GAAGA,OAAO,CAAC,OAAO,CAAC;cACtG,IAAIA,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEoC;gBAAO;kBAAAvG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOoG,OAAO;YAChB,CAAE;YACFzG,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,IAAI;YACVqH,MAAM,EAAC,IAAI;YACXC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGyB,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC/Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,MAAM6H,OAAO,GAAG,OAAO7H,OAAO,CAACnD,EAAE,KAAK,QAAQ,GAAGP,IAAI,CAAC+G,KAAK,CAACrD,OAAO,CAACnD,EAAE,CAAC,GAAGmD,OAAO,CAACnD,EAAE;cACpF,IAAImD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEqC;gBAAO;kBAAAxG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOqG,OAAO;YAChB,CAAE;YACF1G,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,SAAS;YACfqH,MAAM,EAAC,SAAS;YAChBC,QAAQ;YACRjI,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAIsD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,MAAMwM,iBAAiB,GAAG9H,OAAO,CAAC3E,iBAAiB,IAAI,KAAK;gBAC5D,oBACEzC,OAAA,CAAC1B,MAAM;kBACLuH,OAAO,EAAC,UAAU;kBAClBwG,IAAI,EAAC,OAAO;kBACZiB,QAAQ;kBACRvH,EAAE,EAAE;oBACFoJ,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE,GAAG;oBACZC,aAAa,EAAE,MAAM;oBACrBC,QAAQ,EAAE;kBACZ,CAAE;kBAAA1C,QAAA,EAEDsC;gBAAiB;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAEb;cAEA,IAAI2G,UAAU,GAAG,MAAM;cACvB,IAAIC,UAAU,GAAG,KAAK;cAEtB,IAAIpI,OAAO,CAAC3E,iBAAiB,IAAI2E,OAAO,CAAC3E,iBAAiB,KAAK,MAAM,EAAE;gBACrE8M,UAAU,GAAGnI,OAAO,CAAC3E,iBAAiB;gBACtC+M,UAAU,GAAG,IAAI;cACnB;cAEA,oBACExP,OAAA,CAAC1B,MAAM;gBACLuH,OAAO,EAAE2J,UAAU,GAAG,WAAW,GAAG,UAAW;gBAC/CnD,IAAI,EAAC,OAAO;gBACZS,OAAO,EAAEA,CAAA,KAAMP,sBAAsB,CAACnF,OAAO,CAAC9E,EAAE,EAAEiN,UAAU,CAAE;gBAC9DxJ,EAAE,EAAE;kBACFoJ,QAAQ,EAAE,MAAM;kBAChBE,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA1C,QAAA,EAED2C;cAAU;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAEb,CAAE;YACFL,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,UAAU;YAChBqH,MAAM,EAAC,OAAO;YACdC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGyB,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC/Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,MAAMqI,UAAU,GAAG,OAAOrI,OAAO,CAACjD,QAAQ,KAAK,QAAQ,GACpDiD,OAAO,CAACjD,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGiD,OAAO,CAACjD,QAAQ,CAACwG,OAAO,CAAC,CAAC,CAAC,GAAGvD,OAAO,CAACjD,QAAQ,CAACwG,OAAO,CAAC,CAAC,CAAC,GACvFvD,OAAO,CAACjD,QAAQ;cAClB,IAAIiD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAE6C;gBAAU;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC5F;cACA,OAAO6G,UAAU;YACnB,CAAE;YACFlH,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLoI,KAAK,EAAC,YAAY;YAClBqH,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,MAAM,EAAG5G,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACb,OAAO,CAAC,GAAGyB,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC/Ef,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE;gBAC1B,MAAM4L,UAAU,GAAG,OAAOtI,OAAO,CAAChD,UAAU,KAAK,QAAQ,GAAGgD,OAAO,CAAChD,UAAU,CAACuG,OAAO,CAAC,CAAC,CAAC,GACvF,OAAOvD,OAAO,CAAChD,UAAU,KAAK,QAAQ,IAAI,CAACuL,KAAK,CAACzL,MAAM,CAACkD,OAAO,CAAChD,UAAU,CAAC,CAAC,GAAGF,MAAM,CAACkD,OAAO,CAAChD,UAAU,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAC,GAAGvD,OAAO,CAAChD,UAAU;gBAC3I,oBACEpE,OAAA,CAAC3B,UAAU;kBAACwH,OAAO,EAAC,OAAO;kBAAC+J,UAAU,EAAC,MAAM;kBAAC/C,KAAK,EAAC,SAAS;kBAAAD,QAAA,EAC1D8C;gBAAU;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAEjB;cAEA,MAAMiH,WAAW,GAAG,OAAOzI,OAAO,CAAChD,UAAU,KAAK,QAAQ,GAAGgD,OAAO,CAAChD,UAAU,CAACuG,OAAO,CAAC,CAAC,CAAC,GACxF,OAAOvD,OAAO,CAAChD,UAAU,KAAK,QAAQ,IAAI,CAACuL,KAAK,CAACzL,MAAM,CAACkD,OAAO,CAAChD,UAAU,CAAC,CAAC,GAAGF,MAAM,CAACkD,OAAO,CAAChD,UAAU,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAC,GAAGvD,OAAO,CAAChD,UAAU;cAE3I,IAAIgD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBAAO1C,OAAA;kBAAMuI,KAAK,EAAE;oBAAEuG,cAAc,EAAE,cAAc;oBAAEjC,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEiD;gBAAW;kBAAApH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC7F;cACA,OAAOiH,WAAW;YACpB,CAAE;YACFtH,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGF5I,OAAA,CAACd,MAAM;YACLyP,MAAM,EAAC,QAAQ;YACfhI,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;cACvB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAIsD,OAAO,CAAC1E,QAAQ,EAAE;gBACpB,oBACE1C,OAAA,CAAC1B,MAAM;kBACLuH,OAAO,EAAC,WAAW;kBACnBgH,KAAK,EAAC,SAAS;kBACfR,IAAI,EAAC,OAAO;kBACZe,SAAS,eAAEpN,OAAA,CAACP,QAAQ;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBkE,OAAO,EAAEA,CAAA,KAAML,iBAAiB,CAACrF,OAAO,CAAC9E,EAAE,CAAE;kBAC7CyD,EAAE,EAAE;oBACFuJ,QAAQ,EAAE,MAAM;oBAChBD,aAAa,EAAE;kBACjB,CAAE;kBAAAzC,QAAA,EACH;gBAED;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAEb;cACA,oBACE5I,OAAA,CAAC1B,MAAM;gBACLuH,OAAO,EAAC,WAAW;gBACnBgH,KAAK,EAAC,OAAO;gBACbR,IAAI,EAAC,OAAO;gBACZe,SAAS,eAAEpN,OAAA,CAACR,UAAU;kBAAAiJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BkE,OAAO,EAAEA,CAAA,KAAMN,mBAAmB,CAACpF,OAAO,CAAC9E,EAAE,CAAE;gBAC/CyD,EAAE,EAAE;kBACFuJ,QAAQ,EAAE,MAAM;kBAChBD,aAAa,EAAE;gBACjB,CAAE;gBAAAzC,QAAA,EACH;cAED;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAEb,CAAE;YACFL,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA,GAzQG,aAAapF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0QrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR5I,OAAA,CAACxB,MAAM;MACL8G,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBwK,OAAO,EAAE9G,kBAAmB;MAC5B+G,SAAS;MACTZ,QAAQ,EAAC,IAAI;MACba,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAvD,QAAA,gBAEnB5M,OAAA,CAACvB,WAAW;QAAAmO,QAAA,eACV5M,OAAA,CAAC5B,GAAG;UAAC2H,EAAE,EAAE;YAAEoF,OAAO,EAAE,MAAM;YAAE6B,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAL,QAAA,gBAClF5M,OAAA,CAAC3B,UAAU;YAACwH,OAAO,EAAC,IAAI;YAAA+G,QAAA,EAAC;UAAS;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C5I,OAAA,CAAC1B,MAAM;YACL8O,SAAS,eAAEpN,OAAA,CAACT,OAAO;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkE,OAAO,EAAExD,mBAAoB;YAC7BuD,KAAK,EAAC,SAAS;YAAAD,QAAA,EAChB;UAED;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd5I,OAAA,CAACtB,aAAa;QAAC0R,QAAQ;QAACrK,EAAE,EAAE;UAAEsK,CAAC,EAAE;QAAE,CAAE;QAAAzD,QAAA,eACnC5M,OAAA,CAACH,aAAa;UACZ+N,MAAM,EAAE,GAAI;UACZ0C,SAAS,EAAE3P,cAAc,CAACoC,MAAO;UACjCwN,QAAQ,EAAE,EAAG;UACb/H,KAAK,EAAC,MAAM;UAAAoE,QAAA,EAEXA,CAAC;YAAEvK,KAAK;YAAEkG;UAAM,CAAC,KAAK;YACrB,MAAMW,MAAM,GAAGvI,cAAc,CAAC0B,KAAK,CAAC;YACpC,oBACErC,OAAA,CAACpB,QAAQ;cAEP2J,KAAK,EAAEA,KAAM;cACbiI,cAAc;cACdC,eAAe,eACbzQ,OAAA,CAAChB,UAAU;gBACT0R,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB5D,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAACT,MAAM,CAAE;gBAAA0D,QAAA,eAEpC5M,OAAA,CAACR,UAAU;kBAAAiJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAgE,QAAA,eAED5M,OAAA,CAACnB,cAAc;gBAACiO,OAAO,EAAEA,CAAA,KAAM7D,kBAAkB,CAACC,MAAM,CAAE;gBAAA0D,QAAA,eACxD5M,OAAA,CAAClB,YAAY;kBAAC6R,OAAO,EAAEzH;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZM,MAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChB5I,OAAA,CAACrB,aAAa;QAAAiO,QAAA,eACZ5M,OAAA,CAAC1B,MAAM;UAACwO,OAAO,EAAE9D,kBAAmB;UAAA4D,QAAA,EAAC;QAAE;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5I,OAAA,CAACxB,MAAM;MACL8G,IAAI,EAAElE,eAAgB;MACtB0O,OAAO,EAAEvG,oBAAqB;MAC9BwG,SAAS;MACTZ,QAAQ,EAAC,IAAI;MACba,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAvD,QAAA,gBAEnB5M,OAAA,CAACvB,WAAW;QAAAmO,QAAA,EAAC;MAAK;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC5I,OAAA,CAACtB,aAAa;QAAAkO,QAAA,eACZ5M,OAAA,CAACjB,SAAS;UACR6R,SAAS;UACTC,MAAM,EAAC,OAAO;UACdvO,EAAE,EAAC,MAAM;UACTkL,KAAK,EAAC,0BAAM;UACZtF,IAAI,EAAC,MAAM;UACX6H,SAAS;UACTlK,OAAO,EAAC,UAAU;UAClBsC,KAAK,EAAEjH,SAAU;UACjBkH,QAAQ,EAAGjB,CAAC,IAAKhG,YAAY,CAACgG,CAAC,CAACmB,MAAM,CAACH,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB5I,OAAA,CAACrB,aAAa;QAAAiO,QAAA,gBACZ5M,OAAA,CAAC1B,MAAM;UAACwO,OAAO,EAAEvD,oBAAqB;UAAAqD,QAAA,EAAC;QAAE;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD5I,OAAA,CAAC1B,MAAM;UAACwO,OAAO,EAAEtD,YAAa;UAACqD,KAAK,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAE;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnI,EAAA,CArmCIP,aAAa;EAAA,QA+IWJ,WAAW;AAAA;AAAAgR,EAAA,GA/InC5Q,aAAa;AAumCnB,eAAeA,aAAa;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}