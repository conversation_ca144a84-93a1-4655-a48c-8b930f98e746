{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '点击选择备注',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleClick,\n      variant: uiState.isSelected ? 'contained' : 'outlined',\n      color: \"primary\",\n      size: \"small\",\n      sx: {\n        minWidth: '80px',\n        maxWidth: '120px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out'\n      },\n      children: uiState.text || '点击选择'\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 1000),\n  // 1秒防抖\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 800),\n  // 0.8秒防抖\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"error\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              fontSize: '0.75rem',\n              textTransform: 'none',\n              minWidth: '70px'\n            },\n            children: \"\\u79FB\\u9664\"\n          }, \"remove\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 949,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1035,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1026,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1094,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1096,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1085,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 802,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"jBA6e2Ojy8VmgSkRST4KFBPkzRk=\", false, function () {\n  return [useSnackbar];\n});\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "axios", "API_URL", "FixedSizeList", "useSnackbar", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "searchColumn", "setSearchColumn", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "key", "borderRadius", "border", "borderColor", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "prev", "totalValue", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "label", "opacity", "remarkText", "startIcon", "fontWeight", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "toString", "cellValue", "memoGridData", "textAlign", "py", "mt", "mb", "alignItems", "justifyContent", "flexWrap", "gap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "backgroundColor", "lineHeight", "padding", "borderBottom", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "type", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : '点击选择备注'} arrow placement=\"top\">\n      <Button\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        onClick={handleClick}\n        variant={uiState.isSelected ? 'contained' : 'outlined'}\n        color=\"primary\"\n        size=\"small\"\n        sx={{\n          minWidth: '80px',\n          maxWidth: '120px',\n          fontSize: '0.75rem',\n          textTransform: 'none',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          transition: 'all 0.2s ease-in-out'\n        }}\n      >\n        {uiState.text || '点击选择'}\n      </Button>\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 1000), // 1秒防抖\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 800), // 0.8秒防抖\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Button\n                key=\"undo\"\n                variant=\"contained\"\n                color=\"success\"\n                size=\"small\"\n                startIcon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{\n                  fontSize: '0.75rem',\n                  textTransform: 'none',\n                  minWidth: '70px'\n                }}\n              >\n                恢复\n              </Button>\n            );\n          }\n          return (\n            <Button\n              key=\"remove\"\n              variant=\"contained\"\n              color=\"error\"\n              size=\"small\"\n              startIcon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              }}\n            >\n              移除\n            </Button>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              rows={memoGridData}\n              columns={columns}\n              pageSize={100}\n              rowsPerPageOptions={[100, 200, 500]}\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAACvB,KAAK;IAAA,GAAKmC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAGvE,KAAK,CAACwE,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC;IAErC0E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA1E,SAAS,CAAC,MAAM;IACd6E,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC3B,OAAO;IAAC0D,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,QAAS;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eACjFlC,OAAA,CAAC3C,MAAM;MAELqE,OAAO,EAAEG,WAAY;MACrBM,OAAO,EAAER,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;MACvDW,KAAK,EAAC,SAAS;MACfC,IAAI,EAAC,OAAO;MACZC,EAAE,EAAE;QACFC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,SAAS;QACnBC,aAAa,EAAE,MAAM;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE;MACd,CAAE;MAAAZ,QAAA,EAEDP,OAAO,CAACH,IAAI,IAAI;IAAM,GAhBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiBtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC,kCAAC;AAAC8B,GAAA,GA5CG5B,UAAU;AA8ChB,MAAM6B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhH,QAAQ,CAAC,MAAM;IACzD,MAAMiH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGrD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACwH,eAAe,EAAEC,kBAAkB,CAAC,GAAGzH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC4H,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8H,YAAY,EAAEC,eAAe,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgI,YAAY,EAAEC,eAAe,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdiH,YAAY,CAACgB,OAAO,CAAC,gBAAgB,EAAEd,IAAI,CAACe,SAAS,CAACpB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMqB,aAAa,GAAG,CAACjC,IAAI,IAAI,EAAE,EAAEkC,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3I,QAAQ,CAAC,MAAM;IAC7C4I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7CtC,aAAa,GAAG,IAAIA,aAAa,CAACuC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIvC,aAAa,IAAIA,aAAa,CAACuC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGxC,aAAa,CAAC8B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGnJ,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMoJ,iBAAiB,GAAGpJ,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMqJ,gBAAgB,GAAGrJ,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMsJ,mBAAmB,GAAGtJ,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMuJ,UAAU,GAAIvD,IAAI,IAAKA,IAAI,CAACkC,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA1J,SAAS,CAAC,MAAM;IACd,IAAIuG,YAAY,IAAIkC,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAG1C,IAAI,CAACe,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BtG,YAAY,CAAC8F,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGrG,UAAU,CAAC,MAAM;UAC1C2F,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCpD,YAAY,CAAC,CAAC,GAAGkC,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5BtG,YAAY,CAAC8F,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAElC,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAEyD;EAAgB,CAAC,GAAGjH,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACkH,aAAa,EAAEC,gBAAgB,CAAC,GAAGnK,QAAQ,CAAC;IACjDoK,IAAI,EAAE,KAAK;IACX3F,KAAK,EAAE,IAAI;IACX4F,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApK,SAAS,CAAC,MAAM;IACd,IAAI+H,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMsC,YAAY,GAAGpK,WAAW,CAAC,CAACqK,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAId,mBAAmB,CAACO,OAAO,EAAE,EAAE;IAE5EC,eAAe,CAACM,OAAO,EAAE;MACvBlF,OAAO,EAAEmF,QAAQ;MACjB;MACAE,GAAG,EAAED,SAAS;MACdjF,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBmF,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTL,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI1E,MAAM,IAAIA,MAAM,CAAC2E,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7CzE,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM0E,WAAW,GAAG,GAAGlI,OAAO,aAAasD,MAAM,EAAE;MACnD,MAAM6E,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIxB,IAAI,CAAC,CAAC,CAACyB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BX,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrF,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMsF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM/I,KAAK,CAACgJ,MAAM,CAAC,GAAG/I,OAAO,YAAYsD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOuF,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAtF,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMyF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACzD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM2C,kBAAkB,GAAG9L,WAAW,CAAC,CAACiG,IAAI,EAAE8F,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAG/F,IAAI,IAAIuC,QAAQ,IAAI,EAAE;IACxC,IAAI,CAACyD,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD6D,MAAM,CAAC,CAACC,GAAG,EAAEjE,GAAG,KAAK;MACpB,IAAI2D,UAAU,IAAI3D,GAAG,CAACa,EAAE,KAAK8C,UAAU,CAAC9C,EAAE,EAAE;QAC1C,OAAOoD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACtC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO4C,GAAG,IAAIC,MAAM,CAAClE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+D,gBAAgB,GAAGvM,WAAW,CAAEiG,IAAI,IAAK;IAC7C,MAAMuG,QAAQ,GAAGvG,IAAI,CAACwG,IAAI,CAACrE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIqD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGzG,IAAI,CAClBkG,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD6D,MAAM,CAAC,CAACC,GAAG,EAAEjE,GAAG,KAAKiE,GAAG,IAAIC,MAAM,CAAClE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/D+C,QAAQ,CAAC/C,UAAU,GAAGiD,QAAQ;IAChC;IACA,OAAOzG,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0G,2BAA2B,GAAG3M,WAAW,CAC7CiD,QAAQ,CAAEgD,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAACgB,OAAO,CAAC,eAAe,EAAEd,IAAI,CAACe,SAAS,CAAChC,IAAI,CAAC,CAAC;MAC3DyC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE1C,IAAI,CAAC2C,MAAM,CAAC;IAClD,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMmB,qBAAqB,GAAG5M,WAAW,CACvCiD,QAAQ,CAAEgD,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvByC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACrC,YAAY,CACf,CAAC;;EAED;EACA,MAAMuG,gBAAgB,GAAG7M,WAAW,CAAE8M,MAAM,IAAK;IAC/CrE,WAAW,CAACsE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGlB,kBAAkB,CAACiB,IAAI,EAAED,MAAM,CAAC;MACjD,MAAMG,WAAW,GAAGF,IAAI,CAAC5E,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACa,EAAE,KAAK6D,MAAM,CAAC7D,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAG0E;QAAO,CAAC;QACtD,IAAI1E,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAEuD;QAAW,CAAC;QACjE,OAAO5E,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAuE,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOH,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMM,uBAAuB,GAAIzB,KAAK,IAAK;IACzCrB,YAAY,CAAC,SAASqB,KAAK,CAACpB,OAAO,EAAE,EAAE,OAAO,CAAC;EACjD,CAAC;;EAED;EACA,MAAM8C,iBAAiB,GAAGtN,KAAK,CAACG,WAAW,CAAC,CAACuE,KAAK,EAAE4F,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV3F,KAAK;MACL4F;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,kBAAkB,GAAGpN,WAAW,CAAC,MAAM;IAC3CiK,gBAAgB,CAAC8C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP7C,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmD,kBAAkB,GAAGrN,WAAW,CAAEsN,MAAM,IAAK;IACjD,MAAM;MAAE/I;IAAM,CAAC,GAAGyF,aAAa;IAC/B,IAAIzF,KAAK,KAAK,IAAI,EAAE;MAClB;MACA6I,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC/E,WAAW,CAACgF,QAAQ,IAAI;UACtB,IAAIR,WAAW,GAAGQ,QAAQ,CAACtF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAK1E,KAAK,EAAE;cACpB,IAAI+I,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGlF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGF,GAAG;kBAAEC,OAAO,EAAEiF,MAAM;kBAAEhF,iBAAiB,EAAEgF;gBAAO,CAAC;cAC/D;YACF;YACA,OAAOlF,GAAG;UACZ,CAAC,CAAC;UAEF6E,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAxJ,UAAU,CAAC,MAAM;UACf2G,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEoD,kBAAkB,EAAEb,gBAAgB,EAAEnC,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAMsD,mBAAmB,GAAG1N,WAAW,CAAC,MAAM;IAC5CuH,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoG,oBAAoB,GAAG3N,WAAW,CAAC,MAAM;IAC7CuH,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuG,YAAY,GAAG5N,WAAW,CAAC,MAAM;IACrC,IAAIoH,SAAS,CAACyG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAChH,cAAc,CAACiH,QAAQ,CAAC1G,SAAS,CAACyG,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE/G,iBAAiB,CAACiG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3F,SAAS,CAACyG,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDzD,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjCuD,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI9G,cAAc,CAACiH,QAAQ,CAAC1G,SAAS,CAACyG,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDzD,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAAChD,SAAS,EAAEP,cAAc,EAAE8G,oBAAoB,EAAEvD,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAM2D,YAAY,GAAG/N,WAAW,CAAEsN,MAAM,IAAK;IAC3CxG,iBAAiB,CAACiG,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAAC6B,IAAI,IAAIA,IAAI,KAAKV,MAAM,CAAC,CAAC;IAC/DlD,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM6D,eAAe,GAAGjO,WAAW,CAAEiJ,EAAE,IAAK;IAC1CR,WAAW,CAACsE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC5E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnF6E,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,MAAMiB,cAAc,GAAGjB,WAAW,CAACd,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH+E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnF,EAAE,GAAGoF,CAAC,CAACpF,EAAE,CAAC;MAC1CiF,cAAc,CAACI,OAAO,CAAC,CAAClG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOiE,WAAW;IACpB,CAAC,CAAC;IAEF7C,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAACmC,gBAAgB,EAAEnC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMmE,aAAa,GAAGvO,WAAW,CAAEiJ,EAAE,IAAK;IACxCR,WAAW,CAACsE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC5E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpF6E,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,MAAMiB,cAAc,GAAGjB,WAAW,CAACd,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH+E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnF,EAAE,GAAGoF,CAAC,CAACpF,EAAE,CAAC;MAC1CiF,cAAc,CAACI,OAAO,CAAC,CAAClG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOiE,WAAW;IACpB,CAAC,CAAC;IACFxJ,UAAU,CAAC,MAAM;MACf2G,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACmC,gBAAgB,EAAEnC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMoE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF/G,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMgH,YAAY,GAAG,CAACjG,QAAQ,IAAI,EAAE,EACjC2D,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAkG,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACjF,EAAE,KAAK,QAAQ,IAAI,OAAOkF,CAAC,CAAClF,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOiF,CAAC,CAACjF,EAAE,GAAGkF,CAAC,CAAClF,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMuF,OAAO,GAAGD,YAAY,CAACtG,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb2F,IAAI,EAAEvG,GAAG,CAACuG,IAAI,GAAI,OAAOvG,GAAG,CAACuG,IAAI,KAAK,QAAQ,IAAIvG,GAAG,CAACuG,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAG1F,GAAG,CAACuG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGxG,GAAG,CAACuG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEvG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGyG,IAAI,CAACC,KAAK,CAAC1G,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF2G,EAAE,EAAE,OAAO3G,GAAG,CAAC2G,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAC1G,GAAG,CAAC2G,EAAE,CAAC,GAAG3G,GAAG,CAAC2G,EAAE,IAAI,EAAE;QAClE1G,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG0G,KAAK,EAAE,OAAO5G,GAAG,CAAC6G,QAAQ,KAAK,QAAQ,GACpC7G,GAAG,CAAC6G,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG7G,GAAG,CAAC6G,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG9G,GAAG,CAAC6G,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3E9G,GAAG,CAAC6G,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAO/G,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAACyF,OAAO,CAAC,CAAC,CAAC,GAAG9G,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM2F,WAAW,GAAG,CAAC5G,QAAQ,IAAI,EAAE,EAChC2D,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE2C,MAAM,CAAC,CAACC,GAAG,EAAEjE,GAAG,KAAKiE,GAAG,IAAI,OAAOjE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAM4F,YAAY,GAAInJ,MAAM,IAAIA,MAAM,CAAC2E,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG3E,MAAM;MAE5F,MAAMoJ,QAAQ,GAAG,MAAM3M,KAAK,CAAC4M,IAAI,CAAC,GAAG3M,OAAO,oBAAoB,EAAE;QAChEqD,IAAI,EAAEyI,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnChJ,MAAM,EAAEmJ;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACrJ,IAAI,IAAIqJ,QAAQ,CAACrJ,IAAI,CAACuJ,KAAK,EAAE;QACxC;QACA,MAAM1E,WAAW,GAAG,GAAGlI,OAAO,CAACgM,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGU,QAAQ,CAACrJ,IAAI,CAACwJ,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACArF,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACA3G,UAAU,CAAC,MAAM;UACf,MAAMiM,MAAM,GAAG1E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CyE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG/E,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACoE,MAAM,CAAC;UACjCjM,UAAU,CAAC,MAAM;YACfuH,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACkE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACR3C,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMsI,kBAAkB,GAAG/P,WAAW,CAAC,CAACuE,KAAK,EAAEyL,KAAK,KAAK;IACvD7C,iBAAiB,CAAC5I,KAAK,EAAEyL,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC7C,iBAAiB,CAAC,CAAC;EAEvB,MAAM8C,OAAO,GAAG/P,OAAO,CAAC,MAAOsG,WAAW,CAAC2B,GAAG,CAAC+H,GAAG,IAAI;IACpD,IAAI,EAAE1H,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC2H,cAAc,CAACD,GAAG,CAACzJ,KAAK,CAAC,CAAC,IAAIyJ,GAAG,CAACzJ,KAAK,KAAK,SAAS,IAAIyJ,GAAG,CAACzJ,KAAK,KAAK,QAAQ,IAAIyJ,GAAG,CAACzJ,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIyJ,GAAG,CAACzJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEyJ,GAAG,CAACzJ,KAAK;QAChBC,UAAU,EAAEwJ,GAAG,CAACxJ,UAAU;QAC1B0J,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV1J,QAAQ,EAAE,KAAK;QACf2J,UAAU,EAAGzE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACzD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI0C,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMgI,iBAAiB,GAAG1E,MAAM,CAACzD,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEtF,OAAA,CAAC3B,OAAO;cAAC0D,KAAK,EAAE8G,MAAM,CAACzD,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACtD,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvElC,OAAA,CAAC5B,IAAI;gBACHoP,KAAK,EAAED,iBAAkB;gBACzBnL,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAEiL,OAAO,EAAE,GAAG;kBAAE3K,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAE+J,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA9L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIyM,UAAU,GAAG,MAAM;UACvB,IAAIjM,UAAU,GAAG,KAAK;UAEtB,IAAIoH,MAAM,CAACzD,GAAG,CAACE,iBAAiB,IAAIuD,MAAM,CAACzD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EoI,UAAU,GAAG7E,MAAM,CAACzD,GAAG,CAACE,iBAAiB;YACzC7D,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAEsH,MAAM,CAACzD,GAAG,CAACa,EAAG;YACrBzE,IAAI,EAAEkM,UAAW;YACjBjM,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEqL;UAAmB;YAAAjM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIiM,GAAG,CAACzJ,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEyJ,GAAG,CAACzJ,KAAK;QAChBC,UAAU,EAAEwJ,GAAG,CAACxJ,UAAU;QAC1B0J,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV1J,QAAQ,EAAE,KAAK;QACf2J,UAAU,EAAGzE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACzD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI0C,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACEvF,OAAA,CAAC3C,MAAM;cAEL8E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZsL,SAAS,eAAE3N,OAAA,CAACZ,QAAQ;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAM6J,aAAa,CAAC1C,MAAM,CAACzD,GAAG,CAACa,EAAE,CAAE;cAC5C3D,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAL,QAAA,EACH;YAED,GAbM,MAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC;UAEb;UACA,oBACEjB,OAAA,CAAC3C,MAAM;YAEL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,OAAO;YACbC,IAAI,EAAC,OAAO;YACZsL,SAAS,eAAE3N,OAAA,CAACb,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BS,OAAO,EAAEA,CAAA,KAAMuJ,eAAe,CAACpC,MAAM,CAACzD,GAAG,CAACa,EAAE,CAAE;YAC9C3D,EAAE,EAAE;cACFG,QAAQ,EAAE,SAAS;cACnBC,aAAa,EAAE,MAAM;cACrBH,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,EACH;UAED,GAbM,QAAQ;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaN,CAAC;QAEb;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGiM,GAAG;MACNvJ,QAAQ,EAAEkF,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACzD,GAAG,IAAIyD,MAAM,CAACzD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI0C,MAAM,CAACzD,GAAG,IAAIyD,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAO2H,GAAG,CAACvJ,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD2J,UAAU,EAAGzE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACzD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI+G,GAAG,CAACzJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEzD,OAAA,CAAC5C,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACyL,UAAU,EAAC,MAAM;YAACxL,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAO2G,MAAM,CAACmE,KAAK,KAAK,QAAQ,GAAGnE,MAAM,CAACmE,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOrD,MAAM,CAACmE,KAAK,KAAK,QAAQ,IAAI,CAACa,KAAK,CAACvE,MAAM,CAACT,MAAM,CAACmE,KAAK,CAAC,CAAC,GAAG1D,MAAM,CAACT,MAAM,CAACmE,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGrD,MAAM,CAACmE;UAAK;YAAAlM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI4H,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEvF,OAAA,CAAC5C,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAEwL,cAAc,EAAE;YAAe,CAAE;YAAA5L,QAAA,EACtF2G,MAAM,CAACmE;UAAK;YAAAlM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIiM,GAAG,CAACzJ,KAAK,KAAK,MAAM,IAAIoF,MAAM,CAACmE,KAAK,EAAE;UACxC,OAAOnE,MAAM,CAACmE,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIsB,GAAG,CAACzJ,KAAK,KAAK,IAAI,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnB,IAAI,CAACC,KAAK,CAACjD,MAAM,CAACmE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACzJ,KAAK,KAAK,OAAO,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOnB,IAAI,CAACC,KAAK,CAACjD,MAAM,CAACmE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACzJ,KAAK,KAAK,IAAI,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnB,IAAI,CAACC,KAAK,CAACjD,MAAM,CAACmE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACzJ,KAAK,KAAK,UAAU,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOnE,MAAM,CAACmE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGnE,MAAM,CAACmE,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGrD,MAAM,CAACmE,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIgB,GAAG,CAACzJ,KAAK,KAAK,YAAY,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOnE,MAAM,CAACmE,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIgB,GAAG,CAACzJ,KAAK,KAAK,YAAY,IAAI,OAAOoF,MAAM,CAACmE,KAAK,KAAK,QAAQ,IAAI,CAACa,KAAK,CAACvE,MAAM,CAACT,MAAM,CAACmE,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO1D,MAAM,CAACT,MAAM,CAACmE,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOrD,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOnE,MAAM,CAACmE,KAAK;QACrB;QACA,OAAOnE,MAAM,CAACmE,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC7D,MAAM,CAAC4E,OAAO,CAAE,EAAE,CAACvK,WAAW,EAAEgC,QAAQ,EAAEuH,kBAAkB,EAAE9B,eAAe,EAAEM,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAMyC,gBAAgB,GAAG9Q,OAAO,CAAC,MAAM;IACrC,IAAI,CAACwH,UAAU,CAACmG,IAAI,CAAC,CAAC,EAAE;MACtB,OAAOrF,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAMyI,WAAW,GAAGvJ,UAAU,CAACwJ,WAAW,CAAC,CAAC;IAC5C,OAAO,CAAC1I,QAAQ,IAAI,EAAE,EAAE2D,MAAM,CAAC/D,GAAG,IAAI;MACpC,IAAIR,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOuJ,MAAM,CAACC,MAAM,CAAChJ,GAAG,CAAC,CAACiJ,IAAI,CAACrB,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACsB,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACpD,QAAQ,CAACmD,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMM,SAAS,GAAGnJ,GAAG,CAACR,YAAY,CAAC;QACnC,OAAO2J,SAAS,IAAIA,SAAS,CAACD,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACpD,QAAQ,CAACmD,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzI,QAAQ,EAAEd,UAAU,EAAEE,YAAY,CAAC,CAAC;;EAExC;EACA,MAAM4J,YAAY,GAAGtR,OAAO,CAAC,MAAM8Q,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,IAAI,CAACxI,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE5F,OAAA,CAAC7C,GAAG;MAACmF,EAAE,EAAE;QAAEmM,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxM,QAAA,gBACtClC,OAAA,CAAC5C,UAAU;QAAC+E,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC3C,MAAM;QACL8E,OAAO,EAAC,WAAW;QACnBT,OAAO,EAAEyB,OAAQ;QACjBb,EAAE,EAAE;UAAEqM,EAAE,EAAE;QAAE,CAAE;QAAAzM,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAAC7C,GAAG;IAAA+E,QAAA,gBAEFlC,OAAA,CAAC1B,IAAI;MAACgE,EAAE,EAAE;QAAEsM,EAAE,EAAE;MAAE,CAAE;MAAA1M,QAAA,eAClBlC,OAAA,CAACzB,WAAW;QAAA2D,QAAA,eACVlC,OAAA,CAAC7C,GAAG;UAACmF,EAAE,EAAE;YAAEsK,OAAO,EAAE,MAAM;YAAEiC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAA9M,QAAA,gBAC5GlC,OAAA,CAAC7C,GAAG;YAACmF,EAAE,EAAE;cAAEsK,OAAO,EAAE,MAAM;cAAEiC,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAE,CAAE;YAAA9M,QAAA,gBACzDlC,OAAA,CAACV,cAAc;cAACgD,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAAC7C,GAAG;cAAA+E,QAAA,gBACFlC,OAAA,CAAC5C,UAAU;gBAAC+E,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAEsL,UAAU,EAAE,GAAG;kBAAExL,KAAK,EAAE,cAAc;kBAAEwM,EAAE,EAAE;gBAAI,CAAE;gBAAA1M,QAAA,EAAC;cAElF;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5C,UAAU;gBAAC+E,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAF,QAAA,EAAC;cAE7D;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAACxB,KAAK;YAACqC,SAAS,EAAC,KAAK;YAACoO,OAAO,EAAE,CAAE;YAAC3M,EAAE,EAAE;cAAEyM,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA9M,QAAA,gBAClElC,OAAA,CAAC5B,IAAI;cACH8Q,IAAI,eAAElP,OAAA,CAACT,aAAa;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBuM,KAAK,EAAE,GAAG,CAACgB,YAAY,IAAI,EAAE,EAAErF,MAAM,CAAC/D,GAAG,IAAI,CAACA,GAAG,CAACc,OAAO,IAAI,CAACd,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,MAAO;cACzFxD,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAC5B,IAAI;cACH8Q,IAAI,eAAElP,OAAA,CAACR,cAAc;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBuM,KAAK,EAAE,WAAW1E,kBAAkB,CAAC0F,YAAY,CAAC,CAACtC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChE9J,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACuN,YAAY,IAAI,EAAE,EAAErF,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,iBAC1D5F,OAAA,CAAC5B,IAAI;cACH8Q,IAAI,eAAElP,OAAA,CAACb,UAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBuM,KAAK,EAAE,GAAG,CAACgB,YAAY,IAAI,EAAE,EAAErF,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,OAAQ;cACzExD,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC1B,IAAI;MAACgE,EAAE,EAAE;QAAEsM,EAAE,EAAE;MAAE,CAAE;MAAA1M,QAAA,eAClBlC,OAAA,CAACzB,WAAW;QAAA2D,QAAA,gBACVlC,OAAA,CAAC5C,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEsL,UAAU,EAAE,GAAG;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAA1M,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAACxB,KAAK;UAACqC,SAAS,EAAE;YAAEsO,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAA/M,QAAA,gBACxDlC,OAAA,CAAC3C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfuL,SAAS,eAAE3N,OAAA,CAAChB,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAEkG,cAAe;YAAA1F,QAAA,EACzB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC3C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfuL,SAAS,EAAEnJ,oBAAoB,gBAAGxE,OAAA,CAAC7B,gBAAgB;cAACkE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACX,gBAAgB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAE8J,gBAAiB;YAC1B6D,QAAQ,EAAE7K,oBAAqB;YAAAtC,QAAA,EAE9BsC,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC3C,MAAM;YACL8E,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACbuL,SAAS,eAAE3N,OAAA,CAACf,cAAc;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEgH,aAAc;YAAAxG,QAAA,EACxB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC1B,IAAI;MAACgE,EAAE,EAAE;QAAEsM,EAAE,EAAE;MAAE,CAAE;MAAA1M,QAAA,eAClBlC,OAAA,CAACzB,WAAW;QAAA2D,QAAA,gBACVlC,OAAA,CAAC5C,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEsL,UAAU,EAAE,GAAG;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAA1M,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAACxB,KAAK;UAACqC,SAAS,EAAE;YAAEsO,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACJ,UAAU,EAAC,QAAQ;UAAA3M,QAAA,gBAC5ElC,OAAA,CAACtB,WAAW;YAAC2D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAL,QAAA,gBAC9ClC,OAAA,CAACrB,UAAU;cAAAuD,QAAA,EAAC;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACpB,MAAM;cACLoO,KAAK,EAAEpI,YAAa;cACpB4I,KAAK,EAAC,0BAAM;cACZ8B,QAAQ,EAAGxN,CAAC,IAAK+C,eAAe,CAAC/C,CAAC,CAACyN,MAAM,CAACvC,KAAK,CAAE;cAAA9K,QAAA,gBAEjDlC,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,KAAK;gBAAA9K,QAAA,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,IAAI;gBAAA9K,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,MAAM;gBAAA9K,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,YAAY;gBAAA9K,QAAA,EAAC;cAAU;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,OAAO;gBAAA9K,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,IAAI;gBAAA9K,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,SAAS;gBAAA9K,QAAA,EAAC;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,UAAU;gBAAA9K,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACnB,QAAQ;gBAACmO,KAAK,EAAC,YAAY;gBAAA9K,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAAC/B,SAAS;YACRoE,IAAI,EAAC,OAAO;YACZmN,WAAW,EAAC,yCAAW;YACvBxC,KAAK,EAAEtI,UAAW;YAClB4K,QAAQ,EAAGxN,CAAC,IAAK6C,aAAa,CAAC7C,CAAC,CAACyN,MAAM,CAACvC,KAAK,CAAE;YAC/C1K,EAAE,EAAE;cAAEmN,QAAQ,EAAE,CAAC;cAAElN,QAAQ,EAAE;YAAI,CAAE;YACnCmN,UAAU,EAAE;cACVC,cAAc,eACZ3P,OAAA,CAAClB,cAAc;gBAAC8Q,QAAQ,EAAC,OAAO;gBAAA1N,QAAA,eAC9BlC,OAAA,CAACP,UAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACD4O,YAAY,EAAEnL,UAAU,iBACtB1E,OAAA,CAAClB,cAAc;gBAAC8Q,QAAQ,EAAC,KAAK;gBAAA1N,QAAA,eAC5BlC,OAAA,CAAC9B,UAAU;kBACTmE,IAAI,EAAC,OAAO;kBACZX,OAAO,EAAEA,CAAA,KAAMiD,aAAa,CAAC,EAAE,CAAE;kBACjCmL,IAAI,EAAC,KAAK;kBAAA5N,QAAA,eAEVlC,OAAA,CAACN,SAAS;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDyD,UAAU,iBACT1E,OAAA,CAAC5C,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,eAC9C,EAAC8L,gBAAgB,CAAC7E,MAAM,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC1C,KAAK;MAACgF,EAAE,EAAE;QAAE+K,KAAK,EAAE,MAAM;QAAE1K,QAAQ,EAAE;MAAS,CAAE;MAAAT,QAAA,eAC7ClC,OAAA,CAAC7C,GAAG;QAACmF,EAAE,EAAE;UAAEyN,MAAM,EAAE,MAAM;UAAE1C,KAAK,EAAE;QAAO,CAAE;QAAAnL,QAAA,eACzClC,OAAA,CAACjB,QAAQ;UACPiR,IAAI,EAAExB,YAAa;UACnBvB,OAAO,EAAEA,OAAQ;UACjBgD,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGzH,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAACzD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI2C,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFgL,cAAc,EAAG1H,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAACzD,GAAG,CAACc,OAAO,IAAI2C,MAAM,CAACzD,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOsD,MAAM,CAAC2H,MAAM,CAAC7M,QAAQ,IAAI,OAAOkF,MAAM,CAAC2H,MAAM,CAAC7M,QAAQ,KAAK,UAAU,GAC3EkF,MAAM,CAAC2H,MAAM,CAAC7M,QAAQ,CAACkF,MAAM,CAAC,GAAGA,MAAM,CAAC2H,MAAM,CAAC7M,QAAQ;UAC3D,CAAE;UACFkG,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAACrD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOgE,MAAM,CAACrD,UAAU,KAAK,QAAQ,EAAE;gBACzCqD,MAAM,CAACrD,UAAU,GAAG6C,MAAM,CAACQ,MAAM,CAACrD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOoD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFI,uBAAuB,EAAEA,uBAAwB;UACjD5H,EAAE,EAAE;YACF,cAAc,EAAE;cACdmO,eAAe,EAAE,0BAA0B;cAC3C7C,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChB6C,eAAe,EAAE,0BAA0B;cAC3CrO,KAAK,EAAE,eAAe;cACtB0L,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrBjL,UAAU,EAAE,QAAQ;cACpB6N,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,WAAW;cACzBjJ,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9B8I,eAAe,EAAE,oBAAoB;cACrCG,YAAY,EAAE,WAAW;cACzBjJ,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7B8I,eAAe,EAAE,oBAAoB;cACrCrO,KAAK,EAAE,cAAc;cACrByO,WAAW,EAAE,WAAW;cACxBlJ,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdkJ,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClCjD,UAAU,EAAE,MAAM;cAClBxL,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChCmK,OAAO,EAAE;YACX,CAAC;YACDkE,SAAS,EAAE;UACb;QAAE;UAAAhQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAACvC,MAAM;MACLyJ,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB6J,OAAO,EAAE3G,kBAAmB;MAC5B4G,SAAS;MACTxO,QAAQ,EAAC,IAAI;MACbyO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlP,QAAA,gBAEnBlC,OAAA,CAACtC,WAAW;QAAAwE,QAAA,eACVlC,OAAA,CAAC7C,GAAG;UAACmF,EAAE,EAAE;YAAEsK,OAAO,EAAE,MAAM;YAAEkC,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAA3M,QAAA,gBAClFlC,OAAA,CAAC5C,UAAU;YAAC+E,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC3C,MAAM;YACLsQ,SAAS,eAAE3N,OAAA,CAACd,OAAO;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEgJ,mBAAoB;YAC7BtI,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjB,OAAA,CAACrC,aAAa;QAAC0T,QAAQ;QAAC/O,EAAE,EAAE;UAAEgP,CAAC,EAAE;QAAE,CAAE;QAAApP,QAAA,eACnClC,OAAA,CAACH,aAAa;UACZkQ,MAAM,EAAE,GAAI;UACZwB,SAAS,EAAE1N,cAAc,CAAC+B,MAAO;UACjC4L,QAAQ,EAAE,EAAG;UACbnE,KAAK,EAAC,MAAM;UAAAnL,QAAA,EAEXA,CAAC;YAAE8D,KAAK;YAAE2G;UAAM,CAAC,KAAK;YACrB,MAAMrC,MAAM,GAAGzG,cAAc,CAACmC,KAAK,CAAC;YACpC,oBACEhG,OAAA,CAAClC,QAAQ;cAEP6O,KAAK,EAAEA,KAAM;cACb8E,cAAc;cACdC,eAAe,eACb1R,OAAA,CAAC9B,UAAU;gBACT4R,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBpO,OAAO,EAAEA,CAAA,KAAMqJ,YAAY,CAACT,MAAM,CAAE;gBAAApI,QAAA,eAEpClC,OAAA,CAACb,UAAU;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiB,QAAA,eAEDlC,OAAA,CAACjC,cAAc;gBAAC2D,OAAO,EAAEA,CAAA,KAAM2I,kBAAkB,CAACC,MAAM,CAAE;gBAAApI,QAAA,eACxDlC,OAAA,CAAChC,YAAY;kBAAC2T,OAAO,EAAErH;gBAAO;kBAAAxJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZqJ,MAAM;cAAAxJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACpC,aAAa;QAAAsE,QAAA,eACZlC,OAAA,CAAC3C,MAAM;UAACqE,OAAO,EAAE0I,kBAAmB;UAAAlI,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAACvC,MAAM;MACLyJ,IAAI,EAAE5C,eAAgB;MACtByM,OAAO,EAAEpG,oBAAqB;MAC9BqG,SAAS;MACTxO,QAAQ,EAAC,IAAI;MACbyO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlP,QAAA,gBAEnBlC,OAAA,CAACtC,WAAW;QAAAwE,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACrC,aAAa;QAAAuE,QAAA,eACZlC,OAAA,CAAC/B,SAAS;UACR2T,SAAS;UACTC,MAAM,EAAC,OAAO;UACd5L,EAAE,EAAC,MAAM;UACTuH,KAAK,EAAC,0BAAM;UACZsE,IAAI,EAAC,MAAM;UACXd,SAAS;UACT7O,OAAO,EAAC,UAAU;UAClB6K,KAAK,EAAE5I,SAAU;UACjBkL,QAAQ,EAAGxN,CAAC,IAAKuC,YAAY,CAACvC,CAAC,CAACyN,MAAM,CAACvC,KAAK;QAAE;UAAAlM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACpC,aAAa;QAAAsE,QAAA,gBACZlC,OAAA,CAAC3C,MAAM;UAACqE,OAAO,EAAEiJ,oBAAqB;UAAAzI,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC3C,MAAM;UAACqE,OAAO,EAAEkJ,YAAa;UAACxI,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACsC,GAAA,CAz9BIP,aAAa;EAAA,QAqIWlD,WAAW;AAAA;AAAAiS,GAAA,GArInC/O,aAAa;AA29BnB,eAAeA,aAAa;AAAC,IAAA9B,EAAA,EAAAI,GAAA,EAAAyB,GAAA,EAAAgP,GAAA;AAAAC,YAAA,CAAA9Q,EAAA;AAAA8Q,YAAA,CAAA1Q,GAAA;AAAA0Q,YAAA,CAAAjP,GAAA;AAAAiP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}