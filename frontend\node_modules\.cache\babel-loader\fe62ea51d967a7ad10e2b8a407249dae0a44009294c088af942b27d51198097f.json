{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument } from '@mui/utils';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridExpandedRowCountSelector } from '../filter/gridFilterSelector';\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector } from '../columns/gridColumnsSelector';\nimport { gridClasses } from '../../../constants/gridClasses';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getColumnsToExport } from './utils';\nimport { getDerivedPaginationModel } from '../pagination/useGridPaginationModel';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { GridPrintExportMenuItem } from '../../../components/toolbar/GridToolbarExport';\nimport { getTotalHeaderHeight } from '../columns/gridColumnsUtils';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../colDef/gridCheckboxSelectionColDef';\nimport { gridDataRowIdsSelector, gridRowsLookupSelector } from '../rows/gridRowsSelector';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction raf() {\n  return new Promise(resolve => {\n    requestAnimationFrame(() => {\n      resolve();\n    });\n  });\n}\nfunction buildPrintWindow(title) {\n  const iframeEl = document.createElement('iframe');\n  iframeEl.style.position = 'absolute';\n  iframeEl.style.width = '0px';\n  iframeEl.style.height = '0px';\n  iframeEl.title = title || document.title;\n  return iframeEl;\n}\n\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridPrintExport = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPrintExport');\n  const doc = React.useRef(null);\n  const previousGridState = React.useRef(null);\n  const previousColumnVisibility = React.useRef({});\n  const previousRows = React.useRef([]);\n  React.useEffect(() => {\n    doc.current = ownerDocument(apiRef.current.rootElementRef.current);\n  }, [apiRef]);\n\n  // Returns a promise because updateColumns triggers state update and\n  // the new state needs to be in place before the grid can be sized correctly\n  const updateGridColumnsForPrint = React.useCallback((fields, allColumns, includeCheckboxes) => new Promise(resolve => {\n    const exportedColumnFields = getColumnsToExport({\n      apiRef,\n      options: {\n        fields,\n        allColumns\n      }\n    }).map(column => column.field);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newColumnVisibilityModel = {};\n    columns.forEach(column => {\n      newColumnVisibilityModel[column.field] = exportedColumnFields.includes(column.field);\n    });\n    if (includeCheckboxes) {\n      newColumnVisibilityModel[GRID_CHECKBOX_SELECTION_COL_DEF.field] = true;\n    }\n    apiRef.current.setColumnVisibilityModel(newColumnVisibilityModel);\n    resolve();\n  }), [apiRef]);\n  const updateGridRowsForPrint = React.useCallback(getRowsToExport => {\n    const rowsToExportIds = getRowsToExport({\n      apiRef\n    });\n    const newRows = rowsToExportIds.map(id => apiRef.current.getRow(id));\n    apiRef.current.setRows(newRows);\n  }, [apiRef]);\n  const handlePrintWindowLoad = React.useCallback((printWindow, options) => {\n    var _querySelector, _querySelector2;\n    const normalizeOptions = _extends({\n      copyStyles: true,\n      hideToolbar: false,\n      hideFooter: false,\n      includeCheckboxes: false\n    }, options);\n    const printDoc = printWindow.contentDocument;\n    if (!printDoc) {\n      return;\n    }\n    const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n    const gridRootElement = apiRef.current.rootElementRef.current;\n    const gridClone = gridRootElement.cloneNode(true);\n\n    // Allow to overflow to not hide the border of the last row\n    const gridMain = gridClone.querySelector(`.${gridClasses.main}`);\n    gridMain.style.overflow = 'visible';\n\n    // See https://support.google.com/chrome/thread/191619088?hl=en&msgid=193009642\n    gridClone.style.contain = 'size';\n    const columnHeaders = gridClone.querySelector(`.${gridClasses.columnHeaders}`);\n    const columnHeadersInner = columnHeaders.querySelector(`.${gridClasses.columnHeadersInner}`);\n    columnHeadersInner.style.width = '100%';\n    let gridToolbarElementHeight = ((_querySelector = gridRootElement.querySelector(`.${gridClasses.toolbarContainer}`)) == null ? void 0 : _querySelector.offsetHeight) || 0;\n    let gridFooterElementHeight = ((_querySelector2 = gridRootElement.querySelector(`.${gridClasses.footerContainer}`)) == null ? void 0 : _querySelector2.offsetHeight) || 0;\n    if (normalizeOptions.hideToolbar) {\n      var _gridClone$querySelec;\n      (_gridClone$querySelec = gridClone.querySelector(`.${gridClasses.toolbarContainer}`)) == null || _gridClone$querySelec.remove();\n      gridToolbarElementHeight = 0;\n    }\n    if (normalizeOptions.hideFooter) {\n      var _gridClone$querySelec2;\n      (_gridClone$querySelec2 = gridClone.querySelector(`.${gridClasses.footerContainer}`)) == null || _gridClone$querySelec2.remove();\n      gridFooterElementHeight = 0;\n    }\n\n    // Expand container height to accommodate all rows\n    const computedTotalHeight = rowsMeta.currentPageTotalHeight + getTotalHeaderHeight(apiRef, props.columnHeaderHeight) + gridToolbarElementHeight + gridFooterElementHeight;\n    gridClone.style.height = `${computedTotalHeight}px`;\n    // The height above does not include grid border width, so we need to exclude it\n    gridClone.style.boxSizing = 'content-box';\n\n    // the footer is always being placed at the bottom of the page as if all rows are exported\n    // so if getRowsToExport is being used to only export a subset of rows then we need to\n    // adjust the footer position to be correctly placed at the bottom of the grid\n    if (options != null && options.getRowsToExport) {\n      const gridFooterElement = gridClone.querySelector(`.${gridClasses.footerContainer}`);\n      gridFooterElement.style.position = 'absolute';\n      gridFooterElement.style.width = '100%';\n      gridFooterElement.style.top = `${computedTotalHeight - gridFooterElementHeight}px`;\n    }\n\n    // printDoc.body.appendChild(gridClone); should be enough but a clone isolation bug in Safari\n    // prevents us to do it\n    const container = document.createElement('div');\n    container.appendChild(gridClone);\n    printDoc.body.innerHTML = container.innerHTML;\n    const defaultPageStyle = typeof normalizeOptions.pageStyle === 'function' ? normalizeOptions.pageStyle() : normalizeOptions.pageStyle;\n    if (typeof defaultPageStyle === 'string') {\n      // TODO custom styles should always win\n      const styleElement = printDoc.createElement('style');\n      styleElement.appendChild(printDoc.createTextNode(defaultPageStyle));\n      printDoc.head.appendChild(styleElement);\n    }\n    if (normalizeOptions.bodyClassName) {\n      printDoc.body.classList.add(...normalizeOptions.bodyClassName.split(' '));\n    }\n    const stylesheetLoadPromises = [];\n    if (normalizeOptions.copyStyles) {\n      const rootCandidate = gridRootElement.getRootNode();\n      const root = rootCandidate.constructor.name === 'ShadowRoot' ? rootCandidate : doc.current;\n      const headStyleElements = root.querySelectorAll(\"style, link[rel='stylesheet']\");\n      for (let i = 0; i < headStyleElements.length; i += 1) {\n        const node = headStyleElements[i];\n        if (node.tagName === 'STYLE') {\n          const newHeadStyleElements = printDoc.createElement(node.tagName);\n          const sheet = node.sheet;\n          if (sheet) {\n            let styleCSS = '';\n            // NOTE: for-of is not supported by IE\n            for (let j = 0; j < sheet.cssRules.length; j += 1) {\n              if (typeof sheet.cssRules[j].cssText === 'string') {\n                styleCSS += `${sheet.cssRules[j].cssText}\\r\\n`;\n              }\n            }\n            newHeadStyleElements.appendChild(printDoc.createTextNode(styleCSS));\n            printDoc.head.appendChild(newHeadStyleElements);\n          }\n        } else if (node.getAttribute('href')) {\n          // If `href` tag is empty, avoid loading these links\n\n          const newHeadStyleElements = printDoc.createElement(node.tagName);\n          for (let j = 0; j < node.attributes.length; j += 1) {\n            const attr = node.attributes[j];\n            if (attr) {\n              newHeadStyleElements.setAttribute(attr.nodeName, attr.nodeValue || '');\n            }\n          }\n          stylesheetLoadPromises.push(new Promise(resolve => {\n            newHeadStyleElements.addEventListener('load', () => resolve());\n          }));\n          printDoc.head.appendChild(newHeadStyleElements);\n        }\n      }\n    }\n\n    // Trigger print\n    if (process.env.NODE_ENV !== 'test') {\n      // wait for remote stylesheets to load\n      Promise.all(stylesheetLoadPromises).then(() => {\n        printWindow.contentWindow.print();\n      });\n    }\n  }, [apiRef, doc, props.columnHeaderHeight]);\n  const handlePrintWindowAfterPrint = React.useCallback(printWindow => {\n    var _previousGridState$cu;\n    // Remove the print iframe\n    doc.current.body.removeChild(printWindow);\n\n    // Revert grid to previous state\n    apiRef.current.restoreState(previousGridState.current || {});\n    if (!((_previousGridState$cu = previousGridState.current) != null && (_previousGridState$cu = _previousGridState$cu.columns) != null && _previousGridState$cu.columnVisibilityModel)) {\n      // if the apiRef.current.exportState(); did not exported the column visibility, we update it\n      apiRef.current.setColumnVisibilityModel(previousColumnVisibility.current);\n    }\n    apiRef.current.unstable_setVirtualization(true);\n    apiRef.current.setRows(previousRows.current);\n\n    // Clear local state\n    previousGridState.current = null;\n    previousColumnVisibility.current = {};\n    previousRows.current = [];\n  }, [apiRef]);\n  const exportDataAsPrint = React.useCallback(async options => {\n    logger.debug(`Export data as Print`);\n    if (!apiRef.current.rootElementRef.current) {\n      throw new Error('MUI: No grid root element available.');\n    }\n    previousGridState.current = apiRef.current.exportState();\n    // It appends that the visibility model is not exported, especially if columnVisibility is not controlled\n    previousColumnVisibility.current = gridColumnVisibilityModelSelector(apiRef);\n    const gridRowsLookup = gridRowsLookupSelector(apiRef);\n    previousRows.current = gridDataRowIdsSelector(apiRef).map(rowId => gridRowsLookup[rowId]);\n    if (props.pagination) {\n      const visibleRowCount = gridExpandedRowCountSelector(apiRef);\n      const paginationModel = {\n        page: 0,\n        pageSize: visibleRowCount\n      };\n      apiRef.current.setState(state => _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationModel: getDerivedPaginationModel(state.pagination,\n          // Using signature `DataGridPro` to allow more than 100 rows in the print export\n          'DataGridPro', paginationModel)\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n    await updateGridColumnsForPrint(options == null ? void 0 : options.fields, options == null ? void 0 : options.allColumns, options == null ? void 0 : options.includeCheckboxes);\n    if (options != null && options.getRowsToExport) {\n      updateGridRowsForPrint(options.getRowsToExport);\n    }\n    apiRef.current.unstable_setVirtualization(false);\n    await raf(); // wait for the state changes to take action\n    const printWindow = buildPrintWindow(options == null ? void 0 : options.fileName);\n    if (process.env.NODE_ENV === 'test') {\n      doc.current.body.appendChild(printWindow);\n      // In test env, run the all pipeline without waiting for loading\n      handlePrintWindowLoad(printWindow, options);\n      handlePrintWindowAfterPrint(printWindow);\n    } else {\n      printWindow.onload = () => {\n        handlePrintWindowLoad(printWindow, options);\n        const mediaQueryList = printWindow.contentWindow.matchMedia('print');\n        mediaQueryList.addEventListener('change', mql => {\n          const isAfterPrint = mql.matches === false;\n          if (isAfterPrint) {\n            handlePrintWindowAfterPrint(printWindow);\n          }\n        });\n      };\n      doc.current.body.appendChild(printWindow);\n    }\n  }, [props, logger, apiRef, handlePrintWindowLoad, handlePrintWindowAfterPrint, updateGridColumnsForPrint, updateGridRowsForPrint]);\n  const printExportApi = {\n    exportDataAsPrint\n  };\n  useGridApiMethod(apiRef, printExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    var _options$printOptions;\n    if ((_options$printOptions = options.printOptions) != null && _options$printOptions.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridPrintExportMenuItem, {\n        options: options.printOptions\n      }),\n      componentName: 'printExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "useGridLogger", "gridExpandedRowCountSelector", "gridColumnDefinitionsSelector", "gridColumnVisibilityModelSelector", "gridClasses", "useGridApiMethod", "gridRowsMetaSelector", "getColumnsToExport", "getDerivedPaginationModel", "useGridRegisterPipeProcessor", "GridPrintExportMenuItem", "getTotalHeaderHeight", "GRID_CHECKBOX_SELECTION_COL_DEF", "gridDataRowIdsSelector", "gridRowsLookupSelector", "jsx", "_jsx", "raf", "Promise", "resolve", "requestAnimationFrame", "buildPrintWindow", "title", "iframeEl", "document", "createElement", "style", "position", "width", "height", "useGridPrintExport", "apiRef", "props", "logger", "doc", "useRef", "previousGridState", "previousColumnVisibility", "previousRows", "useEffect", "current", "rootElementRef", "updateGridColumnsForPrint", "useCallback", "fields", "allColumns", "includeCheckboxes", "exportedColumnFields", "options", "map", "column", "field", "columns", "newColumnVisibilityModel", "for<PERSON>ach", "includes", "setColumnVisibilityModel", "updateGridRowsForPrint", "getRowsToExport", "rowsToExportIds", "newRows", "id", "getRow", "setRows", "handlePrintWindowLoad", "printWindow", "_querySelector", "_querySelector2", "normalizeOptions", "copyStyles", "hideToolbar", "hideFooter", "printDoc", "contentDocument", "rowsMeta", "state", "gridRootElement", "gridClone", "cloneNode", "gridMain", "querySelector", "main", "overflow", "contain", "columnHeaders", "columnHeadersInner", "gridToolbarElementHeight", "toolbarContainer", "offsetHeight", "gridFooterElementHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_gridClone$querySelec", "remove", "_gridClone$querySelec2", "computedTotalHeight", "currentPageTotalHeight", "columnHeaderHeight", "boxSizing", "gridFooterElement", "top", "container", "append<PERSON><PERSON><PERSON>", "body", "innerHTML", "defaultPageStyle", "pageStyle", "styleElement", "createTextNode", "head", "bodyClassName", "classList", "add", "split", "stylesheetLoadPromises", "rootCandidate", "getRootNode", "root", "constructor", "name", "headStyleElements", "querySelectorAll", "i", "length", "node", "tagName", "newHeadStyleElements", "sheet", "styleCSS", "j", "cssRules", "cssText", "getAttribute", "attributes", "attr", "setAttribute", "nodeName", "nodeValue", "push", "addEventListener", "process", "env", "NODE_ENV", "all", "then", "contentWindow", "print", "handlePrintWindowAfterPrint", "_previousGridState$cu", "<PERSON><PERSON><PERSON><PERSON>", "restoreState", "columnVisibilityModel", "unstable_setVirtualization", "exportDataAsPrint", "debug", "Error", "exportState", "gridRowsLookup", "rowId", "pagination", "visibleRowCount", "paginationModel", "page", "pageSize", "setState", "forceUpdate", "fileName", "onload", "mediaQueryList", "matchMedia", "mql", "isAfterPrint", "matches", "printExportApi", "addExportMenuButtons", "initialValue", "_options$printOptions", "printOptions", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "componentName"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/features/export/useGridPrintExport.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument } from '@mui/utils';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridExpandedRowCountSelector } from '../filter/gridFilterSelector';\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector } from '../columns/gridColumnsSelector';\nimport { gridClasses } from '../../../constants/gridClasses';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getColumnsToExport } from './utils';\nimport { getDerivedPaginationModel } from '../pagination/useGridPaginationModel';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { GridPrintExportMenuItem } from '../../../components/toolbar/GridToolbarExport';\nimport { getTotalHeaderHeight } from '../columns/gridColumnsUtils';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../colDef/gridCheckboxSelectionColDef';\nimport { gridDataRowIdsSelector, gridRowsLookupSelector } from '../rows/gridRowsSelector';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction raf() {\n  return new Promise(resolve => {\n    requestAnimationFrame(() => {\n      resolve();\n    });\n  });\n}\nfunction buildPrintWindow(title) {\n  const iframeEl = document.createElement('iframe');\n  iframeEl.style.position = 'absolute';\n  iframeEl.style.width = '0px';\n  iframeEl.style.height = '0px';\n  iframeEl.title = title || document.title;\n  return iframeEl;\n}\n\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridPrintExport = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPrintExport');\n  const doc = React.useRef(null);\n  const previousGridState = React.useRef(null);\n  const previousColumnVisibility = React.useRef({});\n  const previousRows = React.useRef([]);\n  React.useEffect(() => {\n    doc.current = ownerDocument(apiRef.current.rootElementRef.current);\n  }, [apiRef]);\n\n  // Returns a promise because updateColumns triggers state update and\n  // the new state needs to be in place before the grid can be sized correctly\n  const updateGridColumnsForPrint = React.useCallback((fields, allColumns, includeCheckboxes) => new Promise(resolve => {\n    const exportedColumnFields = getColumnsToExport({\n      apiRef,\n      options: {\n        fields,\n        allColumns\n      }\n    }).map(column => column.field);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newColumnVisibilityModel = {};\n    columns.forEach(column => {\n      newColumnVisibilityModel[column.field] = exportedColumnFields.includes(column.field);\n    });\n    if (includeCheckboxes) {\n      newColumnVisibilityModel[GRID_CHECKBOX_SELECTION_COL_DEF.field] = true;\n    }\n    apiRef.current.setColumnVisibilityModel(newColumnVisibilityModel);\n    resolve();\n  }), [apiRef]);\n  const updateGridRowsForPrint = React.useCallback(getRowsToExport => {\n    const rowsToExportIds = getRowsToExport({\n      apiRef\n    });\n    const newRows = rowsToExportIds.map(id => apiRef.current.getRow(id));\n    apiRef.current.setRows(newRows);\n  }, [apiRef]);\n  const handlePrintWindowLoad = React.useCallback((printWindow, options) => {\n    var _querySelector, _querySelector2;\n    const normalizeOptions = _extends({\n      copyStyles: true,\n      hideToolbar: false,\n      hideFooter: false,\n      includeCheckboxes: false\n    }, options);\n    const printDoc = printWindow.contentDocument;\n    if (!printDoc) {\n      return;\n    }\n    const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n    const gridRootElement = apiRef.current.rootElementRef.current;\n    const gridClone = gridRootElement.cloneNode(true);\n\n    // Allow to overflow to not hide the border of the last row\n    const gridMain = gridClone.querySelector(`.${gridClasses.main}`);\n    gridMain.style.overflow = 'visible';\n\n    // See https://support.google.com/chrome/thread/191619088?hl=en&msgid=193009642\n    gridClone.style.contain = 'size';\n    const columnHeaders = gridClone.querySelector(`.${gridClasses.columnHeaders}`);\n    const columnHeadersInner = columnHeaders.querySelector(`.${gridClasses.columnHeadersInner}`);\n    columnHeadersInner.style.width = '100%';\n    let gridToolbarElementHeight = ((_querySelector = gridRootElement.querySelector(`.${gridClasses.toolbarContainer}`)) == null ? void 0 : _querySelector.offsetHeight) || 0;\n    let gridFooterElementHeight = ((_querySelector2 = gridRootElement.querySelector(`.${gridClasses.footerContainer}`)) == null ? void 0 : _querySelector2.offsetHeight) || 0;\n    if (normalizeOptions.hideToolbar) {\n      var _gridClone$querySelec;\n      (_gridClone$querySelec = gridClone.querySelector(`.${gridClasses.toolbarContainer}`)) == null || _gridClone$querySelec.remove();\n      gridToolbarElementHeight = 0;\n    }\n    if (normalizeOptions.hideFooter) {\n      var _gridClone$querySelec2;\n      (_gridClone$querySelec2 = gridClone.querySelector(`.${gridClasses.footerContainer}`)) == null || _gridClone$querySelec2.remove();\n      gridFooterElementHeight = 0;\n    }\n\n    // Expand container height to accommodate all rows\n    const computedTotalHeight = rowsMeta.currentPageTotalHeight + getTotalHeaderHeight(apiRef, props.columnHeaderHeight) + gridToolbarElementHeight + gridFooterElementHeight;\n    gridClone.style.height = `${computedTotalHeight}px`;\n    // The height above does not include grid border width, so we need to exclude it\n    gridClone.style.boxSizing = 'content-box';\n\n    // the footer is always being placed at the bottom of the page as if all rows are exported\n    // so if getRowsToExport is being used to only export a subset of rows then we need to\n    // adjust the footer position to be correctly placed at the bottom of the grid\n    if (options != null && options.getRowsToExport) {\n      const gridFooterElement = gridClone.querySelector(`.${gridClasses.footerContainer}`);\n      gridFooterElement.style.position = 'absolute';\n      gridFooterElement.style.width = '100%';\n      gridFooterElement.style.top = `${computedTotalHeight - gridFooterElementHeight}px`;\n    }\n\n    // printDoc.body.appendChild(gridClone); should be enough but a clone isolation bug in Safari\n    // prevents us to do it\n    const container = document.createElement('div');\n    container.appendChild(gridClone);\n    printDoc.body.innerHTML = container.innerHTML;\n    const defaultPageStyle = typeof normalizeOptions.pageStyle === 'function' ? normalizeOptions.pageStyle() : normalizeOptions.pageStyle;\n    if (typeof defaultPageStyle === 'string') {\n      // TODO custom styles should always win\n      const styleElement = printDoc.createElement('style');\n      styleElement.appendChild(printDoc.createTextNode(defaultPageStyle));\n      printDoc.head.appendChild(styleElement);\n    }\n    if (normalizeOptions.bodyClassName) {\n      printDoc.body.classList.add(...normalizeOptions.bodyClassName.split(' '));\n    }\n    const stylesheetLoadPromises = [];\n    if (normalizeOptions.copyStyles) {\n      const rootCandidate = gridRootElement.getRootNode();\n      const root = rootCandidate.constructor.name === 'ShadowRoot' ? rootCandidate : doc.current;\n      const headStyleElements = root.querySelectorAll(\"style, link[rel='stylesheet']\");\n      for (let i = 0; i < headStyleElements.length; i += 1) {\n        const node = headStyleElements[i];\n        if (node.tagName === 'STYLE') {\n          const newHeadStyleElements = printDoc.createElement(node.tagName);\n          const sheet = node.sheet;\n          if (sheet) {\n            let styleCSS = '';\n            // NOTE: for-of is not supported by IE\n            for (let j = 0; j < sheet.cssRules.length; j += 1) {\n              if (typeof sheet.cssRules[j].cssText === 'string') {\n                styleCSS += `${sheet.cssRules[j].cssText}\\r\\n`;\n              }\n            }\n            newHeadStyleElements.appendChild(printDoc.createTextNode(styleCSS));\n            printDoc.head.appendChild(newHeadStyleElements);\n          }\n        } else if (node.getAttribute('href')) {\n          // If `href` tag is empty, avoid loading these links\n\n          const newHeadStyleElements = printDoc.createElement(node.tagName);\n          for (let j = 0; j < node.attributes.length; j += 1) {\n            const attr = node.attributes[j];\n            if (attr) {\n              newHeadStyleElements.setAttribute(attr.nodeName, attr.nodeValue || '');\n            }\n          }\n          stylesheetLoadPromises.push(new Promise(resolve => {\n            newHeadStyleElements.addEventListener('load', () => resolve());\n          }));\n          printDoc.head.appendChild(newHeadStyleElements);\n        }\n      }\n    }\n\n    // Trigger print\n    if (process.env.NODE_ENV !== 'test') {\n      // wait for remote stylesheets to load\n      Promise.all(stylesheetLoadPromises).then(() => {\n        printWindow.contentWindow.print();\n      });\n    }\n  }, [apiRef, doc, props.columnHeaderHeight]);\n  const handlePrintWindowAfterPrint = React.useCallback(printWindow => {\n    var _previousGridState$cu;\n    // Remove the print iframe\n    doc.current.body.removeChild(printWindow);\n\n    // Revert grid to previous state\n    apiRef.current.restoreState(previousGridState.current || {});\n    if (!((_previousGridState$cu = previousGridState.current) != null && (_previousGridState$cu = _previousGridState$cu.columns) != null && _previousGridState$cu.columnVisibilityModel)) {\n      // if the apiRef.current.exportState(); did not exported the column visibility, we update it\n      apiRef.current.setColumnVisibilityModel(previousColumnVisibility.current);\n    }\n    apiRef.current.unstable_setVirtualization(true);\n    apiRef.current.setRows(previousRows.current);\n\n    // Clear local state\n    previousGridState.current = null;\n    previousColumnVisibility.current = {};\n    previousRows.current = [];\n  }, [apiRef]);\n  const exportDataAsPrint = React.useCallback(async options => {\n    logger.debug(`Export data as Print`);\n    if (!apiRef.current.rootElementRef.current) {\n      throw new Error('MUI: No grid root element available.');\n    }\n    previousGridState.current = apiRef.current.exportState();\n    // It appends that the visibility model is not exported, especially if columnVisibility is not controlled\n    previousColumnVisibility.current = gridColumnVisibilityModelSelector(apiRef);\n    const gridRowsLookup = gridRowsLookupSelector(apiRef);\n    previousRows.current = gridDataRowIdsSelector(apiRef).map(rowId => gridRowsLookup[rowId]);\n    if (props.pagination) {\n      const visibleRowCount = gridExpandedRowCountSelector(apiRef);\n      const paginationModel = {\n        page: 0,\n        pageSize: visibleRowCount\n      };\n      apiRef.current.setState(state => _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationModel: getDerivedPaginationModel(state.pagination,\n          // Using signature `DataGridPro` to allow more than 100 rows in the print export\n          'DataGridPro', paginationModel)\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n    await updateGridColumnsForPrint(options == null ? void 0 : options.fields, options == null ? void 0 : options.allColumns, options == null ? void 0 : options.includeCheckboxes);\n    if (options != null && options.getRowsToExport) {\n      updateGridRowsForPrint(options.getRowsToExport);\n    }\n    apiRef.current.unstable_setVirtualization(false);\n    await raf(); // wait for the state changes to take action\n    const printWindow = buildPrintWindow(options == null ? void 0 : options.fileName);\n    if (process.env.NODE_ENV === 'test') {\n      doc.current.body.appendChild(printWindow);\n      // In test env, run the all pipeline without waiting for loading\n      handlePrintWindowLoad(printWindow, options);\n      handlePrintWindowAfterPrint(printWindow);\n    } else {\n      printWindow.onload = () => {\n        handlePrintWindowLoad(printWindow, options);\n        const mediaQueryList = printWindow.contentWindow.matchMedia('print');\n        mediaQueryList.addEventListener('change', mql => {\n          const isAfterPrint = mql.matches === false;\n          if (isAfterPrint) {\n            handlePrintWindowAfterPrint(printWindow);\n          }\n        });\n      };\n      doc.current.body.appendChild(printWindow);\n    }\n  }, [props, logger, apiRef, handlePrintWindowLoad, handlePrintWindowAfterPrint, updateGridColumnsForPrint, updateGridRowsForPrint]);\n  const printExportApi = {\n    exportDataAsPrint\n  };\n  useGridApiMethod(apiRef, printExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    var _options$printOptions;\n    if ((_options$printOptions = options.printOptions) != null && _options$printOptions.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridPrintExportMenuItem, {\n        options: options.printOptions\n      }),\n      componentName: 'printExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,4BAA4B,QAAQ,8BAA8B;AAC3E,SAASC,6BAA6B,EAAEC,iCAAiC,QAAQ,gCAAgC;AACjH,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,kBAAkB,QAAQ,SAAS;AAC5C,SAASC,yBAAyB,QAAQ,sCAAsC;AAChF,SAASC,4BAA4B,QAAQ,2BAA2B;AACxE,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,+BAA+B,QAAQ,6CAA6C;AAC7F,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,0BAA0B;AACzF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,GAAGA,CAAA,EAAG;EACb,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5BC,qBAAqB,CAAC,MAAM;MAC1BD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASE,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EACjDF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,UAAU;EACpCJ,QAAQ,CAACG,KAAK,CAACE,KAAK,GAAG,KAAK;EAC5BL,QAAQ,CAACG,KAAK,CAACG,MAAM,GAAG,KAAK;EAC7BN,QAAQ,CAACD,KAAK,GAAGA,KAAK,IAAIE,QAAQ,CAACF,KAAK;EACxC,OAAOC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACnD,MAAMC,MAAM,GAAGjC,aAAa,CAAC+B,MAAM,EAAE,oBAAoB,CAAC;EAC1D,MAAMG,GAAG,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,iBAAiB,GAAGvC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAME,wBAAwB,GAAGxC,KAAK,CAACsC,MAAM,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMG,YAAY,GAAGzC,KAAK,CAACsC,MAAM,CAAC,EAAE,CAAC;EACrCtC,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpBL,GAAG,CAACM,OAAO,GAAGzC,aAAa,CAACgC,MAAM,CAACS,OAAO,CAACC,cAAc,CAACD,OAAO,CAAC;EACpE,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA,MAAMW,yBAAyB,GAAG7C,KAAK,CAAC8C,WAAW,CAAC,CAACC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,KAAK,IAAI5B,OAAO,CAACC,OAAO,IAAI;IACpH,MAAM4B,oBAAoB,GAAGxC,kBAAkB,CAAC;MAC9CwB,MAAM;MACNiB,OAAO,EAAE;QACPJ,MAAM;QACNC;MACF;IACF,CAAC,CAAC,CAACI,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;IAC9B,MAAMC,OAAO,GAAGlD,6BAA6B,CAAC6B,MAAM,CAAC;IACrD,MAAMsB,wBAAwB,GAAG,CAAC,CAAC;IACnCD,OAAO,CAACE,OAAO,CAACJ,MAAM,IAAI;MACxBG,wBAAwB,CAACH,MAAM,CAACC,KAAK,CAAC,GAAGJ,oBAAoB,CAACQ,QAAQ,CAACL,MAAM,CAACC,KAAK,CAAC;IACtF,CAAC,CAAC;IACF,IAAIL,iBAAiB,EAAE;MACrBO,wBAAwB,CAACzC,+BAA+B,CAACuC,KAAK,CAAC,GAAG,IAAI;IACxE;IACApB,MAAM,CAACS,OAAO,CAACgB,wBAAwB,CAACH,wBAAwB,CAAC;IACjElC,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,EAAE,CAACY,MAAM,CAAC,CAAC;EACb,MAAM0B,sBAAsB,GAAG5D,KAAK,CAAC8C,WAAW,CAACe,eAAe,IAAI;IAClE,MAAMC,eAAe,GAAGD,eAAe,CAAC;MACtC3B;IACF,CAAC,CAAC;IACF,MAAM6B,OAAO,GAAGD,eAAe,CAACV,GAAG,CAACY,EAAE,IAAI9B,MAAM,CAACS,OAAO,CAACsB,MAAM,CAACD,EAAE,CAAC,CAAC;IACpE9B,MAAM,CAACS,OAAO,CAACuB,OAAO,CAACH,OAAO,CAAC;EACjC,CAAC,EAAE,CAAC7B,MAAM,CAAC,CAAC;EACZ,MAAMiC,qBAAqB,GAAGnE,KAAK,CAAC8C,WAAW,CAAC,CAACsB,WAAW,EAAEjB,OAAO,KAAK;IACxE,IAAIkB,cAAc,EAAEC,eAAe;IACnC,MAAMC,gBAAgB,GAAGxE,QAAQ,CAAC;MAChCyE,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBzB,iBAAiB,EAAE;IACrB,CAAC,EAAEE,OAAO,CAAC;IACX,MAAMwB,QAAQ,GAAGP,WAAW,CAACQ,eAAe;IAC5C,IAAI,CAACD,QAAQ,EAAE;MACb;IACF;IACA,MAAME,QAAQ,GAAGpE,oBAAoB,CAACyB,MAAM,CAACS,OAAO,CAACmC,KAAK,CAAC;IAC3D,MAAMC,eAAe,GAAG7C,MAAM,CAACS,OAAO,CAACC,cAAc,CAACD,OAAO;IAC7D,MAAMqC,SAAS,GAAGD,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;;IAEjD;IACA,MAAMC,QAAQ,GAAGF,SAAS,CAACG,aAAa,CAAC,IAAI5E,WAAW,CAAC6E,IAAI,EAAE,CAAC;IAChEF,QAAQ,CAACrD,KAAK,CAACwD,QAAQ,GAAG,SAAS;;IAEnC;IACAL,SAAS,CAACnD,KAAK,CAACyD,OAAO,GAAG,MAAM;IAChC,MAAMC,aAAa,GAAGP,SAAS,CAACG,aAAa,CAAC,IAAI5E,WAAW,CAACgF,aAAa,EAAE,CAAC;IAC9E,MAAMC,kBAAkB,GAAGD,aAAa,CAACJ,aAAa,CAAC,IAAI5E,WAAW,CAACiF,kBAAkB,EAAE,CAAC;IAC5FA,kBAAkB,CAAC3D,KAAK,CAACE,KAAK,GAAG,MAAM;IACvC,IAAI0D,wBAAwB,GAAG,CAAC,CAACpB,cAAc,GAAGU,eAAe,CAACI,aAAa,CAAC,IAAI5E,WAAW,CAACmF,gBAAgB,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGrB,cAAc,CAACsB,YAAY,KAAK,CAAC;IACzK,IAAIC,uBAAuB,GAAG,CAAC,CAACtB,eAAe,GAAGS,eAAe,CAACI,aAAa,CAAC,IAAI5E,WAAW,CAACsF,eAAe,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGvB,eAAe,CAACqB,YAAY,KAAK,CAAC;IACzK,IAAIpB,gBAAgB,CAACE,WAAW,EAAE;MAChC,IAAIqB,qBAAqB;MACzB,CAACA,qBAAqB,GAAGd,SAAS,CAACG,aAAa,CAAC,IAAI5E,WAAW,CAACmF,gBAAgB,EAAE,CAAC,KAAK,IAAI,IAAII,qBAAqB,CAACC,MAAM,CAAC,CAAC;MAC/HN,wBAAwB,GAAG,CAAC;IAC9B;IACA,IAAIlB,gBAAgB,CAACG,UAAU,EAAE;MAC/B,IAAIsB,sBAAsB;MAC1B,CAACA,sBAAsB,GAAGhB,SAAS,CAACG,aAAa,CAAC,IAAI5E,WAAW,CAACsF,eAAe,EAAE,CAAC,KAAK,IAAI,IAAIG,sBAAsB,CAACD,MAAM,CAAC,CAAC;MAChIH,uBAAuB,GAAG,CAAC;IAC7B;;IAEA;IACA,MAAMK,mBAAmB,GAAGpB,QAAQ,CAACqB,sBAAsB,GAAGpF,oBAAoB,CAACoB,MAAM,EAAEC,KAAK,CAACgE,kBAAkB,CAAC,GAAGV,wBAAwB,GAAGG,uBAAuB;IACzKZ,SAAS,CAACnD,KAAK,CAACG,MAAM,GAAG,GAAGiE,mBAAmB,IAAI;IACnD;IACAjB,SAAS,CAACnD,KAAK,CAACuE,SAAS,GAAG,aAAa;;IAEzC;IACA;IACA;IACA,IAAIjD,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACU,eAAe,EAAE;MAC9C,MAAMwC,iBAAiB,GAAGrB,SAAS,CAACG,aAAa,CAAC,IAAI5E,WAAW,CAACsF,eAAe,EAAE,CAAC;MACpFQ,iBAAiB,CAACxE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAC7CuE,iBAAiB,CAACxE,KAAK,CAACE,KAAK,GAAG,MAAM;MACtCsE,iBAAiB,CAACxE,KAAK,CAACyE,GAAG,GAAG,GAAGL,mBAAmB,GAAGL,uBAAuB,IAAI;IACpF;;IAEA;IACA;IACA,MAAMW,SAAS,GAAG5E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC/C2E,SAAS,CAACC,WAAW,CAACxB,SAAS,CAAC;IAChCL,QAAQ,CAAC8B,IAAI,CAACC,SAAS,GAAGH,SAAS,CAACG,SAAS;IAC7C,MAAMC,gBAAgB,GAAG,OAAOpC,gBAAgB,CAACqC,SAAS,KAAK,UAAU,GAAGrC,gBAAgB,CAACqC,SAAS,CAAC,CAAC,GAAGrC,gBAAgB,CAACqC,SAAS;IACrI,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;MACxC;MACA,MAAME,YAAY,GAAGlC,QAAQ,CAAC/C,aAAa,CAAC,OAAO,CAAC;MACpDiF,YAAY,CAACL,WAAW,CAAC7B,QAAQ,CAACmC,cAAc,CAACH,gBAAgB,CAAC,CAAC;MACnEhC,QAAQ,CAACoC,IAAI,CAACP,WAAW,CAACK,YAAY,CAAC;IACzC;IACA,IAAItC,gBAAgB,CAACyC,aAAa,EAAE;MAClCrC,QAAQ,CAAC8B,IAAI,CAACQ,SAAS,CAACC,GAAG,CAAC,GAAG3C,gBAAgB,CAACyC,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3E;IACA,MAAMC,sBAAsB,GAAG,EAAE;IACjC,IAAI7C,gBAAgB,CAACC,UAAU,EAAE;MAC/B,MAAM6C,aAAa,GAAGtC,eAAe,CAACuC,WAAW,CAAC,CAAC;MACnD,MAAMC,IAAI,GAAGF,aAAa,CAACG,WAAW,CAACC,IAAI,KAAK,YAAY,GAAGJ,aAAa,GAAGhF,GAAG,CAACM,OAAO;MAC1F,MAAM+E,iBAAiB,GAAGH,IAAI,CAACI,gBAAgB,CAAC,+BAA+B,CAAC;MAChF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACpD,MAAME,IAAI,GAAGJ,iBAAiB,CAACE,CAAC,CAAC;QACjC,IAAIE,IAAI,CAACC,OAAO,KAAK,OAAO,EAAE;UAC5B,MAAMC,oBAAoB,GAAGrD,QAAQ,CAAC/C,aAAa,CAACkG,IAAI,CAACC,OAAO,CAAC;UACjE,MAAME,KAAK,GAAGH,IAAI,CAACG,KAAK;UACxB,IAAIA,KAAK,EAAE;YACT,IAAIC,QAAQ,GAAG,EAAE;YACjB;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,QAAQ,CAACP,MAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;cACjD,IAAI,OAAOF,KAAK,CAACG,QAAQ,CAACD,CAAC,CAAC,CAACE,OAAO,KAAK,QAAQ,EAAE;gBACjDH,QAAQ,IAAI,GAAGD,KAAK,CAACG,QAAQ,CAACD,CAAC,CAAC,CAACE,OAAO,MAAM;cAChD;YACF;YACAL,oBAAoB,CAACxB,WAAW,CAAC7B,QAAQ,CAACmC,cAAc,CAACoB,QAAQ,CAAC,CAAC;YACnEvD,QAAQ,CAACoC,IAAI,CAACP,WAAW,CAACwB,oBAAoB,CAAC;UACjD;QACF,CAAC,MAAM,IAAIF,IAAI,CAACQ,YAAY,CAAC,MAAM,CAAC,EAAE;UACpC;;UAEA,MAAMN,oBAAoB,GAAGrD,QAAQ,CAAC/C,aAAa,CAACkG,IAAI,CAACC,OAAO,CAAC;UACjE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACS,UAAU,CAACV,MAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;YAClD,MAAMK,IAAI,GAAGV,IAAI,CAACS,UAAU,CAACJ,CAAC,CAAC;YAC/B,IAAIK,IAAI,EAAE;cACRR,oBAAoB,CAACS,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEF,IAAI,CAACG,SAAS,IAAI,EAAE,CAAC;YACxE;UACF;UACAvB,sBAAsB,CAACwB,IAAI,CAAC,IAAIvH,OAAO,CAACC,OAAO,IAAI;YACjD0G,oBAAoB,CAACa,gBAAgB,CAAC,MAAM,EAAE,MAAMvH,OAAO,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC;UACHqD,QAAQ,CAACoC,IAAI,CAACP,WAAW,CAACwB,oBAAoB,CAAC;QACjD;MACF;IACF;;IAEA;IACA,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC;MACA3H,OAAO,CAAC4H,GAAG,CAAC7B,sBAAsB,CAAC,CAAC8B,IAAI,CAAC,MAAM;QAC7C9E,WAAW,CAAC+E,aAAa,CAACC,KAAK,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAClH,MAAM,EAAEG,GAAG,EAAEF,KAAK,CAACgE,kBAAkB,CAAC,CAAC;EAC3C,MAAMkD,2BAA2B,GAAGrJ,KAAK,CAAC8C,WAAW,CAACsB,WAAW,IAAI;IACnE,IAAIkF,qBAAqB;IACzB;IACAjH,GAAG,CAACM,OAAO,CAAC8D,IAAI,CAAC8C,WAAW,CAACnF,WAAW,CAAC;;IAEzC;IACAlC,MAAM,CAACS,OAAO,CAAC6G,YAAY,CAACjH,iBAAiB,CAACI,OAAO,IAAI,CAAC,CAAC,CAAC;IAC5D,IAAI,EAAE,CAAC2G,qBAAqB,GAAG/G,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAI,CAAC2G,qBAAqB,GAAGA,qBAAqB,CAAC/F,OAAO,KAAK,IAAI,IAAI+F,qBAAqB,CAACG,qBAAqB,CAAC,EAAE;MACpL;MACAvH,MAAM,CAACS,OAAO,CAACgB,wBAAwB,CAACnB,wBAAwB,CAACG,OAAO,CAAC;IAC3E;IACAT,MAAM,CAACS,OAAO,CAAC+G,0BAA0B,CAAC,IAAI,CAAC;IAC/CxH,MAAM,CAACS,OAAO,CAACuB,OAAO,CAACzB,YAAY,CAACE,OAAO,CAAC;;IAE5C;IACAJ,iBAAiB,CAACI,OAAO,GAAG,IAAI;IAChCH,wBAAwB,CAACG,OAAO,GAAG,CAAC,CAAC;IACrCF,YAAY,CAACE,OAAO,GAAG,EAAE;EAC3B,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;EACZ,MAAMyH,iBAAiB,GAAG3J,KAAK,CAAC8C,WAAW,CAAC,MAAMK,OAAO,IAAI;IAC3Df,MAAM,CAACwH,KAAK,CAAC,sBAAsB,CAAC;IACpC,IAAI,CAAC1H,MAAM,CAACS,OAAO,CAACC,cAAc,CAACD,OAAO,EAAE;MAC1C,MAAM,IAAIkH,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACAtH,iBAAiB,CAACI,OAAO,GAAGT,MAAM,CAACS,OAAO,CAACmH,WAAW,CAAC,CAAC;IACxD;IACAtH,wBAAwB,CAACG,OAAO,GAAGrC,iCAAiC,CAAC4B,MAAM,CAAC;IAC5E,MAAM6H,cAAc,GAAG9I,sBAAsB,CAACiB,MAAM,CAAC;IACrDO,YAAY,CAACE,OAAO,GAAG3B,sBAAsB,CAACkB,MAAM,CAAC,CAACkB,GAAG,CAAC4G,KAAK,IAAID,cAAc,CAACC,KAAK,CAAC,CAAC;IACzF,IAAI7H,KAAK,CAAC8H,UAAU,EAAE;MACpB,MAAMC,eAAe,GAAG9J,4BAA4B,CAAC8B,MAAM,CAAC;MAC5D,MAAMiI,eAAe,GAAG;QACtBC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAEH;MACZ,CAAC;MACDhI,MAAM,CAACS,OAAO,CAAC2H,QAAQ,CAACxF,KAAK,IAAI/E,QAAQ,CAAC,CAAC,CAAC,EAAE+E,KAAK,EAAE;QACnDmF,UAAU,EAAElK,QAAQ,CAAC,CAAC,CAAC,EAAE+E,KAAK,CAACmF,UAAU,EAAE;UACzCE,eAAe,EAAExJ,yBAAyB,CAACmE,KAAK,CAACmF,UAAU;UAC3D;UACA,aAAa,EAAEE,eAAe;QAChC,CAAC;MACH,CAAC,CAAC,CAAC;MACHjI,MAAM,CAACS,OAAO,CAAC4H,WAAW,CAAC,CAAC;IAC9B;IACA,MAAM1H,yBAAyB,CAACM,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACJ,MAAM,EAAEI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACH,UAAU,EAAEG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACF,iBAAiB,CAAC;IAC/K,IAAIE,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACU,eAAe,EAAE;MAC9CD,sBAAsB,CAACT,OAAO,CAACU,eAAe,CAAC;IACjD;IACA3B,MAAM,CAACS,OAAO,CAAC+G,0BAA0B,CAAC,KAAK,CAAC;IAChD,MAAMtI,GAAG,CAAC,CAAC,CAAC,CAAC;IACb,MAAMgD,WAAW,GAAG5C,gBAAgB,CAAC2B,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqH,QAAQ,CAAC;IACjF,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC3G,GAAG,CAACM,OAAO,CAAC8D,IAAI,CAACD,WAAW,CAACpC,WAAW,CAAC;MACzC;MACAD,qBAAqB,CAACC,WAAW,EAAEjB,OAAO,CAAC;MAC3CkG,2BAA2B,CAACjF,WAAW,CAAC;IAC1C,CAAC,MAAM;MACLA,WAAW,CAACqG,MAAM,GAAG,MAAM;QACzBtG,qBAAqB,CAACC,WAAW,EAAEjB,OAAO,CAAC;QAC3C,MAAMuH,cAAc,GAAGtG,WAAW,CAAC+E,aAAa,CAACwB,UAAU,CAAC,OAAO,CAAC;QACpED,cAAc,CAAC7B,gBAAgB,CAAC,QAAQ,EAAE+B,GAAG,IAAI;UAC/C,MAAMC,YAAY,GAAGD,GAAG,CAACE,OAAO,KAAK,KAAK;UAC1C,IAAID,YAAY,EAAE;YAChBxB,2BAA2B,CAACjF,WAAW,CAAC;UAC1C;QACF,CAAC,CAAC;MACJ,CAAC;MACD/B,GAAG,CAACM,OAAO,CAAC8D,IAAI,CAACD,WAAW,CAACpC,WAAW,CAAC;IAC3C;EACF,CAAC,EAAE,CAACjC,KAAK,EAAEC,MAAM,EAAEF,MAAM,EAAEiC,qBAAqB,EAAEkF,2BAA2B,EAAExG,yBAAyB,EAAEe,sBAAsB,CAAC,CAAC;EAClI,MAAMmH,cAAc,GAAG;IACrBpB;EACF,CAAC;EACDnJ,gBAAgB,CAAC0B,MAAM,EAAE6I,cAAc,EAAE,QAAQ,CAAC;;EAElD;AACF;AACA;EACE,MAAMC,oBAAoB,GAAGhL,KAAK,CAAC8C,WAAW,CAAC,CAACmI,YAAY,EAAE9H,OAAO,KAAK;IACxE,IAAI+H,qBAAqB;IACzB,IAAI,CAACA,qBAAqB,GAAG/H,OAAO,CAACgI,YAAY,KAAK,IAAI,IAAID,qBAAqB,CAACE,oBAAoB,EAAE;MACxG,OAAOH,YAAY;IACrB;IACA,OAAO,CAAC,GAAGA,YAAY,EAAE;MACvBI,SAAS,EAAE,aAAalK,IAAI,CAACN,uBAAuB,EAAE;QACpDsC,OAAO,EAAEA,OAAO,CAACgI;MACnB,CAAC,CAAC;MACFG,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN1K,4BAA4B,CAACsB,MAAM,EAAE,YAAY,EAAE8I,oBAAoB,CAAC;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}