{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridFooterPlaceholder() {\n  var _rootProps$slotProps;\n  const rootProps = useGridRootProps();\n  if (rootProps.hideFooter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.footer, _extends({}, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.footer));\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridRootProps", "jsx", "_jsx", "GridFooterPlaceholder", "_rootProps$slotProps", "rootProps", "hideFooter", "slots", "footer", "slotProps"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/base/GridFooterPlaceholder.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridFooterPlaceholder() {\n  var _rootProps$slotProps;\n  const rootProps = useGridRootProps();\n  if (rootProps.hideFooter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.footer, _extends({}, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.footer));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,IAAIC,oBAAoB;EACxB,MAAMC,SAAS,GAAGL,gBAAgB,CAAC,CAAC;EACpC,IAAIK,SAAS,CAACC,UAAU,EAAE;IACxB,OAAO,IAAI;EACb;EACA,OAAO,aAAaJ,IAAI,CAACG,SAAS,CAACE,KAAK,CAACC,MAAM,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACM,oBAAoB,GAAGC,SAAS,CAACI,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,oBAAoB,CAACI,MAAM,CAAC,CAAC;AAC7J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}