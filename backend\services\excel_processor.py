import os
import pandas as pd
import re
import logging
import traceback
from openpyxl import load_workbook
from datetime import datetime
import shutil
from backend.utils.excel_helpers import column_to_index

logger = logging.getLogger('commission_app')

class ExcelProcessor:
    def __init__(self):
        pass
        
    def extract_data(self, file_path, worksheet_name, start_col, end_col, per_hour_rate=None, cbu_car_hour_rate=None, commission_rate=None, wty_hour_rate=None, wty_commission_rate=None):
        """提取指定范围的数据"""
        try:
            logger.info(f"开始处理文件: {file_path}, 工作表: {worksheet_name}, 列范围: {start_col}-{end_col}")
            
            # 读取Excel文件 - 使用engine='openpyxl'以提高大文件的读取速度
            df = pd.read_excel(file_path, sheet_name=worksheet_name, engine='openpyxl')
            
            # 转换列字母为索引
            start_idx = column_to_index(start_col)
            end_idx = column_to_index(end_col)
            
            # 确保索引在有效范围内
            if start_idx >= len(df.columns) or end_idx >= len(df.columns):
                logger.error(f"列范围超出工作表范围: {start_idx}-{end_idx}, 最大列索引: {len(df.columns)-1}")
                return None, None, "列范围超出工作表范围"
            
            # 获取指定范围的数据
            df_subset = df.iloc[:, start_idx:end_idx+1]
            
            # 要查找的标题
            headers_to_find = [
                "NO", "DATE", "VEHICLE NO", "RO NO", "KM", "EGN OIL", "O/FLTER", 
                "ATF", "BT", "EGN FLUSH", "FR BR PAD", "R BR PAD", "D FILTER", 
                "P/SHAFT", "W/RUBBER", "SPARK PLUG", "OTHERS", "MAXCHECK", "REMARKS", "PRICE"
            ]
            
            # 查找标题行 - 优化标题行搜索
            header_row = None
            header_columns = {}
            
            # 预编译正则表达式以提高速度
            header_patterns = {header: re.compile(re.escape(header), re.IGNORECASE) for header in headers_to_find}
            
            # 如果Excel中的标题行是第7行，那么在Python中索引应该是6
            expected_header_row = 6  # 对应Excel中的第7行
            
            # 首先检查预期的标题行
            if expected_header_row < len(df_subset):
                row = df_subset.iloc[expected_header_row]
                found_headers = {}
                
                for col_idx, cell_value in enumerate(row):
                    if pd.notna(cell_value):  # 确保单元格不是NaN
                        cell_str = str(cell_value).strip()
                        for header, pattern in header_patterns.items():
                            if pattern.search(cell_str):
                                found_headers[header] = col_idx
                
                if found_headers and len(found_headers) >= 3:  # 如果找到足够多的标题
                    header_row = expected_header_row
                    header_columns = found_headers
                    logger.info(f"在预期的第7行(索引{expected_header_row})找到标题: {list(found_headers.keys())}")
            
            # 如果预期行没有找到足够的标题，再搜索其他行
            if header_row is None:
                for i in range(len(df_subset)):
                    if i == expected_header_row:  # 已经检查过了
                        continue
                        
                    row = df_subset.iloc[i]
                    found_headers = {}
                    
                    for col_idx, cell_value in enumerate(row):
                        if pd.notna(cell_value):  # 确保单元格不是NaN
                            cell_str = str(cell_value).strip()
                            for header, pattern in header_patterns.items():
                                if pattern.search(cell_str):
                                    found_headers[header] = col_idx
                    
                    # 如果找到至少一个标题，记录这一行
                    if found_headers:
                        # 如果这是第一次找到标题，或者找到的标题比之前更多，或者找到了关键列
                        is_better_match = len(found_headers) > len(header_columns)
                        has_key_columns = "MAXCHECK" in found_headers and "REMARKS" in found_headers
                        
                        if header_row is None or is_better_match or has_key_columns:
                            header_row = i
                            header_columns = found_headers
                            # 如果找到了所有关键列，可以提前结束搜索
                            if "MAXCHECK" in header_columns and "REMARKS" in header_columns and "PRICE" in header_columns:
                                break
            
            if header_row is None:
                logger.error("未找到包含所需标题的行")
                return None, None, "未找到包含所需标题的行"
            
            logger.info(f"最终选择的标题行: {header_row}")
            
            # 获取标题行和列索引
            header_df = df_subset.iloc[header_row]
            
            # 创建一个新的DataFrame，只包含我们需要的列
            result_columns = {}
            for target_header in headers_to_find:
                for col_idx, col_name in enumerate(header_df):
                    if pd.notna(col_name) and header_patterns[target_header].search(str(col_name)):
                        result_columns[target_header] = col_idx
                        break
            
            if not result_columns:
                logger.error("未找到任何目标列")
                return None, None, "未找到任何目标列"
            
            # 创建结果DataFrame - 优化为一次性提取所有列
            result_df = pd.DataFrame()
            
            # 从标题行的下一行开始提取数据
            data_start_row = header_row + 1
            
            # 一次性提取所有列数据
            for header, col_idx in result_columns.items():
                column_data = df_subset.iloc[data_start_row:, col_idx].reset_index(drop=True)

                # 如果是DATE列，转换为只有日期的格式
                if header == "DATE" and len(column_data) > 0:
                    # 尝试将日期时间转换为只有日期的格式
                    try:
                        column_data = pd.to_datetime(column_data, errors='coerce').dt.strftime('%d/%m/%Y')
                    except:
                        pass  # 保持原样

                # 如果是特殊字段（EGN OIL, O/FLTER等），处理ü标记和OTHERS字段
                elif header in ["EGN OIL", "O/FLTER", "ATF", "BT", "EGN FLUSH", "FR BR PAD", "R BR PAD", "D FILTER", "P/SHAFT", "W/RUBBER", "SPARK PLUG"]:
                    # 对于这些字段，如果值是ü就返回true，如果空白就是false
                    def process_checkbox_value(value):
                        if pd.isna(value):
                            return False
                        value_str = str(value).strip().lower()
                        return value_str == 'ü' or value_str == 'u' or value_str == '✓' or value_str == 'x'

                    column_data = column_data.apply(process_checkbox_value)

                # 如果是OTHERS字段，正常返回它的值
                elif header == "OTHERS" and len(column_data) > 0:
                    # OTHERS字段保持原始数据
                    pass

                result_df[header] = column_data

            # 检测VEHICLE NO列的单元格颜色，添加is_havent_close_job字段
            if "VEHICLE NO" in result_columns:
                try:
                    # 使用openpyxl读取Excel文件以检测单元格颜色，通过VEHICLE NO值验证数据对应关系
                    workbook = load_workbook(file_path, data_only=False)
                    sheet = workbook[worksheet_name]

                    vehicle_no_col_idx = result_columns["VEHICLE NO"]
                    excel_col_idx = start_idx + vehicle_no_col_idx + 1  # 转换为Excel列索引（从1开始）

                    is_havent_close_job_list = []

                    # 检查每一行的VEHICLE NO单元格颜色，通过VEHICLE NO值验证数据对应关系
                    for i in range(len(result_df)):
                        excel_row = header_row + 2 + i  # +2是因为Excel行从1开始，且要跳过标题行

                        try:
                            # 获取当前行的VEHICLE NO值（从DataFrame中）
                            df_vehicle_no = result_df.iloc[i]["VEHICLE NO"] if pd.notna(result_df.iloc[i]["VEHICLE NO"]) else ""
                            df_vehicle_no = str(df_vehicle_no).strip()

                            # 获取Excel中对应行的VEHICLE NO值
                            excel_vehicle_cell = sheet.cell(row=excel_row, column=excel_col_idx)
                            excel_vehicle_no = excel_vehicle_cell.value if excel_vehicle_cell.value is not None else ""
                            excel_vehicle_no = str(excel_vehicle_no).strip()

                            # 验证VEHICLE NO值是否匹配
                            if df_vehicle_no != excel_vehicle_no:
                                # logger.warning(f"第{i+1}行VEHICLE NO不匹配: DataFrame='{df_vehicle_no}', Excel='{excel_vehicle_no}'")
                                # 如果不匹配，尝试在Excel中查找正确的行
                                found_correct_row = False
                                for search_row in range(header_row + 2, sheet.max_row + 1):
                                    search_cell = sheet.cell(row=search_row, column=excel_col_idx)
                                    search_vehicle_no = search_cell.value if search_cell.value is not None else ""
                                    search_vehicle_no = str(search_vehicle_no).strip()

                                    if df_vehicle_no == search_vehicle_no:
                                        excel_row = search_row
                                        found_correct_row = True
                                        # logger.info(f"找到匹配的VEHICLE NO '{df_vehicle_no}' 在Excel第{search_row}行")
                                        break

                                if not found_correct_row:
                                    # logger.warning(f"未找到匹配的VEHICLE NO '{df_vehicle_no}'，设置is_havent_close_job为False")
                                    is_havent_close_job_list.append(False)
                                    continue

                            # 检查单元格的填充颜色
                            cell = sheet.cell(row=excel_row, column=excel_col_idx)
                            fill_color = None
                            if cell.fill and cell.fill.start_color and cell.fill.start_color.rgb:
                                # 获取RGB颜色值
                                rgb_color = cell.fill.start_color.rgb
                                if isinstance(rgb_color, str) and len(rgb_color) == 8:
                                    # 移除alpha通道，只保留RGB部分
                                    fill_color = rgb_color[2:]  # 去掉前两位alpha通道
                                elif isinstance(rgb_color, str) and len(rgb_color) == 6:
                                    fill_color = rgb_color

                            # 检查是否为目标颜色 #00B0F0
                            target_color = "00B0F0"
                            is_target_color = fill_color and fill_color.upper() == target_color.upper()
                            is_havent_close_job_list.append(is_target_color)

                            if is_target_color:
                                logger.info(f"检测到VEHICLE NO '{df_vehicle_no}' (第{i+1}行) 有目标颜色 #00B0F0")

                        except Exception as e:
                            logger.warning(f"检测第{i+1}行VEHICLE NO单元格颜色时出错: {str(e)}")
                            is_havent_close_job_list.append(False)

                    # 添加is_havent_close_job列到DataFrame
                    result_df["is_havent_close_job"] = is_havent_close_job_list
                    logger.info(f"成功检测VEHICLE NO单元格颜色，添加is_havent_close_job字段")

                except Exception as e:
                    logger.warning(f"检测VEHICLE NO单元格颜色时出错: {str(e)}")
                    # 如果检测失败，为所有行设置默认值False
                    result_df["is_havent_close_job"] = [False] * len(result_df)
            else:
                # 如果没有VEHICLE NO列，为所有行设置默认值False
                result_df["is_havent_close_job"] = [False] * len(result_df)
                logger.info("未找到VEHICLE NO列，is_havent_close_job字段设置为False")
            
            logger.info(f"提取的数据形状: {result_df.shape}")
            
            # 找到第一个所有列都为空的行
            last_row = None
            for i in range(len(result_df)):
                if all(pd.isna(result_df.iloc[i][col]) for col in result_df.columns):  # 修改：排除NO列，因为可能只有NO列有值
                    last_row = i
                    logger.info(f"在行 {i} 找到全空行")
                    break
            
            # 如果找到了全空行，截取数据
            if last_row is not None:
                result_df = result_df.iloc[:last_row]
                logger.info(f"截取数据到行 {last_row}, 新形状: {result_df.shape}")

            # 过滤掉MAXCHECK、REMARKS、PRICE都为空的行
            if len(result_df) > 0:
                # 检查哪些列存在
                key_columns = []
                if "MAXCHECK" in result_df.columns:
                    key_columns.append("MAXCHECK")
                if "REMARKS" in result_df.columns:
                    key_columns.append("REMARKS")
                if "PRICE" in result_df.columns:
                    key_columns.append("PRICE")

                if key_columns:
                    # 创建过滤条件：至少有一个关键列不为空
                    mask = result_df[key_columns].notna().any(axis=1)

                    # 应用过滤
                    original_count = len(result_df)
                    result_df = result_df[mask].reset_index(drop=True)
                    filtered_count = len(result_df)

                    if filtered_count < original_count:
                        logger.info(f"过滤掉 {original_count - filtered_count} 行空数据，剩余 {filtered_count} 行")

            # 如果提供了小时费率和佣金率，计算佣金
            if per_hour_rate is not None and commission_rate is not None:
                result = self._calculate_commission(file_path, worksheet_name, result_df, result_columns, header_row, start_idx, per_hour_rate, cbu_car_hour_rate, commission_rate, wty_hour_rate, wty_commission_rate)
                
                # 解析返回值
                if len(result) == 3:
                    result_df_data, result_path, error = result
                    
                    if error:
                        logger.error(f"计算佣金时出错: {error}")
                        return None, None, error
                    
                    if result_path:
                        # 处理返回的DataFrame数据
                        if isinstance(result_df_data, tuple) and len(result_df_data) > 0:
                            if isinstance(result_df_data[0], pd.DataFrame):
                                # 使用返回的DataFrame
                                result_df = result_df_data[0]
                                logger.info(f"使用返回的DataFrame，形状: {result_df.shape}")
                            result_df_path = result_df_data[1] if len(result_df_data) > 1 else None
                        else:
                            result_df_path = result_df_data
                        
                        # 在返回前对数据进行排序和清理
                        # 先移除总计行（如果存在）
                        data_df = result_df[result_df["NO"] != "TOTAL"].copy()
                        
                        # 按照日期和NO列排序
                        try:
                            # 确保NO列是数值类型，以便正确排序（修复FutureWarning）
                            if "NO" in data_df.columns:
                                try:
                                    data_df["NO"] = pd.to_numeric(data_df["NO"])
                                except (ValueError, TypeError):
                                    # 如果转换失败，保持原值
                                    logger.debug("NO列包含非数值数据，保持原值")
                            
                            # 按日期和NO排序
                            if "DATE" in data_df.columns and "NO" in data_df.columns:
                                # 忽略类型检查器警告，这是pandas的有效调用
                                date_col = "DATE"  # 类型: str
                                no_col = "NO"  # 类型: str
                                data_df = data_df.sort_values([date_col, no_col])  # type: ignore
                                logger.info("数据按照日期和编号排序")
                        except Exception as e:
                            logger.warning(f"排序数据时出错: {str(e)}")
                        
                        # 重新计算总佣金
                        total_commission = data_df["COMMISSION"].sum()
                        
                        # 重新添加总计行（避免FutureWarning）
                        summary_row = {
                            "NO": "TOTAL",
                            "COMMISSION": total_commission
                        }
                        # 使用列表方式添加行，避免pd.concat警告
                        data_list = data_df.to_dict('records')
                        data_list.append(summary_row)
                        result_df = pd.DataFrame(data_list).reset_index(drop=True)
                        logger.info(f"重新添加总计行，总佣金: {total_commission}")
                        
                        # 保存完整的DataFrame以便下载时使用
                        if not result_df_path:
                            result_df_path = os.path.join(os.path.dirname(file_path), f"df_{os.path.basename(file_path)}")
                        result_df.to_excel(result_df_path, index=False)
                        logger.info(f"保存完整的DataFrame到: {result_df_path}")
                        
                        return result_df, result_path, None
                    else:
                        return None, None, "计算佣金时出错：未生成结果文件"
                else:
                    return None, None, "计算佣金返回值格式错误"
            
            # 如果没有计算佣金，则创建一个简单的Excel文件
            result_path = os.path.join(os.path.dirname(file_path), f"result_{os.path.basename(file_path)}")
            logger.info(f"创建简单的Excel文件: {result_path}")
            result_df.to_excel(result_path, index=False)
            return result_df, result_path, None
        
        except Exception as e:
            error_msg = f"提取数据时出错: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            return None, None, error_msg
    
    def _calculate_commission(self, file_path, worksheet_name, result_df, result_columns, header_row, start_idx, per_hour_rate, cbu_car_hour_rate, commission_rate, wty_hour_rate=None, wty_commission_rate=None):
        """计算佣金并修改Excel文件"""
        try:
            per_hour_rate = float(per_hour_rate)
            commission_rate = float(commission_rate)
            cbu_car_hour_rate = float(cbu_car_hour_rate) if cbu_car_hour_rate is not None else per_hour_rate
            wty_hour_rate = float(wty_hour_rate) if wty_hour_rate is not None else per_hour_rate
            wty_commission_rate = float(wty_commission_rate) if wty_commission_rate is not None else commission_rate
            
            # 确保MAXCHECK列存在
            if "MAXCHECK" in result_df.columns:
                # 预编译正则表达式 - 支持单位如HRS
                rm_format_pattern = re.compile(r'(\d+\.?\d*)\s*(?:HRS?)?\s*RM\s+(\d+\.?\d*)')
                
                # 处理MAXCHECK列，提取数值部分
                def extract_maxcheck_value(value):
                    if pd.isna(value):
                        return value
                    
                    value_str = str(value).strip()
                    # 检查是否为0值
                    if value_str == '0' or value_str == '0.0' or value_str == '0.00':
                        return 0.0  # 保留为0，后面会处理为"NO GIVE"
                    
                    # 检查是否已经包含"RM"格式 (如 "1.5 RM 58.50")
                    rm_match = rm_format_pattern.search(value_str)
                    if rm_match:
                        # 已经是"1.5 RM 58.50"格式，提取小时数
                        return float(rm_match.group(1))
                    else:
                        # 尝试直接转换为数值
                        try:
                            return pd.to_numeric(value, errors='coerce')
                        except:
                            return value
                
                # 应用函数到MAXCHECK列
                result_df["MAXCHECK"] = result_df["MAXCHECK"].apply(extract_maxcheck_value)
                
                # 将MAXCHECK转换为数值
                result_df["MAXCHECK"] = pd.to_numeric(result_df["MAXCHECK"], errors='coerce')
                
                # 处理REMARKS列中的0.0值
                if "REMARKS" in result_df.columns:
                    def process_remarks(remarks):
                        if pd.isna(remarks):
                            return remarks
                        
                        remarks_str = str(remarks)
                        # 检查是否包含"0.0"格式
                        zero_pattern = re.compile(r'([A-Za-z/\s]+)\s+(0\.0+)(?!\s+NO GIVE)')
                        zero_match = zero_pattern.search(remarks_str)
                        if zero_match:
                            # 提取前缀和0.0值
                            prefix = zero_match.group(1).strip()
                            zero_value = zero_match.group(2)
                            # 添加"NO GIVE"，保留0.0值
                            new_remarks = zero_pattern.sub(f"{prefix} {zero_value} NO GIVE", remarks_str)
                            # 如果包含括号内容，保留它
                            if "(" in remarks_str and ")" in remarks_str:
                                bracket_content = re.search(r'\(([^)]+)\)', remarks_str)
                                if bracket_content:
                                    bracket_text = bracket_content.group(0)
                                    if bracket_text not in new_remarks:
                                        new_remarks = f"{new_remarks} {bracket_text}"
                            return new_remarks
                        return remarks
                    
                    # 应用函数到REMARKS列
                    result_df["REMARKS"] = result_df["REMARKS"].apply(process_remarks)
                
                # 添加HOUR_RATE列，根据REMARKS中是否包含"CBU CAR"设置不同的小时费率
                if "REMARKS" in result_df.columns:
                    # 预编译正则表达式
                    decimal_pattern = re.compile(r'\b\d+\.\d+\b')
                    
                    # 定义一个函数来确定每行的小时费率和佣金率
                    def get_hour_rate(row):
                        if pd.notna(row["REMARKS"]):
                            remarks_upper = str(row["REMARKS"]).upper()

                            # 检查是否包含WTY
                            has_wty = "WTY" in remarks_upper

                            if has_wty:
                                # 检查REMARKS中是否有其他小时数（除了WTY相关的）
                                remarks_str = str(row["REMARKS"])
                                decimal_matches = re.findall(r'\b\d+\.\d+\b', remarks_str)

                                # 过滤掉价格值（前面有RM的）
                                non_price_decimals = []
                                for decimal in decimal_matches:
                                    if not re.search(f"RM\\s+{re.escape(decimal)}", remarks_str):
                                        non_price_decimals.append(decimal)

                                # 如果REMARKS中有其他小时数，MAXCHECK用正常费率
                                # 如果REMARKS中只有WTY没有其他小时数，MAXCHECK用WTY费率
                                if len(non_price_decimals) > 0:
                                    # 有其他小时数，MAXCHECK用正常费率
                                    return per_hour_rate
                                else:
                                    # 只有WTY没有其他小时数，MAXCHECK用WTY费率
                                    return wty_hour_rate
                            elif "CBU CAR" in remarks_upper:
                                return cbu_car_hour_rate
                        return per_hour_rate

                    def get_commission_rate(row):
                        if pd.notna(row["REMARKS"]):
                            remarks_upper = str(row["REMARKS"]).upper()

                            # 检查是否包含WTY
                            has_wty = "WTY" in remarks_upper

                            if has_wty:
                                # 检查REMARKS中是否有其他小时数（除了WTY相关的）
                                remarks_str = str(row["REMARKS"])
                                decimal_matches = re.findall(r'\b\d+\.\d+\b', remarks_str)

                                # 过滤掉价格值（前面有RM的）
                                non_price_decimals = []
                                for decimal in decimal_matches:
                                    if not re.search(f"RM\\s+{re.escape(decimal)}", remarks_str):
                                        non_price_decimals.append(decimal)

                                # 如果REMARKS中有其他小时数，MAXCHECK用正常佣金率
                                # 如果REMARKS中只有WTY没有其他小时数，MAXCHECK用WTY佣金率
                                if len(non_price_decimals) > 0:
                                    # 有其他小时数，MAXCHECK用正常佣金率
                                    return commission_rate
                                else:
                                    # 只有WTY没有其他小时数，MAXCHECK用WTY佣金率
                                    return wty_commission_rate
                        return commission_rate
                    
                    # 应用函数到每一行 - 使用向量化操作提高性能
                    result_df["HOUR_RATE"] = result_df.apply(get_hour_rate, axis=1)
                    result_df["COMMISSION_RATE"] = result_df.apply(get_commission_rate, axis=1)
                    
                    # 计算佣金
                    def calculate_commission(row):
                        if pd.isna(row["MAXCHECK"]):
                            return 0
                        
                        maxcheck_str = str(row["MAXCHECK"]).strip()
                        # 检查是否已经包含"RM"格式 (如 "1.5 RM 58.50" 或 "1.3 HRS RM 101.40")
                        rm_match = re.search(r'(\d+\.?\d*)\s*(?:HRS?)?\s*RM\s+(\d+\.?\d*)', maxcheck_str)
                        if rm_match:
                            # 已经是"1.5 RM 58.50"格式，直接提取佣金值
                            try:
                                return float(rm_match.group(2))
                            except (IndexError, AttributeError):
                                logger.warning(f"无法从 '{maxcheck_str}' 中提取佣金值")
                                # 继续处理，将按照普通方式计算
                        
                        # 正常计算佣金
                        # 如果MAXCHECK为0，返回0
                        if row["MAXCHECK"] == 0 or row["MAXCHECK"] == 0.0:
                            return 0
                        return row["MAXCHECK"] * row["HOUR_RATE"] * row["COMMISSION_RATE"]
                    
                    result_df["COMMISSION"] = result_df.apply(calculate_commission, axis=1)
                    
                    # 处理REMARKS列中的小数点值（可能表示额外的工时）
                    additional_rows = []
                    
                    # 优化：批量处理REMARKS列
                    remarks_rows = result_df[result_df["REMARKS"].notna()].copy()
                    
                    # 预编译"0.0 NO GIVE"模式 - 只跳过0.0值的NO GIVE
                    zero_no_give_pattern = re.compile(r'0\.0+\s+NO GIVE', re.IGNORECASE)

                    # 遍历每一行
                    for idx, row in remarks_rows.iterrows():
                        remarks = row.get("REMARKS")
                        remarks_text = str(remarks)

                        # 只有当是"0.0 NO GIVE"格式时才跳过，其他如"0.5 NO GIVE RM 19.50"要处理
                        if zero_no_give_pattern.search(remarks_text):
                            continue
                            
                        # 使用预编译的正则表达式查找小数点值
                        decimal_matches = decimal_pattern.findall(remarks_text)
                        if decimal_matches:
                            # 检查REMARKS中是否包含任何价格
                            has_any_price = re.search(r'RM\s+\d+(\.\d+)?', remarks_text)

                            # 为每个找到的小数点值创建一个新行
                            for decimal_value in decimal_matches:
                                # 检查这个小数值是否是价格（前面有RM）
                                is_price = re.search(f"RM\\s+{re.escape(decimal_value)}", remarks_text)

                                # 检查这个小数值是否是工时且有关联价格（后面有RM）
                                is_hour_with_price = re.search(f"{re.escape(decimal_value)}\\s+.*?RM\\s+\\d+(\\.\\d+)?", remarks_text)

                                # 检查是否为0值，如果是0则不创建新行
                                is_zero_value = decimal_value == '0' or decimal_value == '0.0' or decimal_value == '0.00'

                                # 创建新行的条件：
                                # 1. 如果REMARKS中有价格：只为有价格的工时创建新行
                                # 2. 如果REMARKS中没有价格：为所有工时创建新行
                                should_create_row = False
                                if has_any_price:
                                    # 有价格的情况：只为有价格的工时创建新行
                                    should_create_row = is_hour_with_price and not is_price and not is_zero_value
                                else:
                                    # 没有价格的情况：为所有非价格、非零的小数值创建新行
                                    should_create_row = not is_price and not is_zero_value

                                if should_create_row:
                                    # 创建新行，复制原行的所有值
                                    new_row = row.copy()
                                    
                                    # 更新MAXCHECK为找到的小数点值
                                    new_row["MAXCHECK"] = float(decimal_value)

                                    # 提取实际的佣金值
                                    commission_value = 0
                                    if has_any_price and is_hour_with_price:
                                        # 有价格的工时：提取RM后面的价格
                                        rm_match = re.search(f"{re.escape(decimal_value)}\\s+.*?RM\\s+(\\d+(?:\\.\\d+)?)", remarks_text)
                                        if rm_match:
                                            commission_value = float(rm_match.group(1))
                                    else:
                                        # 没有价格的工时：使用计算的佣金
                                        commission_value = float(decimal_value) * new_row["HOUR_RATE"] * new_row["COMMISSION_RATE"]

                                    new_row["COMMISSION"] = commission_value

                                    # 提取完整的描述
                                    try:
                                        if has_any_price and is_hour_with_price:
                                            # 有价格的工时：提取从开始到RM价格的完整部分
                                            full_match = re.search(r'([A-Za-z/\s\(\)]+' + re.escape(decimal_value) + r'.*?RM\s+\d+(?:\.\d+)?)', remarks_text)
                                            if full_match:
                                                new_row["REMARKS"] = full_match.group(1).strip()
                                            else:
                                                new_row["REMARKS"] = f"ITEM {decimal_value}"
                                        else:
                                            # 没有价格的工时：提取描述部分
                                            match = re.search(r'([A-Za-z/\s]+)\s+' + re.escape(decimal_value), remarks_text)
                                            if match:
                                                prefix = match.group(1).strip()
                                                new_row["REMARKS"] = f"{prefix} {decimal_value}"
                                            else:
                                                new_row["REMARKS"] = f"ITEM {decimal_value}"
                                        
                                        # 记录原行的索引，以便稍后插入
                                        new_row["_original_idx"] = idx
                                        
                                        # 添加到额外行列表
                                        additional_rows.append(new_row)
                                        logger.info(f"在REMARKS中找到小数点值: ['{decimal_value}']")
                                    except Exception as e:
                                        logger.error(f"处理REMARKS中的小数点值时出错: {str(e)}")
                    
                    # 如果有额外行，按照原行的顺序插入
                    if additional_rows:
                        # 按照原行索引排序
                        additional_rows.sort(key=lambda x: x["_original_idx"])
                        
                        # 使用列表收集所有行，然后一次性创建DataFrame（避免FutureWarning）
                        all_rows = []

                        # 遍历原始DataFrame，在适当位置插入新行
                        for i, row in result_df.iterrows():
                            # 添加原始行
                            all_rows.append(row.to_dict())

                            # 检查是否有对应的额外行需要插入
                            inserted_rows = []
                            for new_row in additional_rows:
                                if new_row["_original_idx"] == i:
                                    # 删除临时索引列
                                    if "_original_idx" in new_row:
                                        del new_row["_original_idx"]

                                    # 添加额外行到列表
                                    all_rows.append(new_row)
                                    inserted_rows.append(new_row)

                            # 从列表中移除已插入的行
                            for row_to_remove in inserted_rows:
                                if row_to_remove in additional_rows:  # 确保行仍在列表中
                                    additional_rows.remove(row_to_remove)

                        # 一次性创建新的DataFrame（避免FutureWarning）
                        result_df = pd.DataFrame(all_rows).reset_index(drop=True)
                        
                        # 重新编号NO列（如果存在）
                        if "NO" in result_df.columns:
                            # 跳过最后一行（总计行），如果存在
                            last_row = None
                            if len(result_df) > 0 and result_df.iloc[-1]["NO"] == "TOTAL":
                                last_row = result_df.iloc[-1:].copy()
                                result_df = result_df.iloc[:-1]
                            
                            # 重新编号
                            result_df["NO"] = range(1, len(result_df) + 1)
                            
                            # 添加回总计行（避免FutureWarning）
                            if last_row is not None:
                                # 使用列表方式添加行，避免pd.concat警告
                                data_list = result_df.to_dict('records')
                                total_row_dict = last_row.iloc[0].to_dict()
                                data_list.append(total_row_dict)
                                result_df = pd.DataFrame(data_list).reset_index(drop=True)
                else:
                    # 如果没有REMARKS列，使用默认小时费率和佣金率
                    result_df["HOUR_RATE"] = per_hour_rate
                    result_df["COMMISSION_RATE"] = commission_rate
                    result_df["COMMISSION"] = result_df["MAXCHECK"] * per_hour_rate * commission_rate
                
                # 计算总佣金
                total_commission = result_df["COMMISSION"].sum()
                
                # 添加总计行（避免FutureWarning）
                if "NO" not in result_df.columns or not any(str(no).upper() == "TOTAL" for no in result_df["NO"]):
                    # 使用列表方式添加行，避免pd.concat警告
                    data_list = result_df.to_dict('records')
                    summary_row = {
                        "NO": "TOTAL",
                        "COMMISSION": total_commission
                    }
                    data_list.append(summary_row)
                    result_df = pd.DataFrame(data_list).reset_index(drop=True)
                
                # 创建修改后的Excel文件 - 使用openpyxl保留格式
                result_path = os.path.join(os.path.dirname(file_path), f"processed_{os.path.basename(file_path)}")
                
                # 保存DataFrame的副本，用于后续下载
                result_df_path = os.path.join(os.path.dirname(file_path), f"df_{os.path.basename(file_path)}")
                # result_df.to_excel(result_df_path, index=False)
                
                # 复制原始文件以保留格式
                shutil.copy2(file_path, result_path)
                
                # 使用openpyxl打开复制的文件
                workbook = load_workbook(result_path)
                sheet = workbook[worksheet_name]
                
                # 直接使用已经找到的列索引
                # 将pandas的列索引转换为Excel的列索引（Excel从1开始）
                excel_header_row = header_row + 1  # Excel行从1开始
                
                # 找出对应的Excel列
                maxcheck_col = None
                remarks_col = None
                price_col = None
                
                # 使用前面找到的列索引，转换为Excel的列索引
                if "MAXCHECK" in result_columns:
                    maxcheck_col = start_idx + result_columns["MAXCHECK"] + 1
                
                if "REMARKS" in result_columns:
                    remarks_col = start_idx + result_columns["REMARKS"] + 1
                
                if "PRICE" in result_columns:
                    price_col = start_idx + result_columns["PRICE"] + 1
                
                # 如果找不到PRICE列，查找可能的PRICE/AMOUNT列，如果找不到则创建一个
                if price_col is None:
                    # 先尝试查找可能的PRICE/AMOUNT列
                    price_pattern = re.compile(r'PRICE|AMOUNT|FEE', re.IGNORECASE)
                    for col in range(1, sheet.max_column + 1):
                        cell_value = sheet.cell(row=excel_header_row, column=col).value
                        if cell_value and price_pattern.search(str(cell_value).strip()):
                            price_col = col
                            break
                    
                    # 如果仍然找不到，则创建一个新的PRICE列
                    if price_col is None:
                        # 优先在MAXCHECK列旁边创建
                        if maxcheck_col is not None:
                            new_price_col = maxcheck_col + 1
                        else:
                            # 如果没有找到MAXCHECK列，在开始列之后的第三列位置创建
                            new_price_col = start_idx + 3
                        
                        # 设置列标题
                        sheet.cell(row=excel_header_row, column=new_price_col).value = "PRICE"
                        # 确保PRICE列标题不为空
                        if sheet.cell(row=excel_header_row, column=new_price_col).value is None or sheet.cell(row=excel_header_row, column=new_price_col).value == "":
                            sheet.cell(row=excel_header_row, column=new_price_col).value = "PRICE"
                        price_col = new_price_col
                
                # 如果找不到MAXCHECK列，尝试使用第一个可能的数值列
                if maxcheck_col is None:
                    # 检查前几行数据，查找可能包含数值的列
                    for col in range(start_idx + 1, start_idx + sheet.max_column + 1):  # Excel列从1开始
                        for row in range(excel_header_row + 1, excel_header_row + 6):  # 检查标题行后的几行
                            cell_value = sheet.cell(row=row, column=col).value
                            if isinstance(cell_value, (int, float)) or (isinstance(cell_value, str) and cell_value.replace('.', '', 1).isdigit()):
                                maxcheck_col = col
                                break
                        if maxcheck_col is not None:
                            break
                
                # 如果仍然找不到MAXCHECK列，返回错误
                if maxcheck_col is None:
                    error_msg = "无法在工作表中找到MAXCHECK/HOURS列，无法继续处理"
                    logger.error(error_msg)
                    return result_df_path, None, error_msg
                
                # 如果找到了MAXCHECK列
                logger.info("开始修改Excel文件")
                
                # 创建一个标志来跟踪是否进行了任何修改
                any_modifications = False
                
                # 获取数据的实际行数，确保处理所有行，包括最后一行
                data_rows = len(result_df) - 1  # 减1是因为最后一行是总计行
                
                # 预编译正则表达式
                rm_pattern = re.compile(r'RM')
                maxcheck_number_pattern = re.compile(r'\d+\.?\d*')
                decimal_pattern = re.compile(r'\b\d+\.\d+\b')
                
                # 从标题行的下一行开始修改
                for i in range(data_rows):
                    excel_row = header_row + 3 + i  # +3是因为Excel行从1开始，而header_row是从0开始的，再加2是因为要跳过标题行
                    
                    # 获取MAXCHECK值和REMARKS值
                    maxcheck_value = sheet.cell(row=excel_row, column=maxcheck_col).value
                    remarks_value = None if remarks_col is None else sheet.cell(row=excel_row, column=remarks_col).value
                    
                    # 检查REMARKS类型并选择正确的费率
                    current_hour_rate = per_hour_rate
                    current_commission_rate = commission_rate

                    if remarks_value:
                        remarks_upper = str(remarks_value).upper()
                        remarks_str = str(remarks_value)

                        if "WTY" in remarks_upper:
                            # 检查REMARKS中是否有其他小时数（除了WTY相关的）
                            decimal_matches = re.findall(r'\b\d+\.\d+\b', remarks_str)

                            # 过滤掉价格值（前面有RM的）
                            non_price_decimals = []
                            for decimal in decimal_matches:
                                if not re.search(f"RM\\s+{re.escape(decimal)}", remarks_str):
                                    non_price_decimals.append(decimal)

                            # 如果REMARKS中有其他小时数，MAXCHECK用正常费率
                            # 如果REMARKS中只有WTY没有其他小时数，MAXCHECK用WTY费率
                            if len(non_price_decimals) > 0:
                                # 有其他小时数，MAXCHECK用正常费率
                                current_hour_rate = per_hour_rate
                                current_commission_rate = commission_rate
                            else:
                                # 只有WTY没有其他小时数，MAXCHECK用WTY费率
                                current_hour_rate = wty_hour_rate
                                current_commission_rate = wty_commission_rate
                        elif "CBU CAR" in remarks_upper:
                            current_hour_rate = cbu_car_hour_rate
                            current_commission_rate = commission_rate
                    
                    # 提取数值并计算佣金
                    commission_value = 0
                    
                    if maxcheck_value is not None:
                        try:
                            # 尝试从MAXCHECK中提取数值
                            maxcheck_str = str(maxcheck_value).strip()
                            
                            # 检查是否为0值
                            if maxcheck_str == '0' or maxcheck_str == '0.0' or maxcheck_str == '0.00':
                                # 设置为NO GIVE
                                sheet.cell(row=excel_row, column=maxcheck_col).value = "NO GIVE"
                                commission_value = 0
                                any_modifications = True
                            # 检查是否已经包含"RM"格式 (如 "1.5 RM 58.50" 或 "1.3 HRS RM 101.40")
                            elif re.search(r'(\d+\.?\d*)\s*(?:HRS?)?\s*RM\s+(\d+\.?\d*)', maxcheck_str):
                                # 已经是"1.5 RM 58.50"或"1.3 HRS RM 101.40"格式，直接提取数值
                                rm_match = re.search(r'(\d+\.?\d*)\s*(?:HRS?)?\s*RM\s+(\d+\.?\d*)', maxcheck_str)
                                if rm_match:
                                    maxcheck_float = float(rm_match.group(1))
                                    commission_value = float(rm_match.group(2))
                                    # 不需要修改MAXCHECK列，已经是正确格式
                                    any_modifications = True
                                else:
                                    logger.warning(f"无法从 '{maxcheck_str}' 中提取 RM 格式数据")
                            # 如果不是"RM"格式，按原来的逻辑处理
                            elif not rm_pattern.search(maxcheck_str):
                                try:
                                    # 尝试提取数值部分
                                    maxcheck_match = maxcheck_number_pattern.search(maxcheck_str)
                                    if maxcheck_match:
                                        maxcheck_float = float(maxcheck_match.group(0))
                                        
                                        # 如果值为0，设置为NO GIVE
                                        if maxcheck_float == 0 or maxcheck_float == 0.0:
                                            sheet.cell(row=excel_row, column=maxcheck_col).value = "NO GIVE"
                                            commission_value = 0
                                        else:
                                            # 计算佣金，使用对应的小时费率和佣金率
                                            maxcheck_commission = maxcheck_float * current_hour_rate * current_commission_rate
                                            commission_value = maxcheck_commission
                                            
                                            # 修改MAXCHECK列，格式为"1.5 RM 58.50"或"1.5 RM 58"（如果是整数）
                                            if maxcheck_commission.is_integer():
                                                new_value = f"{maxcheck_float} RM {int(maxcheck_commission)}"
                                            else:
                                                new_value = f"{maxcheck_float} RM {maxcheck_commission:.2f}"
                                            sheet.cell(row=excel_row, column=maxcheck_col).value = new_value
                                        
                                        any_modifications = True
                                except (ValueError, AttributeError):
                                    pass
                        except ValueError:
                            # 如果无法转换为浮点数，可能已经是修改过的格式，跳过
                            pass
                    
                    # 处理REMARKS列中的数值
                    if remarks_col is not None and remarks_value:
                        try:
                            remarks_str = str(remarks_value)
                            
                            # 检查是否包含"0.0"格式，但没有"NO GIVE"
                            zero_pattern = re.compile(r'([A-Za-z/\s]+)\s+(0\.0+)(?!\s+NO GIVE)')
                            zero_match = zero_pattern.search(remarks_str)
                            if zero_match:
                                # 提取前缀和0.0值
                                prefix = zero_match.group(1).strip()
                                zero_value = zero_match.group(2)
                                # 添加"NO GIVE"，保留0.0值
                                new_remarks = zero_pattern.sub(f"{prefix} {zero_value} NO GIVE", remarks_str)
                                # 如果包含括号内容，保留它
                                if "(" in remarks_str and ")" in remarks_str:
                                    bracket_content = re.search(r'\(([^)]+)\)', remarks_str)
                                    if bracket_content:
                                        bracket_text = bracket_content.group(0)
                                        if bracket_text not in new_remarks:
                                            new_remarks = f"{new_remarks} {bracket_text}"
                                
                                # 更新单元格
                                sheet.cell(row=excel_row, column=remarks_col).value = new_remarks
                                any_modifications = True
                            else:
                                # 使用预编译的正则表达式查找所有包含小数点的部分
                                decimal_matches = decimal_pattern.findall(remarks_str)
                                
                                # 如果找到了数值
                                if decimal_matches:
                                    # 记录原始remarks值用于比较
                                    original_remarks = remarks_str
                                    new_remarks = original_remarks
                                    
                                    # 处理每一个找到的小数值
                                    for decimal_value in decimal_matches:
                                        # 检查是否为0值
                                        if decimal_value == '0.0' or decimal_value == '0.00':
                                            # 查找这个0值前面的文本
                                            prefix_match = re.search(r'([A-Za-z/\s]+)\s+' + re.escape(decimal_value), new_remarks)
                                            if prefix_match and "NO GIVE" not in new_remarks:
                                                prefix = prefix_match.group(1).strip()
                                                # 添加"NO GIVE"，保留0.0值
                                                new_remarks = new_remarks.replace(
                                                    f"{prefix} {decimal_value}", 
                                                    f"{prefix} {decimal_value} NO GIVE"
                                                )
                                        else:
                                            # 检查这个特定小数值是否已经被处理过（包含RM标记）
                                            # 1. 检查小数值后面是否已经有RM标记和价格（允许中间有其他内容如括号）
                                            decimal_with_rm_after = re.search(f"{re.escape(decimal_value)}\\s+.*?RM\\s+\\d+(\\.\\d+)?", new_remarks)
                                            # 2. 检查小数值前面是否已经有RM标记，表示这个小数值本身就是价格
                                            decimal_with_rm_before = re.search(f"RM\\s+{re.escape(decimal_value)}", new_remarks)

                                            # 只有当这个特定小数值没有关联的RM价格，才进行佣金计算
                                            if not decimal_with_rm_after and not decimal_with_rm_before:
                                                remarks_float = float(decimal_value)

                                                # 对于REMARKS中的小数值，如果包含WTY则使用WTY费率
                                                if "WTY" in remarks_str.upper():
                                                    remarks_hour_rate = wty_hour_rate
                                                    remarks_commission_rate = wty_commission_rate
                                                elif "CBU CAR" in remarks_str.upper():
                                                    remarks_hour_rate = cbu_car_hour_rate
                                                    remarks_commission_rate = commission_rate
                                                else:
                                                    remarks_hour_rate = per_hour_rate
                                                    remarks_commission_rate = commission_rate

                                                # 计算佣金
                                                remarks_commission = remarks_float * remarks_hour_rate * remarks_commission_rate
                                                
                                                # 替换这个特定的小数值，整数显示为整数，小数显示为2位小数
                                                if remarks_commission.is_integer():
                                                    remarks_commission_str = f"{int(remarks_commission)}"
                                                else:
                                                    remarks_commission_str = f"{remarks_commission:.2f}"
                                                
                                                new_remarks = new_remarks.replace(
                                                    decimal_value, 
                                                    f"{decimal_value} RM {remarks_commission_str}"
                                                )
                                    
                                    # 只有当有变化时才更新单元格
                                    if new_remarks != original_remarks:
                                        sheet.cell(row=excel_row, column=remarks_col).value = new_remarks
                                        any_modifications = True
                        except (ValueError, AttributeError):
                            pass
                    
                    # 只有当PRICE列为空时，才在PRICE列填入MAXCHECK的佣金值
                    if price_col is not None:
                        # 检查REMARKS中是否包含非零小数点值
                        has_non_zero_decimal_in_remarks = False
                        if remarks_col is not None and remarks_value:
                            remarks_str = str(remarks_value)
                            # 查找所有小数点值
                            decimal_matches = decimal_pattern.findall(remarks_str)
                            
                            # 检查是否含有非零的小数值
                            for decimal_value in decimal_matches:
                                # 如果不是0.0格式，且不是"X.X NO GIVE"格式，则认为是有效的小数值
                                if decimal_value != '0.0' and decimal_value != '0.00':
                                    # 检查这个值是否已经有NO GIVE标记
                                    if not re.search(re.escape(decimal_value) + r'\s+NO GIVE', remarks_str):
                                        has_non_zero_decimal_in_remarks = True
                                        break
                        
                        # 检查当前PRICE值
                        current_price = sheet.cell(row=excel_row, column=price_col).value
                        
                        # 只有当PRICE为空且REMARKS不含非零小数值时才填写
                        if (current_price is None or str(current_price).strip() == "") and not has_non_zero_decimal_in_remarks:
                            # PRICE为空且REMARKS不含非零小数值时，填写佣金值
                            price_value = f"{commission_value:.2f}" if commission_value > 0 else ""
                            # 设置单元格值为数字并保持2位小数
                            if price_value:
                                cell = sheet.cell(row=excel_row, column=price_col)
                                cell.value = float(commission_value)
                                # 设置为会计格式，不显示货币符号
                                cell.number_format = '_-* #,##0.00_-;-* #,##0.00_-;_-* "-"??_-;_-@_-'
                                any_modifications = True
                        elif has_non_zero_decimal_in_remarks and (current_price is None or str(current_price).strip() == ""):
                            # REMARKS包含小数点值，确保PRICE为空
                            sheet.cell(row=excel_row, column=price_col).value = ""
                            any_modifications = True
                
                # 保存修改后的工作簿
                try:
                    workbook.save(result_path)
                    # 验证文件是否可以正常打开
                    # try:
                    #     load_workbook(result_path)
                    #     logger.info("验证成功: Excel文件可以正常打开")
                    # except Exception as e:
                    #     logger.error(f"验证失败: 无法打开保存的Excel文件: {str(e)}")
                    #     # 尝试使用pandas保存
                    #     result_df.to_excel(result_path, index=False)
                    #     logger.info("使用pandas保存Excel文件作为备选方案")
                except Exception as e:
                    logger.error(f"保存Excel文件时出错: {str(e)}")
                    # 尝试使用pandas保存
                    result_df.to_excel(result_path, index=False)
                    logger.info("使用pandas保存Excel文件作为备选方案")
                
                # 返回结果
                return (result_df, result_df_path), result_path, None
            else:
                error_msg = "MAXCHECK列不存在，无法计算佣金"
                logger.error(error_msg)
                return None, None, error_msg
        except Exception as e:
            error_msg = f"计算佣金时出错: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            return None, None, error_msg 