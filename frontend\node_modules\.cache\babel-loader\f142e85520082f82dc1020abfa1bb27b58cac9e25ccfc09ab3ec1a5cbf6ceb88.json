{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from './gridColumnsSelector';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nimport { hydrateColumnsWidth, createColumnsState, mergeColumnsState, COLUMNS_DIMENSION_PROPERTIES } from './gridColumnsUtils';\nimport { GridPreferencePanelsValue } from '../preferencesPanel';\nimport { getGridDefaultColumnTypes } from '../../../colDef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultColumnTypes = getGridDefaultColumnTypes();\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  var _props$initialState, _ref, _props$columnVisibili, _props$initialState2;\n  const columnsState = createColumnsState({\n    apiRef,\n    columnTypes: defaultColumnTypes,\n    columnsToUpsert: props.columns,\n    initialState: (_props$initialState = props.initialState) == null ? void 0 : _props$initialState.columns,\n    columnVisibilityModel: (_ref = (_props$columnVisibili = props.columnVisibilityModel) != null ? _props$columnVisibili : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.columns) == null ? void 0 : _props$initialState2.columnVisibilityModel) != null ? _ref : {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  var _props$initialState4, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const columnTypes = defaultColumnTypes;\n  const previousColumnsProp = React.useRef(props.columns);\n  const previousColumnTypesProp = React.useRef(columnTypes);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback(function (field) {\n    let useVisibleColumns = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnTypes,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, columnTypes]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState, columnTypes]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    var _columnVisibilityMode;\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = (_columnVisibilityMode = columnVisibilityModel[field]) != null ? _columnVisibilityMode : true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(\"Moving column \".concat(field, \" to index \").concat(targetIndexPosition));\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    var _apiRef$current$getRo, _apiRef$current$getRo2;\n    logger.debug(\"Updating column \".concat(field, \" width to \").concat(width));\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), (_apiRef$current$getRo = (_apiRef$current$getRo2 = apiRef.current.getRootDimensions()) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState$c, _props$initialState3;\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys((_props$initialState$c = (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.columns) == null ? void 0 : _props$initialState3.columnVisibilityModel) != null ? _props$initialState$c : {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, (_props$initialState4 = props.initialState) == null ? void 0 : _props$initialState4.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const columnVisibilityModelToImport = (_context$stateToResto = context.stateToRestore.columns) == null ? void 0 : _context$stateToResto.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef, columnTypes]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      var _props$slotProps;\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), viewportInnerSize.width));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(\"Columns pipe processing have changed, regenerating the columns\");\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState, columnTypes]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /**\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(\"GridColumns have changed, new length \".concat(props.columns.length));\n    if (previousColumnsProp.current === props.columns && previousColumnTypesProp.current === columnTypes) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    previousColumnTypesProp.current = columnTypes;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, columnTypes]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "gridColumnFieldsSelector", "gridColumnDefinitionsSelector", "gridColumnLookupSelector", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnPositionsSelector", "GridSignature", "useGridApiEventHandler", "useGridRegisterPipeProcessor", "useGridRegisterPipeApplier", "hydrateColumnsWidth", "createColumnsState", "mergeColumnsState", "COLUMNS_DIMENSION_PROPERTIES", "GridPreferencePanelsValue", "getGridDefaultColumnTypes", "jsx", "_jsx", "defaultColumnTypes", "columnsStateInitializer", "state", "props", "apiRef", "_props$initialState", "_ref", "_props$columnVisibili", "_props$initialState2", "columnsState", "columnTypes", "columnsToUpsert", "columns", "initialState", "columnVisibilityModel", "keepOnlyColumnsToUpsert", "useGridColumns", "_props$initialState4", "_props$slotProps2", "logger", "previousColumnsProp", "useRef", "previousColumnTypesProp", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onColumnVisibilityModelChange", "stateSelector", "changeEvent", "setGridColumnsState", "useCallback", "debug", "setState", "forceUpdate", "publishEvent", "orderedFields", "getColumn", "field", "getAllColumns", "getVisibleColumns", "getColumnIndex", "useVisibleColumns", "arguments", "length", "undefined", "findIndex", "col", "getColumnPosition", "index", "setColumnVisibilityModel", "model", "currentModel", "updateColumns", "setColumnVisibility", "isVisible", "_columnVisibilityMode", "isCurrentlyVisible", "newModel", "getColumnIndexRelativeToVisibleColumns", "allColumns", "setColumnIndex", "targetIndexPosition", "oldIndexPosition", "concat", "updatedColumns", "fieldRemoved", "splice", "params", "column", "targetIndex", "oldIndex", "setColumn<PERSON><PERSON><PERSON>", "width", "_apiRef$current$getRo", "_apiRef$current$getRo2", "lookup", "newColumn", "hasBeenResized", "getRootDimensions", "viewportInnerSize", "element", "getColumnHeaderElement", "colDef", "columnApi", "columnReorderApi", "signature", "DataGrid", "stateExportPreProcessing", "prevState", "context", "_props$initialState$c", "_props$initialState3", "columnsStateToExport", "columnVisibilityModelToExport", "shouldExportColumnVisibilityModel", "exportOnlyDirtyModels", "Object", "keys", "dimensions", "for<PERSON>ach", "colDefDimensions", "propertyName", "propertyValue", "Infinity", "stateRestorePreProcessing", "_context$stateToResto", "columnVisibilityModelToImport", "stateToRestore", "preferencePanelPreProcessing", "initialValue", "value", "_props$slotProps", "ColumnsPanel", "slots", "columnsPanel", "slotProps", "addColumnMenuItems", "columnMenuItems", "disableColumnSelector", "prevInnerWidth", "handleGridSizeChange", "hydrateColumns", "info", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columns/useGridColumns.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from './gridColumnsSelector';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nimport { hydrateColumnsWidth, createColumnsState, mergeColumnsState, COLUMNS_DIMENSION_PROPERTIES } from './gridColumnsUtils';\nimport { GridPreferencePanelsValue } from '../preferencesPanel';\nimport { getGridDefaultColumnTypes } from '../../../colDef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultColumnTypes = getGridDefaultColumnTypes();\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  var _props$initialState, _ref, _props$columnVisibili, _props$initialState2;\n  const columnsState = createColumnsState({\n    apiRef,\n    columnTypes: defaultColumnTypes,\n    columnsToUpsert: props.columns,\n    initialState: (_props$initialState = props.initialState) == null ? void 0 : _props$initialState.columns,\n    columnVisibilityModel: (_ref = (_props$columnVisibili = props.columnVisibilityModel) != null ? _props$columnVisibili : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.columns) == null ? void 0 : _props$initialState2.columnVisibilityModel) != null ? _ref : {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  var _props$initialState4, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const columnTypes = defaultColumnTypes;\n  const previousColumnsProp = React.useRef(props.columns);\n  const previousColumnTypesProp = React.useRef(columnTypes);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnTypes,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, columnTypes]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState, columnTypes]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    var _columnVisibilityMode;\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = (_columnVisibilityMode = columnVisibilityModel[field]) != null ? _columnVisibilityMode : true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    var _apiRef$current$getRo, _apiRef$current$getRo2;\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), (_apiRef$current$getRo = (_apiRef$current$getRo2 = apiRef.current.getRootDimensions()) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState$c, _props$initialState3;\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys((_props$initialState$c = (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.columns) == null ? void 0 : _props$initialState3.columnVisibilityModel) != null ? _props$initialState$c : {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, (_props$initialState4 = props.initialState) == null ? void 0 : _props$initialState4.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const columnVisibilityModelToImport = (_context$stateToResto = context.stateToRestore.columns) == null ? void 0 : _context$stateToResto.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef, columnTypes]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      var _props$slotProps;\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), viewportInnerSize.width));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState, columnTypes]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /**\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns && previousColumnTypesProp.current === columnTypes) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    previousColumnTypesProp.current = columnTypes;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, columnTypes]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,wBAAwB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEC,iCAAiC,EAAEC,oCAAoC,EAAEC,2BAA2B,QAAQ,uBAAuB;AACzP,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,oCAAoC;AAC1F,SAASC,4BAA4B,EAAEC,0BAA0B,QAAQ,2BAA2B;AACpG,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,4BAA4B,QAAQ,oBAAoB;AAC7H,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,yBAAyB,QAAQ,iBAAiB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGH,yBAAyB,CAAC,CAAC;AACtD,OAAO,MAAMI,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC/D,IAAIC,mBAAmB,EAAEC,IAAI,EAAEC,qBAAqB,EAAEC,oBAAoB;EAC1E,MAAMC,YAAY,GAAGhB,kBAAkB,CAAC;IACtCW,MAAM;IACNM,WAAW,EAAEV,kBAAkB;IAC/BW,eAAe,EAAER,KAAK,CAACS,OAAO;IAC9BC,YAAY,EAAE,CAACR,mBAAmB,GAAGF,KAAK,CAACU,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,mBAAmB,CAACO,OAAO;IACvGE,qBAAqB,EAAE,CAACR,IAAI,GAAG,CAACC,qBAAqB,GAAGJ,KAAK,CAACW,qBAAqB,KAAK,IAAI,GAAGP,qBAAqB,GAAG,CAACC,oBAAoB,GAAGL,KAAK,CAACU,YAAY,KAAK,IAAI,IAAI,CAACL,oBAAoB,GAAGA,oBAAoB,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,oBAAoB,CAACM,qBAAqB,KAAK,IAAI,GAAGR,IAAI,GAAG,CAAC,CAAC;IACtTS,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAOtC,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACzBU,OAAO,EAAEH;EACX,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,cAAcA,CAACZ,MAAM,EAAED,KAAK,EAAE;EAC5C,IAAIc,oBAAoB,EAAEC,iBAAiB;EAC3C,MAAMC,MAAM,GAAGvC,aAAa,CAACwB,MAAM,EAAE,gBAAgB,CAAC;EACtD,MAAMM,WAAW,GAAGV,kBAAkB;EACtC,MAAMoB,mBAAmB,GAAG1C,KAAK,CAAC2C,MAAM,CAAClB,KAAK,CAACS,OAAO,CAAC;EACvD,MAAMU,uBAAuB,GAAG5C,KAAK,CAAC2C,MAAM,CAACX,WAAW,CAAC;EACzDN,MAAM,CAACmB,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAEvB,KAAK,CAACW,qBAAqB;IACtCa,YAAY,EAAExB,KAAK,CAACyB,6BAA6B;IACjDC,aAAa,EAAE5C,iCAAiC;IAChD6C,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAGrD,KAAK,CAACsD,WAAW,CAACvB,YAAY,IAAI;IAC5DU,MAAM,CAACc,KAAK,CAAC,yBAAyB,CAAC;IACvC7B,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAACxC,iBAAiB,CAACe,YAAY,CAAC,CAAC;IACxDL,MAAM,CAACmB,OAAO,CAACY,WAAW,CAAC,CAAC;IAC5B/B,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,eAAe,EAAE3B,YAAY,CAAC4B,aAAa,CAAC;EAC1E,CAAC,EAAE,CAAClB,MAAM,EAAEf,MAAM,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAMkC,SAAS,GAAG5D,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAIxD,wBAAwB,CAACqB,MAAM,CAAC,CAACmC,KAAK,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EAC/F,MAAMoC,aAAa,GAAG9D,KAAK,CAACsD,WAAW,CAAC,MAAMlD,6BAA6B,CAACsB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC9F,MAAMqC,iBAAiB,GAAG/D,KAAK,CAACsD,WAAW,CAAC,MAAM9C,oCAAoC,CAACkB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACzG,MAAMsC,cAAc,GAAGhE,KAAK,CAACsD,WAAW,CAAC,UAACO,KAAK,EAA+B;IAAA,IAA7BI,iBAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACvE,MAAMhC,OAAO,GAAG+B,iBAAiB,GAAGzD,oCAAoC,CAACkB,MAAM,CAAC,GAAGtB,6BAA6B,CAACsB,MAAM,CAAC;IACxH,OAAOQ,OAAO,CAACmC,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACT,KAAK,KAAKA,KAAK,CAAC;EACtD,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZ,MAAM6C,iBAAiB,GAAGvE,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAI;IACnD,MAAMW,KAAK,GAAGR,cAAc,CAACH,KAAK,CAAC;IACnC,OAAOpD,2BAA2B,CAACiB,MAAM,CAAC,CAAC8C,KAAK,CAAC;EACnD,CAAC,EAAE,CAAC9C,MAAM,EAAEsC,cAAc,CAAC,CAAC;EAC5B,MAAMS,wBAAwB,GAAGzE,KAAK,CAACsD,WAAW,CAACoB,KAAK,IAAI;IAC1D,MAAMC,YAAY,GAAGpE,iCAAiC,CAACmB,MAAM,CAAC;IAC9D,IAAIiD,YAAY,KAAKD,KAAK,EAAE;MAC1BhD,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAAChC,KAAK,IAAIzB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;QACnDU,OAAO,EAAEnB,kBAAkB,CAAC;UAC1BW,MAAM;UACNM,WAAW;UACXC,eAAe,EAAE,EAAE;UACnBE,YAAY,EAAEiC,SAAS;UACvBhC,qBAAqB,EAAEsC,KAAK;UAC5BrC,uBAAuB,EAAE;QAC3B,CAAC;MACH,CAAC,CAAC,CAAC;MACHX,MAAM,CAACmB,OAAO,CAACY,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC/B,MAAM,EAAEM,WAAW,CAAC,CAAC;EACzB,MAAM4C,aAAa,GAAG5E,KAAK,CAACsD,WAAW,CAACpB,OAAO,IAAI;IACjD,MAAMH,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAEC,OAAO;MACxBC,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFgB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAE2B,mBAAmB,EAAErB,WAAW,CAAC,CAAC;EAC9C,MAAM6C,mBAAmB,GAAG7E,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEiB,SAAS,KAAK;IAClE,IAAIC,qBAAqB;IACzB,MAAM3C,qBAAqB,GAAG7B,iCAAiC,CAACmB,MAAM,CAAC;IACvE,MAAMsD,kBAAkB,GAAG,CAACD,qBAAqB,GAAG3C,qBAAqB,CAACyB,KAAK,CAAC,KAAK,IAAI,GAAGkB,qBAAqB,GAAG,IAAI;IACxH,IAAID,SAAS,KAAKE,kBAAkB,EAAE;MACpC,MAAMC,QAAQ,GAAGlF,QAAQ,CAAC,CAAC,CAAC,EAAEqC,qBAAqB,EAAE;QACnD,CAACyB,KAAK,GAAGiB;MACX,CAAC,CAAC;MACFpD,MAAM,CAACmB,OAAO,CAAC4B,wBAAwB,CAACQ,QAAQ,CAAC;IACnD;EACF,CAAC,EAAE,CAACvD,MAAM,CAAC,CAAC;EACZ,MAAMwD,sCAAsC,GAAGlF,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAI;IACxE,MAAMsB,UAAU,GAAGhF,wBAAwB,CAACuB,MAAM,CAAC;IACnD,OAAOyD,UAAU,CAACd,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKT,KAAK,CAAC;EACnD,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZ,MAAM0D,cAAc,GAAGpF,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEwB,mBAAmB,KAAK;IACvE,MAAMF,UAAU,GAAGhF,wBAAwB,CAACuB,MAAM,CAAC;IACnD,MAAM4D,gBAAgB,GAAGJ,sCAAsC,CAACrB,KAAK,CAAC;IACtE,IAAIyB,gBAAgB,KAAKD,mBAAmB,EAAE;MAC5C;IACF;IACA5C,MAAM,CAACc,KAAK,kBAAAgC,MAAA,CAAkB1B,KAAK,gBAAA0B,MAAA,CAAaF,mBAAmB,CAAE,CAAC;IACtE,MAAMG,cAAc,GAAG,CAAC,GAAGL,UAAU,CAAC;IACtC,MAAMM,YAAY,GAAGD,cAAc,CAACE,MAAM,CAACJ,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClEE,cAAc,CAACE,MAAM,CAACL,mBAAmB,EAAE,CAAC,EAAEI,YAAY,CAAC;IAC3DpC,mBAAmB,CAACtD,QAAQ,CAAC,CAAC,CAAC,EAAEO,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC,EAAE;MAC/EmC,aAAa,EAAE6B;IACjB,CAAC,CAAC,CAAC;IACH,MAAMG,MAAM,GAAG;MACbC,MAAM,EAAElE,MAAM,CAACmB,OAAO,CAACe,SAAS,CAACC,KAAK,CAAC;MACvCgC,WAAW,EAAEnE,MAAM,CAACmB,OAAO,CAACqC,sCAAsC,CAACrB,KAAK,CAAC;MACzEiC,QAAQ,EAAER;IACZ,CAAC;IACD5D,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAEiC,MAAM,CAAC;EAC1D,CAAC,EAAE,CAACjE,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,EAAE6B,sCAAsC,CAAC,CAAC;EACjF,MAAMa,cAAc,GAAG/F,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEmC,KAAK,KAAK;IACzD,IAAIC,qBAAqB,EAAEC,sBAAsB;IACjDzD,MAAM,CAACc,KAAK,oBAAAgC,MAAA,CAAoB1B,KAAK,gBAAA0B,MAAA,CAAaS,KAAK,CAAE,CAAC;IAC1D,MAAMjE,YAAY,GAAGzB,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC;IACnE,MAAMoE,MAAM,GAAG7D,YAAY,CAACoE,MAAM,CAACtC,KAAK,CAAC;IACzC,MAAMuC,SAAS,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAE6F,MAAM,EAAE;MACrCI,KAAK;MACLK,cAAc,EAAE;IAClB,CAAC,CAAC;IACFhD,mBAAmB,CAACvC,mBAAmB,CAACf,QAAQ,CAAC,CAAC,CAAC,EAAEgC,YAAY,EAAE;MACjEoE,MAAM,EAAEpG,QAAQ,CAAC,CAAC,CAAC,EAAEgC,YAAY,CAACoE,MAAM,EAAE;QACxC,CAACtC,KAAK,GAAGuC;MACX,CAAC;IACH,CAAC,CAAC,EAAE,CAACH,qBAAqB,GAAG,CAACC,sBAAsB,GAAGxE,MAAM,CAACmB,OAAO,CAACyD,iBAAiB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,sBAAsB,CAACK,iBAAiB,CAACP,KAAK,KAAK,IAAI,GAAGC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACnMvE,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAE;MAC/C8C,OAAO,EAAE9E,MAAM,CAACmB,OAAO,CAAC4D,sBAAsB,CAAC5C,KAAK,CAAC;MACrD6C,MAAM,EAAEN,SAAS;MACjBJ;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtE,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,CAAC,CAAC;EACzC,MAAMsD,SAAS,GAAG;IAChB/C,SAAS;IACTE,aAAa;IACbE,cAAc;IACdO,iBAAiB;IACjBR,iBAAiB;IACjBmB,sCAAsC;IACtCN,aAAa;IACbH,wBAAwB;IACxBI,mBAAmB;IACnBkB;EACF,CAAC;EACD,MAAMa,gBAAgB,GAAG;IACvBxB;EACF,CAAC;EACDnF,gBAAgB,CAACyB,MAAM,EAAEiF,SAAS,EAAE,QAAQ,CAAC;EAC7C1G,gBAAgB,CAACyB,MAAM,EAAEkF,gBAAgB,EAAEnF,KAAK,CAACoF,SAAS,KAAKnG,aAAa,CAACoG,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAE7G;AACF;AACA;EACE,MAAMC,wBAAwB,GAAG/G,KAAK,CAACsD,WAAW,CAAC,CAAC0D,SAAS,EAAEC,OAAO,KAAK;IACzE,IAAIC,qBAAqB,EAAEC,oBAAoB;IAC/C,MAAMC,oBAAoB,GAAG,CAAC,CAAC;IAC/B,MAAMC,6BAA6B,GAAG9G,iCAAiC,CAACmB,MAAM,CAAC;IAC/E,MAAM4F,iCAAiC;IACvC;IACA,CAACL,OAAO,CAACM,qBAAqB;IAC9B;IACA9F,KAAK,CAACW,qBAAqB,IAAI,IAAI;IACnC;IACA;IACAoF,MAAM,CAACC,IAAI,CAAC,CAACP,qBAAqB,GAAG,CAACC,oBAAoB,GAAG1F,KAAK,CAACU,YAAY,KAAK,IAAI,IAAI,CAACgF,oBAAoB,GAAGA,oBAAoB,CAACjF,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiF,oBAAoB,CAAC/E,qBAAqB,KAAK,IAAI,GAAG8E,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC/C,MAAM,GAAG,CAAC;IACjQ;IACAqD,MAAM,CAACC,IAAI,CAACJ,6BAA6B,CAAC,CAAClD,MAAM,GAAG,CAAC;IACrD,IAAImD,iCAAiC,EAAE;MACrCF,oBAAoB,CAAChF,qBAAqB,GAAGiF,6BAA6B;IAC5E;IACAD,oBAAoB,CAACzD,aAAa,GAAGxD,wBAAwB,CAACuB,MAAM,CAAC;IACrE,MAAMQ,OAAO,GAAG9B,6BAA6B,CAACsB,MAAM,CAAC;IACrD,MAAMgG,UAAU,GAAG,CAAC,CAAC;IACrBxF,OAAO,CAACyF,OAAO,CAACjB,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACL,cAAc,EAAE;QACzB,MAAMuB,gBAAgB,GAAG,CAAC,CAAC;QAC3B3G,4BAA4B,CAAC0G,OAAO,CAACE,YAAY,IAAI;UACnD,IAAIC,aAAa,GAAGpB,MAAM,CAACmB,YAAY,CAAC;UACxC,IAAIC,aAAa,KAAKC,QAAQ,EAAE;YAC9BD,aAAa,GAAG,CAAC,CAAC;UACpB;UACAF,gBAAgB,CAACC,YAAY,CAAC,GAAGC,aAAa;QAChD,CAAC,CAAC;QACFJ,UAAU,CAAChB,MAAM,CAAC7C,KAAK,CAAC,GAAG+D,gBAAgB;MAC7C;IACF,CAAC,CAAC;IACF,IAAIJ,MAAM,CAACC,IAAI,CAACC,UAAU,CAAC,CAACvD,MAAM,GAAG,CAAC,EAAE;MACtCiD,oBAAoB,CAACM,UAAU,GAAGA,UAAU;IAC9C;IACA,OAAO3H,QAAQ,CAAC,CAAC,CAAC,EAAEiH,SAAS,EAAE;MAC7B9E,OAAO,EAAEkF;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1F,MAAM,EAAED,KAAK,CAACW,qBAAqB,EAAE,CAACG,oBAAoB,GAAGd,KAAK,CAACU,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,oBAAoB,CAACL,OAAO,CAAC,CAAC;EACtI,MAAM8F,yBAAyB,GAAGhI,KAAK,CAACsD,WAAW,CAAC,CAACqC,MAAM,EAAEsB,OAAO,KAAK;IACvE,IAAIgB,qBAAqB;IACzB,MAAMC,6BAA6B,GAAG,CAACD,qBAAqB,GAAGhB,OAAO,CAACkB,cAAc,CAACjG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+F,qBAAqB,CAAC7F,qBAAqB;IAC7J,MAAMD,YAAY,GAAG8E,OAAO,CAACkB,cAAc,CAACjG,OAAO;IACnD,IAAIgG,6BAA6B,IAAI,IAAI,IAAI/F,YAAY,IAAI,IAAI,EAAE;MACjE,OAAOwD,MAAM;IACf;IACA,MAAM5D,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAE,EAAE;MACnBE,YAAY;MACZC,qBAAqB,EAAE8F,6BAA6B;MACpD7F,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFX,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAACxC,iBAAiB,CAACe,YAAY,CAAC,CAAC;IACxD,IAAII,YAAY,IAAI,IAAI,EAAE;MACxBT,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,eAAe,EAAE3B,YAAY,CAAC4B,aAAa,CAAC;IAC1E;IACA,OAAOgC,MAAM;EACf,CAAC,EAAE,CAACjE,MAAM,EAAEM,WAAW,CAAC,CAAC;EACzB,MAAMoG,4BAA4B,GAAGpI,KAAK,CAACsD,WAAW,CAAC,CAAC+E,YAAY,EAAEC,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKpH,yBAAyB,CAACgB,OAAO,EAAE;MAC/C,IAAIqG,gBAAgB;MACpB,MAAMC,YAAY,GAAG/G,KAAK,CAACgH,KAAK,CAACC,YAAY;MAC7C,OAAO,aAAarH,IAAI,CAACmH,YAAY,EAAEzI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACwI,gBAAgB,GAAG9G,KAAK,CAACkH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,gBAAgB,CAACG,YAAY,CAAC,CAAC;IAC7I;IACA,OAAOL,YAAY;EACrB,CAAC,EAAE,CAAC5G,KAAK,CAACgH,KAAK,CAACC,YAAY,EAAE,CAAClG,iBAAiB,GAAGf,KAAK,CAACkH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnG,iBAAiB,CAACkG,YAAY,CAAC,CAAC;EACvH,MAAME,kBAAkB,GAAG5I,KAAK,CAACsD,WAAW,CAACuF,eAAe,IAAI;IAC9D,IAAIpH,KAAK,CAACqH,qBAAqB,EAAE;MAC/B,OAAOD,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,uBAAuB,CAAC;EACtD,CAAC,EAAE,CAACpH,KAAK,CAACqH,qBAAqB,CAAC,CAAC;EACjClI,4BAA4B,CAACc,MAAM,EAAE,YAAY,EAAEkH,kBAAkB,CAAC;EACtEhI,4BAA4B,CAACc,MAAM,EAAE,aAAa,EAAEqF,wBAAwB,CAAC;EAC7EnG,4BAA4B,CAACc,MAAM,EAAE,cAAc,EAAEsG,yBAAyB,CAAC;EAC/EpH,4BAA4B,CAACc,MAAM,EAAE,iBAAiB,EAAE0G,4BAA4B,CAAC;;EAErF;AACF;AACA;EACE,MAAMW,cAAc,GAAG/I,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMqG,oBAAoB,GAAGzC,iBAAiB,IAAI;IAChD,IAAIwC,cAAc,CAAClG,OAAO,KAAK0D,iBAAiB,CAACP,KAAK,EAAE;MACtD+C,cAAc,CAAClG,OAAO,GAAG0D,iBAAiB,CAACP,KAAK;MAChD3C,mBAAmB,CAACvC,mBAAmB,CAACR,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC,EAAE+E,iBAAiB,CAACP,KAAK,CAAC,CAAC;IACnH;EACF,CAAC;EACDrF,sBAAsB,CAACe,MAAM,EAAE,yBAAyB,EAAEsH,oBAAoB,CAAC;;EAE/E;AACF;AACA;EACE,MAAMC,cAAc,GAAGjJ,KAAK,CAACsD,WAAW,CAAC,MAAM;IAC7Cb,MAAM,CAACyG,IAAI,iEAAiE,CAAC;IAC7E,MAAMnH,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAE,EAAE;MACnBE,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFgB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,EAAErB,WAAW,CAAC,CAAC;EACtDnB,0BAA0B,CAACa,MAAM,EAAE,gBAAgB,EAAEuH,cAAc,CAAC;;EAEpE;AACF;AACA;EACE;EACA;EACA,MAAME,aAAa,GAAGnJ,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACxC3C,KAAK,CAACoJ,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAACtG,OAAO,EAAE;MACzBsG,aAAa,CAACtG,OAAO,GAAG,KAAK;MAC7B;IACF;IACAJ,MAAM,CAACyG,IAAI,yCAAA3D,MAAA,CAAyC9D,KAAK,CAACS,OAAO,CAACiC,MAAM,CAAE,CAAC;IAC3E,IAAIzB,mBAAmB,CAACG,OAAO,KAAKpB,KAAK,CAACS,OAAO,IAAIU,uBAAuB,CAACC,OAAO,KAAKb,WAAW,EAAE;MACpG;IACF;IACA,MAAMD,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXG,YAAY,EAAEiC,SAAS;MACvB;MACAnC,eAAe,EAAER,KAAK,CAACS,OAAO;MAC9BG,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFK,mBAAmB,CAACG,OAAO,GAAGpB,KAAK,CAACS,OAAO;IAC3CU,uBAAuB,CAACC,OAAO,GAAGb,WAAW;IAC7CqB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACU,MAAM,EAAEf,MAAM,EAAE2B,mBAAmB,EAAE5B,KAAK,CAACS,OAAO,EAAEF,WAAW,CAAC,CAAC;EACrEhC,KAAK,CAACoJ,SAAS,CAAC,MAAM;IACpB,IAAI3H,KAAK,CAACW,qBAAqB,KAAKgC,SAAS,EAAE;MAC7C1C,MAAM,CAACmB,OAAO,CAAC4B,wBAAwB,CAAChD,KAAK,CAACW,qBAAqB,CAAC;IACtE;EACF,CAAC,EAAE,CAACV,MAAM,EAAEe,MAAM,EAAEhB,KAAK,CAACW,qBAAqB,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}