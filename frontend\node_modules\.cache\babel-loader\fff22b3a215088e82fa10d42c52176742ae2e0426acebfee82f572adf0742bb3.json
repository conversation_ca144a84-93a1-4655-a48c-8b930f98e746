{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  row,\n  index,\n  columns,\n  onCellEdit\n}) => {\n  _s();\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n  const handleCellSave = columnField => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      handleCellSave(columnField);\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Draggable, {\n    draggableId: row.id.toString(),\n    index: index,\n    children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableRow, {\n      ref: provided.innerRef,\n      ...provided.draggableProps,\n      sx: {\n        backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n        transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n        boxShadow: snapshot.isDragging ? 3 : 0,\n        transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n        '&.removed-row': {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        },\n        ...(snapshot.isDragging && {\n          zIndex: 1000\n        }),\n        ...(row._removed && {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        })\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        ...provided.dragHandleProps,\n        sx: {\n          width: 40,\n          cursor: 'grab',\n          '&:active': {\n            cursor: 'grabbing'\n          },\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n          sx: {\n            color: 'text.secondary'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), columns.map(column => {\n        const value = row[column.field];\n        const isEditing = editingCell === column.field;\n        return /*#__PURE__*/_jsxDEV(TableCell, {\n          sx: {\n            padding: '8px',\n            cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default'\n          },\n          onClick: () => handleCellClick(column.field, value),\n          children: isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            value: editValue,\n            onChange: e => setEditValue(e.target.value),\n            onBlur: () => handleCellSave(column.field),\n            onKeyDown: e => handleKeyPress(e, column.field),\n            autoFocus: true,\n            fullWidth: true,\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 19\n          }, this) : column.renderCell ? column.renderCell({\n            row,\n            value\n          }) : value\n        }, column.field, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 15\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n}, \"vUzEbioNkVLGJwl96b8AAiFyV68=\")), \"vUzEbioNkVLGJwl96b8AAiFyV68=\");\n\n// 默认的REMARKS选项\n_c2 = DraggableTableRow;\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c3 = _s2(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c4 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s3();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01',\n            'FEB': '02',\n            'MAR': '03',\n            'APR': '04',\n            'MAY': '05',\n            'JUNE': '06',\n            'JULY': '07',\n            'AUG': '08',\n            'SEP': '09',\n            'OCT': '10',\n            'NOV': '11',\n            'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            console.log(`解析工作表名称: ${selectedWorksheet} -> ${year}-${monthNumber}`);\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback(result => {\n    if (!result.destination) {\n      return;\n    }\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = {\n            ...row,\n            [field]: newValue\n          };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n          return updatedRow;\n        }\n        return row;\n      });\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) {\n          // 确保更新时保持原始数据类型\n          const updatedRow = {\n            ...row\n          };\n          Object.keys(newRow).forEach(key => {\n            if (newRow[key] !== undefined) {\n              updatedRow[key] = newRow[key];\n            }\n          });\n          return updatedRow;\n        }\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1180,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1222,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1199,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1250,\n                columnNumber: 17\n              }, this), \"\\u6570\\u636E\\u641C\\u7D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: searchColumn,\n                  label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n                  onChange: e => setSearchColumn(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u5168\\u90E8\\u5217\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"NO\",\n                    children: \"NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1263,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DATE\",\n                    children: \"DATE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"VEHICLE NO\",\n                    children: \"VEHICLE NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"RO NO\",\n                    children: \"RO NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"KM\",\n                    children: \"KM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"REMARKS\",\n                    children: \"REMARKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"MAXCHECK\",\n                    children: \"HOURS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMMISSION\",\n                    children: \"AMOUNT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n                value: searchText,\n                onChange: e => setSearchText(e.target.value),\n                sx: {\n                  flexGrow: 1,\n                  minWidth: 200\n                },\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1283,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1282,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setSearchText(''),\n                      edge: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1293,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1288,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1287,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 17\n              }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1311,\n                columnNumber: 17\n              }, this), \"\\u64CD\\u4F5C\\u9009\\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"success\",\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1319,\n                  columnNumber: 30\n                }, this),\n                onClick: handleDownload,\n                children: \"\\u4E0B\\u8F7DExcel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 53\n                }, this) : /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 102\n                }, this),\n                onClick: generateDocument,\n                disabled: isGeneratingDocument,\n                children: isGeneratingDocument ? '生成中...' : '生成Excel文档'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1338,\n                  columnNumber: 30\n                }, this),\n                onClick: handleCleanup,\n                children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragDropContext, {\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    width: 40,\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u62D6\\u62FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 19\n                }, this), columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'bold',\n                    backgroundColor: 'background.default',\n                    borderRight: '1px solid',\n                    borderColor: 'divider',\n                    '&:last-child': {\n                      borderRight: 'none'\n                    }\n                  },\n                  children: column.headerName\n                }, column.field, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Droppable, {\n              droppableId: \"table-body\",\n              children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableBody, {\n                ref: provided.innerRef,\n                ...provided.droppableProps,\n                sx: {\n                  backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                  transition: 'background-color 0.2s ease'\n                },\n                children: [memoGridData.filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                .slice(paginationModel.page * paginationModel.pageSize, (paginationModel.page + 1) * paginationModel.pageSize).map((row, index) => /*#__PURE__*/_jsxDEV(DraggableTableRow, {\n                  row: row,\n                  index: index,\n                  columns: columns\n                }, row.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 25\n                }, this)), memoGridData.find(row => row.NO === 'TOTAL') && /*#__PURE__*/_jsxDEV(TableRow, {\n                  className: \"total-row\",\n                  sx: {\n                    backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                    fontWeight: 'bold',\n                    '& .MuiTableCell-root': {\n                      fontWeight: 'bold',\n                      borderTop: '2px solid',\n                      borderColor: 'primary.main'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    sx: {\n                      width: 40\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1416,\n                    columnNumber: 25\n                  }, this), columns.map(column => {\n                    const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                    const value = totalRow ? totalRow[column.field] : '';\n                    return /*#__PURE__*/_jsxDEV(TableCell, {\n                      sx: {\n                        padding: '8px'\n                      },\n                      children: column.renderCell ? column.renderCell({\n                        row: totalRow,\n                        value\n                      }) : value\n                    }, column.field, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1423,\n                      columnNumber: 29\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 23\n                }, this), provided.placeholder]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          component: \"div\",\n          count: memoGridData.filter(row => row.NO !== 'TOTAL').length,\n          page: paginationModel.page,\n          onPageChange: (event, newPage) => {\n            setPaginationModel(prev => ({\n              ...prev,\n              page: newPage\n            }));\n          },\n          rowsPerPage: paginationModel.pageSize,\n          onRowsPerPageChange: event => {\n            const newPageSize = parseInt(event.target.value, 10);\n            setPaginationModel({\n              page: 0,\n              pageSize: newPageSize\n            });\n            localStorage.setItem('dataGridPageSize', newPageSize.toString());\n          },\n          rowsPerPageOptions: [25, 50, 100],\n          labelRowsPerPage: \"\\u6BCF\\u9875\\u884C\\u6570:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} 共 ${count} 条`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1473,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1470,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1469,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1490,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1500,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1498,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1483,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1531,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1526,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1536,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1535,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1512,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1544,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1543,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1460,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1560,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1573,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1574,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1572,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1549,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1196,\n    columnNumber: 5\n  }, this);\n};\n_s3(ResultDisplay, \"RuNKnzTnsOQleq4TSxXNnDnj71o=\");\n_c5 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DraggableTableRow$React.memo\");\n$RefreshReg$(_c2, \"DraggableTableRow\");\n$RefreshReg$(_c3, \"RemarkChip$React.memo\");\n$RefreshReg$(_c4, \"RemarkChip\");\n$RefreshReg$(_c5, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "SettingsIcon", "axios", "API_URL", "FixedSizeList", "DragDropContext", "Droppable", "Draggable", "DragIndicatorIcon", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DraggableTableRow", "_s", "memo", "_c", "row", "index", "columns", "onCellEdit", "editingCell", "setEditingCell", "editValue", "setEditValue", "handleCellClick", "columnField", "currentValue", "column", "find", "col", "field", "editable", "isTotal", "_removed", "handleCellSave", "id", "handleKeyPress", "e", "key", "draggableId", "toString", "children", "provided", "snapshot", "ref", "innerRef", "draggableProps", "sx", "backgroundColor", "isDragging", "transform", "boxShadow", "transition", "color", "textDecoration", "zIndex", "dragHandleProps", "width", "cursor", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "value", "isEditing", "padding", "onClick", "size", "onChange", "target", "onBlur", "onKeyDown", "autoFocus", "fullWidth", "variant", "renderCell", "_c2", "DEFAULT_REMARKS_OPTIONS", "RemarkChip", "_s2", "_c3", "rowId", "text", "isSelected", "uiState", "setUiState", "handleClick", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "height", "lineHeight", "_c4", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s3", "columnOrder", "headerName", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "getSelectedMonth", "commissionState", "selectedWorksheet", "match", "monthName", "year", "monthMap", "monthNumber", "toUpperCase", "console", "log", "error", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "handleDragEnd", "result", "destination", "sourceIndex", "source", "destinationIndex", "setGridData", "prev", "newData", "nonTotalRows", "filter", "NO", "totalRow", "removed", "splice", "for<PERSON>ach", "reorderedData", "recalculateTotal", "handleCellEdit", "newValue", "updatedData", "updatedRow", "numericFields", "includes", "undefined", "numValue", "Number", "isNaN", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "timer", "setPaginationModel", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "setItem", "originalData", "setOriginalData", "stringify", "processedData", "REMARKS", "_selected_remarks", "gridData", "length", "validatedData", "processedWithIds", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "COMMISSION", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "reduce", "sum", "newTotal", "processRowUpdate", "newRow", "totalValue", "Object", "keys", "onProcessRowUpdateError", "message", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "deleteOption", "item", "handleRemoveRow", "noCounter", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "hasOwnProperty", "flex", "params", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "startIcon", "fontWeight", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "values", "some", "cellValue", "memoGridData", "dataLength", "py", "mt", "mb", "justifyContent", "flexWrap", "direction", "spacing", "icon", "container", "xs", "md", "sm", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "disabled", "onDragEnd", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "borderColor", "droppableId", "droppableProps", "isDraggingOver", "slice", "className", "borderTop", "component", "count", "onPageChange", "event", "newPage", "rowsPerPage", "onRowsPerPageChange", "newPageSize", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "onClose", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "margin", "type", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel,\n  Grid,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\n\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = React.memo(({ row, index, columns, onCellEdit }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n\n  const handleCellSave = (columnField) => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      handleCellSave(columnField);\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n\n  return (\n    <Draggable draggableId={row.id.toString()} index={index}>\n      {(provided, snapshot) => (\n        <TableRow\n          ref={provided.innerRef}\n          {...provided.draggableProps}\n          sx={{\n            backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n            transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n            boxShadow: snapshot.isDragging ? 3 : 0,\n            transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n            '&.removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            },\n            ...(snapshot.isDragging && {\n              zIndex: 1000,\n            }),\n            ...(row._removed && {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            }),\n          }}\n        >\n          {/* 拖拽手柄 */}\n          <TableCell\n            {...provided.dragHandleProps}\n            sx={{\n              width: 40,\n              cursor: 'grab',\n              '&:active': { cursor: 'grabbing' },\n              textAlign: 'center',\n            }}\n          >\n            <DragIndicatorIcon sx={{ color: 'text.secondary' }} />\n          </TableCell>\n\n          {/* 数据列 */}\n          {columns.map((column) => {\n            const value = row[column.field];\n            const isEditing = editingCell === column.field;\n\n            return (\n              <TableCell\n                key={column.field}\n                sx={{\n                  padding: '8px',\n                  cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default',\n                }}\n                onClick={() => handleCellClick(column.field, value)}\n              >\n                {isEditing ? (\n                  <TextField\n                    size=\"small\"\n                    value={editValue}\n                    onChange={(e) => setEditValue(e.target.value)}\n                    onBlur={() => handleCellSave(column.field)}\n                    onKeyDown={(e) => handleKeyPress(e, column.field)}\n                    autoFocus\n                    fullWidth\n                    variant=\"outlined\"\n                  />\n                ) : (\n                  column.renderCell ? column.renderCell({ row, value }) : value\n                )}\n              </TableCell>\n            );\n          })}\n        </TableRow>\n      )}\n    </Draggable>\n  );\n});\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',\n            'MAY': '05', 'JUNE': '06', 'JULY': '07', 'AUG': '08',\n            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            console.log(`解析工作表名称: ${selectedWorksheet} -> ${year}-${monthNumber}`);\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback((result) => {\n    if (!result.destination) {\n      return;\n    }\n\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = { ...row, [field]: newValue };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n\n          return updatedRow;\n        }\n        return row;\n      });\n\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) {\n          // 确保更新时保持原始数据类型\n          const updatedRow = { ...row };\n          Object.keys(newRow).forEach(key => {\n            if (newRow[key] !== undefined) {\n              updatedRow[key] = newRow[key];\n            }\n          });\n          return updatedRow;\n        }\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 处理结果概览 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 数据搜索和操作选项 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={3}>\n            {/* 数据搜索 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SearchIcon sx={{ color: 'primary.main' }} />\n                数据搜索\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n                <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n                  <InputLabel>搜索范围</InputLabel>\n                  <Select\n                    value={searchColumn}\n                    label=\"搜索范围\"\n                    onChange={(e) => setSearchColumn(e.target.value)}\n                  >\n                    <MenuItem value=\"all\">全部列</MenuItem>\n                    <MenuItem value=\"NO\">NO</MenuItem>\n                    <MenuItem value=\"DATE\">DATE</MenuItem>\n                    <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                    <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                    <MenuItem value=\"KM\">KM</MenuItem>\n                    <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                    <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                    <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n                  </Select>\n                </FormControl>\n\n                <TextField\n                  size=\"small\"\n                  placeholder=\"输入搜索内容...\"\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  sx={{ flexGrow: 1, minWidth: 200 }}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    ),\n                    endAdornment: searchText && (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => setSearchText('')}\n                          edge=\"end\"\n                        >\n                          <ClearIcon />\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {searchText && (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n                  </Typography>\n                )}\n              </Stack>\n            </Grid>\n\n            {/* 操作选项 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SettingsIcon sx={{ color: 'primary.main' }} />\n                操作选项\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>\n                <Button\n                  variant=\"contained\"\n                  color=\"success\"\n                  startIcon={<DownloadIcon />}\n                  onClick={handleDownload}\n                >\n                  下载Excel\n                </Button>\n\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <TableViewIcon />}\n                  onClick={generateDocument}\n                  disabled={isGeneratingDocument}\n                >\n                  {isGeneratingDocument ? '生成中...' : '生成Excel文档'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<RestartAltIcon />}\n                  onClick={handleCleanup}\n                >\n                  重新开始\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n      \n      {/* 拖拽数据表格 */}\n      <DragDropContext onDragEnd={handleDragEnd}>\n        <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <TableContainer>\n            <Table stickyHeader>\n              <TableHead>\n                <TableRow>\n                  {/* 拖拽手柄列 */}\n                  <TableCell sx={{ width: 40, fontWeight: 'bold' }}>\n                    拖拽\n                  </TableCell>\n                  {columns.map((column) => (\n                    <TableCell\n                      key={column.field}\n                      sx={{\n                        fontWeight: 'bold',\n                        backgroundColor: 'background.default',\n                        borderRight: '1px solid',\n                        borderColor: 'divider',\n                        '&:last-child': {\n                          borderRight: 'none',\n                        },\n                      }}\n                    >\n                      {column.headerName}\n                    </TableCell>\n                  ))}\n                </TableRow>\n              </TableHead>\n\n              <Droppable droppableId=\"table-body\">\n                {(provided, snapshot) => (\n                  <TableBody\n                    ref={provided.innerRef}\n                    {...provided.droppableProps}\n                    sx={{\n                      backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                      transition: 'background-color 0.2s ease',\n                    }}\n                  >\n                    {/* 只显示当前页的数据 */}\n                    {memoGridData\n                      .filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                      .slice(\n                        paginationModel.page * paginationModel.pageSize,\n                        (paginationModel.page + 1) * paginationModel.pageSize\n                      )\n                      .map((row, index) => (\n                        <DraggableTableRow\n                          key={row.id}\n                          row={row}\n                          index={index}\n                          columns={columns}\n                        />\n                      ))}\n\n                    {/* TOTAL行单独显示在最后 */}\n                    {memoGridData.find(row => row.NO === 'TOTAL') && (\n                      <TableRow className=\"total-row\" sx={{\n                        backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                        fontWeight: 'bold',\n                        '& .MuiTableCell-root': {\n                          fontWeight: 'bold',\n                          borderTop: '2px solid',\n                          borderColor: 'primary.main',\n                        },\n                      }}>\n                        <TableCell sx={{ width: 40 }}>\n                          {/* TOTAL行不显示拖拽手柄 */}\n                        </TableCell>\n                        {columns.map((column) => {\n                          const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                          const value = totalRow ? totalRow[column.field] : '';\n                          return (\n                            <TableCell key={column.field} sx={{ padding: '8px' }}>\n                              {column.renderCell ? column.renderCell({ row: totalRow, value }) : value}\n                            </TableCell>\n                          );\n                        })}\n                      </TableRow>\n                    )}\n\n                    {provided.placeholder}\n                  </TableBody>\n                )}\n              </Droppable>\n            </Table>\n          </TableContainer>\n\n          {/* 分页控件 */}\n          <TablePagination\n            component=\"div\"\n            count={memoGridData.filter(row => row.NO !== 'TOTAL').length}\n            page={paginationModel.page}\n            onPageChange={(event, newPage) => {\n              setPaginationModel(prev => ({ ...prev, page: newPage }));\n            }}\n            rowsPerPage={paginationModel.pageSize}\n            onRowsPerPageChange={(event) => {\n              const newPageSize = parseInt(event.target.value, 10);\n              setPaginationModel({ page: 0, pageSize: newPageSize });\n              localStorage.setItem('dataGridPageSize', newPageSize.toString());\n            }}\n            rowsPerPageOptions={[25, 50, 100]}\n            labelRowsPerPage=\"每页行数:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}\n          />\n        </Paper>\n      </DragDropContext>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,QACV,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,eAAe,EAAEC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACzE,OAAOC,iBAAiB,MAAM,mCAAmC;;AAGjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,iBAAiB,gBAAAC,EAAA,cAAGzE,KAAK,CAAC0E,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,GAAG;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAN,EAAA;EAC5E,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiF,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMmF,eAAe,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACrD;IACA,MAAMC,MAAM,GAAGT,OAAO,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,WAAW,CAAC;IAC7D,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACI,QAAQ,IAAIf,GAAG,CAACgB,OAAO,IAAIhB,GAAG,CAACiB,QAAQ,IAAIR,WAAW,KAAK,IAAI,EAAE;MACtF;IACF;IAEAJ,cAAc,CAACI,WAAW,CAAC;IAC3BF,YAAY,CAACG,YAAY,IAAI,EAAE,CAAC;EAClC,CAAC;EAED,MAAMQ,cAAc,GAAIT,WAAW,IAAK;IACtC,IAAIN,UAAU,EAAE;MACdA,UAAU,CAACH,GAAG,CAACmB,EAAE,EAAEV,WAAW,EAAEH,SAAS,CAAC;IAC5C;IACAD,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACC,CAAC,EAAEZ,WAAW,KAAK;IACzC,IAAIY,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBJ,cAAc,CAACT,WAAW,CAAC;IAC7B,CAAC,MAAM,IAAIY,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BjB,cAAc,CAAC,IAAI,CAAC;MACpBE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EAED,oBACErB,OAAA,CAACH,SAAS;IAACwC,WAAW,EAAEvB,GAAG,CAACmB,EAAE,CAACK,QAAQ,CAAC,CAAE;IAACvB,KAAK,EAAEA,KAAM;IAAAwB,QAAA,EACrDA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBzC,OAAA,CAACxB,QAAQ;MACPkE,GAAG,EAAEF,QAAQ,CAACG,QAAS;MAAA,GACnBH,QAAQ,CAACI,cAAc;MAC3BC,EAAE,EAAE;QACFC,eAAe,EAAEL,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,SAAS;QACjEC,SAAS,EAAEP,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,MAAM;QACxDE,SAAS,EAAER,QAAQ,CAACM,UAAU,GAAG,CAAC,GAAG,CAAC;QACtCG,UAAU,EAAET,QAAQ,CAACM,UAAU,GAAG,MAAM,GAAG,eAAe;QAC1D,eAAe,EAAE;UACfD,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;QACD,IAAIX,QAAQ,CAACM,UAAU,IAAI;UACzBM,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAIvC,GAAG,CAACiB,QAAQ,IAAI;UAClBe,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;MACH,CAAE;MAAAb,QAAA,gBAGFvC,OAAA,CAAC3B,SAAS;QAAA,GACJmE,QAAQ,CAACc,eAAe;QAC5BT,EAAE,EAAE;UACFU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YAAEA,MAAM,EAAE;UAAW,CAAC;UAClCC,SAAS,EAAE;QACb,CAAE;QAAAlB,QAAA,eAEFvC,OAAA,CAACF,iBAAiB;UAAC+C,EAAE,EAAE;YAAEM,KAAK,EAAE;UAAiB;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAGX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,IAAK;QACvB,MAAMsC,KAAK,GAAGjD,GAAG,CAACW,MAAM,CAACG,KAAK,CAAC;QAC/B,MAAMoC,SAAS,GAAG9C,WAAW,KAAKO,MAAM,CAACG,KAAK;QAE9C,oBACE5B,OAAA,CAAC3B,SAAS;UAERwE,EAAE,EAAE;YACFoB,OAAO,EAAE,KAAK;YACdT,MAAM,EAAE/B,MAAM,CAACI,QAAQ,IAAI,CAACf,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,IAAIN,MAAM,CAACG,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG;UAClG,CAAE;UACFsC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAACG,MAAM,CAACG,KAAK,EAAEmC,KAAK,CAAE;UAAAxB,QAAA,EAEnDyB,SAAS,gBACRhE,OAAA,CAAC7C,SAAS;YACRgH,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAE3C,SAAU;YACjBgD,QAAQ,EAAGjC,CAAC,IAAKd,YAAY,CAACc,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;YAC9CO,MAAM,EAAEA,CAAA,KAAMtC,cAAc,CAACP,MAAM,CAACG,KAAK,CAAE;YAC3C2C,SAAS,EAAGpC,CAAC,IAAKD,cAAc,CAACC,CAAC,EAAEV,MAAM,CAACG,KAAK,CAAE;YAClD4C,SAAS;YACTC,SAAS;YACTC,OAAO,EAAC;UAAU;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,GAEFpC,MAAM,CAACkD,UAAU,GAAGlD,MAAM,CAACkD,UAAU,CAAC;YAAE7D,GAAG;YAAEiD;UAAM,CAAC,CAAC,GAAGA;QACzD,GApBItC,MAAM,CAACG,KAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBR,CAAC;MAEhB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC,kCAAC;;AAEF;AAAAe,GAAA,GA5GMlE,iBAAiB;AA6GvB,MAAMmE,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAID;AACA,MAAMC,UAAU,gBAAAC,GAAA,cAAG7I,KAAK,CAAC0E,IAAI,CAAAoE,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEjB;AAAQ,CAAC,KAAK;EAAAa,GAAA;EACtE;EACA,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGlJ,QAAQ,CAAC;IAErC+I,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA/I,SAAS,CAAC,MAAM;IACdiJ,UAAU,CAAC;MACTH,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMG,WAAW,GAAInD,CAAC,IAAK;IACzB+B,OAAO,CAACe,KAAK,CAAC;EAChB,CAAC;EAED,oBACEjF,OAAA,CAACtD,MAAM;IAELwH,OAAO,EAAEoB,WAAY;IACrBZ,OAAO,EAAEU,OAAO,CAACD,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDhC,KAAK,EAAC,SAAS;IACfgB,IAAI,EAAC,OAAO;IACZtB,EAAE,EAAE;MACF0C,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpB3C,UAAU,EAAE,sBAAsB;MAClC4C,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAxD,QAAA,EAED6C,OAAO,CAACF,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAIG,OAAO,CAACD,UAAU,EAAE;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAACmC,GAAA,GA5CGlB,UAAU;AA8ChB,MAAMmB,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAE7E,KAAK,EAAE,IAAI;IAAE8E,UAAU,EAAE,IAAI;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACtE;IAAE/E,KAAK,EAAE,MAAM;IAAE8E,UAAU,EAAE,MAAM;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAE/E,KAAK,EAAE,YAAY;IAAE8E,UAAU,EAAE,YAAY;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACtF;IAAE/E,KAAK,EAAE,OAAO;IAAE8E,UAAU,EAAE,OAAO;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAE/E,KAAK,EAAE,IAAI;IAAE8E,UAAU,EAAE,IAAI;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACtE;IAAE/E,KAAK,EAAE,SAAS;IAAE8E,UAAU,EAAE,SAAS;IAAE7E,QAAQ,EAAE,KAAK;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACjF;IAAE/E,KAAK,EAAE,UAAU;IAAE8E,UAAU,EAAE,OAAO;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAE/E,KAAK,EAAE,YAAY;IAAE8E,UAAU,EAAE,QAAQ;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAClF;IAAE/E,KAAK,EAAE,QAAQ;IAAE8E,UAAU,EAAE,QAAQ;IAAE7E,QAAQ,EAAE,KAAK;IAAE8E,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1K,QAAQ,CAAC,MAAM;IACzD,MAAM2K,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGjC,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGjL,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACkL,eAAe,EAAEC,kBAAkB,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrL,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAMsL,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMC,eAAe,GAAGT,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACnF,MAAMW,iBAAiB,GAAGD,eAAe,CAACC,iBAAiB;MAE3D,IAAIA,iBAAiB,EAAE;QACrB;QACA,MAAMC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAAC,eAAe,CAAC;QACtD,IAAIA,KAAK,EAAE;UACT,MAAM,GAAGC,SAAS,EAAEC,IAAI,CAAC,GAAGF,KAAK;UACjC,MAAMG,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAClD,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YACpD,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAChD,CAAC;UACD,MAAMC,WAAW,GAAGD,QAAQ,CAACF,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC;UACrD,IAAID,WAAW,EAAE;YACfE,OAAO,CAACC,GAAG,CAAC,YAAYR,iBAAiB,OAAOG,IAAI,IAAIE,WAAW,EAAE,CAAC;YACtE,OAAO,GAAGF,IAAI,IAAIE,WAAW,EAAE;UACjC;QACF;MACF;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;;IAEA;IACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,OAAO,GAAGD,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9E,CAAC;EAED,MAAMC,aAAa,GAAGlB,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAG1M,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2M,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5M,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC6M,YAAY,EAAEC,eAAe,CAAC,GAAG9M,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM+M,aAAa,GAAG7M,WAAW,CAAE8M,MAAM,IAAK;IAC5C,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;MACvB;IACF;IAEA,MAAMC,WAAW,GAAGF,MAAM,CAACG,MAAM,CAACvI,KAAK;IACvC,MAAMwI,gBAAgB,GAAGJ,MAAM,CAACC,WAAW,CAACrI,KAAK;IAEjD,IAAIsI,WAAW,KAAKE,gBAAgB,EAAE;MACpC;IACF;IAEAC,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;;MAEzB;MACA,MAAME,YAAY,GAAGD,OAAO,CAACE,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC;MAC9D,MAAMC,QAAQ,GAAGJ,OAAO,CAAChI,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC;;MAExD;MACA,MAAM,CAACE,OAAO,CAAC,GAAGJ,YAAY,CAACK,MAAM,CAACX,WAAW,EAAE,CAAC,CAAC;MACrDM,YAAY,CAACK,MAAM,CAACT,gBAAgB,EAAE,CAAC,EAAEQ,OAAO,CAAC;;MAEjD;MACAJ,YAAY,CAACM,OAAO,CAAC,CAACnJ,GAAG,EAAEC,KAAK,KAAK;QACnC,IAAI,OAAOD,GAAG,CAAC+I,EAAE,KAAK,QAAQ,EAAE;UAC9B/I,GAAG,CAAC+I,EAAE,GAAG9I,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMmJ,aAAa,GAAGJ,QAAQ,GAAG,CAAC,GAAGH,YAAY,EAAEG,QAAQ,CAAC,GAAGH,YAAY;MAE3EzB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvB,OAAOgC,gBAAgB,CAACD,aAAa,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMC,cAAc,GAAG/N,WAAW,CAAC,CAAC4I,KAAK,EAAErD,KAAK,EAAEyI,QAAQ,KAAK;IAC7Db,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMa,WAAW,GAAGb,IAAI,CAAC3F,GAAG,CAAChD,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACmB,EAAE,KAAKgD,KAAK,EAAE;UACpB,MAAMsF,UAAU,GAAG;YAAE,GAAGzJ,GAAG;YAAE,CAACc,KAAK,GAAGyI;UAAS,CAAC;;UAEhD;UACA,MAAMG,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;UACrG,IAAIA,aAAa,CAACC,QAAQ,CAAC7I,KAAK,CAAC,IAAIyI,QAAQ,KAAKK,SAAS,IAAIL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;YACnG,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAChC,MAAMM,QAAQ,GAAGC,MAAM,CAACP,QAAQ,CAAC;cACjC,IAAI,CAACQ,KAAK,CAACF,QAAQ,CAAC,EAAE;gBACpBJ,UAAU,CAAC3I,KAAK,CAAC,GAAG+I,QAAQ;cAC9B;YACF;UACF;UAEA,OAAOJ,UAAU;QACnB;QACA,OAAOzJ,GAAG;MACZ,CAAC,CAAC;MAEF,MAAMqI,MAAM,GAAGgB,gBAAgB,CAACG,WAAW,CAAC;;MAE5C;MACAQ,2BAA2B,CAAC3B,MAAM,CAAC;MACnC4B,qBAAqB,CAAC5B,MAAM,CAAC;MAE7B,OAAOA,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgB,gBAAgB,EAAEW,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;;EAE1E;EACA3O,SAAS,CAAC,MAAM;IACd,MAAM4O,KAAK,GAAGvK,UAAU,CAAC,MAAM;MAC7BsI,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMpI,YAAY,CAACwK,KAAK,CAAC;EAClC,CAAC,EAAE,CAACpC,UAAU,CAAC,CAAC;;EAEhB;EACAxM,SAAS,CAAC,MAAM;IACd6O,kBAAkB,CAACxB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEyB,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAACpC,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjP,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkP,UAAU,EAAEC,aAAa,CAAC,GAAGnP,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACoP,eAAe,EAAEN,kBAAkB,CAAC,GAAG9O,QAAQ,CAAC,MAAM;IAC3D,MAAMqP,KAAK,GAAGzE,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLkE,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACApP,SAAS,CAAC,MAAM;IACd2K,YAAY,CAAC4E,OAAO,CAAC,kBAAkB,EAAEJ,eAAe,CAACE,QAAQ,CAACnJ,QAAQ,CAAC,CAAC,CAAC;IAC7E4F,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoD,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAI9B;EACA,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG1P,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd2K,YAAY,CAAC4E,OAAO,CAAC,gBAAgB,EAAE1E,IAAI,CAAC6E,SAAS,CAAClF,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmF,aAAa,GAAG,CAAC7F,IAAI,IAAI,EAAE,EAAEpC,GAAG,CAAChD,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEkL,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAElK,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACmK,QAAQ,EAAE1C,WAAW,CAAC,GAAGrN,QAAQ,CAAC,MAAM;IAC7C+L,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C7B,aAAa,GAAG,IAAIA,aAAa,CAAC6F,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI7F,aAAa,IAAIA,aAAa,CAAC6F,MAAM,GAAG,CAAC,EAAE;MAC7CjE,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMiE,aAAa,GAAG9F,aAAa,CAACxC,GAAG,CAAChD,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACiB,QAAQ,KAAK2I,SAAS,EAAE;UAC9B5J,GAAG,CAACiB,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIjB,GAAG,CAACmL,iBAAiB,KAAKvB,SAAS,EAAE;UACvC5J,GAAG,CAACmL,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOnL,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMuL,gBAAgB,GAAGN,aAAa,CAACjI,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;QAC1D,GAAGD,GAAG;QACNmB,EAAE,EAAElB,KAAK;QACTe,OAAO,EAAEhB,GAAG,CAAC+I,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHgC,eAAe,CAAC,CAAC,GAAGQ,gBAAgB,CAAC,CAAC;MAEtC,OAAOD,aAAa;IACtB;;IAEA;IACAlE,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMkE,gBAAgB,GAAGN,aAAa,CAACjI,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;MAC1D,GAAGD,GAAG;MACNmB,EAAE,EAAElB,KAAK;MACTe,OAAO,EAAEhB,GAAG,CAAC+I,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHgC,eAAe,CAAC,CAAC,GAAGQ,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGhQ,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMiQ,iBAAiB,GAAGjQ,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMkQ,gBAAgB,GAAGlQ,MAAM,CAAC,IAAI,CAAC;;EAIrC;EACA,MAAMmQ,UAAU,GAAIvG,IAAI,IAAKA,IAAI,CAACpC,GAAG,CAAChD,GAAG,KAAK;IAC5CmB,EAAE,EAAEnB,GAAG,CAACmB,EAAE;IACV4H,EAAE,EAAE/I,GAAG,CAAC+I,EAAE;IACV9H,QAAQ,EAAEjB,GAAG,CAACiB,QAAQ;IACtBiK,OAAO,EAAElL,GAAG,CAACkL,OAAO;IACpBC,iBAAiB,EAAEnL,GAAG,CAACmL,iBAAiB;IACxCS,UAAU,EAAE5L,GAAG,CAAC4L;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAtQ,SAAS,CAAC,MAAM;IACd,IAAImK,YAAY,IAAI2F,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMQ,OAAO,GAAG1F,IAAI,CAAC6E,SAAS,CAACW,UAAU,CAACP,QAAQ,CAAC,CAAC;MACpD,MAAMU,WAAW,GAAGN,mBAAmB,CAACO,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIH,gBAAgB,CAACK,OAAO,EAAE;UAC5BrM,YAAY,CAACgM,gBAAgB,CAACK,OAAO,CAAC;QACxC;QACAL,gBAAgB,CAACK,OAAO,GAAGpM,UAAU,CAAC,MAAM;UAC1C6L,mBAAmB,CAACO,OAAO,GAAGF,OAAO;UACrCJ,iBAAiB,CAACM,OAAO,GAAGvE,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC9B,YAAY,CAAC,CAAC,GAAG2F,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIM,gBAAgB,CAACK,OAAO,EAAE;QAC5BrM,YAAY,CAACgM,gBAAgB,CAACK,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACX,QAAQ,EAAE3F,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAG5Q,QAAQ,CAAC;IACjD6Q,IAAI,EAAE,KAAK;IACX/H,KAAK,EAAE,IAAI;IACXzD,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApF,SAAS,CAAC,MAAM;IACd,IAAIwP,YAAY,CAACO,MAAM,KAAK,CAAC,IAAID,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACpDjE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+D,QAAQ,CAACC,MAAM,CAAC;MACpDN,eAAe,CAAC,CAAC,GAAGK,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEN,YAAY,CAAC,CAAC;EAI5B,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI9G,MAAM,IAAIA,MAAM,CAAC+G,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C7G,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM8G,WAAW,GAAG,GAAG1N,OAAO,aAAa0G,MAAM,EAAE;MACnD,MAAMiH,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIlF,IAAI,CAAC,CAAC,CAACmF,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAOhF,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/B,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMyH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMtO,KAAK,CAACuO,MAAM,CAAC,GAAGtO,OAAO,YAAY0G,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAhC,OAAO,CAAC,CAAC;EACX,CAAC;;EAID;EACA,MAAM4H,kBAAkB,GAAG3R,WAAW,CAAC,CAAC6J,IAAI,EAAE+H,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGhI,IAAI,IAAIgG,QAAQ,IAAI,EAAE;IACxC,IAAI,CAACiC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbtE,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,CAAC,CAClDsM,MAAM,CAAC,CAACC,GAAG,EAAExN,GAAG,KAAK;MACpB,IAAImN,UAAU,IAAInN,GAAG,CAACmB,EAAE,KAAKgM,UAAU,CAAChM,EAAE,EAAE;QAC1C,OAAOqM,GAAG,IAAI1D,MAAM,CAACqD,UAAU,CAACvB,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO4B,GAAG,IAAI1D,MAAM,CAAC9J,GAAG,CAAC4L,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM/B,gBAAgB,GAAG9N,WAAW,CAAE6J,IAAI,IAAK;IAC7C,MAAM4D,QAAQ,GAAG5D,IAAI,CAACxE,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIC,QAAQ,EAAE;MACZ,MAAMyE,QAAQ,GAAGrI,IAAI,CAClB0D,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,CAAC,CAClDsM,MAAM,CAAC,CAACC,GAAG,EAAExN,GAAG,KAAKwN,GAAG,IAAI1D,MAAM,CAAC9J,GAAG,CAAC4L,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/D5C,QAAQ,CAAC4C,UAAU,GAAG6B,QAAQ;IAChC;IACA,OAAOrI,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4E,2BAA2B,GAAGzO,WAAW,CAC7C4D,QAAQ,CAAEiG,IAAI,IAAK;IACjB,IAAI;MACFa,YAAY,CAAC4E,OAAO,CAAC,eAAe,EAAE1E,IAAI,CAAC6E,SAAS,CAAC5F,IAAI,CAAC,CAAC;MAC3DgC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEjC,IAAI,CAACiG,MAAM,CAAC;IAClD,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAM2C,qBAAqB,GAAG1O,WAAW,CACvC4D,QAAQ,CAAEiG,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvBgC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAAC5B,YAAY,CACf,CAAC;;EAED;EACA,MAAMiI,gBAAgB,GAAGnS,WAAW,CAAEoS,MAAM,IAAK;IAC/CjF,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIiF,UAAU,GAAGV,kBAAkB,CAACvE,IAAI,EAAEgF,MAAM,CAAC;MACjD,MAAMnE,WAAW,GAAGb,IAAI,CAAC3F,GAAG,CAAChD,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACmB,EAAE,KAAKwM,MAAM,CAACxM,EAAE,EAAE;UACxB;UACA,MAAMsI,UAAU,GAAG;YAAE,GAAGzJ;UAAI,CAAC;UAC7B6N,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACxE,OAAO,CAAC7H,GAAG,IAAI;YACjC,IAAIqM,MAAM,CAACrM,GAAG,CAAC,KAAKsI,SAAS,EAAE;cAC7BH,UAAU,CAACnI,GAAG,CAAC,GAAGqM,MAAM,CAACrM,GAAG,CAAC;YAC/B;UACF,CAAC,CAAC;UACF,OAAOmI,UAAU;QACnB;QACA,IAAIzJ,GAAG,CAAC+I,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAG/I,GAAG;UAAE4L,UAAU,EAAEgC;QAAW,CAAC;QACjE,OAAO5N,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAgK,2BAA2B,CAACR,WAAW,CAAC;MACxCS,qBAAqB,CAACT,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOmE,MAAM;EACf,CAAC,EAAE,CAACT,kBAAkB,EAAElD,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAM8D,uBAAuB,GAAIzG,KAAK,IAAK;IACzCF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC0G,OAAO,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG7S,KAAK,CAACG,WAAW,CAAC,CAAC4I,KAAK,EAAEzD,YAAY,KAAK;IACnE;IACAuL,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV/H,KAAK;MACLzD;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwN,kBAAkB,GAAG3S,WAAW,CAAC,MAAM;IAC3C0Q,gBAAgB,CAACtD,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPuD,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACA5B,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2D,kBAAkB,GAAG5S,WAAW,CAAE6S,MAAM,IAAK;IACjD,MAAM;MAAEjK;IAAM,CAAC,GAAG6H,aAAa;IAC/B,IAAI7H,KAAK,KAAK,IAAI,EAAE;MAClB;MACA+J,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC5F,WAAW,CAAC6F,QAAQ,IAAI;UACtB,IAAI/E,WAAW,GAAG+E,QAAQ,CAACvL,GAAG,CAAChD,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACmB,EAAE,KAAKgD,KAAK,EAAE;cACpB,IAAIiK,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGpO,GAAG;kBAAEkL,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIqD,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAIpE,aAAa,EAAE;kBACjBoE,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAInE,UAAU,EAAE;kBACdkE,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;kBACvBmD,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG3O,GAAG;kBAAEkL,OAAO,EAAEsD,WAAW;kBAAErD,iBAAiB,EAAEqD;gBAAY,CAAC;cACzE;YACF;YACA,OAAOxO,GAAG;UACZ,CAAC,CAAC;UAEFwJ,WAAW,GAAGH,gBAAgB,CAACG,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACA7J,UAAU,CAAC,MAAM;UACfyH,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC2E,aAAa,EAAEkC,kBAAkB,EAAE7E,gBAAgB,EAAEgB,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAMqE,mBAAmB,GAAGrT,WAAW,CAAC,MAAM;IAC5CiL,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqI,oBAAoB,GAAGtT,WAAW,CAAC,MAAM;IAC7CiL,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwI,YAAY,GAAGvT,WAAW,CAAC,MAAM;IACrC,IAAI8K,SAAS,CAAC0I,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACjJ,cAAc,CAAC6D,QAAQ,CAACtD,SAAS,CAAC0I,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEhJ,iBAAiB,CAAC4C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEtC,SAAS,CAAC0I,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD3H,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrBwH,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI/I,cAAc,CAAC6D,QAAQ,CAACtD,SAAS,CAAC0I,IAAI,CAAC,CAAC,CAAC,EAAE;MACpD3H,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAAChB,SAAS,EAAEP,cAAc,EAAE+I,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMG,YAAY,GAAGzT,WAAW,CAAE6S,MAAM,IAAK;IAC3CrI,iBAAiB,CAAC4C,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACmG,IAAI,IAAIA,IAAI,KAAKb,MAAM,CAAC,CAAC;IAC/DhH,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6H,eAAe,GAAG3T,WAAW,CAAE4F,EAAE,IAAK;IAC1CuH,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIa,WAAW,GAAGb,IAAI,CAAC3F,GAAG,CAAChD,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAK,CAAC,GAAGjB,GAAG,CAAC;;MAEnF;MACA,IAAImP,SAAS,GAAG,CAAC;MACjB3F,WAAW,CAACL,OAAO,CAACnJ,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+I,EAAE,KAAK,QAAQ,EAAE;UACrE/I,GAAG,CAAC+I,EAAE,GAAGoG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF3F,WAAW,GAAGH,gBAAgB,CAACG,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFpC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACgC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM+F,aAAa,GAAG7T,WAAW,CAAE4F,EAAE,IAAK;IACxCuH,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIa,WAAW,GAAGb,IAAI,CAAC3F,GAAG,CAAChD,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAM,CAAC,GAAGjB,GAAG,CAAC;;MAEpF;MACA,IAAImP,SAAS,GAAG,CAAC;MACjB3F,WAAW,CAACL,OAAO,CAACnJ,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+I,EAAE,KAAK,QAAQ,EAAE;UACrE/I,GAAG,CAAC+I,EAAE,GAAGoG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF3F,WAAW,GAAGH,gBAAgB,CAACG,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF7J,UAAU,CAAC,MAAM;MACfyH,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACgC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMgG,eAAe,GAAG9T,WAAW,CAAE4F,EAAE,IAAK;IAC1CuH,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAM2G,YAAY,GAAG3G,IAAI,CAACG,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAIgO,SAAS,GAAG,CAAC;MACjBG,YAAY,CAACnG,OAAO,CAACnJ,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+I,EAAE,KAAK,QAAQ,EAAE;UACrE/I,GAAG,CAAC+I,EAAE,GAAGoG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM3F,WAAW,GAAGH,gBAAgB,CAACiG,YAAY,CAAC;;MAElD;MACAtF,2BAA2B,CAACR,WAAW,CAAC;MACxCS,qBAAqB,CAACT,WAAW,CAAC;MAElCpC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrB,OAAOmC,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,gBAAgB,EAAEW,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMsF,YAAY,GAAGhU,WAAW,CAAEiU,UAAU,IAAK;IAC/C9G,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAM8G,WAAW,GAAG9G,IAAI,CAAC+G,SAAS,CAAC1P,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKqO,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO9G,IAAI;;MAEnC;MACA,MAAMgH,KAAK,GAAGnI,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAMqI,UAAU,GAAGjH,IAAI,CAAC8G,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAAC7G,EAAE,KAAK,QAAQ,GAAG6G,UAAU,CAAC7G,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM4E,MAAM,GAAG;QACbxM,EAAE,EAAEwO,KAAK;QACT5G,EAAE,EAAE8G,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACN7E,OAAO,EAAE,EAAE;QACX8E,QAAQ,EAAE,EAAE;QACZpE,UAAU,EAAE,CAAC;QACbT,iBAAiB,EAAE,EAAE;QACrBlK,QAAQ,EAAE,KAAK;QACfD,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAM4H,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;MACzBC,OAAO,CAACM,MAAM,CAACuG,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE9B,MAAM,CAAC;;MAE1C;MACA,IAAIwB,SAAS,GAAG,CAAC;MACjBvG,OAAO,CAACO,OAAO,CAACnJ,GAAG,IAAI;QACrB,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+I,EAAE,KAAK,QAAQ,EAAE;UACrE/I,GAAG,CAAC+I,EAAE,GAAGoG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM3F,WAAW,GAAGH,gBAAgB,CAACT,OAAO,CAAC;;MAE7C;MACAoB,2BAA2B,CAACR,WAAW,CAAC;MACxCS,qBAAqB,CAACT,WAAW,CAAC;MAElCpC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;MACpB,OAAOmC,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,gBAAgB,EAAEW,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMgG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFvJ,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMwJ,YAAY,GAAG,CAAC9E,QAAQ,IAAI,EAAE,EACjCtC,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,CAAC;;MAErD;MACAiP,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACrH,EAAE,KAAK,QAAQ,IAAI,OAAOsH,CAAC,CAACtH,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOqH,CAAC,CAACrH,EAAE,GAAGsH,CAAC,CAACtH,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMuH,OAAO,GAAGJ,YAAY,CAAClN,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;QAChD;QACA8I,EAAE,EAAE9I,KAAK,GAAG,CAAC;QACb6P,IAAI,EAAE9P,GAAG,CAAC8P,IAAI,GAAI,OAAO9P,GAAG,CAAC8P,IAAI,KAAK,QAAQ,IAAI9P,GAAG,CAAC8P,IAAI,CAACnG,QAAQ,CAAC,GAAG,CAAC,GAAG3J,GAAG,CAAC8P,IAAI,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGvQ,GAAG,CAAC8P,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE9P,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGwQ,IAAI,CAACC,KAAK,CAACzQ,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF+P,EAAE,EAAE,OAAO/P,GAAG,CAAC+P,EAAE,KAAK,QAAQ,GAAGS,IAAI,CAACC,KAAK,CAACzQ,GAAG,CAAC+P,EAAE,CAAC,GAAG/P,GAAG,CAAC+P,EAAE,IAAI,EAAE;QAClE7E,OAAO,EAAGlL,GAAG,CAACmL,iBAAiB,IAAInL,GAAG,CAACmL,iBAAiB,KAAK,MAAM,GAAInL,GAAG,CAACmL,iBAAiB,GAAG,EAAE;QACjGuF,KAAK,EAAE,OAAO1Q,GAAG,CAACgQ,QAAQ,KAAK,QAAQ,GACpChQ,GAAG,CAACgQ,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGhQ,GAAG,CAACgQ,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,GAAG3Q,GAAG,CAACgQ,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,GAC3E3Q,GAAG,CAACgQ,QAAQ,IAAI,EAAE;QACpBY,MAAM,EAAE,OAAO5Q,GAAG,CAAC4L,UAAU,KAAK,QAAQ,GAAG5L,GAAG,CAAC4L,UAAU,CAAC+E,OAAO,CAAC,CAAC,CAAC,GAAG3Q,GAAG,CAAC4L,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMiF,WAAW,GAAG,CAACzF,QAAQ,IAAI,EAAE,EAChCtC,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAI,CAAC/I,GAAG,CAACiB,QAAQ,IAAIjB,GAAG,CAAC4L,UAAU,CAAC,CACpE2B,MAAM,CAAC,CAACC,GAAG,EAAExN,GAAG,KAAKwN,GAAG,IAAI,OAAOxN,GAAG,CAAC4L,UAAU,KAAK,QAAQ,GAAG5L,GAAG,CAAC4L,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMkF,YAAY,GAAIzL,MAAM,IAAIA,MAAM,CAAC+G,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG/G,MAAM;MAE5F,MAAM0L,QAAQ,GAAG,MAAMrS,KAAK,CAACsS,IAAI,CAAC,GAAGrS,OAAO,oBAAoB,EAAE;QAChEyG,IAAI,EAAEkL,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCtL,MAAM,EAAEyL,YAAY;QACpBjJ,aAAa,EAAEA;MACjB,CAAC,CAAC;MAEF,IAAIkJ,QAAQ,CAAC3L,IAAI,IAAI2L,QAAQ,CAAC3L,IAAI,CAAC6L,KAAK,EAAE;QACxC;QACA,MAAM5E,WAAW,GAAG,GAAG1N,OAAO,CAAC4R,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAAC3L,IAAI,CAAC8L,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA9J,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACA1H,UAAU,CAAC,MAAM;UACf,MAAMwR,MAAM,GAAG5E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C2E,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGjF,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACsE,MAAM,CAAC;UACjCxR,UAAU,CAAC,MAAM;YACf4M,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACoE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOjK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRZ,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM8K,kBAAkB,GAAGjW,WAAW,CAAC,CAAC4I,KAAK,EAAElB,KAAK,KAAK;IACvDgL,iBAAiB,CAAC9J,KAAK,EAAElB,KAAK,CAAC;EACjC,CAAC,EAAE,CAACgL,iBAAiB,CAAC,CAAC;EAEvB,MAAM/N,OAAO,GAAGzE,OAAO,CAAC,MAAOkK,WAAW,CAAC3C,GAAG,CAACnC,GAAG,IAAI;IACpD,IAAI,EAAEuK,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACqG,cAAc,CAAC5Q,GAAG,CAACC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8E,UAAU,EAAE/E,GAAG,CAAC+E,UAAU;QAC1B8L,IAAI,EAAE,GAAG;QACTjP,KAAK,EAAE,GAAG;QACV1B,QAAQ,EAAE,KAAK;QACf8C,UAAU,EAAG8N,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC3R,GAAG,CAAC+I,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI4I,MAAM,CAAC3R,GAAG,CAACiB,QAAQ,EAAE;YACvB,MAAM2Q,iBAAiB,GAAGD,MAAM,CAAC3R,GAAG,CAACmL,iBAAiB,IAAI,KAAK;YAC/D,oBACEjM,OAAA,CAACzC,OAAO;cAACoV,KAAK,EAAEF,MAAM,CAAC3R,GAAG,CAACmL,iBAAiB,IAAI,EAAG;cAAC2G,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAtQ,QAAA,eACvEvC,OAAA,CAAC1C,IAAI;gBACHwV,KAAK,EAAEJ,iBAAkB;gBACzBvP,KAAK,EAAC,SAAS;gBACfuB,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACZtB,EAAE,EAAE;kBAAE2C,QAAQ,EAAE,MAAM;kBAAEuN,OAAO,EAAE,GAAG;kBAAE7P,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEsM,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAzO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAImP,UAAU,GAAG,MAAM;UACvB,IAAI7N,UAAU,GAAG,KAAK;UAEtB,IAAIsN,MAAM,CAAC3R,GAAG,CAACmL,iBAAiB,IAAIwG,MAAM,CAAC3R,GAAG,CAACmL,iBAAiB,KAAK,MAAM,EAAE;YAC3E+G,UAAU,GAAGP,MAAM,CAAC3R,GAAG,CAACmL,iBAAiB;YACzC9G,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEnF,OAAA,CAAC8E,UAAU;YACTG,KAAK,EAAEwN,MAAM,CAAC3R,GAAG,CAACmB,EAAG;YACrBiD,IAAI,EAAE8N,UAAW;YACjB7N,UAAU,EAAEA,UAAW;YACvBjB,OAAO,EAAEoO;UAAmB;YAAA5O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIlC,GAAG,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8E,UAAU,EAAE/E,GAAG,CAAC+E,UAAU;QAC1B8L,IAAI,EAAE,GAAG;QACTjP,KAAK,EAAE,GAAG;QACV1B,QAAQ,EAAE,KAAK;QACf8C,UAAU,EAAG8N,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC3R,GAAG,CAAC+I,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACE7J,OAAA,CAACxD,GAAG;YAACqG,EAAE,EAAE;cAAEsP,OAAO,EAAE,MAAM;cAAEc,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAA3Q,QAAA,gBAE3DvC,OAAA,CAACzC,OAAO;cAACoV,KAAK,EAAC,wDAAW;cAAApQ,QAAA,eACxBvC,OAAA,CAAC5C,UAAU;gBACT+G,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,SAAS;gBACfe,OAAO,EAAEA,CAAA,KAAMmM,YAAY,CAACoC,MAAM,CAAC3R,GAAG,CAACmB,EAAE,CAAE;gBAC3CY,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,cAAc;oBAC/BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEFvC,OAAA,CAACX,oBAAoB;kBAACoG,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGV7D,OAAA,CAACzC,OAAO;cAACoV,KAAK,EAAC,0EAAc;cAAApQ,QAAA,eAC3BvC,OAAA,CAAC5C,UAAU;gBACT+G,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,OAAO;gBACbe,OAAO,EAAEA,CAAA,KAAMiM,eAAe,CAACsC,MAAM,CAAC3R,GAAG,CAACmB,EAAE,CAAE;gBAC9CY,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,YAAY;oBAC7BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEFvC,OAAA,CAACV,uBAAuB;kBAACmG,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGT4O,MAAM,CAAC3R,GAAG,CAACiB,QAAQ,gBAClB/B,OAAA,CAACtD,MAAM;cAELgI,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,SAAS;cACfgB,IAAI,EAAC,OAAO;cACZgP,SAAS,eAAEnT,OAAA,CAACjB,QAAQ;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBK,OAAO,EAAEA,CAAA,KAAMgM,aAAa,CAACuC,MAAM,CAAC3R,GAAG,CAACmB,EAAE,CAAE;cAC5CY,EAAE,EAAE;gBACF4C,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACH;YAED,GAbM,MAAM;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAET7D,OAAA,CAACtD,MAAM;cAELgI,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,OAAO;cACbgB,IAAI,EAAC,OAAO;cACZgP,SAAS,eAAEnT,OAAA,CAAClB,UAAU;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BK,OAAO,EAAEA,CAAA,KAAM8L,eAAe,CAACyC,MAAM,CAAC3R,GAAG,CAACmB,EAAE,CAAE;cAC9CY,EAAE,EAAE;gBACF4C,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACH;YAED,GAbM,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGlC,GAAG;MACNE,QAAQ,EAAE4Q,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC3R,GAAG,IAAI2R,MAAM,CAAC3R,GAAG,CAAC+I,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI4I,MAAM,CAAC3R,GAAG,IAAI2R,MAAM,CAAC3R,GAAG,CAACiB,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOJ,GAAG,CAACE,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD8C,UAAU,EAAG8N,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC3R,GAAG,CAAC+I,EAAE,KAAK,OAAO,IAAIlI,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE5B,OAAA,CAACvD,UAAU;YAACiI,OAAO,EAAC,OAAO;YAAC0O,UAAU,EAAC,MAAM;YAACjQ,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAC1D,OAAOkQ,MAAM,CAAC1O,KAAK,KAAK,QAAQ,GAAG0O,MAAM,CAAC1O,KAAK,CAAC0N,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOgB,MAAM,CAAC1O,KAAK,KAAK,QAAQ,IAAI,CAAC8G,KAAK,CAACD,MAAM,CAAC6H,MAAM,CAAC1O,KAAK,CAAC,CAAC,GAAG6G,MAAM,CAAC6H,MAAM,CAAC1O,KAAK,CAAC,CAAC0N,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAAC1O;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI4O,MAAM,CAAC3R,GAAG,CAACiB,QAAQ,EAAE;UACvB,oBACE/B,OAAA,CAACvD,UAAU;YAACiI,OAAO,EAAC,OAAO;YAACvB,KAAK,EAAC,eAAe;YAACN,EAAE,EAAE;cAAEO,cAAc,EAAE;YAAe,CAAE;YAAAb,QAAA,EACtFkQ,MAAM,CAAC1O;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIlC,GAAG,CAACC,KAAK,KAAK,MAAM,IAAI6Q,MAAM,CAAC1O,KAAK,EAAE;UACxC,OAAO0O,MAAM,CAAC1O,KAAK,CAACsN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAI1P,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOuN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAAC1O,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,OAAO,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOuN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAAC1O,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOuN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAAC1O,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,UAAU,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO0O,MAAM,CAAC1O,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG0O,MAAM,CAAC1O,KAAK,CAAC0N,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAAC1O,KAAK,CAAC0N,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAI9P,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO0O,MAAM,CAAC1O,KAAK,CAAC0N,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAI9P,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAO6Q,MAAM,CAAC1O,KAAK,KAAK,QAAQ,IAAI,CAAC8G,KAAK,CAACD,MAAM,CAAC6H,MAAM,CAAC1O,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO6G,MAAM,CAAC6H,MAAM,CAAC1O,KAAK,CAAC,CAAC0N,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOgB,MAAM,CAAC1O,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO0O,MAAM,CAAC1O,KAAK;QACrB;QACA,OAAO0O,MAAM,CAAC1O,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC6F,MAAM,CAACyJ,OAAO,CAAE,EAAE,CAAC5M,WAAW,EAAE6L,kBAAkB,EAAEtC,eAAe,EAAEE,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAMmD,gBAAgB,GAAG/W,OAAO,CAAC,MAAM;IACrC,IAAI,CAACuM,mBAAmB,CAAC+G,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAO3D,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAMqH,WAAW,GAAGzK,mBAAmB,CAAC0K,WAAW,CAAC,CAAC;IACrD,OAAO,CAACtH,QAAQ,IAAI,EAAE,EAAEtC,MAAM,CAAC9I,GAAG,IAAI;MACpC,IAAIkI,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAO2F,MAAM,CAAC8E,MAAM,CAAC3S,GAAG,CAAC,CAAC4S,IAAI,CAAC3P,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACzB,QAAQ,CAAC,CAAC,CAACkR,WAAW,CAAC,CAAC,CAAC/I,QAAQ,CAAC8I,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMI,SAAS,GAAG7S,GAAG,CAACkI,YAAY,CAAC;QACnC,OAAO2K,SAAS,IAAIA,SAAS,CAACrR,QAAQ,CAAC,CAAC,CAACkR,WAAW,CAAC,CAAC,CAAC/I,QAAQ,CAAC8I,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrH,QAAQ,EAAEpD,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAM4K,YAAY,GAAGrX,OAAO,CAAC,MAAM+W,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACAlX,SAAS,CAAC,MAAM;IACd8L,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBsD,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1B2I,UAAU,EAAED,YAAY,CAACzH;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAE0I,YAAY,CAACzH,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEnM,OAAA,CAACxD,GAAG;MAACqG,EAAE,EAAE;QAAEY,SAAS,EAAE,QAAQ;QAAEqQ,EAAE,EAAE;MAAE,CAAE;MAAAvR,QAAA,gBACtCvC,OAAA,CAACvD,UAAU;QAACiI,OAAO,EAAC,IAAI;QAACvB,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAAC;MAEhD;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACtD,MAAM;QACLgI,OAAO,EAAC,WAAW;QACnBR,OAAO,EAAEkC,OAAQ;QACjBvD,EAAE,EAAE;UAAEkR,EAAE,EAAE;QAAE,CAAE;QAAAxR,QAAA,EACf;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE7D,OAAA,CAACxD,GAAG;IAAA+F,QAAA,gBAEFvC,OAAA,CAACxC,IAAI;MAACqF,EAAE,EAAE;QAAEmR,EAAE,EAAE;MAAE,CAAE;MAAAzR,QAAA,eAClBvC,OAAA,CAACvC,WAAW;QAAA8E,QAAA,eACVvC,OAAA,CAACxD,GAAG;UAACqG,EAAE,EAAE;YAAEsP,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE,QAAQ;YAAEe,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEjB,GAAG,EAAE;UAAE,CAAE;UAAA1Q,QAAA,gBAC5GvC,OAAA,CAACxD,GAAG;YAACqG,EAAE,EAAE;cAAEsP,OAAO,EAAE,MAAM;cAAEe,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAA1Q,QAAA,gBACzDvC,OAAA,CAAChB,cAAc;cAAC6D,EAAE,EAAE;gBAAEM,KAAK,EAAE,cAAc;gBAAEsC,QAAQ,EAAE;cAAG;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D7D,OAAA,CAACxD,GAAG;cAAA+F,QAAA,gBACFvC,OAAA,CAACvD,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAC7B,EAAE,EAAE;kBAAEuQ,UAAU,EAAE,GAAG;kBAAEjQ,KAAK,EAAE,cAAc;kBAAE6Q,EAAE,EAAE;gBAAI,CAAE;gBAAAzR,QAAA,EAAC;cAElF;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7D,OAAA,CAACvD,UAAU;gBAACiI,OAAO,EAAC,OAAO;gBAAC7B,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAiB,CAAE;gBAAAZ,QAAA,EAAC;cAE7D;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA,CAACtC,KAAK;YAACyW,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACvR,EAAE,EAAE;cAAEqR,QAAQ,EAAE,MAAM;cAAEjB,GAAG,EAAE;YAAE,CAAE;YAAA1Q,QAAA,gBAClEvC,OAAA,CAAC1C,IAAI;cACH+W,IAAI,eAAErU,OAAA,CAACf,aAAa;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBiP,KAAK,EAAE,GAAG,CAACc,YAAY,IAAI,EAAE,EAAEhK,MAAM,CAAC9I,GAAG,IAAI,CAACA,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,CAAC,CAACoK,MAAM,MAAO;cACzFhJ,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACF7D,OAAA,CAAC1C,IAAI;cACH+W,IAAI,eAAErU,OAAA,CAACd,cAAc;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBiP,KAAK,EAAE,WAAW9E,kBAAkB,CAAC4F,YAAY,CAAC,CAACnC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEtO,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAAC+P,YAAY,IAAI,EAAE,EAAEhK,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAACoK,MAAM,GAAG,CAAC,iBAC1DnM,OAAA,CAAC1C,IAAI;cACH+W,IAAI,eAAErU,OAAA,CAAClB,UAAU;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBiP,KAAK,EAAE,GAAG,CAACc,YAAY,IAAI,EAAE,EAAEhK,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAACoK,MAAM,OAAQ;cACzEhJ,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACxC,IAAI;MAACqF,EAAE,EAAE;QAAEmR,EAAE,EAAE;MAAE,CAAE;MAAAzR,QAAA,eAClBvC,OAAA,CAACvC,WAAW;QAAA8E,QAAA,eACVvC,OAAA,CAAC9B,IAAI;UAACoW,SAAS;UAACF,OAAO,EAAE,CAAE;UAAA7R,QAAA,gBAEzBvC,OAAA,CAAC9B,IAAI;YAAC6R,IAAI;YAACwE,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjS,QAAA,gBACvBvC,OAAA,CAACvD,UAAU;cAACiI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEuQ,UAAU,EAAE,GAAG;gBAAEY,EAAE,EAAE,CAAC;gBAAE7B,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAA1Q,QAAA,gBAC5GvC,OAAA,CAACb,UAAU;gBAAC0D,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7D,OAAA,CAACtC,KAAK;cAACyW,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAClB,UAAU,EAAC,QAAQ;cAAA3Q,QAAA,gBAC5EvC,OAAA,CAACrC,WAAW;gBAACwG,IAAI,EAAC,OAAO;gBAACtB,EAAE,EAAE;kBAAE0C,QAAQ,EAAE;gBAAI,CAAE;gBAAAhD,QAAA,gBAC9CvC,OAAA,CAACpC,UAAU;kBAAA2E,QAAA,EAAC;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B7D,OAAA,CAACnC,MAAM;kBACLkG,KAAK,EAAEiF,YAAa;kBACpB8J,KAAK,EAAC,0BAAM;kBACZ1O,QAAQ,EAAGjC,CAAC,IAAK8G,eAAe,CAAC9G,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;kBAAAxB,QAAA,gBAEjDvC,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,KAAK;oBAAAxB,QAAA,EAAC;kBAAG;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpC7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClC7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,MAAM;oBAAAxB,QAAA,EAAC;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtC7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAU;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClD7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,OAAO;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxC7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClC7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5C7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,UAAU;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC3C7D,OAAA,CAAClC,QAAQ;oBAACiG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAM;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEd7D,OAAA,CAAC7C,SAAS;gBACRgH,IAAI,EAAC,OAAO;gBACZuQ,WAAW,EAAC,yCAAW;gBACvB3Q,KAAK,EAAE6E,UAAW;gBAClBxE,QAAQ,EAAGjC,CAAC,IAAK0G,aAAa,CAAC1G,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;gBAC/ClB,EAAE,EAAE;kBAAE8R,QAAQ,EAAE,CAAC;kBAAEpP,QAAQ,EAAE;gBAAI,CAAE;gBACnCqP,UAAU,EAAE;kBACVC,cAAc,eACZ7U,OAAA,CAACjC,cAAc;oBAAC+W,QAAQ,EAAC,OAAO;oBAAAvS,QAAA,eAC9BvC,OAAA,CAACb,UAAU;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBACDkR,YAAY,EAAEnM,UAAU,iBACtB5I,OAAA,CAACjC,cAAc;oBAAC+W,QAAQ,EAAC,KAAK;oBAAAvS,QAAA,eAC5BvC,OAAA,CAAC5C,UAAU;sBACT+G,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM2E,aAAa,CAAC,EAAE,CAAE;sBACjCmM,IAAI,EAAC,KAAK;sBAAAzS,QAAA,eAEVvC,OAAA,CAACZ,SAAS;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAED+E,UAAU,iBACT5I,OAAA,CAACvD,UAAU;gBAACiI,OAAO,EAAC,OAAO;gBAACvB,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,GAAC,eAC9C,EAAC+Q,gBAAgB,CAAC1J,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC,CAACsC,MAAM,EAAC,qBAChE;cAAA;gBAAAzI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGP7D,OAAA,CAAC9B,IAAI;YAAC6R,IAAI;YAACwE,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjS,QAAA,gBACvBvC,OAAA,CAACvD,UAAU;cAACiI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEuQ,UAAU,EAAE,GAAG;gBAAEY,EAAE,EAAE,CAAC;gBAAE7B,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAA1Q,QAAA,gBAC5GvC,OAAA,CAACT,YAAY;gBAACsD,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7D,OAAA,CAACtC,KAAK;cAACyW,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAA7R,QAAA,gBACxDvC,OAAA,CAACtD,MAAM;gBACLgI,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACfgQ,SAAS,eAAEnT,OAAA,CAACrB,YAAY;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BK,OAAO,EAAE+I,cAAe;gBAAA1K,QAAA,EACzB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET7D,OAAA,CAACtD,MAAM;gBACLgI,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACfgQ,SAAS,EAAE5L,oBAAoB,gBAAGvH,OAAA,CAAC3C,gBAAgB;kBAAC8G,IAAI,EAAE,EAAG;kBAAChB,KAAK,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACf,aAAa;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrGK,OAAO,EAAE6M,gBAAiB;gBAC1BkE,QAAQ,EAAE1N,oBAAqB;gBAAAhF,QAAA,EAE9BgF,oBAAoB,GAAG,QAAQ,GAAG;cAAW;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAET7D,OAAA,CAACtD,MAAM;gBACLgI,OAAO,EAAC,UAAU;gBAClBvB,KAAK,EAAC,OAAO;gBACbgQ,SAAS,eAAEnT,OAAA,CAACpB,cAAc;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BK,OAAO,EAAE4J,aAAc;gBAAAvL,QAAA,EACxB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACL,eAAe;MAACuV,SAAS,EAAEhM,aAAc;MAAA3G,QAAA,eACxCvC,OAAA,CAACrD,KAAK;QAACkG,EAAE,EAAE;UAAEU,KAAK,EAAE,MAAM;UAAEoC,QAAQ,EAAE;QAAS,CAAE;QAAApD,QAAA,gBAC/CvC,OAAA,CAAC1B,cAAc;UAAAiE,QAAA,eACbvC,OAAA,CAAC7B,KAAK;YAACgX,YAAY;YAAA5S,QAAA,gBACjBvC,OAAA,CAACzB,SAAS;cAAAgE,QAAA,eACRvC,OAAA,CAACxB,QAAQ;gBAAA+D,QAAA,gBAEPvC,OAAA,CAAC3B,SAAS;kBAACwE,EAAE,EAAE;oBAAEU,KAAK,EAAE,EAAE;oBAAE6P,UAAU,EAAE;kBAAO,CAAE;kBAAA7Q,QAAA,EAAC;gBAElD;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EACX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,iBAClBzB,OAAA,CAAC3B,SAAS;kBAERwE,EAAE,EAAE;oBACFuQ,UAAU,EAAE,MAAM;oBAClBtQ,eAAe,EAAE,oBAAoB;oBACrCsS,WAAW,EAAE,WAAW;oBACxBC,WAAW,EAAE,SAAS;oBACtB,cAAc,EAAE;sBACdD,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAA7S,QAAA,EAEDd,MAAM,CAACiF;gBAAU,GAXbjF,MAAM,CAACG,KAAK;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYR,CACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEZ7D,OAAA,CAACJ,SAAS;cAAC0V,WAAW,EAAC,YAAY;cAAA/S,QAAA,EAChCA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBzC,OAAA,CAAC5B,SAAS;gBACRsE,GAAG,EAAEF,QAAQ,CAACG,QAAS;gBAAA,GACnBH,QAAQ,CAAC+S,cAAc;gBAC3B1S,EAAE,EAAE;kBACFC,eAAe,EAAEL,QAAQ,CAAC+S,cAAc,GAAG,cAAc,GAAG,SAAS;kBACrEtS,UAAU,EAAE;gBACd,CAAE;gBAAAX,QAAA,GAGDqR,YAAY,CACVhK,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC,CAAC;gBAAA,CAClC4L,KAAK,CACJlK,eAAe,CAACL,IAAI,GAAGK,eAAe,CAACE,QAAQ,EAC/C,CAACF,eAAe,CAACL,IAAI,GAAG,CAAC,IAAIK,eAAe,CAACE,QAC/C,CAAC,CACA3H,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,kBACdf,OAAA,CAACU,iBAAiB;kBAEhBI,GAAG,EAAEA,GAAI;kBACTC,KAAK,EAAEA,KAAM;kBACbC,OAAO,EAAEA;gBAAQ,GAHZF,GAAG,CAACmB,EAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIZ,CACF,CAAC,EAGH+P,YAAY,CAAClS,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC,iBAC3C7J,OAAA,CAACxB,QAAQ;kBAACkX,SAAS,EAAC,WAAW;kBAAC7S,EAAE,EAAE;oBAClCC,eAAe,EAAE,0BAA0B;oBAC3CsQ,UAAU,EAAE,MAAM;oBAClB,sBAAsB,EAAE;sBACtBA,UAAU,EAAE,MAAM;sBAClBuC,SAAS,EAAE,WAAW;sBACtBN,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAA9S,QAAA,gBACAvC,OAAA,CAAC3B,SAAS;oBAACwE,EAAE,EAAE;sBAAEU,KAAK,EAAE;oBAAG;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElB,CAAC,EACX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,IAAK;oBACvB,MAAMqI,QAAQ,GAAG8J,YAAY,CAAClS,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC;oBAC7D,MAAM9F,KAAK,GAAG+F,QAAQ,GAAGA,QAAQ,CAACrI,MAAM,CAACG,KAAK,CAAC,GAAG,EAAE;oBACpD,oBACE5B,OAAA,CAAC3B,SAAS;sBAAoBwE,EAAE,EAAE;wBAAEoB,OAAO,EAAE;sBAAM,CAAE;sBAAA1B,QAAA,EAClDd,MAAM,CAACkD,UAAU,GAAGlD,MAAM,CAACkD,UAAU,CAAC;wBAAE7D,GAAG,EAAEgJ,QAAQ;wBAAE/F;sBAAM,CAAC,CAAC,GAAGA;oBAAK,GAD1DtC,MAAM,CAACG,KAAK;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEjB,CAAC;kBAEhB,CAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CACX,EAEArB,QAAQ,CAACkS,WAAW;cAAA;gBAAAhR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACZ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjB7D,OAAA,CAACvB,eAAe;UACdmX,SAAS,EAAC,KAAK;UACfC,KAAK,EAAEjC,YAAY,CAAChK,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC+I,EAAE,KAAK,OAAO,CAAC,CAACsC,MAAO;UAC7DjB,IAAI,EAAEK,eAAe,CAACL,IAAK;UAC3B4K,YAAY,EAAEA,CAACC,KAAK,EAAEC,OAAO,KAAK;YAChC/K,kBAAkB,CAACxB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEyB,IAAI,EAAE8K;YAAQ,CAAC,CAAC,CAAC;UAC1D,CAAE;UACFC,WAAW,EAAE1K,eAAe,CAACE,QAAS;UACtCyK,mBAAmB,EAAGH,KAAK,IAAK;YAC9B,MAAMI,WAAW,GAAGzK,QAAQ,CAACqK,KAAK,CAAC1R,MAAM,CAACN,KAAK,EAAE,EAAE,CAAC;YACpDkH,kBAAkB,CAAC;cAAEC,IAAI,EAAE,CAAC;cAAEO,QAAQ,EAAE0K;YAAY,CAAC,CAAC;YACtDpP,YAAY,CAAC4E,OAAO,CAAC,kBAAkB,EAAEwK,WAAW,CAAC7T,QAAQ,CAAC,CAAC,CAAC;UAClE,CAAE;UACF8T,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAClCC,gBAAgB,EAAC,2BAAO;UACxBC,kBAAkB,EAAEA,CAAC;YAAEC,IAAI;YAAEC,EAAE;YAAEX;UAAM,CAAC,KAAK,GAAGU,IAAI,IAAIC,EAAE,MAAMX,KAAK;QAAK;UAAAnS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGlB7D,OAAA,CAACpD,MAAM;MACLoQ,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzByJ,OAAO,EAAEzH,kBAAmB;MAC5BvK,SAAS;MACTe,QAAQ,EAAC,IAAI;MACbkR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAtU,QAAA,gBAEnBvC,OAAA,CAACnD,WAAW;QAAA0F,QAAA,eACVvC,OAAA,CAACxD,GAAG;UAACqG,EAAE,EAAE;YAAEsP,OAAO,EAAE,MAAM;YAAE8B,cAAc,EAAE,eAAe;YAAEf,UAAU,EAAE;UAAS,CAAE;UAAA3Q,QAAA,gBAClFvC,OAAA,CAACvD,UAAU;YAACiI,OAAO,EAAC,IAAI;YAAAnC,QAAA,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C7D,OAAA,CAACtD,MAAM;YACLyW,SAAS,eAAEnT,OAAA,CAACnB,OAAO;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBK,OAAO,EAAEwL,mBAAoB;YAC7BvM,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAChB;UAED;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGd7D,OAAA,CAACxD,GAAG;QAACqG,EAAE,EAAE;UAAEiU,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxU,QAAA,gBACxBvC,OAAA,CAACvD,UAAU;UAACiI,OAAO,EAAC,OAAO;UAACvB,KAAK,EAAC,gBAAgB;UAACN,EAAE,EAAE;YAAEmR,EAAE,EAAE;UAAE,CAAE;UAAAzR,QAAA,EAAC;QAElE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7D,OAAA,CAACtC,KAAK;UAACyW,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAA7R,QAAA,gBAChCvC,OAAA,CAAC/B,gBAAgB;YACf+Y,OAAO,eACLhX,OAAA,CAAChC,QAAQ;cACPiZ,OAAO,EAAE9L,aAAc;cACvB/G,QAAQ,EAAGjC,CAAC,IAAKiJ,gBAAgB,CAACjJ,CAAC,CAACkC,MAAM,CAAC4S,OAAO,CAAE;cACpD9S,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDiP,KAAK,EAAC;UAAS;YAAApP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF7D,OAAA,CAAC/B,gBAAgB;YACf+Y,OAAO,eACLhX,OAAA,CAAChC,QAAQ;cACPiZ,OAAO,EAAE5L,UAAW;cACpBjH,QAAQ,EAAGjC,CAAC,IAAKmJ,aAAa,CAACnJ,CAAC,CAACkC,MAAM,CAAC4S,OAAO,CAAE;cACjD9S,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDiP,KAAK,EAAC;UAAK;YAAApP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA,CAAClD,aAAa;QAACoa,QAAQ;QAACrU,EAAE,EAAE;UAAEsU,CAAC,EAAE;QAAE,CAAE;QAAA5U,QAAA,eACnCvC,OAAA,CAACN,aAAa;UACZoG,MAAM,EAAE,GAAI;UACZsR,SAAS,EAAExQ,cAAc,CAACuF,MAAO;UACjCkL,QAAQ,EAAE,EAAG;UACb9T,KAAK,EAAC,MAAM;UAAAhB,QAAA,EAEXA,CAAC;YAAExB,KAAK;YAAEmR;UAAM,CAAC,KAAK;YACrB,MAAMhD,MAAM,GAAGtI,cAAc,CAAC7F,KAAK,CAAC;YACpC,oBACEf,OAAA,CAAChD,QAAQ;cAEPkV,KAAK,EAAEA,KAAM;cACboF,cAAc;cACdC,eAAe,EAAGrI,MAAM,KAAK,MAAM,iBACjClP,OAAA,CAAC5C,UAAU;gBACT4X,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB9Q,OAAO,EAAEA,CAAA,KAAM4L,YAAY,CAACZ,MAAM,CAAE;gBAAA3M,QAAA,eAEpCvC,OAAA,CAAClB,UAAU;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAtB,QAAA,eAEFvC,OAAA,CAAC/C,cAAc;gBAACiH,OAAO,EAAEA,CAAA,KAAM+K,kBAAkB,CAACC,MAAM,CAAE;gBAAA3M,QAAA,eACxDvC,OAAA,CAAC9C,YAAY;kBAACsa,OAAO,EAAEtI;gBAAO;kBAAAxL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZqL,MAAM;cAAAxL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChB7D,OAAA,CAACjD,aAAa;QAAAwF,QAAA,eACZvC,OAAA,CAACtD,MAAM;UAACwH,OAAO,EAAE8K,kBAAmB;UAAAzM,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7D,OAAA,CAACpD,MAAM;MACLoQ,IAAI,EAAE3F,eAAgB;MACtBoP,OAAO,EAAE9G,oBAAqB;MAC9BlL,SAAS;MACTe,QAAQ,EAAC,IAAI;MACbkR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAtU,QAAA,gBAEnBvC,OAAA,CAACnD,WAAW;QAAA0F,QAAA,EAAC;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC7D,OAAA,CAAClD,aAAa;QAAAyF,QAAA,eACZvC,OAAA,CAAC7C,SAAS;UACRqH,SAAS;UACTiT,MAAM,EAAC,OAAO;UACdxV,EAAE,EAAC,MAAM;UACT6Q,KAAK,EAAC,0BAAM;UACZ4E,IAAI,EAAC,MAAM;UACXjT,SAAS;UACTC,OAAO,EAAC,UAAU;UAClBX,KAAK,EAAEoD,SAAU;UACjB/C,QAAQ,EAAGjC,CAAC,IAAKiF,YAAY,CAACjF,CAAC,CAACkC,MAAM,CAACN,KAAK;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB7D,OAAA,CAACjD,aAAa;QAAAwF,QAAA,gBACZvC,OAAA,CAACtD,MAAM;UAACwH,OAAO,EAAEyL,oBAAqB;UAAApN,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD7D,OAAA,CAACtD,MAAM;UAACwH,OAAO,EAAE0L,YAAa;UAACzM,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC2C,GAAA,CApzCIP,aAAa;AAAA0R,GAAA,GAAb1R,aAAa;AAszCnB,eAAeA,aAAa;AAAC,IAAApF,EAAA,EAAA+D,GAAA,EAAAI,GAAA,EAAAgB,GAAA,EAAA2R,GAAA;AAAAC,YAAA,CAAA/W,EAAA;AAAA+W,YAAA,CAAAhT,GAAA;AAAAgT,YAAA,CAAA5S,GAAA;AAAA4S,YAAA,CAAA5R,GAAA;AAAA4R,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}