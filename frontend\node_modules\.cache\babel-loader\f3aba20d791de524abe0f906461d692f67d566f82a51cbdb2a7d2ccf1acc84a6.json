{"ast": null, "code": "import { gridVisibleColumnDefinitionsSelector } from '../features/columns/gridColumnsSelector';\nimport { useGridSelector } from './useGridSelector';\nimport { useGridRootProps } from './useGridRootProps';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../features/columnGrouping/gridColumnGroupsSelector';\nimport { gridPinnedRowsCountSelector, gridRowCountSelector } from '../features/rows/gridRowsSelector';\nimport { useGridPrivateApiContext } from './useGridPrivateApiContext';\nexport const useGridAriaAttributes = () => {\n  var _rootProps$experiment;\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const totalRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  const pinnedRowsCount = useGridSelector(apiRef, gridPinnedRowsCountSelector);\n  let role = 'grid';\n  if ((_rootProps$experiment = rootProps.experimentalFeatures) != null && _rootProps$experiment.ariaV7 && rootProps.treeData) {\n    role = 'treegrid';\n  }\n  return {\n    role,\n    'aria-colcount': visibleColumns.length,\n    'aria-rowcount': headerGroupingMaxDepth + 1 + pinnedRowsCount + totalRowCount,\n    'aria-multiselectable': !rootProps.disableMultipleRowSelection\n  };\n};", "map": {"version": 3, "names": ["gridVisibleColumnDefinitionsSelector", "useGridSelector", "useGridRootProps", "gridColumnGroupsHeaderMaxDepthSelector", "gridPinnedRowsCountSelector", "gridRowCountSelector", "useGridPrivateApiContext", "useGridAriaAttributes", "_rootProps$experiment", "apiRef", "rootProps", "visibleColumns", "totalRowCount", "headerGroupingMaxDepth", "pinnedRowsCount", "role", "experimentalFeatures", "ariaV7", "treeData", "length", "disableMultipleRowSelection"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/utils/useGridAriaAttributes.js"], "sourcesContent": ["import { gridVisibleColumnDefinitionsSelector } from '../features/columns/gridColumnsSelector';\nimport { useGridSelector } from './useGridSelector';\nimport { useGridRootProps } from './useGridRootProps';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../features/columnGrouping/gridColumnGroupsSelector';\nimport { gridPinnedRowsCountSelector, gridRowCountSelector } from '../features/rows/gridRowsSelector';\nimport { useGridPrivateApiContext } from './useGridPrivateApiContext';\nexport const useGridAriaAttributes = () => {\n  var _rootProps$experiment;\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const totalRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  const pinnedRowsCount = useGridSelector(apiRef, gridPinnedRowsCountSelector);\n  let role = 'grid';\n  if ((_rootProps$experiment = rootProps.experimentalFeatures) != null && _rootProps$experiment.ariaV7 && rootProps.treeData) {\n    role = 'treegrid';\n  }\n  return {\n    role,\n    'aria-colcount': visibleColumns.length,\n    'aria-rowcount': headerGroupingMaxDepth + 1 + pinnedRowsCount + totalRowCount,\n    'aria-multiselectable': !rootProps.disableMultipleRowSelection\n  };\n};"], "mappings": "AAAA,SAASA,oCAAoC,QAAQ,yCAAyC;AAC9F,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,sCAAsC,QAAQ,qDAAqD;AAC5G,SAASC,2BAA2B,EAAEC,oBAAoB,QAAQ,mCAAmC;AACrG,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EACzC,IAAIC,qBAAqB;EACzB,MAAMC,MAAM,GAAGH,wBAAwB,CAAC,CAAC;EACzC,MAAMI,SAAS,GAAGR,gBAAgB,CAAC,CAAC;EACpC,MAAMS,cAAc,GAAGV,eAAe,CAACQ,MAAM,EAAET,oCAAoC,CAAC;EACpF,MAAMY,aAAa,GAAGX,eAAe,CAACQ,MAAM,EAAEJ,oBAAoB,CAAC;EACnE,MAAMQ,sBAAsB,GAAGZ,eAAe,CAACQ,MAAM,EAAEN,sCAAsC,CAAC;EAC9F,MAAMW,eAAe,GAAGb,eAAe,CAACQ,MAAM,EAAEL,2BAA2B,CAAC;EAC5E,IAAIW,IAAI,GAAG,MAAM;EACjB,IAAI,CAACP,qBAAqB,GAAGE,SAAS,CAACM,oBAAoB,KAAK,IAAI,IAAIR,qBAAqB,CAACS,MAAM,IAAIP,SAAS,CAACQ,QAAQ,EAAE;IAC1HH,IAAI,GAAG,UAAU;EACnB;EACA,OAAO;IACLA,IAAI;IACJ,eAAe,EAAEJ,cAAc,CAACQ,MAAM;IACtC,eAAe,EAAEN,sBAAsB,GAAG,CAAC,GAAGC,eAAe,GAAGF,aAAa;IAC7E,sBAAsB,EAAE,CAACF,SAAS,CAACU;EACrC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}