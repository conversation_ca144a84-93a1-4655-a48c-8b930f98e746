{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF } from '../../../colDef';\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from './gridColumnsSelector';\nimport { clamp } from '../../../utils/utils';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../columnGrouping/gridColumnGroupsSelector';\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, viewportInnerWidth) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute there minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    const newColumn = _extends({}, rawState.lookup[columnField]);\n    if (rawState.columnVisibilityModel[columnField] === false) {\n      newColumn.computedWidth = 0;\n    } else {\n      let computedWidth;\n      if (newColumn.flex && newColumn.flex > 0) {\n        totalFlexUnits += newColumn.flex;\n        computedWidth = 0;\n        flexColumns.push(newColumn);\n      } else {\n        computedWidth = clamp(newColumn.width || GRID_STRING_COL_DEF.width, newColumn.minWidth || GRID_STRING_COL_DEF.minWidth, newColumn.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n      newColumn.computedWidth = computedWidth;\n    }\n    columnsLookup[columnField] = newColumn;\n  });\n  const initialFreeSpace = Math.max(viewportInnerWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && viewportInnerWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(columnTypes, type) {\n  let colDef = columnTypes[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && columnTypes[type]) {\n    colDef = columnTypes[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnTypes,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false\n}) => {\n  var _apiRef$current$getRo, _apiRef$current$getRo2, _apiRef$current;\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = _extends({}, existingState, newColumn, {\n      hasBeenResized\n    });\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, (_apiRef$current$getRo = (_apiRef$current$getRo2 = (_apiRef$current = apiRef.current).getRootDimensions) == null || (_apiRef$current$getRo2 = _apiRef$current$getRo2.call(_apiRef$current)) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0);\n};\nexport const mergeColumnsState = columnsState => state => _extends({}, state, {\n  columns: columnsState\n});\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getFirstColumnIndexToRender({\n  firstColumnIndex,\n  minColumnIndex,\n  columnBuffer,\n  firstRowToRender,\n  lastRowToRender,\n  apiRef,\n  visibleRows\n}) {\n  const initialFirstColumnToRender = Math.max(firstColumnIndex - columnBuffer, minColumnIndex);\n  const firstColumnToRender = getFirstNonSpannedColumnToRender({\n    firstColumnToRender: initialFirstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  });\n  return firstColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, headerHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  return Math.floor(headerHeight * densityFactor) * ((maxDepth != null ? maxDepth : 0) + 1);\n}", "map": {"version": 3, "names": ["_extends", "DEFAULT_GRID_COL_TYPE_KEY", "GRID_STRING_COL_DEF", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "clamp", "gridDensityFactorSelector", "gridColumnGroupsHeaderMaxDepthSelector", "COLUMNS_DIMENSION_PROPERTIES", "computeFlexColumnsWidth", "initialFreeSpace", "totalFlexUnits", "flexColumns", "uniqueFlexColumns", "Set", "map", "col", "field", "flexColumnsLookup", "all", "frozenFields", "freeze", "value", "frozen", "push", "loopOverFlexItems", "length", "size", "violationsLookup", "min", "max", "remainingFreeSpace", "flexUnits", "totalViolation", "for<PERSON>ach", "computedWidth", "flex", "i", "column", "widthPerFlexUnit", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Object", "keys", "hydrateColumnsWidth", "rawState", "viewportInnerWidth", "columnsLookup", "widthAllocatedBeforeFlex", "orderedFields", "columnField", "newColumn", "lookup", "columnVisibilityModel", "width", "Math", "computedColumnWidths", "applyInitialState", "columnsState", "initialState", "dimensions", "columnsWithUpdatedDimensions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanOrderedFields", "new<PERSON><PERSON><PERSON><PERSON><PERSON>s", "filter", "newColumnLookup", "newColDef", "hasBeenResized", "entries", "key", "Infinity", "newColumnsState", "getDefaultColTypeDef", "columnTypes", "type", "colDef", "createColumnsState", "apiRef", "columnsToUpsert", "keepOnlyColumnsToUpsert", "_apiRef$current$getRo", "_apiRef$current$getRo2", "_apiRef$current", "isInsideStateInitializer", "current", "state", "columns", "currentState", "columnsToKeep", "reduce", "acc", "columnsToUpsertLookup", "existingState", "undefined", "columnsStateWithPreProcessing", "unstable_applyPipeProcessors", "columnsStateWithPortableColumns", "getRootDimensions", "call", "viewportInnerSize", "mergeColumnsState", "getFirstNonSpannedColumnToRender", "firstColumnToRender", "firstRowToRender", "lastRowToRender", "visibleRows", "firstNonSpannedColumnToRender", "row", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "getFirstColumnIndexToRender", "firstColumnIndex", "minColumnIndex", "columnBuffer", "initialFirstColumnToRender", "getTotalHeaderHeight", "headerHeight", "densityFactor", "max<PERSON><PERSON><PERSON>", "floor"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columns/gridColumnsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF } from '../../../colDef';\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from './gridColumnsSelector';\nimport { clamp } from '../../../utils/utils';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../columnGrouping/gridColumnGroupsSelector';\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, viewportInnerWidth) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute there minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    const newColumn = _extends({}, rawState.lookup[columnField]);\n    if (rawState.columnVisibilityModel[columnField] === false) {\n      newColumn.computedWidth = 0;\n    } else {\n      let computedWidth;\n      if (newColumn.flex && newColumn.flex > 0) {\n        totalFlexUnits += newColumn.flex;\n        computedWidth = 0;\n        flexColumns.push(newColumn);\n      } else {\n        computedWidth = clamp(newColumn.width || GRID_STRING_COL_DEF.width, newColumn.minWidth || GRID_STRING_COL_DEF.minWidth, newColumn.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n      newColumn.computedWidth = computedWidth;\n    }\n    columnsLookup[columnField] = newColumn;\n  });\n  const initialFreeSpace = Math.max(viewportInnerWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && viewportInnerWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(columnTypes, type) {\n  let colDef = columnTypes[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && columnTypes[type]) {\n    colDef = columnTypes[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnTypes,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false\n}) => {\n  var _apiRef$current$getRo, _apiRef$current$getRo2, _apiRef$current;\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = _extends({}, existingState, newColumn, {\n      hasBeenResized\n    });\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, (_apiRef$current$getRo = (_apiRef$current$getRo2 = (_apiRef$current = apiRef.current).getRootDimensions) == null || (_apiRef$current$getRo2 = _apiRef$current$getRo2.call(_apiRef$current)) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0);\n};\nexport const mergeColumnsState = columnsState => state => _extends({}, state, {\n  columns: columnsState\n});\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getFirstColumnIndexToRender({\n  firstColumnIndex,\n  minColumnIndex,\n  columnBuffer,\n  firstRowToRender,\n  lastRowToRender,\n  apiRef,\n  visibleRows\n}) {\n  const initialFirstColumnToRender = Math.max(firstColumnIndex - columnBuffer, minColumnIndex);\n  const firstColumnToRender = getFirstNonSpannedColumnToRender({\n    firstColumnToRender: initialFirstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  });\n  return firstColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, headerHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  return Math.floor(headerHeight * densityFactor) * ((maxDepth != null ? maxDepth : 0) + 1);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,yBAAyB,EAAEC,mBAAmB,QAAQ,iBAAiB;AAChF,SAASC,wBAAwB,EAAEC,iCAAiC,QAAQ,uBAAuB;AACnG,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,sCAAsC,QAAQ,4CAA4C;AACnG,OAAO,MAAMC,4BAA4B,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAAC;EACtCC,gBAAgB;EAChBC,cAAc;EACdC;AACF,CAAC,EAAE;EACD,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMC,iBAAiB,GAAG;IACxBC,GAAG,EAAE,CAAC,CAAC;IACPC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAEJ,KAAK,IAAI;MACf,MAAMK,KAAK,GAAGJ,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC;MAC1C,IAAIK,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,EAAE;QAClCL,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACM,MAAM,GAAG,IAAI;QAC1CL,iBAAiB,CAACE,YAAY,CAACI,IAAI,CAACP,KAAK,CAAC;MAC5C;IACF;EACF,CAAC;;EAED;EACA,SAASQ,iBAAiBA,CAAA,EAAG;IAC3B;IACA,IAAIP,iBAAiB,CAACE,YAAY,CAACM,MAAM,KAAKb,iBAAiB,CAACc,IAAI,EAAE;MACpE;IACF;IACA,MAAMC,gBAAgB,GAAG;MACvBC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC;IACR,CAAC;IACD,IAAIC,kBAAkB,GAAGrB,gBAAgB;IACzC,IAAIsB,SAAS,GAAGrB,cAAc;IAC9B,IAAIsB,cAAc,GAAG,CAAC;;IAEtB;IACAf,iBAAiB,CAACE,YAAY,CAACc,OAAO,CAACjB,KAAK,IAAI;MAC9Cc,kBAAkB,IAAIb,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACkB,aAAa;MAChEH,SAAS,IAAId,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACmB,IAAI;IAChD,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,WAAW,CAACc,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;MAC9C,MAAMC,MAAM,GAAG1B,WAAW,CAACyB,CAAC,CAAC;MAC7B,IAAInB,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,IAAIC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,CAACM,MAAM,KAAK,IAAI,EAAE;QAC9F;MACF;;MAEA;MACA,MAAMgB,gBAAgB,GAAGR,kBAAkB,GAAGC,SAAS;MACvD,IAAIG,aAAa,GAAGI,gBAAgB,GAAGD,MAAM,CAACF,IAAI;;MAElD;MACA,IAAID,aAAa,GAAGG,MAAM,CAACE,QAAQ,EAAE;QACnCP,cAAc,IAAIK,MAAM,CAACE,QAAQ,GAAGL,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACE,QAAQ;QAC/BZ,gBAAgB,CAACC,GAAG,CAACS,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C,CAAC,MAAM,IAAIkB,aAAa,GAAGG,MAAM,CAACG,QAAQ,EAAE;QAC1CR,cAAc,IAAIK,MAAM,CAACG,QAAQ,GAAGN,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACG,QAAQ;QAC/Bb,gBAAgB,CAACE,GAAG,CAACQ,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C;MACAC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,GAAG;QACpCM,MAAM,EAAE,KAAK;QACbY,aAAa;QACbC,IAAI,EAAEE,MAAM,CAACF;MACf,CAAC;IACH;;IAEA;IACA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACE,GAAG,CAAC,CAACI,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgB,cAAc,GAAG,CAAC,EAAE;MAC7B;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACC,GAAG,CAAC,CAACK,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAACsB,OAAO,CAAC,CAAC;QACnBjB;MACF,CAAC,KAAK;QACJC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;;IAEA;IACAQ,iBAAiB,CAAC,CAAC;EACrB;EACAA,iBAAiB,CAAC,CAAC;EACnB,OAAOP,iBAAiB,CAACC,GAAG;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyB,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,kBAAkB,KAAK;EACnE,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIpC,cAAc,GAAG,CAAC;EACtB,IAAIqC,wBAAwB,GAAG,CAAC;EAChC,MAAMpC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACAiC,QAAQ,CAACI,aAAa,CAACf,OAAO,CAACgB,WAAW,IAAI;IAC5C,MAAMC,SAAS,GAAGnD,QAAQ,CAAC,CAAC,CAAC,EAAE6C,QAAQ,CAACO,MAAM,CAACF,WAAW,CAAC,CAAC;IAC5D,IAAIL,QAAQ,CAACQ,qBAAqB,CAACH,WAAW,CAAC,KAAK,KAAK,EAAE;MACzDC,SAAS,CAAChB,aAAa,GAAG,CAAC;IAC7B,CAAC,MAAM;MACL,IAAIA,aAAa;MACjB,IAAIgB,SAAS,CAACf,IAAI,IAAIe,SAAS,CAACf,IAAI,GAAG,CAAC,EAAE;QACxCzB,cAAc,IAAIwC,SAAS,CAACf,IAAI;QAChCD,aAAa,GAAG,CAAC;QACjBvB,WAAW,CAACY,IAAI,CAAC2B,SAAS,CAAC;MAC7B,CAAC,MAAM;QACLhB,aAAa,GAAG9B,KAAK,CAAC8C,SAAS,CAACG,KAAK,IAAIpD,mBAAmB,CAACoD,KAAK,EAAEH,SAAS,CAACX,QAAQ,IAAItC,mBAAmB,CAACsC,QAAQ,EAAEW,SAAS,CAACV,QAAQ,IAAIvC,mBAAmB,CAACuC,QAAQ,CAAC;MAC7K;MACAO,wBAAwB,IAAIb,aAAa;MACzCgB,SAAS,CAAChB,aAAa,GAAGA,aAAa;IACzC;IACAY,aAAa,CAACG,WAAW,CAAC,GAAGC,SAAS;EACxC,CAAC,CAAC;EACF,MAAMzC,gBAAgB,GAAG6C,IAAI,CAACzB,GAAG,CAACgB,kBAAkB,GAAGE,wBAAwB,EAAE,CAAC,CAAC;;EAEnF;EACA,IAAIrC,cAAc,GAAG,CAAC,IAAImC,kBAAkB,GAAG,CAAC,EAAE;IAChD,MAAMU,oBAAoB,GAAG/C,uBAAuB,CAAC;MACnDC,gBAAgB;MAChBC,cAAc;MACdC;IACF,CAAC,CAAC;IACF8B,MAAM,CAACC,IAAI,CAACa,oBAAoB,CAAC,CAACtB,OAAO,CAACjB,KAAK,IAAI;MACjD8B,aAAa,CAAC9B,KAAK,CAAC,CAACkB,aAAa,GAAGqB,oBAAoB,CAACvC,KAAK,CAAC,CAACkB,aAAa;IAChF,CAAC,CAAC;EACJ;EACA,OAAOnC,QAAQ,CAAC,CAAC,CAAC,EAAE6C,QAAQ,EAAE;IAC5BO,MAAM,EAAEL;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMU,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;EAC/D,IAAI,CAACA,YAAY,EAAE;IACjB,OAAOD,YAAY;EACrB;EACA,MAAM;IACJT,aAAa,GAAG,EAAE;IAClBW,UAAU,GAAG,CAAC;EAChB,CAAC,GAAGD,YAAY;EAChB,MAAME,4BAA4B,GAAGnB,MAAM,CAACC,IAAI,CAACiB,UAAU,CAAC;EAC5D,IAAIC,4BAA4B,CAACnC,MAAM,KAAK,CAAC,IAAIuB,aAAa,CAACvB,MAAM,KAAK,CAAC,EAAE;IAC3E,OAAOgC,YAAY;EACrB;EACA,MAAMI,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,aAAa,CAACvB,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMpB,KAAK,GAAGgC,aAAa,CAACZ,CAAC,CAAC;;IAE9B;IACA,IAAIqB,YAAY,CAACN,MAAM,CAACnC,KAAK,CAAC,EAAE;MAC9B6C,mBAAmB,CAAC7C,KAAK,CAAC,GAAG,IAAI;MACjC8C,kBAAkB,CAACvC,IAAI,CAACP,KAAK,CAAC;IAChC;EACF;EACA,MAAM+C,gBAAgB,GAAGD,kBAAkB,CAACrC,MAAM,KAAK,CAAC,GAAGgC,YAAY,CAACT,aAAa,GAAG,CAAC,GAAGc,kBAAkB,EAAE,GAAGL,YAAY,CAACT,aAAa,CAACgB,MAAM,CAAChD,KAAK,IAAI,CAAC6C,mBAAmB,CAAC7C,KAAK,CAAC,CAAC,CAAC;EAC3L,MAAMiD,eAAe,GAAGlE,QAAQ,CAAC,CAAC,CAAC,EAAE0D,YAAY,CAACN,MAAM,CAAC;EACzD,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,4BAA4B,CAACnC,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAC/D,MAAMpB,KAAK,GAAG4C,4BAA4B,CAACxB,CAAC,CAAC;IAC7C,MAAM8B,SAAS,GAAGnE,QAAQ,CAAC,CAAC,CAAC,EAAEkE,eAAe,CAACjD,KAAK,CAAC,EAAE;MACrDmD,cAAc,EAAE;IAClB,CAAC,CAAC;IACF1B,MAAM,CAAC2B,OAAO,CAACT,UAAU,CAAC3C,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,CAACoC,GAAG,EAAEhD,KAAK,CAAC,KAAK;MAC1D6C,SAAS,CAACG,GAAG,CAAC,GAAGhD,KAAK,KAAK,CAAC,CAAC,GAAGiD,QAAQ,GAAGjD,KAAK;IAClD,CAAC,CAAC;IACF4C,eAAe,CAACjD,KAAK,CAAC,GAAGkD,SAAS;EACpC;EACA,MAAMK,eAAe,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAE0D,YAAY,EAAE;IACjDT,aAAa,EAAEe,gBAAgB;IAC/BZ,MAAM,EAAEc;EACV,CAAC,CAAC;EACF,OAAOM,eAAe;AACxB,CAAC;AACD,SAASC,oBAAoBA,CAACC,WAAW,EAAEC,IAAI,EAAE;EAC/C,IAAIC,MAAM,GAAGF,WAAW,CAACzE,yBAAyB,CAAC;EACnD,IAAI0E,IAAI,IAAID,WAAW,CAACC,IAAI,CAAC,EAAE;IAC7BC,MAAM,GAAGF,WAAW,CAACC,IAAI,CAAC;EAC5B;EACA,OAAOC,MAAM;AACf;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,MAAM;EACNC,eAAe;EACfpB,YAAY;EACZe,WAAW;EACXrB,qBAAqB,GAAGjD,iCAAiC,CAAC0E,MAAM,CAAC;EACjEE,uBAAuB,GAAG;AAC5B,CAAC,KAAK;EACJ,IAAIC,qBAAqB,EAAEC,sBAAsB,EAAEC,eAAe;EAClE,MAAMC,wBAAwB,GAAG,CAACN,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,OAAO;EAC9D,IAAI7B,YAAY;EAChB,IAAI0B,wBAAwB,EAAE;IAC5B1B,YAAY,GAAG;MACbT,aAAa,EAAE,EAAE;MACjBG,MAAM,EAAE,CAAC,CAAC;MACVC;IACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMmC,YAAY,GAAGrF,wBAAwB,CAAC2E,MAAM,CAACO,OAAO,CAACC,KAAK,CAAC;IACnE5B,YAAY,GAAG;MACbT,aAAa,EAAE+B,uBAAuB,GAAG,EAAE,GAAG,CAAC,GAAGQ,YAAY,CAACvC,aAAa,CAAC;MAC7EG,MAAM,EAAEpD,QAAQ,CAAC,CAAC,CAAC,EAAEwF,YAAY,CAACpC,MAAM,CAAC;MACzC;MACAC;IACF,CAAC;EACH;EACA,IAAIoC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIT,uBAAuB,IAAI,CAACI,wBAAwB,EAAE;IACxDK,aAAa,GAAG/C,MAAM,CAACC,IAAI,CAACe,YAAY,CAACN,MAAM,CAAC,CAACsC,MAAM,CAAC,CAACC,GAAG,EAAErB,GAAG,KAAKtE,QAAQ,CAAC,CAAC,CAAC,EAAE2F,GAAG,EAAE;MACtF,CAACrB,GAAG,GAAG;IACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT;EACA,MAAMsB,qBAAqB,GAAG,CAAC,CAAC;EAChCb,eAAe,CAAC7C,OAAO,CAACiB,SAAS,IAAI;IACnC,MAAM;MACJlC;IACF,CAAC,GAAGkC,SAAS;IACbyC,qBAAqB,CAAC3E,KAAK,CAAC,GAAG,IAAI;IACnCwE,aAAa,CAACxE,KAAK,CAAC,GAAG,IAAI;IAC3B,IAAI4E,aAAa,GAAGnC,YAAY,CAACN,MAAM,CAACnC,KAAK,CAAC;IAC9C,IAAI4E,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAG7F,QAAQ,CAAC,CAAC,CAAC,EAAEyE,oBAAoB,CAACC,WAAW,EAAEvB,SAAS,CAACwB,IAAI,CAAC,EAAE;QAC9E1D,KAAK;QACLmD,cAAc,EAAE;MAClB,CAAC,CAAC;MACFV,YAAY,CAACT,aAAa,CAACzB,IAAI,CAACP,KAAK,CAAC;IACxC,CAAC,MAAM,IAAI+D,uBAAuB,EAAE;MAClCtB,YAAY,CAACT,aAAa,CAACzB,IAAI,CAACP,KAAK,CAAC;IACxC;;IAEA;IACA,IAAI4E,aAAa,IAAIA,aAAa,CAAClB,IAAI,KAAKxB,SAAS,CAACwB,IAAI,EAAE;MAC1DkB,aAAa,GAAG7F,QAAQ,CAAC,CAAC,CAAC,EAAEyE,oBAAoB,CAACC,WAAW,EAAEvB,SAAS,CAACwB,IAAI,CAAC,EAAE;QAC9E1D;MACF,CAAC,CAAC;IACJ;IACA,IAAImD,cAAc,GAAGyB,aAAa,CAACzB,cAAc;IACjD5D,4BAA4B,CAAC0B,OAAO,CAACoC,GAAG,IAAI;MAC1C,IAAInB,SAAS,CAACmB,GAAG,CAAC,KAAKwB,SAAS,EAAE;QAChC1B,cAAc,GAAG,IAAI;QACrB,IAAIjB,SAAS,CAACmB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzBnB,SAAS,CAACmB,GAAG,CAAC,GAAGC,QAAQ;QAC3B;MACF;IACF,CAAC,CAAC;IACFb,YAAY,CAACN,MAAM,CAACnC,KAAK,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE6F,aAAa,EAAE1C,SAAS,EAAE;MAClEiB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIY,uBAAuB,IAAI,CAACI,wBAAwB,EAAE;IACxD1C,MAAM,CAACC,IAAI,CAACe,YAAY,CAACN,MAAM,CAAC,CAAClB,OAAO,CAACjB,KAAK,IAAI;MAChD,IAAI,CAACwE,aAAa,CAACxE,KAAK,CAAC,EAAE;QACzB,OAAOyC,YAAY,CAACN,MAAM,CAACnC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;EACA,MAAM8E,6BAA6B,GAAGjB,MAAM,CAACO,OAAO,CAACW,4BAA4B,CAAC,gBAAgB,EAAEtC,YAAY,CAAC;EACjH,MAAMuC,+BAA+B,GAAGxC,iBAAiB,CAACsC,6BAA6B,EAAEpC,YAAY,CAAC;EACtG,OAAOf,mBAAmB,CAACqD,+BAA+B,EAAE,CAAChB,qBAAqB,GAAG,CAACC,sBAAsB,GAAG,CAACC,eAAe,GAAGL,MAAM,CAACO,OAAO,EAAEa,iBAAiB,KAAK,IAAI,IAAI,CAAChB,sBAAsB,GAAGA,sBAAsB,CAACiB,IAAI,CAAChB,eAAe,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAACkB,iBAAiB,CAAC9C,KAAK,KAAK,IAAI,GAAG2B,qBAAqB,GAAG,CAAC,CAAC;AACjW,CAAC;AACD,OAAO,MAAMoB,iBAAiB,GAAG3C,YAAY,IAAI4B,KAAK,IAAItF,QAAQ,CAAC,CAAC,CAAC,EAAEsF,KAAK,EAAE;EAC5EC,OAAO,EAAE7B;AACX,CAAC,CAAC;AACF,OAAO,SAAS4C,gCAAgCA,CAAC;EAC/CC,mBAAmB;EACnBzB,MAAM;EACN0B,gBAAgB;EAChBC,eAAe;EACfC;AACF,CAAC,EAAE;EACD,IAAIC,6BAA6B,GAAGJ,mBAAmB;EACvD,KAAK,IAAIlE,CAAC,GAAGmE,gBAAgB,EAAEnE,CAAC,GAAGoE,eAAe,EAAEpE,CAAC,IAAI,CAAC,EAAE;IAC1D,MAAMuE,GAAG,GAAGF,WAAW,CAACrE,CAAC,CAAC;IAC1B,IAAIuE,GAAG,EAAE;MACP,MAAMC,KAAK,GAAGH,WAAW,CAACrE,CAAC,CAAC,CAACyE,EAAE;MAC/B,MAAMC,eAAe,GAAGjC,MAAM,CAACO,OAAO,CAAC2B,2BAA2B,CAACH,KAAK,EAAEN,mBAAmB,CAAC;MAC9F,IAAIQ,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;QACvDN,6BAA6B,GAAGI,eAAe,CAACG,oBAAoB;MACtE;IACF;EACF;EACA,OAAOP,6BAA6B;AACtC;AACA,OAAO,SAASQ,2BAA2BA,CAAC;EAC1CC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZd,gBAAgB;EAChBC,eAAe;EACf3B,MAAM;EACN4B;AACF,CAAC,EAAE;EACD,MAAMa,0BAA0B,GAAGhE,IAAI,CAACzB,GAAG,CAACsF,gBAAgB,GAAGE,YAAY,EAAED,cAAc,CAAC;EAC5F,MAAMd,mBAAmB,GAAGD,gCAAgC,CAAC;IAC3DC,mBAAmB,EAAEgB,0BAA0B;IAC/CzC,MAAM;IACN0B,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,CAAC;EACF,OAAOH,mBAAmB;AAC5B;AACA,OAAO,SAASiB,oBAAoBA,CAAC1C,MAAM,EAAE2C,YAAY,EAAE;EACzD,MAAMC,aAAa,GAAGpH,yBAAyB,CAACwE,MAAM,CAAC;EACvD,MAAM6C,QAAQ,GAAGpH,sCAAsC,CAACuE,MAAM,CAAC;EAC/D,OAAOvB,IAAI,CAACqE,KAAK,CAACH,YAAY,GAAGC,aAAa,CAAC,IAAI,CAACC,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}