{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>,<PERSON>po<PERSON>,<PERSON><PERSON>,Card,CardActionArea,Grid,Chip,useTheme}from'@mui/material';import TableChartIcon from'@mui/icons-material/TableChart';import ArrowBackIcon from'@mui/icons-material/ArrowBack';import CheckCircleIcon from'@mui/icons-material/CheckCircle';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WorksheetSelect=_ref=>{let{worksheets,onSelect,onBack}=_ref;const theme=useTheme();const[selectedIndex,setSelectedIndex]=useState(null);const handleSelect=(worksheet,index)=>{setSelectedIndex(index);setTimeout(()=>{onSelect(worksheet);},300);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:'text.primary',mb:1},children:\"\\u9009\\u62E9\\u5DE5\\u4F5C\\u8868\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'text.secondary',mb:2},children:\"\\u8BF7\\u4ECE\\u4E0B\\u65B9\\u9009\\u62E9\\u8981\\u5904\\u7406\\u7684Excel\\u5DE5\\u4F5C\\u8868\"}),/*#__PURE__*/_jsx(Chip,{label:\"\\u5171 \".concat(worksheets.length,\" \\u4E2A\\u5DE5\\u4F5C\\u8868\"),size:\"small\",color:\"primary\",variant:\"outlined\"})]}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,sx:{mb:4},children:worksheets.map((worksheet,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{sx:{height:'100%',cursor:'pointer',transition:'all 0.2s ease','&:hover':{transform:'translateY(-4px)',boxShadow:3},border:selectedIndex===index?\"2px solid \".concat(theme.palette.primary.main):'1px solid',borderColor:selectedIndex===index?'primary.main':'divider',backgroundColor:selectedIndex===index?'primary.50':'background.paper'},children:/*#__PURE__*/_jsxs(CardActionArea,{onClick:()=>handleSelect(worksheet,index),sx:{height:'100%',p:3,display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',minHeight:120},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(TableChartIcon,{sx:{fontSize:40,color:selectedIndex===index?'primary.main':'text.secondary'}}),selectedIndex===index&&/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{position:'absolute',ml:2,mt:-1,color:'success.main',fontSize:20}})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,textAlign:'center',color:selectedIndex===index?'primary.main':'text.primary'},children:worksheet}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'text.secondary',textAlign:'center',mt:1},children:\"\\u70B9\\u51FB\\u9009\\u62E9\"})]})})},worksheet))}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-start'},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:onBack,children:\"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u6B65\"})})]});};export default WorksheetSelect;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Card", "CardActionArea", "Grid", "Chip", "useTheme", "TableChartIcon", "ArrowBackIcon", "CheckCircleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "WorksheetSelect", "_ref", "worksheets", "onSelect", "onBack", "theme", "selectedIndex", "setSelectedIndex", "handleSelect", "worksheet", "index", "setTimeout", "children", "sx", "textAlign", "mb", "variant", "fontWeight", "color", "label", "concat", "length", "size", "container", "spacing", "map", "item", "xs", "sm", "md", "height", "cursor", "transition", "transform", "boxShadow", "border", "palette", "primary", "main", "borderColor", "backgroundColor", "onClick", "p", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "fontSize", "position", "ml", "mt", "startIcon"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/WorksheetSelect.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  CardActionArea,\r\n  Grid,\r\n  Chip,\r\n  useTheme\r\n} from '@mui/material';\r\nimport TableChartIcon from '@mui/icons-material/TableChart';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\r\n\r\nconst WorksheetSelect = ({ worksheets, onSelect, onBack }) => {\r\n  const theme = useTheme();\r\n  const [selectedIndex, setSelectedIndex] = useState(null);\r\n\r\n  const handleSelect = (worksheet, index) => {\r\n    setSelectedIndex(index);\r\n    setTimeout(() => {\r\n      onSelect(worksheet);\r\n    }, 300);\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      {/* 标题区域 */}\r\n      <Box sx={{ textAlign: 'center', mb: 4 }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            color: 'text.primary',\r\n            mb: 1,\r\n          }}\r\n        >\r\n          选择工作表\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            color: 'text.secondary',\r\n            mb: 2,\r\n          }}\r\n        >\r\n          请从下方选择要处理的Excel工作表\r\n        </Typography>\r\n        <Chip\r\n          label={`共 ${worksheets.length} 个工作表`}\r\n          size=\"small\"\r\n          color=\"primary\"\r\n          variant=\"outlined\"\r\n        />\r\n      </Box>\r\n\r\n      {/* 工作表卡片网格 */}\r\n      <Grid container spacing={2} sx={{ mb: 4 }}>\r\n        {worksheets.map((worksheet, index) => (\r\n          <Grid item xs={12} sm={6} md={4} key={worksheet}>\r\n            <Card\r\n              sx={{\r\n                height: '100%',\r\n                cursor: 'pointer',\r\n                transition: 'all 0.2s ease',\r\n                '&:hover': {\r\n                  transform: 'translateY(-4px)',\r\n                  boxShadow: 3,\r\n                },\r\n                border: selectedIndex === index\r\n                  ? `2px solid ${theme.palette.primary.main}`\r\n                  : '1px solid',\r\n                borderColor: selectedIndex === index\r\n                  ? 'primary.main'\r\n                  : 'divider',\r\n                backgroundColor: selectedIndex === index\r\n                  ? 'primary.50'\r\n                  : 'background.paper',\r\n              }}\r\n            >\r\n              <CardActionArea\r\n                onClick={() => handleSelect(worksheet, index)}\r\n                sx={{\r\n                  height: '100%',\r\n                  p: 3,\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  minHeight: 120,\r\n                }}\r\n              >\r\n                <Box sx={{ mb: 2 }}>\r\n                  <TableChartIcon\r\n                    sx={{\r\n                      fontSize: 40,\r\n                      color: selectedIndex === index ? 'primary.main' : 'text.secondary',\r\n                    }}\r\n                  />\r\n                  {selectedIndex === index && (\r\n                    <CheckCircleIcon\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        ml: 2,\r\n                        mt: -1,\r\n                        color: 'success.main',\r\n                        fontSize: 20,\r\n                      }}\r\n                    />\r\n                  )}\r\n                </Box>\r\n\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  sx={{\r\n                    fontWeight: 600,\r\n                    textAlign: 'center',\r\n                    color: selectedIndex === index ? 'primary.main' : 'text.primary',\r\n                  }}\r\n                >\r\n                  {worksheet}\r\n                </Typography>\r\n\r\n                <Typography\r\n                  variant=\"body2\"\r\n                  sx={{\r\n                    color: 'text.secondary',\r\n                    textAlign: 'center',\r\n                    mt: 1,\r\n                  }}\r\n                >\r\n                  点击选择\r\n                </Typography>\r\n              </CardActionArea>\r\n            </Card>\r\n          </Grid>\r\n        ))}\r\n      </Grid>\r\n\r\n      {/* 返回按钮 */}\r\n      <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>\r\n        <Button\r\n          variant=\"outlined\"\r\n          startIcon={<ArrowBackIcon />}\r\n          onClick={onBack}\r\n        >\r\n          返回上一步\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default WorksheetSelect; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,IAAI,CACJC,cAAc,CACdC,IAAI,CACJC,IAAI,CACJC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAsC,IAArC,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,MAAO,CAAC,CAAAH,IAAA,CACvD,KAAM,CAAAI,KAAK,CAAGb,QAAQ,CAAC,CAAC,CACxB,KAAM,CAACc,aAAa,CAAEC,gBAAgB,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAExD,KAAM,CAAAwB,YAAY,CAAGA,CAACC,SAAS,CAAEC,KAAK,GAAK,CACzCH,gBAAgB,CAACG,KAAK,CAAC,CACvBC,UAAU,CAAC,IAAM,CACfR,QAAQ,CAACM,SAAS,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAED,mBACEV,KAAA,CAACd,GAAG,EAAA2B,QAAA,eAEFb,KAAA,CAACd,GAAG,EAAC4B,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACtCf,IAAA,CAACX,UAAU,EACT8B,OAAO,CAAC,IAAI,CACZH,EAAE,CAAE,CACFI,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,cAAc,CACrBH,EAAE,CAAE,CACN,CAAE,CAAAH,QAAA,CACH,gCAED,CAAY,CAAC,cACbf,IAAA,CAACX,UAAU,EACT8B,OAAO,CAAC,OAAO,CACfH,EAAE,CAAE,CACFK,KAAK,CAAE,gBAAgB,CACvBH,EAAE,CAAE,CACN,CAAE,CAAAH,QAAA,CACH,qFAED,CAAY,CAAC,cACbf,IAAA,CAACN,IAAI,EACH4B,KAAK,WAAAC,MAAA,CAAOlB,UAAU,CAACmB,MAAM,6BAAQ,CACrCC,IAAI,CAAC,OAAO,CACZJ,KAAK,CAAC,SAAS,CACfF,OAAO,CAAC,UAAU,CACnB,CAAC,EACC,CAAC,cAGNnB,IAAA,CAACP,IAAI,EAACiC,SAAS,MAACC,OAAO,CAAE,CAAE,CAACX,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CACvCV,UAAU,CAACuB,GAAG,CAAC,CAAChB,SAAS,CAAEC,KAAK,gBAC/Bb,IAAA,CAACP,IAAI,EAACoC,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAjB,QAAA,cAC9Bf,IAAA,CAACT,IAAI,EACHyB,EAAE,CAAE,CACFiB,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BC,SAAS,CAAE,CACb,CAAC,CACDC,MAAM,CAAE7B,aAAa,GAAKI,KAAK,cAAAU,MAAA,CACdf,KAAK,CAAC+B,OAAO,CAACC,OAAO,CAACC,IAAI,EACvC,WAAW,CACfC,WAAW,CAAEjC,aAAa,GAAKI,KAAK,CAChC,cAAc,CACd,SAAS,CACb8B,eAAe,CAAElC,aAAa,GAAKI,KAAK,CACpC,YAAY,CACZ,kBACN,CAAE,CAAAE,QAAA,cAEFb,KAAA,CAACV,cAAc,EACboD,OAAO,CAAEA,CAAA,GAAMjC,YAAY,CAACC,SAAS,CAAEC,KAAK,CAAE,CAC9CG,EAAE,CAAE,CACFiB,MAAM,CAAE,MAAM,CACdY,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,SAAS,CAAE,GACb,CAAE,CAAAnC,QAAA,eAEFb,KAAA,CAACd,GAAG,EAAC4B,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjBf,IAAA,CAACJ,cAAc,EACboB,EAAE,CAAE,CACFmC,QAAQ,CAAE,EAAE,CACZ9B,KAAK,CAAEZ,aAAa,GAAKI,KAAK,CAAG,cAAc,CAAG,gBACpD,CAAE,CACH,CAAC,CACDJ,aAAa,GAAKI,KAAK,eACtBb,IAAA,CAACF,eAAe,EACdkB,EAAE,CAAE,CACFoC,QAAQ,CAAE,UAAU,CACpBC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CAAC,CACNjC,KAAK,CAAE,cAAc,CACrB8B,QAAQ,CAAE,EACZ,CAAE,CACH,CACF,EACE,CAAC,cAENnD,IAAA,CAACX,UAAU,EACT8B,OAAO,CAAC,WAAW,CACnBH,EAAE,CAAE,CACFI,UAAU,CAAE,GAAG,CACfH,SAAS,CAAE,QAAQ,CACnBI,KAAK,CAAEZ,aAAa,GAAKI,KAAK,CAAG,cAAc,CAAG,cACpD,CAAE,CAAAE,QAAA,CAEDH,SAAS,CACA,CAAC,cAEbZ,IAAA,CAACX,UAAU,EACT8B,OAAO,CAAC,OAAO,CACfH,EAAE,CAAE,CACFK,KAAK,CAAE,gBAAgB,CACvBJ,SAAS,CAAE,QAAQ,CACnBqC,EAAE,CAAE,CACN,CAAE,CAAAvC,QAAA,CACH,0BAED,CAAY,CAAC,EACC,CAAC,CACb,CAAC,EA3E6BH,SA4EhC,CACP,CAAC,CACE,CAAC,cAGPZ,IAAA,CAACZ,GAAG,EAAC4B,EAAE,CAAE,CAAE8B,OAAO,CAAE,MAAM,CAAEG,cAAc,CAAE,YAAa,CAAE,CAAAlC,QAAA,cACzDf,IAAA,CAACV,MAAM,EACL6B,OAAO,CAAC,UAAU,CAClBoC,SAAS,cAAEvD,IAAA,CAACH,aAAa,GAAE,CAAE,CAC7B+C,OAAO,CAAErC,MAAO,CAAAQ,QAAA,CACjB,gCAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}