{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{Container,Box,Typography,Stepper,Step,StepLabel,Paper,CssBaseline,ThemeProvider,createTheme,Tooltip,IconButton}from'@mui/material';import DeleteSweepIcon from'@mui/icons-material/DeleteSweep';import CalculateIcon from'@mui/icons-material/Calculate';import LightModeIcon from'@mui/icons-material/LightMode';import DarkModeIcon from'@mui/icons-material/DarkMode';import FileUpload from'./components/FileUpload';import WorksheetSelect from'./components/WorksheetSelect';import ProcessForm from'./components/ProcessForm';import ResultDisplay from'./components/ResultDisplay';import ErrorSnackbar from'./components/ErrorSnackbar';// 创建主题函数\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const createAppTheme=mode=>createTheme({palette:{mode,primary:{main:'#1976d2',light:'#42a5f5',dark:'#1565c0'},secondary:{main:'#9c27b0',light:'#ba68c8',dark:'#7b1fa2'},background:{default:mode==='light'?'#f5f5f5':'#121212',paper:mode==='light'?'#ffffff':'#1e1e1e'},text:{primary:mode==='light'?'#212121':'#ffffff',secondary:mode==='light'?'#757575':'#b3b3b3'}},typography:{fontFamily:['-apple-system','BlinkMacSystemFont','\"Segoe UI\"','Roboto','\"Helvetica Neue\"','Arial','sans-serif'].join(','),h4:{fontWeight:600,fontSize:'2rem'},h6:{fontWeight:600,fontSize:'1.25rem'}},shape:{borderRadius:8},components:{MuiPaper:{styleOverrides:{root:{backgroundImage:'none'}}},MuiButton:{styleOverrides:{root:{textTransform:'none',fontWeight:600,borderRadius:'8px',padding:'8px 16px'}}},MuiCard:{styleOverrides:{root:{boxShadow:mode==='light'?'0 2px 8px rgba(0,0,0,0.1)':'0 2px 8px rgba(0,0,0,0.3)'}}}}});const steps=['上传Excel文件','选择工作表','设置参数','查看结果'];function App(){// 主题模式状态\nconst[themeMode,setThemeMode]=useState(()=>{const savedMode=localStorage.getItem('themeMode');return savedMode||'light';});// 创建主题\nconst theme=createAppTheme(themeMode);const[activeStep,setActiveStep]=useState(0);const[fileData,setFileData]=useState(null);const[selectedWorksheet,setSelectedWorksheet]=useState('');const[processParams,setProcessParams]=useState({startCol:'',endCol:'',perHourRate:'',cbuCarHourRate:'',commissionRate:''});const[resultData,setResultData]=useState(null);const[error,setError]=useState('');// 保存ResultDisplay组件中的表格数据\nconst[gridData,setGridData]=useState([]);// 使用ref来避免无限循环\nconst lastGridDataRef=useRef(null);const lastUpdateTimeRef=useRef(0);// 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\nuseEffect(()=>{// 当gridData变化且不为空时，延迟保存到localStorage\nif(gridData&&gridData.length>0){// 使用防抖技术，延迟1秒后保存\nconst saveTimeout=setTimeout(()=>{try{localStorage.setItem('savedGridData',JSON.stringify(gridData));// console.log('保存gridData到localStorage:', gridData.length);\n}catch(error){console.error('保存gridData到localStorage失败:',error);}},1000);// 清除上一个定时器\nreturn()=>clearTimeout(saveTimeout);}},[gridData]);// 在组件加载时，尝试从localStorage恢复数据\nuseEffect(()=>{// 只在初始化时执行一次\nconst savedGridDataString=localStorage.getItem('savedGridData');const savedStateString=localStorage.getItem('commissionState');console.log('检查localStorage恢复:',{hasSavedGridData:!!savedGridDataString,hasSavedState:!!savedStateString});// 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\nif(savedGridDataString){try{const savedData=JSON.parse(savedGridDataString);if(savedData&&savedData.length>0){setGridData(savedData);console.log('从localStorage恢复gridData:',savedData.length);// 如果有savedGridData，说明应该在ResultDisplay页面\n// 需要恢复所有必要的状态以支持ResultDisplay\nif(savedStateString){try{const savedState=JSON.parse(savedStateString);console.log('从localStorage恢复commissionState以支持ResultDisplay');// 恢复到ResultDisplay页面需要的所有状态\nsetActiveStep(3);// 直接跳转到ResultDisplay\nif(savedState.fileData&&savedState.fileData.file_id){setFileData(savedState.fileData);}setSelectedWorksheet(savedState.selectedWorksheet||'');setProcessParams(savedState.processParams||{startCol:'',endCol:'',perHourRate:'',cbuCarHourRate:'',commissionRate:''});setResultData(savedState.resultData||null);}catch(error){console.error('解析commissionState失败:',error);// 即使commissionState解析失败，也要跳转到ResultDisplay\nsetActiveStep(3);}}else{// 没有commissionState但有gridData，创建最小状态支持ResultDisplay\nconsole.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');setActiveStep(3);// 创建最小的状态以支持ResultDisplay正常工作\n// 使用时间戳作为fileId，确保唯一性\nconst recoveredFileId=\"recovered_\".concat(Date.now());setFileData({file_id:recoveredFileId,worksheets:['recovered']});setSelectedWorksheet('recovered');setResultData([]);// 空的结果数据，因为实际数据在gridData中\n}}}catch(error){console.error('解析savedGridData失败:',error);}}else if(savedStateString){// 只有commissionState，没有gridData的情况\ntry{const savedState=JSON.parse(savedStateString);console.log('只恢复commissionState，没有gridData');setActiveStep(savedState.activeStep||0);if(savedState.fileData&&savedState.fileData.file_id){setFileData(savedState.fileData);}setSelectedWorksheet(savedState.selectedWorksheet||'');setProcessParams(savedState.processParams||{startCol:'',endCol:'',perHourRate:'',cbuCarHourRate:'',commissionRate:''});setResultData(savedState.resultData||null);}catch(error){console.error('解析commissionState失败:',error);}}},[]);const resetPagePosition=()=>{window.scrollTo(0,0);};const handleFileUpload=data=>{setFileData(data);setActiveStep(1);};const handleWorksheetSelect=worksheet=>{setSelectedWorksheet(worksheet);resetPagePosition();setActiveStep(2);};const handleProcessSubmit=(params,result)=>{setProcessParams(params);setResultData(result);resetPagePosition();setActiveStep(3);// 当获取新的处理结果时，清除之前的gridData\nsetGridData([]);localStorage.removeItem('savedGridData');// 保存当前状态到localStorage，以便页面刷新后能恢复\nconst stateToSave={activeStep:3,fileData,selectedWorksheet,processParams:params,resultData:result};try{localStorage.setItem('commissionState',JSON.stringify(stateToSave));console.log('保存commissionState到localStorage');}catch(error){console.error('保存commissionState失败:',error);}};const handleReset=()=>{setActiveStep(0);setFileData(null);setSelectedWorksheet('');setProcessParams({startCol:'',endCol:'',perHourRate:'',cbuCarHourRate:'',commissionRate:''});setResultData(null);setGridData([]);// 清除localStorage中的状态\nlocalStorage.removeItem('savedGridData');localStorage.removeItem('commissionState');console.log('重置所有状态并清除localStorage');};// 处理ResultDisplay组件中数据变化的回调\nconst handleGridDataChange=newData=>{// 避免不必要的更新，只有当数据真正变化时才更新状态\nif(newData&&newData.length>0){const now=Date.now();// 如果距离上次更新时间不足500ms，则跳过此次更新\nif(now-lastUpdateTimeRef.current<500){return;}// 比较新数据和上一次的数据是否相同\nconst currentDataString=JSON.stringify(newData);const lastDataString=lastGridDataRef.current;if(lastDataString!==currentDataString){// console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\nlastGridDataRef.current=currentDataString;lastUpdateTimeRef.current=now;setGridData(newData);// 当gridData更新时，保存到localStorage\ntry{localStorage.setItem('savedGridData',JSON.stringify(newData));console.log('App.js中保存gridData到localStorage:',newData.length);}catch(error){console.error('保存gridData到localStorage失败:',error);}// 同时更新commissionState以保持同步\nconst stateToSave={activeStep:3,fileData,selectedWorksheet,processParams,resultData};try{localStorage.setItem('commissionState',JSON.stringify(stateToSave));// console.log('更新commissionState到localStorage');\n}catch(error){console.error('更新commissionState失败:',error);}}else{// console.log('接收到的gridData与上次相同，跳过更新');\n}}};// 切换主题模式\nconst toggleThemeMode=()=>{const newMode=themeMode==='light'?'dark':'light';setThemeMode(newMode);localStorage.setItem('themeMode',newMode);console.log('切换主题模式到:',newMode);};// 清除所有保存的状态\nconst clearAllSavedState=()=>{localStorage.removeItem('savedGridData');localStorage.removeItem('commissionState');setError('所有保存的状态已清除，请刷新页面');console.log('手动清除所有保存的状态');};const handleError=errorMessage=>{setError(errorMessage);};const handleCloseError=()=>{setError('');};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',backgroundColor:'background.default',py:{xs:2,md:4}},children:/*#__PURE__*/_jsxs(Container,{maxWidth:false,sx:{maxWidth:'95vw',// 使用95%的视口宽度\npx:{xs:1,sm:2,md:3}// 响应式内边距\n},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(CalculateIcon,{sx:{fontSize:40,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",sx:{color:'text.primary',fontWeight:600},children:\"\\u4F63\\u91D1\\u8BA1\\u7B97\\u5DE5\\u5177\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5207\\u6362\\u5230\".concat(themeMode==='light'?'深色':'浅色',\"\\u6A21\\u5F0F\"),children:/*#__PURE__*/_jsx(IconButton,{onClick:toggleThemeMode,color:\"primary\",children:themeMode==='light'?/*#__PURE__*/_jsx(DarkModeIcon,{}):/*#__PURE__*/_jsx(LightModeIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u6E05\\u9664\\u6240\\u6709\\u4FDD\\u5B58\\u7684\\u72B6\\u6001\\uFF08\\u5982\\u679C\\u9047\\u5230\\u95EE\\u9898\\u8BF7\\u70B9\\u51FB\\uFF09\",children:/*#__PURE__*/_jsx(IconButton,{onClick:clearAllSavedState,color:\"error\",children:/*#__PURE__*/_jsx(DeleteSweepIcon,{})})})]})]}),/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:{xs:2,md:4}},children:[/*#__PURE__*/_jsx(Box,{sx:{mb:4},children:/*#__PURE__*/_jsx(Stepper,{activeStep:activeStep,children:steps.map(label=>/*#__PURE__*/_jsx(Step,{children:/*#__PURE__*/_jsx(StepLabel,{children:label})},label))})}),/*#__PURE__*/_jsxs(Box,{sx:{minHeight:'400px'},children:[activeStep===0&&/*#__PURE__*/_jsx(FileUpload,{onFileUpload:handleFileUpload,onError:handleError}),activeStep===1&&fileData&&/*#__PURE__*/_jsx(WorksheetSelect,{worksheets:fileData.worksheets,onSelect:handleWorksheetSelect,onBack:()=>setActiveStep(0),onError:handleError}),activeStep===2&&fileData&&selectedWorksheet&&/*#__PURE__*/_jsx(ProcessForm,{fileId:fileData.file_id,worksheet:selectedWorksheet,onSubmit:handleProcessSubmit,onBack:()=>{resetPagePosition();setActiveStep(1);},onError:handleError}),activeStep===3&&(resultData||gridData.length>0)&&/*#__PURE__*/_jsx(ResultDisplay,{data:resultData||[],fileId:fileData===null||fileData===void 0?void 0:fileData.file_id,onReset:handleReset,onError:handleError,savedGridData:gridData,onDataChange:handleGridDataChange})]})]})]})}),/*#__PURE__*/_jsx(ErrorSnackbar,{open:!!error,message:error,onClose:handleCloseError})]})});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Container", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "CssBaseline", "ThemeProvider", "createTheme", "<PERSON><PERSON><PERSON>", "IconButton", "DeleteSweepIcon", "CalculateIcon", "LightModeIcon", "DarkModeIcon", "FileUpload", "WorksheetSelect", "ProcessForm", "ResultDisplay", "ErrorSnackbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "createAppTheme", "mode", "palette", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "join", "h4", "fontWeight", "fontSize", "h6", "shape", "borderRadius", "components", "MuiPaper", "styleOverrides", "root", "backgroundImage", "MuiB<PERSON>on", "textTransform", "padding", "MuiCard", "boxShadow", "steps", "App", "themeMode", "setThemeMode", "savedMode", "localStorage", "getItem", "theme", "activeStep", "setActiveStep", "fileData", "setFileData", "selectedWorksheet", "setSelectedWorksheet", "processParams", "setProcessParams", "startCol", "endCol", "perHourRate", "cbuCarHourRate", "commissionRate", "resultData", "setResultData", "error", "setError", "gridData", "setGridData", "lastGridDataRef", "lastUpdateTimeRef", "length", "saveTimeout", "setTimeout", "setItem", "JSON", "stringify", "console", "clearTimeout", "savedGridDataString", "savedStateString", "log", "hasSavedGridData", "hasSavedState", "savedData", "parse", "savedState", "file_id", "recoveredFileId", "concat", "Date", "now", "worksheets", "resetPagePosition", "window", "scrollTo", "handleFileUpload", "data", "handleWorksheetSelect", "worksheet", "handleProcessSubmit", "params", "result", "removeItem", "stateToSave", "handleReset", "handleGridDataChange", "newData", "current", "currentDataString", "lastDataString", "toggleThemeMode", "newMode", "clearAllSavedState", "handleError", "errorMessage", "handleCloseError", "children", "sx", "minHeight", "backgroundColor", "py", "xs", "md", "max<PERSON><PERSON><PERSON>", "px", "sm", "display", "justifyContent", "alignItems", "mb", "gap", "color", "variant", "component", "title", "onClick", "elevation", "p", "map", "label", "onFileUpload", "onError", "onSelect", "onBack", "fileId", "onSubmit", "onReset", "savedGridData", "onDataChange", "open", "message", "onClose"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Paper,\n  CssBaseline,\n  ThemeProvider,\n  createTheme,\n  Tooltip,\n  IconButton\n} from '@mui/material';\nimport DeleteSweepIcon from '@mui/icons-material/DeleteSweep';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport LightModeIcon from '@mui/icons-material/LightMode';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport FileUpload from './components/FileUpload';\nimport WorksheetSelect from './components/WorksheetSelect';\nimport ProcessForm from './components/ProcessForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport ErrorSnackbar from './components/ErrorSnackbar';\n\n\n\n// 创建主题函数\nconst createAppTheme = (mode) => createTheme({\n  palette: {\n    mode,\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n    },\n    secondary: {\n      main: '#9c27b0',\n      light: '#ba68c8',\n      dark: '#7b1fa2',\n    },\n    background: {\n      default: mode === 'light' ? '#f5f5f5' : '#121212',\n      paper: mode === 'light' ? '#ffffff' : '#1e1e1e',\n    },\n    text: {\n      primary: mode === 'light' ? '#212121' : '#ffffff',\n      secondary: mode === 'light' ? '#757575' : '#b3b3b3',\n    },\n  },\n  typography: {\n    fontFamily: [\n      '-apple-system',\n      'BlinkMacSystemFont',\n      '\"Segoe UI\"',\n      'Roboto',\n      '\"Helvetica Neue\"',\n      'Arial',\n      'sans-serif',\n    ].join(','),\n    h4: {\n      fontWeight: 600,\n      fontSize: '2rem',\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1.25rem',\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n          borderRadius: '8px',\n          padding: '8px 16px',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: mode === 'light'\n            ? '0 2px 8px rgba(0,0,0,0.1)'\n            : '0 2px 8px rgba(0,0,0,0.3)',\n        },\n      },\n    },\n  },\n});\n\nconst steps = ['上传Excel文件', '选择工作表', '设置参数', '查看结果'];\n\nfunction App() {\n  // 主题模式状态\n  const [themeMode, setThemeMode] = useState(() => {\n    const savedMode = localStorage.getItem('themeMode');\n    return savedMode || 'light';\n  });\n\n  // 创建主题\n  const theme = createAppTheme(themeMode);\n\n  const [activeStep, setActiveStep] = useState(0);\n  const [fileData, setFileData] = useState(null);\n  const [selectedWorksheet, setSelectedWorksheet] = useState('');\n  const [processParams, setProcessParams] = useState({\n    startCol: '',\n    endCol: '',\n    perHourRate: '',\n    cbuCarHourRate: '',\n    commissionRate: ''\n  });\n  const [resultData, setResultData] = useState(null);\n  const [error, setError] = useState('');\n\n  \n  // 保存ResultDisplay组件中的表格数据\n  const [gridData, setGridData] = useState([]);\n  \n  // 使用ref来避免无限循环\n  const lastGridDataRef = useRef(null);\n  const lastUpdateTimeRef = useRef(0);\n  \n\n\n  // 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\n  useEffect(() => {\n    // 当gridData变化且不为空时，延迟保存到localStorage\n    if (gridData && gridData.length > 0) {\n      // 使用防抖技术，延迟1秒后保存\n      const saveTimeout = setTimeout(() => {\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(gridData));\n          // console.log('保存gridData到localStorage:', gridData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n      }, 1000);\n      \n      // 清除上一个定时器\n      return () => clearTimeout(saveTimeout);\n    }\n  }, [gridData]);\n\n  // 在组件加载时，尝试从localStorage恢复数据\n  useEffect(() => {\n    // 只在初始化时执行一次\n    const savedGridDataString = localStorage.getItem('savedGridData');\n    const savedStateString = localStorage.getItem('commissionState');\n\n    console.log('检查localStorage恢复:', {\n      hasSavedGridData: !!savedGridDataString,\n      hasSavedState: !!savedStateString\n    });\n\n    // 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\n    if (savedGridDataString) {\n      try {\n        const savedData = JSON.parse(savedGridDataString);\n        if (savedData && savedData.length > 0) {\n          setGridData(savedData);\n          console.log('从localStorage恢复gridData:', savedData.length);\n\n          // 如果有savedGridData，说明应该在ResultDisplay页面\n          // 需要恢复所有必要的状态以支持ResultDisplay\n          if (savedStateString) {\n            try {\n              const savedState = JSON.parse(savedStateString);\n              console.log('从localStorage恢复commissionState以支持ResultDisplay');\n\n              // 恢复到ResultDisplay页面需要的所有状态\n              setActiveStep(3); // 直接跳转到ResultDisplay\n\n              if (savedState.fileData && savedState.fileData.file_id) {\n                setFileData(savedState.fileData);\n              }\n\n              setSelectedWorksheet(savedState.selectedWorksheet || '');\n              setProcessParams(savedState.processParams || {\n                startCol: '',\n                endCol: '',\n                perHourRate: '',\n                cbuCarHourRate: '',\n                commissionRate: ''\n              });\n\n              setResultData(savedState.resultData || null);\n            } catch (error) {\n              console.error('解析commissionState失败:', error);\n              // 即使commissionState解析失败，也要跳转到ResultDisplay\n              setActiveStep(3);\n            }\n          } else {\n            // 没有commissionState但有gridData，创建最小状态支持ResultDisplay\n            console.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');\n            setActiveStep(3);\n\n            // 创建最小的状态以支持ResultDisplay正常工作\n            // 使用时间戳作为fileId，确保唯一性\n            const recoveredFileId = `recovered_${Date.now()}`;\n            setFileData({ file_id: recoveredFileId, worksheets: ['recovered'] });\n            setSelectedWorksheet('recovered');\n            setResultData([]); // 空的结果数据，因为实际数据在gridData中\n          }\n        }\n      } catch (error) {\n        console.error('解析savedGridData失败:', error);\n      }\n    } else if (savedStateString) {\n      // 只有commissionState，没有gridData的情况\n      try {\n        const savedState = JSON.parse(savedStateString);\n        console.log('只恢复commissionState，没有gridData');\n\n        setActiveStep(savedState.activeStep || 0);\n\n        if (savedState.fileData && savedState.fileData.file_id) {\n          setFileData(savedState.fileData);\n        }\n\n        setSelectedWorksheet(savedState.selectedWorksheet || '');\n        setProcessParams(savedState.processParams || {\n          startCol: '',\n          endCol: '',\n          perHourRate: '',\n          cbuCarHourRate: '',\n          commissionRate: ''\n        });\n\n        setResultData(savedState.resultData || null);\n      } catch (error) {\n        console.error('解析commissionState失败:', error);\n      }\n    }\n  }, []);\n\n  const resetPagePosition = () => {\n    window.scrollTo(0, 0);\n  };\n\n  const handleFileUpload = (data) => {\n    setFileData(data);\n    setActiveStep(1);\n  };\n\n  const handleWorksheetSelect = (worksheet) => {\n    setSelectedWorksheet(worksheet);\n    resetPagePosition();\n    setActiveStep(2);\n  };\n\n  const handleProcessSubmit = (params, result) => {\n    setProcessParams(params);\n    setResultData(result);\n    resetPagePosition();\n    setActiveStep(3);\n    // 当获取新的处理结果时，清除之前的gridData\n    setGridData([]);\n    localStorage.removeItem('savedGridData');\n\n    // 保存当前状态到localStorage，以便页面刷新后能恢复\n    const stateToSave = {\n      activeStep: 3,\n      fileData,\n      selectedWorksheet,\n      processParams: params,\n      resultData: result\n    };\n\n    try {\n      localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n      console.log('保存commissionState到localStorage');\n    } catch (error) {\n      console.error('保存commissionState失败:', error);\n    }\n  };\n\n  const handleReset = () => {\n    setActiveStep(0);\n    setFileData(null);\n    setSelectedWorksheet('');\n    setProcessParams({\n      startCol: '',\n      endCol: '',\n      perHourRate: '',\n      cbuCarHourRate: '',\n      commissionRate: ''\n    });\n    setResultData(null);\n    setGridData([]);\n    \n    // 清除localStorage中的状态\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    \n    console.log('重置所有状态并清除localStorage');\n  };\n\n\n  \n  // 处理ResultDisplay组件中数据变化的回调\n  const handleGridDataChange = (newData) => {\n    // 避免不必要的更新，只有当数据真正变化时才更新状态\n    if (newData && newData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次更新时间不足500ms，则跳过此次更新\n      if (now - lastUpdateTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次的数据是否相同\n      const currentDataString = JSON.stringify(newData);\n      const lastDataString = lastGridDataRef.current;\n\n      if (lastDataString !== currentDataString) {\n        // console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\n        lastGridDataRef.current = currentDataString;\n        lastUpdateTimeRef.current = now;\n        setGridData(newData);\n\n        // 当gridData更新时，保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(newData));\n          console.log('App.js中保存gridData到localStorage:', newData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n\n        // 同时更新commissionState以保持同步\n        const stateToSave = {\n          activeStep: 3,\n          fileData,\n          selectedWorksheet,\n          processParams,\n          resultData\n        };\n\n        try {\n          localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n          // console.log('更新commissionState到localStorage');\n        } catch (error) {\n          console.error('更新commissionState失败:', error);\n        }\n      } else {\n        // console.log('接收到的gridData与上次相同，跳过更新');\n      }\n    }\n  };\n\n\n\n  // 切换主题模式\n  const toggleThemeMode = () => {\n    const newMode = themeMode === 'light' ? 'dark' : 'light';\n    setThemeMode(newMode);\n    localStorage.setItem('themeMode', newMode);\n    console.log('切换主题模式到:', newMode);\n  };\n\n  // 清除所有保存的状态\n  const clearAllSavedState = () => {\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    setError('所有保存的状态已清除，请刷新页面');\n    console.log('手动清除所有保存的状态');\n  };\n\n  const handleError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleCloseError = () => {\n    setError('');\n  };\n\n  return (\n    <>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Box\n          sx={{\n            minHeight: '100vh',\n            backgroundColor: 'background.default',\n            py: { xs: 2, md: 4 },\n          }}\n        >\n          <Container\n            maxWidth={false}\n            sx={{\n              maxWidth: '95vw', // 使用95%的视口宽度\n              px: { xs: 1, sm: 2, md: 3 } // 响应式内边距\n            }}\n          >\n            {/* 头部区域 */}\n            <Box\n              sx={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 4,\n              }}\n            >\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <CalculateIcon\n                  sx={{\n                    fontSize: 40,\n                    color: 'primary.main',\n                  }}\n                />\n                <Typography\n                  variant=\"h4\"\n                  component=\"h1\"\n                  sx={{\n                    color: 'text.primary',\n                    fontWeight: 600,\n                  }}\n                >\n                  佣金计算工具\n                </Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={`切换到${themeMode === 'light' ? '深色' : '浅色'}模式`}>\n                  <IconButton\n                    onClick={toggleThemeMode}\n                    color=\"primary\"\n                  >\n                    {themeMode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}\n                  </IconButton>\n                </Tooltip>\n\n                <Tooltip title=\"清除所有保存的状态（如果遇到问题请点击）\">\n                  <IconButton\n                    onClick={clearAllSavedState}\n                    color=\"error\"\n                  >\n                    <DeleteSweepIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n            </Box>\n\n            {/* 主要内容区域 */}\n            <Paper\n              elevation={2}\n              sx={{\n                p: { xs: 2, md: 4 },\n              }}\n            >\n              {/* 步骤指示器 */}\n              <Box sx={{ mb: 4 }}>\n                <Stepper activeStep={activeStep}>\n                  {steps.map((label) => (\n                    <Step key={label}>\n                      <StepLabel>{label}</StepLabel>\n                    </Step>\n                  ))}\n                </Stepper>\n              </Box>\n\n              {/* 步骤内容 */}\n              <Box sx={{ minHeight: '400px' }}>\n                {activeStep === 0 && (\n                  <FileUpload onFileUpload={handleFileUpload} onError={handleError} />\n                )}\n\n                {activeStep === 1 && fileData && (\n                  <WorksheetSelect\n                    worksheets={fileData.worksheets}\n                    onSelect={handleWorksheetSelect}\n                    onBack={() => setActiveStep(0)}\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 2 && fileData && selectedWorksheet && (\n                  <ProcessForm\n                    fileId={fileData.file_id}\n                    worksheet={selectedWorksheet}\n                    onSubmit={handleProcessSubmit}\n                    onBack={() => {resetPagePosition(); setActiveStep(1);} }\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 3 && (resultData || gridData.length > 0) && (\n                  <ResultDisplay\n                    data={resultData || []}\n                    fileId={fileData?.file_id}\n                    onReset={handleReset}\n                    onError={handleError}\n                    savedGridData={gridData}\n                    onDataChange={handleGridDataChange}\n                  />\n                )}\n              </Box>\n            </Paper>\n          </Container>\n        </Box>\n\n        <ErrorSnackbar\n          open={!!error}\n          message={error}\n          onClose={handleCloseError}\n        />\n      </ThemeProvider>\n    </>\n  );\n}\n\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OACEC,SAAS,CACTC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,IAAI,CACJC,SAAS,CACTC,KAAK,CACLC,WAAW,CACXC,aAAa,CACbC,WAAW,CACXC,OAAO,CACPC,UAAU,KACL,eAAe,CACtB,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CAItD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,cAAc,CAAIC,IAAI,EAAKnB,WAAW,CAAC,CAC3CoB,OAAO,CAAE,CACPD,IAAI,CACJE,OAAO,CAAE,CACPC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDC,SAAS,CAAE,CACTH,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDE,UAAU,CAAE,CACVC,OAAO,CAAER,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SAAS,CACjDS,KAAK,CAAET,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SACxC,CAAC,CACDU,IAAI,CAAE,CACJR,OAAO,CAAEF,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SAAS,CACjDM,SAAS,CAAEN,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SAC5C,CACF,CAAC,CACDW,UAAU,CAAE,CACVC,UAAU,CAAE,CACV,eAAe,CACf,oBAAoB,CACpB,YAAY,CACZ,QAAQ,CACR,kBAAkB,CAClB,OAAO,CACP,YAAY,CACb,CAACC,IAAI,CAAC,GAAG,CAAC,CACXC,EAAE,CAAE,CACFC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,MACZ,CAAC,CACDC,EAAE,CAAE,CACFF,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,SACZ,CACF,CAAC,CACDE,KAAK,CAAE,CACLC,YAAY,CAAE,CAChB,CAAC,CACDC,UAAU,CAAE,CACVC,QAAQ,CAAE,CACRC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,eAAe,CAAE,MACnB,CACF,CACF,CAAC,CACDC,SAAS,CAAE,CACTH,cAAc,CAAE,CACdC,IAAI,CAAE,CACJG,aAAa,CAAE,MAAM,CACrBX,UAAU,CAAE,GAAG,CACfI,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,UACX,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPN,cAAc,CAAE,CACdC,IAAI,CAAE,CACJM,SAAS,CAAE7B,IAAI,GAAK,OAAO,CACvB,2BAA2B,CAC3B,2BACN,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,KAAM,CAAA8B,KAAK,CAAG,CAAC,WAAW,CAAE,OAAO,CAAE,MAAM,CAAE,MAAM,CAAC,CAEpD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,IAAM,CAC/C,KAAM,CAAAiE,SAAS,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CACnD,MAAO,CAAAF,SAAS,EAAI,OAAO,CAC7B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,KAAK,CAAGtC,cAAc,CAACiC,SAAS,CAAC,CAEvC,KAAM,CAACM,UAAU,CAAEC,aAAa,CAAC,CAAGtE,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACuE,QAAQ,CAAEC,WAAW,CAAC,CAAGxE,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACyE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC2E,aAAa,CAAEC,gBAAgB,CAAC,CAAG5E,QAAQ,CAAC,CACjD6E,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,cAAc,CAAE,EAClB,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGnF,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACoF,KAAK,CAAEC,QAAQ,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAGtC;AACA,KAAM,CAACsF,QAAQ,CAAEC,WAAW,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAE5C;AACA,KAAM,CAAAwF,eAAe,CAAGtF,MAAM,CAAC,IAAI,CAAC,CACpC,KAAM,CAAAuF,iBAAiB,CAAGvF,MAAM,CAAC,CAAC,CAAC,CAInC;AACAD,SAAS,CAAC,IAAM,CACd;AACA,GAAIqF,QAAQ,EAAIA,QAAQ,CAACI,MAAM,CAAG,CAAC,CAAE,CACnC;AACA,KAAM,CAAAC,WAAW,CAAGC,UAAU,CAAC,IAAM,CACnC,GAAI,CACF1B,YAAY,CAAC2B,OAAO,CAAC,eAAe,CAAEC,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC,CAAC,CAC/D;AACF,CAAE,MAAOF,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAMa,YAAY,CAACN,WAAW,CAAC,CACxC,CACF,CAAC,CAAE,CAACL,QAAQ,CAAC,CAAC,CAEd;AACArF,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAiG,mBAAmB,CAAGhC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CACjE,KAAM,CAAAgC,gBAAgB,CAAGjC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAEhE6B,OAAO,CAACI,GAAG,CAAC,mBAAmB,CAAE,CAC/BC,gBAAgB,CAAE,CAAC,CAACH,mBAAmB,CACvCI,aAAa,CAAE,CAAC,CAACH,gBACnB,CAAC,CAAC,CAEF;AACA,GAAID,mBAAmB,CAAE,CACvB,GAAI,CACF,KAAM,CAAAK,SAAS,CAAGT,IAAI,CAACU,KAAK,CAACN,mBAAmB,CAAC,CACjD,GAAIK,SAAS,EAAIA,SAAS,CAACb,MAAM,CAAG,CAAC,CAAE,CACrCH,WAAW,CAACgB,SAAS,CAAC,CACtBP,OAAO,CAACI,GAAG,CAAC,0BAA0B,CAAEG,SAAS,CAACb,MAAM,CAAC,CAEzD;AACA;AACA,GAAIS,gBAAgB,CAAE,CACpB,GAAI,CACF,KAAM,CAAAM,UAAU,CAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC,CAC/CH,OAAO,CAACI,GAAG,CAAC,gDAAgD,CAAC,CAE7D;AACA9B,aAAa,CAAC,CAAC,CAAC,CAAE;AAElB,GAAImC,UAAU,CAAClC,QAAQ,EAAIkC,UAAU,CAAClC,QAAQ,CAACmC,OAAO,CAAE,CACtDlC,WAAW,CAACiC,UAAU,CAAClC,QAAQ,CAAC,CAClC,CAEAG,oBAAoB,CAAC+B,UAAU,CAAChC,iBAAiB,EAAI,EAAE,CAAC,CACxDG,gBAAgB,CAAC6B,UAAU,CAAC9B,aAAa,EAAI,CAC3CE,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,cAAc,CAAE,EAClB,CAAC,CAAC,CAEFE,aAAa,CAACsB,UAAU,CAACvB,UAAU,EAAI,IAAI,CAAC,CAC9C,CAAE,MAAOE,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACAd,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,IAAM,CACL;AACA0B,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC,CACjE9B,aAAa,CAAC,CAAC,CAAC,CAEhB;AACA;AACA,KAAM,CAAAqC,eAAe,cAAAC,MAAA,CAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACjDtC,WAAW,CAAC,CAAEkC,OAAO,CAAEC,eAAe,CAAEI,UAAU,CAAE,CAAC,WAAW,CAAE,CAAC,CAAC,CACpErC,oBAAoB,CAAC,WAAW,CAAC,CACjCS,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC5C,CACF,CAAC,IAAM,IAAIe,gBAAgB,CAAE,CAC3B;AACA,GAAI,CACF,KAAM,CAAAM,UAAU,CAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC,CAC/CH,OAAO,CAACI,GAAG,CAAC,+BAA+B,CAAC,CAE5C9B,aAAa,CAACmC,UAAU,CAACpC,UAAU,EAAI,CAAC,CAAC,CAEzC,GAAIoC,UAAU,CAAClC,QAAQ,EAAIkC,UAAU,CAAClC,QAAQ,CAACmC,OAAO,CAAE,CACtDlC,WAAW,CAACiC,UAAU,CAAClC,QAAQ,CAAC,CAClC,CAEAG,oBAAoB,CAAC+B,UAAU,CAAChC,iBAAiB,EAAI,EAAE,CAAC,CACxDG,gBAAgB,CAAC6B,UAAU,CAAC9B,aAAa,EAAI,CAC3CE,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,cAAc,CAAE,EAClB,CAAC,CAAC,CAEFE,aAAa,CAACsB,UAAU,CAACvB,UAAU,EAAI,IAAI,CAAC,CAC9C,CAAE,MAAOE,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA4B,iBAAiB,CAAGA,CAAA,GAAM,CAC9BC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,IAAI,EAAK,CACjC5C,WAAW,CAAC4C,IAAI,CAAC,CACjB9C,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,CAED,KAAM,CAAA+C,qBAAqB,CAAIC,SAAS,EAAK,CAC3C5C,oBAAoB,CAAC4C,SAAS,CAAC,CAC/BN,iBAAiB,CAAC,CAAC,CACnB1C,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,CAED,KAAM,CAAAiD,mBAAmB,CAAGA,CAACC,MAAM,CAAEC,MAAM,GAAK,CAC9C7C,gBAAgB,CAAC4C,MAAM,CAAC,CACxBrC,aAAa,CAACsC,MAAM,CAAC,CACrBT,iBAAiB,CAAC,CAAC,CACnB1C,aAAa,CAAC,CAAC,CAAC,CAChB;AACAiB,WAAW,CAAC,EAAE,CAAC,CACfrB,YAAY,CAACwD,UAAU,CAAC,eAAe,CAAC,CAExC;AACA,KAAM,CAAAC,WAAW,CAAG,CAClBtD,UAAU,CAAE,CAAC,CACbE,QAAQ,CACRE,iBAAiB,CACjBE,aAAa,CAAE6C,MAAM,CACrBtC,UAAU,CAAEuC,MACd,CAAC,CAED,GAAI,CACFvD,YAAY,CAAC2B,OAAO,CAAC,iBAAiB,CAAEC,IAAI,CAACC,SAAS,CAAC4B,WAAW,CAAC,CAAC,CACpE3B,OAAO,CAACI,GAAG,CAAC,gCAAgC,CAAC,CAC/C,CAAE,MAAOhB,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAwC,WAAW,CAAGA,CAAA,GAAM,CACxBtD,aAAa,CAAC,CAAC,CAAC,CAChBE,WAAW,CAAC,IAAI,CAAC,CACjBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,gBAAgB,CAAC,CACfC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,cAAc,CAAE,EAClB,CAAC,CAAC,CACFE,aAAa,CAAC,IAAI,CAAC,CACnBI,WAAW,CAAC,EAAE,CAAC,CAEf;AACArB,YAAY,CAACwD,UAAU,CAAC,eAAe,CAAC,CACxCxD,YAAY,CAACwD,UAAU,CAAC,iBAAiB,CAAC,CAE1C1B,OAAO,CAACI,GAAG,CAAC,uBAAuB,CAAC,CACtC,CAAC,CAID;AACA,KAAM,CAAAyB,oBAAoB,CAAIC,OAAO,EAAK,CACxC;AACA,GAAIA,OAAO,EAAIA,OAAO,CAACpC,MAAM,CAAG,CAAC,CAAE,CACjC,KAAM,CAAAoB,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACA,GAAIA,GAAG,CAAGrB,iBAAiB,CAACsC,OAAO,CAAG,GAAG,CAAE,CACzC,OACF,CAEA;AACA,KAAM,CAAAC,iBAAiB,CAAGlC,IAAI,CAACC,SAAS,CAAC+B,OAAO,CAAC,CACjD,KAAM,CAAAG,cAAc,CAAGzC,eAAe,CAACuC,OAAO,CAE9C,GAAIE,cAAc,GAAKD,iBAAiB,CAAE,CACxC;AACAxC,eAAe,CAACuC,OAAO,CAAGC,iBAAiB,CAC3CvC,iBAAiB,CAACsC,OAAO,CAAGjB,GAAG,CAC/BvB,WAAW,CAACuC,OAAO,CAAC,CAEpB;AACA,GAAI,CACF5D,YAAY,CAAC2B,OAAO,CAAC,eAAe,CAAEC,IAAI,CAACC,SAAS,CAAC+B,OAAO,CAAC,CAAC,CAC9D9B,OAAO,CAACI,GAAG,CAAC,iCAAiC,CAAE0B,OAAO,CAACpC,MAAM,CAAC,CAChE,CAAE,MAAON,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CAEA;AACA,KAAM,CAAAuC,WAAW,CAAG,CAClBtD,UAAU,CAAE,CAAC,CACbE,QAAQ,CACRE,iBAAiB,CACjBE,aAAa,CACbO,UACF,CAAC,CAED,GAAI,CACFhB,YAAY,CAAC2B,OAAO,CAAC,iBAAiB,CAAEC,IAAI,CAACC,SAAS,CAAC4B,WAAW,CAAC,CAAC,CACpE;AACF,CAAE,MAAOvC,KAAK,CAAE,CACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,IAAM,CACL;AAAA,CAEJ,CACF,CAAC,CAID;AACA,KAAM,CAAA8C,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,OAAO,CAAGpE,SAAS,GAAK,OAAO,CAAG,MAAM,CAAG,OAAO,CACxDC,YAAY,CAACmE,OAAO,CAAC,CACrBjE,YAAY,CAAC2B,OAAO,CAAC,WAAW,CAAEsC,OAAO,CAAC,CAC1CnC,OAAO,CAACI,GAAG,CAAC,UAAU,CAAE+B,OAAO,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/BlE,YAAY,CAACwD,UAAU,CAAC,eAAe,CAAC,CACxCxD,YAAY,CAACwD,UAAU,CAAC,iBAAiB,CAAC,CAC1CrC,QAAQ,CAAC,kBAAkB,CAAC,CAC5BW,OAAO,CAACI,GAAG,CAAC,aAAa,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAiC,WAAW,CAAIC,YAAY,EAAK,CACpCjD,QAAQ,CAACiD,YAAY,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BlD,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,mBACE5D,IAAA,CAAAI,SAAA,EAAA2G,QAAA,cACE7G,KAAA,CAAChB,aAAa,EAACyD,KAAK,CAAEA,KAAM,CAAAoE,QAAA,eAC1B/G,IAAA,CAACf,WAAW,GAAE,CAAC,cACfe,IAAA,CAACrB,GAAG,EACFqI,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,eAAe,CAAE,oBAAoB,CACrCC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACrB,CAAE,CAAAN,QAAA,cAEF7G,KAAA,CAACxB,SAAS,EACR4I,QAAQ,CAAE,KAAM,CAChBN,EAAE,CAAE,CACFM,QAAQ,CAAE,MAAM,CAAE;AAClBC,EAAE,CAAE,CAAEH,EAAE,CAAE,CAAC,CAAEI,EAAE,CAAE,CAAC,CAAEH,EAAE,CAAE,CAAE,CAAE;AAC9B,CAAE,CAAAN,QAAA,eAGF7G,KAAA,CAACvB,GAAG,EACFqI,EAAE,CAAE,CACFS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,EAAE,CAAE,CACN,CAAE,CAAAb,QAAA,eAEF7G,KAAA,CAACvB,GAAG,EAACqI,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAd,QAAA,eACzD/G,IAAA,CAACT,aAAa,EACZyH,EAAE,CAAE,CACF1F,QAAQ,CAAE,EAAE,CACZwG,KAAK,CAAE,cACT,CAAE,CACH,CAAC,cACF9H,IAAA,CAACpB,UAAU,EACTmJ,OAAO,CAAC,IAAI,CACZC,SAAS,CAAC,IAAI,CACdhB,EAAE,CAAE,CACFc,KAAK,CAAE,cAAc,CACrBzG,UAAU,CAAE,GACd,CAAE,CAAA0F,QAAA,CACH,sCAED,CAAY,CAAC,EACV,CAAC,cAEN7G,KAAA,CAACvB,GAAG,EAACqI,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEI,GAAG,CAAE,CAAE,CAAE,CAAAd,QAAA,eACnC/G,IAAA,CAACZ,OAAO,EAAC6I,KAAK,sBAAA9C,MAAA,CAAQ7C,SAAS,GAAK,OAAO,CAAG,IAAI,CAAG,IAAI,gBAAK,CAAAyE,QAAA,cAC5D/G,IAAA,CAACX,UAAU,EACT6I,OAAO,CAAEzB,eAAgB,CACzBqB,KAAK,CAAC,SAAS,CAAAf,QAAA,CAEdzE,SAAS,GAAK,OAAO,cAAGtC,IAAA,CAACP,YAAY,GAAE,CAAC,cAAGO,IAAA,CAACR,aAAa,GAAE,CAAC,CACnD,CAAC,CACN,CAAC,cAEVQ,IAAA,CAACZ,OAAO,EAAC6I,KAAK,CAAC,0HAAsB,CAAAlB,QAAA,cACnC/G,IAAA,CAACX,UAAU,EACT6I,OAAO,CAAEvB,kBAAmB,CAC5BmB,KAAK,CAAC,OAAO,CAAAf,QAAA,cAEb/G,IAAA,CAACV,eAAe,GAAE,CAAC,CACT,CAAC,CACN,CAAC,EACP,CAAC,EACH,CAAC,cAGNY,KAAA,CAAClB,KAAK,EACJmJ,SAAS,CAAE,CAAE,CACbnB,EAAE,CAAE,CACFoB,CAAC,CAAE,CAAEhB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACpB,CAAE,CAAAN,QAAA,eAGF/G,IAAA,CAACrB,GAAG,EAACqI,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cACjB/G,IAAA,CAACnB,OAAO,EAAC+D,UAAU,CAAEA,UAAW,CAAAmE,QAAA,CAC7B3E,KAAK,CAACiG,GAAG,CAAEC,KAAK,eACftI,IAAA,CAAClB,IAAI,EAAAiI,QAAA,cACH/G,IAAA,CAACjB,SAAS,EAAAgI,QAAA,CAAEuB,KAAK,CAAY,CAAC,EADrBA,KAEL,CACP,CAAC,CACK,CAAC,CACP,CAAC,cAGNpI,KAAA,CAACvB,GAAG,EAACqI,EAAE,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAF,QAAA,EAC7BnE,UAAU,GAAK,CAAC,eACf5C,IAAA,CAACN,UAAU,EAAC6I,YAAY,CAAE7C,gBAAiB,CAAC8C,OAAO,CAAE5B,WAAY,CAAE,CACpE,CAEAhE,UAAU,GAAK,CAAC,EAAIE,QAAQ,eAC3B9C,IAAA,CAACL,eAAe,EACd2F,UAAU,CAAExC,QAAQ,CAACwC,UAAW,CAChCmD,QAAQ,CAAE7C,qBAAsB,CAChC8C,MAAM,CAAEA,CAAA,GAAM7F,aAAa,CAAC,CAAC,CAAE,CAC/B2F,OAAO,CAAE5B,WAAY,CACtB,CACF,CAEAhE,UAAU,GAAK,CAAC,EAAIE,QAAQ,EAAIE,iBAAiB,eAChDhD,IAAA,CAACJ,WAAW,EACV+I,MAAM,CAAE7F,QAAQ,CAACmC,OAAQ,CACzBY,SAAS,CAAE7C,iBAAkB,CAC7B4F,QAAQ,CAAE9C,mBAAoB,CAC9B4C,MAAM,CAAEA,CAAA,GAAM,CAACnD,iBAAiB,CAAC,CAAC,CAAE1C,aAAa,CAAC,CAAC,CAAC,CAAC,CAAG,CACxD2F,OAAO,CAAE5B,WAAY,CACtB,CACF,CAEAhE,UAAU,GAAK,CAAC,GAAKa,UAAU,EAAII,QAAQ,CAACI,MAAM,CAAG,CAAC,CAAC,eACtDjE,IAAA,CAACH,aAAa,EACZ8F,IAAI,CAAElC,UAAU,EAAI,EAAG,CACvBkF,MAAM,CAAE7F,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEmC,OAAQ,CAC1B4D,OAAO,CAAE1C,WAAY,CACrBqC,OAAO,CAAE5B,WAAY,CACrBkC,aAAa,CAAEjF,QAAS,CACxBkF,YAAY,CAAE3C,oBAAqB,CACpC,CACF,EACE,CAAC,EACD,CAAC,EACC,CAAC,CACT,CAAC,cAENpG,IAAA,CAACF,aAAa,EACZkJ,IAAI,CAAE,CAAC,CAACrF,KAAM,CACdsF,OAAO,CAAEtF,KAAM,CACfuF,OAAO,CAAEpC,gBAAiB,CAC3B,CAAC,EACW,CAAC,CAChB,CAAC,CAEP,CAEA,cAAe,CAAAzE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}