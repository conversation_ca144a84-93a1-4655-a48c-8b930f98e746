{"ast": null, "code": "export function isLeaf(node) {\n  return node.field !== undefined;\n}\n\n/**\n * A function used to process headerClassName params.\n */\n\n/**\n * The union type representing the [[GridColDef]] column header class type.\n */", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "node", "field", "undefined"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/models/gridColumnGrouping.js"], "sourcesContent": ["export function isLeaf(node) {\n  return node.field !== undefined;\n}\n\n/**\n * A function used to process headerClassName params.\n */\n\n/**\n * The union type representing the [[GridColDef]] column header class type.\n */"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAOA,IAAI,CAACC,KAAK,KAAKC,SAAS;AACjC;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}