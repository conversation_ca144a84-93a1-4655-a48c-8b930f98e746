{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"components\", \"componentsProps\", \"invisible\", \"open\", \"slotProps\", \"slots\", \"TransitionComponent\", \"transitionDuration\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Fade from '../Fade';\nimport { getBackdropUtilityClass } from './backdropClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'Mu<PERSON><PERSON>ackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})(_ref2 => {\n  let {\n    ownerState\n  } = _ref2;\n  return _extends({\n    position: 'fixed',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    right: 0,\n    bottom: 0,\n    top: 0,\n    left: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    WebkitTapHighlightColor: 'transparent'\n  }, ownerState.invisible && {\n    backgroundColor: 'transparent'\n  });\n});\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  var _slotProps$root, _ref, _slots$root;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      components = {},\n      componentsProps = {},\n      invisible = false,\n      open,\n      slotProps = {},\n      slots = {},\n      TransitionComponent = Fade,\n      transitionDuration\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    invisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const rootSlotProps = (_slotProps$root = slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    timeout: transitionDuration\n  }, other, {\n    children: /*#__PURE__*/_jsx(BackdropRoot, _extends({\n      \"aria-hidden\": true\n    }, rootSlotProps, {\n      as: (_ref = (_slots$root = slots.root) != null ? _slots$root : components.Root) != null ? _ref : component,\n      className: clsx(classes.root, className, rootSlotProps == null ? void 0 : rootSlotProps.className),\n      ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),\n      classes: classes,\n      ref: ref,\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "Fade", "getBackdropUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "invisible", "slots", "root", "BackdropRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref2", "position", "display", "alignItems", "justifyContent", "right", "bottom", "top", "left", "backgroundColor", "WebkitTapHighlightColor", "Backdrop", "forwardRef", "inProps", "ref", "_slotProps$root", "_ref", "_slots$root", "children", "className", "component", "components", "componentsProps", "open", "slotProps", "TransitionComponent", "transitionDuration", "other", "rootSlotProps", "in", "timeout", "as", "Root", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "shape", "bool", "isRequired", "sx", "oneOfType", "arrayOf", "func", "number", "appear", "enter", "exit"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/material/Backdrop/Backdrop.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"components\", \"componentsProps\", \"invisible\", \"open\", \"slotProps\", \"slots\", \"TransitionComponent\", \"transitionDuration\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Fade from '../Fade';\nimport { getBackdropUtilityClass } from './backdropClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'Mu<PERSON><PERSON>ackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  WebkitTapHighlightColor: 'transparent'\n}, ownerState.invisible && {\n  backgroundColor: 'transparent'\n}));\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  var _slotProps$root, _ref, _slots$root;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      components = {},\n      componentsProps = {},\n      invisible = false,\n      open,\n      slotProps = {},\n      slots = {},\n      TransitionComponent = Fade,\n      transitionDuration\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    invisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const rootSlotProps = (_slotProps$root = slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    timeout: transitionDuration\n  }, other, {\n    children: /*#__PURE__*/_jsx(BackdropRoot, _extends({\n      \"aria-hidden\": true\n    }, rootSlotProps, {\n      as: (_ref = (_slots$root = slots.root) != null ? _slots$root : components.Root) != null ? _ref : component,\n      className: clsx(classes.root, className, rootSlotProps == null ? void 0 : rootSlotProps.className),\n      ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),\n      classes: classes,\n      ref: ref,\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,qBAAqB,EAAE,oBAAoB,CAAC;AACjL,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,SAAS,IAAI,WAAW;EACzC,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEP,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMI,YAAY,GAAGZ,MAAM,CAAC,KAAK,EAAE;EACjCa,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACE,SAAS,IAAIQ,MAAM,CAACR,SAAS,CAAC;EAChE;AACF,CAAC,CAAC,CAACS,KAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,KAAA;EAAA,OAAKxB,QAAQ,CAAC;IACbyB,QAAQ,EAAE,OAAO;IACjBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,oBAAoB;IACrCC,uBAAuB,EAAE;EAC3B,CAAC,EAAErB,UAAU,CAACE,SAAS,IAAI;IACzBkB,eAAe,EAAE;EACnB,CAAC,CAAC;AAAA,EAAC;AACH,MAAME,QAAQ,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,IAAIC,eAAe,EAAEC,IAAI,EAAEC,WAAW;EACtC,MAAMnB,KAAK,GAAGf,eAAe,CAAC;IAC5Be,KAAK,EAAEe,OAAO;IACdlB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFuB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpB/B,SAAS,GAAG,KAAK;MACjBgC,IAAI;MACJC,SAAS,GAAG,CAAC,CAAC;MACdhC,KAAK,GAAG,CAAC,CAAC;MACViC,mBAAmB,GAAGzC,IAAI;MAC1B0C;IACF,CAAC,GAAG5B,KAAK;IACT6B,KAAK,GAAGpD,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCsB,SAAS;IACT7B;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMuC,aAAa,GAAG,CAACb,eAAe,GAAGS,SAAS,CAAC/B,IAAI,KAAK,IAAI,GAAGsB,eAAe,GAAGO,eAAe,CAAC7B,IAAI;EACzG,OAAO,aAAaN,IAAI,CAACsC,mBAAmB,EAAEjD,QAAQ,CAAC;IACrDqD,EAAE,EAAEN,IAAI;IACRO,OAAO,EAAEJ;EACX,CAAC,EAAEC,KAAK,EAAE;IACRT,QAAQ,EAAE,aAAa/B,IAAI,CAACO,YAAY,EAAElB,QAAQ,CAAC;MACjD,aAAa,EAAE;IACjB,CAAC,EAAEoD,aAAa,EAAE;MAChBG,EAAE,EAAE,CAACf,IAAI,GAAG,CAACC,WAAW,GAAGzB,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGwB,WAAW,GAAGI,UAAU,CAACW,IAAI,KAAK,IAAI,GAAGhB,IAAI,GAAGI,SAAS;MAC1GD,SAAS,EAAEvC,IAAI,CAACU,OAAO,CAACG,IAAI,EAAE0B,SAAS,EAAES,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACT,SAAS,CAAC;MAClG9B,UAAU,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,EAAEuC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACvC,UAAU,CAAC;MAC/FC,OAAO,EAAEA,OAAO;MAChBwB,GAAG,EAAEA,GAAG;MACRI,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACElB,QAAQ,EAAEvC,SAAS,CAAC0D,IAAI;EACxB;AACF;AACA;EACE/C,OAAO,EAAEX,SAAS,CAAC2D,MAAM;EACzB;AACF;AACA;EACEnB,SAAS,EAAExC,SAAS,CAAC4D,MAAM;EAC3B;AACF;AACA;AACA;EACEnB,SAAS,EAAEzC,SAAS,CAAC6D,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnB,UAAU,EAAE1C,SAAS,CAAC8D,KAAK,CAAC;IAC1BT,IAAI,EAAErD,SAAS,CAAC6D;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElB,eAAe,EAAE3C,SAAS,CAAC8D,KAAK,CAAC;IAC/BhD,IAAI,EAAEd,SAAS,CAAC2D;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE/C,SAAS,EAAEZ,SAAS,CAAC+D,IAAI;EACzB;AACF;AACA;EACEnB,IAAI,EAAE5C,SAAS,CAAC+D,IAAI,CAACC,UAAU;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnB,SAAS,EAAE7C,SAAS,CAAC8D,KAAK,CAAC;IACzBhD,IAAI,EAAEd,SAAS,CAAC2D;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACE9C,KAAK,EAAEb,SAAS,CAAC8D,KAAK,CAAC;IACrBhD,IAAI,EAAEd,SAAS,CAAC6D;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEI,EAAE,EAAEjE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACmE,OAAO,CAACnE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC2D,MAAM,EAAE3D,SAAS,CAAC+D,IAAI,CAAC,CAAC,CAAC,EAAE/D,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC2D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACEb,mBAAmB,EAAE9C,SAAS,CAAC6D,WAAW;EAC1C;AACF;AACA;AACA;EACEd,kBAAkB,EAAE/C,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACqE,MAAM,EAAErE,SAAS,CAAC8D,KAAK,CAAC;IACzEQ,MAAM,EAAEtE,SAAS,CAACqE,MAAM;IACxBE,KAAK,EAAEvE,SAAS,CAACqE,MAAM;IACvBG,IAAI,EAAExE,SAAS,CAACqE;EAClB,CAAC,CAAC,CAAC;AACL,CAAC,GAAG,KAAK,CAAC;AACV,eAAerC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}