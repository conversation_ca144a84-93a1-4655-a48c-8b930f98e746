{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"style\"],\n  _excluded2 = [\"style\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useTheme } from '@mui/material/styles';\nimport { defaultMemoize } from 'reselect';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nimport { useGridRootProps } from '../../utils/useGridRootProps';\nimport { useGridSelector } from '../../utils/useGridSelector';\nimport { gridVisibleColumnDefinitionsSelector, gridColumnsTotalWidthSelector, gridColumnPositionsSelector } from '../columns/gridColumnsSelector';\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from '../focus/gridFocusStateSelector';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { clamp } from '../../../utils/utils';\nimport { selectedIdsLookupSelector } from '../rowSelection/gridRowSelectionSelector';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getFirstNonSpannedColumnToRender } from '../columns/gridColumnsUtils';\nimport { getMinimalContentHeight } from '../rows/gridRowsUtils';\nimport { gridVirtualizationEnabledSelector, gridVirtualizationColumnEnabledSelector } from './gridVirtualizationSelectors';\n\n// Uses binary search to avoid looping through all possible positions\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function binarySearch(offset, positions, sliceStart = 0, sliceEnd = positions.length) {\n  if (positions.length <= 0) {\n    return -1;\n  }\n  if (sliceStart >= sliceEnd) {\n    return sliceStart;\n  }\n  const pivot = sliceStart + Math.floor((sliceEnd - sliceStart) / 2);\n  const itemOffset = positions[pivot];\n  return offset <= itemOffset ? binarySearch(offset, positions, sliceStart, pivot) : binarySearch(offset, positions, pivot + 1, sliceEnd);\n}\nfunction exponentialSearch(offset, positions, index) {\n  let interval = 1;\n  while (index < positions.length && Math.abs(positions[index]) < offset) {\n    index += interval;\n    interval *= 2;\n  }\n  return binarySearch(offset, positions, Math.floor(index / 2), Math.min(index, positions.length));\n}\nexport const getRenderableIndexes = ({\n  firstIndex,\n  lastIndex,\n  buffer,\n  minFirstIndex,\n  maxLastIndex\n}) => {\n  return [clamp(firstIndex - buffer, minFirstIndex, maxLastIndex), clamp(lastIndex + buffer, minFirstIndex, maxLastIndex)];\n};\nexport const areRenderContextsEqual = (context1, context2) => {\n  if (context1 === context2) {\n    return true;\n  }\n  return context1.firstRowIndex === context2.firstRowIndex && context1.lastRowIndex === context2.lastRowIndex && context1.firstColumnIndex === context2.firstColumnIndex && context1.lastColumnIndex === context2.lastColumnIndex;\n};\n// The `maxSize` is 3 so that reselect caches the `renderedColumns` values for the pinned left,\n// unpinned, and pinned right sections.\nconst MEMOIZE_OPTIONS = {\n  maxSize: 3\n};\nexport const useGridVirtualScroller = props => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const enabled = useGridSelector(apiRef, gridVirtualizationEnabledSelector);\n  const enabledForColumns = useGridSelector(apiRef, gridVirtualizationColumnEnabledSelector);\n  const {\n    ref,\n    onRenderZonePositioning,\n    renderZoneMinColumnIndex = 0,\n    renderZoneMaxColumnIndex = visibleColumns.length,\n    getRowProps\n  } = props;\n  const theme = useTheme();\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const cellFocus = useGridSelector(apiRef, gridFocusCellSelector);\n  const cellTabIndex = useGridSelector(apiRef, gridTabIndexCellSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const selectedRowsLookup = useGridSelector(apiRef, selectedIdsLookupSelector);\n  const currentPage = useGridVisibleRows(apiRef, rootProps);\n  const renderZoneRef = React.useRef(null);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(ref, rootRef);\n  const [renderContext, setRenderContextState] = React.useState(null);\n  const prevRenderContext = React.useRef(renderContext);\n  const scrollPosition = React.useRef({\n    top: 0,\n    left: 0\n  });\n  const [containerDimensions, setContainerDimensions] = React.useState({\n    width: null,\n    height: null\n  });\n  const prevTotalWidth = React.useRef(columnsTotalWidth);\n  // Each visible row (not to be confused with a filter result) is composed of a central row element\n  // and up to two additional row elements for pinned columns (left and right).\n  // When hovering any of these elements, the :hover styles are applied only to the row element that\n  // was actually hovered, not its additional siblings. To make it look like a contiguous row,\n  // we add/remove the .Mui-hovered class to all of the row elements inside one visible row.\n  const [hoveredRowId, setHoveredRowId] = React.useState(null);\n  const rowStyleCache = React.useRef(Object.create(null));\n  const prevGetRowProps = React.useRef();\n  const prevRootRowStyle = React.useRef();\n  const getRenderedColumnsRef = React.useRef(defaultMemoize((columns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, indexOfColumnWithFocusedCell) => {\n    // If the selected column is not within the current range of columns being displayed,\n    // we need to render it at either the left or right of the columns,\n    // depending on whether it is above or below the range.\n    let focusedCellColumnIndexNotInRange;\n    const renderedColumns = columns.slice(firstColumnToRender, lastColumnToRender);\n    if (indexOfColumnWithFocusedCell > -1) {\n      // check if it is not on the left pinned column.\n      if (firstColumnToRender > indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell >= minFirstColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n      // check if it is not on the right pinned column.\n      else if (lastColumnToRender < indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell < maxLastColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n    }\n    return {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    };\n  }, MEMOIZE_OPTIONS));\n  const indexOfColumnWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return visibleColumns.findIndex(column => column.field === cellFocus.field);\n    }\n    return -1;\n  }, [cellFocus, visibleColumns]);\n  const computeRenderContext = React.useCallback(() => {\n    if (!enabled) {\n      return {\n        firstRowIndex: 0,\n        lastRowIndex: currentPage.rows.length,\n        firstColumnIndex: 0,\n        lastColumnIndex: visibleColumns.length\n      };\n    }\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n\n    // Clamp the value because the search may return an index out of bounds.\n    // In the last index, this is not needed because Array.slice doesn't include it.\n    const firstRowIndex = Math.min(getNearestIndexToRender(apiRef, currentPage, rowsMeta, top), rowsMeta.positions.length - 1);\n    const lastRowIndex = rootProps.autoHeight ? firstRowIndex + currentPage.rows.length : getNearestIndexToRender(apiRef, currentPage, rowsMeta, top + containerDimensions.height);\n    let firstColumnIndex = 0;\n    let lastColumnIndex = columnPositions.length;\n    if (enabledForColumns) {\n      let hasRowWithAutoHeight = false;\n      const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n        firstIndex: firstRowIndex,\n        lastIndex: lastRowIndex,\n        minFirstIndex: 0,\n        maxLastIndex: currentPage.rows.length,\n        buffer: rootProps.rowBuffer\n      });\n      for (let i = firstRowToRender; i < lastRowToRender && !hasRowWithAutoHeight; i += 1) {\n        const row = currentPage.rows[i];\n        hasRowWithAutoHeight = apiRef.current.rowHasAutoHeight(row.id);\n      }\n      if (!hasRowWithAutoHeight) {\n        firstColumnIndex = binarySearch(Math.abs(left), columnPositions);\n        lastColumnIndex = binarySearch(Math.abs(left) + containerDimensions.width, columnPositions);\n      }\n    }\n    return {\n      firstRowIndex,\n      lastRowIndex,\n      firstColumnIndex,\n      lastColumnIndex\n    };\n  }, [enabled, enabledForColumns, rowsMeta, rootProps.autoHeight, rootProps.rowBuffer, currentPage, columnPositions, visibleColumns.length, apiRef, containerDimensions]);\n  useEnhancedEffect(() => {\n    if (enabled) {\n      // TODO a scroll reset should not be necessary\n      rootRef.current.scrollLeft = 0;\n      rootRef.current.scrollTop = 0;\n    } else {\n      renderZoneRef.current.style.transform = `translate3d(0px, 0px, 0px)`;\n    }\n  }, [enabled]);\n  useEnhancedEffect(() => {\n    setContainerDimensions({\n      width: rootRef.current.clientWidth,\n      height: rootRef.current.clientHeight\n    });\n  }, [rowsMeta.currentPageTotalHeight]);\n  const handleResize = React.useCallback(() => {\n    if (rootRef.current) {\n      setContainerDimensions({\n        width: rootRef.current.clientWidth,\n        height: rootRef.current.clientHeight\n      });\n    }\n  }, []);\n  useGridApiEventHandler(apiRef, 'debouncedResize', handleResize);\n  const updateRenderZonePosition = React.useCallback(nextRenderContext => {\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    const [initialFirstColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: renderZoneMinColumnIndex,\n      maxLastIndex: renderZoneMaxColumnIndex,\n      buffer: rootProps.columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    const direction = theme.direction === 'ltr' ? 1 : -1;\n    const top = gridRowsMetaSelector(apiRef.current.state).positions[firstRowToRender];\n    const left = direction * gridColumnPositionsSelector(apiRef)[firstColumnToRender]; // Call directly the selector because it might be outdated when this method is called\n    renderZoneRef.current.style.transform = `translate3d(${left}px, ${top}px, 0px)`;\n    if (typeof onRenderZonePositioning === 'function') {\n      onRenderZonePositioning({\n        top,\n        left\n      });\n    }\n  }, [apiRef, currentPage.rows, onRenderZonePositioning, renderZoneMinColumnIndex, renderZoneMaxColumnIndex, rootProps.columnBuffer, rootProps.rowBuffer, theme.direction]);\n  const getRenderContext = React.useCallback(() => prevRenderContext.current, []);\n  const setRenderContext = React.useCallback(nextRenderContext => {\n    if (prevRenderContext.current && areRenderContextsEqual(nextRenderContext, prevRenderContext.current)) {\n      updateRenderZonePosition(nextRenderContext);\n      return;\n    }\n    setRenderContextState(nextRenderContext);\n    updateRenderZonePosition(nextRenderContext);\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    apiRef.current.publishEvent('renderedRowsIntervalChange', {\n      firstRowToRender,\n      lastRowToRender\n    });\n    prevRenderContext.current = nextRenderContext;\n  }, [apiRef, setRenderContextState, prevRenderContext, currentPage.rows.length, rootProps.rowBuffer, updateRenderZonePosition]);\n  useEnhancedEffect(() => {\n    if (containerDimensions.width == null) {\n      return;\n    }\n    const initialRenderContext = computeRenderContext();\n    setRenderContext(initialRenderContext);\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n    const params = {\n      top,\n      left,\n      renderContext: initialRenderContext\n    };\n    apiRef.current.publishEvent('scrollPositionChange', params);\n  }, [apiRef, computeRenderContext, containerDimensions.width, setRenderContext]);\n  const handleScroll = useEventCallback(event => {\n    const {\n      scrollTop,\n      scrollLeft\n    } = event.currentTarget;\n    scrollPosition.current.top = scrollTop;\n    scrollPosition.current.left = scrollLeft;\n\n    // On iOS and macOS, negative offsets are possible when swiping past the start\n    if (!prevRenderContext.current || scrollTop < 0) {\n      return;\n    }\n    if (theme.direction === 'ltr') {\n      if (scrollLeft < 0) {\n        return;\n      }\n    }\n    if (theme.direction === 'rtl') {\n      if (scrollLeft > 0) {\n        return;\n      }\n    }\n\n    // When virtualization is disabled, the context never changes during scroll\n    const nextRenderContext = enabled ? computeRenderContext() : prevRenderContext.current;\n    const topRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstRowIndex - prevRenderContext.current.firstRowIndex);\n    const bottomRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastRowIndex - prevRenderContext.current.lastRowIndex);\n    const topColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstColumnIndex - prevRenderContext.current.firstColumnIndex);\n    const bottomColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastColumnIndex - prevRenderContext.current.lastColumnIndex);\n    const shouldSetState = topRowsScrolledSincePreviousRender >= rootProps.rowThreshold || bottomRowsScrolledSincePreviousRender >= rootProps.rowThreshold || topColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || bottomColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || prevTotalWidth.current !== columnsTotalWidth;\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollTop,\n      left: scrollLeft,\n      renderContext: shouldSetState ? nextRenderContext : prevRenderContext.current\n    }, event);\n    if (shouldSetState) {\n      // Prevents batching render context changes\n      ReactDOM.flushSync(() => {\n        setRenderContext(nextRenderContext);\n      });\n      prevTotalWidth.current = columnsTotalWidth;\n    }\n  });\n  const handleWheel = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n  });\n  const handleTouchMove = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n  });\n  const indexOfRowWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return currentPage.rows.findIndex(row => row.id === cellFocus.id);\n    }\n    return -1;\n  }, [cellFocus, currentPage.rows]);\n  useGridApiEventHandler(apiRef, 'rowMouseOver', (params, event) => {\n    var _params$id;\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId((_params$id = params.id) != null ? _params$id : null);\n  });\n  useGridApiEventHandler(apiRef, 'rowMouseOut', (params, event) => {\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId(null);\n  });\n  const getRows = (params = {\n    renderContext\n  }) => {\n    var _rootProps$slotProps;\n    const {\n      onRowRender,\n      renderContext: nextRenderContext,\n      minFirstColumn = renderZoneMinColumnIndex,\n      maxLastColumn = renderZoneMaxColumnIndex,\n      availableSpace = containerDimensions.width,\n      rowIndexOffset = 0,\n      position = 'center'\n    } = params;\n    if (!nextRenderContext || availableSpace == null) {\n      return null;\n    }\n    const rowBuffer = enabled ? rootProps.rowBuffer : 0;\n    const columnBuffer = enabled ? rootProps.columnBuffer : 0;\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rowBuffer\n    });\n    const renderedRows = [];\n    if (params.rows) {\n      params.rows.forEach(row => {\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      });\n    } else {\n      if (!currentPage.range) {\n        return null;\n      }\n      for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n        const row = currentPage.rows[i];\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    // If the selected row is not within the current range of rows being displayed,\n    // we need to render it at either the top or bottom of the rows,\n    // depending on whether it is above or below the range.\n\n    let isRowWithFocusedCellNotInRange = false;\n    if (indexOfRowWithFocusedCell > -1) {\n      const rowWithFocusedCell = currentPage.rows[indexOfRowWithFocusedCell];\n      if (firstRowToRender > indexOfRowWithFocusedCell || lastRowToRender < indexOfRowWithFocusedCell) {\n        isRowWithFocusedCellNotInRange = true;\n        if (indexOfRowWithFocusedCell > firstRowToRender) {\n          renderedRows.push(rowWithFocusedCell);\n        } else {\n          renderedRows.unshift(rowWithFocusedCell);\n        }\n        apiRef.current.calculateColSpan({\n          rowId: rowWithFocusedCell.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    const [initialFirstColumnToRender, lastColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: minFirstColumn,\n      maxLastIndex: maxLastColumn,\n      buffer: columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    let isColumnWihFocusedCellNotInRange = false;\n    if (firstColumnToRender > indexOfColumnWithFocusedCell || lastColumnToRender < indexOfColumnWithFocusedCell) {\n      isColumnWihFocusedCellNotInRange = true;\n    }\n    const {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    } = getRenderedColumnsRef.current(visibleColumns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, isColumnWihFocusedCellNotInRange ? indexOfColumnWithFocusedCell : -1);\n    const _ref = ((_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.row) || {},\n      {\n        style: rootRowStyle\n      } = _ref,\n      rootRowProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const invalidatesCachedRowStyle = prevGetRowProps.current !== getRowProps || prevRootRowStyle.current !== rootRowStyle;\n    if (invalidatesCachedRowStyle) {\n      rowStyleCache.current = Object.create(null);\n    }\n    const rows = [];\n    let isRowWithFocusedCellRendered = false;\n    for (let i = 0; i < renderedRows.length; i += 1) {\n      var _currentPage$range;\n      const {\n        id,\n        model\n      } = renderedRows[i];\n      const isRowNotVisible = isRowWithFocusedCellNotInRange && cellFocus.id === id;\n      const lastVisibleRowIndex = isRowWithFocusedCellNotInRange ? firstRowToRender + i === currentPage.rows.length : firstRowToRender + i === currentPage.rows.length - 1;\n      const baseRowHeight = !apiRef.current.rowHasAutoHeight(id) ? apiRef.current.unstable_getRowHeight(id) : 'auto';\n      let isSelected;\n      if (selectedRowsLookup[id] == null) {\n        isSelected = false;\n      } else {\n        isSelected = apiRef.current.isRowSelectable(id);\n      }\n      if (onRowRender) {\n        onRowRender(id);\n      }\n      const focusedCell = cellFocus !== null && cellFocus.id === id ? cellFocus.field : null;\n      const columnWithFocusedCellNotInRange = focusedCellColumnIndexNotInRange !== undefined && visibleColumns[focusedCellColumnIndexNotInRange];\n      const renderedColumnsWithFocusedCell = columnWithFocusedCellNotInRange && focusedCell ? [columnWithFocusedCellNotInRange, ...renderedColumns] : renderedColumns;\n      let tabbableCell = null;\n      if (cellTabIndex !== null && cellTabIndex.id === id) {\n        const cellParams = apiRef.current.getCellParams(id, cellTabIndex.field);\n        tabbableCell = cellParams.cellMode === 'view' ? cellTabIndex.field : null;\n      }\n      const _ref2 = typeof getRowProps === 'function' && getRowProps(id, model) || {},\n        {\n          style: rowStyle\n        } = _ref2,\n        rowProps = _objectWithoutPropertiesLoose(_ref2, _excluded2);\n      if (!rowStyleCache.current[id]) {\n        const style = _extends({}, rowStyle, rootRowStyle);\n        rowStyleCache.current[id] = style;\n      }\n      let index = rowIndexOffset + ((currentPage == null || (_currentPage$range = currentPage.range) == null ? void 0 : _currentPage$range.firstRowIndex) || 0) + firstRowToRender + i;\n      if (isRowWithFocusedCellNotInRange && (cellFocus == null ? void 0 : cellFocus.id) === id) {\n        index = indexOfRowWithFocusedCell;\n        isRowWithFocusedCellRendered = true;\n      } else if (isRowWithFocusedCellRendered) {\n        index -= 1;\n      }\n      rows.push(/*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n        row: model,\n        rowId: id,\n        focusedCellColumnIndexNotInRange: focusedCellColumnIndexNotInRange,\n        isNotVisible: isRowNotVisible,\n        rowHeight: baseRowHeight,\n        focusedCell: focusedCell,\n        tabbableCell: tabbableCell,\n        renderedColumns: renderedColumnsWithFocusedCell,\n        visibleColumns: visibleColumns,\n        firstColumnToRender: firstColumnToRender,\n        lastColumnToRender: lastColumnToRender,\n        selected: isSelected,\n        index: index,\n        containerWidth: availableSpace,\n        isLastVisible: lastVisibleRowIndex,\n        position: position\n      }, rowProps, rootRowProps, {\n        hovered: hoveredRowId === id,\n        style: rowStyleCache.current[id]\n      }), id));\n    }\n    prevGetRowProps.current = getRowProps;\n    prevRootRowStyle.current = rootRowStyle;\n    return rows;\n  };\n  const needsHorizontalScrollbar = containerDimensions.width && columnsTotalWidth >= containerDimensions.width;\n  const contentSize = React.useMemo(() => {\n    // In cases where the columns exceed the available width,\n    // the horizontal scrollbar should be shown even when there're no rows.\n    // Keeping 1px as minimum height ensures that the scrollbar will visible if necessary.\n    const height = Math.max(rowsMeta.currentPageTotalHeight, 1);\n    let shouldExtendContent = false;\n    if (rootRef != null && rootRef.current && height <= (rootRef == null ? void 0 : rootRef.current.clientHeight)) {\n      shouldExtendContent = true;\n    }\n    const size = {\n      width: needsHorizontalScrollbar ? columnsTotalWidth : 'auto',\n      height,\n      minHeight: shouldExtendContent ? '100%' : 'auto'\n    };\n    if (rootProps.autoHeight && currentPage.rows.length === 0) {\n      size.height = getMinimalContentHeight(apiRef, rootProps.rowHeight); // Give room to show the overlay when there no rows.\n    }\n    return size;\n  }, [apiRef, rootRef, columnsTotalWidth, rowsMeta.currentPageTotalHeight, needsHorizontalScrollbar, rootProps.autoHeight, rootProps.rowHeight, currentPage.rows.length]);\n  React.useEffect(() => {\n    apiRef.current.publishEvent('virtualScrollerContentSizeChange');\n  }, [apiRef, contentSize]);\n  const rootStyle = React.useMemo(() => {\n    const style = {};\n    if (!needsHorizontalScrollbar) {\n      style.overflowX = 'hidden';\n    }\n    if (rootProps.autoHeight) {\n      style.overflowY = 'hidden';\n    }\n    return style;\n  }, [needsHorizontalScrollbar, rootProps.autoHeight]);\n  apiRef.current.register('private', {\n    getRenderContext\n  });\n  return {\n    renderContext,\n    updateRenderZonePosition,\n    getRows,\n    getRootProps: (inputProps = {}) => _extends({\n      ref: handleRef,\n      onScroll: handleScroll,\n      onWheel: handleWheel,\n      onTouchMove: handleTouchMove\n    }, inputProps, {\n      style: inputProps.style ? _extends({}, inputProps.style, rootStyle) : rootStyle,\n      role: 'presentation'\n    }),\n    getContentProps: ({\n      style\n    } = {}) => ({\n      style: style ? _extends({}, style, contentSize) : contentSize,\n      role: 'presentation'\n    }),\n    getRenderZoneProps: () => ({\n      ref: renderZoneRef,\n      role: 'rowgroup'\n    })\n  };\n};\nfunction getNearestIndexToRender(apiRef, currentPage, rowsMeta, offset) {\n  var _currentPage$range2, _currentPage$range3;\n  const lastMeasuredIndexRelativeToAllRows = apiRef.current.getLastMeasuredRowIndex();\n  let allRowsMeasured = lastMeasuredIndexRelativeToAllRows === Infinity;\n  if ((_currentPage$range2 = currentPage.range) != null && _currentPage$range2.lastRowIndex && !allRowsMeasured) {\n    // Check if all rows in this page are already measured\n    allRowsMeasured = lastMeasuredIndexRelativeToAllRows >= currentPage.range.lastRowIndex;\n  }\n  const lastMeasuredIndexRelativeToCurrentPage = clamp(lastMeasuredIndexRelativeToAllRows - (((_currentPage$range3 = currentPage.range) == null ? void 0 : _currentPage$range3.firstRowIndex) || 0), 0, rowsMeta.positions.length);\n  if (allRowsMeasured || rowsMeta.positions[lastMeasuredIndexRelativeToCurrentPage] >= offset) {\n    // If all rows were measured (when no row has \"auto\" as height) or all rows before the offset\n    // were measured, then use a binary search because it's faster.\n    return binarySearch(offset, rowsMeta.positions);\n  }\n\n  // Otherwise, use an exponential search.\n  // If rows have \"auto\" as height, their positions will be based on estimated heights.\n  // In this case, we can skip several steps until we find a position higher than the offset.\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  return exponentialSearch(offset, rowsMeta.positions, lastMeasuredIndexRelativeToCurrentPage);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "ReactDOM", "unstable_useForkRef", "useForkRef", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "useTheme", "defaultMemoize", "useGridPrivateApiContext", "useGridRootProps", "useGridSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnsTotalWidthSelector", "gridColumnPositionsSelector", "gridFocusCellSelector", "gridTabIndexCellSelector", "useGridVisibleRows", "useGridApiEventHandler", "clamp", "selectedIdsLookupSelector", "gridRowsMetaSelector", "getFirstNonSpannedColumnToRender", "getMinimalContentHeight", "gridVirtualizationEnabledSelector", "gridVirtualizationColumnEnabledSelector", "jsx", "_jsx", "binarySearch", "offset", "positions", "sliceStart", "sliceEnd", "length", "pivot", "Math", "floor", "itemOffset", "exponentialSearch", "index", "interval", "abs", "min", "getRenderableIndexes", "firstIndex", "lastIndex", "buffer", "minFirstIndex", "maxLastIndex", "areRenderContextsEqual", "context1", "context2", "firstRowIndex", "lastRowIndex", "firstColumnIndex", "lastColumnIndex", "MEMOIZE_OPTIONS", "maxSize", "useGridVirtualScroller", "props", "apiRef", "rootProps", "visibleColumns", "enabled", "enabledForColumns", "ref", "onRenderZonePositioning", "renderZoneMinColumnIndex", "renderZoneMaxColumnIndex", "getRowProps", "theme", "columnPositions", "columnsTotalWidth", "cellFocus", "cellTabIndex", "rowsMeta", "selectedRowsLookup", "currentPage", "renderZoneRef", "useRef", "rootRef", "handleRef", "renderContext", "setRenderContextState", "useState", "prevRenderContext", "scrollPosition", "top", "left", "containerDimensions", "setContainerDimensions", "width", "height", "prevTotalWidth", "hoveredRowId", "setHoveredRowId", "rowStyleCache", "Object", "create", "prevGetRowProps", "prevRootRowStyle", "getRenderedColumnsRef", "columns", "firstColumnToRender", "lastColumnToRender", "minFirstColumn", "maxLastColumn", "indexOfColumnWithFocusedCell", "focusedCellColumnIndexNotInRange", "renderedColumns", "slice", "useMemo", "findIndex", "column", "field", "computeRenderContext", "useCallback", "rows", "current", "getNearestIndexToRender", "autoHeight", "hasRowWithAutoHeight", "firstRowToRender", "lastRowToRender", "<PERSON><PERSON><PERSON><PERSON>", "i", "row", "rowHasAutoHeight", "id", "scrollLeft", "scrollTop", "style", "transform", "clientWidth", "clientHeight", "currentPageTotalHeight", "handleResize", "updateRenderZonePosition", "nextRenderContext", "initialFirstColumnToRender", "columnBuffer", "visibleRows", "direction", "state", "getRenderContext", "setRenderContext", "publishEvent", "initialRenderContext", "params", "handleScroll", "event", "currentTarget", "topRowsScrolledSincePreviousRender", "bottomRowsScrolledSincePreviousRender", "topColumnsScrolledSincePreviousRender", "bottomColumnsScrolledSincePreviousRender", "shouldSetState", "rowThreshold", "columnThreshold", "flushSync", "handleWheel", "handleTouchMove", "indexOfRowWithFocusedCell", "_params$id", "contains", "relatedTarget", "getRows", "_rootProps$slotProps", "onRowRender", "availableSpace", "rowIndexOffset", "position", "renderedRows", "for<PERSON>ach", "push", "calculateColSpan", "rowId", "range", "isRowWithFocusedCellNotInRange", "rowWithFocusedCell", "unshift", "isColumnWihFocusedCellNotInRange", "_ref", "slotProps", "rootRowStyle", "rootRowProps", "invalidatesCachedRowStyle", "isRowWithFocusedCellRendered", "_currentPage$range", "model", "isRowNotVisible", "lastVisibleRowIndex", "baseRowHeight", "unstable_getRowHeight", "isSelected", "isRowSelectable", "focusedCell", "columnWithFocusedCellNotInRange", "undefined", "renderedColumnsWithFocusedCell", "tabbableCell", "cellParams", "getCellParams", "cellMode", "_ref2", "rowStyle", "rowProps", "slots", "isNotVisible", "rowHeight", "selected", "containerWidth", "isLastVisible", "hovered", "needsHorizontalScrollbar", "contentSize", "max", "shouldExtendContent", "size", "minHeight", "useEffect", "rootStyle", "overflowX", "overflowY", "register", "getRootProps", "inputProps", "onScroll", "onWheel", "onTouchMove", "role", "getContentProps", "getRenderZoneProps", "_currentPage$range2", "_currentPage$range3", "lastMeasuredIndexRelativeToAllRows", "getLastMeasuredRowIndex", "allRowsMeasured", "Infinity", "lastMeasuredIndexRelativeToCurrentPage"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/virtualization/useGridVirtualScroller.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"style\"],\n  _excluded2 = [\"style\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useTheme } from '@mui/material/styles';\nimport { defaultMemoize } from 'reselect';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nimport { useGridRootProps } from '../../utils/useGridRootProps';\nimport { useGridSelector } from '../../utils/useGridSelector';\nimport { gridVisibleColumnDefinitionsSelector, gridColumnsTotalWidthSelector, gridColumnPositionsSelector } from '../columns/gridColumnsSelector';\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from '../focus/gridFocusStateSelector';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { clamp } from '../../../utils/utils';\nimport { selectedIdsLookupSelector } from '../rowSelection/gridRowSelectionSelector';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getFirstNonSpannedColumnToRender } from '../columns/gridColumnsUtils';\nimport { getMinimalContentHeight } from '../rows/gridRowsUtils';\nimport { gridVirtualizationEnabledSelector, gridVirtualizationColumnEnabledSelector } from './gridVirtualizationSelectors';\n\n// Uses binary search to avoid looping through all possible positions\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function binarySearch(offset, positions, sliceStart = 0, sliceEnd = positions.length) {\n  if (positions.length <= 0) {\n    return -1;\n  }\n  if (sliceStart >= sliceEnd) {\n    return sliceStart;\n  }\n  const pivot = sliceStart + Math.floor((sliceEnd - sliceStart) / 2);\n  const itemOffset = positions[pivot];\n  return offset <= itemOffset ? binarySearch(offset, positions, sliceStart, pivot) : binarySearch(offset, positions, pivot + 1, sliceEnd);\n}\nfunction exponentialSearch(offset, positions, index) {\n  let interval = 1;\n  while (index < positions.length && Math.abs(positions[index]) < offset) {\n    index += interval;\n    interval *= 2;\n  }\n  return binarySearch(offset, positions, Math.floor(index / 2), Math.min(index, positions.length));\n}\nexport const getRenderableIndexes = ({\n  firstIndex,\n  lastIndex,\n  buffer,\n  minFirstIndex,\n  maxLastIndex\n}) => {\n  return [clamp(firstIndex - buffer, minFirstIndex, maxLastIndex), clamp(lastIndex + buffer, minFirstIndex, maxLastIndex)];\n};\nexport const areRenderContextsEqual = (context1, context2) => {\n  if (context1 === context2) {\n    return true;\n  }\n  return context1.firstRowIndex === context2.firstRowIndex && context1.lastRowIndex === context2.lastRowIndex && context1.firstColumnIndex === context2.firstColumnIndex && context1.lastColumnIndex === context2.lastColumnIndex;\n};\n// The `maxSize` is 3 so that reselect caches the `renderedColumns` values for the pinned left,\n// unpinned, and pinned right sections.\nconst MEMOIZE_OPTIONS = {\n  maxSize: 3\n};\nexport const useGridVirtualScroller = props => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const enabled = useGridSelector(apiRef, gridVirtualizationEnabledSelector);\n  const enabledForColumns = useGridSelector(apiRef, gridVirtualizationColumnEnabledSelector);\n  const {\n    ref,\n    onRenderZonePositioning,\n    renderZoneMinColumnIndex = 0,\n    renderZoneMaxColumnIndex = visibleColumns.length,\n    getRowProps\n  } = props;\n  const theme = useTheme();\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const cellFocus = useGridSelector(apiRef, gridFocusCellSelector);\n  const cellTabIndex = useGridSelector(apiRef, gridTabIndexCellSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const selectedRowsLookup = useGridSelector(apiRef, selectedIdsLookupSelector);\n  const currentPage = useGridVisibleRows(apiRef, rootProps);\n  const renderZoneRef = React.useRef(null);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(ref, rootRef);\n  const [renderContext, setRenderContextState] = React.useState(null);\n  const prevRenderContext = React.useRef(renderContext);\n  const scrollPosition = React.useRef({\n    top: 0,\n    left: 0\n  });\n  const [containerDimensions, setContainerDimensions] = React.useState({\n    width: null,\n    height: null\n  });\n  const prevTotalWidth = React.useRef(columnsTotalWidth);\n  // Each visible row (not to be confused with a filter result) is composed of a central row element\n  // and up to two additional row elements for pinned columns (left and right).\n  // When hovering any of these elements, the :hover styles are applied only to the row element that\n  // was actually hovered, not its additional siblings. To make it look like a contiguous row,\n  // we add/remove the .Mui-hovered class to all of the row elements inside one visible row.\n  const [hoveredRowId, setHoveredRowId] = React.useState(null);\n  const rowStyleCache = React.useRef(Object.create(null));\n  const prevGetRowProps = React.useRef();\n  const prevRootRowStyle = React.useRef();\n  const getRenderedColumnsRef = React.useRef(defaultMemoize((columns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, indexOfColumnWithFocusedCell) => {\n    // If the selected column is not within the current range of columns being displayed,\n    // we need to render it at either the left or right of the columns,\n    // depending on whether it is above or below the range.\n    let focusedCellColumnIndexNotInRange;\n    const renderedColumns = columns.slice(firstColumnToRender, lastColumnToRender);\n    if (indexOfColumnWithFocusedCell > -1) {\n      // check if it is not on the left pinned column.\n      if (firstColumnToRender > indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell >= minFirstColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n      // check if it is not on the right pinned column.\n      else if (lastColumnToRender < indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell < maxLastColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n    }\n    return {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    };\n  }, MEMOIZE_OPTIONS));\n  const indexOfColumnWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return visibleColumns.findIndex(column => column.field === cellFocus.field);\n    }\n    return -1;\n  }, [cellFocus, visibleColumns]);\n  const computeRenderContext = React.useCallback(() => {\n    if (!enabled) {\n      return {\n        firstRowIndex: 0,\n        lastRowIndex: currentPage.rows.length,\n        firstColumnIndex: 0,\n        lastColumnIndex: visibleColumns.length\n      };\n    }\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n\n    // Clamp the value because the search may return an index out of bounds.\n    // In the last index, this is not needed because Array.slice doesn't include it.\n    const firstRowIndex = Math.min(getNearestIndexToRender(apiRef, currentPage, rowsMeta, top), rowsMeta.positions.length - 1);\n    const lastRowIndex = rootProps.autoHeight ? firstRowIndex + currentPage.rows.length : getNearestIndexToRender(apiRef, currentPage, rowsMeta, top + containerDimensions.height);\n    let firstColumnIndex = 0;\n    let lastColumnIndex = columnPositions.length;\n    if (enabledForColumns) {\n      let hasRowWithAutoHeight = false;\n      const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n        firstIndex: firstRowIndex,\n        lastIndex: lastRowIndex,\n        minFirstIndex: 0,\n        maxLastIndex: currentPage.rows.length,\n        buffer: rootProps.rowBuffer\n      });\n      for (let i = firstRowToRender; i < lastRowToRender && !hasRowWithAutoHeight; i += 1) {\n        const row = currentPage.rows[i];\n        hasRowWithAutoHeight = apiRef.current.rowHasAutoHeight(row.id);\n      }\n      if (!hasRowWithAutoHeight) {\n        firstColumnIndex = binarySearch(Math.abs(left), columnPositions);\n        lastColumnIndex = binarySearch(Math.abs(left) + containerDimensions.width, columnPositions);\n      }\n    }\n    return {\n      firstRowIndex,\n      lastRowIndex,\n      firstColumnIndex,\n      lastColumnIndex\n    };\n  }, [enabled, enabledForColumns, rowsMeta, rootProps.autoHeight, rootProps.rowBuffer, currentPage, columnPositions, visibleColumns.length, apiRef, containerDimensions]);\n  useEnhancedEffect(() => {\n    if (enabled) {\n      // TODO a scroll reset should not be necessary\n      rootRef.current.scrollLeft = 0;\n      rootRef.current.scrollTop = 0;\n    } else {\n      renderZoneRef.current.style.transform = `translate3d(0px, 0px, 0px)`;\n    }\n  }, [enabled]);\n  useEnhancedEffect(() => {\n    setContainerDimensions({\n      width: rootRef.current.clientWidth,\n      height: rootRef.current.clientHeight\n    });\n  }, [rowsMeta.currentPageTotalHeight]);\n  const handleResize = React.useCallback(() => {\n    if (rootRef.current) {\n      setContainerDimensions({\n        width: rootRef.current.clientWidth,\n        height: rootRef.current.clientHeight\n      });\n    }\n  }, []);\n  useGridApiEventHandler(apiRef, 'debouncedResize', handleResize);\n  const updateRenderZonePosition = React.useCallback(nextRenderContext => {\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    const [initialFirstColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: renderZoneMinColumnIndex,\n      maxLastIndex: renderZoneMaxColumnIndex,\n      buffer: rootProps.columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    const direction = theme.direction === 'ltr' ? 1 : -1;\n    const top = gridRowsMetaSelector(apiRef.current.state).positions[firstRowToRender];\n    const left = direction * gridColumnPositionsSelector(apiRef)[firstColumnToRender]; // Call directly the selector because it might be outdated when this method is called\n    renderZoneRef.current.style.transform = `translate3d(${left}px, ${top}px, 0px)`;\n    if (typeof onRenderZonePositioning === 'function') {\n      onRenderZonePositioning({\n        top,\n        left\n      });\n    }\n  }, [apiRef, currentPage.rows, onRenderZonePositioning, renderZoneMinColumnIndex, renderZoneMaxColumnIndex, rootProps.columnBuffer, rootProps.rowBuffer, theme.direction]);\n  const getRenderContext = React.useCallback(() => prevRenderContext.current, []);\n  const setRenderContext = React.useCallback(nextRenderContext => {\n    if (prevRenderContext.current && areRenderContextsEqual(nextRenderContext, prevRenderContext.current)) {\n      updateRenderZonePosition(nextRenderContext);\n      return;\n    }\n    setRenderContextState(nextRenderContext);\n    updateRenderZonePosition(nextRenderContext);\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    apiRef.current.publishEvent('renderedRowsIntervalChange', {\n      firstRowToRender,\n      lastRowToRender\n    });\n    prevRenderContext.current = nextRenderContext;\n  }, [apiRef, setRenderContextState, prevRenderContext, currentPage.rows.length, rootProps.rowBuffer, updateRenderZonePosition]);\n  useEnhancedEffect(() => {\n    if (containerDimensions.width == null) {\n      return;\n    }\n    const initialRenderContext = computeRenderContext();\n    setRenderContext(initialRenderContext);\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n    const params = {\n      top,\n      left,\n      renderContext: initialRenderContext\n    };\n    apiRef.current.publishEvent('scrollPositionChange', params);\n  }, [apiRef, computeRenderContext, containerDimensions.width, setRenderContext]);\n  const handleScroll = useEventCallback(event => {\n    const {\n      scrollTop,\n      scrollLeft\n    } = event.currentTarget;\n    scrollPosition.current.top = scrollTop;\n    scrollPosition.current.left = scrollLeft;\n\n    // On iOS and macOS, negative offsets are possible when swiping past the start\n    if (!prevRenderContext.current || scrollTop < 0) {\n      return;\n    }\n    if (theme.direction === 'ltr') {\n      if (scrollLeft < 0) {\n        return;\n      }\n    }\n    if (theme.direction === 'rtl') {\n      if (scrollLeft > 0) {\n        return;\n      }\n    }\n\n    // When virtualization is disabled, the context never changes during scroll\n    const nextRenderContext = enabled ? computeRenderContext() : prevRenderContext.current;\n    const topRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstRowIndex - prevRenderContext.current.firstRowIndex);\n    const bottomRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastRowIndex - prevRenderContext.current.lastRowIndex);\n    const topColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstColumnIndex - prevRenderContext.current.firstColumnIndex);\n    const bottomColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastColumnIndex - prevRenderContext.current.lastColumnIndex);\n    const shouldSetState = topRowsScrolledSincePreviousRender >= rootProps.rowThreshold || bottomRowsScrolledSincePreviousRender >= rootProps.rowThreshold || topColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || bottomColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || prevTotalWidth.current !== columnsTotalWidth;\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollTop,\n      left: scrollLeft,\n      renderContext: shouldSetState ? nextRenderContext : prevRenderContext.current\n    }, event);\n    if (shouldSetState) {\n      // Prevents batching render context changes\n      ReactDOM.flushSync(() => {\n        setRenderContext(nextRenderContext);\n      });\n      prevTotalWidth.current = columnsTotalWidth;\n    }\n  });\n  const handleWheel = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n  });\n  const handleTouchMove = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n  });\n  const indexOfRowWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return currentPage.rows.findIndex(row => row.id === cellFocus.id);\n    }\n    return -1;\n  }, [cellFocus, currentPage.rows]);\n  useGridApiEventHandler(apiRef, 'rowMouseOver', (params, event) => {\n    var _params$id;\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId((_params$id = params.id) != null ? _params$id : null);\n  });\n  useGridApiEventHandler(apiRef, 'rowMouseOut', (params, event) => {\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId(null);\n  });\n  const getRows = (params = {\n    renderContext\n  }) => {\n    var _rootProps$slotProps;\n    const {\n      onRowRender,\n      renderContext: nextRenderContext,\n      minFirstColumn = renderZoneMinColumnIndex,\n      maxLastColumn = renderZoneMaxColumnIndex,\n      availableSpace = containerDimensions.width,\n      rowIndexOffset = 0,\n      position = 'center'\n    } = params;\n    if (!nextRenderContext || availableSpace == null) {\n      return null;\n    }\n    const rowBuffer = enabled ? rootProps.rowBuffer : 0;\n    const columnBuffer = enabled ? rootProps.columnBuffer : 0;\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rowBuffer\n    });\n    const renderedRows = [];\n    if (params.rows) {\n      params.rows.forEach(row => {\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      });\n    } else {\n      if (!currentPage.range) {\n        return null;\n      }\n      for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n        const row = currentPage.rows[i];\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    // If the selected row is not within the current range of rows being displayed,\n    // we need to render it at either the top or bottom of the rows,\n    // depending on whether it is above or below the range.\n\n    let isRowWithFocusedCellNotInRange = false;\n    if (indexOfRowWithFocusedCell > -1) {\n      const rowWithFocusedCell = currentPage.rows[indexOfRowWithFocusedCell];\n      if (firstRowToRender > indexOfRowWithFocusedCell || lastRowToRender < indexOfRowWithFocusedCell) {\n        isRowWithFocusedCellNotInRange = true;\n        if (indexOfRowWithFocusedCell > firstRowToRender) {\n          renderedRows.push(rowWithFocusedCell);\n        } else {\n          renderedRows.unshift(rowWithFocusedCell);\n        }\n        apiRef.current.calculateColSpan({\n          rowId: rowWithFocusedCell.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    const [initialFirstColumnToRender, lastColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: minFirstColumn,\n      maxLastIndex: maxLastColumn,\n      buffer: columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    let isColumnWihFocusedCellNotInRange = false;\n    if (firstColumnToRender > indexOfColumnWithFocusedCell || lastColumnToRender < indexOfColumnWithFocusedCell) {\n      isColumnWihFocusedCellNotInRange = true;\n    }\n    const {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    } = getRenderedColumnsRef.current(visibleColumns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, isColumnWihFocusedCellNotInRange ? indexOfColumnWithFocusedCell : -1);\n    const _ref = ((_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.row) || {},\n      {\n        style: rootRowStyle\n      } = _ref,\n      rootRowProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const invalidatesCachedRowStyle = prevGetRowProps.current !== getRowProps || prevRootRowStyle.current !== rootRowStyle;\n    if (invalidatesCachedRowStyle) {\n      rowStyleCache.current = Object.create(null);\n    }\n    const rows = [];\n    let isRowWithFocusedCellRendered = false;\n    for (let i = 0; i < renderedRows.length; i += 1) {\n      var _currentPage$range;\n      const {\n        id,\n        model\n      } = renderedRows[i];\n      const isRowNotVisible = isRowWithFocusedCellNotInRange && cellFocus.id === id;\n      const lastVisibleRowIndex = isRowWithFocusedCellNotInRange ? firstRowToRender + i === currentPage.rows.length : firstRowToRender + i === currentPage.rows.length - 1;\n      const baseRowHeight = !apiRef.current.rowHasAutoHeight(id) ? apiRef.current.unstable_getRowHeight(id) : 'auto';\n      let isSelected;\n      if (selectedRowsLookup[id] == null) {\n        isSelected = false;\n      } else {\n        isSelected = apiRef.current.isRowSelectable(id);\n      }\n      if (onRowRender) {\n        onRowRender(id);\n      }\n      const focusedCell = cellFocus !== null && cellFocus.id === id ? cellFocus.field : null;\n      const columnWithFocusedCellNotInRange = focusedCellColumnIndexNotInRange !== undefined && visibleColumns[focusedCellColumnIndexNotInRange];\n      const renderedColumnsWithFocusedCell = columnWithFocusedCellNotInRange && focusedCell ? [columnWithFocusedCellNotInRange, ...renderedColumns] : renderedColumns;\n      let tabbableCell = null;\n      if (cellTabIndex !== null && cellTabIndex.id === id) {\n        const cellParams = apiRef.current.getCellParams(id, cellTabIndex.field);\n        tabbableCell = cellParams.cellMode === 'view' ? cellTabIndex.field : null;\n      }\n      const _ref2 = typeof getRowProps === 'function' && getRowProps(id, model) || {},\n        {\n          style: rowStyle\n        } = _ref2,\n        rowProps = _objectWithoutPropertiesLoose(_ref2, _excluded2);\n      if (!rowStyleCache.current[id]) {\n        const style = _extends({}, rowStyle, rootRowStyle);\n        rowStyleCache.current[id] = style;\n      }\n      let index = rowIndexOffset + ((currentPage == null || (_currentPage$range = currentPage.range) == null ? void 0 : _currentPage$range.firstRowIndex) || 0) + firstRowToRender + i;\n      if (isRowWithFocusedCellNotInRange && (cellFocus == null ? void 0 : cellFocus.id) === id) {\n        index = indexOfRowWithFocusedCell;\n        isRowWithFocusedCellRendered = true;\n      } else if (isRowWithFocusedCellRendered) {\n        index -= 1;\n      }\n      rows.push( /*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n        row: model,\n        rowId: id,\n        focusedCellColumnIndexNotInRange: focusedCellColumnIndexNotInRange,\n        isNotVisible: isRowNotVisible,\n        rowHeight: baseRowHeight,\n        focusedCell: focusedCell,\n        tabbableCell: tabbableCell,\n        renderedColumns: renderedColumnsWithFocusedCell,\n        visibleColumns: visibleColumns,\n        firstColumnToRender: firstColumnToRender,\n        lastColumnToRender: lastColumnToRender,\n        selected: isSelected,\n        index: index,\n        containerWidth: availableSpace,\n        isLastVisible: lastVisibleRowIndex,\n        position: position\n      }, rowProps, rootRowProps, {\n        hovered: hoveredRowId === id,\n        style: rowStyleCache.current[id]\n      }), id));\n    }\n    prevGetRowProps.current = getRowProps;\n    prevRootRowStyle.current = rootRowStyle;\n    return rows;\n  };\n  const needsHorizontalScrollbar = containerDimensions.width && columnsTotalWidth >= containerDimensions.width;\n  const contentSize = React.useMemo(() => {\n    // In cases where the columns exceed the available width,\n    // the horizontal scrollbar should be shown even when there're no rows.\n    // Keeping 1px as minimum height ensures that the scrollbar will visible if necessary.\n    const height = Math.max(rowsMeta.currentPageTotalHeight, 1);\n    let shouldExtendContent = false;\n    if (rootRef != null && rootRef.current && height <= (rootRef == null ? void 0 : rootRef.current.clientHeight)) {\n      shouldExtendContent = true;\n    }\n    const size = {\n      width: needsHorizontalScrollbar ? columnsTotalWidth : 'auto',\n      height,\n      minHeight: shouldExtendContent ? '100%' : 'auto'\n    };\n    if (rootProps.autoHeight && currentPage.rows.length === 0) {\n      size.height = getMinimalContentHeight(apiRef, rootProps.rowHeight); // Give room to show the overlay when there no rows.\n    }\n    return size;\n  }, [apiRef, rootRef, columnsTotalWidth, rowsMeta.currentPageTotalHeight, needsHorizontalScrollbar, rootProps.autoHeight, rootProps.rowHeight, currentPage.rows.length]);\n  React.useEffect(() => {\n    apiRef.current.publishEvent('virtualScrollerContentSizeChange');\n  }, [apiRef, contentSize]);\n  const rootStyle = React.useMemo(() => {\n    const style = {};\n    if (!needsHorizontalScrollbar) {\n      style.overflowX = 'hidden';\n    }\n    if (rootProps.autoHeight) {\n      style.overflowY = 'hidden';\n    }\n    return style;\n  }, [needsHorizontalScrollbar, rootProps.autoHeight]);\n  apiRef.current.register('private', {\n    getRenderContext\n  });\n  return {\n    renderContext,\n    updateRenderZonePosition,\n    getRows,\n    getRootProps: (inputProps = {}) => _extends({\n      ref: handleRef,\n      onScroll: handleScroll,\n      onWheel: handleWheel,\n      onTouchMove: handleTouchMove\n    }, inputProps, {\n      style: inputProps.style ? _extends({}, inputProps.style, rootStyle) : rootStyle,\n      role: 'presentation'\n    }),\n    getContentProps: ({\n      style\n    } = {}) => ({\n      style: style ? _extends({}, style, contentSize) : contentSize,\n      role: 'presentation'\n    }),\n    getRenderZoneProps: () => ({\n      ref: renderZoneRef,\n      role: 'rowgroup'\n    })\n  };\n};\nfunction getNearestIndexToRender(apiRef, currentPage, rowsMeta, offset) {\n  var _currentPage$range2, _currentPage$range3;\n  const lastMeasuredIndexRelativeToAllRows = apiRef.current.getLastMeasuredRowIndex();\n  let allRowsMeasured = lastMeasuredIndexRelativeToAllRows === Infinity;\n  if ((_currentPage$range2 = currentPage.range) != null && _currentPage$range2.lastRowIndex && !allRowsMeasured) {\n    // Check if all rows in this page are already measured\n    allRowsMeasured = lastMeasuredIndexRelativeToAllRows >= currentPage.range.lastRowIndex;\n  }\n  const lastMeasuredIndexRelativeToCurrentPage = clamp(lastMeasuredIndexRelativeToAllRows - (((_currentPage$range3 = currentPage.range) == null ? void 0 : _currentPage$range3.firstRowIndex) || 0), 0, rowsMeta.positions.length);\n  if (allRowsMeasured || rowsMeta.positions[lastMeasuredIndexRelativeToCurrentPage] >= offset) {\n    // If all rows were measured (when no row has \"auto\" as height) or all rows before the offset\n    // were measured, then use a binary search because it's faster.\n    return binarySearch(offset, rowsMeta.positions);\n  }\n\n  // Otherwise, use an exponential search.\n  // If rows have \"auto\" as height, their positions will be based on estimated heights.\n  // In this case, we can skip several steps until we find a position higher than the offset.\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  return exponentialSearch(offset, rowsMeta.positions, lastMeasuredIndexRelativeToCurrentPage);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;EACzBC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AAC9J,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,oCAAoC,EAAEC,6BAA6B,EAAEC,2BAA2B,QAAQ,gCAAgC;AACjJ,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,iCAAiC;AACjG,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,yBAAyB,QAAQ,0CAA0C;AACpF,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,gCAAgC,QAAQ,6BAA6B;AAC9E,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,SAASC,iCAAiC,EAAEC,uCAAuC,QAAQ,+BAA+B;;AAE1H;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,EAAEC,QAAQ,GAAGF,SAAS,CAACG,MAAM,EAAE;EAC3F,IAAIH,SAAS,CAACG,MAAM,IAAI,CAAC,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;EACA,IAAIF,UAAU,IAAIC,QAAQ,EAAE;IAC1B,OAAOD,UAAU;EACnB;EACA,MAAMG,KAAK,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAAC,CAACJ,QAAQ,GAAGD,UAAU,IAAI,CAAC,CAAC;EAClE,MAAMM,UAAU,GAAGP,SAAS,CAACI,KAAK,CAAC;EACnC,OAAOL,MAAM,IAAIQ,UAAU,GAAGT,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEG,KAAK,CAAC,GAAGN,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEI,KAAK,GAAG,CAAC,EAAEF,QAAQ,CAAC;AACzI;AACA,SAASM,iBAAiBA,CAACT,MAAM,EAAEC,SAAS,EAAES,KAAK,EAAE;EACnD,IAAIC,QAAQ,GAAG,CAAC;EAChB,OAAOD,KAAK,GAAGT,SAAS,CAACG,MAAM,IAAIE,IAAI,CAACM,GAAG,CAACX,SAAS,CAACS,KAAK,CAAC,CAAC,GAAGV,MAAM,EAAE;IACtEU,KAAK,IAAIC,QAAQ;IACjBA,QAAQ,IAAI,CAAC;EACf;EACA,OAAOZ,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEK,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACO,GAAG,CAACH,KAAK,EAAET,SAAS,CAACG,MAAM,CAAC,CAAC;AAClG;AACA,OAAO,MAAMU,oBAAoB,GAAGA,CAAC;EACnCC,UAAU;EACVC,SAAS;EACTC,MAAM;EACNC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,OAAO,CAAC7B,KAAK,CAACyB,UAAU,GAAGE,MAAM,EAAEC,aAAa,EAAEC,YAAY,CAAC,EAAE7B,KAAK,CAAC0B,SAAS,GAAGC,MAAM,EAAEC,aAAa,EAAEC,YAAY,CAAC,CAAC;AAC1H,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;EAC5D,IAAID,QAAQ,KAAKC,QAAQ,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAOD,QAAQ,CAACE,aAAa,KAAKD,QAAQ,CAACC,aAAa,IAAIF,QAAQ,CAACG,YAAY,KAAKF,QAAQ,CAACE,YAAY,IAAIH,QAAQ,CAACI,gBAAgB,KAAKH,QAAQ,CAACG,gBAAgB,IAAIJ,QAAQ,CAACK,eAAe,KAAKJ,QAAQ,CAACI,eAAe;AACjO,CAAC;AACD;AACA;AACA,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EAC7C,MAAMC,MAAM,GAAGnD,wBAAwB,CAAC,CAAC;EACzC,MAAMoD,SAAS,GAAGnD,gBAAgB,CAAC,CAAC;EACpC,MAAMoD,cAAc,GAAGnD,eAAe,CAACiD,MAAM,EAAEhD,oCAAoC,CAAC;EACpF,MAAMmD,OAAO,GAAGpD,eAAe,CAACiD,MAAM,EAAEpC,iCAAiC,CAAC;EAC1E,MAAMwC,iBAAiB,GAAGrD,eAAe,CAACiD,MAAM,EAAEnC,uCAAuC,CAAC;EAC1F,MAAM;IACJwC,GAAG;IACHC,uBAAuB;IACvBC,wBAAwB,GAAG,CAAC;IAC5BC,wBAAwB,GAAGN,cAAc,CAAC7B,MAAM;IAChDoC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,KAAK,GAAG/D,QAAQ,CAAC,CAAC;EACxB,MAAMgE,eAAe,GAAG5D,eAAe,CAACiD,MAAM,EAAE9C,2BAA2B,CAAC;EAC5E,MAAM0D,iBAAiB,GAAG7D,eAAe,CAACiD,MAAM,EAAE/C,6BAA6B,CAAC;EAChF,MAAM4D,SAAS,GAAG9D,eAAe,CAACiD,MAAM,EAAE7C,qBAAqB,CAAC;EAChE,MAAM2D,YAAY,GAAG/D,eAAe,CAACiD,MAAM,EAAE5C,wBAAwB,CAAC;EACtE,MAAM2D,QAAQ,GAAGhE,eAAe,CAACiD,MAAM,EAAEvC,oBAAoB,CAAC;EAC9D,MAAMuD,kBAAkB,GAAGjE,eAAe,CAACiD,MAAM,EAAExC,yBAAyB,CAAC;EAC7E,MAAMyD,WAAW,GAAG5D,kBAAkB,CAAC2C,MAAM,EAAEC,SAAS,CAAC;EACzD,MAAMiB,aAAa,GAAG/E,KAAK,CAACgF,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,OAAO,GAAGjF,KAAK,CAACgF,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,SAAS,GAAG/E,UAAU,CAAC+D,GAAG,EAAEe,OAAO,CAAC;EAC1C,MAAM,CAACE,aAAa,EAAEC,qBAAqB,CAAC,GAAGpF,KAAK,CAACqF,QAAQ,CAAC,IAAI,CAAC;EACnE,MAAMC,iBAAiB,GAAGtF,KAAK,CAACgF,MAAM,CAACG,aAAa,CAAC;EACrD,MAAMI,cAAc,GAAGvF,KAAK,CAACgF,MAAM,CAAC;IAClCQ,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3F,KAAK,CAACqF,QAAQ,CAAC;IACnEO,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,cAAc,GAAG9F,KAAK,CAACgF,MAAM,CAACP,iBAAiB,CAAC;EACtD;EACA;EACA;EACA;EACA;EACA,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGhG,KAAK,CAACqF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAMY,aAAa,GAAGjG,KAAK,CAACgF,MAAM,CAACkB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACvD,MAAMC,eAAe,GAAGpG,KAAK,CAACgF,MAAM,CAAC,CAAC;EACtC,MAAMqB,gBAAgB,GAAGrG,KAAK,CAACgF,MAAM,CAAC,CAAC;EACvC,MAAMsB,qBAAqB,GAAGtG,KAAK,CAACgF,MAAM,CAACvE,cAAc,CAAC,CAAC8F,OAAO,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,4BAA4B,KAAK;IAC3K;IACA;IACA;IACA,IAAIC,gCAAgC;IACpC,MAAMC,eAAe,GAAGP,OAAO,CAACQ,KAAK,CAACP,mBAAmB,EAAEC,kBAAkB,CAAC;IAC9E,IAAIG,4BAA4B,GAAG,CAAC,CAAC,EAAE;MACrC;MACA,IAAIJ,mBAAmB,GAAGI,4BAA4B,IAAIA,4BAA4B,IAAIF,cAAc,EAAE;QACxGG,gCAAgC,GAAGD,4BAA4B;MACjE;MACA;MAAA,KACK,IAAIH,kBAAkB,GAAGG,4BAA4B,IAAIA,4BAA4B,GAAGD,aAAa,EAAE;QAC1GE,gCAAgC,GAAGD,4BAA4B;MACjE;IACF;IACA,OAAO;MACLC,gCAAgC;MAChCC;IACF,CAAC;EACH,CAAC,EAAErD,eAAe,CAAC,CAAC;EACpB,MAAMmD,4BAA4B,GAAG5G,KAAK,CAACgH,OAAO,CAAC,MAAM;IACvD,IAAItC,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOX,cAAc,CAACkD,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKzC,SAAS,CAACyC,KAAK,CAAC;IAC7E;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACzC,SAAS,EAAEX,cAAc,CAAC,CAAC;EAC/B,MAAMqD,oBAAoB,GAAGpH,KAAK,CAACqH,WAAW,CAAC,MAAM;IACnD,IAAI,CAACrD,OAAO,EAAE;MACZ,OAAO;QACLX,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAEwB,WAAW,CAACwC,IAAI,CAACpF,MAAM;QACrCqB,gBAAgB,EAAE,CAAC;QACnBC,eAAe,EAAEO,cAAc,CAAC7B;MAClC,CAAC;IACH;IACA,MAAM;MACJsD,GAAG;MACHC;IACF,CAAC,GAAGF,cAAc,CAACgC,OAAO;;IAE1B;IACA;IACA,MAAMlE,aAAa,GAAGjB,IAAI,CAACO,GAAG,CAAC6E,uBAAuB,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAEY,GAAG,CAAC,EAAEZ,QAAQ,CAAC7C,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC;IAC1H,MAAMoB,YAAY,GAAGQ,SAAS,CAAC2D,UAAU,GAAGpE,aAAa,GAAGyB,WAAW,CAACwC,IAAI,CAACpF,MAAM,GAAGsF,uBAAuB,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAEY,GAAG,GAAGE,mBAAmB,CAACG,MAAM,CAAC;IAC9K,IAAItC,gBAAgB,GAAG,CAAC;IACxB,IAAIC,eAAe,GAAGgB,eAAe,CAACtC,MAAM;IAC5C,IAAI+B,iBAAiB,EAAE;MACrB,IAAIyD,oBAAoB,GAAG,KAAK;MAChC,MAAM,CAACC,gBAAgB,EAAEC,eAAe,CAAC,GAAGhF,oBAAoB,CAAC;QAC/DC,UAAU,EAAEQ,aAAa;QACzBP,SAAS,EAAEQ,YAAY;QACvBN,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACpF,MAAM;QACrCa,MAAM,EAAEe,SAAS,CAAC+D;MACpB,CAAC,CAAC;MACF,KAAK,IAAIC,CAAC,GAAGH,gBAAgB,EAAEG,CAAC,GAAGF,eAAe,IAAI,CAACF,oBAAoB,EAAEI,CAAC,IAAI,CAAC,EAAE;QACnF,MAAMC,GAAG,GAAGjD,WAAW,CAACwC,IAAI,CAACQ,CAAC,CAAC;QAC/BJ,oBAAoB,GAAG7D,MAAM,CAAC0D,OAAO,CAACS,gBAAgB,CAACD,GAAG,CAACE,EAAE,CAAC;MAChE;MACA,IAAI,CAACP,oBAAoB,EAAE;QACzBnE,gBAAgB,GAAG1B,YAAY,CAACO,IAAI,CAACM,GAAG,CAAC+C,IAAI,CAAC,EAAEjB,eAAe,CAAC;QAChEhB,eAAe,GAAG3B,YAAY,CAACO,IAAI,CAACM,GAAG,CAAC+C,IAAI,CAAC,GAAGC,mBAAmB,CAACE,KAAK,EAAEpB,eAAe,CAAC;MAC7F;IACF;IACA,OAAO;MACLnB,aAAa;MACbC,YAAY;MACZC,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC,EAAE,CAACQ,OAAO,EAAEC,iBAAiB,EAAEW,QAAQ,EAAEd,SAAS,CAAC2D,UAAU,EAAE3D,SAAS,CAAC+D,SAAS,EAAE/C,WAAW,EAAEN,eAAe,EAAET,cAAc,CAAC7B,MAAM,EAAE2B,MAAM,EAAE6B,mBAAmB,CAAC,CAAC;EACvKrF,iBAAiB,CAAC,MAAM;IACtB,IAAI2D,OAAO,EAAE;MACX;MACAiB,OAAO,CAACsC,OAAO,CAACW,UAAU,GAAG,CAAC;MAC9BjD,OAAO,CAACsC,OAAO,CAACY,SAAS,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLpD,aAAa,CAACwC,OAAO,CAACa,KAAK,CAACC,SAAS,GAAG,4BAA4B;IACtE;EACF,CAAC,EAAE,CAACrE,OAAO,CAAC,CAAC;EACb3D,iBAAiB,CAAC,MAAM;IACtBsF,sBAAsB,CAAC;MACrBC,KAAK,EAAEX,OAAO,CAACsC,OAAO,CAACe,WAAW;MAClCzC,MAAM,EAAEZ,OAAO,CAACsC,OAAO,CAACgB;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3D,QAAQ,CAAC4D,sBAAsB,CAAC,CAAC;EACrC,MAAMC,YAAY,GAAGzI,KAAK,CAACqH,WAAW,CAAC,MAAM;IAC3C,IAAIpC,OAAO,CAACsC,OAAO,EAAE;MACnB5B,sBAAsB,CAAC;QACrBC,KAAK,EAAEX,OAAO,CAACsC,OAAO,CAACe,WAAW;QAClCzC,MAAM,EAAEZ,OAAO,CAACsC,OAAO,CAACgB;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EACNpH,sBAAsB,CAAC0C,MAAM,EAAE,iBAAiB,EAAE4E,YAAY,CAAC;EAC/D,MAAMC,wBAAwB,GAAG1I,KAAK,CAACqH,WAAW,CAACsB,iBAAiB,IAAI;IACtE,MAAM,CAAChB,gBAAgB,EAAEC,eAAe,CAAC,GAAGhF,oBAAoB,CAAC;MAC/DC,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACpF,MAAM;MACrCa,MAAM,EAAEe,SAAS,CAAC+D;IACpB,CAAC,CAAC;IACF,MAAM,CAACe,0BAA0B,CAAC,GAAGhG,oBAAoB,CAAC;MACxDC,UAAU,EAAE8F,iBAAiB,CAACpF,gBAAgB;MAC9CT,SAAS,EAAE6F,iBAAiB,CAACnF,eAAe;MAC5CR,aAAa,EAAEoB,wBAAwB;MACvCnB,YAAY,EAAEoB,wBAAwB;MACtCtB,MAAM,EAAEe,SAAS,CAAC+E;IACpB,CAAC,CAAC;IACF,MAAMrC,mBAAmB,GAAGjF,gCAAgC,CAAC;MAC3DiF,mBAAmB,EAAEoC,0BAA0B;MAC/C/E,MAAM;MACN8D,gBAAgB;MAChBC,eAAe;MACfkB,WAAW,EAAEhE,WAAW,CAACwC;IAC3B,CAAC,CAAC;IACF,MAAMyB,SAAS,GAAGxE,KAAK,CAACwE,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,MAAMvD,GAAG,GAAGlE,oBAAoB,CAACuC,MAAM,CAAC0D,OAAO,CAACyB,KAAK,CAAC,CAACjH,SAAS,CAAC4F,gBAAgB,CAAC;IAClF,MAAMlC,IAAI,GAAGsD,SAAS,GAAGhI,2BAA2B,CAAC8C,MAAM,CAAC,CAAC2C,mBAAmB,CAAC,CAAC,CAAC;IACnFzB,aAAa,CAACwC,OAAO,CAACa,KAAK,CAACC,SAAS,GAAG,eAAe5C,IAAI,OAAOD,GAAG,UAAU;IAC/E,IAAI,OAAOrB,uBAAuB,KAAK,UAAU,EAAE;MACjDA,uBAAuB,CAAC;QACtBqB,GAAG;QACHC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEiB,WAAW,CAACwC,IAAI,EAAEnD,uBAAuB,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEP,SAAS,CAAC+E,YAAY,EAAE/E,SAAS,CAAC+D,SAAS,EAAEtD,KAAK,CAACwE,SAAS,CAAC,CAAC;EACzK,MAAME,gBAAgB,GAAGjJ,KAAK,CAACqH,WAAW,CAAC,MAAM/B,iBAAiB,CAACiC,OAAO,EAAE,EAAE,CAAC;EAC/E,MAAM2B,gBAAgB,GAAGlJ,KAAK,CAACqH,WAAW,CAACsB,iBAAiB,IAAI;IAC9D,IAAIrD,iBAAiB,CAACiC,OAAO,IAAIrE,sBAAsB,CAACyF,iBAAiB,EAAErD,iBAAiB,CAACiC,OAAO,CAAC,EAAE;MACrGmB,wBAAwB,CAACC,iBAAiB,CAAC;MAC3C;IACF;IACAvD,qBAAqB,CAACuD,iBAAiB,CAAC;IACxCD,wBAAwB,CAACC,iBAAiB,CAAC;IAC3C,MAAM,CAAChB,gBAAgB,EAAEC,eAAe,CAAC,GAAGhF,oBAAoB,CAAC;MAC/DC,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACpF,MAAM;MACrCa,MAAM,EAAEe,SAAS,CAAC+D;IACpB,CAAC,CAAC;IACFhE,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,4BAA4B,EAAE;MACxDxB,gBAAgB;MAChBC;IACF,CAAC,CAAC;IACFtC,iBAAiB,CAACiC,OAAO,GAAGoB,iBAAiB;EAC/C,CAAC,EAAE,CAAC9E,MAAM,EAAEuB,qBAAqB,EAAEE,iBAAiB,EAAER,WAAW,CAACwC,IAAI,CAACpF,MAAM,EAAE4B,SAAS,CAAC+D,SAAS,EAAEa,wBAAwB,CAAC,CAAC;EAC9HrI,iBAAiB,CAAC,MAAM;IACtB,IAAIqF,mBAAmB,CAACE,KAAK,IAAI,IAAI,EAAE;MACrC;IACF;IACA,MAAMwD,oBAAoB,GAAGhC,oBAAoB,CAAC,CAAC;IACnD8B,gBAAgB,CAACE,oBAAoB,CAAC;IACtC,MAAM;MACJ5D,GAAG;MACHC;IACF,CAAC,GAAGF,cAAc,CAACgC,OAAO;IAC1B,MAAM8B,MAAM,GAAG;MACb7D,GAAG;MACHC,IAAI;MACJN,aAAa,EAAEiE;IACjB,CAAC;IACDvF,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,sBAAsB,EAAEE,MAAM,CAAC;EAC7D,CAAC,EAAE,CAACxF,MAAM,EAAEuD,oBAAoB,EAAE1B,mBAAmB,CAACE,KAAK,EAAEsD,gBAAgB,CAAC,CAAC;EAC/E,MAAMI,YAAY,GAAG/I,gBAAgB,CAACgJ,KAAK,IAAI;IAC7C,MAAM;MACJpB,SAAS;MACTD;IACF,CAAC,GAAGqB,KAAK,CAACC,aAAa;IACvBjE,cAAc,CAACgC,OAAO,CAAC/B,GAAG,GAAG2C,SAAS;IACtC5C,cAAc,CAACgC,OAAO,CAAC9B,IAAI,GAAGyC,UAAU;;IAExC;IACA,IAAI,CAAC5C,iBAAiB,CAACiC,OAAO,IAAIY,SAAS,GAAG,CAAC,EAAE;MAC/C;IACF;IACA,IAAI5D,KAAK,CAACwE,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAIb,UAAU,GAAG,CAAC,EAAE;QAClB;MACF;IACF;IACA,IAAI3D,KAAK,CAACwE,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAIb,UAAU,GAAG,CAAC,EAAE;QAClB;MACF;IACF;;IAEA;IACA,MAAMS,iBAAiB,GAAG3E,OAAO,GAAGoD,oBAAoB,CAAC,CAAC,GAAG9B,iBAAiB,CAACiC,OAAO;IACtF,MAAMkC,kCAAkC,GAAGrH,IAAI,CAACM,GAAG,CAACiG,iBAAiB,CAACtF,aAAa,GAAGiC,iBAAiB,CAACiC,OAAO,CAAClE,aAAa,CAAC;IAC9H,MAAMqG,qCAAqC,GAAGtH,IAAI,CAACM,GAAG,CAACiG,iBAAiB,CAACrF,YAAY,GAAGgC,iBAAiB,CAACiC,OAAO,CAACjE,YAAY,CAAC;IAC/H,MAAMqG,qCAAqC,GAAGvH,IAAI,CAACM,GAAG,CAACiG,iBAAiB,CAACpF,gBAAgB,GAAG+B,iBAAiB,CAACiC,OAAO,CAAChE,gBAAgB,CAAC;IACvI,MAAMqG,wCAAwC,GAAGxH,IAAI,CAACM,GAAG,CAACiG,iBAAiB,CAACnF,eAAe,GAAG8B,iBAAiB,CAACiC,OAAO,CAAC/D,eAAe,CAAC;IACxI,MAAMqG,cAAc,GAAGJ,kCAAkC,IAAI3F,SAAS,CAACgG,YAAY,IAAIJ,qCAAqC,IAAI5F,SAAS,CAACgG,YAAY,IAAIH,qCAAqC,IAAI7F,SAAS,CAACiG,eAAe,IAAIH,wCAAwC,IAAI9F,SAAS,CAACiG,eAAe,IAAIjE,cAAc,CAACyB,OAAO,KAAK9C,iBAAiB;IACrVZ,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,sBAAsB,EAAE;MAClD3D,GAAG,EAAE2C,SAAS;MACd1C,IAAI,EAAEyC,UAAU;MAChB/C,aAAa,EAAE0E,cAAc,GAAGlB,iBAAiB,GAAGrD,iBAAiB,CAACiC;IACxE,CAAC,EAAEgC,KAAK,CAAC;IACT,IAAIM,cAAc,EAAE;MAClB;MACA5J,QAAQ,CAAC+J,SAAS,CAAC,MAAM;QACvBd,gBAAgB,CAACP,iBAAiB,CAAC;MACrC,CAAC,CAAC;MACF7C,cAAc,CAACyB,OAAO,GAAG9C,iBAAiB;IAC5C;EACF,CAAC,CAAC;EACF,MAAMwF,WAAW,GAAG1J,gBAAgB,CAACgJ,KAAK,IAAI;IAC5C1F,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAEI,KAAK,CAAC;EAChE,CAAC,CAAC;EACF,MAAMW,eAAe,GAAG3J,gBAAgB,CAACgJ,KAAK,IAAI;IAChD1F,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAEI,KAAK,CAAC;EACpE,CAAC,CAAC;EACF,MAAMY,yBAAyB,GAAGnK,KAAK,CAACgH,OAAO,CAAC,MAAM;IACpD,IAAItC,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOI,WAAW,CAACwC,IAAI,CAACL,SAAS,CAACc,GAAG,IAAIA,GAAG,CAACE,EAAE,KAAKvD,SAAS,CAACuD,EAAE,CAAC;IACnE;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACvD,SAAS,EAAEI,WAAW,CAACwC,IAAI,CAAC,CAAC;EACjCnG,sBAAsB,CAAC0C,MAAM,EAAE,cAAc,EAAE,CAACwF,MAAM,EAAEE,KAAK,KAAK;IAChE,IAAIa,UAAU;IACd,IAAIb,KAAK,CAACC,aAAa,CAACa,QAAQ,CAACd,KAAK,CAACe,aAAa,CAAC,EAAE;MACrD;IACF;IACAtE,eAAe,CAAC,CAACoE,UAAU,GAAGf,MAAM,CAACpB,EAAE,KAAK,IAAI,GAAGmC,UAAU,GAAG,IAAI,CAAC;EACvE,CAAC,CAAC;EACFjJ,sBAAsB,CAAC0C,MAAM,EAAE,aAAa,EAAE,CAACwF,MAAM,EAAEE,KAAK,KAAK;IAC/D,IAAIA,KAAK,CAACC,aAAa,CAACa,QAAQ,CAACd,KAAK,CAACe,aAAa,CAAC,EAAE;MACrD;IACF;IACAtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EACF,MAAMuE,OAAO,GAAGA,CAAClB,MAAM,GAAG;IACxBlE;EACF,CAAC,KAAK;IACJ,IAAIqF,oBAAoB;IACxB,MAAM;MACJC,WAAW;MACXtF,aAAa,EAAEwD,iBAAiB;MAChCjC,cAAc,GAAGtC,wBAAwB;MACzCuC,aAAa,GAAGtC,wBAAwB;MACxCqG,cAAc,GAAGhF,mBAAmB,CAACE,KAAK;MAC1C+E,cAAc,GAAG,CAAC;MAClBC,QAAQ,GAAG;IACb,CAAC,GAAGvB,MAAM;IACV,IAAI,CAACV,iBAAiB,IAAI+B,cAAc,IAAI,IAAI,EAAE;MAChD,OAAO,IAAI;IACb;IACA,MAAM7C,SAAS,GAAG7D,OAAO,GAAGF,SAAS,CAAC+D,SAAS,GAAG,CAAC;IACnD,MAAMgB,YAAY,GAAG7E,OAAO,GAAGF,SAAS,CAAC+E,YAAY,GAAG,CAAC;IACzD,MAAM,CAAClB,gBAAgB,EAAEC,eAAe,CAAC,GAAGhF,oBAAoB,CAAC;MAC/DC,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACpF,MAAM;MACrCa,MAAM,EAAE8E;IACV,CAAC,CAAC;IACF,MAAMgD,YAAY,GAAG,EAAE;IACvB,IAAIxB,MAAM,CAAC/B,IAAI,EAAE;MACf+B,MAAM,CAAC/B,IAAI,CAACwD,OAAO,CAAC/C,GAAG,IAAI;QACzB8C,YAAY,CAACE,IAAI,CAAChD,GAAG,CAAC;QACtBlE,MAAM,CAAC0D,OAAO,CAACyD,gBAAgB,CAAC;UAC9BC,KAAK,EAAElD,GAAG,CAACE,EAAE;UACbvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACe,WAAW,CAACoG,KAAK,EAAE;QACtB,OAAO,IAAI;MACb;MACA,KAAK,IAAIpD,CAAC,GAAGH,gBAAgB,EAAEG,CAAC,GAAGF,eAAe,EAAEE,CAAC,IAAI,CAAC,EAAE;QAC1D,MAAMC,GAAG,GAAGjD,WAAW,CAACwC,IAAI,CAACQ,CAAC,CAAC;QAC/B+C,YAAY,CAACE,IAAI,CAAChD,GAAG,CAAC;QACtBlE,MAAM,CAAC0D,OAAO,CAACyD,gBAAgB,CAAC;UAC9BC,KAAK,EAAElD,GAAG,CAACE,EAAE;UACbvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ;IACF;IACA;IACA;IACA;;IAEA,IAAIoH,8BAA8B,GAAG,KAAK;IAC1C,IAAIhB,yBAAyB,GAAG,CAAC,CAAC,EAAE;MAClC,MAAMiB,kBAAkB,GAAGtG,WAAW,CAACwC,IAAI,CAAC6C,yBAAyB,CAAC;MACtE,IAAIxC,gBAAgB,GAAGwC,yBAAyB,IAAIvC,eAAe,GAAGuC,yBAAyB,EAAE;QAC/FgB,8BAA8B,GAAG,IAAI;QACrC,IAAIhB,yBAAyB,GAAGxC,gBAAgB,EAAE;UAChDkD,YAAY,CAACE,IAAI,CAACK,kBAAkB,CAAC;QACvC,CAAC,MAAM;UACLP,YAAY,CAACQ,OAAO,CAACD,kBAAkB,CAAC;QAC1C;QACAvH,MAAM,CAAC0D,OAAO,CAACyD,gBAAgB,CAAC;UAC9BC,KAAK,EAAEG,kBAAkB,CAACnD,EAAE;UAC5BvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ;IACF;IACA,MAAM,CAAC6E,0BAA0B,EAAEnC,kBAAkB,CAAC,GAAG7D,oBAAoB,CAAC;MAC5EC,UAAU,EAAE8F,iBAAiB,CAACpF,gBAAgB;MAC9CT,SAAS,EAAE6F,iBAAiB,CAACnF,eAAe;MAC5CR,aAAa,EAAE0D,cAAc;MAC7BzD,YAAY,EAAE0D,aAAa;MAC3B5D,MAAM,EAAE8F;IACV,CAAC,CAAC;IACF,MAAMrC,mBAAmB,GAAGjF,gCAAgC,CAAC;MAC3DiF,mBAAmB,EAAEoC,0BAA0B;MAC/C/E,MAAM;MACN8D,gBAAgB;MAChBC,eAAe;MACfkB,WAAW,EAAEhE,WAAW,CAACwC;IAC3B,CAAC,CAAC;IACF,IAAIgE,gCAAgC,GAAG,KAAK;IAC5C,IAAI9E,mBAAmB,GAAGI,4BAA4B,IAAIH,kBAAkB,GAAGG,4BAA4B,EAAE;MAC3G0E,gCAAgC,GAAG,IAAI;IACzC;IACA,MAAM;MACJzE,gCAAgC;MAChCC;IACF,CAAC,GAAGR,qBAAqB,CAACiB,OAAO,CAACxD,cAAc,EAAEyC,mBAAmB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAE2E,gCAAgC,GAAG1E,4BAA4B,GAAG,CAAC,CAAC,CAAC;IAC/L,MAAM2E,IAAI,GAAG,CAAC,CAACf,oBAAoB,GAAG1G,SAAS,CAAC0H,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhB,oBAAoB,CAACzC,GAAG,KAAK,CAAC,CAAC;MAC3G;QACEK,KAAK,EAAEqD;MACT,CAAC,GAAGF,IAAI;MACRG,YAAY,GAAG7L,6BAA6B,CAAC0L,IAAI,EAAEzL,SAAS,CAAC;IAC/D,MAAM6L,yBAAyB,GAAGvF,eAAe,CAACmB,OAAO,KAAKjD,WAAW,IAAI+B,gBAAgB,CAACkB,OAAO,KAAKkE,YAAY;IACtH,IAAIE,yBAAyB,EAAE;MAC7B1F,aAAa,CAACsB,OAAO,GAAGrB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7C;IACA,MAAMmB,IAAI,GAAG,EAAE;IACf,IAAIsE,4BAA4B,GAAG,KAAK;IACxC,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,YAAY,CAAC3I,MAAM,EAAE4F,CAAC,IAAI,CAAC,EAAE;MAC/C,IAAI+D,kBAAkB;MACtB,MAAM;QACJ5D,EAAE;QACF6D;MACF,CAAC,GAAGjB,YAAY,CAAC/C,CAAC,CAAC;MACnB,MAAMiE,eAAe,GAAGZ,8BAA8B,IAAIzG,SAAS,CAACuD,EAAE,KAAKA,EAAE;MAC7E,MAAM+D,mBAAmB,GAAGb,8BAA8B,GAAGxD,gBAAgB,GAAGG,CAAC,KAAKhD,WAAW,CAACwC,IAAI,CAACpF,MAAM,GAAGyF,gBAAgB,GAAGG,CAAC,KAAKhD,WAAW,CAACwC,IAAI,CAACpF,MAAM,GAAG,CAAC;MACpK,MAAM+J,aAAa,GAAG,CAACpI,MAAM,CAAC0D,OAAO,CAACS,gBAAgB,CAACC,EAAE,CAAC,GAAGpE,MAAM,CAAC0D,OAAO,CAAC2E,qBAAqB,CAACjE,EAAE,CAAC,GAAG,MAAM;MAC9G,IAAIkE,UAAU;MACd,IAAItH,kBAAkB,CAACoD,EAAE,CAAC,IAAI,IAAI,EAAE;QAClCkE,UAAU,GAAG,KAAK;MACpB,CAAC,MAAM;QACLA,UAAU,GAAGtI,MAAM,CAAC0D,OAAO,CAAC6E,eAAe,CAACnE,EAAE,CAAC;MACjD;MACA,IAAIwC,WAAW,EAAE;QACfA,WAAW,CAACxC,EAAE,CAAC;MACjB;MACA,MAAMoE,WAAW,GAAG3H,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACuD,EAAE,KAAKA,EAAE,GAAGvD,SAAS,CAACyC,KAAK,GAAG,IAAI;MACtF,MAAMmF,+BAA+B,GAAGzF,gCAAgC,KAAK0F,SAAS,IAAIxI,cAAc,CAAC8C,gCAAgC,CAAC;MAC1I,MAAM2F,8BAA8B,GAAGF,+BAA+B,IAAID,WAAW,GAAG,CAACC,+BAA+B,EAAE,GAAGxF,eAAe,CAAC,GAAGA,eAAe;MAC/J,IAAI2F,YAAY,GAAG,IAAI;MACvB,IAAI9H,YAAY,KAAK,IAAI,IAAIA,YAAY,CAACsD,EAAE,KAAKA,EAAE,EAAE;QACnD,MAAMyE,UAAU,GAAG7I,MAAM,CAAC0D,OAAO,CAACoF,aAAa,CAAC1E,EAAE,EAAEtD,YAAY,CAACwC,KAAK,CAAC;QACvEsF,YAAY,GAAGC,UAAU,CAACE,QAAQ,KAAK,MAAM,GAAGjI,YAAY,CAACwC,KAAK,GAAG,IAAI;MAC3E;MACA,MAAM0F,KAAK,GAAG,OAAOvI,WAAW,KAAK,UAAU,IAAIA,WAAW,CAAC2D,EAAE,EAAE6D,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7E;UACE1D,KAAK,EAAE0E;QACT,CAAC,GAAGD,KAAK;QACTE,QAAQ,GAAGlN,6BAA6B,CAACgN,KAAK,EAAE9M,UAAU,CAAC;MAC7D,IAAI,CAACkG,aAAa,CAACsB,OAAO,CAACU,EAAE,CAAC,EAAE;QAC9B,MAAMG,KAAK,GAAGxI,QAAQ,CAAC,CAAC,CAAC,EAAEkN,QAAQ,EAAErB,YAAY,CAAC;QAClDxF,aAAa,CAACsB,OAAO,CAACU,EAAE,CAAC,GAAGG,KAAK;MACnC;MACA,IAAI5F,KAAK,GAAGmI,cAAc,IAAI,CAAC7F,WAAW,IAAI,IAAI,IAAI,CAAC+G,kBAAkB,GAAG/G,WAAW,CAACoG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,kBAAkB,CAACxI,aAAa,KAAK,CAAC,CAAC,GAAGsE,gBAAgB,GAAGG,CAAC;MAChL,IAAIqD,8BAA8B,IAAI,CAACzG,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACuD,EAAE,MAAMA,EAAE,EAAE;QACxFzF,KAAK,GAAG2H,yBAAyB;QACjCyB,4BAA4B,GAAG,IAAI;MACrC,CAAC,MAAM,IAAIA,4BAA4B,EAAE;QACvCpJ,KAAK,IAAI,CAAC;MACZ;MACA8E,IAAI,CAACyD,IAAI,CAAE,aAAanJ,IAAI,CAACkC,SAAS,CAACkJ,KAAK,CAACjF,GAAG,EAAEnI,QAAQ,CAAC;QACzDmI,GAAG,EAAE+D,KAAK;QACVb,KAAK,EAAEhD,EAAE;QACTpB,gCAAgC,EAAEA,gCAAgC;QAClEoG,YAAY,EAAElB,eAAe;QAC7BmB,SAAS,EAAEjB,aAAa;QACxBI,WAAW,EAAEA,WAAW;QACxBI,YAAY,EAAEA,YAAY;QAC1B3F,eAAe,EAAE0F,8BAA8B;QAC/CzI,cAAc,EAAEA,cAAc;QAC9ByC,mBAAmB,EAAEA,mBAAmB;QACxCC,kBAAkB,EAAEA,kBAAkB;QACtC0G,QAAQ,EAAEhB,UAAU;QACpB3J,KAAK,EAAEA,KAAK;QACZ4K,cAAc,EAAE1C,cAAc;QAC9B2C,aAAa,EAAErB,mBAAmB;QAClCpB,QAAQ,EAAEA;MACZ,CAAC,EAAEmC,QAAQ,EAAErB,YAAY,EAAE;QACzB4B,OAAO,EAAEvH,YAAY,KAAKkC,EAAE;QAC5BG,KAAK,EAAEnC,aAAa,CAACsB,OAAO,CAACU,EAAE;MACjC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC;IACV;IACA7B,eAAe,CAACmB,OAAO,GAAGjD,WAAW;IACrC+B,gBAAgB,CAACkB,OAAO,GAAGkE,YAAY;IACvC,OAAOnE,IAAI;EACb,CAAC;EACD,MAAMiG,wBAAwB,GAAG7H,mBAAmB,CAACE,KAAK,IAAInB,iBAAiB,IAAIiB,mBAAmB,CAACE,KAAK;EAC5G,MAAM4H,WAAW,GAAGxN,KAAK,CAACgH,OAAO,CAAC,MAAM;IACtC;IACA;IACA;IACA,MAAMnB,MAAM,GAAGzD,IAAI,CAACqL,GAAG,CAAC7I,QAAQ,CAAC4D,sBAAsB,EAAE,CAAC,CAAC;IAC3D,IAAIkF,mBAAmB,GAAG,KAAK;IAC/B,IAAIzI,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACsC,OAAO,IAAI1B,MAAM,KAAKZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsC,OAAO,CAACgB,YAAY,CAAC,EAAE;MAC7GmF,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAMC,IAAI,GAAG;MACX/H,KAAK,EAAE2H,wBAAwB,GAAG9I,iBAAiB,GAAG,MAAM;MAC5DoB,MAAM;MACN+H,SAAS,EAAEF,mBAAmB,GAAG,MAAM,GAAG;IAC5C,CAAC;IACD,IAAI5J,SAAS,CAAC2D,UAAU,IAAI3C,WAAW,CAACwC,IAAI,CAACpF,MAAM,KAAK,CAAC,EAAE;MACzDyL,IAAI,CAAC9H,MAAM,GAAGrE,uBAAuB,CAACqC,MAAM,EAAEC,SAAS,CAACoJ,SAAS,CAAC,CAAC,CAAC;IACtE;IACA,OAAOS,IAAI;EACb,CAAC,EAAE,CAAC9J,MAAM,EAAEoB,OAAO,EAAER,iBAAiB,EAAEG,QAAQ,CAAC4D,sBAAsB,EAAE+E,wBAAwB,EAAEzJ,SAAS,CAAC2D,UAAU,EAAE3D,SAAS,CAACoJ,SAAS,EAAEpI,WAAW,CAACwC,IAAI,CAACpF,MAAM,CAAC,CAAC;EACvKlC,KAAK,CAAC6N,SAAS,CAAC,MAAM;IACpBhK,MAAM,CAAC0D,OAAO,CAAC4B,YAAY,CAAC,kCAAkC,CAAC;EACjE,CAAC,EAAE,CAACtF,MAAM,EAAE2J,WAAW,CAAC,CAAC;EACzB,MAAMM,SAAS,GAAG9N,KAAK,CAACgH,OAAO,CAAC,MAAM;IACpC,MAAMoB,KAAK,GAAG,CAAC,CAAC;IAChB,IAAI,CAACmF,wBAAwB,EAAE;MAC7BnF,KAAK,CAAC2F,SAAS,GAAG,QAAQ;IAC5B;IACA,IAAIjK,SAAS,CAAC2D,UAAU,EAAE;MACxBW,KAAK,CAAC4F,SAAS,GAAG,QAAQ;IAC5B;IACA,OAAO5F,KAAK;EACd,CAAC,EAAE,CAACmF,wBAAwB,EAAEzJ,SAAS,CAAC2D,UAAU,CAAC,CAAC;EACpD5D,MAAM,CAAC0D,OAAO,CAAC0G,QAAQ,CAAC,SAAS,EAAE;IACjChF;EACF,CAAC,CAAC;EACF,OAAO;IACL9D,aAAa;IACbuD,wBAAwB;IACxB6B,OAAO;IACP2D,YAAY,EAAEA,CAACC,UAAU,GAAG,CAAC,CAAC,KAAKvO,QAAQ,CAAC;MAC1CsE,GAAG,EAAEgB,SAAS;MACdkJ,QAAQ,EAAE9E,YAAY;MACtB+E,OAAO,EAAEpE,WAAW;MACpBqE,WAAW,EAAEpE;IACf,CAAC,EAAEiE,UAAU,EAAE;MACb/F,KAAK,EAAE+F,UAAU,CAAC/F,KAAK,GAAGxI,QAAQ,CAAC,CAAC,CAAC,EAAEuO,UAAU,CAAC/F,KAAK,EAAE0F,SAAS,CAAC,GAAGA,SAAS;MAC/ES,IAAI,EAAE;IACR,CAAC,CAAC;IACFC,eAAe,EAAEA,CAAC;MAChBpG;IACF,CAAC,GAAG,CAAC,CAAC,MAAM;MACVA,KAAK,EAAEA,KAAK,GAAGxI,QAAQ,CAAC,CAAC,CAAC,EAAEwI,KAAK,EAAEoF,WAAW,CAAC,GAAGA,WAAW;MAC7De,IAAI,EAAE;IACR,CAAC,CAAC;IACFE,kBAAkB,EAAEA,CAAA,MAAO;MACzBvK,GAAG,EAAEa,aAAa;MAClBwJ,IAAI,EAAE;IACR,CAAC;EACH,CAAC;AACH,CAAC;AACD,SAAS/G,uBAAuBA,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAE9C,MAAM,EAAE;EACtE,IAAI4M,mBAAmB,EAAEC,mBAAmB;EAC5C,MAAMC,kCAAkC,GAAG/K,MAAM,CAAC0D,OAAO,CAACsH,uBAAuB,CAAC,CAAC;EACnF,IAAIC,eAAe,GAAGF,kCAAkC,KAAKG,QAAQ;EACrE,IAAI,CAACL,mBAAmB,GAAG5J,WAAW,CAACoG,KAAK,KAAK,IAAI,IAAIwD,mBAAmB,CAACpL,YAAY,IAAI,CAACwL,eAAe,EAAE;IAC7G;IACAA,eAAe,GAAGF,kCAAkC,IAAI9J,WAAW,CAACoG,KAAK,CAAC5H,YAAY;EACxF;EACA,MAAM0L,sCAAsC,GAAG5N,KAAK,CAACwN,kCAAkC,IAAI,CAAC,CAACD,mBAAmB,GAAG7J,WAAW,CAACoG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,mBAAmB,CAACtL,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEuB,QAAQ,CAAC7C,SAAS,CAACG,MAAM,CAAC;EAChO,IAAI4M,eAAe,IAAIlK,QAAQ,CAAC7C,SAAS,CAACiN,sCAAsC,CAAC,IAAIlN,MAAM,EAAE;IAC3F;IACA;IACA,OAAOD,YAAY,CAACC,MAAM,EAAE8C,QAAQ,CAAC7C,SAAS,CAAC;EACjD;;EAEA;EACA;EACA;EACA;EACA,OAAOQ,iBAAiB,CAACT,MAAM,EAAE8C,QAAQ,CAAC7C,SAAS,EAAEiN,sCAAsC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}