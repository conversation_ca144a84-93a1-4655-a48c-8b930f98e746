{"ast": null, "code": "'use client';\n\nexport { default } from './TabScrollButton';\nexport { default as tabScrollButtonClasses } from './tabScrollButtonClasses';\nexport * from './tabScrollButtonClasses';", "map": {"version": 3, "names": ["default", "tabScrollButtonClasses"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/TabScrollButton/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TabScrollButton';\nexport { default as tabScrollButtonClasses } from './tabScrollButtonClasses';\nexport * from './tabScrollButtonClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASA,OAAO,IAAIC,sBAAsB,QAAQ,0BAA0B;AAC5E,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}