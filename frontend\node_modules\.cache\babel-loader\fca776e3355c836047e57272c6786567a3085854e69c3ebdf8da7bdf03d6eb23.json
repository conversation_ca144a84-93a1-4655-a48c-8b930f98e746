{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '点击选择备注',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleClick,\n      variant: uiState.isSelected ? 'contained' : 'outlined',\n      color: \"primary\",\n      size: \"small\",\n      sx: {\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      },\n      children: uiState.text || '点击选择'\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 1000),\n  // 1秒防抖\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 800),\n  // 0.8秒防抖\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号所有非总计行和非删除行\n      const nonTotalRows = filteredData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号所有非总计行和非删除行\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 984,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 980,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 998,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1084,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1099,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1088,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1126,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1120,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1087,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1083,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1082,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1144,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1250,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1286,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1281,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1334,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1309,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 996,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"V4X1MJy/efj1eDX57ZGG4JnFCiQ=\");\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "axios", "API_URL", "FixedSizeList", "toast", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "searchColumn", "setSearchColumn", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showToast", "message", "type", "success", "duration", "position", "style", "background", "borderRadius", "fontWeight", "padding", "boxShadow", "error", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "prev", "totalValue", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "noCounter", "for<PERSON>ach", "handleUndoRow", "nonRemovedRows", "sort", "a", "b", "handleDeleteRow", "filteredData", "nonTotalRows", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "newData", "splice", "generateDocument", "filteredRows", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "label", "opacity", "remarkText", "gap", "alignItems", "backgroundColor", "startIcon", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "toString", "cellValue", "memoGridData", "textAlign", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "endAdornment", "edge", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : '点击选择备注'} arrow placement=\"top\">\n      <Button\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        onClick={handleClick}\n        variant={uiState.isSelected ? 'contained' : 'outlined'}\n        color=\"primary\"\n        size=\"small\"\n        sx={{\n          minWidth: '150px',\n          maxWidth: '300px',\n          fontSize: '0.75rem',\n          textTransform: 'none',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          transition: 'all 0.2s ease-in-out',\n          height: 'auto',\n          lineHeight: 1.2\n        }}\n      >\n        {uiState.text || '点击选择'}\n      </Button>\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 1000), // 1秒防抖\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 800), // 0.8秒防抖\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号所有非总计行和非删除行\n      const nonTotalRows = filteredData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号所有非总计行和非删除行\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              rows={memoGridData}\n              columns={columns}\n              pageSize={100}\n              rowsPerPageOptions={[100, 200, 500]}\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAAC3B,KAAK;IAAA,GAAKuC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAG3E,KAAK,CAAC4E,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC;IAErC8E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA9E,SAAS,CAAC,MAAM;IACdiF,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC/B,OAAO;IAAC8D,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,QAAS;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eACjFlC,OAAA,CAAC/C,MAAM;MAELyE,OAAO,EAAEG,WAAY;MACrBM,OAAO,EAAER,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;MACvDW,KAAK,EAAC,SAAS;MACfC,IAAI,EAAC,OAAO;MACZC,EAAE,EAAE;QACFC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,SAAS;QACnBC,aAAa,EAAE,MAAM;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,sBAAsB;QAClCC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,EAEDP,OAAO,CAACH,IAAI,IAAI;IAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC,kCAAC;AAACgC,GAAA,GA9CG9B,UAAU;AAgDhB,MAAM+B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtH,QAAQ,CAAC,MAAM;IACzD,MAAMuH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGvD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC8H,eAAe,EAAEC,kBAAkB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACkI,UAAU,EAAEC,aAAa,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoI,YAAY,EAAEC,eAAe,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACsI,aAAa,EAAEC,gBAAgB,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwI,UAAU,EAAEC,aAAa,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC0I,YAAY,EAAEC,eAAe,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACduH,YAAY,CAACoB,OAAO,CAAC,gBAAgB,EAAElB,IAAI,CAACmB,SAAS,CAACxB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMyB,aAAa,GAAG,CAACrC,IAAI,IAAI,EAAE,EAAEsC,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrJ,QAAQ,CAAC,MAAM;IAC7CsJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C1C,aAAa,GAAG,IAAIA,aAAa,CAAC2C,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI3C,aAAa,IAAIA,aAAa,CAAC2C,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG5C,aAAa,CAACkC,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG7J,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM8J,iBAAiB,GAAG9J,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM+J,gBAAgB,GAAG/J,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMgK,mBAAmB,GAAGhK,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMiK,UAAU,GAAI3D,IAAI,IAAKA,IAAI,CAACsC,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACApK,SAAS,CAAC,MAAM;IACd,IAAI6G,YAAY,IAAIsC,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAG9C,IAAI,CAACmB,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5B5G,YAAY,CAACoG,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAG3G,UAAU,CAAC,MAAM;UAC1CiG,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCxD,YAAY,CAAC,CAAC,GAAGsC,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5B5G,YAAY,CAACoG,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAEtC,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5K,QAAQ,CAAC;IACjD6K,IAAI,EAAE,KAAK;IACXhG,KAAK,EAAE,IAAI;IACXiG,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA7K,SAAS,CAAC,MAAM;IACd,IAAIyI,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMqC,SAAS,GAAG7K,WAAW,CAAC,CAAC8K,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC3D,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ7H,KAAK,CAAC8H,OAAO,CAACF,OAAO,EAAE;UACrBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,OAAO;QACVtI,KAAK,CAACuI,KAAK,CAACX,OAAO,EAAE;UACnBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,MAAM;QACTtI,KAAK,CAAC4H,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZtI,KAAK,CAAC4H,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF;QACEtI,KAAK,CAAC4H,OAAO,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAIlF,MAAM,IAAIA,MAAM,CAACmF,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7CjF,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAMkF,WAAW,GAAG,GAAG5I,OAAO,aAAawD,MAAM,EAAE;MACnD,MAAMqF,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAI5B,IAAI,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhB,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC;IACxC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/E,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM6F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMxJ,KAAK,CAACyJ,MAAM,CAAC,GAAGxJ,OAAO,YAAYwD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOiF,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAhF,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMgG,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8C,kBAAkB,GAAG3M,WAAW,CAAC,CAACuG,IAAI,EAAEqG,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGtG,IAAI,IAAI2C,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC4D,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAK;MACpB,IAAI8D,UAAU,IAAI9D,GAAG,CAACa,EAAE,KAAKiD,UAAU,CAACjD,EAAE,EAAE;QAC1C,OAAOuD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACzC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO+C,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkE,gBAAgB,GAAGpN,WAAW,CAAEuG,IAAI,IAAK;IAC7C,MAAM8G,QAAQ,GAAG9G,IAAI,CAAC+G,IAAI,CAACxE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIwD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGhH,IAAI,CAClByG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DkD,QAAQ,CAAClD,UAAU,GAAGoD,QAAQ;IAChC;IACA,OAAOhH,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiH,2BAA2B,GAAGxN,WAAW,CAC7CqD,QAAQ,CAAEkD,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAACoB,OAAO,CAAC,eAAe,EAAElB,IAAI,CAACmB,SAAS,CAACpC,IAAI,CAAC,CAAC;MAC3D6C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9C,IAAI,CAAC+C,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGzN,WAAW,CACvCqD,QAAQ,CAAEkD,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvB6C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACzC,YAAY,CACf,CAAC;;EAED;EACA,MAAM8G,gBAAgB,GAAG1N,WAAW,CAAE2N,MAAM,IAAK;IAC/CxE,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGlB,kBAAkB,CAACiB,IAAI,EAAED,MAAM,CAAC;MACjD,MAAMG,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACa,EAAE,KAAKgE,MAAM,CAAChE,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAG6E;QAAO,CAAC;QACtD,IAAI7E,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAE0D;QAAW,CAAC;QACjE,OAAO/E,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA0E,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOH,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMM,uBAAuB,GAAItC,KAAK,IAAK;IACzCZ,SAAS,CAAC,SAASY,KAAK,CAACX,OAAO,EAAE,EAAE,OAAO,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGnO,KAAK,CAACG,WAAW,CAAC,CAAC2E,KAAK,EAAEiG,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVhG,KAAK;MACLiG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqD,kBAAkB,GAAGjO,WAAW,CAAC,MAAM;IAC3C0K,gBAAgB,CAACkD,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPjD,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAtC,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2F,kBAAkB,GAAGlO,WAAW,CAAEmO,MAAM,IAAK;IACjD,MAAM;MAAExJ;IAAM,CAAC,GAAG8F,aAAa;IAC/B,IAAI9F,KAAK,KAAK,IAAI,EAAE;MAClB;MACAsJ,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjClF,WAAW,CAACmF,QAAQ,IAAI;UACtB,IAAIR,WAAW,GAAGQ,QAAQ,CAACzF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKhF,KAAK,EAAE;cACpB,IAAIwJ,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGrF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIuF,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAIpG,aAAa,EAAE;kBACjBoG,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAInG,UAAU,EAAE;kBACdkG,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAClF,MAAM,GAAG,CAAC,EAAE;kBACvBiF,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG5F,GAAG;kBAAEC,OAAO,EAAEwF,WAAW;kBAAEvF,iBAAiB,EAAEuF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOzF,GAAG;UACZ,CAAC,CAAC;UAEFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAjK,UAAU,CAAC,MAAM;UACfgH,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC;QACpC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEwD,kBAAkB,EAAEb,gBAAgB,EAAEvC,SAAS,EAAEzC,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAE/F;EACA,MAAMqG,mBAAmB,GAAG3O,WAAW,CAAC,MAAM;IAC5C6H,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+G,oBAAoB,GAAG5O,WAAW,CAAC,MAAM;IAC7C6H,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkH,YAAY,GAAG7O,WAAW,CAAC,MAAM;IACrC,IAAI0H,SAAS,CAACoH,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC3H,cAAc,CAAC4H,QAAQ,CAACrH,SAAS,CAACoH,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE1H,iBAAiB,CAACwG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAElG,SAAS,CAACoH,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDjE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B+D,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIzH,cAAc,CAAC4H,QAAQ,CAACrH,SAAS,CAACoH,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDjE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACnD,SAAS,EAAEP,cAAc,EAAEyH,oBAAoB,EAAE/D,SAAS,CAAC,CAAC;;EAEhE;EACA,MAAMmE,YAAY,GAAGhP,WAAW,CAAEmO,MAAM,IAAK;IAC3C/G,iBAAiB,CAACwG,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAACiC,IAAI,IAAIA,IAAI,KAAKd,MAAM,CAAC,CAAC;IAC/DtD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMqE,eAAe,GAAGlP,WAAW,CAAE2J,EAAE,IAAK;IAC1CR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA,IAAIqG,SAAS,GAAG,CAAC;MACjBrB,WAAW,CAACsB,OAAO,CAACtG,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UACrEf,GAAG,CAACe,EAAE,GAAGsF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFrB,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFjD,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;EAChC,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMwE,aAAa,GAAGrP,WAAW,CAAE2J,EAAE,IAAK;IACxCR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,MAAMwB,cAAc,GAAGxB,WAAW,CAACd,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHyF,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7F,EAAE,GAAG8F,CAAC,CAAC9F,EAAE,CAAC;MAC1C2F,cAAc,CAACF,OAAO,CAAC,CAACtG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOoE,WAAW;IACpB,CAAC,CAAC;IACFjK,UAAU,CAAC,MAAM;MACfgH,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAM6E,eAAe,GAAG1P,WAAW,CAAE2J,EAAE,IAAK;IAC1CR,WAAW,CAACyE,IAAI,IAAI;MAClB;MACA,MAAM+B,YAAY,GAAG/B,IAAI,CAACZ,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,MAAMiG,YAAY,GAAGD,YAAY,CAAC3C,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;MACpF2G,YAAY,CAACR,OAAO,CAAC,CAACtG,GAAG,EAAEY,KAAK,KAAK;QACnC,IAAI,OAAOZ,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UAC9Bf,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMoE,WAAW,GAAGV,gBAAgB,CAACuC,YAAY,CAAC;;MAElD;MACAnC,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElCjD,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B,OAAOiD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAMgF,YAAY,GAAG7P,WAAW,CAAE8P,UAAU,IAAK;IAC/C3G,WAAW,CAACyE,IAAI,IAAI;MAClB;MACA,MAAMmC,WAAW,GAAGnC,IAAI,CAACoC,SAAS,CAAClH,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKmG,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAOnC,IAAI;;MAEnC;MACA,MAAMqC,KAAK,GAAG5F,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAM8F,UAAU,GAAGtC,IAAI,CAACmC,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAACrG,EAAE,KAAK,QAAQ,GAAGqG,UAAU,CAACrG,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM8D,MAAM,GAAG;QACbhE,EAAE,EAAEsG,KAAK;QACTpG,EAAE,EAAEsG,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNtH,OAAO,EAAE,EAAE;QACXuH,QAAQ,EAAE,EAAE;QACZnG,UAAU,EAAE,CAAC;QACbnB,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,KAAK;QACfW,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAM2G,OAAO,GAAG,CAAC,GAAG3C,IAAI,CAAC;MACzB2C,OAAO,CAACC,MAAM,CAACT,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEpC,MAAM,CAAC;;MAE1C;MACA,MAAMiC,YAAY,GAAGW,OAAO,CAACvD,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;MAC/E2G,YAAY,CAACR,OAAO,CAAC,CAACtG,GAAG,EAAEY,KAAK,KAAK;QACnC,IAAI,OAAOZ,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UAC9Bf,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMoE,WAAW,GAAGV,gBAAgB,CAACmD,OAAO,CAAC;;MAE7C;MACA/C,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElCjD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;MAC7B,OAAOiD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAM4F,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF1I,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM2I,YAAY,GAAG,CAACxH,QAAQ,IAAI,EAAE,EACjC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAyH,YAAY,CAACnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC3F,EAAE,KAAK,QAAQ,IAAI,OAAO4F,CAAC,CAAC5F,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO2F,CAAC,CAAC3F,EAAE,GAAG4F,CAAC,CAAC5F,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM8G,OAAO,GAAGD,YAAY,CAAC7H,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb0G,IAAI,EAAEtH,GAAG,CAACsH,IAAI,GAAI,OAAOtH,GAAG,CAACsH,IAAI,KAAK,QAAQ,IAAItH,GAAG,CAACsH,IAAI,CAACrB,QAAQ,CAAC,GAAG,CAAC,GAAGjG,GAAG,CAACsH,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9H,GAAG,CAACsH,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEtH,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG+H,IAAI,CAACC,KAAK,CAAChI,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFuH,EAAE,EAAE,OAAOvH,GAAG,CAACuH,EAAE,KAAK,QAAQ,GAAGQ,IAAI,CAACC,KAAK,CAAChI,GAAG,CAACuH,EAAE,CAAC,GAAGvH,GAAG,CAACuH,EAAE,IAAI,EAAE;QAClEtH,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG+H,KAAK,EAAE,OAAOjI,GAAG,CAACwH,QAAQ,KAAK,QAAQ,GACpCxH,GAAG,CAACwH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGxH,GAAG,CAACwH,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,GAAGlI,GAAG,CAACwH,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,GAC3ElI,GAAG,CAACwH,QAAQ,IAAI,EAAE;QACpBW,MAAM,EAAE,OAAOnI,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAAC6G,OAAO,CAAC,CAAC,CAAC,GAAGlI,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM+G,WAAW,GAAG,CAAChI,QAAQ,IAAI,EAAE,EAChC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE8C,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAI,OAAOpE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMgH,YAAY,GAAI3K,MAAM,IAAIA,MAAM,CAACmF,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAGnF,MAAM;MAE5F,MAAM4K,QAAQ,GAAG,MAAMrO,KAAK,CAACsO,IAAI,CAAC,GAAGrO,OAAO,oBAAoB,EAAE;QAChEuD,IAAI,EAAEoK,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCxK,MAAM,EAAE2K;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAAC7K,IAAI,IAAI6K,QAAQ,CAAC7K,IAAI,CAAC+K,KAAK,EAAE;QACxC;QACA,MAAM1F,WAAW,GAAG,GAAG5I,OAAO,CAAC4N,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAAC7K,IAAI,CAACgL,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA1G,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC;;QAErC;QACAhH,UAAU,CAAC,MAAM;UACf,MAAM2N,MAAM,GAAG1F,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CyF,MAAM,CAACrG,KAAK,CAACsG,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAG9F,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACoF,MAAM,CAAC;UACjC3N,UAAU,CAAC,MAAM;YACfiI,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACkF,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BZ,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;IAClC,CAAC,SAAS;MACR9C,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM6J,kBAAkB,GAAG5R,WAAW,CAAC,CAAC2E,KAAK,EAAEkN,KAAK,KAAK;IACvD7D,iBAAiB,CAACrJ,KAAK,EAAEkN,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC7D,iBAAiB,CAAC,CAAC;EAEvB,MAAM8D,OAAO,GAAG5R,OAAO,CAAC,MAAO4G,WAAW,CAAC+B,GAAG,CAACkJ,GAAG,IAAI;IACpD,IAAI,EAAE7I,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC8I,cAAc,CAACD,GAAG,CAAChL,KAAK,CAAC,CAAC,IAAIgL,GAAG,CAAChL,KAAK,KAAK,SAAS,IAAIgL,GAAG,CAAChL,KAAK,KAAK,QAAQ,IAAIgL,GAAG,CAAChL,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIgL,GAAG,CAAChL,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEgL,GAAG,CAAChL,KAAK;QAChBC,UAAU,EAAE+K,GAAG,CAAC/K,UAAU;QAC1BiL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVjL,QAAQ,EAAE,KAAK;QACfkL,UAAU,EAAGzF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI6C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMmJ,iBAAiB,GAAG1F,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE5F,OAAA,CAAC/B,OAAO;cAAC8D,KAAK,EAAEuH,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAAC5D,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvElC,OAAA,CAAChC,IAAI;gBACHiR,KAAK,EAAED,iBAAkB;gBACzB5M,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAE0M,OAAO,EAAE,GAAG;kBAAEpM,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEwL,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAvN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIkO,UAAU,GAAG,MAAM;UACvB,IAAI1N,UAAU,GAAG,KAAK;UAEtB,IAAI6H,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI0D,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EuJ,UAAU,GAAG7F,MAAM,CAAC5D,GAAG,CAACE,iBAAiB;YACzCnE,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAE+H,MAAM,CAAC5D,GAAG,CAACa,EAAG;YACrB/E,IAAI,EAAE2N,UAAW;YACjB1N,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAE8M;UAAmB;YAAA1N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI0N,GAAG,CAAChL,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEgL,GAAG,CAAChL,KAAK;QAChBC,UAAU,EAAE+K,GAAG,CAAC/K,UAAU;QAC1BiL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVjL,QAAQ,EAAE,KAAK;QACfkL,UAAU,EAAGzF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEzG,OAAA,CAACjD,GAAG;YAACuF,EAAE,EAAE;cAAE+L,OAAO,EAAE,MAAM;cAAEe,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAnN,QAAA,gBAE3DlC,OAAA,CAAC/B,OAAO;cAAC8D,KAAK,EAAC,wDAAW;cAAAG,QAAA,eACxBlC,OAAA,CAAClC,UAAU;gBACTuE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,SAAS;gBACfV,OAAO,EAAEA,CAAA,KAAM+K,YAAY,CAACnD,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;gBAC3CjE,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTgN,eAAe,EAAE,cAAc;oBAC/BlN,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAF,QAAA,eAEFlC,OAAA,CAACP,oBAAoB;kBAACgD,QAAQ,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVjB,OAAA,CAAC/B,OAAO;cAAC8D,KAAK,EAAC,0EAAc;cAAAG,QAAA,eAC3BlC,OAAA,CAAClC,UAAU;gBACTuE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,OAAO;gBACbV,OAAO,EAAEA,CAAA,KAAM4K,eAAe,CAAChD,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;gBAC9CjE,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTgN,eAAe,EAAE,YAAY;oBAC7BlN,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAF,QAAA,eAEFlC,OAAA,CAACN,uBAAuB;kBAAC+C,QAAQ,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGTqI,MAAM,CAAC5D,GAAG,CAACG,QAAQ,gBAClB7F,OAAA,CAAC/C,MAAM;cAELkF,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZkN,SAAS,eAAEvP,OAAA,CAACd,QAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAMuK,aAAa,CAAC3C,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;cAC5CjE,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAL,QAAA,EACH;YAED,GAbM,MAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETjB,OAAA,CAAC/C,MAAM;cAELkF,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,OAAO;cACbC,IAAI,EAAC,OAAO;cACZkN,SAAS,eAAEvP,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BS,OAAO,EAAEA,CAAA,KAAMoK,eAAe,CAACxC,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;cAC9CjE,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAL,QAAA,EACH;YAED,GAbM,QAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG0N,GAAG;MACN9K,QAAQ,EAAEyF,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI6C,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAO8I,GAAG,CAAC9K,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDkL,UAAU,EAAGzF,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIkI,GAAG,CAAChL,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE3D,OAAA,CAAChD,UAAU;YAACmF,OAAO,EAAC,OAAO;YAAC+F,UAAU,EAAC,MAAM;YAAC9F,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOoH,MAAM,CAACmF,KAAK,KAAK,QAAQ,GAAGnF,MAAM,CAACmF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOtE,MAAM,CAACmF,KAAK,KAAK,QAAQ,IAAI,CAACe,KAAK,CAACzF,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAAC,GAAG1E,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGtE,MAAM,CAACmF;UAAK;YAAA3N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIqI,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE7F,OAAA,CAAChD,UAAU;YAACmF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAEmN,cAAc,EAAE;YAAe,CAAE;YAAAvN,QAAA,EACtFoH,MAAM,CAACmF;UAAK;YAAA3N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI0N,GAAG,CAAChL,KAAK,KAAK,MAAM,IAAI2F,MAAM,CAACmF,KAAK,EAAE;UACxC,OAAOnF,MAAM,CAACmF,KAAK,CAACjB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAImB,GAAG,CAAChL,KAAK,KAAK,IAAI,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAACpE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAChL,KAAK,KAAK,OAAO,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOhB,IAAI,CAACC,KAAK,CAACpE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAChL,KAAK,KAAK,IAAI,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAACpE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAChL,KAAK,KAAK,UAAU,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOnF,MAAM,CAACmF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGnF,MAAM,CAACmF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGtE,MAAM,CAACmF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAAChL,KAAK,KAAK,YAAY,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOnF,MAAM,CAACmF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAAChL,KAAK,KAAK,YAAY,IAAI,OAAO2F,MAAM,CAACmF,KAAK,KAAK,QAAQ,IAAI,CAACe,KAAK,CAACzF,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO1E,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOtE,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOnF,MAAM,CAACmF,KAAK;QACrB;QACA,OAAOnF,MAAM,CAACmF,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC7E,MAAM,CAAC8F,OAAO,CAAE,EAAE,CAAChM,WAAW,EAAEoC,QAAQ,EAAE0I,kBAAkB,EAAE1C,eAAe,EAAEG,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAM0D,gBAAgB,GAAG7S,OAAO,CAAC,MAAM;IACrC,IAAI,CAAC8H,UAAU,CAAC8G,IAAI,CAAC,CAAC,EAAE;MACtB,OAAO5F,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAM8J,WAAW,GAAGhL,UAAU,CAACiL,WAAW,CAAC,CAAC;IAC5C,OAAO,CAAC/J,QAAQ,IAAI,EAAE,EAAE8D,MAAM,CAAClE,GAAG,IAAI;MACpC,IAAIZ,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOgL,MAAM,CAACC,MAAM,CAACrK,GAAG,CAAC,CAACsK,IAAI,CAACvB,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACwB,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAClE,QAAQ,CAACiE,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMM,SAAS,GAAGxK,GAAG,CAACZ,YAAY,CAAC;QACnC,OAAOoL,SAAS,IAAIA,SAAS,CAACD,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAClE,QAAQ,CAACiE,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9J,QAAQ,EAAElB,UAAU,EAAEE,YAAY,CAAC,CAAC;;EAExC;EACA,MAAMqL,YAAY,GAAGrT,OAAO,CAAC,MAAM6S,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,IAAI,CAAC7J,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACElG,OAAA,CAACjD,GAAG;MAACuF,EAAE,EAAE;QAAE8N,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAnO,QAAA,gBACtClC,OAAA,CAAChD,UAAU;QAACmF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC/C,MAAM;QACLkF,OAAO,EAAC,WAAW;QACnBT,OAAO,EAAE2B,OAAQ;QACjBf,EAAE,EAAE;UAAEgO,EAAE,EAAE;QAAE,CAAE;QAAApO,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAACjD,GAAG;IAAAmF,QAAA,gBAEFlC,OAAA,CAAC9B,IAAI;MAACoE,EAAE,EAAE;QAAEiO,EAAE,EAAE;MAAE,CAAE;MAAArO,QAAA,eAClBlC,OAAA,CAAC7B,WAAW;QAAA+D,QAAA,eACVlC,OAAA,CAACjD,GAAG;UAACuF,EAAE,EAAE;YAAE+L,OAAO,EAAE,MAAM;YAAEgB,UAAU,EAAE,QAAQ;YAAEmB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAErB,GAAG,EAAE;UAAE,CAAE;UAAAlN,QAAA,gBAC5GlC,OAAA,CAACjD,GAAG;YAACuF,EAAE,EAAE;cAAE+L,OAAO,EAAE,MAAM;cAAEgB,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAAlN,QAAA,gBACzDlC,OAAA,CAACZ,cAAc;cAACkD,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACjD,GAAG;cAAAmF,QAAA,gBACFlC,OAAA,CAAChD,UAAU;gBAACmF,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAE4F,UAAU,EAAE,GAAG;kBAAE9F,KAAK,EAAE,cAAc;kBAAEmO,EAAE,EAAE;gBAAI,CAAE;gBAAArO,QAAA,EAAC;cAElF;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAChD,UAAU;gBAACmF,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAF,QAAA,EAAC;cAE7D;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAAC5B,KAAK;YAACyC,SAAS,EAAC,KAAK;YAAC6P,OAAO,EAAE,CAAE;YAACpO,EAAE,EAAE;cAAEmO,QAAQ,EAAE,MAAM;cAAErB,GAAG,EAAE;YAAE,CAAE;YAAAlN,QAAA,gBAClElC,OAAA,CAAChC,IAAI;cACH2S,IAAI,eAAE3Q,OAAA,CAACX,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBgO,KAAK,EAAE,GAAG,CAACkB,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAI,CAACA,GAAG,CAACc,OAAO,IAAI,CAACd,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,MAAO;cACzF9D,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAChC,IAAI;cACH2S,IAAI,eAAE3Q,OAAA,CAACV,cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBgO,KAAK,EAAE,WAAW1F,kBAAkB,CAAC4G,YAAY,CAAC,CAACvC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChExL,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACkP,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,iBAC1DlG,OAAA,CAAChC,IAAI;cACH2S,IAAI,eAAE3Q,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBgO,KAAK,EAAE,GAAG,CAACkB,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,OAAQ;cACzE9D,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACoE,EAAE,EAAE;QAAEiO,EAAE,EAAE;MAAE,CAAE;MAAArO,QAAA,eAClBlC,OAAA,CAAC7B,WAAW;QAAA+D,QAAA,gBACVlC,OAAA,CAAChD,UAAU;UAACmF,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAE4F,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAArO,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAE+P,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAAxO,QAAA,gBACxDlC,OAAA,CAAC/C,MAAM;YACLkF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfmN,SAAS,eAAEvP,OAAA,CAAClB,YAAY;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAE4G,cAAe;YAAApG,QAAA,EACzB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACLkF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfmN,SAAS,EAAE7K,oBAAoB,gBAAG1E,OAAA,CAACjC,gBAAgB;cAACsE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACb,gBAAgB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAE2L,gBAAiB;YAC1ByD,QAAQ,EAAEpM,oBAAqB;YAAAxC,QAAA,EAE9BwC,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACLkF,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACbmN,SAAS,eAAEvP,OAAA,CAACjB,cAAc;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEyH,aAAc;YAAAjH,QAAA,EACxB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACoE,EAAE,EAAE;QAAEiO,EAAE,EAAE;MAAE,CAAE;MAAArO,QAAA,eAClBlC,OAAA,CAAC7B,WAAW;QAAA+D,QAAA,gBACVlC,OAAA,CAAChD,UAAU;UAACmF,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAE4F,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAArO,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAE+P,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACrB,UAAU,EAAC,QAAQ;UAAAnN,QAAA,gBAC5ElC,OAAA,CAAC1B,WAAW;YAAC+D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAL,QAAA,gBAC9ClC,OAAA,CAACzB,UAAU;cAAA2D,QAAA,EAAC;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACxB,MAAM;cACLiQ,KAAK,EAAE3J,YAAa;cACpBmK,KAAK,EAAC,0BAAM;cACZ8B,QAAQ,EAAGjP,CAAC,IAAKiD,eAAe,CAACjD,CAAC,CAACkP,MAAM,CAACvC,KAAK,CAAE;cAAAvM,QAAA,gBAEjDlC,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,KAAK;gBAAAvM,QAAA,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,IAAI;gBAAAvM,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,MAAM;gBAAAvM,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,YAAY;gBAAAvM,QAAA,EAAC;cAAU;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,OAAO;gBAAAvM,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,IAAI;gBAAAvM,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,SAAS;gBAAAvM,QAAA,EAAC;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,UAAU;gBAAAvM,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACvB,QAAQ;gBAACgQ,KAAK,EAAC,YAAY;gBAAAvM,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAACnC,SAAS;YACRwE,IAAI,EAAC,OAAO;YACZ4O,WAAW,EAAC,yCAAW;YACvBxC,KAAK,EAAE7J,UAAW;YAClBmM,QAAQ,EAAGjP,CAAC,IAAK+C,aAAa,CAAC/C,CAAC,CAACkP,MAAM,CAACvC,KAAK,CAAE;YAC/CnM,EAAE,EAAE;cAAE4O,QAAQ,EAAE,CAAC;cAAE3O,QAAQ,EAAE;YAAI,CAAE;YACnC4O,UAAU,EAAE;cACVC,cAAc,eACZpR,OAAA,CAACtB,cAAc;gBAACoJ,QAAQ,EAAC,OAAO;gBAAA5F,QAAA,eAC9BlC,OAAA,CAACT,UAAU;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACDoQ,YAAY,EAAEzM,UAAU,iBACtB5E,OAAA,CAACtB,cAAc;gBAACoJ,QAAQ,EAAC,KAAK;gBAAA5F,QAAA,eAC5BlC,OAAA,CAAClC,UAAU;kBACTuE,IAAI,EAAC,OAAO;kBACZX,OAAO,EAAEA,CAAA,KAAMmD,aAAa,CAAC,EAAE,CAAE;kBACjCyM,IAAI,EAAC,KAAK;kBAAApP,QAAA,eAEVlC,OAAA,CAACR,SAAS;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAED2D,UAAU,iBACT5E,OAAA,CAAChD,UAAU;YAACmF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,eAC9C,EAACyN,gBAAgB,CAAC/F,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9C,KAAK;MAACoF,EAAE,EAAE;QAAEwM,KAAK,EAAE,MAAM;QAAEnM,QAAQ,EAAE;MAAS,CAAE;MAAAT,QAAA,eAC7ClC,OAAA,CAACjD,GAAG;QAACuF,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAA5M,QAAA,eACzClC,OAAA,CAACnB,QAAQ;UACP0S,IAAI,EAAEpB,YAAa;UACnBzB,OAAO,EAAEA,OAAQ;UACjB8C,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGvI,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFiM,cAAc,EAAGxI,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOyD,MAAM,CAACyI,MAAM,CAAClO,QAAQ,IAAI,OAAOyF,MAAM,CAACyI,MAAM,CAAClO,QAAQ,KAAK,UAAU,GAC3EyF,MAAM,CAACyI,MAAM,CAAClO,QAAQ,CAACyF,MAAM,CAAC,GAAGA,MAAM,CAACyI,MAAM,CAAClO,QAAQ;UAC3D,CAAE;UACFyG,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAACxD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOmE,MAAM,CAACxD,UAAU,KAAK,QAAQ,EAAE;gBACzCwD,MAAM,CAACxD,UAAU,GAAGgD,MAAM,CAACQ,MAAM,CAACxD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOuD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFI,uBAAuB,EAAEA,uBAAwB;UACjDrI,EAAE,EAAE;YACF,cAAc,EAAE;cACdgN,eAAe,EAAE,0BAA0B;cAC3CpH,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBoH,eAAe,EAAE,0BAA0B;cAC3ClN,KAAK,EAAE,eAAe;cACtBqN,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrB5M,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpBmF,OAAO,EAAE,KAAK;cACd6J,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9B3C,eAAe,EAAE,oBAAoB;cACrC0C,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7B3C,eAAe,EAAE,oBAAoB;cACrClN,KAAK,EAAE,cAAc;cACrB8P,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClChK,UAAU,EAAE,MAAM;cAClB9F,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChC4L,OAAO,EAAE;YACX,CAAC;YACD8D,SAAS,EAAE;UACb;QAAE;UAAArR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAAC3C,MAAM;MACLkK,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB6K,OAAO,EAAEvH,kBAAmB;MAC5BwH,SAAS;MACT7P,QAAQ,EAAC,IAAI;MACb8P,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAvQ,QAAA,gBAEnBlC,OAAA,CAAC1C,WAAW;QAAA4E,QAAA,eACVlC,OAAA,CAACjD,GAAG;UAACuF,EAAE,EAAE;YAAE+L,OAAO,EAAE,MAAM;YAAEmC,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE;UAAS,CAAE;UAAAnN,QAAA,gBAClFlC,OAAA,CAAChD,UAAU;YAACmF,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC/C,MAAM;YACLsS,SAAS,eAAEvP,OAAA,CAAChB,OAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAE6J,mBAAoB;YAC7BnJ,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjB,OAAA,CAACjD,GAAG;QAACuF,EAAE,EAAE;UAAEoQ,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzQ,QAAA,gBACxBlC,OAAA,CAAChD,UAAU;UAACmF,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAEiO,EAAE,EAAE;UAAE,CAAE;UAAArO,QAAA,EAAC;QAElE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAC,KAAK;UAAC6P,OAAO,EAAE,CAAE;UAAAxO,QAAA,gBAChClC,OAAA,CAACpB,gBAAgB;YACfgU,OAAO,eACL5S,OAAA,CAACrB,QAAQ;cACPkU,OAAO,EAAE7N,aAAc;cACvB+L,QAAQ,EAAGjP,CAAC,IAAKmD,gBAAgB,CAACnD,CAAC,CAACkP,MAAM,CAAC6B,OAAO,CAAE;cACpDxQ,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDgO,KAAK,EAAC;UAAS;YAAAnO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjB,OAAA,CAACpB,gBAAgB;YACfgU,OAAO,eACL5S,OAAA,CAACrB,QAAQ;cACPkU,OAAO,EAAE3N,UAAW;cACpB6L,QAAQ,EAAGjP,CAAC,IAAKqD,aAAa,CAACrD,CAAC,CAACkP,MAAM,CAAC6B,OAAO,CAAE;cACjDxQ,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDgO,KAAK,EAAC;UAAK;YAAAnO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjB,OAAA,CAACzC,aAAa;QAACuV,QAAQ;QAACxQ,EAAE,EAAE;UAAEyQ,CAAC,EAAE;QAAE,CAAE;QAAA7Q,QAAA,eACnClC,OAAA,CAACH,aAAa;UACZkD,MAAM,EAAE,GAAI;UACZiQ,SAAS,EAAEjP,cAAc,CAACmC,MAAO;UACjC+M,QAAQ,EAAE,EAAG;UACbnE,KAAK,EAAC,MAAM;UAAA5M,QAAA,EAEXA,CAAC;YAAEoE,KAAK;YAAEyB;UAAM,CAAC,KAAK;YACrB,MAAMgD,MAAM,GAAGhH,cAAc,CAACuC,KAAK,CAAC;YACpC,oBACEtG,OAAA,CAACtC,QAAQ;cAEPqK,KAAK,EAAEA,KAAM;cACbmL,cAAc;cACdC,eAAe,EAAGpI,MAAM,KAAK,MAAM,iBACjC/K,OAAA,CAAClC,UAAU;gBACTwT,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB5P,OAAO,EAAEA,CAAA,KAAMkK,YAAY,CAACb,MAAM,CAAE;gBAAA7I,QAAA,eAEpClC,OAAA,CAACf,UAAU;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAiB,QAAA,eAEFlC,OAAA,CAACrC,cAAc;gBAAC+D,OAAO,EAAEA,CAAA,KAAMoJ,kBAAkB,CAACC,MAAM,CAAE;gBAAA7I,QAAA,eACxDlC,OAAA,CAACpC,YAAY;kBAACwV,OAAO,EAAErI;gBAAO;kBAAAjK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ8J,MAAM;cAAAjK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAA0E,QAAA,eACZlC,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAEmJ,kBAAmB;UAAA3I,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAAC3C,MAAM;MACLkK,IAAI,EAAE/C,eAAgB;MACtB4N,OAAO,EAAE5G,oBAAqB;MAC9B6G,SAAS;MACT7P,QAAQ,EAAC,IAAI;MACb8P,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAvQ,QAAA,gBAEnBlC,OAAA,CAAC1C,WAAW;QAAA4E,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACzC,aAAa;QAAA2E,QAAA,eACZlC,OAAA,CAACnC,SAAS;UACRwV,SAAS;UACTC,MAAM,EAAC,OAAO;UACd/M,EAAE,EAAC,MAAM;UACT0I,KAAK,EAAC,0BAAM;UACZtH,IAAI,EAAC,MAAM;UACX0K,SAAS;UACTlQ,OAAO,EAAC,UAAU;UAClBsM,KAAK,EAAEnK,SAAU;UACjByM,QAAQ,EAAGjP,CAAC,IAAKyC,YAAY,CAACzC,CAAC,CAACkP,MAAM,CAACvC,KAAK;QAAE;UAAA3N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAA0E,QAAA,gBACZlC,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAE8J,oBAAqB;UAAAtJ,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAE+J,YAAa;UAACrJ,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACwC,GAAA,CAnrCIP,aAAa;AAAAqQ,GAAA,GAAbrQ,aAAa;AAqrCnB,eAAeA,aAAa;AAAC,IAAAhC,EAAA,EAAAI,GAAA,EAAA2B,GAAA,EAAAsQ,GAAA;AAAAC,YAAA,CAAAtS,EAAA;AAAAsS,YAAA,CAAAlS,GAAA;AAAAkS,YAAA,CAAAvQ,GAAA;AAAAuQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}