{"ast": null, "code": "import * as React from 'react';\nimport { TimerBasedCleanupTracking } from '../../utils/cleanupTracking/TimerBasedCleanupTracking';\nimport { FinalizationRegistryBasedCleanupTracking } from '../../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking';\n/**\n * Signal to the underlying logic what version of the public component API\n * of the data grid is exposed.\n */\nvar GridSignature = /*#__PURE__*/function (GridSignature) {\n  GridSignature[\"DataGrid\"] = \"DataGrid\";\n  GridSignature[\"DataGridPro\"] = \"DataGridPro\";\n  return GridSignature;\n}(GridSignature || {});\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {}\n\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\nexport function createUseGridApiEventHandler(registryContainer) {\n  let cleanupTokensCounter = 0;\n  return function useGridApiEventHandler(apiRef, eventName, handler, options) {\n    if (registryContainer.registry === null) {\n      registryContainer.registry = typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n    }\n    const [objectRetainedByReact] = React.useState(new ObjectToBeRetainedByReact());\n    const subscription = React.useRef(null);\n    const handlerRef = React.useRef();\n    handlerRef.current = handler;\n    const cleanupTokenRef = React.useRef(null);\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event, details) => {\n        if (!event.defaultMuiPrevented) {\n          var _handlerRef$current;\n          (_handlerRef$current = handlerRef.current) == null || _handlerRef$current.call(handlerRef, params, event, details);\n        }\n      };\n      subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n      cleanupTokensCounter += 1;\n      cleanupTokenRef.current = {\n        cleanupToken: cleanupTokensCounter\n      };\n      registryContainer.registry.register(objectRetainedByReact,\n      // The callback below will be called once this reference stops being retained\n      () => {\n        var _subscription$current;\n        (_subscription$current = subscription.current) == null || _subscription$current.call(subscription);\n        subscription.current = null;\n        cleanupTokenRef.current = null;\n      }, cleanupTokenRef.current);\n    } else if (!handlerRef.current && subscription.current) {\n      subscription.current();\n      subscription.current = null;\n      if (cleanupTokenRef.current) {\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n    }\n    React.useEffect(() => {\n      if (!subscription.current && handlerRef.current) {\n        const enhancedHandler = (params, event, details) => {\n          if (!event.defaultMuiPrevented) {\n            var _handlerRef$current2;\n            (_handlerRef$current2 = handlerRef.current) == null || _handlerRef$current2.call(handlerRef, params, event, details);\n          }\n        };\n        subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n      }\n      if (cleanupTokenRef.current && registryContainer.registry) {\n        // If the effect was called, it means that this render was committed\n        // so we can trust the cleanup function to remove the listener.\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n      return () => {\n        var _subscription$current2;\n        (_subscription$current2 = subscription.current) == null || _subscription$current2.call(subscription);\n        subscription.current = null;\n      };\n    }, [apiRef, eventName, options]);\n  };\n}\nconst registryContainer = {\n  registry: null\n};\n\n// TODO: move to @mui/x-data-grid/internals\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_resetCleanupTracking = () => {\n  var _registryContainer$re;\n  (_registryContainer$re = registryContainer.registry) == null || _registryContainer$re.reset();\n  registryContainer.registry = null;\n};\nexport const useGridApiEventHandler = createUseGridApiEventHandler(registryContainer);\nconst optionsSubscriberOptions = {\n  isFirst: true\n};\nexport function useGridApiOptionHandler(apiRef, eventName, handler) {\n  // Validate that only one per event name?\n  useGridApiEventHandler(apiRef, eventName, handler, optionsSubscriberOptions);\n}\nexport { GridSignature };", "map": {"version": 3, "names": ["React", "TimerBasedCleanupTracking", "FinalizationRegistryBasedCleanupTracking", "GridSignature", "ObjectToBeRetainedByReact", "createUseGridApiEventHandler", "registryContainer", "cleanupTokensCounter", "useGridApiEventHandler", "apiRef", "eventName", "handler", "options", "registry", "FinalizationRegistry", "objectRetainedByReact", "useState", "subscription", "useRef", "handler<PERSON>ef", "current", "cleanupTokenRef", "enhancedHandler", "params", "event", "details", "defaultMuiPrevented", "_handlerRef$current", "call", "subscribeEvent", "cleanupToken", "register", "_subscription$current", "unregister", "useEffect", "_handlerRef$current2", "_subscription$current2", "unstable_resetCleanupTracking", "_registryContainer$re", "reset", "optionsSubscriberOptions", "<PERSON><PERSON><PERSON><PERSON>", "useGridApiOptionHandler"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/utils/useGridApiEventHandler.js"], "sourcesContent": ["import * as React from 'react';\nimport { TimerBasedCleanupTracking } from '../../utils/cleanupTracking/TimerBasedCleanupTracking';\nimport { FinalizationRegistryBasedCleanupTracking } from '../../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking';\n/**\n * Signal to the underlying logic what version of the public component API\n * of the data grid is exposed.\n */\nvar GridSignature = /*#__PURE__*/function (GridSignature) {\n  GridSignature[\"DataGrid\"] = \"DataGrid\";\n  GridSignature[\"DataGridPro\"] = \"DataGridPro\";\n  return GridSignature;\n}(GridSignature || {});\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {}\n\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\nexport function createUseGridApiEventHandler(registryContainer) {\n  let cleanupTokensCounter = 0;\n  return function useGridApiEventHandler(apiRef, eventName, handler, options) {\n    if (registryContainer.registry === null) {\n      registryContainer.registry = typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n    }\n    const [objectRetainedByReact] = React.useState(new ObjectToBeRetainedByReact());\n    const subscription = React.useRef(null);\n    const handlerRef = React.useRef();\n    handlerRef.current = handler;\n    const cleanupTokenRef = React.useRef(null);\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event, details) => {\n        if (!event.defaultMuiPrevented) {\n          var _handlerRef$current;\n          (_handlerRef$current = handlerRef.current) == null || _handlerRef$current.call(handlerRef, params, event, details);\n        }\n      };\n      subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n      cleanupTokensCounter += 1;\n      cleanupTokenRef.current = {\n        cleanupToken: cleanupTokensCounter\n      };\n      registryContainer.registry.register(objectRetainedByReact,\n      // The callback below will be called once this reference stops being retained\n      () => {\n        var _subscription$current;\n        (_subscription$current = subscription.current) == null || _subscription$current.call(subscription);\n        subscription.current = null;\n        cleanupTokenRef.current = null;\n      }, cleanupTokenRef.current);\n    } else if (!handlerRef.current && subscription.current) {\n      subscription.current();\n      subscription.current = null;\n      if (cleanupTokenRef.current) {\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n    }\n    React.useEffect(() => {\n      if (!subscription.current && handlerRef.current) {\n        const enhancedHandler = (params, event, details) => {\n          if (!event.defaultMuiPrevented) {\n            var _handlerRef$current2;\n            (_handlerRef$current2 = handlerRef.current) == null || _handlerRef$current2.call(handlerRef, params, event, details);\n          }\n        };\n        subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n      }\n      if (cleanupTokenRef.current && registryContainer.registry) {\n        // If the effect was called, it means that this render was committed\n        // so we can trust the cleanup function to remove the listener.\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n      return () => {\n        var _subscription$current2;\n        (_subscription$current2 = subscription.current) == null || _subscription$current2.call(subscription);\n        subscription.current = null;\n      };\n    }, [apiRef, eventName, options]);\n  };\n}\nconst registryContainer = {\n  registry: null\n};\n\n// TODO: move to @mui/x-data-grid/internals\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_resetCleanupTracking = () => {\n  var _registryContainer$re;\n  (_registryContainer$re = registryContainer.registry) == null || _registryContainer$re.reset();\n  registryContainer.registry = null;\n};\nexport const useGridApiEventHandler = createUseGridApiEventHandler(registryContainer);\nconst optionsSubscriberOptions = {\n  isFirst: true\n};\nexport function useGridApiOptionHandler(apiRef, eventName, handler) {\n  // Validate that only one per event name?\n  useGridApiEventHandler(apiRef, eventName, handler, optionsSubscriberOptions);\n}\nexport { GridSignature };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,QAAQ,uDAAuD;AACjG,SAASC,wCAAwC,QAAQ,sEAAsE;AAC/H;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAa,UAAUA,aAAa,EAAE;EACxDA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtCA,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa;EAC5C,OAAOA,aAAa;AACtB,CAAC,CAACA,aAAa,IAAI,CAAC,CAAC,CAAC;AACtB;AACA,MAAMC,yBAAyB,CAAC;;AAEhC;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,iBAAiB,EAAE;EAC9D,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC1E,IAAIN,iBAAiB,CAACO,QAAQ,KAAK,IAAI,EAAE;MACvCP,iBAAiB,CAACO,QAAQ,GAAG,OAAOC,oBAAoB,KAAK,WAAW,GAAG,IAAIZ,wCAAwC,CAAC,CAAC,GAAG,IAAID,yBAAyB,CAAC,CAAC;IAC7J;IACA,MAAM,CAACc,qBAAqB,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAAC,IAAIZ,yBAAyB,CAAC,CAAC,CAAC;IAC/E,MAAMa,YAAY,GAAGjB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;IACvC,MAAMC,UAAU,GAAGnB,KAAK,CAACkB,MAAM,CAAC,CAAC;IACjCC,UAAU,CAACC,OAAO,GAAGT,OAAO;IAC5B,MAAMU,eAAe,GAAGrB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACD,YAAY,CAACG,OAAO,IAAID,UAAU,CAACC,OAAO,EAAE;MAC/C,MAAME,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,KAAK;QAClD,IAAI,CAACD,KAAK,CAACE,mBAAmB,EAAE;UAC9B,IAAIC,mBAAmB;UACvB,CAACA,mBAAmB,GAAGR,UAAU,CAACC,OAAO,KAAK,IAAI,IAAIO,mBAAmB,CAACC,IAAI,CAACT,UAAU,EAAEI,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC;QACpH;MACF,CAAC;MACDR,YAAY,CAACG,OAAO,GAAGX,MAAM,CAACW,OAAO,CAACS,cAAc,CAACnB,SAAS,EAAEY,eAAe,EAAEV,OAAO,CAAC;MACzFL,oBAAoB,IAAI,CAAC;MACzBc,eAAe,CAACD,OAAO,GAAG;QACxBU,YAAY,EAAEvB;MAChB,CAAC;MACDD,iBAAiB,CAACO,QAAQ,CAACkB,QAAQ,CAAChB,qBAAqB;MACzD;MACA,MAAM;QACJ,IAAIiB,qBAAqB;QACzB,CAACA,qBAAqB,GAAGf,YAAY,CAACG,OAAO,KAAK,IAAI,IAAIY,qBAAqB,CAACJ,IAAI,CAACX,YAAY,CAAC;QAClGA,YAAY,CAACG,OAAO,GAAG,IAAI;QAC3BC,eAAe,CAACD,OAAO,GAAG,IAAI;MAChC,CAAC,EAAEC,eAAe,CAACD,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAI,CAACD,UAAU,CAACC,OAAO,IAAIH,YAAY,CAACG,OAAO,EAAE;MACtDH,YAAY,CAACG,OAAO,CAAC,CAAC;MACtBH,YAAY,CAACG,OAAO,GAAG,IAAI;MAC3B,IAAIC,eAAe,CAACD,OAAO,EAAE;QAC3Bd,iBAAiB,CAACO,QAAQ,CAACoB,UAAU,CAACZ,eAAe,CAACD,OAAO,CAAC;QAC9DC,eAAe,CAACD,OAAO,GAAG,IAAI;MAChC;IACF;IACApB,KAAK,CAACkC,SAAS,CAAC,MAAM;MACpB,IAAI,CAACjB,YAAY,CAACG,OAAO,IAAID,UAAU,CAACC,OAAO,EAAE;QAC/C,MAAME,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,KAAK;UAClD,IAAI,CAACD,KAAK,CAACE,mBAAmB,EAAE;YAC9B,IAAIS,oBAAoB;YACxB,CAACA,oBAAoB,GAAGhB,UAAU,CAACC,OAAO,KAAK,IAAI,IAAIe,oBAAoB,CAACP,IAAI,CAACT,UAAU,EAAEI,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC;UACtH;QACF,CAAC;QACDR,YAAY,CAACG,OAAO,GAAGX,MAAM,CAACW,OAAO,CAACS,cAAc,CAACnB,SAAS,EAAEY,eAAe,EAAEV,OAAO,CAAC;MAC3F;MACA,IAAIS,eAAe,CAACD,OAAO,IAAId,iBAAiB,CAACO,QAAQ,EAAE;QACzD;QACA;QACAP,iBAAiB,CAACO,QAAQ,CAACoB,UAAU,CAACZ,eAAe,CAACD,OAAO,CAAC;QAC9DC,eAAe,CAACD,OAAO,GAAG,IAAI;MAChC;MACA,OAAO,MAAM;QACX,IAAIgB,sBAAsB;QAC1B,CAACA,sBAAsB,GAAGnB,YAAY,CAACG,OAAO,KAAK,IAAI,IAAIgB,sBAAsB,CAACR,IAAI,CAACX,YAAY,CAAC;QACpGA,YAAY,CAACG,OAAO,GAAG,IAAI;MAC7B,CAAC;IACH,CAAC,EAAE,CAACX,MAAM,EAAEC,SAAS,EAAEE,OAAO,CAAC,CAAC;EAClC,CAAC;AACH;AACA,MAAMN,iBAAiB,GAAG;EACxBO,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA,OAAO,MAAMwB,6BAA6B,GAAGA,CAAA,KAAM;EACjD,IAAIC,qBAAqB;EACzB,CAACA,qBAAqB,GAAGhC,iBAAiB,CAACO,QAAQ,KAAK,IAAI,IAAIyB,qBAAqB,CAACC,KAAK,CAAC,CAAC;EAC7FjC,iBAAiB,CAACO,QAAQ,GAAG,IAAI;AACnC,CAAC;AACD,OAAO,MAAML,sBAAsB,GAAGH,4BAA4B,CAACC,iBAAiB,CAAC;AACrF,MAAMkC,wBAAwB,GAAG;EAC/BC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,SAASC,uBAAuBA,CAACjC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAClE;EACAH,sBAAsB,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE6B,wBAAwB,CAAC;AAC9E;AACA,SAASrC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}