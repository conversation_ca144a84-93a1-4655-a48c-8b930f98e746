{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"style\"],\n  _excluded2 = [\"style\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useTheme } from '@mui/material/styles';\nimport { defaultMemoize } from 'reselect';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nimport { useGridRootProps } from '../../utils/useGridRootProps';\nimport { useGridSelector } from '../../utils/useGridSelector';\nimport { gridVisibleColumnDefinitionsSelector, gridColumnsTotalWidthSelector, gridColumnPositionsSelector } from '../columns/gridColumnsSelector';\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from '../focus/gridFocusStateSelector';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { clamp } from '../../../utils/utils';\nimport { selectedIdsLookupSelector } from '../rowSelection/gridRowSelectionSelector';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getFirstNonSpannedColumnToRender } from '../columns/gridColumnsUtils';\nimport { getMinimalContentHeight } from '../rows/gridRowsUtils';\nimport { gridVirtualizationEnabledSelector, gridVirtualizationColumnEnabledSelector } from './gridVirtualizationSelectors';\n\n// Uses binary search to avoid looping through all possible positions\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function binarySearch(offset, positions) {\n  let sliceStart = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let sliceEnd = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : positions.length;\n  if (positions.length <= 0) {\n    return -1;\n  }\n  if (sliceStart >= sliceEnd) {\n    return sliceStart;\n  }\n  const pivot = sliceStart + Math.floor((sliceEnd - sliceStart) / 2);\n  const itemOffset = positions[pivot];\n  return offset <= itemOffset ? binarySearch(offset, positions, sliceStart, pivot) : binarySearch(offset, positions, pivot + 1, sliceEnd);\n}\nfunction exponentialSearch(offset, positions, index) {\n  let interval = 1;\n  while (index < positions.length && Math.abs(positions[index]) < offset) {\n    index += interval;\n    interval *= 2;\n  }\n  return binarySearch(offset, positions, Math.floor(index / 2), Math.min(index, positions.length));\n}\nexport const getRenderableIndexes = _ref3 => {\n  let {\n    firstIndex,\n    lastIndex,\n    buffer,\n    minFirstIndex,\n    maxLastIndex\n  } = _ref3;\n  return [clamp(firstIndex - buffer, minFirstIndex, maxLastIndex), clamp(lastIndex + buffer, minFirstIndex, maxLastIndex)];\n};\nexport const areRenderContextsEqual = (context1, context2) => {\n  if (context1 === context2) {\n    return true;\n  }\n  return context1.firstRowIndex === context2.firstRowIndex && context1.lastRowIndex === context2.lastRowIndex && context1.firstColumnIndex === context2.firstColumnIndex && context1.lastColumnIndex === context2.lastColumnIndex;\n};\n// The `maxSize` is 3 so that reselect caches the `renderedColumns` values for the pinned left,\n// unpinned, and pinned right sections.\nconst MEMOIZE_OPTIONS = {\n  maxSize: 3\n};\nexport const useGridVirtualScroller = props => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const enabled = useGridSelector(apiRef, gridVirtualizationEnabledSelector);\n  const enabledForColumns = useGridSelector(apiRef, gridVirtualizationColumnEnabledSelector);\n  const {\n    ref,\n    onRenderZonePositioning,\n    renderZoneMinColumnIndex = 0,\n    renderZoneMaxColumnIndex = visibleColumns.length,\n    getRowProps\n  } = props;\n  const theme = useTheme();\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const cellFocus = useGridSelector(apiRef, gridFocusCellSelector);\n  const cellTabIndex = useGridSelector(apiRef, gridTabIndexCellSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const selectedRowsLookup = useGridSelector(apiRef, selectedIdsLookupSelector);\n  const currentPage = useGridVisibleRows(apiRef, rootProps);\n  const renderZoneRef = React.useRef(null);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(ref, rootRef);\n  const [renderContext, setRenderContextState] = React.useState(null);\n  const prevRenderContext = React.useRef(renderContext);\n  const scrollPosition = React.useRef({\n    top: 0,\n    left: 0\n  });\n  const [containerDimensions, setContainerDimensions] = React.useState({\n    width: null,\n    height: null\n  });\n  const prevTotalWidth = React.useRef(columnsTotalWidth);\n  // Each visible row (not to be confused with a filter result) is composed of a central row element\n  // and up to two additional row elements for pinned columns (left and right).\n  // When hovering any of these elements, the :hover styles are applied only to the row element that\n  // was actually hovered, not its additional siblings. To make it look like a contiguous row,\n  // we add/remove the .Mui-hovered class to all of the row elements inside one visible row.\n  const [hoveredRowId, setHoveredRowId] = React.useState(null);\n  const rowStyleCache = React.useRef(Object.create(null));\n  const prevGetRowProps = React.useRef();\n  const prevRootRowStyle = React.useRef();\n  const getRenderedColumnsRef = React.useRef(defaultMemoize((columns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, indexOfColumnWithFocusedCell) => {\n    // If the selected column is not within the current range of columns being displayed,\n    // we need to render it at either the left or right of the columns,\n    // depending on whether it is above or below the range.\n    let focusedCellColumnIndexNotInRange;\n    const renderedColumns = columns.slice(firstColumnToRender, lastColumnToRender);\n    if (indexOfColumnWithFocusedCell > -1) {\n      // check if it is not on the left pinned column.\n      if (firstColumnToRender > indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell >= minFirstColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n      // check if it is not on the right pinned column.\n      else if (lastColumnToRender < indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell < maxLastColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n    }\n    return {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    };\n  }, MEMOIZE_OPTIONS));\n  const indexOfColumnWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return visibleColumns.findIndex(column => column.field === cellFocus.field);\n    }\n    return -1;\n  }, [cellFocus, visibleColumns]);\n  const computeRenderContext = React.useCallback(() => {\n    if (!enabled) {\n      return {\n        firstRowIndex: 0,\n        lastRowIndex: currentPage.rows.length,\n        firstColumnIndex: 0,\n        lastColumnIndex: visibleColumns.length\n      };\n    }\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n\n    // Clamp the value because the search may return an index out of bounds.\n    // In the last index, this is not needed because Array.slice doesn't include it.\n    const firstRowIndex = Math.min(getNearestIndexToRender(apiRef, currentPage, rowsMeta, top), rowsMeta.positions.length - 1);\n    const lastRowIndex = rootProps.autoHeight ? firstRowIndex + currentPage.rows.length : getNearestIndexToRender(apiRef, currentPage, rowsMeta, top + containerDimensions.height);\n    let firstColumnIndex = 0;\n    let lastColumnIndex = columnPositions.length;\n    if (enabledForColumns) {\n      let hasRowWithAutoHeight = false;\n      const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n        firstIndex: firstRowIndex,\n        lastIndex: lastRowIndex,\n        minFirstIndex: 0,\n        maxLastIndex: currentPage.rows.length,\n        buffer: rootProps.rowBuffer\n      });\n      for (let i = firstRowToRender; i < lastRowToRender && !hasRowWithAutoHeight; i += 1) {\n        const row = currentPage.rows[i];\n        hasRowWithAutoHeight = apiRef.current.rowHasAutoHeight(row.id);\n      }\n      if (!hasRowWithAutoHeight) {\n        firstColumnIndex = binarySearch(Math.abs(left), columnPositions);\n        lastColumnIndex = binarySearch(Math.abs(left) + containerDimensions.width, columnPositions);\n      }\n    }\n    return {\n      firstRowIndex,\n      lastRowIndex,\n      firstColumnIndex,\n      lastColumnIndex\n    };\n  }, [enabled, enabledForColumns, rowsMeta, rootProps.autoHeight, rootProps.rowBuffer, currentPage, columnPositions, visibleColumns.length, apiRef, containerDimensions]);\n  useEnhancedEffect(() => {\n    if (enabled) {\n      // TODO a scroll reset should not be necessary\n      rootRef.current.scrollLeft = 0;\n      rootRef.current.scrollTop = 0;\n    } else {\n      renderZoneRef.current.style.transform = \"translate3d(0px, 0px, 0px)\";\n    }\n  }, [enabled]);\n  useEnhancedEffect(() => {\n    setContainerDimensions({\n      width: rootRef.current.clientWidth,\n      height: rootRef.current.clientHeight\n    });\n  }, [rowsMeta.currentPageTotalHeight]);\n  const handleResize = React.useCallback(() => {\n    if (rootRef.current) {\n      setContainerDimensions({\n        width: rootRef.current.clientWidth,\n        height: rootRef.current.clientHeight\n      });\n    }\n  }, []);\n  useGridApiEventHandler(apiRef, 'debouncedResize', handleResize);\n  const updateRenderZonePosition = React.useCallback(nextRenderContext => {\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    const [initialFirstColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: renderZoneMinColumnIndex,\n      maxLastIndex: renderZoneMaxColumnIndex,\n      buffer: rootProps.columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    const direction = theme.direction === 'ltr' ? 1 : -1;\n    const top = gridRowsMetaSelector(apiRef.current.state).positions[firstRowToRender];\n    const left = direction * gridColumnPositionsSelector(apiRef)[firstColumnToRender]; // Call directly the selector because it might be outdated when this method is called\n    renderZoneRef.current.style.transform = \"translate3d(\".concat(left, \"px, \").concat(top, \"px, 0px)\");\n    if (typeof onRenderZonePositioning === 'function') {\n      onRenderZonePositioning({\n        top,\n        left\n      });\n    }\n  }, [apiRef, currentPage.rows, onRenderZonePositioning, renderZoneMinColumnIndex, renderZoneMaxColumnIndex, rootProps.columnBuffer, rootProps.rowBuffer, theme.direction]);\n  const getRenderContext = React.useCallback(() => prevRenderContext.current, []);\n  const setRenderContext = React.useCallback(nextRenderContext => {\n    if (prevRenderContext.current && areRenderContextsEqual(nextRenderContext, prevRenderContext.current)) {\n      updateRenderZonePosition(nextRenderContext);\n      return;\n    }\n    setRenderContextState(nextRenderContext);\n    updateRenderZonePosition(nextRenderContext);\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    apiRef.current.publishEvent('renderedRowsIntervalChange', {\n      firstRowToRender,\n      lastRowToRender\n    });\n    prevRenderContext.current = nextRenderContext;\n  }, [apiRef, setRenderContextState, prevRenderContext, currentPage.rows.length, rootProps.rowBuffer, updateRenderZonePosition]);\n  useEnhancedEffect(() => {\n    if (containerDimensions.width == null) {\n      return;\n    }\n    const initialRenderContext = computeRenderContext();\n    setRenderContext(initialRenderContext);\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n    const params = {\n      top,\n      left,\n      renderContext: initialRenderContext\n    };\n    apiRef.current.publishEvent('scrollPositionChange', params);\n  }, [apiRef, computeRenderContext, containerDimensions.width, setRenderContext]);\n  const handleScroll = useEventCallback(event => {\n    const {\n      scrollTop,\n      scrollLeft\n    } = event.currentTarget;\n    scrollPosition.current.top = scrollTop;\n    scrollPosition.current.left = scrollLeft;\n\n    // On iOS and macOS, negative offsets are possible when swiping past the start\n    if (!prevRenderContext.current || scrollTop < 0) {\n      return;\n    }\n    if (theme.direction === 'ltr') {\n      if (scrollLeft < 0) {\n        return;\n      }\n    }\n    if (theme.direction === 'rtl') {\n      if (scrollLeft > 0) {\n        return;\n      }\n    }\n\n    // When virtualization is disabled, the context never changes during scroll\n    const nextRenderContext = enabled ? computeRenderContext() : prevRenderContext.current;\n    const topRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstRowIndex - prevRenderContext.current.firstRowIndex);\n    const bottomRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastRowIndex - prevRenderContext.current.lastRowIndex);\n    const topColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstColumnIndex - prevRenderContext.current.firstColumnIndex);\n    const bottomColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastColumnIndex - prevRenderContext.current.lastColumnIndex);\n    const shouldSetState = topRowsScrolledSincePreviousRender >= rootProps.rowThreshold || bottomRowsScrolledSincePreviousRender >= rootProps.rowThreshold || topColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || bottomColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || prevTotalWidth.current !== columnsTotalWidth;\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollTop,\n      left: scrollLeft,\n      renderContext: shouldSetState ? nextRenderContext : prevRenderContext.current\n    }, event);\n    if (shouldSetState) {\n      // Prevents batching render context changes\n      ReactDOM.flushSync(() => {\n        setRenderContext(nextRenderContext);\n      });\n      prevTotalWidth.current = columnsTotalWidth;\n    }\n  });\n  const handleWheel = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n  });\n  const handleTouchMove = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n  });\n  const indexOfRowWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return currentPage.rows.findIndex(row => row.id === cellFocus.id);\n    }\n    return -1;\n  }, [cellFocus, currentPage.rows]);\n  useGridApiEventHandler(apiRef, 'rowMouseOver', (params, event) => {\n    var _params$id;\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId((_params$id = params.id) != null ? _params$id : null);\n  });\n  useGridApiEventHandler(apiRef, 'rowMouseOut', (params, event) => {\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId(null);\n  });\n  const getRows = function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      renderContext\n    };\n    var _rootProps$slotProps;\n    const {\n      onRowRender,\n      renderContext: nextRenderContext,\n      minFirstColumn = renderZoneMinColumnIndex,\n      maxLastColumn = renderZoneMaxColumnIndex,\n      availableSpace = containerDimensions.width,\n      rowIndexOffset = 0,\n      position = 'center'\n    } = params;\n    if (!nextRenderContext || availableSpace == null) {\n      return null;\n    }\n    const rowBuffer = enabled ? rootProps.rowBuffer : 0;\n    const columnBuffer = enabled ? rootProps.columnBuffer : 0;\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rowBuffer\n    });\n    const renderedRows = [];\n    if (params.rows) {\n      params.rows.forEach(row => {\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      });\n    } else {\n      if (!currentPage.range) {\n        return null;\n      }\n      for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n        const row = currentPage.rows[i];\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    // If the selected row is not within the current range of rows being displayed,\n    // we need to render it at either the top or bottom of the rows,\n    // depending on whether it is above or below the range.\n\n    let isRowWithFocusedCellNotInRange = false;\n    if (indexOfRowWithFocusedCell > -1) {\n      const rowWithFocusedCell = currentPage.rows[indexOfRowWithFocusedCell];\n      if (firstRowToRender > indexOfRowWithFocusedCell || lastRowToRender < indexOfRowWithFocusedCell) {\n        isRowWithFocusedCellNotInRange = true;\n        if (indexOfRowWithFocusedCell > firstRowToRender) {\n          renderedRows.push(rowWithFocusedCell);\n        } else {\n          renderedRows.unshift(rowWithFocusedCell);\n        }\n        apiRef.current.calculateColSpan({\n          rowId: rowWithFocusedCell.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    const [initialFirstColumnToRender, lastColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: minFirstColumn,\n      maxLastIndex: maxLastColumn,\n      buffer: columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    let isColumnWihFocusedCellNotInRange = false;\n    if (firstColumnToRender > indexOfColumnWithFocusedCell || lastColumnToRender < indexOfColumnWithFocusedCell) {\n      isColumnWihFocusedCellNotInRange = true;\n    }\n    const {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    } = getRenderedColumnsRef.current(visibleColumns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, isColumnWihFocusedCellNotInRange ? indexOfColumnWithFocusedCell : -1);\n    const _ref = ((_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.row) || {},\n      {\n        style: rootRowStyle\n      } = _ref,\n      rootRowProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const invalidatesCachedRowStyle = prevGetRowProps.current !== getRowProps || prevRootRowStyle.current !== rootRowStyle;\n    if (invalidatesCachedRowStyle) {\n      rowStyleCache.current = Object.create(null);\n    }\n    const rows = [];\n    let isRowWithFocusedCellRendered = false;\n    for (let i = 0; i < renderedRows.length; i += 1) {\n      var _currentPage$range;\n      const {\n        id,\n        model\n      } = renderedRows[i];\n      const isRowNotVisible = isRowWithFocusedCellNotInRange && cellFocus.id === id;\n      const lastVisibleRowIndex = isRowWithFocusedCellNotInRange ? firstRowToRender + i === currentPage.rows.length : firstRowToRender + i === currentPage.rows.length - 1;\n      const baseRowHeight = !apiRef.current.rowHasAutoHeight(id) ? apiRef.current.unstable_getRowHeight(id) : 'auto';\n      let isSelected;\n      if (selectedRowsLookup[id] == null) {\n        isSelected = false;\n      } else {\n        isSelected = apiRef.current.isRowSelectable(id);\n      }\n      if (onRowRender) {\n        onRowRender(id);\n      }\n      const focusedCell = cellFocus !== null && cellFocus.id === id ? cellFocus.field : null;\n      const columnWithFocusedCellNotInRange = focusedCellColumnIndexNotInRange !== undefined && visibleColumns[focusedCellColumnIndexNotInRange];\n      const renderedColumnsWithFocusedCell = columnWithFocusedCellNotInRange && focusedCell ? [columnWithFocusedCellNotInRange, ...renderedColumns] : renderedColumns;\n      let tabbableCell = null;\n      if (cellTabIndex !== null && cellTabIndex.id === id) {\n        const cellParams = apiRef.current.getCellParams(id, cellTabIndex.field);\n        tabbableCell = cellParams.cellMode === 'view' ? cellTabIndex.field : null;\n      }\n      const _ref2 = typeof getRowProps === 'function' && getRowProps(id, model) || {},\n        {\n          style: rowStyle\n        } = _ref2,\n        rowProps = _objectWithoutPropertiesLoose(_ref2, _excluded2);\n      if (!rowStyleCache.current[id]) {\n        const style = _extends({}, rowStyle, rootRowStyle);\n        rowStyleCache.current[id] = style;\n      }\n      let index = rowIndexOffset + ((currentPage == null || (_currentPage$range = currentPage.range) == null ? void 0 : _currentPage$range.firstRowIndex) || 0) + firstRowToRender + i;\n      if (isRowWithFocusedCellNotInRange && (cellFocus == null ? void 0 : cellFocus.id) === id) {\n        index = indexOfRowWithFocusedCell;\n        isRowWithFocusedCellRendered = true;\n      } else if (isRowWithFocusedCellRendered) {\n        index -= 1;\n      }\n      rows.push(/*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n        row: model,\n        rowId: id,\n        focusedCellColumnIndexNotInRange: focusedCellColumnIndexNotInRange,\n        isNotVisible: isRowNotVisible,\n        rowHeight: baseRowHeight,\n        focusedCell: focusedCell,\n        tabbableCell: tabbableCell,\n        renderedColumns: renderedColumnsWithFocusedCell,\n        visibleColumns: visibleColumns,\n        firstColumnToRender: firstColumnToRender,\n        lastColumnToRender: lastColumnToRender,\n        selected: isSelected,\n        index: index,\n        containerWidth: availableSpace,\n        isLastVisible: lastVisibleRowIndex,\n        position: position\n      }, rowProps, rootRowProps, {\n        hovered: hoveredRowId === id,\n        style: rowStyleCache.current[id]\n      }), id));\n    }\n    prevGetRowProps.current = getRowProps;\n    prevRootRowStyle.current = rootRowStyle;\n    return rows;\n  };\n  const needsHorizontalScrollbar = containerDimensions.width && columnsTotalWidth >= containerDimensions.width;\n  const contentSize = React.useMemo(() => {\n    // In cases where the columns exceed the available width,\n    // the horizontal scrollbar should be shown even when there're no rows.\n    // Keeping 1px as minimum height ensures that the scrollbar will visible if necessary.\n    const height = Math.max(rowsMeta.currentPageTotalHeight, 1);\n    let shouldExtendContent = false;\n    if (rootRef != null && rootRef.current && height <= (rootRef == null ? void 0 : rootRef.current.clientHeight)) {\n      shouldExtendContent = true;\n    }\n    const size = {\n      width: needsHorizontalScrollbar ? columnsTotalWidth : 'auto',\n      height,\n      minHeight: shouldExtendContent ? '100%' : 'auto'\n    };\n    if (rootProps.autoHeight && currentPage.rows.length === 0) {\n      size.height = getMinimalContentHeight(apiRef, rootProps.rowHeight); // Give room to show the overlay when there no rows.\n    }\n    return size;\n  }, [apiRef, rootRef, columnsTotalWidth, rowsMeta.currentPageTotalHeight, needsHorizontalScrollbar, rootProps.autoHeight, rootProps.rowHeight, currentPage.rows.length]);\n  React.useEffect(() => {\n    apiRef.current.publishEvent('virtualScrollerContentSizeChange');\n  }, [apiRef, contentSize]);\n  const rootStyle = React.useMemo(() => {\n    const style = {};\n    if (!needsHorizontalScrollbar) {\n      style.overflowX = 'hidden';\n    }\n    if (rootProps.autoHeight) {\n      style.overflowY = 'hidden';\n    }\n    return style;\n  }, [needsHorizontalScrollbar, rootProps.autoHeight]);\n  apiRef.current.register('private', {\n    getRenderContext\n  });\n  return {\n    renderContext,\n    updateRenderZonePosition,\n    getRows,\n    getRootProps: function () {\n      let inputProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return _extends({\n        ref: handleRef,\n        onScroll: handleScroll,\n        onWheel: handleWheel,\n        onTouchMove: handleTouchMove\n      }, inputProps, {\n        style: inputProps.style ? _extends({}, inputProps.style, rootStyle) : rootStyle,\n        role: 'presentation'\n      });\n    },\n    getContentProps: function () {\n      let {\n        style\n      } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return {\n        style: style ? _extends({}, style, contentSize) : contentSize,\n        role: 'presentation'\n      };\n    },\n    getRenderZoneProps: () => ({\n      ref: renderZoneRef,\n      role: 'rowgroup'\n    })\n  };\n};\nfunction getNearestIndexToRender(apiRef, currentPage, rowsMeta, offset) {\n  var _currentPage$range2, _currentPage$range3;\n  const lastMeasuredIndexRelativeToAllRows = apiRef.current.getLastMeasuredRowIndex();\n  let allRowsMeasured = lastMeasuredIndexRelativeToAllRows === Infinity;\n  if ((_currentPage$range2 = currentPage.range) != null && _currentPage$range2.lastRowIndex && !allRowsMeasured) {\n    // Check if all rows in this page are already measured\n    allRowsMeasured = lastMeasuredIndexRelativeToAllRows >= currentPage.range.lastRowIndex;\n  }\n  const lastMeasuredIndexRelativeToCurrentPage = clamp(lastMeasuredIndexRelativeToAllRows - (((_currentPage$range3 = currentPage.range) == null ? void 0 : _currentPage$range3.firstRowIndex) || 0), 0, rowsMeta.positions.length);\n  if (allRowsMeasured || rowsMeta.positions[lastMeasuredIndexRelativeToCurrentPage] >= offset) {\n    // If all rows were measured (when no row has \"auto\" as height) or all rows before the offset\n    // were measured, then use a binary search because it's faster.\n    return binarySearch(offset, rowsMeta.positions);\n  }\n\n  // Otherwise, use an exponential search.\n  // If rows have \"auto\" as height, their positions will be based on estimated heights.\n  // In this case, we can skip several steps until we find a position higher than the offset.\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  return exponentialSearch(offset, rowsMeta.positions, lastMeasuredIndexRelativeToCurrentPage);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "ReactDOM", "unstable_useForkRef", "useForkRef", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "useTheme", "defaultMemoize", "useGridPrivateApiContext", "useGridRootProps", "useGridSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnsTotalWidthSelector", "gridColumnPositionsSelector", "gridFocusCellSelector", "gridTabIndexCellSelector", "useGridVisibleRows", "useGridApiEventHandler", "clamp", "selectedIdsLookupSelector", "gridRowsMetaSelector", "getFirstNonSpannedColumnToRender", "getMinimalContentHeight", "gridVirtualizationEnabledSelector", "gridVirtualizationColumnEnabledSelector", "jsx", "_jsx", "binarySearch", "offset", "positions", "sliceStart", "arguments", "length", "undefined", "sliceEnd", "pivot", "Math", "floor", "itemOffset", "exponentialSearch", "index", "interval", "abs", "min", "getRenderableIndexes", "_ref3", "firstIndex", "lastIndex", "buffer", "minFirstIndex", "maxLastIndex", "areRenderContextsEqual", "context1", "context2", "firstRowIndex", "lastRowIndex", "firstColumnIndex", "lastColumnIndex", "MEMOIZE_OPTIONS", "maxSize", "useGridVirtualScroller", "props", "apiRef", "rootProps", "visibleColumns", "enabled", "enabledForColumns", "ref", "onRenderZonePositioning", "renderZoneMinColumnIndex", "renderZoneMaxColumnIndex", "getRowProps", "theme", "columnPositions", "columnsTotalWidth", "cellFocus", "cellTabIndex", "rowsMeta", "selectedRowsLookup", "currentPage", "renderZoneRef", "useRef", "rootRef", "handleRef", "renderContext", "setRenderContextState", "useState", "prevRenderContext", "scrollPosition", "top", "left", "containerDimensions", "setContainerDimensions", "width", "height", "prevTotalWidth", "hoveredRowId", "setHoveredRowId", "rowStyleCache", "Object", "create", "prevGetRowProps", "prevRootRowStyle", "getRenderedColumnsRef", "columns", "firstColumnToRender", "lastColumnToRender", "minFirstColumn", "maxLastColumn", "indexOfColumnWithFocusedCell", "focusedCellColumnIndexNotInRange", "renderedColumns", "slice", "useMemo", "findIndex", "column", "field", "computeRenderContext", "useCallback", "rows", "current", "getNearestIndexToRender", "autoHeight", "hasRowWithAutoHeight", "firstRowToRender", "lastRowToRender", "<PERSON><PERSON><PERSON><PERSON>", "i", "row", "rowHasAutoHeight", "id", "scrollLeft", "scrollTop", "style", "transform", "clientWidth", "clientHeight", "currentPageTotalHeight", "handleResize", "updateRenderZonePosition", "nextRenderContext", "initialFirstColumnToRender", "columnBuffer", "visibleRows", "direction", "state", "concat", "getRenderContext", "setRenderContext", "publishEvent", "initialRenderContext", "params", "handleScroll", "event", "currentTarget", "topRowsScrolledSincePreviousRender", "bottomRowsScrolledSincePreviousRender", "topColumnsScrolledSincePreviousRender", "bottomColumnsScrolledSincePreviousRender", "shouldSetState", "rowThreshold", "columnThreshold", "flushSync", "handleWheel", "handleTouchMove", "indexOfRowWithFocusedCell", "_params$id", "contains", "relatedTarget", "getRows", "_rootProps$slotProps", "onRowRender", "availableSpace", "rowIndexOffset", "position", "renderedRows", "for<PERSON>ach", "push", "calculateColSpan", "rowId", "range", "isRowWithFocusedCellNotInRange", "rowWithFocusedCell", "unshift", "isColumnWihFocusedCellNotInRange", "_ref", "slotProps", "rootRowStyle", "rootRowProps", "invalidatesCachedRowStyle", "isRowWithFocusedCellRendered", "_currentPage$range", "model", "isRowNotVisible", "lastVisibleRowIndex", "baseRowHeight", "unstable_getRowHeight", "isSelected", "isRowSelectable", "focusedCell", "columnWithFocusedCellNotInRange", "renderedColumnsWithFocusedCell", "tabbableCell", "cellParams", "getCellParams", "cellMode", "_ref2", "rowStyle", "rowProps", "slots", "isNotVisible", "rowHeight", "selected", "containerWidth", "isLastVisible", "hovered", "needsHorizontalScrollbar", "contentSize", "max", "shouldExtendContent", "size", "minHeight", "useEffect", "rootStyle", "overflowX", "overflowY", "register", "getRootProps", "inputProps", "onScroll", "onWheel", "onTouchMove", "role", "getContentProps", "getRenderZoneProps", "_currentPage$range2", "_currentPage$range3", "lastMeasuredIndexRelativeToAllRows", "getLastMeasuredRowIndex", "allRowsMeasured", "Infinity", "lastMeasuredIndexRelativeToCurrentPage"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/virtualization/useGridVirtualScroller.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"style\"],\n  _excluded2 = [\"style\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useTheme } from '@mui/material/styles';\nimport { defaultMemoize } from 'reselect';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nimport { useGridRootProps } from '../../utils/useGridRootProps';\nimport { useGridSelector } from '../../utils/useGridSelector';\nimport { gridVisibleColumnDefinitionsSelector, gridColumnsTotalWidthSelector, gridColumnPositionsSelector } from '../columns/gridColumnsSelector';\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from '../focus/gridFocusStateSelector';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { clamp } from '../../../utils/utils';\nimport { selectedIdsLookupSelector } from '../rowSelection/gridRowSelectionSelector';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { getFirstNonSpannedColumnToRender } from '../columns/gridColumnsUtils';\nimport { getMinimalContentHeight } from '../rows/gridRowsUtils';\nimport { gridVirtualizationEnabledSelector, gridVirtualizationColumnEnabledSelector } from './gridVirtualizationSelectors';\n\n// Uses binary search to avoid looping through all possible positions\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function binarySearch(offset, positions, sliceStart = 0, sliceEnd = positions.length) {\n  if (positions.length <= 0) {\n    return -1;\n  }\n  if (sliceStart >= sliceEnd) {\n    return sliceStart;\n  }\n  const pivot = sliceStart + Math.floor((sliceEnd - sliceStart) / 2);\n  const itemOffset = positions[pivot];\n  return offset <= itemOffset ? binarySearch(offset, positions, sliceStart, pivot) : binarySearch(offset, positions, pivot + 1, sliceEnd);\n}\nfunction exponentialSearch(offset, positions, index) {\n  let interval = 1;\n  while (index < positions.length && Math.abs(positions[index]) < offset) {\n    index += interval;\n    interval *= 2;\n  }\n  return binarySearch(offset, positions, Math.floor(index / 2), Math.min(index, positions.length));\n}\nexport const getRenderableIndexes = ({\n  firstIndex,\n  lastIndex,\n  buffer,\n  minFirstIndex,\n  maxLastIndex\n}) => {\n  return [clamp(firstIndex - buffer, minFirstIndex, maxLastIndex), clamp(lastIndex + buffer, minFirstIndex, maxLastIndex)];\n};\nexport const areRenderContextsEqual = (context1, context2) => {\n  if (context1 === context2) {\n    return true;\n  }\n  return context1.firstRowIndex === context2.firstRowIndex && context1.lastRowIndex === context2.lastRowIndex && context1.firstColumnIndex === context2.firstColumnIndex && context1.lastColumnIndex === context2.lastColumnIndex;\n};\n// The `maxSize` is 3 so that reselect caches the `renderedColumns` values for the pinned left,\n// unpinned, and pinned right sections.\nconst MEMOIZE_OPTIONS = {\n  maxSize: 3\n};\nexport const useGridVirtualScroller = props => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const enabled = useGridSelector(apiRef, gridVirtualizationEnabledSelector);\n  const enabledForColumns = useGridSelector(apiRef, gridVirtualizationColumnEnabledSelector);\n  const {\n    ref,\n    onRenderZonePositioning,\n    renderZoneMinColumnIndex = 0,\n    renderZoneMaxColumnIndex = visibleColumns.length,\n    getRowProps\n  } = props;\n  const theme = useTheme();\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const cellFocus = useGridSelector(apiRef, gridFocusCellSelector);\n  const cellTabIndex = useGridSelector(apiRef, gridTabIndexCellSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const selectedRowsLookup = useGridSelector(apiRef, selectedIdsLookupSelector);\n  const currentPage = useGridVisibleRows(apiRef, rootProps);\n  const renderZoneRef = React.useRef(null);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(ref, rootRef);\n  const [renderContext, setRenderContextState] = React.useState(null);\n  const prevRenderContext = React.useRef(renderContext);\n  const scrollPosition = React.useRef({\n    top: 0,\n    left: 0\n  });\n  const [containerDimensions, setContainerDimensions] = React.useState({\n    width: null,\n    height: null\n  });\n  const prevTotalWidth = React.useRef(columnsTotalWidth);\n  // Each visible row (not to be confused with a filter result) is composed of a central row element\n  // and up to two additional row elements for pinned columns (left and right).\n  // When hovering any of these elements, the :hover styles are applied only to the row element that\n  // was actually hovered, not its additional siblings. To make it look like a contiguous row,\n  // we add/remove the .Mui-hovered class to all of the row elements inside one visible row.\n  const [hoveredRowId, setHoveredRowId] = React.useState(null);\n  const rowStyleCache = React.useRef(Object.create(null));\n  const prevGetRowProps = React.useRef();\n  const prevRootRowStyle = React.useRef();\n  const getRenderedColumnsRef = React.useRef(defaultMemoize((columns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, indexOfColumnWithFocusedCell) => {\n    // If the selected column is not within the current range of columns being displayed,\n    // we need to render it at either the left or right of the columns,\n    // depending on whether it is above or below the range.\n    let focusedCellColumnIndexNotInRange;\n    const renderedColumns = columns.slice(firstColumnToRender, lastColumnToRender);\n    if (indexOfColumnWithFocusedCell > -1) {\n      // check if it is not on the left pinned column.\n      if (firstColumnToRender > indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell >= minFirstColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n      // check if it is not on the right pinned column.\n      else if (lastColumnToRender < indexOfColumnWithFocusedCell && indexOfColumnWithFocusedCell < maxLastColumn) {\n        focusedCellColumnIndexNotInRange = indexOfColumnWithFocusedCell;\n      }\n    }\n    return {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    };\n  }, MEMOIZE_OPTIONS));\n  const indexOfColumnWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return visibleColumns.findIndex(column => column.field === cellFocus.field);\n    }\n    return -1;\n  }, [cellFocus, visibleColumns]);\n  const computeRenderContext = React.useCallback(() => {\n    if (!enabled) {\n      return {\n        firstRowIndex: 0,\n        lastRowIndex: currentPage.rows.length,\n        firstColumnIndex: 0,\n        lastColumnIndex: visibleColumns.length\n      };\n    }\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n\n    // Clamp the value because the search may return an index out of bounds.\n    // In the last index, this is not needed because Array.slice doesn't include it.\n    const firstRowIndex = Math.min(getNearestIndexToRender(apiRef, currentPage, rowsMeta, top), rowsMeta.positions.length - 1);\n    const lastRowIndex = rootProps.autoHeight ? firstRowIndex + currentPage.rows.length : getNearestIndexToRender(apiRef, currentPage, rowsMeta, top + containerDimensions.height);\n    let firstColumnIndex = 0;\n    let lastColumnIndex = columnPositions.length;\n    if (enabledForColumns) {\n      let hasRowWithAutoHeight = false;\n      const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n        firstIndex: firstRowIndex,\n        lastIndex: lastRowIndex,\n        minFirstIndex: 0,\n        maxLastIndex: currentPage.rows.length,\n        buffer: rootProps.rowBuffer\n      });\n      for (let i = firstRowToRender; i < lastRowToRender && !hasRowWithAutoHeight; i += 1) {\n        const row = currentPage.rows[i];\n        hasRowWithAutoHeight = apiRef.current.rowHasAutoHeight(row.id);\n      }\n      if (!hasRowWithAutoHeight) {\n        firstColumnIndex = binarySearch(Math.abs(left), columnPositions);\n        lastColumnIndex = binarySearch(Math.abs(left) + containerDimensions.width, columnPositions);\n      }\n    }\n    return {\n      firstRowIndex,\n      lastRowIndex,\n      firstColumnIndex,\n      lastColumnIndex\n    };\n  }, [enabled, enabledForColumns, rowsMeta, rootProps.autoHeight, rootProps.rowBuffer, currentPage, columnPositions, visibleColumns.length, apiRef, containerDimensions]);\n  useEnhancedEffect(() => {\n    if (enabled) {\n      // TODO a scroll reset should not be necessary\n      rootRef.current.scrollLeft = 0;\n      rootRef.current.scrollTop = 0;\n    } else {\n      renderZoneRef.current.style.transform = `translate3d(0px, 0px, 0px)`;\n    }\n  }, [enabled]);\n  useEnhancedEffect(() => {\n    setContainerDimensions({\n      width: rootRef.current.clientWidth,\n      height: rootRef.current.clientHeight\n    });\n  }, [rowsMeta.currentPageTotalHeight]);\n  const handleResize = React.useCallback(() => {\n    if (rootRef.current) {\n      setContainerDimensions({\n        width: rootRef.current.clientWidth,\n        height: rootRef.current.clientHeight\n      });\n    }\n  }, []);\n  useGridApiEventHandler(apiRef, 'debouncedResize', handleResize);\n  const updateRenderZonePosition = React.useCallback(nextRenderContext => {\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    const [initialFirstColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: renderZoneMinColumnIndex,\n      maxLastIndex: renderZoneMaxColumnIndex,\n      buffer: rootProps.columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    const direction = theme.direction === 'ltr' ? 1 : -1;\n    const top = gridRowsMetaSelector(apiRef.current.state).positions[firstRowToRender];\n    const left = direction * gridColumnPositionsSelector(apiRef)[firstColumnToRender]; // Call directly the selector because it might be outdated when this method is called\n    renderZoneRef.current.style.transform = `translate3d(${left}px, ${top}px, 0px)`;\n    if (typeof onRenderZonePositioning === 'function') {\n      onRenderZonePositioning({\n        top,\n        left\n      });\n    }\n  }, [apiRef, currentPage.rows, onRenderZonePositioning, renderZoneMinColumnIndex, renderZoneMaxColumnIndex, rootProps.columnBuffer, rootProps.rowBuffer, theme.direction]);\n  const getRenderContext = React.useCallback(() => prevRenderContext.current, []);\n  const setRenderContext = React.useCallback(nextRenderContext => {\n    if (prevRenderContext.current && areRenderContextsEqual(nextRenderContext, prevRenderContext.current)) {\n      updateRenderZonePosition(nextRenderContext);\n      return;\n    }\n    setRenderContextState(nextRenderContext);\n    updateRenderZonePosition(nextRenderContext);\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rootProps.rowBuffer\n    });\n    apiRef.current.publishEvent('renderedRowsIntervalChange', {\n      firstRowToRender,\n      lastRowToRender\n    });\n    prevRenderContext.current = nextRenderContext;\n  }, [apiRef, setRenderContextState, prevRenderContext, currentPage.rows.length, rootProps.rowBuffer, updateRenderZonePosition]);\n  useEnhancedEffect(() => {\n    if (containerDimensions.width == null) {\n      return;\n    }\n    const initialRenderContext = computeRenderContext();\n    setRenderContext(initialRenderContext);\n    const {\n      top,\n      left\n    } = scrollPosition.current;\n    const params = {\n      top,\n      left,\n      renderContext: initialRenderContext\n    };\n    apiRef.current.publishEvent('scrollPositionChange', params);\n  }, [apiRef, computeRenderContext, containerDimensions.width, setRenderContext]);\n  const handleScroll = useEventCallback(event => {\n    const {\n      scrollTop,\n      scrollLeft\n    } = event.currentTarget;\n    scrollPosition.current.top = scrollTop;\n    scrollPosition.current.left = scrollLeft;\n\n    // On iOS and macOS, negative offsets are possible when swiping past the start\n    if (!prevRenderContext.current || scrollTop < 0) {\n      return;\n    }\n    if (theme.direction === 'ltr') {\n      if (scrollLeft < 0) {\n        return;\n      }\n    }\n    if (theme.direction === 'rtl') {\n      if (scrollLeft > 0) {\n        return;\n      }\n    }\n\n    // When virtualization is disabled, the context never changes during scroll\n    const nextRenderContext = enabled ? computeRenderContext() : prevRenderContext.current;\n    const topRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstRowIndex - prevRenderContext.current.firstRowIndex);\n    const bottomRowsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastRowIndex - prevRenderContext.current.lastRowIndex);\n    const topColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.firstColumnIndex - prevRenderContext.current.firstColumnIndex);\n    const bottomColumnsScrolledSincePreviousRender = Math.abs(nextRenderContext.lastColumnIndex - prevRenderContext.current.lastColumnIndex);\n    const shouldSetState = topRowsScrolledSincePreviousRender >= rootProps.rowThreshold || bottomRowsScrolledSincePreviousRender >= rootProps.rowThreshold || topColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || bottomColumnsScrolledSincePreviousRender >= rootProps.columnThreshold || prevTotalWidth.current !== columnsTotalWidth;\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollTop,\n      left: scrollLeft,\n      renderContext: shouldSetState ? nextRenderContext : prevRenderContext.current\n    }, event);\n    if (shouldSetState) {\n      // Prevents batching render context changes\n      ReactDOM.flushSync(() => {\n        setRenderContext(nextRenderContext);\n      });\n      prevTotalWidth.current = columnsTotalWidth;\n    }\n  });\n  const handleWheel = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n  });\n  const handleTouchMove = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n  });\n  const indexOfRowWithFocusedCell = React.useMemo(() => {\n    if (cellFocus !== null) {\n      return currentPage.rows.findIndex(row => row.id === cellFocus.id);\n    }\n    return -1;\n  }, [cellFocus, currentPage.rows]);\n  useGridApiEventHandler(apiRef, 'rowMouseOver', (params, event) => {\n    var _params$id;\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId((_params$id = params.id) != null ? _params$id : null);\n  });\n  useGridApiEventHandler(apiRef, 'rowMouseOut', (params, event) => {\n    if (event.currentTarget.contains(event.relatedTarget)) {\n      return;\n    }\n    setHoveredRowId(null);\n  });\n  const getRows = (params = {\n    renderContext\n  }) => {\n    var _rootProps$slotProps;\n    const {\n      onRowRender,\n      renderContext: nextRenderContext,\n      minFirstColumn = renderZoneMinColumnIndex,\n      maxLastColumn = renderZoneMaxColumnIndex,\n      availableSpace = containerDimensions.width,\n      rowIndexOffset = 0,\n      position = 'center'\n    } = params;\n    if (!nextRenderContext || availableSpace == null) {\n      return null;\n    }\n    const rowBuffer = enabled ? rootProps.rowBuffer : 0;\n    const columnBuffer = enabled ? rootProps.columnBuffer : 0;\n    const [firstRowToRender, lastRowToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstRowIndex,\n      lastIndex: nextRenderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: currentPage.rows.length,\n      buffer: rowBuffer\n    });\n    const renderedRows = [];\n    if (params.rows) {\n      params.rows.forEach(row => {\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      });\n    } else {\n      if (!currentPage.range) {\n        return null;\n      }\n      for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n        const row = currentPage.rows[i];\n        renderedRows.push(row);\n        apiRef.current.calculateColSpan({\n          rowId: row.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    // If the selected row is not within the current range of rows being displayed,\n    // we need to render it at either the top or bottom of the rows,\n    // depending on whether it is above or below the range.\n\n    let isRowWithFocusedCellNotInRange = false;\n    if (indexOfRowWithFocusedCell > -1) {\n      const rowWithFocusedCell = currentPage.rows[indexOfRowWithFocusedCell];\n      if (firstRowToRender > indexOfRowWithFocusedCell || lastRowToRender < indexOfRowWithFocusedCell) {\n        isRowWithFocusedCellNotInRange = true;\n        if (indexOfRowWithFocusedCell > firstRowToRender) {\n          renderedRows.push(rowWithFocusedCell);\n        } else {\n          renderedRows.unshift(rowWithFocusedCell);\n        }\n        apiRef.current.calculateColSpan({\n          rowId: rowWithFocusedCell.id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n      }\n    }\n    const [initialFirstColumnToRender, lastColumnToRender] = getRenderableIndexes({\n      firstIndex: nextRenderContext.firstColumnIndex,\n      lastIndex: nextRenderContext.lastColumnIndex,\n      minFirstIndex: minFirstColumn,\n      maxLastIndex: maxLastColumn,\n      buffer: columnBuffer\n    });\n    const firstColumnToRender = getFirstNonSpannedColumnToRender({\n      firstColumnToRender: initialFirstColumnToRender,\n      apiRef,\n      firstRowToRender,\n      lastRowToRender,\n      visibleRows: currentPage.rows\n    });\n    let isColumnWihFocusedCellNotInRange = false;\n    if (firstColumnToRender > indexOfColumnWithFocusedCell || lastColumnToRender < indexOfColumnWithFocusedCell) {\n      isColumnWihFocusedCellNotInRange = true;\n    }\n    const {\n      focusedCellColumnIndexNotInRange,\n      renderedColumns\n    } = getRenderedColumnsRef.current(visibleColumns, firstColumnToRender, lastColumnToRender, minFirstColumn, maxLastColumn, isColumnWihFocusedCellNotInRange ? indexOfColumnWithFocusedCell : -1);\n    const _ref = ((_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.row) || {},\n      {\n        style: rootRowStyle\n      } = _ref,\n      rootRowProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const invalidatesCachedRowStyle = prevGetRowProps.current !== getRowProps || prevRootRowStyle.current !== rootRowStyle;\n    if (invalidatesCachedRowStyle) {\n      rowStyleCache.current = Object.create(null);\n    }\n    const rows = [];\n    let isRowWithFocusedCellRendered = false;\n    for (let i = 0; i < renderedRows.length; i += 1) {\n      var _currentPage$range;\n      const {\n        id,\n        model\n      } = renderedRows[i];\n      const isRowNotVisible = isRowWithFocusedCellNotInRange && cellFocus.id === id;\n      const lastVisibleRowIndex = isRowWithFocusedCellNotInRange ? firstRowToRender + i === currentPage.rows.length : firstRowToRender + i === currentPage.rows.length - 1;\n      const baseRowHeight = !apiRef.current.rowHasAutoHeight(id) ? apiRef.current.unstable_getRowHeight(id) : 'auto';\n      let isSelected;\n      if (selectedRowsLookup[id] == null) {\n        isSelected = false;\n      } else {\n        isSelected = apiRef.current.isRowSelectable(id);\n      }\n      if (onRowRender) {\n        onRowRender(id);\n      }\n      const focusedCell = cellFocus !== null && cellFocus.id === id ? cellFocus.field : null;\n      const columnWithFocusedCellNotInRange = focusedCellColumnIndexNotInRange !== undefined && visibleColumns[focusedCellColumnIndexNotInRange];\n      const renderedColumnsWithFocusedCell = columnWithFocusedCellNotInRange && focusedCell ? [columnWithFocusedCellNotInRange, ...renderedColumns] : renderedColumns;\n      let tabbableCell = null;\n      if (cellTabIndex !== null && cellTabIndex.id === id) {\n        const cellParams = apiRef.current.getCellParams(id, cellTabIndex.field);\n        tabbableCell = cellParams.cellMode === 'view' ? cellTabIndex.field : null;\n      }\n      const _ref2 = typeof getRowProps === 'function' && getRowProps(id, model) || {},\n        {\n          style: rowStyle\n        } = _ref2,\n        rowProps = _objectWithoutPropertiesLoose(_ref2, _excluded2);\n      if (!rowStyleCache.current[id]) {\n        const style = _extends({}, rowStyle, rootRowStyle);\n        rowStyleCache.current[id] = style;\n      }\n      let index = rowIndexOffset + ((currentPage == null || (_currentPage$range = currentPage.range) == null ? void 0 : _currentPage$range.firstRowIndex) || 0) + firstRowToRender + i;\n      if (isRowWithFocusedCellNotInRange && (cellFocus == null ? void 0 : cellFocus.id) === id) {\n        index = indexOfRowWithFocusedCell;\n        isRowWithFocusedCellRendered = true;\n      } else if (isRowWithFocusedCellRendered) {\n        index -= 1;\n      }\n      rows.push( /*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n        row: model,\n        rowId: id,\n        focusedCellColumnIndexNotInRange: focusedCellColumnIndexNotInRange,\n        isNotVisible: isRowNotVisible,\n        rowHeight: baseRowHeight,\n        focusedCell: focusedCell,\n        tabbableCell: tabbableCell,\n        renderedColumns: renderedColumnsWithFocusedCell,\n        visibleColumns: visibleColumns,\n        firstColumnToRender: firstColumnToRender,\n        lastColumnToRender: lastColumnToRender,\n        selected: isSelected,\n        index: index,\n        containerWidth: availableSpace,\n        isLastVisible: lastVisibleRowIndex,\n        position: position\n      }, rowProps, rootRowProps, {\n        hovered: hoveredRowId === id,\n        style: rowStyleCache.current[id]\n      }), id));\n    }\n    prevGetRowProps.current = getRowProps;\n    prevRootRowStyle.current = rootRowStyle;\n    return rows;\n  };\n  const needsHorizontalScrollbar = containerDimensions.width && columnsTotalWidth >= containerDimensions.width;\n  const contentSize = React.useMemo(() => {\n    // In cases where the columns exceed the available width,\n    // the horizontal scrollbar should be shown even when there're no rows.\n    // Keeping 1px as minimum height ensures that the scrollbar will visible if necessary.\n    const height = Math.max(rowsMeta.currentPageTotalHeight, 1);\n    let shouldExtendContent = false;\n    if (rootRef != null && rootRef.current && height <= (rootRef == null ? void 0 : rootRef.current.clientHeight)) {\n      shouldExtendContent = true;\n    }\n    const size = {\n      width: needsHorizontalScrollbar ? columnsTotalWidth : 'auto',\n      height,\n      minHeight: shouldExtendContent ? '100%' : 'auto'\n    };\n    if (rootProps.autoHeight && currentPage.rows.length === 0) {\n      size.height = getMinimalContentHeight(apiRef, rootProps.rowHeight); // Give room to show the overlay when there no rows.\n    }\n    return size;\n  }, [apiRef, rootRef, columnsTotalWidth, rowsMeta.currentPageTotalHeight, needsHorizontalScrollbar, rootProps.autoHeight, rootProps.rowHeight, currentPage.rows.length]);\n  React.useEffect(() => {\n    apiRef.current.publishEvent('virtualScrollerContentSizeChange');\n  }, [apiRef, contentSize]);\n  const rootStyle = React.useMemo(() => {\n    const style = {};\n    if (!needsHorizontalScrollbar) {\n      style.overflowX = 'hidden';\n    }\n    if (rootProps.autoHeight) {\n      style.overflowY = 'hidden';\n    }\n    return style;\n  }, [needsHorizontalScrollbar, rootProps.autoHeight]);\n  apiRef.current.register('private', {\n    getRenderContext\n  });\n  return {\n    renderContext,\n    updateRenderZonePosition,\n    getRows,\n    getRootProps: (inputProps = {}) => _extends({\n      ref: handleRef,\n      onScroll: handleScroll,\n      onWheel: handleWheel,\n      onTouchMove: handleTouchMove\n    }, inputProps, {\n      style: inputProps.style ? _extends({}, inputProps.style, rootStyle) : rootStyle,\n      role: 'presentation'\n    }),\n    getContentProps: ({\n      style\n    } = {}) => ({\n      style: style ? _extends({}, style, contentSize) : contentSize,\n      role: 'presentation'\n    }),\n    getRenderZoneProps: () => ({\n      ref: renderZoneRef,\n      role: 'rowgroup'\n    })\n  };\n};\nfunction getNearestIndexToRender(apiRef, currentPage, rowsMeta, offset) {\n  var _currentPage$range2, _currentPage$range3;\n  const lastMeasuredIndexRelativeToAllRows = apiRef.current.getLastMeasuredRowIndex();\n  let allRowsMeasured = lastMeasuredIndexRelativeToAllRows === Infinity;\n  if ((_currentPage$range2 = currentPage.range) != null && _currentPage$range2.lastRowIndex && !allRowsMeasured) {\n    // Check if all rows in this page are already measured\n    allRowsMeasured = lastMeasuredIndexRelativeToAllRows >= currentPage.range.lastRowIndex;\n  }\n  const lastMeasuredIndexRelativeToCurrentPage = clamp(lastMeasuredIndexRelativeToAllRows - (((_currentPage$range3 = currentPage.range) == null ? void 0 : _currentPage$range3.firstRowIndex) || 0), 0, rowsMeta.positions.length);\n  if (allRowsMeasured || rowsMeta.positions[lastMeasuredIndexRelativeToCurrentPage] >= offset) {\n    // If all rows were measured (when no row has \"auto\" as height) or all rows before the offset\n    // were measured, then use a binary search because it's faster.\n    return binarySearch(offset, rowsMeta.positions);\n  }\n\n  // Otherwise, use an exponential search.\n  // If rows have \"auto\" as height, their positions will be based on estimated heights.\n  // In this case, we can skip several steps until we find a position higher than the offset.\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  return exponentialSearch(offset, rowsMeta.positions, lastMeasuredIndexRelativeToCurrentPage);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;EACzBC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AAC9J,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,oCAAoC,EAAEC,6BAA6B,EAAEC,2BAA2B,QAAQ,gCAAgC;AACjJ,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,iCAAiC;AACjG,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,yBAAyB,QAAQ,0CAA0C;AACpF,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,gCAAgC,QAAQ,6BAA6B;AAC9E,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,SAASC,iCAAiC,EAAEC,uCAAuC,QAAQ,+BAA+B;;AAE1H;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,SAAS,EAA+C;EAAA,IAA7CC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,QAAQ,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGF,SAAS,CAACG,MAAM;EACzF,IAAIH,SAAS,CAACG,MAAM,IAAI,CAAC,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;EACA,IAAIF,UAAU,IAAII,QAAQ,EAAE;IAC1B,OAAOJ,UAAU;EACnB;EACA,MAAMK,KAAK,GAAGL,UAAU,GAAGM,IAAI,CAACC,KAAK,CAAC,CAACH,QAAQ,GAAGJ,UAAU,IAAI,CAAC,CAAC;EAClE,MAAMQ,UAAU,GAAGT,SAAS,CAACM,KAAK,CAAC;EACnC,OAAOP,MAAM,IAAIU,UAAU,GAAGX,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEK,KAAK,CAAC,GAAGR,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEM,KAAK,GAAG,CAAC,EAAED,QAAQ,CAAC;AACzI;AACA,SAASK,iBAAiBA,CAACX,MAAM,EAAEC,SAAS,EAAEW,KAAK,EAAE;EACnD,IAAIC,QAAQ,GAAG,CAAC;EAChB,OAAOD,KAAK,GAAGX,SAAS,CAACG,MAAM,IAAII,IAAI,CAACM,GAAG,CAACb,SAAS,CAACW,KAAK,CAAC,CAAC,GAAGZ,MAAM,EAAE;IACtEY,KAAK,IAAIC,QAAQ;IACjBA,QAAQ,IAAI,CAAC;EACf;EACA,OAAOd,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEO,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACO,GAAG,CAACH,KAAK,EAAEX,SAAS,CAACG,MAAM,CAAC,CAAC;AAClG;AACA,OAAO,MAAMY,oBAAoB,GAAGC,KAAA,IAM9B;EAAA,IAN+B;IACnCC,UAAU;IACVC,SAAS;IACTC,MAAM;IACNC,aAAa;IACbC;EACF,CAAC,GAAAL,KAAA;EACC,OAAO,CAAC3B,KAAK,CAAC4B,UAAU,GAAGE,MAAM,EAAEC,aAAa,EAAEC,YAAY,CAAC,EAAEhC,KAAK,CAAC6B,SAAS,GAAGC,MAAM,EAAEC,aAAa,EAAEC,YAAY,CAAC,CAAC;AAC1H,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;EAC5D,IAAID,QAAQ,KAAKC,QAAQ,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAOD,QAAQ,CAACE,aAAa,KAAKD,QAAQ,CAACC,aAAa,IAAIF,QAAQ,CAACG,YAAY,KAAKF,QAAQ,CAACE,YAAY,IAAIH,QAAQ,CAACI,gBAAgB,KAAKH,QAAQ,CAACG,gBAAgB,IAAIJ,QAAQ,CAACK,eAAe,KAAKJ,QAAQ,CAACI,eAAe;AACjO,CAAC;AACD;AACA;AACA,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EAC7C,MAAMC,MAAM,GAAGtD,wBAAwB,CAAC,CAAC;EACzC,MAAMuD,SAAS,GAAGtD,gBAAgB,CAAC,CAAC;EACpC,MAAMuD,cAAc,GAAGtD,eAAe,CAACoD,MAAM,EAAEnD,oCAAoC,CAAC;EACpF,MAAMsD,OAAO,GAAGvD,eAAe,CAACoD,MAAM,EAAEvC,iCAAiC,CAAC;EAC1E,MAAM2C,iBAAiB,GAAGxD,eAAe,CAACoD,MAAM,EAAEtC,uCAAuC,CAAC;EAC1F,MAAM;IACJ2C,GAAG;IACHC,uBAAuB;IACvBC,wBAAwB,GAAG,CAAC;IAC5BC,wBAAwB,GAAGN,cAAc,CAAChC,MAAM;IAChDuC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,KAAK,GAAGlE,QAAQ,CAAC,CAAC;EACxB,MAAMmE,eAAe,GAAG/D,eAAe,CAACoD,MAAM,EAAEjD,2BAA2B,CAAC;EAC5E,MAAM6D,iBAAiB,GAAGhE,eAAe,CAACoD,MAAM,EAAElD,6BAA6B,CAAC;EAChF,MAAM+D,SAAS,GAAGjE,eAAe,CAACoD,MAAM,EAAEhD,qBAAqB,CAAC;EAChE,MAAM8D,YAAY,GAAGlE,eAAe,CAACoD,MAAM,EAAE/C,wBAAwB,CAAC;EACtE,MAAM8D,QAAQ,GAAGnE,eAAe,CAACoD,MAAM,EAAE1C,oBAAoB,CAAC;EAC9D,MAAM0D,kBAAkB,GAAGpE,eAAe,CAACoD,MAAM,EAAE3C,yBAAyB,CAAC;EAC7E,MAAM4D,WAAW,GAAG/D,kBAAkB,CAAC8C,MAAM,EAAEC,SAAS,CAAC;EACzD,MAAMiB,aAAa,GAAGlF,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,OAAO,GAAGpF,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,SAAS,GAAGlF,UAAU,CAACkE,GAAG,EAAEe,OAAO,CAAC;EAC1C,MAAM,CAACE,aAAa,EAAEC,qBAAqB,CAAC,GAAGvF,KAAK,CAACwF,QAAQ,CAAC,IAAI,CAAC;EACnE,MAAMC,iBAAiB,GAAGzF,KAAK,CAACmF,MAAM,CAACG,aAAa,CAAC;EACrD,MAAMI,cAAc,GAAG1F,KAAK,CAACmF,MAAM,CAAC;IAClCQ,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9F,KAAK,CAACwF,QAAQ,CAAC;IACnEO,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGjG,KAAK,CAACmF,MAAM,CAACP,iBAAiB,CAAC;EACtD;EACA;EACA;EACA;EACA;EACA,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGnG,KAAK,CAACwF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAMY,aAAa,GAAGpG,KAAK,CAACmF,MAAM,CAACkB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACvD,MAAMC,eAAe,GAAGvG,KAAK,CAACmF,MAAM,CAAC,CAAC;EACtC,MAAMqB,gBAAgB,GAAGxG,KAAK,CAACmF,MAAM,CAAC,CAAC;EACvC,MAAMsB,qBAAqB,GAAGzG,KAAK,CAACmF,MAAM,CAAC1E,cAAc,CAAC,CAACiG,OAAO,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,4BAA4B,KAAK;IAC3K;IACA;IACA;IACA,IAAIC,gCAAgC;IACpC,MAAMC,eAAe,GAAGP,OAAO,CAACQ,KAAK,CAACP,mBAAmB,EAAEC,kBAAkB,CAAC;IAC9E,IAAIG,4BAA4B,GAAG,CAAC,CAAC,EAAE;MACrC;MACA,IAAIJ,mBAAmB,GAAGI,4BAA4B,IAAIA,4BAA4B,IAAIF,cAAc,EAAE;QACxGG,gCAAgC,GAAGD,4BAA4B;MACjE;MACA;MAAA,KACK,IAAIH,kBAAkB,GAAGG,4BAA4B,IAAIA,4BAA4B,GAAGD,aAAa,EAAE;QAC1GE,gCAAgC,GAAGD,4BAA4B;MACjE;IACF;IACA,OAAO;MACLC,gCAAgC;MAChCC;IACF,CAAC;EACH,CAAC,EAAErD,eAAe,CAAC,CAAC;EACpB,MAAMmD,4BAA4B,GAAG/G,KAAK,CAACmH,OAAO,CAAC,MAAM;IACvD,IAAItC,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOX,cAAc,CAACkD,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKzC,SAAS,CAACyC,KAAK,CAAC;IAC7E;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACzC,SAAS,EAAEX,cAAc,CAAC,CAAC;EAC/B,MAAMqD,oBAAoB,GAAGvH,KAAK,CAACwH,WAAW,CAAC,MAAM;IACnD,IAAI,CAACrD,OAAO,EAAE;MACZ,OAAO;QACLX,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAEwB,WAAW,CAACwC,IAAI,CAACvF,MAAM;QACrCwB,gBAAgB,EAAE,CAAC;QACnBC,eAAe,EAAEO,cAAc,CAAChC;MAClC,CAAC;IACH;IACA,MAAM;MACJyD,GAAG;MACHC;IACF,CAAC,GAAGF,cAAc,CAACgC,OAAO;;IAE1B;IACA;IACA,MAAMlE,aAAa,GAAGlB,IAAI,CAACO,GAAG,CAAC8E,uBAAuB,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAEY,GAAG,CAAC,EAAEZ,QAAQ,CAAChD,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC;IAC1H,MAAMuB,YAAY,GAAGQ,SAAS,CAAC2D,UAAU,GAAGpE,aAAa,GAAGyB,WAAW,CAACwC,IAAI,CAACvF,MAAM,GAAGyF,uBAAuB,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAEY,GAAG,GAAGE,mBAAmB,CAACG,MAAM,CAAC;IAC9K,IAAItC,gBAAgB,GAAG,CAAC;IACxB,IAAIC,eAAe,GAAGgB,eAAe,CAACzC,MAAM;IAC5C,IAAIkC,iBAAiB,EAAE;MACrB,IAAIyD,oBAAoB,GAAG,KAAK;MAChC,MAAM,CAACC,gBAAgB,EAAEC,eAAe,CAAC,GAAGjF,oBAAoB,CAAC;QAC/DE,UAAU,EAAEQ,aAAa;QACzBP,SAAS,EAAEQ,YAAY;QACvBN,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACvF,MAAM;QACrCgB,MAAM,EAAEe,SAAS,CAAC+D;MACpB,CAAC,CAAC;MACF,KAAK,IAAIC,CAAC,GAAGH,gBAAgB,EAAEG,CAAC,GAAGF,eAAe,IAAI,CAACF,oBAAoB,EAAEI,CAAC,IAAI,CAAC,EAAE;QACnF,MAAMC,GAAG,GAAGjD,WAAW,CAACwC,IAAI,CAACQ,CAAC,CAAC;QAC/BJ,oBAAoB,GAAG7D,MAAM,CAAC0D,OAAO,CAACS,gBAAgB,CAACD,GAAG,CAACE,EAAE,CAAC;MAChE;MACA,IAAI,CAACP,oBAAoB,EAAE;QACzBnE,gBAAgB,GAAG7B,YAAY,CAACS,IAAI,CAACM,GAAG,CAACgD,IAAI,CAAC,EAAEjB,eAAe,CAAC;QAChEhB,eAAe,GAAG9B,YAAY,CAACS,IAAI,CAACM,GAAG,CAACgD,IAAI,CAAC,GAAGC,mBAAmB,CAACE,KAAK,EAAEpB,eAAe,CAAC;MAC7F;IACF;IACA,OAAO;MACLnB,aAAa;MACbC,YAAY;MACZC,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC,EAAE,CAACQ,OAAO,EAAEC,iBAAiB,EAAEW,QAAQ,EAAEd,SAAS,CAAC2D,UAAU,EAAE3D,SAAS,CAAC+D,SAAS,EAAE/C,WAAW,EAAEN,eAAe,EAAET,cAAc,CAAChC,MAAM,EAAE8B,MAAM,EAAE6B,mBAAmB,CAAC,CAAC;EACvKxF,iBAAiB,CAAC,MAAM;IACtB,IAAI8D,OAAO,EAAE;MACX;MACAiB,OAAO,CAACsC,OAAO,CAACW,UAAU,GAAG,CAAC;MAC9BjD,OAAO,CAACsC,OAAO,CAACY,SAAS,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLpD,aAAa,CAACwC,OAAO,CAACa,KAAK,CAACC,SAAS,+BAA+B;IACtE;EACF,CAAC,EAAE,CAACrE,OAAO,CAAC,CAAC;EACb9D,iBAAiB,CAAC,MAAM;IACtByF,sBAAsB,CAAC;MACrBC,KAAK,EAAEX,OAAO,CAACsC,OAAO,CAACe,WAAW;MAClCzC,MAAM,EAAEZ,OAAO,CAACsC,OAAO,CAACgB;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3D,QAAQ,CAAC4D,sBAAsB,CAAC,CAAC;EACrC,MAAMC,YAAY,GAAG5I,KAAK,CAACwH,WAAW,CAAC,MAAM;IAC3C,IAAIpC,OAAO,CAACsC,OAAO,EAAE;MACnB5B,sBAAsB,CAAC;QACrBC,KAAK,EAAEX,OAAO,CAACsC,OAAO,CAACe,WAAW;QAClCzC,MAAM,EAAEZ,OAAO,CAACsC,OAAO,CAACgB;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EACNvH,sBAAsB,CAAC6C,MAAM,EAAE,iBAAiB,EAAE4E,YAAY,CAAC;EAC/D,MAAMC,wBAAwB,GAAG7I,KAAK,CAACwH,WAAW,CAACsB,iBAAiB,IAAI;IACtE,MAAM,CAAChB,gBAAgB,EAAEC,eAAe,CAAC,GAAGjF,oBAAoB,CAAC;MAC/DE,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACvF,MAAM;MACrCgB,MAAM,EAAEe,SAAS,CAAC+D;IACpB,CAAC,CAAC;IACF,MAAM,CAACe,0BAA0B,CAAC,GAAGjG,oBAAoB,CAAC;MACxDE,UAAU,EAAE8F,iBAAiB,CAACpF,gBAAgB;MAC9CT,SAAS,EAAE6F,iBAAiB,CAACnF,eAAe;MAC5CR,aAAa,EAAEoB,wBAAwB;MACvCnB,YAAY,EAAEoB,wBAAwB;MACtCtB,MAAM,EAAEe,SAAS,CAAC+E;IACpB,CAAC,CAAC;IACF,MAAMrC,mBAAmB,GAAGpF,gCAAgC,CAAC;MAC3DoF,mBAAmB,EAAEoC,0BAA0B;MAC/C/E,MAAM;MACN8D,gBAAgB;MAChBC,eAAe;MACfkB,WAAW,EAAEhE,WAAW,CAACwC;IAC3B,CAAC,CAAC;IACF,MAAMyB,SAAS,GAAGxE,KAAK,CAACwE,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,MAAMvD,GAAG,GAAGrE,oBAAoB,CAAC0C,MAAM,CAAC0D,OAAO,CAACyB,KAAK,CAAC,CAACpH,SAAS,CAAC+F,gBAAgB,CAAC;IAClF,MAAMlC,IAAI,GAAGsD,SAAS,GAAGnI,2BAA2B,CAACiD,MAAM,CAAC,CAAC2C,mBAAmB,CAAC,CAAC,CAAC;IACnFzB,aAAa,CAACwC,OAAO,CAACa,KAAK,CAACC,SAAS,kBAAAY,MAAA,CAAkBxD,IAAI,UAAAwD,MAAA,CAAOzD,GAAG,aAAU;IAC/E,IAAI,OAAOrB,uBAAuB,KAAK,UAAU,EAAE;MACjDA,uBAAuB,CAAC;QACtBqB,GAAG;QACHC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEiB,WAAW,CAACwC,IAAI,EAAEnD,uBAAuB,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEP,SAAS,CAAC+E,YAAY,EAAE/E,SAAS,CAAC+D,SAAS,EAAEtD,KAAK,CAACwE,SAAS,CAAC,CAAC;EACzK,MAAMG,gBAAgB,GAAGrJ,KAAK,CAACwH,WAAW,CAAC,MAAM/B,iBAAiB,CAACiC,OAAO,EAAE,EAAE,CAAC;EAC/E,MAAM4B,gBAAgB,GAAGtJ,KAAK,CAACwH,WAAW,CAACsB,iBAAiB,IAAI;IAC9D,IAAIrD,iBAAiB,CAACiC,OAAO,IAAIrE,sBAAsB,CAACyF,iBAAiB,EAAErD,iBAAiB,CAACiC,OAAO,CAAC,EAAE;MACrGmB,wBAAwB,CAACC,iBAAiB,CAAC;MAC3C;IACF;IACAvD,qBAAqB,CAACuD,iBAAiB,CAAC;IACxCD,wBAAwB,CAACC,iBAAiB,CAAC;IAC3C,MAAM,CAAChB,gBAAgB,EAAEC,eAAe,CAAC,GAAGjF,oBAAoB,CAAC;MAC/DE,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACvF,MAAM;MACrCgB,MAAM,EAAEe,SAAS,CAAC+D;IACpB,CAAC,CAAC;IACFhE,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,4BAA4B,EAAE;MACxDzB,gBAAgB;MAChBC;IACF,CAAC,CAAC;IACFtC,iBAAiB,CAACiC,OAAO,GAAGoB,iBAAiB;EAC/C,CAAC,EAAE,CAAC9E,MAAM,EAAEuB,qBAAqB,EAAEE,iBAAiB,EAAER,WAAW,CAACwC,IAAI,CAACvF,MAAM,EAAE+B,SAAS,CAAC+D,SAAS,EAAEa,wBAAwB,CAAC,CAAC;EAC9HxI,iBAAiB,CAAC,MAAM;IACtB,IAAIwF,mBAAmB,CAACE,KAAK,IAAI,IAAI,EAAE;MACrC;IACF;IACA,MAAMyD,oBAAoB,GAAGjC,oBAAoB,CAAC,CAAC;IACnD+B,gBAAgB,CAACE,oBAAoB,CAAC;IACtC,MAAM;MACJ7D,GAAG;MACHC;IACF,CAAC,GAAGF,cAAc,CAACgC,OAAO;IAC1B,MAAM+B,MAAM,GAAG;MACb9D,GAAG;MACHC,IAAI;MACJN,aAAa,EAAEkE;IACjB,CAAC;IACDxF,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,sBAAsB,EAAEE,MAAM,CAAC;EAC7D,CAAC,EAAE,CAACzF,MAAM,EAAEuD,oBAAoB,EAAE1B,mBAAmB,CAACE,KAAK,EAAEuD,gBAAgB,CAAC,CAAC;EAC/E,MAAMI,YAAY,GAAGnJ,gBAAgB,CAACoJ,KAAK,IAAI;IAC7C,MAAM;MACJrB,SAAS;MACTD;IACF,CAAC,GAAGsB,KAAK,CAACC,aAAa;IACvBlE,cAAc,CAACgC,OAAO,CAAC/B,GAAG,GAAG2C,SAAS;IACtC5C,cAAc,CAACgC,OAAO,CAAC9B,IAAI,GAAGyC,UAAU;;IAExC;IACA,IAAI,CAAC5C,iBAAiB,CAACiC,OAAO,IAAIY,SAAS,GAAG,CAAC,EAAE;MAC/C;IACF;IACA,IAAI5D,KAAK,CAACwE,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAIb,UAAU,GAAG,CAAC,EAAE;QAClB;MACF;IACF;IACA,IAAI3D,KAAK,CAACwE,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAIb,UAAU,GAAG,CAAC,EAAE;QAClB;MACF;IACF;;IAEA;IACA,MAAMS,iBAAiB,GAAG3E,OAAO,GAAGoD,oBAAoB,CAAC,CAAC,GAAG9B,iBAAiB,CAACiC,OAAO;IACtF,MAAMmC,kCAAkC,GAAGvH,IAAI,CAACM,GAAG,CAACkG,iBAAiB,CAACtF,aAAa,GAAGiC,iBAAiB,CAACiC,OAAO,CAAClE,aAAa,CAAC;IAC9H,MAAMsG,qCAAqC,GAAGxH,IAAI,CAACM,GAAG,CAACkG,iBAAiB,CAACrF,YAAY,GAAGgC,iBAAiB,CAACiC,OAAO,CAACjE,YAAY,CAAC;IAC/H,MAAMsG,qCAAqC,GAAGzH,IAAI,CAACM,GAAG,CAACkG,iBAAiB,CAACpF,gBAAgB,GAAG+B,iBAAiB,CAACiC,OAAO,CAAChE,gBAAgB,CAAC;IACvI,MAAMsG,wCAAwC,GAAG1H,IAAI,CAACM,GAAG,CAACkG,iBAAiB,CAACnF,eAAe,GAAG8B,iBAAiB,CAACiC,OAAO,CAAC/D,eAAe,CAAC;IACxI,MAAMsG,cAAc,GAAGJ,kCAAkC,IAAI5F,SAAS,CAACiG,YAAY,IAAIJ,qCAAqC,IAAI7F,SAAS,CAACiG,YAAY,IAAIH,qCAAqC,IAAI9F,SAAS,CAACkG,eAAe,IAAIH,wCAAwC,IAAI/F,SAAS,CAACkG,eAAe,IAAIlE,cAAc,CAACyB,OAAO,KAAK9C,iBAAiB;IACrVZ,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,sBAAsB,EAAE;MAClD5D,GAAG,EAAE2C,SAAS;MACd1C,IAAI,EAAEyC,UAAU;MAChB/C,aAAa,EAAE2E,cAAc,GAAGnB,iBAAiB,GAAGrD,iBAAiB,CAACiC;IACxE,CAAC,EAAEiC,KAAK,CAAC;IACT,IAAIM,cAAc,EAAE;MAClB;MACAhK,QAAQ,CAACmK,SAAS,CAAC,MAAM;QACvBd,gBAAgB,CAACR,iBAAiB,CAAC;MACrC,CAAC,CAAC;MACF7C,cAAc,CAACyB,OAAO,GAAG9C,iBAAiB;IAC5C;EACF,CAAC,CAAC;EACF,MAAMyF,WAAW,GAAG9J,gBAAgB,CAACoJ,KAAK,IAAI;IAC5C3F,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAEI,KAAK,CAAC;EAChE,CAAC,CAAC;EACF,MAAMW,eAAe,GAAG/J,gBAAgB,CAACoJ,KAAK,IAAI;IAChD3F,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAEI,KAAK,CAAC;EACpE,CAAC,CAAC;EACF,MAAMY,yBAAyB,GAAGvK,KAAK,CAACmH,OAAO,CAAC,MAAM;IACpD,IAAItC,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOI,WAAW,CAACwC,IAAI,CAACL,SAAS,CAACc,GAAG,IAAIA,GAAG,CAACE,EAAE,KAAKvD,SAAS,CAACuD,EAAE,CAAC;IACnE;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACvD,SAAS,EAAEI,WAAW,CAACwC,IAAI,CAAC,CAAC;EACjCtG,sBAAsB,CAAC6C,MAAM,EAAE,cAAc,EAAE,CAACyF,MAAM,EAAEE,KAAK,KAAK;IAChE,IAAIa,UAAU;IACd,IAAIb,KAAK,CAACC,aAAa,CAACa,QAAQ,CAACd,KAAK,CAACe,aAAa,CAAC,EAAE;MACrD;IACF;IACAvE,eAAe,CAAC,CAACqE,UAAU,GAAGf,MAAM,CAACrB,EAAE,KAAK,IAAI,GAAGoC,UAAU,GAAG,IAAI,CAAC;EACvE,CAAC,CAAC;EACFrJ,sBAAsB,CAAC6C,MAAM,EAAE,aAAa,EAAE,CAACyF,MAAM,EAAEE,KAAK,KAAK;IAC/D,IAAIA,KAAK,CAACC,aAAa,CAACa,QAAQ,CAACd,KAAK,CAACe,aAAa,CAAC,EAAE;MACrD;IACF;IACAvE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EACF,MAAMwE,OAAO,GAAG,SAAAA,CAAA,EAEV;IAAA,IAFWlB,MAAM,GAAAxH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;MACxBqD;IACF,CAAC;IACC,IAAIsF,oBAAoB;IACxB,MAAM;MACJC,WAAW;MACXvF,aAAa,EAAEwD,iBAAiB;MAChCjC,cAAc,GAAGtC,wBAAwB;MACzCuC,aAAa,GAAGtC,wBAAwB;MACxCsG,cAAc,GAAGjF,mBAAmB,CAACE,KAAK;MAC1CgF,cAAc,GAAG,CAAC;MAClBC,QAAQ,GAAG;IACb,CAAC,GAAGvB,MAAM;IACV,IAAI,CAACX,iBAAiB,IAAIgC,cAAc,IAAI,IAAI,EAAE;MAChD,OAAO,IAAI;IACb;IACA,MAAM9C,SAAS,GAAG7D,OAAO,GAAGF,SAAS,CAAC+D,SAAS,GAAG,CAAC;IACnD,MAAMgB,YAAY,GAAG7E,OAAO,GAAGF,SAAS,CAAC+E,YAAY,GAAG,CAAC;IACzD,MAAM,CAAClB,gBAAgB,EAAEC,eAAe,CAAC,GAAGjF,oBAAoB,CAAC;MAC/DE,UAAU,EAAE8F,iBAAiB,CAACtF,aAAa;MAC3CP,SAAS,EAAE6F,iBAAiB,CAACrF,YAAY;MACzCN,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE6B,WAAW,CAACwC,IAAI,CAACvF,MAAM;MACrCgB,MAAM,EAAE8E;IACV,CAAC,CAAC;IACF,MAAMiD,YAAY,GAAG,EAAE;IACvB,IAAIxB,MAAM,CAAChC,IAAI,EAAE;MACfgC,MAAM,CAAChC,IAAI,CAACyD,OAAO,CAAChD,GAAG,IAAI;QACzB+C,YAAY,CAACE,IAAI,CAACjD,GAAG,CAAC;QACtBlE,MAAM,CAAC0D,OAAO,CAAC0D,gBAAgB,CAAC;UAC9BC,KAAK,EAAEnD,GAAG,CAACE,EAAE;UACbvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACe,WAAW,CAACqG,KAAK,EAAE;QACtB,OAAO,IAAI;MACb;MACA,KAAK,IAAIrD,CAAC,GAAGH,gBAAgB,EAAEG,CAAC,GAAGF,eAAe,EAAEE,CAAC,IAAI,CAAC,EAAE;QAC1D,MAAMC,GAAG,GAAGjD,WAAW,CAACwC,IAAI,CAACQ,CAAC,CAAC;QAC/BgD,YAAY,CAACE,IAAI,CAACjD,GAAG,CAAC;QACtBlE,MAAM,CAAC0D,OAAO,CAAC0D,gBAAgB,CAAC;UAC9BC,KAAK,EAAEnD,GAAG,CAACE,EAAE;UACbvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ;IACF;IACA;IACA;IACA;;IAEA,IAAIqH,8BAA8B,GAAG,KAAK;IAC1C,IAAIhB,yBAAyB,GAAG,CAAC,CAAC,EAAE;MAClC,MAAMiB,kBAAkB,GAAGvG,WAAW,CAACwC,IAAI,CAAC8C,yBAAyB,CAAC;MACtE,IAAIzC,gBAAgB,GAAGyC,yBAAyB,IAAIxC,eAAe,GAAGwC,yBAAyB,EAAE;QAC/FgB,8BAA8B,GAAG,IAAI;QACrC,IAAIhB,yBAAyB,GAAGzC,gBAAgB,EAAE;UAChDmD,YAAY,CAACE,IAAI,CAACK,kBAAkB,CAAC;QACvC,CAAC,MAAM;UACLP,YAAY,CAACQ,OAAO,CAACD,kBAAkB,CAAC;QAC1C;QACAxH,MAAM,CAAC0D,OAAO,CAAC0D,gBAAgB,CAAC;UAC9BC,KAAK,EAAEG,kBAAkB,CAACpD,EAAE;UAC5BvB,cAAc;UACdC,aAAa;UACbJ,OAAO,EAAExC;QACX,CAAC,CAAC;MACJ;IACF;IACA,MAAM,CAAC6E,0BAA0B,EAAEnC,kBAAkB,CAAC,GAAG9D,oBAAoB,CAAC;MAC5EE,UAAU,EAAE8F,iBAAiB,CAACpF,gBAAgB;MAC9CT,SAAS,EAAE6F,iBAAiB,CAACnF,eAAe;MAC5CR,aAAa,EAAE0D,cAAc;MAC7BzD,YAAY,EAAE0D,aAAa;MAC3B5D,MAAM,EAAE8F;IACV,CAAC,CAAC;IACF,MAAMrC,mBAAmB,GAAGpF,gCAAgC,CAAC;MAC3DoF,mBAAmB,EAAEoC,0BAA0B;MAC/C/E,MAAM;MACN8D,gBAAgB;MAChBC,eAAe;MACfkB,WAAW,EAAEhE,WAAW,CAACwC;IAC3B,CAAC,CAAC;IACF,IAAIiE,gCAAgC,GAAG,KAAK;IAC5C,IAAI/E,mBAAmB,GAAGI,4BAA4B,IAAIH,kBAAkB,GAAGG,4BAA4B,EAAE;MAC3G2E,gCAAgC,GAAG,IAAI;IACzC;IACA,MAAM;MACJ1E,gCAAgC;MAChCC;IACF,CAAC,GAAGR,qBAAqB,CAACiB,OAAO,CAACxD,cAAc,EAAEyC,mBAAmB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAE4E,gCAAgC,GAAG3E,4BAA4B,GAAG,CAAC,CAAC,CAAC;IAC/L,MAAM4E,IAAI,GAAG,CAAC,CAACf,oBAAoB,GAAG3G,SAAS,CAAC2H,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhB,oBAAoB,CAAC1C,GAAG,KAAK,CAAC,CAAC;MAC3G;QACEK,KAAK,EAAEsD;MACT,CAAC,GAAGF,IAAI;MACRG,YAAY,GAAGjM,6BAA6B,CAAC8L,IAAI,EAAE7L,SAAS,CAAC;IAC/D,MAAMiM,yBAAyB,GAAGxF,eAAe,CAACmB,OAAO,KAAKjD,WAAW,IAAI+B,gBAAgB,CAACkB,OAAO,KAAKmE,YAAY;IACtH,IAAIE,yBAAyB,EAAE;MAC7B3F,aAAa,CAACsB,OAAO,GAAGrB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7C;IACA,MAAMmB,IAAI,GAAG,EAAE;IACf,IAAIuE,4BAA4B,GAAG,KAAK;IACxC,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,YAAY,CAAC/I,MAAM,EAAE+F,CAAC,IAAI,CAAC,EAAE;MAC/C,IAAIgE,kBAAkB;MACtB,MAAM;QACJ7D,EAAE;QACF8D;MACF,CAAC,GAAGjB,YAAY,CAAChD,CAAC,CAAC;MACnB,MAAMkE,eAAe,GAAGZ,8BAA8B,IAAI1G,SAAS,CAACuD,EAAE,KAAKA,EAAE;MAC7E,MAAMgE,mBAAmB,GAAGb,8BAA8B,GAAGzD,gBAAgB,GAAGG,CAAC,KAAKhD,WAAW,CAACwC,IAAI,CAACvF,MAAM,GAAG4F,gBAAgB,GAAGG,CAAC,KAAKhD,WAAW,CAACwC,IAAI,CAACvF,MAAM,GAAG,CAAC;MACpK,MAAMmK,aAAa,GAAG,CAACrI,MAAM,CAAC0D,OAAO,CAACS,gBAAgB,CAACC,EAAE,CAAC,GAAGpE,MAAM,CAAC0D,OAAO,CAAC4E,qBAAqB,CAAClE,EAAE,CAAC,GAAG,MAAM;MAC9G,IAAImE,UAAU;MACd,IAAIvH,kBAAkB,CAACoD,EAAE,CAAC,IAAI,IAAI,EAAE;QAClCmE,UAAU,GAAG,KAAK;MACpB,CAAC,MAAM;QACLA,UAAU,GAAGvI,MAAM,CAAC0D,OAAO,CAAC8E,eAAe,CAACpE,EAAE,CAAC;MACjD;MACA,IAAIyC,WAAW,EAAE;QACfA,WAAW,CAACzC,EAAE,CAAC;MACjB;MACA,MAAMqE,WAAW,GAAG5H,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACuD,EAAE,KAAKA,EAAE,GAAGvD,SAAS,CAACyC,KAAK,GAAG,IAAI;MACtF,MAAMoF,+BAA+B,GAAG1F,gCAAgC,KAAK7E,SAAS,IAAI+B,cAAc,CAAC8C,gCAAgC,CAAC;MAC1I,MAAM2F,8BAA8B,GAAGD,+BAA+B,IAAID,WAAW,GAAG,CAACC,+BAA+B,EAAE,GAAGzF,eAAe,CAAC,GAAGA,eAAe;MAC/J,IAAI2F,YAAY,GAAG,IAAI;MACvB,IAAI9H,YAAY,KAAK,IAAI,IAAIA,YAAY,CAACsD,EAAE,KAAKA,EAAE,EAAE;QACnD,MAAMyE,UAAU,GAAG7I,MAAM,CAAC0D,OAAO,CAACoF,aAAa,CAAC1E,EAAE,EAAEtD,YAAY,CAACwC,KAAK,CAAC;QACvEsF,YAAY,GAAGC,UAAU,CAACE,QAAQ,KAAK,MAAM,GAAGjI,YAAY,CAACwC,KAAK,GAAG,IAAI;MAC3E;MACA,MAAM0F,KAAK,GAAG,OAAOvI,WAAW,KAAK,UAAU,IAAIA,WAAW,CAAC2D,EAAE,EAAE8D,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7E;UACE3D,KAAK,EAAE0E;QACT,CAAC,GAAGD,KAAK;QACTE,QAAQ,GAAGrN,6BAA6B,CAACmN,KAAK,EAAEjN,UAAU,CAAC;MAC7D,IAAI,CAACqG,aAAa,CAACsB,OAAO,CAACU,EAAE,CAAC,EAAE;QAC9B,MAAMG,KAAK,GAAG3I,QAAQ,CAAC,CAAC,CAAC,EAAEqN,QAAQ,EAAEpB,YAAY,CAAC;QAClDzF,aAAa,CAACsB,OAAO,CAACU,EAAE,CAAC,GAAGG,KAAK;MACnC;MACA,IAAI7F,KAAK,GAAGqI,cAAc,IAAI,CAAC9F,WAAW,IAAI,IAAI,IAAI,CAACgH,kBAAkB,GAAGhH,WAAW,CAACqG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,kBAAkB,CAACzI,aAAa,KAAK,CAAC,CAAC,GAAGsE,gBAAgB,GAAGG,CAAC;MAChL,IAAIsD,8BAA8B,IAAI,CAAC1G,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACuD,EAAE,MAAMA,EAAE,EAAE;QACxF1F,KAAK,GAAG6H,yBAAyB;QACjCyB,4BAA4B,GAAG,IAAI;MACrC,CAAC,MAAM,IAAIA,4BAA4B,EAAE;QACvCtJ,KAAK,IAAI,CAAC;MACZ;MACA+E,IAAI,CAAC0D,IAAI,CAAE,aAAavJ,IAAI,CAACqC,SAAS,CAACkJ,KAAK,CAACjF,GAAG,EAAEtI,QAAQ,CAAC;QACzDsI,GAAG,EAAEgE,KAAK;QACVb,KAAK,EAAEjD,EAAE;QACTpB,gCAAgC,EAAEA,gCAAgC;QAClEoG,YAAY,EAAEjB,eAAe;QAC7BkB,SAAS,EAAEhB,aAAa;QACxBI,WAAW,EAAEA,WAAW;QACxBG,YAAY,EAAEA,YAAY;QAC1B3F,eAAe,EAAE0F,8BAA8B;QAC/CzI,cAAc,EAAEA,cAAc;QAC9ByC,mBAAmB,EAAEA,mBAAmB;QACxCC,kBAAkB,EAAEA,kBAAkB;QACtC0G,QAAQ,EAAEf,UAAU;QACpB7J,KAAK,EAAEA,KAAK;QACZ6K,cAAc,EAAEzC,cAAc;QAC9B0C,aAAa,EAAEpB,mBAAmB;QAClCpB,QAAQ,EAAEA;MACZ,CAAC,EAAEkC,QAAQ,EAAEpB,YAAY,EAAE;QACzB2B,OAAO,EAAEvH,YAAY,KAAKkC,EAAE;QAC5BG,KAAK,EAAEnC,aAAa,CAACsB,OAAO,CAACU,EAAE;MACjC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC;IACV;IACA7B,eAAe,CAACmB,OAAO,GAAGjD,WAAW;IACrC+B,gBAAgB,CAACkB,OAAO,GAAGmE,YAAY;IACvC,OAAOpE,IAAI;EACb,CAAC;EACD,MAAMiG,wBAAwB,GAAG7H,mBAAmB,CAACE,KAAK,IAAInB,iBAAiB,IAAIiB,mBAAmB,CAACE,KAAK;EAC5G,MAAM4H,WAAW,GAAG3N,KAAK,CAACmH,OAAO,CAAC,MAAM;IACtC;IACA;IACA;IACA,MAAMnB,MAAM,GAAG1D,IAAI,CAACsL,GAAG,CAAC7I,QAAQ,CAAC4D,sBAAsB,EAAE,CAAC,CAAC;IAC3D,IAAIkF,mBAAmB,GAAG,KAAK;IAC/B,IAAIzI,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACsC,OAAO,IAAI1B,MAAM,KAAKZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsC,OAAO,CAACgB,YAAY,CAAC,EAAE;MAC7GmF,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAMC,IAAI,GAAG;MACX/H,KAAK,EAAE2H,wBAAwB,GAAG9I,iBAAiB,GAAG,MAAM;MAC5DoB,MAAM;MACN+H,SAAS,EAAEF,mBAAmB,GAAG,MAAM,GAAG;IAC5C,CAAC;IACD,IAAI5J,SAAS,CAAC2D,UAAU,IAAI3C,WAAW,CAACwC,IAAI,CAACvF,MAAM,KAAK,CAAC,EAAE;MACzD4L,IAAI,CAAC9H,MAAM,GAAGxE,uBAAuB,CAACwC,MAAM,EAAEC,SAAS,CAACoJ,SAAS,CAAC,CAAC,CAAC;IACtE;IACA,OAAOS,IAAI;EACb,CAAC,EAAE,CAAC9J,MAAM,EAAEoB,OAAO,EAAER,iBAAiB,EAAEG,QAAQ,CAAC4D,sBAAsB,EAAE+E,wBAAwB,EAAEzJ,SAAS,CAAC2D,UAAU,EAAE3D,SAAS,CAACoJ,SAAS,EAAEpI,WAAW,CAACwC,IAAI,CAACvF,MAAM,CAAC,CAAC;EACvKlC,KAAK,CAACgO,SAAS,CAAC,MAAM;IACpBhK,MAAM,CAAC0D,OAAO,CAAC6B,YAAY,CAAC,kCAAkC,CAAC;EACjE,CAAC,EAAE,CAACvF,MAAM,EAAE2J,WAAW,CAAC,CAAC;EACzB,MAAMM,SAAS,GAAGjO,KAAK,CAACmH,OAAO,CAAC,MAAM;IACpC,MAAMoB,KAAK,GAAG,CAAC,CAAC;IAChB,IAAI,CAACmF,wBAAwB,EAAE;MAC7BnF,KAAK,CAAC2F,SAAS,GAAG,QAAQ;IAC5B;IACA,IAAIjK,SAAS,CAAC2D,UAAU,EAAE;MACxBW,KAAK,CAAC4F,SAAS,GAAG,QAAQ;IAC5B;IACA,OAAO5F,KAAK;EACd,CAAC,EAAE,CAACmF,wBAAwB,EAAEzJ,SAAS,CAAC2D,UAAU,CAAC,CAAC;EACpD5D,MAAM,CAAC0D,OAAO,CAAC0G,QAAQ,CAAC,SAAS,EAAE;IACjC/E;EACF,CAAC,CAAC;EACF,OAAO;IACL/D,aAAa;IACbuD,wBAAwB;IACxB8B,OAAO;IACP0D,YAAY,EAAE,SAAAA,CAAA;MAAA,IAACC,UAAU,GAAArM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAKrC,QAAQ,CAAC;QAC1CyE,GAAG,EAAEgB,SAAS;QACdkJ,QAAQ,EAAE7E,YAAY;QACtB8E,OAAO,EAAEnE,WAAW;QACpBoE,WAAW,EAAEnE;MACf,CAAC,EAAEgE,UAAU,EAAE;QACb/F,KAAK,EAAE+F,UAAU,CAAC/F,KAAK,GAAG3I,QAAQ,CAAC,CAAC,CAAC,EAAE0O,UAAU,CAAC/F,KAAK,EAAE0F,SAAS,CAAC,GAAGA,SAAS;QAC/ES,IAAI,EAAE;MACR,CAAC,CAAC;IAAA;IACFC,eAAe,EAAE,SAAAA,CAAA;MAAA,IAAC;QAChBpG;MACF,CAAC,GAAAtG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAM;QACVsG,KAAK,EAAEA,KAAK,GAAG3I,QAAQ,CAAC,CAAC,CAAC,EAAE2I,KAAK,EAAEoF,WAAW,CAAC,GAAGA,WAAW;QAC7De,IAAI,EAAE;MACR,CAAC;IAAA,CAAC;IACFE,kBAAkB,EAAEA,CAAA,MAAO;MACzBvK,GAAG,EAAEa,aAAa;MAClBwJ,IAAI,EAAE;IACR,CAAC;EACH,CAAC;AACH,CAAC;AACD,SAAS/G,uBAAuBA,CAAC3D,MAAM,EAAEiB,WAAW,EAAEF,QAAQ,EAAEjD,MAAM,EAAE;EACtE,IAAI+M,mBAAmB,EAAEC,mBAAmB;EAC5C,MAAMC,kCAAkC,GAAG/K,MAAM,CAAC0D,OAAO,CAACsH,uBAAuB,CAAC,CAAC;EACnF,IAAIC,eAAe,GAAGF,kCAAkC,KAAKG,QAAQ;EACrE,IAAI,CAACL,mBAAmB,GAAG5J,WAAW,CAACqG,KAAK,KAAK,IAAI,IAAIuD,mBAAmB,CAACpL,YAAY,IAAI,CAACwL,eAAe,EAAE;IAC7G;IACAA,eAAe,GAAGF,kCAAkC,IAAI9J,WAAW,CAACqG,KAAK,CAAC7H,YAAY;EACxF;EACA,MAAM0L,sCAAsC,GAAG/N,KAAK,CAAC2N,kCAAkC,IAAI,CAAC,CAACD,mBAAmB,GAAG7J,WAAW,CAACqG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwD,mBAAmB,CAACtL,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEuB,QAAQ,CAAChD,SAAS,CAACG,MAAM,CAAC;EAChO,IAAI+M,eAAe,IAAIlK,QAAQ,CAAChD,SAAS,CAACoN,sCAAsC,CAAC,IAAIrN,MAAM,EAAE;IAC3F;IACA;IACA,OAAOD,YAAY,CAACC,MAAM,EAAEiD,QAAQ,CAAChD,SAAS,CAAC;EACjD;;EAEA;EACA;EACA;EACA;EACA,OAAOU,iBAAiB,CAACX,MAAM,EAAEiD,QAAQ,CAAChD,SAAS,EAAEoN,sCAAsC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}