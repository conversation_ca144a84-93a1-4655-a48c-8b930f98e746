{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onClick\"];\nimport * as React from 'react';\nimport { unstable_useId as useId } from '@mui/material/utils';\nimport { useGridSelector } from '../../hooks/utils/useGridSelector';\nimport { gridPreferencePanelStateSelector } from '../../hooks/features/preferencesPanel/gridPreferencePanelSelector';\nimport { GridPreferencePanelsValue } from '../../hooks/features/preferencesPanel/gridPreferencePanelsValue';\nimport { useGridApiContext } from '../../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridToolbarColumnsButton = /*#__PURE__*/React.forwardRef(function GridToolbarColumnsButton(props, ref) {\n  var _rootProps$slotProps;\n  const {\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const columnButtonId = useId();\n  const columnPanelId = useId();\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const showColumns = event => {\n    if (preferencePanel.open && preferencePanel.openedPanelValue === GridPreferencePanelsValue.columns) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.columns, columnPanelId, columnButtonId);\n    }\n    onClick == null || onClick(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === columnPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n    ref: ref,\n    id: columnButtonId,\n    size: \"small\",\n    \"aria-label\": apiRef.current.getLocaleText('toolbarColumnsLabel'),\n    \"aria-haspopup\": \"menu\",\n    \"aria-expanded\": isOpen,\n    \"aria-controls\": isOpen ? columnPanelId : undefined,\n    startIcon: /*#__PURE__*/_jsx(rootProps.slots.columnSelectorIcon, {})\n  }, other, {\n    onClick: showColumns\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseButton, {\n    children: apiRef.current.getLocaleText('toolbarColumns')\n  }));\n});", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "unstable_useId", "useId", "useGridSelector", "gridPreferencePanelStateSelector", "GridPreferencePanelsValue", "useGridApiContext", "useGridRootProps", "jsx", "_jsx", "GridToolbarColumnsButton", "forwardRef", "props", "ref", "_rootProps$slotProps", "onClick", "other", "columnButtonId", "columnPanelId", "apiRef", "rootProps", "preferencePanel", "showColumns", "event", "open", "openedPanelValue", "columns", "current", "hidePreferences", "showPreferences", "disableColumnSelector", "isOpen", "panelId", "slots", "baseButton", "id", "size", "getLocaleText", "undefined", "startIcon", "columnSelectorIcon", "slotProps", "children"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/toolbar/GridToolbarColumnsButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onClick\"];\nimport * as React from 'react';\nimport { unstable_useId as useId } from '@mui/material/utils';\nimport { useGridSelector } from '../../hooks/utils/useGridSelector';\nimport { gridPreferencePanelStateSelector } from '../../hooks/features/preferencesPanel/gridPreferencePanelSelector';\nimport { GridPreferencePanelsValue } from '../../hooks/features/preferencesPanel/gridPreferencePanelsValue';\nimport { useGridApiContext } from '../../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridToolbarColumnsButton = /*#__PURE__*/React.forwardRef(function GridToolbarColumnsButton(props, ref) {\n  var _rootProps$slotProps;\n  const {\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const columnButtonId = useId();\n  const columnPanelId = useId();\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const showColumns = event => {\n    if (preferencePanel.open && preferencePanel.openedPanelValue === GridPreferencePanelsValue.columns) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.columns, columnPanelId, columnButtonId);\n    }\n    onClick == null || onClick(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === columnPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n    ref: ref,\n    id: columnButtonId,\n    size: \"small\",\n    \"aria-label\": apiRef.current.getLocaleText('toolbarColumnsLabel'),\n    \"aria-haspopup\": \"menu\",\n    \"aria-expanded\": isOpen,\n    \"aria-controls\": isOpen ? columnPanelId : undefined,\n    startIcon: /*#__PURE__*/_jsx(rootProps.slots.columnSelectorIcon, {})\n  }, other, {\n    onClick: showColumns\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseButton, {\n    children: apiRef.current.getLocaleText('toolbarColumns')\n  }));\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,QAAQ,qBAAqB;AAC7D,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,gCAAgC,QAAQ,mEAAmE;AACpH,SAASC,yBAAyB,QAAQ,iEAAiE;AAC3G,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,wBAAwB,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,SAASD,wBAAwBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAClH,IAAIC,oBAAoB;EACxB,MAAM;MACFC;IACF,CAAC,GAAGH,KAAK;IACTI,KAAK,GAAGlB,6BAA6B,CAACc,KAAK,EAAEb,SAAS,CAAC;EACzD,MAAMkB,cAAc,GAAGf,KAAK,CAAC,CAAC;EAC9B,MAAMgB,aAAa,GAAGhB,KAAK,CAAC,CAAC;EAC7B,MAAMiB,MAAM,GAAGb,iBAAiB,CAAC,CAAC;EAClC,MAAMc,SAAS,GAAGb,gBAAgB,CAAC,CAAC;EACpC,MAAMc,eAAe,GAAGlB,eAAe,CAACgB,MAAM,EAAEf,gCAAgC,CAAC;EACjF,MAAMkB,WAAW,GAAGC,KAAK,IAAI;IAC3B,IAAIF,eAAe,CAACG,IAAI,IAAIH,eAAe,CAACI,gBAAgB,KAAKpB,yBAAyB,CAACqB,OAAO,EAAE;MAClGP,MAAM,CAACQ,OAAO,CAACC,eAAe,CAAC,CAAC;IAClC,CAAC,MAAM;MACLT,MAAM,CAACQ,OAAO,CAACE,eAAe,CAACxB,yBAAyB,CAACqB,OAAO,EAAER,aAAa,EAAED,cAAc,CAAC;IAClG;IACAF,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACQ,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,IAAIH,SAAS,CAACU,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,MAAMC,MAAM,GAAGV,eAAe,CAACG,IAAI,IAAIH,eAAe,CAACW,OAAO,KAAKd,aAAa;EAChF,OAAO,aAAaT,IAAI,CAACW,SAAS,CAACa,KAAK,CAACC,UAAU,EAAErC,QAAQ,CAAC;IAC5DgB,GAAG,EAAEA,GAAG;IACRsB,EAAE,EAAElB,cAAc;IAClBmB,IAAI,EAAE,OAAO;IACb,YAAY,EAAEjB,MAAM,CAACQ,OAAO,CAACU,aAAa,CAAC,qBAAqB,CAAC;IACjE,eAAe,EAAE,MAAM;IACvB,eAAe,EAAEN,MAAM;IACvB,eAAe,EAAEA,MAAM,GAAGb,aAAa,GAAGoB,SAAS;IACnDC,SAAS,EAAE,aAAa9B,IAAI,CAACW,SAAS,CAACa,KAAK,CAACO,kBAAkB,EAAE,CAAC,CAAC;EACrE,CAAC,EAAExB,KAAK,EAAE;IACRD,OAAO,EAAEO;EACX,CAAC,EAAE,CAACR,oBAAoB,GAAGM,SAAS,CAACqB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3B,oBAAoB,CAACoB,UAAU,EAAE;IAClGQ,QAAQ,EAAEvB,MAAM,CAACQ,OAAO,CAACU,aAAa,CAAC,gBAAgB;EACzD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}