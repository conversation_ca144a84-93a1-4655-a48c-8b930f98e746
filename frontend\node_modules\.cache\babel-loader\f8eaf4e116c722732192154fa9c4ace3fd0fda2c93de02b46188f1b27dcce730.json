{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\nimport { useSnackbar } from 'notistack';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Chip, {\n      label: uiState.text,\n      color: uiState.isSelected ? 'primary' : 'default',\n      variant: uiState.isSelected ? 'filled' : 'outlined',\n      size: \"small\",\n      onClick: handleClick,\n      clickable: true,\n      sx: {\n        maxWidth: '100%',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease-in-out',\n        '& .MuiChip-label': {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          display: 'block'\n        }\n      }\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n}, \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\")), \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    return data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, []);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n  const onProcessRowUpdateError = error => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, \"remove\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: (newRow, oldRow) => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 814,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 873,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 692,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"BFnsrwongaK9RanfNmGdfntFpYk=\", false, function () {\n  return [useSnackbar];\n});\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "Slide", "useSnackbar", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "clickable", "sx", "max<PERSON><PERSON><PERSON>", "cursor", "transition", "overflow", "textOverflow", "whiteSpace", "display", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "key", "borderRadius", "border", "borderColor", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "processRowUpdate", "newRow", "prev", "totalValue", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "updatedData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "style", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "opacity", "remarkText", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "memoGridData", "textAlign", "py", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "backgroundColor", "lineHeight", "padding", "overflowX", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "target", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\nimport { useSnackbar } from 'notistack';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : ''} arrow placement=\"top\">\n      <Chip\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        label={uiState.text}\n        color={uiState.isSelected ? 'primary' : 'default'}\n        variant={uiState.isSelected ? 'filled' : 'outlined'}\n        size=\"small\"\n        onClick={handleClick}\n        clickable\n        sx={{ \n          maxWidth: '100%', \n          cursor: 'pointer',\n          transition: 'all 0.2s ease-in-out',\n          '& .MuiChip-label': { \n            overflow: 'hidden', \n            textOverflow: 'ellipsis', \n            whiteSpace: 'nowrap', \n            display: 'block' \n          }\n        }}\n      />\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    return data\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, []);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n\n  const onProcessRowUpdateError = (error) => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Chip\n                key=\"undo\"\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          return (\n            <Chip\n              key=\"remove\"\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <DataGrid\n            rows={memoGridData}\n            columns={columns}\n            pageSize={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            disableSelectionOnClick\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n            }}\n            processRowUpdate={(newRow, oldRow) => {\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            }}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOH,OAAA,CAACH,KAAK;IAAA,GAAKM,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAGjD,KAAK,CAACkD,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC;IACrCoD,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACApD,SAAS,CAAC,MAAM;IACduD,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEd,OAAA,CAACd,OAAO;IAACoC,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,EAAG;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eAC3EzB,OAAA,CAACf,IAAI;MAEHyC,KAAK,EAAER,OAAO,CAACH,IAAK;MACpBY,KAAK,EAAET,OAAO,CAACF,UAAU,GAAG,SAAS,GAAG,SAAU;MAClDY,OAAO,EAAEV,OAAO,CAACF,UAAU,GAAG,QAAQ,GAAG,UAAW;MACpDa,IAAI,EAAC,OAAO;MACZZ,OAAO,EAAEG,WAAY;MACrBU,SAAS;MACTC,EAAE,EAAE;QACFC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,sBAAsB;QAClC,kBAAkB,EAAE;UAClBC,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE,QAAQ;UACpBC,OAAO,EAAE;QACX;MACF;IAAE,GAjBG,UAAUxB,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAkB7C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC,kCAAC;AAAC+B,GAAA,GA5CG7B,UAAU;AA8ChB,MAAM8B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,MAAM;IACzD,MAAM4F,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGtD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACmG,eAAe,EAAEC,kBAAkB,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACuG,YAAY,EAAEC,eAAe,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd4F,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAG7B,IAAI,CAAC8B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlH,QAAQ,CAAC,MAAM;IAC7CmH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7ClC,aAAa,GAAG,IAAIA,aAAa,CAACmC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAInC,aAAa,IAAIA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGpC,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG1H,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM2H,iBAAiB,GAAG3H,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM4H,gBAAgB,GAAG5H,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM6H,mBAAmB,GAAG7H,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM8H,UAAU,GAAInD,IAAI,IAAKA,IAAI,CAAC8B,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAjI,SAAS,CAAC,MAAM;IACd,IAAIkF,YAAY,IAAI8B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGtC,IAAI,CAACW,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BC,YAAY,CAACT,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CZ,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtChD,YAAY,CAAC,CAAC,GAAG8B,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5BC,YAAY,CAACT,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAE9B,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAEuD;EAAgB,CAAC,GAAGvG,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACwG,aAAa,EAAEC,gBAAgB,CAAC,GAAG5I,QAAQ,CAAC;IACjD6I,IAAI,EAAE,KAAK;IACX1F,KAAK,EAAE,IAAI;IACX2F,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA7I,SAAS,CAAC,MAAM;IACd,IAAIsG,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAG7I,WAAW,CAAC,CAAC8I,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAIhB,mBAAmB,CAACO,OAAO,EAAE,EAAE;IAE5EG,eAAe,CAACM,OAAO,EAAE;MACvB/E,OAAO,EAAEgF,QAAQ;MACjB;MACAE,GAAG,EAAED,SAAS;MACd9E,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBgF,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTL,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,WAAW,GAAG,GAAGxH,OAAO,aAAa+C,MAAM,EAAE;MACnD,MAAM0E,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BV,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlF,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMmF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMrI,KAAK,CAACsI,MAAM,CAAC,GAAGrI,OAAO,YAAY+C,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOoF,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAnF,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMsF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAGtK,WAAW,CAAC,CAAC4E,IAAI,EAAE2F,UAAU,KAAK;IAC3D,OAAO3F,IAAI,CACR4F,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD2D,MAAM,CAAC,CAACC,GAAG,EAAE/D,GAAG,KAAK;MACpB,IAAI4D,UAAU,IAAI5D,GAAG,CAACa,EAAE,KAAK+C,UAAU,CAAC/C,EAAE,EAAE;QAC1C,OAAOkD,GAAG,IAAIC,MAAM,CAACJ,UAAU,CAACvC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO0C,GAAG,IAAIC,MAAM,CAAChE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4C,gBAAgB,GAAG5K,WAAW,CAAE4E,IAAI,IAAK;IAC7C,MAAMiG,QAAQ,GAAGjG,IAAI,CAACkG,IAAI,CAACnE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAImD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGnG,IAAI,CAClB4F,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD2D,MAAM,CAAC,CAACC,GAAG,EAAE/D,GAAG,KAAK+D,GAAG,IAAIC,MAAM,CAAChE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/D6C,QAAQ,CAAC7C,UAAU,GAAG+C,QAAQ;IAChC;IACA,OAAOnG,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoG,gBAAgB,GAAGhL,WAAW,CAAEiL,MAAM,IAAK;IAC/CjE,WAAW,CAACkE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGb,kBAAkB,CAACY,IAAI,EAAED,MAAM,CAAC;MACjD,OAAOC,IAAI,CAACxE,GAAG,CAACC,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAKyD,MAAM,CAACzD,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAGsE;QAAO,CAAC;QACtD,IAAItE,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAEmD;QAAW,CAAC;QACjE,OAAOxE,GAAG;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOsE,MAAM;EACf,CAAC,EAAE,CAACX,kBAAkB,CAAC,CAAC;EAExB,MAAMc,uBAAuB,GAAInB,KAAK,IAAK;IACzCpB,YAAY,CAAC,SAASoB,KAAK,CAACnB,OAAO,EAAE,EAAE,OAAO,CAAC;EACjD,CAAC;;EAED;EACA,MAAMuC,iBAAiB,GAAGxL,KAAK,CAACG,WAAW,CAAC,CAACiD,KAAK,EAAE2F,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV1F,KAAK;MACL2F;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0C,kBAAkB,GAAGtL,WAAW,CAAC,MAAM;IAC3C0I,gBAAgB,CAACwC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPvC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4C,kBAAkB,GAAGvL,WAAW,CAAEwL,MAAM,IAAK;IACjD,MAAM;MAAEvI;IAAM,CAAC,GAAGwF,aAAa;IAC/B,IAAIxF,KAAK,KAAK,IAAI,EAAE;MAClB;MACAqI,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC1E,WAAW,CAAC2E,QAAQ,IAAI;UACtB,IAAIC,WAAW,GAAGD,QAAQ,CAACjF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKvE,KAAK,EAAE;cACpB,IAAIuI,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAG7E,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGF,GAAG;kBAAEC,OAAO,EAAE4E,MAAM;kBAAE3E,iBAAiB,EAAE2E;gBAAO,CAAC;cAC/D;YACF;YACA,OAAO7E,GAAG;UACZ,CAAC,CAAC;UAEFiF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACArD,UAAU,CAAC,MAAM;UACfM,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAE6C,kBAAkB,EAAEV,gBAAgB,EAAE/B,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAMgD,mBAAmB,GAAG7L,WAAW,CAAC,MAAM;IAC5CkG,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4F,oBAAoB,GAAG9L,WAAW,CAAC,MAAM;IAC7CkG,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+F,YAAY,GAAG/L,WAAW,CAAC,MAAM;IACrC,IAAI+F,SAAS,CAACiG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACxG,cAAc,CAACyG,QAAQ,CAAClG,SAAS,CAACiG,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEvG,iBAAiB,CAACyF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEnF,SAAS,CAACiG,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDnD,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjCiD,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAItG,cAAc,CAACyG,QAAQ,CAAClG,SAAS,CAACiG,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDnD,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAAC9C,SAAS,EAAEP,cAAc,EAAEsG,oBAAoB,EAAEjD,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAMqD,YAAY,GAAGlM,WAAW,CAAEwL,MAAM,IAAK;IAC3C/F,iBAAiB,CAACyF,IAAI,IAAIA,IAAI,CAACV,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAKX,MAAM,CAAC,CAAC;IAC/D3C,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMuD,eAAe,GAAGpM,WAAW,CAAEwH,EAAE,IAAK;IAC1CR,WAAW,CAACkE,IAAI,IAAI;MAClB,IAAIU,WAAW,GAAGV,IAAI,CAACxE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnFiF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;MAC3C,MAAMS,cAAc,GAAGT,WAAW,CAACpB,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH2E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/E,EAAE,GAAGgF,CAAC,CAAChF,EAAE,CAAC;MAC1C6E,cAAc,CAACI,OAAO,CAAC,CAAC9F,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOqE,WAAW;IACpB,CAAC,CAAC;IAEF/C,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAAC+B,gBAAgB,EAAE/B,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM6D,aAAa,GAAG1M,WAAW,CAAEwH,EAAE,IAAK;IACxCR,WAAW,CAACkE,IAAI,IAAI;MAClB,IAAIU,WAAW,GAAGV,IAAI,CAACxE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpFiF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;MAC3C,MAAMS,cAAc,GAAGT,WAAW,CAACpB,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH2E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/E,EAAE,GAAGgF,CAAC,CAAChF,EAAE,CAAC;MAC1C6E,cAAc,CAACI,OAAO,CAAC,CAAC9F,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOqE,WAAW;IACpB,CAAC,CAAC;IACFrD,UAAU,CAAC,MAAM;MACfM,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAAC+B,gBAAgB,EAAE/B,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM8D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFvG,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMwG,YAAY,GAAG7F,QAAQ,CAC1ByD,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACA8F,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC7E,EAAE,KAAK,QAAQ,IAAI,OAAO8E,CAAC,CAAC9E,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO6E,CAAC,CAAC7E,EAAE,GAAG8E,CAAC,CAAC9E,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMmF,OAAO,GAAGD,YAAY,CAAClG,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbuF,IAAI,EAAEnG,GAAG,CAACmG,IAAI,GAAI,OAAOnG,GAAG,CAACmG,IAAI,KAAK,QAAQ,IAAInG,GAAG,CAACmG,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGtF,GAAG,CAACmG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpG,GAAG,CAACmG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEnG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGqG,IAAI,CAACC,KAAK,CAACtG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFuG,EAAE,EAAE,OAAOvG,GAAG,CAACuG,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACtG,GAAG,CAACuG,EAAE,CAAC,GAAGvG,GAAG,CAACuG,EAAE,IAAI,EAAE;QAClEtG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGsG,KAAK,EAAE,OAAOxG,GAAG,CAACyG,QAAQ,KAAK,QAAQ,GACpCzG,GAAG,CAACyG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGzG,GAAG,CAACyG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG1G,GAAG,CAACyG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3E1G,GAAG,CAACyG,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAO3G,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAACqF,OAAO,CAAC,CAAC,CAAC,GAAG1G,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMuF,WAAW,GAAGxG,QAAQ,CACzByD,MAAM,CAAC7D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpEyC,MAAM,CAAC,CAACC,GAAG,EAAE/D,GAAG,KAAK+D,GAAG,IAAI,OAAO/D,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMwF,QAAQ,GAAG,MAAM3L,KAAK,CAAC4L,IAAI,CAAC,GAAG3L,OAAO,oBAAoB,EAAE;QAChE8C,IAAI,EAAEiI,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCxI,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI2I,QAAQ,CAAC5I,IAAI,IAAI4I,QAAQ,CAAC5I,IAAI,CAAC8I,KAAK,EAAE;QACxC;QACA,MAAMpE,WAAW,GAAG,GAAGxH,OAAO,CAACiL,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAC5I,IAAI,CAAC+I,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA9E,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACAN,UAAU,CAAC,MAAM;UACf,MAAMqF,MAAM,GAAGpE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CmE,MAAM,CAACC,KAAK,CAACpJ,OAAO,GAAG,MAAM;UAC7BmJ,MAAM,CAACE,GAAG,GAAGxE,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC8D,MAAM,CAAC;UACjCrF,UAAU,CAAC,MAAM;YACfiB,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC4D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACRzC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM4H,kBAAkB,GAAGhO,WAAW,CAAC,CAACiD,KAAK,EAAEgL,KAAK,KAAK;IACvD5C,iBAAiB,CAACpI,KAAK,EAAEgL,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC5C,iBAAiB,CAAC,CAAC;EAEvB,MAAM6C,OAAO,GAAGhO,OAAO,CAAC,MAAOiF,WAAW,CAACuB,GAAG,CAACyH,GAAG,IAAI;IACpD,IAAI,EAAEpH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACqH,cAAc,CAACD,GAAG,CAAC/I,KAAK,CAAC,CAAC,IAAI+I,GAAG,CAAC/I,KAAK,KAAK,SAAS,IAAI+I,GAAG,CAAC/I,KAAK,KAAK,QAAQ,IAAI+I,GAAG,CAAC/I,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAI+I,GAAG,CAAC/I,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAE+I,GAAG,CAAC/I,KAAK;QAChBC,UAAU,EAAE8I,GAAG,CAAC9I,UAAU;QAC1BgJ,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVhJ,QAAQ,EAAE,KAAK;QACfiJ,UAAU,EAAGlE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI2C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAM0H,iBAAiB,GAAGnE,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE1E,OAAA,CAACd,OAAO;cAACoC,KAAK,EAAE4G,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACnD,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvEzB,OAAA,CAACf,IAAI;gBACHyC,KAAK,EAAE2K,iBAAkB;gBACzB1K,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC,OAAO;gBACZE,EAAE,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEsK,OAAO,EAAE,GAAG;kBAAEpK,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAI+L,UAAU,GAAG,MAAM;UACvB,IAAIvL,UAAU,GAAG,KAAK;UAEtB,IAAIkH,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAIwD,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3E6H,UAAU,GAAGrE,MAAM,CAAC1D,GAAG,CAACE,iBAAiB;YACzC1D,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEhB,OAAA,CAACU,UAAU;YACTI,KAAK,EAAEoH,MAAM,CAAC1D,GAAG,CAACa,EAAG;YACrBtE,IAAI,EAAEwL,UAAW;YACjBvL,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAE4K;UAAmB;YAAAxL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIwL,GAAG,CAAC/I,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAE+I,GAAG,CAAC/I,KAAK;QAChBC,UAAU,EAAE8I,GAAG,CAAC9I,UAAU;QAC1BgJ,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVhJ,QAAQ,EAAE,KAAK;QACfiJ,UAAU,EAAGlE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI2C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACE3E,OAAA,CAACf,IAAI;cAEHyC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAC,SAAS;cACfE,IAAI,EAAC,OAAO;cACZ2K,IAAI,eAAExM,OAAA,CAACR,QAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBS,OAAO,EAAEA,CAAA,KAAMsJ,aAAa,CAACrC,MAAM,CAAC1D,GAAG,CAACa,EAAE,CAAE;cAC5CtD,EAAE,EAAE;gBAAEE,MAAM,EAAE;cAAU;YAAE,GANtB,MAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOX,CAAC;UAEN;UACA,oBACER,OAAA,CAACf,IAAI;YAEHyC,KAAK,EAAC,cAAI;YACVC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZ2K,IAAI,eAAExM,OAAA,CAACT,UAAU;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBS,OAAO,EAAEA,CAAA,KAAMgJ,eAAe,CAAC/B,MAAM,CAAC1D,GAAG,CAACa,EAAE,CAAE;YAC9CtD,EAAE,EAAE;cAAEE,MAAM,EAAE;YAAU;UAAE,GANtB,QAAQ;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOb,CAAC;QAEN;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGwL,GAAG;MACN7I,QAAQ,EAAE+E,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI2C,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOqH,GAAG,CAAC7I,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDiJ,UAAU,EAAGlE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIyG,GAAG,CAAC/I,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEjD,OAAA,CAAC/B,UAAU;YAAC2D,OAAO,EAAC,OAAO;YAAC6K,UAAU,EAAC,MAAM;YAAC9K,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOyG,MAAM,CAAC4D,KAAK,KAAK,QAAQ,GAAG5D,MAAM,CAAC4D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOhD,MAAM,CAAC4D,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAClE,MAAM,CAACN,MAAM,CAAC4D,KAAK,CAAC,CAAC,GAAGtD,MAAM,CAACN,MAAM,CAAC4D,KAAK,CAAC,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGhD,MAAM,CAAC4D;UAAK;YAAAzL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI0H,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE3E,OAAA,CAAC/B,UAAU;YAAC2D,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,eAAe;YAACI,EAAE,EAAE;cAAE4K,cAAc,EAAE;YAAe,CAAE;YAAAlL,QAAA,EACtFyG,MAAM,CAAC4D;UAAK;YAAAzL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIwL,GAAG,CAAC/I,KAAK,KAAK,MAAM,IAAIiF,MAAM,CAAC4D,KAAK,EAAE;UACxC,OAAO5D,MAAM,CAAC4D,KAAK,CAAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIoB,GAAG,CAAC/I,KAAK,KAAK,IAAI,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/I,KAAK,KAAK,OAAO,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOjB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/I,KAAK,KAAK,IAAI,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/I,KAAK,KAAK,UAAU,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO5D,MAAM,CAAC4D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG5D,MAAM,CAAC4D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGhD,MAAM,CAAC4D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIc,GAAG,CAAC/I,KAAK,KAAK,YAAY,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO5D,MAAM,CAAC4D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIc,GAAG,CAAC/I,KAAK,KAAK,YAAY,IAAI,OAAOiF,MAAM,CAAC4D,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAClE,MAAM,CAACN,MAAM,CAAC4D,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOtD,MAAM,CAACN,MAAM,CAAC4D,KAAK,CAAC,CAACZ,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOhD,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO5D,MAAM,CAAC4D,KAAK;QACrB;QACA,OAAO5D,MAAM,CAAC4D,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACzD,MAAM,CAACuE,OAAO,CAAE,EAAE,CAAC5J,WAAW,EAAE4B,QAAQ,EAAEiH,kBAAkB,EAAE5B,eAAe,EAAEM,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAMsC,YAAY,GAAG9O,OAAO,CAAC,MAAM6G,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAExD;EACA,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEhF,OAAA,CAAChC,GAAG;MAAC+D,EAAE,EAAE;QAAE+K,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAtL,QAAA,gBACtCzB,OAAA,CAAC/B,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAAC9B,MAAM;QACL0D,OAAO,EAAC,WAAW;QACnBX,OAAO,EAAE0B,OAAQ;QACjBZ,EAAE,EAAE;UAAEiL,EAAE,EAAE;QAAE,CAAE;QAAAvL,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACER,OAAA,CAAChC,GAAG;IAAAyD,QAAA,gBACFzB,OAAA,CAAChC,GAAG;MAAC+D,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE2K,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1L,QAAA,gBACzFzB,OAAA,CAAC/B,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACwL,YAAY;QAAA3L,QAAA,EAAC;MAEtC;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbR,OAAA,CAAChC,GAAG;QAAAyD,QAAA,gBACFzB,OAAA,CAAC9B,MAAM;UACL0D,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf0L,SAAS,eAAErN,OAAA,CAACZ,YAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BS,OAAO,EAAEiG,cAAe;UACxBnF,EAAE,EAAE;YAAEuL,EAAE,EAAE;UAAE,CAAE;UAAA7L,QAAA,EACf;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETR,OAAA,CAAC9B,MAAM;UACL0D,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf0L,SAAS,eAAErN,OAAA,CAACP,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCS,OAAO,EAAEuJ,gBAAiB;UAC1B+C,QAAQ,EAAEvJ,oBAAqB;UAC/BjC,EAAE,EAAE;YAAEuL,EAAE,EAAE;UAAE,CAAE;UAAA7L,QAAA,EAEbuC,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETR,OAAA,CAAC9B,MAAM;UACL0D,OAAO,EAAC,UAAU;UAClByL,SAAS,eAAErN,OAAA,CAACX,cAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BS,OAAO,EAAE8G,aAAc;UAAAtG,QAAA,EACxB;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAAC7B,KAAK;MAAC4D,EAAE,EAAE;QAAEoK,KAAK,EAAE,MAAM;QAAEhK,QAAQ,EAAE;MAAS,CAAE;MAAAV,QAAA,eAC/CzB,OAAA,CAAChC,GAAG;QAAC+D,EAAE,EAAE;UAAEyL,MAAM,EAAE,MAAM;UAAErB,KAAK,EAAE;QAAO,CAAE;QAAA1K,QAAA,eACzCzB,OAAA,CAACb,QAAQ;UACPsO,IAAI,EAAEZ,YAAa;UACnBd,OAAO,EAAEA,OAAQ;UACjB2B,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAG7F,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC1D,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI4C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFqJ,cAAc,EAAG9F,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC1D,GAAG,CAACc,OAAO,IAAI4C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOuD,MAAM,CAAC+F,MAAM,CAAC9K,QAAQ,IAAI,OAAO+E,MAAM,CAAC+F,MAAM,CAAC9K,QAAQ,KAAK,UAAU,GAC3E+E,MAAM,CAAC+F,MAAM,CAAC9K,QAAQ,CAAC+E,MAAM,CAAC,GAAGA,MAAM,CAAC+F,MAAM,CAAC9K,QAAQ;UAC3D,CAAE;UACF0F,gBAAgB,EAAEA,CAACC,MAAM,EAAEoF,MAAM,KAAK;YACpC,IAAIpF,MAAM,CAACjD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAO4D,MAAM,CAACjD,UAAU,KAAK,QAAQ,EAAE;gBACzCiD,MAAM,CAACjD,UAAU,GAAG2C,MAAM,CAACM,MAAM,CAACjD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOgD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDlH,EAAE,EAAE;YACF,cAAc,EAAE;cACdoM,eAAe,EAAE,0BAA0B;cAC3C1B,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChB0B,eAAe,EAAE,0BAA0B;cAC3CxM,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrBU,UAAU,EAAE,QAAQ;cACpB+L,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCG,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrBnM,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnB6E,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BqH,OAAO,EAAE,OAAO;cAChBhM,UAAU,EAAE,QAAQ;cACpB+L,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClC/L,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnBiM,UAAU,EAAE,MAAM;cAClB3B,UAAU,EAAE;YACd;UACF;QAAE;UAAApM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRR,OAAA,CAAC1B,MAAM;MACLkI,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB+H,OAAO,EAAEpF,kBAAmB;MAC5BqF,SAAS;MACTxM,QAAQ,EAAC,IAAI;MACbyM,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAnN,QAAA,gBAEnBzB,OAAA,CAACzB,WAAW;QAAAkD,QAAA,eACVzB,OAAA,CAAChC,GAAG;UAAC+D,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE2K,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAzL,QAAA,gBAClFzB,OAAA,CAAC/B,UAAU;YAAC2D,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CR,OAAA,CAAC9B,MAAM;YACLmP,SAAS,eAAErN,OAAA,CAACV,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEyI,mBAAoB;YAC7B/H,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdR,OAAA,CAACxB,aAAa;QAACqQ,QAAQ;QAAC9M,EAAE,EAAE;UAAE+M,CAAC,EAAE;QAAE,CAAE;QAAArN,QAAA,eACnCzB,OAAA,CAACJ,aAAa;UACZ4N,MAAM,EAAE,GAAI;UACZuB,SAAS,EAAE1L,cAAc,CAAC2B,MAAO;UACjCgK,QAAQ,EAAE,EAAG;UACb7C,KAAK,EAAC,MAAM;UAAA1K,QAAA,EAEXA,CAAC;YAAE2D,KAAK;YAAEsG;UAAM,CAAC,KAAK;YACrB,MAAMrC,MAAM,GAAGhG,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACEpF,OAAA,CAACrB,QAAQ;cAEP+M,KAAK,EAAEA,KAAM;cACbuD,cAAc;cACdC,eAAe,eACblP,OAAA,CAACjB,UAAU;gBACToQ,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBlO,OAAO,EAAEA,CAAA,KAAM8I,YAAY,CAACV,MAAM,CAAE;gBAAA5H,QAAA,eAEpCzB,OAAA,CAACT,UAAU;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiB,QAAA,eAEDzB,OAAA,CAACpB,cAAc;gBAACqC,OAAO,EAAEA,CAAA,KAAMmI,kBAAkB,CAACC,MAAM,CAAE;gBAAA5H,QAAA,eACxDzB,OAAA,CAACnB,YAAY;kBAACuQ,OAAO,EAAE/F;gBAAO;kBAAAhJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ6I,MAAM;cAAAhJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBR,OAAA,CAACvB,aAAa;QAAAgD,QAAA,eACZzB,OAAA,CAAC9B,MAAM;UAAC+C,OAAO,EAAEkI,kBAAmB;UAAA1H,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTR,OAAA,CAAC1B,MAAM;MACLkI,IAAI,EAAE1C,eAAgB;MACtByK,OAAO,EAAE5E,oBAAqB;MAC9B6E,SAAS;MACTxM,QAAQ,EAAC,IAAI;MACbyM,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAnN,QAAA,gBAEnBzB,OAAA,CAACzB,WAAW;QAAAkD,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCR,OAAA,CAACxB,aAAa;QAAAiD,QAAA,eACZzB,OAAA,CAAClB,SAAS;UACRuQ,SAAS;UACTC,MAAM,EAAC,OAAO;UACdjK,EAAE,EAAC,MAAM;UACT3D,KAAK,EAAC,0BAAM;UACZ6N,IAAI,EAAC,MAAM;UACXf,SAAS;UACT5M,OAAO,EAAC,UAAU;UAClBkK,KAAK,EAAElI,SAAU;UACjB4L,QAAQ,EAAGnO,CAAC,IAAKwC,YAAY,CAACxC,CAAC,CAACoO,MAAM,CAAC3D,KAAK;QAAE;UAAAzL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBR,OAAA,CAACvB,aAAa;QAAAgD,QAAA,gBACZzB,OAAA,CAAC9B,MAAM;UAAC+C,OAAO,EAAE0I,oBAAqB;UAAAlI,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDR,OAAA,CAAC9B,MAAM;UAAC+C,OAAO,EAAE2I,YAAa;UAACjI,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACuC,GAAA,CA1xBIP,aAAa;EAAA,QAiIW1C,WAAW;AAAA;AAAA4P,GAAA,GAjInClN,aAAa;AA4xBnB,eAAeA,aAAa;AAAC,IAAA/B,EAAA,EAAAI,GAAA,EAAA0B,GAAA,EAAAmN,GAAA;AAAAC,YAAA,CAAAlP,EAAA;AAAAkP,YAAA,CAAA9O,GAAA;AAAA8O,YAAA,CAAApN,GAAA;AAAAoN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}