{"ast": null, "code": "/**\n * Split this component for RSC import\n */\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_MODE_STORAGE_KEY = 'mode';\nexport const DEFAULT_COLOR_SCHEME_STORAGE_KEY = 'color-scheme';\nexport const DEFAULT_ATTRIBUTE = 'data-color-scheme';\nexport default function InitColorSchemeScript(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme = 'light',\n    defaultDarkColorScheme = 'dark',\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    attribute = DEFAULT_ATTRIBUTE,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = options || {};\n  return /*#__PURE__*/_jsx(\"script\", {\n    suppressHydrationWarning: true,\n    nonce: typeof window === 'undefined' ? nonce : ''\n    // eslint-disable-next-line react/no-danger\n    ,\n\n    dangerouslySetInnerHTML: {\n      __html: \"(function() {\\ntry {\\n  var mode = localStorage.getItem('\".concat(modeStorageKey, \"') || '\").concat(defaultMode, \"';\\n  var colorScheme = '';\\n  if (mode === 'system') {\\n    // handle system mode\\n    var mql = window.matchMedia('(prefers-color-scheme: dark)');\\n    if (mql.matches) {\\n      colorScheme = localStorage.getItem('\").concat(colorSchemeStorageKey, \"-dark') || '\").concat(defaultDarkColorScheme, \"';\\n    } else {\\n      colorScheme = localStorage.getItem('\").concat(colorSchemeStorageKey, \"-light') || '\").concat(defaultLightColorScheme, \"';\\n    }\\n  }\\n  if (mode === 'light') {\\n    colorScheme = localStorage.getItem('\").concat(colorSchemeStorageKey, \"-light') || '\").concat(defaultLightColorScheme, \"';\\n  }\\n  if (mode === 'dark') {\\n    colorScheme = localStorage.getItem('\").concat(colorSchemeStorageKey, \"-dark') || '\").concat(defaultDarkColorScheme, \"';\\n  }\\n  if (colorScheme) {\\n    \").concat(colorSchemeNode, \".setAttribute('\").concat(attribute, \"', colorScheme);\\n  }\\n} catch(e){}})();\")\n    }\n  }, \"mui-color-scheme-init\");\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "DEFAULT_MODE_STORAGE_KEY", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "DEFAULT_ATTRIBUTE", "InitColorSchemeScript", "options", "defaultMode", "defaultLightColorScheme", "defaultDarkColorScheme", "modeStorageKey", "colorSchemeStorageKey", "attribute", "colorSchemeNode", "nonce", "suppressHydrationWarning", "window", "dangerouslySetInnerHTML", "__html", "concat"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/system/esm/InitColorSchemeScript/InitColorSchemeScript.js"], "sourcesContent": ["/**\n * Split this component for RSC import\n */\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_MODE_STORAGE_KEY = 'mode';\nexport const DEFAULT_COLOR_SCHEME_STORAGE_KEY = 'color-scheme';\nexport const DEFAULT_ATTRIBUTE = 'data-color-scheme';\nexport default function InitColorSchemeScript(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme = 'light',\n    defaultDarkColorScheme = 'dark',\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    attribute = DEFAULT_ATTRIBUTE,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = options || {};\n  return /*#__PURE__*/_jsx(\"script\", {\n    suppressHydrationWarning: true,\n    nonce: typeof window === 'undefined' ? nonce : ''\n    // eslint-disable-next-line react/no-danger\n    ,\n    dangerouslySetInnerHTML: {\n      __html: `(function() {\ntry {\n  var mode = localStorage.getItem('${modeStorageKey}') || '${defaultMode}';\n  var colorScheme = '';\n  if (mode === 'system') {\n    // handle system mode\n    var mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      colorScheme = localStorage.getItem('${colorSchemeStorageKey}-dark') || '${defaultDarkColorScheme}';\n    } else {\n      colorScheme = localStorage.getItem('${colorSchemeStorageKey}-light') || '${defaultLightColorScheme}';\n    }\n  }\n  if (mode === 'light') {\n    colorScheme = localStorage.getItem('${colorSchemeStorageKey}-light') || '${defaultLightColorScheme}';\n  }\n  if (mode === 'dark') {\n    colorScheme = localStorage.getItem('${colorSchemeStorageKey}-dark') || '${defaultDarkColorScheme}';\n  }\n  if (colorScheme) {\n    ${colorSchemeNode}.setAttribute('${attribute}', colorScheme);\n  }\n} catch(e){}})();`\n    }\n  }, \"mui-color-scheme-init\");\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,wBAAwB,GAAG,MAAM;AAC9C,OAAO,MAAMC,gCAAgC,GAAG,cAAc;AAC9D,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,OAAO;IACrBC,uBAAuB,GAAG,OAAO;IACjCC,sBAAsB,GAAG,MAAM;IAC/BC,cAAc,GAAGR,wBAAwB;IACzCS,qBAAqB,GAAGR,gCAAgC;IACxDS,SAAS,GAAGR,iBAAiB;IAC7BS,eAAe,GAAG,0BAA0B;IAC5CC;EACF,CAAC,GAAGR,OAAO,IAAI,CAAC,CAAC;EACjB,OAAO,aAAaL,IAAI,CAAC,QAAQ,EAAE;IACjCc,wBAAwB,EAAE,IAAI;IAC9BD,KAAK,EAAE,OAAOE,MAAM,KAAK,WAAW,GAAGF,KAAK,GAAG;IAC/C;IAAA;;IAEAG,uBAAuB,EAAE;MACvBC,MAAM,8DAAAC,MAAA,CAEyBT,cAAc,aAAAS,MAAA,CAAUZ,WAAW,8NAAAY,MAAA,CAM5BR,qBAAqB,kBAAAQ,MAAA,CAAeV,sBAAsB,kEAAAU,MAAA,CAE1DR,qBAAqB,mBAAAQ,MAAA,CAAgBX,uBAAuB,yFAAAW,MAAA,CAI9DR,qBAAqB,mBAAAQ,MAAA,CAAgBX,uBAAuB,iFAAAW,MAAA,CAG5DR,qBAAqB,kBAAAQ,MAAA,CAAeV,sBAAsB,yCAAAU,MAAA,CAG9FN,eAAe,qBAAAM,MAAA,CAAkBP,SAAS;IAG5C;EACF,CAAC,EAAE,uBAAuB,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}