{"ast": null, "code": "import merge from './merge';\nfunction compose() {\n  for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {\n    styles[_key] = arguments[_key];\n  }\n  const handlers = styles.reduce((acc, style) => {\n    style.filterProps.forEach(prop => {\n      acc[prop] = style;\n    });\n    return acc;\n  }, {});\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (handlers[prop]) {\n        return merge(acc, handlers[prop](props));\n      }\n      return acc;\n    }, {});\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? styles.reduce((acc, style) => Object.assign(acc, style.propTypes), {}) : {};\n  fn.filterProps = styles.reduce((acc, style) => acc.concat(style.filterProps), []);\n  return fn;\n}\nexport default compose;", "map": {"version": 3, "names": ["merge", "compose", "_len", "arguments", "length", "styles", "Array", "_key", "handlers", "reduce", "acc", "style", "filterProps", "for<PERSON>ach", "prop", "fn", "props", "Object", "keys", "propTypes", "process", "env", "NODE_ENV", "assign", "concat"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/system/esm/compose.js"], "sourcesContent": ["import merge from './merge';\nfunction compose(...styles) {\n  const handlers = styles.reduce((acc, style) => {\n    style.filterProps.forEach(prop => {\n      acc[prop] = style;\n    });\n    return acc;\n  }, {});\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (handlers[prop]) {\n        return merge(acc, handlers[prop](props));\n      }\n      return acc;\n    }, {});\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? styles.reduce((acc, style) => Object.assign(acc, style.propTypes), {}) : {};\n  fn.filterProps = styles.reduce((acc, style) => acc.concat(style.filterProps), []);\n  return fn;\n}\nexport default compose;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,OAAOA,CAAA,EAAY;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACxB,MAAMC,QAAQ,GAAGH,MAAM,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC7CA,KAAK,CAACC,WAAW,CAACC,OAAO,CAACC,IAAI,IAAI;MAChCJ,GAAG,CAACI,IAAI,CAAC,GAAGH,KAAK;IACnB,CAAC,CAAC;IACF,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEN;EACA;EACA,MAAMK,EAAE,GAAGC,KAAK,IAAI;IAClB,OAAOC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACP,MAAM,CAAC,CAACC,GAAG,EAAEI,IAAI,KAAK;MAC9C,IAAIN,QAAQ,CAACM,IAAI,CAAC,EAAE;QAClB,OAAOd,KAAK,CAACU,GAAG,EAAEF,QAAQ,CAACM,IAAI,CAAC,CAACE,KAAK,CAAC,CAAC;MAC1C;MACA,OAAON,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC;EACDK,EAAE,CAACI,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,MAAM,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKM,MAAM,CAACM,MAAM,CAACb,GAAG,EAAEC,KAAK,CAACQ,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClIJ,EAAE,CAACH,WAAW,GAAGP,MAAM,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,CAACc,MAAM,CAACb,KAAK,CAACC,WAAW,CAAC,EAAE,EAAE,CAAC;EACjF,OAAOG,EAAE;AACX;AACA,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}