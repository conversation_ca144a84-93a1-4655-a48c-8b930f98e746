{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport TablePagination, { tablePaginationClasses } from '@mui/material/TablePagination';\nimport { styled } from '@mui/material/styles';\nimport { useGridSelector } from '../hooks/utils/useGridSelector';\nimport { useGridApiContext } from '../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../hooks/utils/useGridRootProps';\nimport { gridPaginationModelSelector, gridPaginationRowCountSelector } from '../hooks/features/pagination/gridPaginationSelector';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridPaginationRoot = styled(TablePagination)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    [\"& .\".concat(tablePaginationClasses.selectLabel)]: {\n      display: 'none',\n      [theme.breakpoints.up('sm')]: {\n        display: 'block'\n      }\n    },\n    [\"& .\".concat(tablePaginationClasses.input)]: {\n      display: 'none',\n      [theme.breakpoints.up('sm')]: {\n        display: 'inline-flex'\n      }\n    }\n  };\n});\n\n// A mutable version of a readonly array.\n\nconst GridPagination = /*#__PURE__*/React.forwardRef(function GridPagination(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const lastPage = React.useMemo(() => Math.floor(rowCount / (paginationModel.pageSize || 1)), [rowCount, paginationModel.pageSize]);\n  const handlePageSizeChange = React.useCallback(event => {\n    const pageSize = Number(event.target.value);\n    apiRef.current.setPageSize(pageSize);\n  }, [apiRef]);\n  const handlePageChange = React.useCallback((_, page) => {\n    apiRef.current.setPage(page);\n  }, [apiRef]);\n  const isPageSizeIncludedInPageSizeOptions = pageSize => {\n    for (let i = 0; i < rootProps.pageSizeOptions.length; i += 1) {\n      const option = rootProps.pageSizeOptions[i];\n      if (typeof option === 'number') {\n        if (option === pageSize) {\n          return true;\n        }\n      } else if (option.value === pageSize) {\n        return true;\n      }\n    }\n    return false;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    var _rootProps$pagination, _rootProps$pagination2;\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const warnedOnceMissingInPageSizeOptions = React.useRef(false);\n    const pageSize = (_rootProps$pagination = (_rootProps$pagination2 = rootProps.paginationModel) == null ? void 0 : _rootProps$pagination2.pageSize) != null ? _rootProps$pagination : paginationModel.pageSize;\n    if (!warnedOnceMissingInPageSizeOptions.current && !rootProps.autoPageSize && !isPageSizeIncludedInPageSizeOptions(pageSize)) {\n      console.warn([\"MUI X: The page size `\".concat(paginationModel.pageSize, \"` is not preset in the `pageSizeOptions`.\"), \"Add it to show the pagination select.\"].join('\\n'));\n      warnedOnceMissingInPageSizeOptions.current = true;\n    }\n  }\n  const pageSizeOptions = isPageSizeIncludedInPageSizeOptions(paginationModel.pageSize) ? rootProps.pageSizeOptions : [];\n  return /*#__PURE__*/_jsx(GridPaginationRoot, _extends({\n    ref: ref,\n    component: \"div\",\n    count: rowCount,\n    page: paginationModel.page <= lastPage ? paginationModel.page : lastPage\n    // TODO: Remove the cast once the type is fixed in Material UI and that the min Material UI version\n    // for x-data-grid is past the fix.\n    // Note that Material UI will not mutate the array, so this is safe.\n    ,\n\n    rowsPerPageOptions: pageSizeOptions,\n    rowsPerPage: paginationModel.pageSize,\n    onPageChange: handlePageChange,\n    onRowsPerPageChange: handlePageSizeChange\n  }, apiRef.current.getLocaleText('MuiTablePagination'), props));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridPagination.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  component: PropTypes.elementType\n} : void 0;\nexport { GridPagination };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "TablePagination", "tablePaginationClasses", "styled", "useGridSelector", "useGridApiContext", "useGridRootProps", "gridPaginationModelSelector", "gridPaginationRowCountSelector", "jsx", "_jsx", "GridPaginationRoot", "_ref", "theme", "concat", "selectLabel", "display", "breakpoints", "up", "input", "GridPagination", "forwardRef", "props", "ref", "apiRef", "rootProps", "paginationModel", "rowCount", "lastPage", "useMemo", "Math", "floor", "pageSize", "handlePageSizeChange", "useCallback", "event", "Number", "target", "value", "current", "setPageSize", "handlePageChange", "_", "page", "setPage", "isPageSizeIncludedInPageSizeOptions", "i", "pageSizeOptions", "length", "option", "process", "env", "NODE_ENV", "_rootProps$pagination", "_rootProps$pagination2", "warnedOnceMissingInPageSizeOptions", "useRef", "autoPageSize", "console", "warn", "join", "component", "count", "rowsPerPageOptions", "rowsPerPage", "onPageChange", "onRowsPerPageChange", "getLocaleText", "propTypes", "elementType"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/GridPagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport TablePagination, { tablePaginationClasses } from '@mui/material/TablePagination';\nimport { styled } from '@mui/material/styles';\nimport { useGridSelector } from '../hooks/utils/useGridSelector';\nimport { useGridApiContext } from '../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../hooks/utils/useGridRootProps';\nimport { gridPaginationModelSelector, gridPaginationRowCountSelector } from '../hooks/features/pagination/gridPaginationSelector';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridPaginationRoot = styled(TablePagination)(({\n  theme\n}) => ({\n  [`& .${tablePaginationClasses.selectLabel}`]: {\n    display: 'none',\n    [theme.breakpoints.up('sm')]: {\n      display: 'block'\n    }\n  },\n  [`& .${tablePaginationClasses.input}`]: {\n    display: 'none',\n    [theme.breakpoints.up('sm')]: {\n      display: 'inline-flex'\n    }\n  }\n}));\n\n// A mutable version of a readonly array.\n\nconst GridPagination = /*#__PURE__*/React.forwardRef(function GridPagination(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const lastPage = React.useMemo(() => Math.floor(rowCount / (paginationModel.pageSize || 1)), [rowCount, paginationModel.pageSize]);\n  const handlePageSizeChange = React.useCallback(event => {\n    const pageSize = Number(event.target.value);\n    apiRef.current.setPageSize(pageSize);\n  }, [apiRef]);\n  const handlePageChange = React.useCallback((_, page) => {\n    apiRef.current.setPage(page);\n  }, [apiRef]);\n  const isPageSizeIncludedInPageSizeOptions = pageSize => {\n    for (let i = 0; i < rootProps.pageSizeOptions.length; i += 1) {\n      const option = rootProps.pageSizeOptions[i];\n      if (typeof option === 'number') {\n        if (option === pageSize) {\n          return true;\n        }\n      } else if (option.value === pageSize) {\n        return true;\n      }\n    }\n    return false;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    var _rootProps$pagination, _rootProps$pagination2;\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const warnedOnceMissingInPageSizeOptions = React.useRef(false);\n    const pageSize = (_rootProps$pagination = (_rootProps$pagination2 = rootProps.paginationModel) == null ? void 0 : _rootProps$pagination2.pageSize) != null ? _rootProps$pagination : paginationModel.pageSize;\n    if (!warnedOnceMissingInPageSizeOptions.current && !rootProps.autoPageSize && !isPageSizeIncludedInPageSizeOptions(pageSize)) {\n      console.warn([`MUI X: The page size \\`${paginationModel.pageSize}\\` is not preset in the \\`pageSizeOptions\\`.`, `Add it to show the pagination select.`].join('\\n'));\n      warnedOnceMissingInPageSizeOptions.current = true;\n    }\n  }\n  const pageSizeOptions = isPageSizeIncludedInPageSizeOptions(paginationModel.pageSize) ? rootProps.pageSizeOptions : [];\n  return /*#__PURE__*/_jsx(GridPaginationRoot, _extends({\n    ref: ref,\n    component: \"div\",\n    count: rowCount,\n    page: paginationModel.page <= lastPage ? paginationModel.page : lastPage\n    // TODO: Remove the cast once the type is fixed in Material UI and that the min Material UI version\n    // for x-data-grid is past the fix.\n    // Note that Material UI will not mutate the array, so this is safe.\n    ,\n    rowsPerPageOptions: pageSizeOptions,\n    rowsPerPage: paginationModel.pageSize,\n    onPageChange: handlePageChange,\n    onRowsPerPageChange: handlePageSizeChange\n  }, apiRef.current.getLocaleText('MuiTablePagination'), props));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridPagination.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  component: PropTypes.elementType\n} : void 0;\nexport { GridPagination };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,eAAe,IAAIC,sBAAsB,QAAQ,+BAA+B;AACvF,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,2BAA2B,EAAEC,8BAA8B,QAAQ,qDAAqD;AACjI,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGR,MAAM,CAACF,eAAe,CAAC,CAACW,IAAA;EAAA,IAAC;IAClDC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACL,OAAAE,MAAA,CAAOZ,sBAAsB,CAACa,WAAW,IAAK;MAC5CC,OAAO,EAAE,MAAM;MACf,CAACH,KAAK,CAACI,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BF,OAAO,EAAE;MACX;IACF,CAAC;IACD,OAAAF,MAAA,CAAOZ,sBAAsB,CAACiB,KAAK,IAAK;MACtCH,OAAO,EAAE,MAAM;MACf,CAACH,KAAK,CAACI,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BF,OAAO,EAAE;MACX;IACF;EACF,CAAC;AAAA,CAAC,CAAC;;AAEH;;AAEA,MAAMI,cAAc,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,SAASD,cAAcA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACvF,MAAMC,MAAM,GAAGnB,iBAAiB,CAAC,CAAC;EAClC,MAAMoB,SAAS,GAAGnB,gBAAgB,CAAC,CAAC;EACpC,MAAMoB,eAAe,GAAGtB,eAAe,CAACoB,MAAM,EAAEjB,2BAA2B,CAAC;EAC5E,MAAMoB,QAAQ,GAAGvB,eAAe,CAACoB,MAAM,EAAEhB,8BAA8B,CAAC;EACxE,MAAMoB,QAAQ,GAAG7B,KAAK,CAAC8B,OAAO,CAAC,MAAMC,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAID,eAAe,CAACM,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,CAACL,QAAQ,EAAED,eAAe,CAACM,QAAQ,CAAC,CAAC;EAClI,MAAMC,oBAAoB,GAAGlC,KAAK,CAACmC,WAAW,CAACC,KAAK,IAAI;IACtD,MAAMH,QAAQ,GAAGI,MAAM,CAACD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC;IAC3Cd,MAAM,CAACe,OAAO,CAACC,WAAW,CAACR,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EACZ,MAAMiB,gBAAgB,GAAG1C,KAAK,CAACmC,WAAW,CAAC,CAACQ,CAAC,EAAEC,IAAI,KAAK;IACtDnB,MAAM,CAACe,OAAO,CAACK,OAAO,CAACD,IAAI,CAAC;EAC9B,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EACZ,MAAMqB,mCAAmC,GAAGb,QAAQ,IAAI;IACtD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,SAAS,CAACsB,eAAe,CAACC,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAMG,MAAM,GAAGxB,SAAS,CAACsB,eAAe,CAACD,CAAC,CAAC;MAC3C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAIA,MAAM,KAAKjB,QAAQ,EAAE;UACvB,OAAO,IAAI;QACb;MACF,CAAC,MAAM,IAAIiB,MAAM,CAACX,KAAK,KAAKN,QAAQ,EAAE;QACpC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EACD,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,qBAAqB,EAAEC,sBAAsB;IACjD;IACA,MAAMC,kCAAkC,GAAGxD,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;IAC9D,MAAMxB,QAAQ,GAAG,CAACqB,qBAAqB,GAAG,CAACC,sBAAsB,GAAG7B,SAAS,CAACC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4B,sBAAsB,CAACtB,QAAQ,KAAK,IAAI,GAAGqB,qBAAqB,GAAG3B,eAAe,CAACM,QAAQ;IAC7M,IAAI,CAACuB,kCAAkC,CAAChB,OAAO,IAAI,CAACd,SAAS,CAACgC,YAAY,IAAI,CAACZ,mCAAmC,CAACb,QAAQ,CAAC,EAAE;MAC5H0B,OAAO,CAACC,IAAI,CAAC,0BAAA7C,MAAA,CAA2BY,eAAe,CAACM,QAAQ,wFAAwF,CAAC4B,IAAI,CAAC,IAAI,CAAC,CAAC;MACpKL,kCAAkC,CAAChB,OAAO,GAAG,IAAI;IACnD;EACF;EACA,MAAMQ,eAAe,GAAGF,mCAAmC,CAACnB,eAAe,CAACM,QAAQ,CAAC,GAAGP,SAAS,CAACsB,eAAe,GAAG,EAAE;EACtH,OAAO,aAAarC,IAAI,CAACC,kBAAkB,EAAEb,QAAQ,CAAC;IACpDyB,GAAG,EAAEA,GAAG;IACRsC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAEnC,QAAQ;IACfgB,IAAI,EAAEjB,eAAe,CAACiB,IAAI,IAAIf,QAAQ,GAAGF,eAAe,CAACiB,IAAI,GAAGf;IAChE;IACA;IACA;IAAA;;IAEAmC,kBAAkB,EAAEhB,eAAe;IACnCiB,WAAW,EAAEtC,eAAe,CAACM,QAAQ;IACrCiC,YAAY,EAAExB,gBAAgB;IAC9ByB,mBAAmB,EAAEjC;EACvB,CAAC,EAAET,MAAM,CAACe,OAAO,CAAC4B,aAAa,CAAC,oBAAoB,CAAC,EAAE7C,KAAK,CAAC,CAAC;AAChE,CAAC,CAAC;AACF4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhC,cAAc,CAACgD,SAAS,GAAG;EACjE;EACA;EACA;EACA;EACAP,SAAS,EAAE7D,SAAS,CAACqE;AACvB,CAAC,GAAG,KAAK,CAAC;AACV,SAASjD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}