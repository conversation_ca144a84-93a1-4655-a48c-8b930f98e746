{"ast": null, "code": "export const isEscapeKey = key => key === 'Escape'; // TODO remove\nexport const isEnterKey = key => key === 'Enter'; // TODO remove\nexport const isTabKey = key => key === 'Tab'; // TODO remove\n\nexport const isSpaceKey = key => key === ' ';\nexport const isArrowKeys = key => key.indexOf('Arrow') === 0;\nexport const isHomeOrEndKeys = key => key === 'Home' || key === 'End';\nexport const isPageKeys = key => key.indexOf('Page') === 0;\nexport const isDeleteKeys = key => key === 'Delete' || key === 'Backspace';\n\n// Non printable keys have a name, e.g. \"ArrowRight\", see the whole list:\n// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values\n// So event.key.length === 1 is often enough.\n//\n// However, we also need to ignore shortcuts, for example: select all:\n// - Windows: Ctrl+A, event.ctrlKey is true\n// - macOS: ⌘ Command+A, event.metaKey is true\nexport function isPrintableKey(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\nexport const GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];\nexport const GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];\nexport const GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];\nexport const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;\nexport const isCellEnterEditModeKeys = event => isEnterKey(event.key) || isDeleteKeys(event.key) || isPrintableKey(event);\nexport const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;\nexport const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;\nexport const isNavigationKey = key => isHomeOrEndKeys(key) || isArrowKeys(key) || isPageKeys(key) || isSpaceKey(key);\nexport const isKeyboardEvent = event => !!event.key;\nexport const isHideMenuKey = key => isTabKey(key) || isEscapeKey(key);", "map": {"version": 3, "names": ["isEscapeKey", "key", "isEnterKey", "isTabKey", "isSpaceKey", "isArrowKeys", "indexOf", "isHomeOrEndKeys", "isPageKeys", "isDeleteKeys", "isPrintableKey", "event", "length", "ctrl<PERSON>ey", "metaKey", "GRID_MULTIPLE_SELECTION_KEYS", "GRID_CELL_EXIT_EDIT_MODE_KEYS", "GRID_CELL_EDIT_COMMIT_KEYS", "isMultipleKey", "isCellEnterEditModeKeys", "isCellExitEditModeKeys", "isCellEditCommitKeys", "isNavigationKey", "isKeyboardEvent", "isHideMenuKey"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/utils/keyboardUtils.js"], "sourcesContent": ["export const isEscapeKey = key => key === 'Escape'; // TODO remove\nexport const isEnterKey = key => key === 'Enter'; // TODO remove\nexport const isTabKey = key => key === 'Tab'; // TODO remove\n\nexport const isSpaceKey = key => key === ' ';\nexport const isArrowKeys = key => key.indexOf('Arrow') === 0;\nexport const isHomeOrEndKeys = key => key === 'Home' || key === 'End';\nexport const isPageKeys = key => key.indexOf('Page') === 0;\nexport const isDeleteKeys = key => key === 'Delete' || key === 'Backspace';\n\n// Non printable keys have a name, e.g. \"ArrowRight\", see the whole list:\n// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values\n// So event.key.length === 1 is often enough.\n//\n// However, we also need to ignore shortcuts, for example: select all:\n// - Windows: Ctrl+A, event.ctrlKey is true\n// - macOS: ⌘ Command+A, event.metaKey is true\nexport function isPrintableKey(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\nexport const GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];\nexport const GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];\nexport const GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];\nexport const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;\nexport const isCellEnterEditModeKeys = event => isEnterKey(event.key) || isDeleteKeys(event.key) || isPrintableKey(event);\nexport const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;\nexport const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;\nexport const isNavigationKey = key => isHomeOrEndKeys(key) || isArrowKeys(key) || isPageKeys(key) || isSpaceKey(key);\nexport const isKeyboardEvent = event => !!event.key;\nexport const isHideMenuKey = key => isTabKey(key) || isEscapeKey(key);"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAGC,GAAG,IAAIA,GAAG,KAAK,QAAQ,CAAC,CAAC;AACpD,OAAO,MAAMC,UAAU,GAAGD,GAAG,IAAIA,GAAG,KAAK,OAAO,CAAC,CAAC;AAClD,OAAO,MAAME,QAAQ,GAAGF,GAAG,IAAIA,GAAG,KAAK,KAAK,CAAC,CAAC;;AAE9C,OAAO,MAAMG,UAAU,GAAGH,GAAG,IAAIA,GAAG,KAAK,GAAG;AAC5C,OAAO,MAAMI,WAAW,GAAGJ,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC5D,OAAO,MAAMC,eAAe,GAAGN,GAAG,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK;AACrE,OAAO,MAAMO,UAAU,GAAGP,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1D,OAAO,MAAMG,YAAY,GAAGR,GAAG,IAAIA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,WAAW;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOA,KAAK,CAACV,GAAG,CAACW,MAAM,KAAK,CAAC,IAAI,CAACD,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACG,OAAO;AACnE;AACA,OAAO,MAAMC,4BAA4B,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;AACxE,OAAO,MAAMC,6BAA6B,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;AACvE,OAAO,MAAMC,0BAA0B,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AAC1D,OAAO,MAAMC,aAAa,GAAGjB,GAAG,IAAIc,4BAA4B,CAACT,OAAO,CAACL,GAAG,CAAC,GAAG,CAAC,CAAC;AAClF,OAAO,MAAMkB,uBAAuB,GAAGR,KAAK,IAAIT,UAAU,CAACS,KAAK,CAACV,GAAG,CAAC,IAAIQ,YAAY,CAACE,KAAK,CAACV,GAAG,CAAC,IAAIS,cAAc,CAACC,KAAK,CAAC;AACzH,OAAO,MAAMS,sBAAsB,GAAGnB,GAAG,IAAIe,6BAA6B,CAACV,OAAO,CAACL,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5F,OAAO,MAAMoB,oBAAoB,GAAGpB,GAAG,IAAIgB,0BAA0B,CAACX,OAAO,CAACL,GAAG,CAAC,GAAG,CAAC,CAAC;AACvF,OAAO,MAAMqB,eAAe,GAAGrB,GAAG,IAAIM,eAAe,CAACN,GAAG,CAAC,IAAII,WAAW,CAACJ,GAAG,CAAC,IAAIO,UAAU,CAACP,GAAG,CAAC,IAAIG,UAAU,CAACH,GAAG,CAAC;AACpH,OAAO,MAAMsB,eAAe,GAAGZ,KAAK,IAAI,CAAC,CAACA,KAAK,CAACV,GAAG;AACnD,OAAO,MAAMuB,aAAa,GAAGvB,GAAG,IAAIE,QAAQ,CAACF,GAAG,CAAC,IAAID,WAAW,CAACC,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}