{"ast": null, "code": "import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\nimport { gridSortedRowEntriesSelector } from '../sorting/gridSortingSelector';\nimport { gridColumnLookupSelector } from '../columns/gridColumnsSelector';\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from '../rows/gridRowsSelector';\n\n/**\n * @category Filtering\n */\nconst gridFilterStateSelector = state => state.filter;\n\n/**\n * Get the current filter model.\n * @category Filtering\n */\nexport const gridFilterModelSelector = createSelector(gridFilterStateSelector, filterState => filterState.filterModel);\n\n/**\n * Get the current quick filter values.\n * @category Filtering\n */\nexport const gridQuickFilterValuesSelector = createSelector(gridFilterModelSelector, filterModel => filterModel.quickFilterValues);\n\n/**\n * @category Visible rows\n * @ignore - do not document.\n */\nexport const gridVisibleRowsLookupSelector = state => state.visibleRowsLookup;\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredRowsLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredRowsLookup);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredDescendantCountLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredDescendantCountLookup);\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowEntriesSelector = createSelectorMemoized(gridVisibleRowsLookupSelector, gridSortedRowEntriesSelector, (visibleRowsLookup, sortedRows) => sortedRows.filter(row => visibleRowsLookup[row.id] !== false));\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, visibleSortedRowEntries => visibleSortedRowEntries.map(row => row.id));\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowEntriesSelector = createSelectorMemoized(gridFilteredRowsLookupSelector, gridSortedRowEntriesSelector, (filteredRowsLookup, sortedRows) => sortedRows.filter(row => filteredRowsLookup[row.id] !== false));\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowIdsSelector = createSelectorMemoized(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.map(row => row.id));\n\n/**\n * Get the id and the model of the top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredSortedTopLevelRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, (visibleSortedRows, rowTree, rowTreeDepth) => {\n  if (rowTreeDepth < 2) {\n    return visibleSortedRows;\n  }\n  return visibleSortedRows.filter(row => {\n    var _rowTree$row$id;\n    return ((_rowTree$row$id = rowTree[row.id]) == null ? void 0 : _rowTree$row$id.depth) === 0;\n  });\n});\n\n/**\n * Get the amount of rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridExpandedRowCountSelector = createSelector(gridExpandedSortedRowEntriesSelector, visibleSortedRows => visibleSortedRows.length);\n\n/**\n * Get the amount of top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredTopLevelRowCountSelector = createSelector(gridFilteredSortedTopLevelRowEntriesSelector, visibleSortedTopLevelRows => visibleSortedTopLevelRows.length);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsSelector = createSelectorMemoized(gridFilterModelSelector, gridColumnLookupSelector, (filterModel, columnLookup) => {\n  var _filterModel$items;\n  return (_filterModel$items = filterModel.items) == null ? void 0 : _filterModel$items.filter(item => {\n    var _column$filterOperato, _item$value;\n    if (!item.field) {\n      return false;\n    }\n    const column = columnLookup[item.field];\n    if (!(column != null && column.filterOperators) || (column == null || (_column$filterOperato = column.filterOperators) == null ? void 0 : _column$filterOperato.length) === 0) {\n      return false;\n    }\n    const filterOperator = column.filterOperators.find(operator => operator.value === item.operator);\n    if (!filterOperator) {\n      return false;\n    }\n    return !filterOperator.InputComponent || item.value != null && ((_item$value = item.value) == null ? void 0 : _item$value.toString()) !== '';\n  });\n});\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsLookupSelector = createSelectorMemoized(gridFilterActiveItemsSelector, activeFilters => {\n  const result = activeFilters.reduce((res, filterItem) => {\n    if (!res[filterItem.field]) {\n      res[filterItem.field] = [filterItem];\n    } else {\n      res[filterItem.field].push(filterItem);\n    }\n    return res;\n  }, {});\n  return result;\n});", "map": {"version": 3, "names": ["createSelector", "createSelectorMemoized", "gridSortedRowEntriesSelector", "gridColumnLookupSelector", "gridRowMaximumTreeDepthSelector", "gridRowTreeSelector", "gridFilterStateSelector", "state", "filter", "gridFilterModelSelector", "filterState", "filterModel", "gridQuickFilterValuesSelector", "quickFilterV<PERSON>ues", "gridVisibleRowsLookupSelector", "visibleRowsLookup", "gridFilteredRowsLookupSelector", "filteredRowsLookup", "gridFilteredDescendantCountLookupSelector", "filteredDescendantCountLookup", "gridExpandedSortedRowEntriesSelector", "sortedRows", "row", "id", "gridExpandedSortedRowIdsSelector", "visibleSortedRowEntries", "map", "gridFilteredSortedRowEntriesSelector", "gridFilteredSortedRowIdsSelector", "filteredSortedRowEntries", "gridFilteredSortedTopLevelRowEntriesSelector", "visibleSortedRows", "rowTree", "row<PERSON><PERSON><PERSON><PERSON><PERSON>", "_rowTree$row$id", "depth", "gridExpandedRowCountSelector", "length", "gridFilteredTopLevelRowCountSelector", "visibleSortedTopLevelRows", "gridFilterActiveItemsSelector", "columnLookup", "_filterModel$items", "items", "item", "_column$filterOperato", "_item$value", "field", "column", "filterOperators", "filterOperator", "find", "operator", "value", "InputComponent", "toString", "gridFilterActiveItemsLookupSelector", "activeFilters", "result", "reduce", "res", "filterItem", "push"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/filter/gridFilterSelector.js"], "sourcesContent": ["import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\nimport { gridSortedRowEntriesSelector } from '../sorting/gridSortingSelector';\nimport { gridColumnLookupSelector } from '../columns/gridColumnsSelector';\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from '../rows/gridRowsSelector';\n\n/**\n * @category Filtering\n */\nconst gridFilterStateSelector = state => state.filter;\n\n/**\n * Get the current filter model.\n * @category Filtering\n */\nexport const gridFilterModelSelector = createSelector(gridFilterStateSelector, filterState => filterState.filterModel);\n\n/**\n * Get the current quick filter values.\n * @category Filtering\n */\nexport const gridQuickFilterValuesSelector = createSelector(gridFilterModelSelector, filterModel => filterModel.quickFilterValues);\n\n/**\n * @category Visible rows\n * @ignore - do not document.\n */\nexport const gridVisibleRowsLookupSelector = state => state.visibleRowsLookup;\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredRowsLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredRowsLookup);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredDescendantCountLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredDescendantCountLookup);\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowEntriesSelector = createSelectorMemoized(gridVisibleRowsLookupSelector, gridSortedRowEntriesSelector, (visibleRowsLookup, sortedRows) => sortedRows.filter(row => visibleRowsLookup[row.id] !== false));\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, visibleSortedRowEntries => visibleSortedRowEntries.map(row => row.id));\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowEntriesSelector = createSelectorMemoized(gridFilteredRowsLookupSelector, gridSortedRowEntriesSelector, (filteredRowsLookup, sortedRows) => sortedRows.filter(row => filteredRowsLookup[row.id] !== false));\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowIdsSelector = createSelectorMemoized(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.map(row => row.id));\n\n/**\n * Get the id and the model of the top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredSortedTopLevelRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, (visibleSortedRows, rowTree, rowTreeDepth) => {\n  if (rowTreeDepth < 2) {\n    return visibleSortedRows;\n  }\n  return visibleSortedRows.filter(row => {\n    var _rowTree$row$id;\n    return ((_rowTree$row$id = rowTree[row.id]) == null ? void 0 : _rowTree$row$id.depth) === 0;\n  });\n});\n\n/**\n * Get the amount of rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridExpandedRowCountSelector = createSelector(gridExpandedSortedRowEntriesSelector, visibleSortedRows => visibleSortedRows.length);\n\n/**\n * Get the amount of top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredTopLevelRowCountSelector = createSelector(gridFilteredSortedTopLevelRowEntriesSelector, visibleSortedTopLevelRows => visibleSortedTopLevelRows.length);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsSelector = createSelectorMemoized(gridFilterModelSelector, gridColumnLookupSelector, (filterModel, columnLookup) => {\n  var _filterModel$items;\n  return (_filterModel$items = filterModel.items) == null ? void 0 : _filterModel$items.filter(item => {\n    var _column$filterOperato, _item$value;\n    if (!item.field) {\n      return false;\n    }\n    const column = columnLookup[item.field];\n    if (!(column != null && column.filterOperators) || (column == null || (_column$filterOperato = column.filterOperators) == null ? void 0 : _column$filterOperato.length) === 0) {\n      return false;\n    }\n    const filterOperator = column.filterOperators.find(operator => operator.value === item.operator);\n    if (!filterOperator) {\n      return false;\n    }\n    return !filterOperator.InputComponent || item.value != null && ((_item$value = item.value) == null ? void 0 : _item$value.toString()) !== '';\n  });\n});\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsLookupSelector = createSelectorMemoized(gridFilterActiveItemsSelector, activeFilters => {\n  const result = activeFilters.reduce((res, filterItem) => {\n    if (!res[filterItem.field]) {\n      res[filterItem.field] = [filterItem];\n    } else {\n      res[filterItem.field].push(filterItem);\n    }\n    return res;\n  }, {});\n  return result;\n});"], "mappings": "AAAA,SAASA,cAAc,EAAEC,sBAAsB,QAAQ,+BAA+B;AACtF,SAASC,4BAA4B,QAAQ,gCAAgC;AAC7E,SAASC,wBAAwB,QAAQ,gCAAgC;AACzE,SAASC,+BAA+B,EAAEC,mBAAmB,QAAQ,0BAA0B;;AAE/F;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGC,KAAK,IAAIA,KAAK,CAACC,MAAM;;AAErD;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGT,cAAc,CAACM,uBAAuB,EAAEI,WAAW,IAAIA,WAAW,CAACC,WAAW,CAAC;;AAEtH;AACA;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAGZ,cAAc,CAACS,uBAAuB,EAAEE,WAAW,IAAIA,WAAW,CAACE,iBAAiB,CAAC;;AAElI;AACA;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAGP,KAAK,IAAIA,KAAK,CAACQ,iBAAiB;;AAE7E;AACA;AACA;AACA;AACA,OAAO,MAAMC,8BAA8B,GAAGhB,cAAc,CAACM,uBAAuB,EAAEI,WAAW,IAAIA,WAAW,CAACO,kBAAkB,CAAC;;AAEpI;AACA;AACA;AACA;AACA,OAAO,MAAMC,yCAAyC,GAAGlB,cAAc,CAACM,uBAAuB,EAAEI,WAAW,IAAIA,WAAW,CAACS,6BAA6B,CAAC;;AAE1J;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGnB,sBAAsB,CAACa,6BAA6B,EAAEZ,4BAA4B,EAAE,CAACa,iBAAiB,EAAEM,UAAU,KAAKA,UAAU,CAACb,MAAM,CAACc,GAAG,IAAIP,iBAAiB,CAACO,GAAG,CAACC,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC;;AAEzO;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gCAAgC,GAAGvB,sBAAsB,CAACmB,oCAAoC,EAAEK,uBAAuB,IAAIA,uBAAuB,CAACC,GAAG,CAACJ,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC,CAAC;;AAEnL;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,oCAAoC,GAAG1B,sBAAsB,CAACe,8BAA8B,EAAEd,4BAA4B,EAAE,CAACe,kBAAkB,EAAEI,UAAU,KAAKA,UAAU,CAACb,MAAM,CAACc,GAAG,IAAIL,kBAAkB,CAACK,GAAG,CAACC,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC;;AAE5O;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,gCAAgC,GAAG3B,sBAAsB,CAAC0B,oCAAoC,EAAEE,wBAAwB,IAAIA,wBAAwB,CAACH,GAAG,CAACJ,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC,CAAC;;AAErL;AACA;AACA;AACA;AACA,OAAO,MAAMO,4CAA4C,GAAG7B,sBAAsB,CAACmB,oCAAoC,EAAEf,mBAAmB,EAAED,+BAA+B,EAAE,CAAC2B,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,KAAK;EAC3N,IAAIA,YAAY,GAAG,CAAC,EAAE;IACpB,OAAOF,iBAAiB;EAC1B;EACA,OAAOA,iBAAiB,CAACvB,MAAM,CAACc,GAAG,IAAI;IACrC,IAAIY,eAAe;IACnB,OAAO,CAAC,CAACA,eAAe,GAAGF,OAAO,CAACV,GAAG,CAACC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,eAAe,CAACC,KAAK,MAAM,CAAC;EAC7F,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMC,4BAA4B,GAAGpC,cAAc,CAACoB,oCAAoC,EAAEW,iBAAiB,IAAIA,iBAAiB,CAACM,MAAM,CAAC;;AAE/I;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGtC,cAAc,CAAC8B,4CAA4C,EAAES,yBAAyB,IAAIA,yBAAyB,CAACF,MAAM,CAAC;;AAE/K;AACA;AACA;AACA;AACA,OAAO,MAAMG,6BAA6B,GAAGvC,sBAAsB,CAACQ,uBAAuB,EAAEN,wBAAwB,EAAE,CAACQ,WAAW,EAAE8B,YAAY,KAAK;EACpJ,IAAIC,kBAAkB;EACtB,OAAO,CAACA,kBAAkB,GAAG/B,WAAW,CAACgC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,kBAAkB,CAAClC,MAAM,CAACoC,IAAI,IAAI;IACnG,IAAIC,qBAAqB,EAAEC,WAAW;IACtC,IAAI,CAACF,IAAI,CAACG,KAAK,EAAE;MACf,OAAO,KAAK;IACd;IACA,MAAMC,MAAM,GAAGP,YAAY,CAACG,IAAI,CAACG,KAAK,CAAC;IACvC,IAAI,EAAEC,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,eAAe,CAAC,IAAI,CAACD,MAAM,IAAI,IAAI,IAAI,CAACH,qBAAqB,GAAGG,MAAM,CAACC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,qBAAqB,CAACR,MAAM,MAAM,CAAC,EAAE;MAC7K,OAAO,KAAK;IACd;IACA,MAAMa,cAAc,GAAGF,MAAM,CAACC,eAAe,CAACE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,KAAK,KAAKT,IAAI,CAACQ,QAAQ,CAAC;IAChG,IAAI,CAACF,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;IACA,OAAO,CAACA,cAAc,CAACI,cAAc,IAAIV,IAAI,CAACS,KAAK,IAAI,IAAI,IAAI,CAAC,CAACP,WAAW,GAAGF,IAAI,CAACS,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,WAAW,CAACS,QAAQ,CAAC,CAAC,MAAM,EAAE;EAC9I,CAAC,CAAC;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,OAAO,MAAMC,mCAAmC,GAAGvD,sBAAsB,CAACuC,6BAA6B,EAAEiB,aAAa,IAAI;EACxH,MAAMC,MAAM,GAAGD,aAAa,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,UAAU,KAAK;IACvD,IAAI,CAACD,GAAG,CAACC,UAAU,CAACd,KAAK,CAAC,EAAE;MAC1Ba,GAAG,CAACC,UAAU,CAACd,KAAK,CAAC,GAAG,CAACc,UAAU,CAAC;IACtC,CAAC,MAAM;MACLD,GAAG,CAACC,UAAU,CAACd,KAAK,CAAC,CAACe,IAAI,CAACD,UAAU,CAAC;IACxC;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOF,MAAM;AACf,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}