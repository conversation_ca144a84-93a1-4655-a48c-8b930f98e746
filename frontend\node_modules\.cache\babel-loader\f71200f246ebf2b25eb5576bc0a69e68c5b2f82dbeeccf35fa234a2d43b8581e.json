{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Box,Typography,TextField,Button,Grid,InputAdornment,CircularProgress,Card,CardContent,Chip,Alert,Tooltip}from'@mui/material';import ArrowBackIcon from'@mui/icons-material/ArrowBack';import SendIcon from'@mui/icons-material/Send';import SettingsIcon from'@mui/icons-material/Settings';import InfoIcon from'@mui/icons-material/Info';import MonetizationOnIcon from'@mui/icons-material/MonetizationOn';import TableViewIcon from'@mui/icons-material/TableView';import axios from'axios';import{API_URL}from'../config';// localStorage的键名\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STORAGE_KEY='processFormParams';const ProcessForm=_ref=>{let{fileId,worksheet,onSubmit,onBack,onError}=_ref;// 从localStorage获取上次保存的参数\nconst[startCol,setStartCol]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).startCol||'':'';});const[endCol,setEndCol]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).endCol||'':'';});const[perHourRate,setPerHourRate]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).perHourRate||'':'';});const[cbuCarHourRate,setCbuCarHourRate]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).cbuCarHourRate||'':'';});const[wtyHourRate,setWtyHourRate]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).wtyHourRate||'':'';});const[wtyCommissionRate,setWtyCommissionRate]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).wtyCommissionRate||'':'';});const[commissionRate,setCommissionRate]=useState(()=>{const savedParams=localStorage.getItem(STORAGE_KEY);return savedParams?JSON.parse(savedParams).commissionRate||'':'';});const[loading,setLoading]=useState(false);const[errors,setErrors]=useState({});// 验证函数\nconst validateColumn=value=>{const colRegex=/^[A-Za-z]+$/;if(!value)return'请输入列名';if(!colRegex.test(value))return'列名格式无效，请使用字母 (例如: A, AB, AT)';return'';};const validateRate=(value,fieldName)=>{if(value&&(isNaN(value)||parseFloat(value)<0)){return\"\".concat(fieldName,\"\\u5FC5\\u987B\\u662F\\u6B63\\u6570\");}return'';};// 当参数变化时保存到localStorage\nuseEffect(()=>{const paramsToSave={startCol,endCol,perHourRate,cbuCarHourRate,wtyHourRate,wtyCommissionRate,commissionRate};localStorage.setItem(STORAGE_KEY,JSON.stringify(paramsToSave));},[startCol,endCol,perHourRate,cbuCarHourRate,wtyHourRate,wtyCommissionRate,commissionRate]);const handleSubmit=async e=>{e.preventDefault();// 验证输入\nconst newErrors={};const startColError=validateColumn(startCol);const endColError=validateColumn(endCol);const perHourRateError=validateRate(perHourRate,'小时费率');const cbuCarHourRateError=validateRate(cbuCarHourRate,'CBU CAR小时费率');const wtyHourRateError=validateRate(wtyHourRate,'WTY小时费率');const wtyCommissionRateError=validateRate(wtyCommissionRate,'WTY佣金率');const commissionRateError=validateRate(commissionRate,'佣金率');if(startColError)newErrors.startCol=startColError;if(endColError)newErrors.endCol=endColError;if(perHourRateError)newErrors.perHourRate=perHourRateError;if(cbuCarHourRateError)newErrors.cbuCarHourRate=cbuCarHourRateError;if(wtyHourRateError)newErrors.wtyHourRate=wtyHourRateError;if(wtyCommissionRateError)newErrors.wtyCommissionRate=wtyCommissionRateError;if(commissionRateError)newErrors.commissionRate=commissionRateError;if(commissionRate&&parseFloat(commissionRate)>1){newErrors.commissionRate='佣金率应在0-1之间';}if(wtyCommissionRate&&parseFloat(wtyCommissionRate)>1){newErrors.wtyCommissionRate='WTY佣金率应在0-1之间';}setErrors(newErrors);if(Object.keys(newErrors).length>0){onError('请检查输入的参数');return;}setLoading(true);try{const requestData={fileId,worksheet,startCol,endCol,perHourRate:perHourRate||undefined,cbuCarHourRate:cbuCarHourRate||undefined,wtyHourRate:wtyHourRate||undefined,wtyCommissionRate:wtyCommissionRate||undefined,commissionRate:commissionRate||undefined};const response=await axios.post(\"\".concat(API_URL,\"/process\"),requestData);if(response.data&&response.data.success){onSubmit({startCol,endCol,perHourRate,cbuCarHourRate,wtyHourRate,wtyCommissionRate,commissionRate},response.data.data);}else{onError('处理数据失败，请重试');}}catch(error){var _error$response,_error$response$data;console.error('处理数据出错:',error);onError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.error)||'处理数据失败，请重试');}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,noValidate:true,children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',mb:2,gap:2},children:[/*#__PURE__*/_jsx(SettingsIcon,{sx:{fontSize:32,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:'text.primary'},children:\"\\u53C2\\u6570\\u8BBE\\u7F6E\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',gap:1,mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'text.secondary'},children:\"\\u5F53\\u524D\\u5DE5\\u4F5C\\u8868:\"}),/*#__PURE__*/_jsx(Chip,{label:worksheet,size:\"small\",color:\"primary\",variant:\"outlined\"})]}),/*#__PURE__*/_jsx(Alert,{severity:\"info\",icon:/*#__PURE__*/_jsx(InfoIcon,{}),sx:{textAlign:'left'},children:\"\\u8BF7\\u8BBE\\u7F6E\\u6570\\u636E\\u5904\\u7406\\u7684\\u5217\\u8303\\u56F4\\u548C\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\\u3002\\u5217\\u8303\\u56F4\\u4E3A\\u5FC5\\u586B\\u9879\\uFF0C\\u4F63\\u91D1\\u53C2\\u6570\\u4E3A\\u53EF\\u9009\\u9879\\u3002\"})]}),/*#__PURE__*/_jsx(Card,{sx:{mb:3},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(TableViewIcon,{sx:{color:'primary.main',mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600},children:\"\\u6570\\u636E\\u5217\\u8303\\u56F4\"}),/*#__PURE__*/_jsx(Chip,{label:\"\\u5FC5\\u586B\",size:\"small\",color:\"error\",sx:{ml:2}})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,label:\"\\u8D77\\u59CB\\u5217\",value:startCol,onChange:e=>{const value=e.target.value.toUpperCase();setStartCol(value);if(errors.startCol){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{startCol:''}));}},placeholder:\"\\u4F8B\\u5982: AT\",error:!!errors.startCol,helperText:errors.startCol||\"请输入起始列的字母\",inputProps:{maxLength:3},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,label:\"\\u7ED3\\u675F\\u5217\",value:endCol,onChange:e=>{const value=e.target.value.toUpperCase();setEndCol(value);if(errors.endCol){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{endCol:''}));}},placeholder:\"\\u4F8B\\u5982: BP\",error:!!errors.endCol,helperText:errors.endCol||\"请输入结束列的字母\",inputProps:{maxLength:3},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})})]})]})}),/*#__PURE__*/_jsx(Card,{sx:{mb:4},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(MonetizationOnIcon,{sx:{color:'secondary.main',mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600},children:\"\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\"}),/*#__PURE__*/_jsx(Chip,{label:\"\\u53EF\\u9009\",size:\"small\",color:\"success\",sx:{ml:2}})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"\\u5C0F\\u65F6\\u8D39\\u7387\",type:\"number\",value:perHourRate,onChange:e=>{setPerHourRate(e.target.value);if(errors.perHourRate){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{perHourRate:''}));}},placeholder:\"\\u4F8B\\u5982: 65\",error:!!errors.perHourRate,helperText:errors.perHourRate||\"每小时费率 (RM)\",InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"RM\"})},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"CBU CAR\\u5C0F\\u65F6\\u8D39\\u7387\",type:\"number\",value:cbuCarHourRate,onChange:e=>{setCbuCarHourRate(e.target.value);if(errors.cbuCarHourRate){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{cbuCarHourRate:''}));}},placeholder:\"\\u4F8B\\u5982: 80\",error:!!errors.cbuCarHourRate,helperText:errors.cbuCarHourRate||\"CBU CAR每小时费率 (RM)\",InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"RM\"})},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"WTY\\u5C0F\\u65F6\\u8D39\\u7387\",type:\"number\",value:wtyHourRate,onChange:e=>{setWtyHourRate(e.target.value);if(errors.wtyHourRate){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{wtyHourRate:''}));}},placeholder:\"\\u4F8B\\u5982: 70\",error:!!errors.wtyHourRate,helperText:errors.wtyHourRate||\"WTY每小时费率 (RM)\",InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"RM\"})},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u4F63\\u91D1\\u6BD4\\u4F8B\\uFF0C\\u8303\\u56F4\\u57280\\u52301\\u4E4B\\u95F4\\uFF0C\\u4F8B\\u59820.6\\u8868\\u793A60%\",children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"\\u4F63\\u91D1\\u7387\",type:\"number\",value:commissionRate,onChange:e=>{setCommissionRate(e.target.value);if(errors.commissionRate){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{commissionRate:''}));}},placeholder:\"\\u4F8B\\u5982: 0.6\",error:!!errors.commissionRate,helperText:errors.commissionRate||\"佣金比例 (0-1 之间)\",inputProps:{step:\"0.01\",min:\"0\",max:\"1\"},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(Tooltip,{title:\"WTY\\u4F63\\u91D1\\u6BD4\\u4F8B\\uFF0C\\u8303\\u56F4\\u57280\\u52301\\u4E4B\\u95F4\\uFF0C\\u4F8B\\u59820.5\\u8868\\u793A50%\",children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"WTY\\u4F63\\u91D1\\u7387\",type:\"number\",value:wtyCommissionRate,onChange:e=>{setWtyCommissionRate(e.target.value);if(errors.wtyCommissionRate){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{wtyCommissionRate:''}));}},placeholder:\"\\u4F8B\\u5982: 0.5\",error:!!errors.wtyCommissionRate,helperText:errors.wtyCommissionRate||\"WTY佣金比例 (0-1 之间)\",inputProps:{step:\"0.01\",min:\"0\",max:\"1\"},sx:{'& .MuiOutlinedInput-root':{borderRadius:2}}})})})]})]})}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',gap:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:onBack,disabled:loading,children:\"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u6B65\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",endIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(SendIcon,{}),disabled:loading,children:loading?'处理中...':'开始处理'})]})]});};export default ProcessForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "InputAdornment", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ArrowBackIcon", "SendIcon", "SettingsIcon", "InfoIcon", "MonetizationOnIcon", "TableViewIcon", "axios", "API_URL", "jsx", "_jsx", "jsxs", "_jsxs", "STORAGE_KEY", "ProcessForm", "_ref", "fileId", "worksheet", "onSubmit", "onBack", "onError", "startCol", "setStartCol", "savedParams", "localStorage", "getItem", "JSON", "parse", "endCol", "setEndCol", "perHourRate", "setPerHourRate", "cbuCarHourRate", "setCbuCarHourRate", "wtyHourRate", "setWtyHourRate", "wtyCommissionRate", "setWtyCommissionRate", "commissionRate", "setCommissionRate", "loading", "setLoading", "errors", "setErrors", "validateColumn", "value", "colRegex", "test", "validateRate", "fieldName", "isNaN", "parseFloat", "concat", "paramsToSave", "setItem", "stringify", "handleSubmit", "e", "preventDefault", "newErrors", "startColError", "endColError", "perHourRateError", "cbuCarHourRateError", "wtyHourRateError", "wtyCommissionRateError", "commissionRateError", "Object", "keys", "length", "requestData", "undefined", "response", "post", "data", "success", "error", "_error$response", "_error$response$data", "console", "component", "noValidate", "children", "sx", "textAlign", "mb", "display", "alignItems", "justifyContent", "gap", "fontSize", "color", "variant", "fontWeight", "label", "size", "severity", "icon", "mr", "ml", "container", "spacing", "item", "xs", "sm", "required", "fullWidth", "onChange", "target", "toUpperCase", "prev", "_objectSpread", "placeholder", "helperText", "inputProps", "max<PERSON><PERSON><PERSON>", "borderRadius", "type", "InputProps", "startAdornment", "position", "title", "step", "min", "max", "startIcon", "onClick", "disabled", "endIcon"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ProcessForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  TextField,\n  Button,\n  Grid,\n  InputAdornment,\n  CircularProgress,\n  Card,\n  CardContent,\n  Chip,\n  Alert,\n  Tooltip\n} from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SendIcon from '@mui/icons-material/Send';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport InfoIcon from '@mui/icons-material/Info';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// localStorage的键名\nconst STORAGE_KEY = 'processFormParams';\n\nconst ProcessForm = ({ fileId, worksheet, onSubmit, onBack, onError }) => {\n\n  // 从localStorage获取上次保存的参数\n  const [startCol, setStartCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).startCol || '' : '';\n  });\n\n  const [endCol, setEndCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).endCol || '' : '';\n  });\n\n  const [perHourRate, setPerHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).perHourRate || '' : '';\n  });\n\n  const [cbuCarHourRate, setCbuCarHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).cbuCarHourRate || '' : '';\n  });\n\n  const [wtyHourRate, setWtyHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyHourRate || '' : '';\n  });\n\n  const [wtyCommissionRate, setWtyCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyCommissionRate || '' : '';\n  });\n\n  const [commissionRate, setCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).commissionRate || '' : '';\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // 验证函数\n  const validateColumn = (value) => {\n    const colRegex = /^[A-Za-z]+$/;\n    if (!value) return '请输入列名';\n    if (!colRegex.test(value)) return '列名格式无效，请使用字母 (例如: A, AB, AT)';\n    return '';\n  };\n\n  const validateRate = (value, fieldName) => {\n    if (value && (isNaN(value) || parseFloat(value) < 0)) {\n      return `${fieldName}必须是正数`;\n    }\n    return '';\n  };\n  \n  // 当参数变化时保存到localStorage\n  useEffect(() => {\n    const paramsToSave = {\n      startCol,\n      endCol,\n      perHourRate,\n      cbuCarHourRate,\n      wtyHourRate,\n      wtyCommissionRate,\n      commissionRate\n    };\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(paramsToSave));\n  }, [startCol, endCol, perHourRate, cbuCarHourRate, wtyHourRate, wtyCommissionRate, commissionRate]);\n  \n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // 验证输入\n    const newErrors = {};\n\n    const startColError = validateColumn(startCol);\n    const endColError = validateColumn(endCol);\n    const perHourRateError = validateRate(perHourRate, '小时费率');\n    const cbuCarHourRateError = validateRate(cbuCarHourRate, 'CBU CAR小时费率');\n    const wtyHourRateError = validateRate(wtyHourRate, 'WTY小时费率');\n    const wtyCommissionRateError = validateRate(wtyCommissionRate, 'WTY佣金率');\n    const commissionRateError = validateRate(commissionRate, '佣金率');\n\n    if (startColError) newErrors.startCol = startColError;\n    if (endColError) newErrors.endCol = endColError;\n    if (perHourRateError) newErrors.perHourRate = perHourRateError;\n    if (cbuCarHourRateError) newErrors.cbuCarHourRate = cbuCarHourRateError;\n    if (wtyHourRateError) newErrors.wtyHourRate = wtyHourRateError;\n    if (wtyCommissionRateError) newErrors.wtyCommissionRate = wtyCommissionRateError;\n    if (commissionRateError) newErrors.commissionRate = commissionRateError;\n\n    if (commissionRate && (parseFloat(commissionRate) > 1)) {\n      newErrors.commissionRate = '佣金率应在0-1之间';\n    }\n\n    if (wtyCommissionRate && (parseFloat(wtyCommissionRate) > 1)) {\n      newErrors.wtyCommissionRate = 'WTY佣金率应在0-1之间';\n    }\n\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      onError('请检查输入的参数');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const requestData = {\n        fileId,\n        worksheet,\n        startCol,\n        endCol,\n        perHourRate: perHourRate || undefined,\n        cbuCarHourRate: cbuCarHourRate || undefined,\n        wtyHourRate: wtyHourRate || undefined,\n        wtyCommissionRate: wtyCommissionRate || undefined,\n        commissionRate: commissionRate || undefined\n      };\n\n      const response = await axios.post(`${API_URL}/process`, requestData);\n\n      if (response.data && response.data.success) {\n        onSubmit(\n          { startCol, endCol, perHourRate, cbuCarHourRate, wtyHourRate, wtyCommissionRate, commissionRate },\n          response.data.data\n        );\n      } else {\n        onError('处理数据失败，请重试');\n      }\n    } catch (error) {\n      console.error('处理数据出错:', error);\n      onError(error.response?.data?.error || '处理数据失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {/* 标题区域 */}\n      <Box sx={{ textAlign: 'center', mb: 4 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2, gap: 2 }}>\n          <SettingsIcon sx={{ fontSize: 32, color: 'primary.main' }} />\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary' }}>\n            参数设置\n          </Typography>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>\n          <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n            当前工作表:\n          </Typography>\n          <Chip label={worksheet} size=\"small\" color=\"primary\" variant=\"outlined\" />\n        </Box>\n\n        <Alert severity=\"info\" icon={<InfoIcon />} sx={{ textAlign: 'left' }}>\n          请设置数据处理的列范围和佣金计算参数。列范围为必填项，佣金参数为可选项。\n        </Alert>\n      </Box>\n\n      {/* 列范围设置 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <TableViewIcon sx={{ color: 'primary.main', mr: 1 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n              数据列范围\n            </Typography>\n            <Chip label=\"必填\" size=\"small\" color=\"error\" sx={{ ml: 2 }} />\n          </Box>\n\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"起始列\"\n                  value={startCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setStartCol(value);\n                    if (errors.startCol) {\n                      setErrors(prev => ({ ...prev, startCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: AT\"\n                  error={!!errors.startCol}\n                  helperText={errors.startCol || \"请输入起始列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"结束列\"\n                  value={endCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setEndCol(value);\n                    if (errors.endCol) {\n                      setErrors(prev => ({ ...prev, endCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: BP\"\n                  error={!!errors.endCol}\n                  helperText={errors.endCol || \"请输入结束列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n\n      {/* 佣金计算参数 */}\n      <Card sx={{ mb: 4 }}>\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <MonetizationOnIcon sx={{ color: 'secondary.main', mr: 1 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                佣金计算参数\n              </Typography>\n              <Chip label=\"可选\" size=\"small\" color=\"success\" sx={{ ml: 2 }} />\n            </Box>\n\n            <Grid container spacing={3}>\n              {/* 第一行：小时费率相关 */}\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  fullWidth\n                  label=\"小时费率\"\n                  type=\"number\"\n                  value={perHourRate}\n                  onChange={(e) => {\n                    setPerHourRate(e.target.value);\n                    if (errors.perHourRate) {\n                      setErrors(prev => ({ ...prev, perHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 65\"\n                  error={!!errors.perHourRate}\n                  helperText={errors.perHourRate || \"每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  fullWidth\n                  label=\"CBU CAR小时费率\"\n                  type=\"number\"\n                  value={cbuCarHourRate}\n                  onChange={(e) => {\n                    setCbuCarHourRate(e.target.value);\n                    if (errors.cbuCarHourRate) {\n                      setErrors(prev => ({ ...prev, cbuCarHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 80\"\n                  error={!!errors.cbuCarHourRate}\n                  helperText={errors.cbuCarHourRate || \"CBU CAR每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  fullWidth\n                  label=\"WTY小时费率\"\n                  type=\"number\"\n                  value={wtyHourRate}\n                  onChange={(e) => {\n                    setWtyHourRate(e.target.value);\n                    if (errors.wtyHourRate) {\n                      setErrors(prev => ({ ...prev, wtyHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 70\"\n                  error={!!errors.wtyHourRate}\n                  helperText={errors.wtyHourRate || \"WTY每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              {/* 第二行：佣金率相关 */}\n              <Grid item xs={12} sm={6}>\n                <Tooltip title=\"佣金比例，范围在0到1之间，例如0.6表示60%\">\n                  <TextField\n                    fullWidth\n                    label=\"佣金率\"\n                    type=\"number\"\n                    value={commissionRate}\n                    onChange={(e) => {\n                      setCommissionRate(e.target.value);\n                      if (errors.commissionRate) {\n                        setErrors(prev => ({ ...prev, commissionRate: '' }));\n                      }\n                    }}\n                    placeholder=\"例如: 0.6\"\n                    error={!!errors.commissionRate}\n                    helperText={errors.commissionRate || \"佣金比例 (0-1 之间)\"}\n                    inputProps={{ step: \"0.01\", min: \"0\", max: \"1\" }}\n                    sx={{\n                      '& .MuiOutlinedInput-root': {\n                        borderRadius: 2,\n                      },\n                    }}\n                  />\n                </Tooltip>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Tooltip title=\"WTY佣金比例，范围在0到1之间，例如0.5表示50%\">\n                  <TextField\n                    fullWidth\n                    label=\"WTY佣金率\"\n                    type=\"number\"\n                    value={wtyCommissionRate}\n                    onChange={(e) => {\n                      setWtyCommissionRate(e.target.value);\n                      if (errors.wtyCommissionRate) {\n                        setErrors(prev => ({ ...prev, wtyCommissionRate: '' }));\n                      }\n                    }}\n                    placeholder=\"例如: 0.5\"\n                    error={!!errors.wtyCommissionRate}\n                    helperText={errors.wtyCommissionRate || \"WTY佣金比例 (0-1 之间)\"}\n                    inputProps={{ step: \"0.01\", min: \"0\", max: \"1\" }}\n                    sx={{\n                      '& .MuiOutlinedInput-root': {\n                        borderRadius: 2,\n                      },\n                    }}\n                  />\n                </Tooltip>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n\n      {/* 操作按钮 */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>\n        <Button\n          variant=\"outlined\"\n          startIcon={<ArrowBackIcon />}\n          onClick={onBack}\n          disabled={loading}\n        >\n          返回上一步\n        </Button>\n\n        <Button\n          type=\"submit\"\n          variant=\"contained\"\n          endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SendIcon />}\n          disabled={loading}\n        >\n          {loading ? '处理中...' : '开始处理'}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default ProcessForm; "], "mappings": "6JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,SAAS,CACTC,MAAM,CACNC,IAAI,CACJC,cAAc,CACdC,gBAAgB,CAChBC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,KAAK,CACLC,OAAO,KACF,eAAe,CACtB,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,WAAW,CAEnC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,WAAW,CAAG,mBAAmB,CAEvC,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAsD,IAArD,CAAEC,MAAM,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,OAAQ,CAAC,CAAAL,IAAA,CAEnE;AACA,KAAM,CAACM,QAAQ,CAAEC,WAAW,CAAC,CAAGnC,QAAQ,CAAC,IAAM,CAC7C,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACF,QAAQ,EAAI,EAAE,CAAG,EAAE,CAClE,CAAC,CAAC,CAEF,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAG1C,QAAQ,CAAC,IAAM,CACzC,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACK,MAAM,EAAI,EAAE,CAAG,EAAE,CAChE,CAAC,CAAC,CAEF,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,IAAM,CACnD,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACO,WAAW,EAAI,EAAE,CAAG,EAAE,CACrE,CAAC,CAAC,CAEF,KAAM,CAACE,cAAc,CAAEC,iBAAiB,CAAC,CAAG9C,QAAQ,CAAC,IAAM,CACzD,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACS,cAAc,EAAI,EAAE,CAAG,EAAE,CACxE,CAAC,CAAC,CAEF,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGhD,QAAQ,CAAC,IAAM,CACnD,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACW,WAAW,EAAI,EAAE,CAAG,EAAE,CACrE,CAAC,CAAC,CAEF,KAAM,CAACE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlD,QAAQ,CAAC,IAAM,CAC/D,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACa,iBAAiB,EAAI,EAAE,CAAG,EAAE,CAC3E,CAAC,CAAC,CAEF,KAAM,CAACE,cAAc,CAAEC,iBAAiB,CAAC,CAAGpD,QAAQ,CAAC,IAAM,CACzD,KAAM,CAAAoC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC,CACrD,MAAO,CAAAU,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACe,cAAc,EAAI,EAAE,CAAG,EAAE,CACxE,CAAC,CAAC,CAEF,KAAM,CAACE,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuD,MAAM,CAAEC,SAAS,CAAC,CAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAExC;AACA,KAAM,CAAAyD,cAAc,CAAIC,KAAK,EAAK,CAChC,KAAM,CAAAC,QAAQ,CAAG,aAAa,CAC9B,GAAI,CAACD,KAAK,CAAE,MAAO,OAAO,CAC1B,GAAI,CAACC,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC,CAAE,MAAO,8BAA8B,CAChE,MAAO,EAAE,CACX,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGA,CAACH,KAAK,CAAEI,SAAS,GAAK,CACzC,GAAIJ,KAAK,GAAKK,KAAK,CAACL,KAAK,CAAC,EAAIM,UAAU,CAACN,KAAK,CAAC,CAAG,CAAC,CAAC,CAAE,CACpD,SAAAO,MAAA,CAAUH,SAAS,mCACrB,CACA,MAAO,EAAE,CACX,CAAC,CAED;AACA7D,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiE,YAAY,CAAG,CACnBhC,QAAQ,CACRO,MAAM,CACNE,WAAW,CACXE,cAAc,CACdE,WAAW,CACXE,iBAAiB,CACjBE,cACF,CAAC,CACDd,YAAY,CAAC8B,OAAO,CAACzC,WAAW,CAAEa,IAAI,CAAC6B,SAAS,CAACF,YAAY,CAAC,CAAC,CACjE,CAAC,CAAE,CAAChC,QAAQ,CAAEO,MAAM,CAAEE,WAAW,CAAEE,cAAc,CAAEE,WAAW,CAAEE,iBAAiB,CAAEE,cAAc,CAAC,CAAC,CAEnG,KAAM,CAAAkB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CAEpB,KAAM,CAAAC,aAAa,CAAGhB,cAAc,CAACvB,QAAQ,CAAC,CAC9C,KAAM,CAAAwC,WAAW,CAAGjB,cAAc,CAAChB,MAAM,CAAC,CAC1C,KAAM,CAAAkC,gBAAgB,CAAGd,YAAY,CAAClB,WAAW,CAAE,MAAM,CAAC,CAC1D,KAAM,CAAAiC,mBAAmB,CAAGf,YAAY,CAAChB,cAAc,CAAE,aAAa,CAAC,CACvE,KAAM,CAAAgC,gBAAgB,CAAGhB,YAAY,CAACd,WAAW,CAAE,SAAS,CAAC,CAC7D,KAAM,CAAA+B,sBAAsB,CAAGjB,YAAY,CAACZ,iBAAiB,CAAE,QAAQ,CAAC,CACxE,KAAM,CAAA8B,mBAAmB,CAAGlB,YAAY,CAACV,cAAc,CAAE,KAAK,CAAC,CAE/D,GAAIsB,aAAa,CAAED,SAAS,CAACtC,QAAQ,CAAGuC,aAAa,CACrD,GAAIC,WAAW,CAAEF,SAAS,CAAC/B,MAAM,CAAGiC,WAAW,CAC/C,GAAIC,gBAAgB,CAAEH,SAAS,CAAC7B,WAAW,CAAGgC,gBAAgB,CAC9D,GAAIC,mBAAmB,CAAEJ,SAAS,CAAC3B,cAAc,CAAG+B,mBAAmB,CACvE,GAAIC,gBAAgB,CAAEL,SAAS,CAACzB,WAAW,CAAG8B,gBAAgB,CAC9D,GAAIC,sBAAsB,CAAEN,SAAS,CAACvB,iBAAiB,CAAG6B,sBAAsB,CAChF,GAAIC,mBAAmB,CAAEP,SAAS,CAACrB,cAAc,CAAG4B,mBAAmB,CAEvE,GAAI5B,cAAc,EAAKa,UAAU,CAACb,cAAc,CAAC,CAAG,CAAE,CAAE,CACtDqB,SAAS,CAACrB,cAAc,CAAG,YAAY,CACzC,CAEA,GAAIF,iBAAiB,EAAKe,UAAU,CAACf,iBAAiB,CAAC,CAAG,CAAE,CAAE,CAC5DuB,SAAS,CAACvB,iBAAiB,CAAG,eAAe,CAC/C,CAEAO,SAAS,CAACgB,SAAS,CAAC,CAEpB,GAAIQ,MAAM,CAACC,IAAI,CAACT,SAAS,CAAC,CAACU,MAAM,CAAG,CAAC,CAAE,CACrCjD,OAAO,CAAC,UAAU,CAAC,CACnB,OACF,CAEAqB,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAA6B,WAAW,CAAG,CAClBtD,MAAM,CACNC,SAAS,CACTI,QAAQ,CACRO,MAAM,CACNE,WAAW,CAAEA,WAAW,EAAIyC,SAAS,CACrCvC,cAAc,CAAEA,cAAc,EAAIuC,SAAS,CAC3CrC,WAAW,CAAEA,WAAW,EAAIqC,SAAS,CACrCnC,iBAAiB,CAAEA,iBAAiB,EAAImC,SAAS,CACjDjC,cAAc,CAAEA,cAAc,EAAIiC,SACpC,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjE,KAAK,CAACkE,IAAI,IAAArB,MAAA,CAAI5C,OAAO,aAAY8D,WAAW,CAAC,CAEpE,GAAIE,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CzD,QAAQ,CACN,CAAEG,QAAQ,CAAEO,MAAM,CAAEE,WAAW,CAAEE,cAAc,CAAEE,WAAW,CAAEE,iBAAiB,CAAEE,cAAe,CAAC,CACjGkC,QAAQ,CAACE,IAAI,CAACA,IAChB,CAAC,CACH,CAAC,IAAM,CACLtD,OAAO,CAAC,YAAY,CAAC,CACvB,CACF,CAAE,MAAOwD,KAAK,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACdC,OAAO,CAACH,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BxD,OAAO,CAAC,EAAAyD,eAAA,CAAAD,KAAK,CAACJ,QAAQ,UAAAK,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBH,IAAI,UAAAI,oBAAA,iBAApBA,oBAAA,CAAsBF,KAAK,GAAI,YAAY,CAAC,CACtD,CAAC,OAAS,CACRnC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACE7B,KAAA,CAACvB,GAAG,EAAC2F,SAAS,CAAC,MAAM,CAAC9D,QAAQ,CAAEsC,YAAa,CAACyB,UAAU,MAAAC,QAAA,eAEtDtE,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACtCtE,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAQ,CAAEH,EAAE,CAAE,CAAC,CAAEI,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,eAC1FxE,IAAA,CAACP,YAAY,EAACgF,EAAE,CAAE,CAAEO,QAAQ,CAAE,EAAE,CAAEC,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cAC7DjF,IAAA,CAACpB,UAAU,EAACsG,OAAO,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,GAAG,CAAEF,KAAK,CAAE,cAAe,CAAE,CAAAT,QAAA,CAAC,0BAEzE,CAAY,CAAC,EACV,CAAC,cAENtE,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAC,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eAC1FxE,IAAA,CAACpB,UAAU,EAACsG,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEQ,KAAK,CAAE,gBAAiB,CAAE,CAAAT,QAAA,CAAC,iCAE7D,CAAY,CAAC,cACbxE,IAAA,CAACZ,IAAI,EAACgG,KAAK,CAAE7E,SAAU,CAAC8E,IAAI,CAAC,OAAO,CAACJ,KAAK,CAAC,SAAS,CAACC,OAAO,CAAC,UAAU,CAAE,CAAC,EACvE,CAAC,cAENlF,IAAA,CAACX,KAAK,EAACiG,QAAQ,CAAC,MAAM,CAACC,IAAI,cAAEvF,IAAA,CAACN,QAAQ,GAAE,CAAE,CAAC+E,EAAE,CAAE,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,0NAEtE,CAAO,CAAC,EACL,CAAC,cAGNxE,IAAA,CAACd,IAAI,EAACuF,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cAClBtE,KAAA,CAACf,WAAW,EAAAqF,QAAA,eACVtE,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEF,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDxE,IAAA,CAACJ,aAAa,EAAC6E,EAAE,CAAE,CAAEQ,KAAK,CAAE,cAAc,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACvDxF,IAAA,CAACpB,UAAU,EAACsG,OAAO,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,GAAI,CAAE,CAAAX,QAAA,CAAC,gCAElD,CAAY,CAAC,cACbxE,IAAA,CAACZ,IAAI,EAACgG,KAAK,CAAC,cAAI,CAACC,IAAI,CAAC,OAAO,CAACJ,KAAK,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAC1D,CAAC,cAEJvF,KAAA,CAACnB,IAAI,EAAC2G,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,eACzBxE,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACnB,SAAS,EACRkH,QAAQ,MACRC,SAAS,MACTZ,KAAK,CAAC,oBAAK,CACXjD,KAAK,CAAExB,QAAS,CAChBsF,QAAQ,CAAGlD,CAAC,EAAK,CACf,KAAM,CAAAZ,KAAK,CAAGY,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAACgE,WAAW,CAAC,CAAC,CAC1CvF,WAAW,CAACuB,KAAK,CAAC,CAClB,GAAIH,MAAM,CAACrB,QAAQ,CAAE,CACnBsB,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEzF,QAAQ,CAAE,EAAE,EAAG,CAAC,CAChD,CACF,CAAE,CACF2F,WAAW,CAAC,kBAAQ,CACpBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACrB,QAAS,CACzB4F,UAAU,CAAEvE,MAAM,CAACrB,QAAQ,EAAI,WAAY,CAC3C6F,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAE,CAAE,CAC7BhC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACE,CAAC,cAEP1G,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACnB,SAAS,EACRkH,QAAQ,MACRC,SAAS,MACTZ,KAAK,CAAC,oBAAK,CACXjD,KAAK,CAAEjB,MAAO,CACd+E,QAAQ,CAAGlD,CAAC,EAAK,CACf,KAAM,CAAAZ,KAAK,CAAGY,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAACgE,WAAW,CAAC,CAAC,CAC1ChF,SAAS,CAACgB,KAAK,CAAC,CAChB,GAAIH,MAAM,CAACd,MAAM,CAAE,CACjBe,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAElF,MAAM,CAAE,EAAE,EAAG,CAAC,CAC9C,CACF,CAAE,CACFoF,WAAW,CAAC,kBAAQ,CACpBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACd,MAAO,CACvBqF,UAAU,CAAEvE,MAAM,CAACd,MAAM,EAAI,WAAY,CACzCsF,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAE,CAAE,CAC7BhC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACE,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,cAGT1G,IAAA,CAACd,IAAI,EAACuF,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cAChBtE,KAAA,CAACf,WAAW,EAAAqF,QAAA,eACVtE,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEF,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDxE,IAAA,CAACL,kBAAkB,EAAC8E,EAAE,CAAE,CAAEQ,KAAK,CAAE,gBAAgB,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC9DxF,IAAA,CAACpB,UAAU,EAACsG,OAAO,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,GAAI,CAAE,CAAAX,QAAA,CAAC,sCAElD,CAAY,CAAC,cACbxE,IAAA,CAACZ,IAAI,EAACgG,KAAK,CAAC,cAAI,CAACC,IAAI,CAAC,OAAO,CAACJ,KAAK,CAAC,SAAS,CAACR,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAC5D,CAAC,cAENvF,KAAA,CAACnB,IAAI,EAAC2G,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,eAEzBxE,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACnB,SAAS,EACRmH,SAAS,MACTZ,KAAK,CAAC,0BAAM,CACZuB,IAAI,CAAC,QAAQ,CACbxE,KAAK,CAAEf,WAAY,CACnB6E,QAAQ,CAAGlD,CAAC,EAAK,CACf1B,cAAc,CAAC0B,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAAC,CAC9B,GAAIH,MAAM,CAACZ,WAAW,CAAE,CACtBa,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEhF,WAAW,CAAE,EAAE,EAAG,CAAC,CACnD,CACF,CAAE,CACFkF,WAAW,CAAC,kBAAQ,CACpBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACZ,WAAY,CAC5BmF,UAAU,CAAEvE,MAAM,CAACZ,WAAW,EAAI,YAAa,CAC/CwF,UAAU,CAAE,CACVC,cAAc,cAAE7G,IAAA,CAAChB,cAAc,EAAC8H,QAAQ,CAAC,OAAO,CAAAtC,QAAA,CAAC,IAAE,CAAgB,CACrE,CAAE,CACFC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACE,CAAC,cAEP1G,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACnB,SAAS,EACRmH,SAAS,MACTZ,KAAK,CAAC,iCAAa,CACnBuB,IAAI,CAAC,QAAQ,CACbxE,KAAK,CAAEb,cAAe,CACtB2E,QAAQ,CAAGlD,CAAC,EAAK,CACfxB,iBAAiB,CAACwB,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAAC,CACjC,GAAIH,MAAM,CAACV,cAAc,CAAE,CACzBW,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE9E,cAAc,CAAE,EAAE,EAAG,CAAC,CACtD,CACF,CAAE,CACFgF,WAAW,CAAC,kBAAQ,CACpBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACV,cAAe,CAC/BiF,UAAU,CAAEvE,MAAM,CAACV,cAAc,EAAI,mBAAoB,CACzDsF,UAAU,CAAE,CACVC,cAAc,cAAE7G,IAAA,CAAChB,cAAc,EAAC8H,QAAQ,CAAC,OAAO,CAAAtC,QAAA,CAAC,IAAE,CAAgB,CACrE,CAAE,CACFC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACE,CAAC,cAEP1G,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACnB,SAAS,EACRmH,SAAS,MACTZ,KAAK,CAAC,6BAAS,CACfuB,IAAI,CAAC,QAAQ,CACbxE,KAAK,CAAEX,WAAY,CACnByE,QAAQ,CAAGlD,CAAC,EAAK,CACftB,cAAc,CAACsB,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAAC,CAC9B,GAAIH,MAAM,CAACR,WAAW,CAAE,CACtBS,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE5E,WAAW,CAAE,EAAE,EAAG,CAAC,CACnD,CACF,CAAE,CACF8E,WAAW,CAAC,kBAAQ,CACpBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACR,WAAY,CAC5B+E,UAAU,CAAEvE,MAAM,CAACR,WAAW,EAAI,eAAgB,CAClDoF,UAAU,CAAE,CACVC,cAAc,cAAE7G,IAAA,CAAChB,cAAc,EAAC8H,QAAQ,CAAC,OAAO,CAAAtC,QAAA,CAAC,IAAE,CAAgB,CACrE,CAAE,CACFC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACE,CAAC,cAGP1G,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACV,OAAO,EAACyH,KAAK,CAAC,0GAA0B,CAAAvC,QAAA,cACvCxE,IAAA,CAACnB,SAAS,EACRmH,SAAS,MACTZ,KAAK,CAAC,oBAAK,CACXuB,IAAI,CAAC,QAAQ,CACbxE,KAAK,CAAEP,cAAe,CACtBqE,QAAQ,CAAGlD,CAAC,EAAK,CACflB,iBAAiB,CAACkB,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAAC,CACjC,GAAIH,MAAM,CAACJ,cAAc,CAAE,CACzBK,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExE,cAAc,CAAE,EAAE,EAAG,CAAC,CACtD,CACF,CAAE,CACF0E,WAAW,CAAC,mBAAS,CACrBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACJ,cAAe,CAC/B2E,UAAU,CAAEvE,MAAM,CAACJ,cAAc,EAAI,eAAgB,CACrD4E,UAAU,CAAE,CAAEQ,IAAI,CAAE,MAAM,CAAEC,GAAG,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAI,CAAE,CACjDzC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACK,CAAC,CACN,CAAC,cAEP1G,IAAA,CAACjB,IAAI,EAAC6G,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACvBxE,IAAA,CAACV,OAAO,EAACyH,KAAK,CAAC,6GAA6B,CAAAvC,QAAA,cAC1CxE,IAAA,CAACnB,SAAS,EACRmH,SAAS,MACTZ,KAAK,CAAC,uBAAQ,CACduB,IAAI,CAAC,QAAQ,CACbxE,KAAK,CAAET,iBAAkB,CACzBuE,QAAQ,CAAGlD,CAAC,EAAK,CACfpB,oBAAoB,CAACoB,CAAC,CAACmD,MAAM,CAAC/D,KAAK,CAAC,CACpC,GAAIH,MAAM,CAACN,iBAAiB,CAAE,CAC5BO,SAAS,CAACmE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE1E,iBAAiB,CAAE,EAAE,EAAG,CAAC,CACzD,CACF,CAAE,CACF4E,WAAW,CAAC,mBAAS,CACrBpC,KAAK,CAAE,CAAC,CAAClC,MAAM,CAACN,iBAAkB,CAClC6E,UAAU,CAAEvE,MAAM,CAACN,iBAAiB,EAAI,kBAAmB,CAC3D8E,UAAU,CAAE,CAAEQ,IAAI,CAAE,MAAM,CAAEC,GAAG,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAI,CAAE,CACjDzC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BiC,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,CACK,CAAC,CACN,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,cAGTxG,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,eAAe,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,eACpExE,IAAA,CAAClB,MAAM,EACLoG,OAAO,CAAC,UAAU,CAClBiC,SAAS,cAAEnH,IAAA,CAACT,aAAa,GAAE,CAAE,CAC7B6H,OAAO,CAAE3G,MAAO,CAChB4G,QAAQ,CAAEvF,OAAQ,CAAA0C,QAAA,CACnB,gCAED,CAAQ,CAAC,cAETxE,IAAA,CAAClB,MAAM,EACL6H,IAAI,CAAC,QAAQ,CACbzB,OAAO,CAAC,WAAW,CACnBoC,OAAO,CAAExF,OAAO,cAAG9B,IAAA,CAACf,gBAAgB,EAACoG,IAAI,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,cAAGjF,IAAA,CAACR,QAAQ,GAAE,CAAE,CACjF6H,QAAQ,CAAEvF,OAAQ,CAAA0C,QAAA,CAEjB1C,OAAO,CAAG,QAAQ,CAAG,MAAM,CACtB,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}