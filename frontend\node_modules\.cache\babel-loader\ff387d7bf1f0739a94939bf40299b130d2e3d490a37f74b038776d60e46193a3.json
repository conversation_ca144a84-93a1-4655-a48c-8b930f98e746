{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled } from '@mui/system';\nimport { isOverflown } from '../../utils/domUtils';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaderTitle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeaderTitleRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderTitle',\n  overridesResolver: (props, styles) => styles.columnHeaderTitle\n})({\n  textOverflow: 'ellipsis',\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  fontWeight: 'var(--unstable_DataGrid-headWeight)'\n});\nconst ColumnHeaderInnerTitle = /*#__PURE__*/React.forwardRef(function ColumnHeaderInnerTitle(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeaderTitleRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n});\n// No React.memo here as if we display the sort icon, we need to recalculate the isOver\nfunction GridColumnHeaderTitle(props) {\n  var _rootProps$slotProps;\n  const {\n    label,\n    description\n  } = props;\n  const rootProps = useGridRootProps();\n  const titleRef = React.useRef(null);\n  const [tooltip, setTooltip] = React.useState('');\n  const handleMouseOver = React.useCallback(() => {\n    if (!description && titleRef != null && titleRef.current) {\n      const isOver = isOverflown(titleRef.current);\n      if (isOver) {\n        setTooltip(label);\n      } else {\n        setTooltip('');\n      }\n    }\n  }, [description, label]);\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: description || tooltip\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseTooltip, {\n    children: /*#__PURE__*/_jsx(ColumnHeaderInnerTitle, {\n      onMouseOver: handleMouseOver,\n      ref: titleRef,\n      children: label\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderTitle.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnWidth: PropTypes.number.isRequired,\n  description: PropTypes.node,\n  label: PropTypes.string.isRequired\n} : void 0;\nexport { GridColumnHeaderTitle };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "styled", "isOverflown", "getDataGridUtilityClass", "useGridRootProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "GridColumnHeaderTitleRoot", "name", "slot", "overridesResolver", "props", "styles", "columnHeaderTitle", "textOverflow", "overflow", "whiteSpace", "fontWeight", "ColumnHeaderInnerTitle", "forwardRef", "ref", "className", "other", "rootProps", "GridColumnHeaderTitle", "_rootProps$slotProps", "label", "description", "titleRef", "useRef", "tooltip", "setTooltip", "useState", "handleMouseOver", "useCallback", "current", "isOver", "baseTooltip", "title", "slotProps", "children", "onMouseOver", "process", "env", "NODE_ENV", "propTypes", "columnWidth", "number", "isRequired", "node", "string"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/columnHeaders/GridColumnHeaderTitle.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled } from '@mui/system';\nimport { isOverflown } from '../../utils/domUtils';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaderTitle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeaderTitleRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderTitle',\n  overridesResolver: (props, styles) => styles.columnHeaderTitle\n})({\n  textOverflow: 'ellipsis',\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  fontWeight: 'var(--unstable_DataGrid-headWeight)'\n});\nconst ColumnHeaderInnerTitle = /*#__PURE__*/React.forwardRef(function ColumnHeaderInnerTitle(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeaderTitleRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n});\n// No React.memo here as if we display the sort icon, we need to recalculate the isOver\nfunction GridColumnHeaderTitle(props) {\n  var _rootProps$slotProps;\n  const {\n    label,\n    description\n  } = props;\n  const rootProps = useGridRootProps();\n  const titleRef = React.useRef(null);\n  const [tooltip, setTooltip] = React.useState('');\n  const handleMouseOver = React.useCallback(() => {\n    if (!description && titleRef != null && titleRef.current) {\n      const isOver = isOverflown(titleRef.current);\n      if (isOver) {\n        setTooltip(label);\n      } else {\n        setTooltip('');\n      }\n    }\n  }, [description, label]);\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: description || tooltip\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseTooltip, {\n    children: /*#__PURE__*/_jsx(ColumnHeaderInnerTitle, {\n      onMouseOver: handleMouseOver,\n      ref: titleRef,\n      children: label\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderTitle.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnWidth: PropTypes.number.isRequired,\n  description: PropTypes.node,\n  label: PropTypes.string.isRequired\n} : void 0;\nexport { GridColumnHeaderTitle };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACtE,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,mBAAmB;EAC5B,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAEP,uBAAuB,EAAEM,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,yBAAyB,GAAGX,MAAM,CAAC,KAAK,EAAE;EAC9CY,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,mBAAmB;EACzBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,YAAY,EAAE,UAAU;EACxBC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,SAASD,sBAAsBA,CAACP,KAAK,EAAES,GAAG,EAAE;EACvG,MAAM;MACFC;IACF,CAAC,GAAGV,KAAK;IACTW,KAAK,GAAGjC,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMiC,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAMK,OAAO,GAAGF,iBAAiB,CAACqB,SAAS,CAAC;EAC5C,OAAO,aAAatB,IAAI,CAACM,yBAAyB,EAAEnB,QAAQ,CAAC;IAC3DgC,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAE5B,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEe,SAAS,CAAC;IACxClB,UAAU,EAAEoB;EACd,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF;AACA,SAASE,qBAAqBA,CAACb,KAAK,EAAE;EACpC,IAAIc,oBAAoB;EACxB,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGhB,KAAK;EACT,MAAMY,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAM6B,QAAQ,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,KAAK,CAACyC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMC,eAAe,GAAG1C,KAAK,CAAC2C,WAAW,CAAC,MAAM;IAC9C,IAAI,CAACP,WAAW,IAAIC,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACO,OAAO,EAAE;MACxD,MAAMC,MAAM,GAAGvC,WAAW,CAAC+B,QAAQ,CAACO,OAAO,CAAC;MAC5C,IAAIC,MAAM,EAAE;QACVL,UAAU,CAACL,KAAK,CAAC;MACnB,CAAC,MAAM;QACLK,UAAU,CAAC,EAAE,CAAC;MAChB;IACF;EACF,CAAC,EAAE,CAACJ,WAAW,EAAED,KAAK,CAAC,CAAC;EACxB,OAAO,aAAazB,IAAI,CAACsB,SAAS,CAAClB,KAAK,CAACgC,WAAW,EAAEjD,QAAQ,CAAC;IAC7DkD,KAAK,EAAEX,WAAW,IAAIG;EACxB,CAAC,EAAE,CAACL,oBAAoB,GAAGF,SAAS,CAACgB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,oBAAoB,CAACY,WAAW,EAAE;IACnGG,QAAQ,EAAE,aAAavC,IAAI,CAACiB,sBAAsB,EAAE;MAClDuB,WAAW,EAAER,eAAe;MAC5Bb,GAAG,EAAEQ,QAAQ;MACbY,QAAQ,EAAEd;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,qBAAqB,CAACqB,SAAS,GAAG;EACxE;EACA;EACA;EACA;EACAC,WAAW,EAAEtD,SAAS,CAACuD,MAAM,CAACC,UAAU;EACxCrB,WAAW,EAAEnC,SAAS,CAACyD,IAAI;EAC3BvB,KAAK,EAAElC,SAAS,CAAC0D,MAAM,CAACF;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASxB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}