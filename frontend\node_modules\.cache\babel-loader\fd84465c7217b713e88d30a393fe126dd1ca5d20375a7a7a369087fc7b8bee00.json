{"ast": null, "code": "import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\n/**\n * @category ColumnGrouping\n * @ignore - do not document.\n */\nexport const gridColumnGroupingSelector = state => state.columnGrouping;\nexport const gridColumnGroupsUnwrappedModelSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$unwra;\n  return (_columnGrouping$unwra = columnGrouping == null ? void 0 : columnGrouping.unwrappedGroupingModel) != null ? _columnGrouping$unwra : {};\n});\nexport const gridColumnGroupsLookupSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$looku;\n  return (_columnGrouping$looku = columnGrouping == null ? void 0 : columnGrouping.lookup) != null ? _columnGrouping$looku : {};\n});\nexport const gridColumnGroupsHeaderStructureSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$heade;\n  return (_columnGrouping$heade = columnGrouping == null ? void 0 : columnGrouping.headerStructure) != null ? _columnGrouping$heade : [];\n});\nexport const gridColumnGroupsHeaderMaxDepthSelector = createSelector(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$maxDe;\n  return (_columnGrouping$maxDe = columnGrouping == null ? void 0 : columnGrouping.maxDepth) != null ? _columnGrouping$maxDe : 0;\n});", "map": {"version": 3, "names": ["createSelector", "createSelectorMemoized", "gridColumnGroupingSelector", "state", "columnGrouping", "gridColumnGroupsUnwrappedModelSelector", "_columnGrouping$unwra", "unwrappedGroupingModel", "gridColumnGroupsLookupSelector", "_columnGrouping$looku", "lookup", "gridColumnGroupsHeaderStructureSelector", "_columnGrouping$heade", "headerStructure", "gridColumnGroupsHeaderMaxDepthSelector", "_columnGrouping$maxDe", "max<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columnGrouping/gridColumnGroupsSelector.js"], "sourcesContent": ["import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\n/**\n * @category ColumnGrouping\n * @ignore - do not document.\n */\nexport const gridColumnGroupingSelector = state => state.columnGrouping;\nexport const gridColumnGroupsUnwrappedModelSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$unwra;\n  return (_columnGrouping$unwra = columnGrouping == null ? void 0 : columnGrouping.unwrappedGroupingModel) != null ? _columnGrouping$unwra : {};\n});\nexport const gridColumnGroupsLookupSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$looku;\n  return (_columnGrouping$looku = columnGrouping == null ? void 0 : columnGrouping.lookup) != null ? _columnGrouping$looku : {};\n});\nexport const gridColumnGroupsHeaderStructureSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$heade;\n  return (_columnGrouping$heade = columnGrouping == null ? void 0 : columnGrouping.headerStructure) != null ? _columnGrouping$heade : [];\n});\nexport const gridColumnGroupsHeaderMaxDepthSelector = createSelector(gridColumnGroupingSelector, columnGrouping => {\n  var _columnGrouping$maxDe;\n  return (_columnGrouping$maxDe = columnGrouping == null ? void 0 : columnGrouping.maxDepth) != null ? _columnGrouping$maxDe : 0;\n});"], "mappings": "AAAA,SAASA,cAAc,EAAEC,sBAAsB,QAAQ,+BAA+B;AACtF;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAGC,KAAK,IAAIA,KAAK,CAACC,cAAc;AACvE,OAAO,MAAMC,sCAAsC,GAAGJ,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAI;EACzH,IAAIE,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGF,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACG,sBAAsB,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC,CAAC;AAC/I,CAAC,CAAC;AACF,OAAO,MAAME,8BAA8B,GAAGP,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAI;EACjH,IAAIK,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGL,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACM,MAAM,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC,CAAC;AAC/H,CAAC,CAAC;AACF,OAAO,MAAME,uCAAuC,GAAGV,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAI;EAC1H,IAAIQ,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACS,eAAe,KAAK,IAAI,GAAGD,qBAAqB,GAAG,EAAE;AACxI,CAAC,CAAC;AACF,OAAO,MAAME,sCAAsC,GAAGd,cAAc,CAACE,0BAA0B,EAAEE,cAAc,IAAI;EACjH,IAAIW,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGX,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACY,QAAQ,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC;AAChI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}