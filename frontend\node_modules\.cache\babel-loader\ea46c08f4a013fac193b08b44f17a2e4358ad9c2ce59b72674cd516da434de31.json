{"ast": null, "code": "import * as React from 'react';\nimport { useFirstRender } from '../../utils/useFirstRender';\nexport const useGridRegisterStrategyProcessor = (apiRef, strategyName, group, processor) => {\n  const registerPreProcessor = React.useCallback(() => {\n    apiRef.current.registerStrategyProcessor(strategyName, group, processor);\n  }, [apiRef, processor, group, strategyName]);\n  useFirstRender(() => {\n    registerPreProcessor();\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      registerPreProcessor();\n    }\n  }, [registerPreProcessor]);\n};", "map": {"version": 3, "names": ["React", "useFirstRender", "useGridRegisterStrategyProcessor", "apiRef", "strategyName", "group", "processor", "registerPreProcessor", "useCallback", "current", "registerStrategyProcessor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useRef", "useEffect"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/core/strategyProcessing/useGridRegisterStrategyProcessor.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFirstRender } from '../../utils/useFirstRender';\nexport const useGridRegisterStrategyProcessor = (apiRef, strategyName, group, processor) => {\n  const registerPreProcessor = React.useCallback(() => {\n    apiRef.current.registerStrategyProcessor(strategyName, group, processor);\n  }, [apiRef, processor, group, strategyName]);\n  useFirstRender(() => {\n    registerPreProcessor();\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      registerPreProcessor();\n    }\n  }, [registerPreProcessor]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,MAAMC,gCAAgC,GAAGA,CAACC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,KAAK;EAC1F,MAAMC,oBAAoB,GAAGP,KAAK,CAACQ,WAAW,CAAC,MAAM;IACnDL,MAAM,CAACM,OAAO,CAACC,yBAAyB,CAACN,YAAY,EAAEC,KAAK,EAAEC,SAAS,CAAC;EAC1E,CAAC,EAAE,CAACH,MAAM,EAAEG,SAAS,EAAED,KAAK,EAAED,YAAY,CAAC,CAAC;EAC5CH,cAAc,CAAC,MAAM;IACnBM,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMI,aAAa,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EACxCZ,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAIF,aAAa,CAACF,OAAO,EAAE;MACzBE,aAAa,CAACF,OAAO,GAAG,KAAK;IAC/B,CAAC,MAAM;MACLF,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}