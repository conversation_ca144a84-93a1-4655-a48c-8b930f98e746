{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton } from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 搜索和排序状态\n  const [globalFilter, setGlobalFilter] = useState('');\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onCellEditComplete = e => {\n    let {\n      rowData,\n      newValue,\n      field\n    } = e;\n\n    // 阻止总计行被编辑\n    if (rowData.NO === 'TOTAL' || rowData._removed) {\n      return;\n    }\n\n    // 处理数值字段\n    if (field === 'COMMISSION' || field === 'MAXCHECK' || field === 'KM' || field === 'RO NO') {\n      if (typeof newValue === 'string') {\n        newValue = Number(newValue) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowData.id) {\n          return {\n            ...row,\n            [field]: newValue\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n      return updatedData;\n    });\n  };\n  const allowEdit = (rowData, field) => {\n    // NO列不允许编辑，因为它是自动生成的序号\n    if (field === 'NO') {\n      return false;\n    }\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"text\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this);\n  };\n  const numberEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"number\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      // 首先标记行为已删除\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return {\n            ...row,\n            NO: newNumber++\n          };\n        }\n        return row;\n      });\n      return updatedData;\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      // 首先恢复行\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return {\n            ...row,\n            NO: newNumber++\n          };\n        }\n        return row;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"\\u641C\\u7D22\\u8868\\u683C\\u6570\\u636E\",\n        variant: \"outlined\",\n        size: \"small\",\n        value: globalFilter,\n        onChange: e => setGlobalFilter(e.target.value),\n        placeholder: \"\\u8F93\\u5165\\u5173\\u952E\\u8BCD\\u641C\\u7D22...\",\n        sx: {\n          width: '300px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          value: memoGridData,\n          editMode: \"cell\",\n          dataKey: \"id\",\n          globalFilter: globalFilter,\n          sortMode: \"multiple\",\n          removableSort: true,\n          rowClassName: rowData => {\n            if (rowData.NO === 'TOTAL') return 'total-row';\n            if (rowData._removed) return 'removed-row';\n            return '';\n          },\n          className: \"custom-datatable\",\n          paginator: true,\n          rows: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          paginatorTemplate: \"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\",\n          currentPageReportTemplate: \"\\u663E\\u793A {first} \\u5230 {last} \\u6761\\uFF0C\\u5171 {totalRecords} \\u6761\\u8BB0\\u5F55\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"NO\",\n            header: \"NO\",\n            sortable: true,\n            body: rowData => {\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: Math.floor(rowData.NO)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 26\n                }, this);\n              }\n              return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n            },\n            style: {\n              width: '80px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"DATE\",\n            header: \"DATE\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'DATE') ? textEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: dateValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 26\n                }, this);\n              }\n              return dateValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"VEHICLE NO\",\n            header: \"VEHICLE NO\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'VEHICLE NO') ? textEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: rowData['VEHICLE NO']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 26\n                }, this);\n              }\n              return rowData['VEHICLE NO'];\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"RO NO\",\n            header: \"RO NO\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'RO NO') ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: roValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 26\n                }, this);\n              }\n              return roValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"KM\",\n            header: \"KM\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'KM') ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: kmValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 26\n                }, this);\n              }\n              return kmValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"REMARKS\",\n            header: \"REMARKS\",\n            sortable: true,\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                const removedRemarkText = rowData._selected_remarks || '无备注';\n                return /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  disabled: true,\n                  sx: {\n                    maxWidth: '100%',\n                    opacity: 0.6,\n                    textTransform: 'none',\n                    fontSize: '12px'\n                  },\n                  children: removedRemarkText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 21\n                }, this);\n              }\n              let remarkText = '点击选择';\n              let isSelected = false;\n              if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                remarkText = rowData._selected_remarks;\n                isSelected = true;\n              }\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: isSelected ? \"contained\" : \"outlined\",\n                size: \"small\",\n                onClick: () => handleRemarksClick(rowData.id, remarkText),\n                sx: {\n                  maxWidth: '100%',\n                  textTransform: 'none',\n                  fontSize: '12px'\n                },\n                children: remarkText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '200px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"MAXCHECK\",\n            header: \"HOURS\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'MAXCHECK') ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              const hoursValue = typeof rowData.MAXCHECK === 'number' ? rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: hoursValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 26\n                }, this);\n              }\n              return hoursValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"COMMISSION\",\n            header: \"AMOUNT\",\n            sortable: true,\n            editor: options => allowEdit(options.rowData, 'COMMISSION') ? numberEditor(options) : null,\n            onCellEditComplete: onCellEditComplete,\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') {\n                const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                return /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  children: totalValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 21\n                }, this);\n              }\n              const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: amountValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 26\n                }, this);\n              }\n              return amountValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            header: \"ACTION\",\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  color: \"success\",\n                  size: \"small\",\n                  startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => handleUndoRow(rowData.id),\n                  sx: {\n                    fontSize: '12px',\n                    textTransform: 'none'\n                  },\n                  children: \"\\u6062\\u590D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"error\",\n                size: \"small\",\n                startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 32\n                }, this),\n                onClick: () => handleRemoveRow(rowData.id),\n                sx: {\n                  fontSize: '12px',\n                  textTransform: 'none'\n                },\n                children: \"\\u79FB\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 965,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 964,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 970,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 572,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"MSErpbkpyOGMycOhk/w2trt/KP4=\", false, function () {\n  return [useSnackbar];\n});\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "DataTable", "Column", "InputText", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "useSnackbar", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "globalFilter", "setGlobalFilter", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "Date", "now", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "rowId", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "variant", "key", "sx", "borderRadius", "border", "borderColor", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "onCellEditComplete", "e", "rowData", "newValue", "field", "Number", "prev", "updatedData", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "allowEdit", "textEditor", "options", "type", "value", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "target", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "numberEditor", "recalculateTotal", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "newNumber", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "memoGridData", "textAlign", "py", "children", "color", "onClick", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "label", "size", "placeholder", "overflow", "height", "editMode", "dataKey", "sortMode", "removableSort", "rowClassName", "className", "paginator", "rows", "rowsPerPageOptions", "paginatorTemplate", "currentPageReportTemplate", "header", "sortable", "textDecoration", "editor", "dateValue", "roValue", "kmValue", "removedRemarkText", "max<PERSON><PERSON><PERSON>", "opacity", "textTransform", "fontSize", "remarkText", "isSelected", "hoursValue", "totalValue", "isNaN", "fontWeight", "amountValue", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton\n} from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\n\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n\n\n\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 搜索和排序状态\n  const [globalFilter, setGlobalFilter] = useState('');\n\n\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onCellEditComplete = (e) => {\n    let { rowData, newValue, field } = e;\n\n    // 阻止总计行被编辑\n    if (rowData.NO === 'TOTAL' || rowData._removed) {\n      return;\n    }\n\n    // 处理数值字段\n    if (field === 'COMMISSION' || field === 'MAXCHECK' || field === 'KM' || field === 'RO NO') {\n      if (typeof newValue === 'string') {\n        newValue = Number(newValue) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowData.id) {\n          return { ...row, [field]: newValue };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData\n          .filter(row => row.NO !== 'TOTAL' && !row._removed)\n          .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n\n      return updatedData;\n    });\n  };\n\n  const allowEdit = (rowData, field) => {\n    // NO列不允许编辑，因为它是自动生成的序号\n    if (field === 'NO') {\n      return false;\n    }\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = (options) => {\n    return (\n      <InputText\n        type=\"text\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n  const numberEditor = (options) => {\n    return (\n      <InputText\n        type=\"number\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      // 首先标记行为已删除\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return { ...row, NO: newNumber++ };\n        }\n        return row;\n      });\n\n      return updatedData;\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      // 首先恢复行\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return { ...row, NO: newNumber++ };\n        }\n        return row;\n      });\n\n      return updatedData;\n    });\n\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n\n  \n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n\n        <Box>\n          <Button\n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n\n      {/* 搜索框 */}\n      <Box sx={{ mb: 2 }}>\n        <TextField\n          label=\"搜索表格数据\"\n          variant=\"outlined\"\n          size=\"small\"\n          value={globalFilter}\n          onChange={(e) => setGlobalFilter(e.target.value)}\n          placeholder=\"输入关键词搜索...\"\n          sx={{ width: '300px' }}\n        />\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <style>\n            {`\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `}\n          </style>\n          <DataTable\n            value={memoGridData}\n            editMode=\"cell\"\n            dataKey=\"id\"\n            globalFilter={globalFilter}\n            sortMode=\"multiple\"\n            removableSort\n            rowClassName={(rowData) => {\n              if (rowData.NO === 'TOTAL') return 'total-row';\n              if (rowData._removed) return 'removed-row';\n              return '';\n            }}\n            className=\"custom-datatable\"\n            paginator\n            rows={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            paginatorTemplate=\"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\"\n            currentPageReportTemplate=\"显示 {first} 到 {last} 条，共 {totalRecords} 条记录\"\n          >\n            {/* NO Column - 不支持编辑 */}\n            <Column\n              field=\"NO\"\n              header=\"NO\"\n              sortable\n              body={(rowData) => {\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{Math.floor(rowData.NO)}</span>;\n                }\n                return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n              }}\n              style={{ width: '80px' }}\n            />\n\n            {/* DATE Column */}\n            <Column\n              field=\"DATE\"\n              header=\"DATE\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'DATE') ? textEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{dateValue}</span>;\n                }\n                return dateValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* VEHICLE NO Column */}\n            <Column\n              field=\"VEHICLE NO\"\n              header=\"VEHICLE NO\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'VEHICLE NO') ? textEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{rowData['VEHICLE NO']}</span>;\n                }\n                return rowData['VEHICLE NO'];\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* RO NO Column */}\n            <Column\n              field=\"RO NO\"\n              header=\"RO NO\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'RO NO') ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{roValue}</span>;\n                }\n                return roValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* KM Column */}\n            <Column\n              field=\"KM\"\n              header=\"KM\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'KM') ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{kmValue}</span>;\n                }\n                return kmValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* REMARKS Column */}\n            <Column\n              field=\"REMARKS\"\n              header=\"REMARKS\"\n              sortable\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  const removedRemarkText = rowData._selected_remarks || '无备注';\n                  return (\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      disabled\n                      sx={{\n                        maxWidth: '100%',\n                        opacity: 0.6,\n                        textTransform: 'none',\n                        fontSize: '12px'\n                      }}\n                    >\n                      {removedRemarkText}\n                    </Button>\n                  );\n                }\n\n                let remarkText = '点击选择';\n                let isSelected = false;\n\n                if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                  remarkText = rowData._selected_remarks;\n                  isSelected = true;\n                }\n\n                return (\n                  <Button\n                    variant={isSelected ? \"contained\" : \"outlined\"}\n                    size=\"small\"\n                    onClick={() => handleRemarksClick(rowData.id, remarkText)}\n                    sx={{\n                      maxWidth: '100%',\n                      textTransform: 'none',\n                      fontSize: '12px'\n                    }}\n                  >\n                    {remarkText}\n                  </Button>\n                );\n              }}\n              style={{ width: '200px' }}\n            />\n\n            {/* MAXCHECK (HOURS) Column */}\n            <Column\n              field=\"MAXCHECK\"\n              header=\"HOURS\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'MAXCHECK') ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                const hoursValue = typeof rowData.MAXCHECK === 'number' ?\n                  (rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1)) :\n                  rowData.MAXCHECK;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{hoursValue}</span>;\n                }\n                return hoursValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* COMMISSION (AMOUNT) Column */}\n            <Column\n              field=\"COMMISSION\"\n              header=\"AMOUNT\"\n              sortable\n              editor={(options) => allowEdit(options.rowData, 'COMMISSION') ? numberEditor(options) : null}\n              onCellEditComplete={onCellEditComplete}\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') {\n                  const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                    typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                  return (\n                    <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n                      {totalValue}\n                    </Typography>\n                  );\n                }\n\n                const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                  typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{amountValue}</span>;\n                }\n                return amountValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* ACTION Column */}\n            <Column\n              header=\"ACTION\"\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  return (\n                    <Button\n                      variant=\"contained\"\n                      color=\"success\"\n                      size=\"small\"\n                      startIcon={<UndoIcon />}\n                      onClick={() => handleUndoRow(rowData.id)}\n                      sx={{\n                        fontSize: '12px',\n                        textTransform: 'none'\n                      }}\n                    >\n                      恢复\n                    </Button>\n                  );\n                }\n                return (\n                  <Button\n                    variant=\"contained\"\n                    color=\"error\"\n                    size=\"small\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleRemoveRow(rowData.id)}\n                    sx={{\n                      fontSize: '12px',\n                      textTransform: 'none'\n                    }}\n                  >\n                    移除\n                  </Button>\n                );\n              }}\n              style={{ width: '120px' }}\n            />\n          </DataTable>\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,QACL,eAAe;AACtB,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAEhD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AACA,OAAO,yDAAyD;AAChE,OAAO,yCAAyC;AAChD,OAAO,2BAA2B;;AAElC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;AAMD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAEzF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,MAAM;IACzD,MAAM4C,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGX,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;;EAIpD;EACAC,SAAS,CAAC,MAAM;IACd4C,YAAY,CAACc,OAAO,CAAC,gBAAgB,EAAEZ,IAAI,CAACa,SAAS,CAAClB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmB,aAAa,GAAG1B,IAAI,CAAC2B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,MAAM;IAC7CqE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C/B,aAAa,GAAG,IAAIA,aAAa,CAACgC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIhC,aAAa,IAAIA,aAAa,CAACgC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGjC,aAAa,CAACuB,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHtB,eAAe,CAAC,CAAC,GAAGkB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHtB,eAAe,CAAC,CAAC,GAAGkB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG5E,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM6E,iBAAiB,GAAG7E,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM8E,gBAAgB,GAAG9E,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM+E,mBAAmB,GAAG/E,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMgF,UAAU,GAAIhD,IAAI,IAAKA,IAAI,CAAC2B,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAnF,SAAS,CAAC,MAAM;IACd,IAAIuC,YAAY,IAAI2B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,OAAO,GAAGtC,IAAI,CAACa,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMmB,WAAW,GAAGP,mBAAmB,CAACQ,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIJ,gBAAgB,CAACM,OAAO,EAAE;UAC5BC,YAAY,CAACP,gBAAgB,CAACM,OAAO,CAAC;QACxC;QACAN,gBAAgB,CAACM,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CV,mBAAmB,CAACQ,OAAO,GAAGF,OAAO;UACrCL,iBAAiB,CAACO,OAAO,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC;UACtCnD,YAAY,CAAC,CAAC,GAAG2B,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACM,OAAO,EAAE;QAC5BC,YAAY,CAACP,gBAAgB,CAACM,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACpB,QAAQ,EAAE3B,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAEoD;EAAgB,CAAC,GAAG9D,WAAW,CAAC,CAAC;EAEzC,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9F,QAAQ,CAAC;IACjD+F,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAhG,SAAS,CAAC,MAAM;IACd,IAAIsD,YAAY,CAACgB,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDf,eAAe,CAAC,CAAC,GAAGW,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEZ,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAM2C,YAAY,GAAGhG,WAAW,CAAC,CAACiG,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAIjB,mBAAmB,CAACK,OAAO,EAAE,EAAE;IAE5EK,eAAe,CAACO,OAAO,EAAE;MACvBG,OAAO,EAAEF,QAAQ;MACjB;MACAG,GAAG,EAAEF,SAAS;MACdG,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTP,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMgB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,WAAW,GAAG,GAAGjF,OAAO,aAAaQ,MAAM,EAAE;MACnD,MAAM0E,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIxB,IAAI,CAAC,CAAC,CAACyB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BZ,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdnD,OAAO,CAACmD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlF,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMmF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM9F,KAAK,CAAC+F,MAAM,CAAC,GAAG9F,OAAO,YAAYQ,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOoF,KAAK,EAAE;MACdnD,OAAO,CAACmD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAnF,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMsF,kBAAkB,GAAIC,CAAC,IAAK;IAChC,IAAI;MAAEC,OAAO;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAGH,CAAC;;IAEpC;IACA,IAAIC,OAAO,CAAC/C,EAAE,KAAK,OAAO,IAAI+C,OAAO,CAAC3D,QAAQ,EAAE;MAC9C;IACF;;IAEA;IACA,IAAI6D,KAAK,KAAK,YAAY,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,OAAO,EAAE;MACzF,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAGE,MAAM,CAACF,QAAQ,CAAC,IAAI,CAAC;MAClC;IACF;;IAEA;IACA1D,WAAW,CAAC6D,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAGD,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACa,EAAE,KAAKiD,OAAO,CAACjD,EAAE,EAAE;UACzB,OAAO;YAAE,GAAGb,GAAG;YAAE,CAACgE,KAAK,GAAGD;UAAS,CAAC;QACtC;QACA,OAAO/D,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMoE,QAAQ,GAAGD,WAAW,CAACE,IAAI,CAACrE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAIqD,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGH,WAAW,CACzBI,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDqE,MAAM,CAAC,CAACC,GAAG,EAAEzE,GAAG,KAAKyE,GAAG,IAAIR,MAAM,CAACjE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/D+C,QAAQ,CAAC/C,UAAU,GAAGiD,QAAQ;MAChC;MAEA,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,SAAS,GAAGA,CAACZ,OAAO,EAAEE,KAAK,KAAK;IACpC;IACA,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAOF,OAAO,CAAC/C,EAAE,KAAK,OAAO,IAAI,CAAC+C,OAAO,CAAC3D,QAAQ;EACpD,CAAC;;EAED;EACA,MAAMwE,UAAU,GAAIC,OAAO,IAAK;IAC9B,oBACE3G,OAAA,CAACZ,SAAS;MACRwH,IAAI,EAAC,MAAM;MACXC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGlB,CAAC,IAAKe,OAAO,CAACI,cAAc,CAACnB,CAAC,CAACoB,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAED,MAAMC,YAAY,GAAIZ,OAAO,IAAK;IAChC,oBACE3G,OAAA,CAACZ,SAAS;MACRwH,IAAI,EAAC,QAAQ;MACbC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGlB,CAAC,IAAKe,OAAO,CAACI,cAAc,CAACnB,CAAC,CAACoB,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;;EAID;EACA,MAAME,gBAAgB,GAAGtJ,WAAW,CAAEiC,IAAI,IAAK;IAC7C,MAAMgG,QAAQ,GAAGhG,IAAI,CAACiG,IAAI,CAACrE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIqD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGlG,IAAI,CAClBmG,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDqE,MAAM,CAAC,CAACC,GAAG,EAAEzE,GAAG,KAAKyE,GAAG,IAAIR,MAAM,CAACjE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/D+C,QAAQ,CAAC/C,UAAU,GAAGiD,QAAQ;IAChC;IACA,OAAOlG,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAMsH,iBAAiB,GAAG1J,KAAK,CAACG,WAAW,CAAC,CAAC8F,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAH,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyD,kBAAkB,GAAGxJ,WAAW,CAAC,MAAM;IAC3C4F,gBAAgB,CAACmC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPlC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4D,kBAAkB,GAAGzJ,WAAW,CAAE0J,MAAM,IAAK;IACjD,MAAM;MAAE5D;IAAM,CAAC,GAAGH,aAAa;IAC/B,IAAIG,KAAK,KAAK,IAAI,EAAE;MAClB;MACA0D,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC1F,WAAW,CAAC2F,QAAQ,IAAI;UACtB,IAAI7B,WAAW,GAAG6B,QAAQ,CAACjG,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKoB,KAAK,EAAE;cACpB,IAAI4D,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAG7F,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGF,GAAG;kBAAEC,OAAO,EAAE4F,MAAM;kBAAE3F,iBAAiB,EAAE2F;gBAAO,CAAC;cAC/D;YACF;YACA,OAAO7F,GAAG;UACZ,CAAC,CAAC;UAEFmE,WAAW,GAAGsB,gBAAgB,CAACtB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAzC,UAAU,CAAC,MAAM;UACfS,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACL,aAAa,EAAE6D,kBAAkB,EAAEF,gBAAgB,EAAEtD,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAM8D,mBAAmB,GAAG9J,WAAW,CAAC,MAAM;IAC5CkD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6G,oBAAoB,GAAG/J,WAAW,CAAC,MAAM;IAC7CkD,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgH,YAAY,GAAGhK,WAAW,CAAC,MAAM;IACrC,IAAI+C,SAAS,CAACkH,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACzH,cAAc,CAAC0H,QAAQ,CAACnH,SAAS,CAACkH,IAAI,CAAC,CAAC,CAAC,EAAE;MACzExH,iBAAiB,CAACsF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhF,SAAS,CAACkH,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDjE,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjC+D,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIvH,cAAc,CAAC0H,QAAQ,CAACnH,SAAS,CAACkH,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDjE,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAACjD,SAAS,EAAEP,cAAc,EAAEuH,oBAAoB,EAAE/D,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAMmE,YAAY,GAAGnK,WAAW,CAAE0J,MAAM,IAAK;IAC3CjH,iBAAiB,CAACsF,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACgC,IAAI,IAAIA,IAAI,KAAKV,MAAM,CAAC,CAAC;IAC/D1D,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMqE,eAAe,GAAGrK,WAAW,CAAE0E,EAAE,IAAK;IAC1CR,WAAW,CAAC6D,IAAI,IAAI;MAClB;MACA,IAAIC,WAAW,GAAGD,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACAmE,WAAW,GAAGsB,gBAAgB,CAACtB,WAAW,CAAC;;MAE3C;MACA,MAAMsC,cAAc,GAAGtC,WAAW,CAACI,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;MACrFsG,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9F,EAAE,GAAG+F,CAAC,CAAC/F,EAAE,CAAC;;MAE1C;MACA,IAAIgG,SAAS,GAAG,CAAC;MACjB1C,WAAW,GAAGA,WAAW,CAACpE,GAAG,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,EAAE;UACvC,OAAO;YAAE,GAAGH,GAAG;YAAEe,EAAE,EAAE8F,SAAS;UAAG,CAAC;QACpC;QACA,OAAO7G,GAAG;MACZ,CAAC,CAAC;MAEF,OAAOmE,WAAW;IACpB,CAAC,CAAC;IAEFhC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAACsD,gBAAgB,EAAEtD,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM2E,aAAa,GAAG3K,WAAW,CAAE0E,EAAE,IAAK;IACxCR,WAAW,CAAC6D,IAAI,IAAI;MAClB;MACA,IAAIC,WAAW,GAAGD,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;;MAEpF;MACAmE,WAAW,GAAGsB,gBAAgB,CAACtB,WAAW,CAAC;;MAE3C;MACA,MAAMsC,cAAc,GAAGtC,WAAW,CAACI,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;MACrFsG,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9F,EAAE,GAAG+F,CAAC,CAAC/F,EAAE,CAAC;;MAE1C;MACA,IAAIgG,SAAS,GAAG,CAAC;MACjB1C,WAAW,GAAGA,WAAW,CAACpE,GAAG,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,EAAE;UACvC,OAAO;YAAE,GAAGH,GAAG;YAAEe,EAAE,EAAE8F,SAAS;UAAG,CAAC;QACpC;QACA,OAAO7G,GAAG;MACZ,CAAC,CAAC;MAEF,OAAOmE,WAAW;IACpB,CAAC,CAAC;IAEFzC,UAAU,CAAC,MAAM;MACfS,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACsD,gBAAgB,EAAEtD,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM4E,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFxH,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMyH,YAAY,GAAG5G,QAAQ,CAC1BmE,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACA6G,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC5F,EAAE,KAAK,QAAQ,IAAI,OAAO6F,CAAC,CAAC7F,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO4F,CAAC,CAAC5F,EAAE,GAAG6F,CAAC,CAAC7F,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMkG,OAAO,GAAGD,YAAY,CAACjH,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbsG,IAAI,EAAElH,GAAG,CAACkH,IAAI,GAAI,OAAOlH,GAAG,CAACkH,IAAI,KAAK,QAAQ,IAAIlH,GAAG,CAACkH,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGrG,GAAG,CAACkH,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGnH,GAAG,CAACkH,IAAI,GAAI,EAAE;QAClH,YAAY,EAAElH,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGoH,IAAI,CAACC,KAAK,CAACrH,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFsH,EAAE,EAAE,OAAOtH,GAAG,CAACsH,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACrH,GAAG,CAACsH,EAAE,CAAC,GAAGtH,GAAG,CAACsH,EAAE,IAAI,EAAE;QAClErH,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGqH,KAAK,EAAE,OAAOvH,GAAG,CAACwH,QAAQ,KAAK,QAAQ,GACpCxH,GAAG,CAACwH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGxH,GAAG,CAACwH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGzH,GAAG,CAACwH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EzH,GAAG,CAACwH,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAO1H,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAACoG,OAAO,CAAC,CAAC,CAAC,GAAGzH,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMsG,WAAW,GAAGvH,QAAQ,CACzBmE,MAAM,CAACvE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpEmD,MAAM,CAAC,CAACC,GAAG,EAAEzE,GAAG,KAAKyE,GAAG,IAAI,OAAOzE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMuG,QAAQ,GAAG,MAAMhK,KAAK,CAACiK,IAAI,CAAC,GAAGhK,OAAO,oBAAoB,EAAE;QAChEO,IAAI,EAAE6I,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCpJ,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIuJ,QAAQ,CAACxJ,IAAI,IAAIwJ,QAAQ,CAACxJ,IAAI,CAAC0J,KAAK,EAAE;QACxC;QACA,MAAMhF,WAAW,GAAG,GAAGjF,OAAO,CAACsJ,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAACxJ,IAAI,CAAC2J,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA5F,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACAT,UAAU,CAAC,MAAM;UACf,MAAMsG,MAAM,GAAGhF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C+E,MAAM,CAAC9C,KAAK,CAAC+C,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAGpF,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC0E,MAAM,CAAC;UACjCtG,UAAU,CAAC,MAAM;YACfsB,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACwE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACdnD,OAAO,CAACmD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACR5C,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM6I,kBAAkB,GAAGjM,WAAW,CAAC,CAAC8F,KAAK,EAAE6C,KAAK,KAAK;IACvDY,iBAAiB,CAACzD,KAAK,EAAE6C,KAAK,CAAC;EACjC,CAAC,EAAE,CAACY,iBAAiB,CAAC,CAAC;;EAIvB;EACA,MAAM2C,YAAY,GAAGhM,OAAO,CAAC,MAAM+D,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAExD;EACA,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEvC,OAAA,CAAC3B,GAAG;MAACmG,EAAE,EAAE;QAAE6F,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtCvK,OAAA,CAAC1B,UAAU;QAACgG,OAAO,EAAC,IAAI;QAACkG,KAAK,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAEhD;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtH,OAAA,CAACzB,MAAM;QACL+F,OAAO,EAAC,WAAW;QACnBmG,OAAO,EAAEpK,OAAQ;QACjBmE,EAAE,EAAE;UAAEkG,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EACf;MAED;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEtH,OAAA,CAAC3B,GAAG;IAAAkM,QAAA,gBACFvK,OAAA,CAAC3B,GAAG;MAACmG,EAAE,EAAE;QAAEwF,OAAO,EAAE,MAAM;QAAEW,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzFvK,OAAA,CAAC1B,UAAU;QAACgG,OAAO,EAAC,IAAI;QAACwG,YAAY;QAAAP,QAAA,EAAC;MAEtC;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtH,OAAA,CAAC3B,GAAG;QAAAkM,QAAA,gBACFvK,OAAA,CAACzB,MAAM;UACL+F,OAAO,EAAC,WAAW;UACnBkG,KAAK,EAAC,SAAS;UACfO,SAAS,eAAE/K,OAAA,CAACX,YAAY;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BmD,OAAO,EAAE7F,cAAe;UACxBJ,EAAE,EAAE;YAAEwG,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACf;QAED;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtH,OAAA,CAACzB,MAAM;UACL+F,OAAO,EAAC,WAAW;UACnBkG,KAAK,EAAC,SAAS;UACfO,SAAS,eAAE/K,OAAA,CAACN,gBAAgB;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCmD,OAAO,EAAE3B,gBAAiB;UAC1BmC,QAAQ,EAAE5J,oBAAqB;UAC/BmD,EAAE,EAAE;YAAEwG,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EAEblJ,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETtH,OAAA,CAACzB,MAAM;UACL+F,OAAO,EAAC,UAAU;UAClByG,SAAS,eAAE/K,OAAA,CAACV,cAAc;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BmD,OAAO,EAAEhF,aAAc;UAAA8E,QAAA,EACxB;QAED;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtH,OAAA,CAAC3B,GAAG;MAACmG,EAAE,EAAE;QAAEqG,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eACjBvK,OAAA,CAAChB,SAAS;QACRkM,KAAK,EAAC,sCAAQ;QACd5G,OAAO,EAAC,UAAU;QAClB6G,IAAI,EAAC,OAAO;QACZtE,KAAK,EAAEpF,YAAa;QACpBqF,QAAQ,EAAGlB,CAAC,IAAKlE,eAAe,CAACkE,CAAC,CAACoB,MAAM,CAACH,KAAK,CAAE;QACjDuE,WAAW,EAAC,+CAAY;QACxB5G,EAAE,EAAE;UAAE0C,KAAK,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtH,OAAA,CAACxB,KAAK;MAACgG,EAAE,EAAE;QAAE0C,KAAK,EAAE,MAAM;QAAEmE,QAAQ,EAAE;MAAS,CAAE;MAAAd,QAAA,eAC/CvK,OAAA,CAAC3B,GAAG;QAACmG,EAAE,EAAE;UAAE8G,MAAM,EAAE,MAAM;UAAEpE,KAAK,EAAE;QAAO,CAAE;QAAAqD,QAAA,gBACzCvK,OAAA;UAAAuK,QAAA,EACG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QAAa;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACRtH,OAAA,CAACd,SAAS;UACR2H,KAAK,EAAEuD,YAAa;UACpBmB,QAAQ,EAAC,MAAM;UACfC,OAAO,EAAC,IAAI;UACZ/J,YAAY,EAAEA,YAAa;UAC3BgK,QAAQ,EAAC,UAAU;UACnBC,aAAa;UACbC,YAAY,EAAG9F,OAAO,IAAK;YACzB,IAAIA,OAAO,CAAC/C,EAAE,KAAK,OAAO,EAAE,OAAO,WAAW;YAC9C,IAAI+C,OAAO,CAAC3D,QAAQ,EAAE,OAAO,aAAa;YAC1C,OAAO,EAAE;UACX,CAAE;UACF0J,SAAS,EAAC,kBAAkB;UAC5BC,SAAS;UACTC,IAAI,EAAE,GAAI;UACVC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,iBAAiB,EAAC,sGAAsG;UACxHC,yBAAyB,EAAC,yFAA4C;UAAA1B,QAAA,gBAGtEvK,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,IAAI;YACVmG,MAAM,EAAC,IAAI;YACXC,QAAQ;YACR/G,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEpB,IAAI,CAACC,KAAK,CAACvD,OAAO,CAAC/C,EAAE;gBAAC;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACxG;cACA,OAAO,OAAOzB,OAAO,CAAC/C,EAAE,KAAK,QAAQ,GAAGqG,IAAI,CAACC,KAAK,CAACvD,OAAO,CAAC/C,EAAE,CAAC,GAAG+C,OAAO,CAAC/C,EAAE;YAC7E,CAAE;YACFmE,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,MAAM;YACZmG,MAAM,EAAC,MAAM;YACbC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,MAAM,CAAC,GAAGa,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YACrFhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,MAAMyG,SAAS,GAAGzG,OAAO,CAACoD,IAAI,IAAIpD,OAAO,CAACoD,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGvC,OAAO,CAACoD,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGrD,OAAO,CAACoD,IAAI;cACxG,IAAIpD,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAE+B;gBAAS;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC3F;cACA,OAAOgF,SAAS;YAClB,CAAE;YACFrF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,YAAY;YAClBmG,MAAM,EAAC,YAAY;YACnBC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,YAAY,CAAC,GAAGa,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC3FhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAE1E,OAAO,CAAC,YAAY;gBAAC;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACvG;cACA,OAAOzB,OAAO,CAAC,YAAY,CAAC;YAC9B,CAAE;YACFoB,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,OAAO;YACbmG,MAAM,EAAC,OAAO;YACdC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,OAAO,CAAC,GAAG0B,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YACxFhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,MAAM0G,OAAO,GAAG,OAAO1G,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGsD,IAAI,CAACC,KAAK,CAACvD,OAAO,CAAC,OAAO,CAAC,CAAC,GAAGA,OAAO,CAAC,OAAO,CAAC;cACtG,IAAIA,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEgC;gBAAO;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOiF,OAAO;YAChB,CAAE;YACFtF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,IAAI;YACVmG,MAAM,EAAC,IAAI;YACXC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,IAAI,CAAC,GAAG0B,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YACrFhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,MAAM2G,OAAO,GAAG,OAAO3G,OAAO,CAACwD,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACvD,OAAO,CAACwD,EAAE,CAAC,GAAGxD,OAAO,CAACwD,EAAE;cACpF,IAAIxD,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEiC;gBAAO;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOkF,OAAO;YAChB,CAAE;YACFvF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,SAAS;YACfmG,MAAM,EAAC,SAAS;YAChBC,QAAQ;YACR/G,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAC/C,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAI+C,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,MAAMuK,iBAAiB,GAAG5G,OAAO,CAAC5D,iBAAiB,IAAI,KAAK;gBAC5D,oBACEjC,OAAA,CAACzB,MAAM;kBACL+F,OAAO,EAAC,UAAU;kBAClB6G,IAAI,EAAC,OAAO;kBACZF,QAAQ;kBACRzG,EAAE,EAAE;oBACFkI,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE,GAAG;oBACZC,aAAa,EAAE,MAAM;oBACrBC,QAAQ,EAAE;kBACZ,CAAE;kBAAAtC,QAAA,EAEDkC;gBAAiB;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAEb;cAEA,IAAIwF,UAAU,GAAG,MAAM;cACvB,IAAIC,UAAU,GAAG,KAAK;cAEtB,IAAIlH,OAAO,CAAC5D,iBAAiB,IAAI4D,OAAO,CAAC5D,iBAAiB,KAAK,MAAM,EAAE;gBACrE6K,UAAU,GAAGjH,OAAO,CAAC5D,iBAAiB;gBACtC8K,UAAU,GAAG,IAAI;cACnB;cAEA,oBACE/M,OAAA,CAACzB,MAAM;gBACL+F,OAAO,EAAEyI,UAAU,GAAG,WAAW,GAAG,UAAW;gBAC/C5B,IAAI,EAAC,OAAO;gBACZV,OAAO,EAAEA,CAAA,KAAMN,kBAAkB,CAACtE,OAAO,CAACjD,EAAE,EAAEkK,UAAU,CAAE;gBAC1DtI,EAAE,EAAE;kBACFkI,QAAQ,EAAE,MAAM;kBAChBE,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE;gBACZ,CAAE;gBAAAtC,QAAA,EAEDuC;cAAU;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAEb,CAAE;YACFL,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,UAAU;YAChBmG,MAAM,EAAC,OAAO;YACdC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,UAAU,CAAC,GAAG0B,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC3FhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,MAAMmH,UAAU,GAAG,OAAOnH,OAAO,CAAC0D,QAAQ,KAAK,QAAQ,GACpD1D,OAAO,CAAC0D,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG1D,OAAO,CAAC0D,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG3D,OAAO,CAAC0D,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GACvF3D,OAAO,CAAC0D,QAAQ;cAClB,IAAI1D,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAEyC;gBAAU;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC5F;cACA,OAAO0F,UAAU;YACnB,CAAE;YACF/F,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL4G,KAAK,EAAC,YAAY;YAClBmG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRE,MAAM,EAAG1F,OAAO,IAAKF,SAAS,CAACE,OAAO,CAACd,OAAO,EAAE,YAAY,CAAC,GAAG0B,YAAY,CAACZ,OAAO,CAAC,GAAG,IAAK;YAC7FhB,kBAAkB,EAAEA,kBAAmB;YACvCP,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAC/C,EAAE,KAAK,OAAO,EAAE;gBAC1B,MAAMmK,UAAU,GAAG,OAAOpH,OAAO,CAACzC,UAAU,KAAK,QAAQ,GAAGyC,OAAO,CAACzC,UAAU,CAACoG,OAAO,CAAC,CAAC,CAAC,GACvF,OAAO3D,OAAO,CAACzC,UAAU,KAAK,QAAQ,IAAI,CAAC8J,KAAK,CAAClH,MAAM,CAACH,OAAO,CAACzC,UAAU,CAAC,CAAC,GAAG4C,MAAM,CAACH,OAAO,CAACzC,UAAU,CAAC,CAACoG,OAAO,CAAC,CAAC,CAAC,GAAG3D,OAAO,CAACzC,UAAU;gBAC3I,oBACEpD,OAAA,CAAC1B,UAAU;kBAACgG,OAAO,EAAC,OAAO;kBAAC6I,UAAU,EAAC,MAAM;kBAAC3C,KAAK,EAAC,SAAS;kBAAAD,QAAA,EAC1D0C;gBAAU;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAEjB;cAEA,MAAM8F,WAAW,GAAG,OAAOvH,OAAO,CAACzC,UAAU,KAAK,QAAQ,GAAGyC,OAAO,CAACzC,UAAU,CAACoG,OAAO,CAAC,CAAC,CAAC,GACxF,OAAO3D,OAAO,CAACzC,UAAU,KAAK,QAAQ,IAAI,CAAC8J,KAAK,CAAClH,MAAM,CAACH,OAAO,CAACzC,UAAU,CAAC,CAAC,GAAG4C,MAAM,CAACH,OAAO,CAACzC,UAAU,CAAC,CAACoG,OAAO,CAAC,CAAC,CAAC,GAAG3D,OAAO,CAACzC,UAAU;cAE3I,IAAIyC,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBAAOlC,OAAA;kBAAMiH,KAAK,EAAE;oBAAEmF,cAAc,EAAE,cAAc;oBAAE5B,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAAE6C;gBAAW;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC7F;cACA,OAAO8F,WAAW;YACpB,CAAE;YACFnG,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFtH,OAAA,CAACb,MAAM;YACL+M,MAAM,EAAC,QAAQ;YACf9G,IAAI,EAAGS,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAC/C,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAI+C,OAAO,CAAC3D,QAAQ,EAAE;gBACpB,oBACElC,OAAA,CAACzB,MAAM;kBACL+F,OAAO,EAAC,WAAW;kBACnBkG,KAAK,EAAC,SAAS;kBACfW,IAAI,EAAC,OAAO;kBACZJ,SAAS,eAAE/K,OAAA,CAACP,QAAQ;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBmD,OAAO,EAAEA,CAAA,KAAM5B,aAAa,CAAChD,OAAO,CAACjD,EAAE,CAAE;kBACzC4B,EAAE,EAAE;oBACFqI,QAAQ,EAAE,MAAM;oBAChBD,aAAa,EAAE;kBACjB,CAAE;kBAAArC,QAAA,EACH;gBAED;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAEb;cACA,oBACEtH,OAAA,CAACzB,MAAM;gBACL+F,OAAO,EAAC,WAAW;gBACnBkG,KAAK,EAAC,OAAO;gBACbW,IAAI,EAAC,OAAO;gBACZJ,SAAS,eAAE/K,OAAA,CAACR,UAAU;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BmD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC1C,OAAO,CAACjD,EAAE,CAAE;gBAC3C4B,EAAE,EAAE;kBACFqI,QAAQ,EAAE,MAAM;kBAChBD,aAAa,EAAE;gBACjB,CAAE;gBAAArC,QAAA,EACH;cAED;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAEb,CAAE;YACFL,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtH,OAAA,CAACvB,MAAM;MACLsF,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBsJ,OAAO,EAAE3F,kBAAmB;MAC5B4F,SAAS;MACTZ,QAAQ,EAAC,IAAI;MACba,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAnD,QAAA,gBAEnBvK,OAAA,CAACtB,WAAW;QAAA6L,QAAA,eACVvK,OAAA,CAAC3B,GAAG;UAACmG,EAAE,EAAE;YAAEwF,OAAO,EAAE,MAAM;YAAEW,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAL,QAAA,gBAClFvK,OAAA,CAAC1B,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAAiG,QAAA,EAAC;UAAS;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CtH,OAAA,CAACzB,MAAM;YACLwM,SAAS,eAAE/K,OAAA,CAACT,OAAO;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBmD,OAAO,EAAEzC,mBAAoB;YAC7BwC,KAAK,EAAC,SAAS;YAAAD,QAAA,EAChB;UAED;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdtH,OAAA,CAACrB,aAAa;QAACgP,QAAQ;QAACnJ,EAAE,EAAE;UAAEoJ,CAAC,EAAE;QAAE,CAAE;QAAArD,QAAA,eACnCvK,OAAA,CAACH,aAAa;UACZyL,MAAM,EAAE,GAAI;UACZuC,SAAS,EAAEnN,cAAc,CAAC6B,MAAO;UACjCuL,QAAQ,EAAE,EAAG;UACb5G,KAAK,EAAC,MAAM;UAAAqD,QAAA,EAEXA,CAAC;YAAE5H,KAAK;YAAEsE;UAAM,CAAC,KAAK;YACrB,MAAMW,MAAM,GAAGlH,cAAc,CAACiC,KAAK,CAAC;YACpC,oBACE3C,OAAA,CAACnB,QAAQ;cAEPoI,KAAK,EAAEA,KAAM;cACb8G,cAAc;cACdC,eAAe,eACbhO,OAAA,CAACf,UAAU;gBACTgP,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBxD,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACT,MAAM,CAAE;gBAAA2C,QAAA,eAEpCvK,OAAA,CAACR,UAAU;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiD,QAAA,eAEDvK,OAAA,CAAClB,cAAc;gBAAC2L,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACC,MAAM,CAAE;gBAAA2C,QAAA,eACxDvK,OAAA,CAACjB,YAAY;kBAACmP,OAAO,EAAEtG;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZM,MAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBtH,OAAA,CAACpB,aAAa;QAAA2L,QAAA,eACZvK,OAAA,CAACzB,MAAM;UAACkM,OAAO,EAAE/C,kBAAmB;UAAA6C,QAAA,EAAC;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtH,OAAA,CAACvB,MAAM;MACLsF,IAAI,EAAE5C,eAAgB;MACtBkM,OAAO,EAAEpF,oBAAqB;MAC9BqF,SAAS;MACTZ,QAAQ,EAAC,IAAI;MACba,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAnD,QAAA,gBAEnBvK,OAAA,CAACtB,WAAW;QAAA6L,QAAA,EAAC;MAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCtH,OAAA,CAACrB,aAAa;QAAA4L,QAAA,eACZvK,OAAA,CAAChB,SAAS;UACRmP,SAAS;UACTC,MAAM,EAAC,OAAO;UACdxL,EAAE,EAAC,MAAM;UACTsI,KAAK,EAAC,0BAAM;UACZtE,IAAI,EAAC,MAAM;UACX0G,SAAS;UACThJ,OAAO,EAAC,UAAU;UAClBuC,KAAK,EAAE5F,SAAU;UACjB6F,QAAQ,EAAGlB,CAAC,IAAK1E,YAAY,CAAC0E,CAAC,CAACoB,MAAM,CAACH,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBtH,OAAA,CAACpB,aAAa;QAAA2L,QAAA,gBACZvK,OAAA,CAACzB,MAAM;UAACkM,OAAO,EAAExC,oBAAqB;UAAAsC,QAAA,EAAC;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDtH,OAAA,CAACzB,MAAM;UAACkM,OAAO,EAAEvC,YAAa;UAACsC,KAAK,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC7G,EAAA,CAl7BIP,aAAa;EAAA,QAyHWJ,WAAW;AAAA;AAAAuO,EAAA,GAzHnCnO,aAAa;AAo7BnB,eAAeA,aAAa;AAAC,IAAAmO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}