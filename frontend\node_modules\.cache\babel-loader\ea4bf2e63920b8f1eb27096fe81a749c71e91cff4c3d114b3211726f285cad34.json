{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1025,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1043,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1098,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1095,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1089,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1171,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          paginationModel: paginationModel,\n          onPaginationModelChange: setPaginationModel,\n          pageSizeOptions: [25, 50, 150],\n          pagination: true,\n          paginationMode: \"client\",\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          disableVirtualization: false,\n          rowHeight: 48,\n          density: \"compact\",\n          rowBuffer: 5,\n          columnBuffer: 2,\n          disableColumnMenu: true,\n          disableColumnFilter: true,\n          disableColumnSelector: true,\n          disableDensitySelector: true,\n          hideFooterSelectedRowCount: true,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, `datagrid-${paginationModel.pageSize}-${memoGridData.length}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1190,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1189,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1309,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1319,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1354,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1340,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1363,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1041,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"9/MIQOQFDVgLItTGBXCcnM0tHtU=\");\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "axios", "API_URL", "FixedSizeList", "toast", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "children", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "timer", "setPaginationModel", "prev", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "setItem", "toString", "console", "log", "originalData", "setOriginalData", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showToast", "message", "type", "success", "duration", "position", "style", "background", "borderRadius", "fontWeight", "padding", "boxShadow", "error", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "totalValue", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "noCounter", "for<PERSON>ach", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "newData", "splice", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "backgroundColor", "startIcon", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "memoGridData", "dataLength", "textAlign", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "endAdornment", "edge", "rows", "onPaginationModelChange", "pageSizeOptions", "pagination", "paginationMode", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "disableVirtualization", "rowHeight", "density", "<PERSON><PERSON><PERSON><PERSON>", "columnBuffer", "disableColumnMenu", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnSelector", "disableDensitySelector", "hideFooterSelectedRowCount", "getRowClassName", "isCellEditable", "colDef", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              key={`datagrid-${paginationModel.pageSize}-${memoGridData.length}`}\n              rows={memoGridData}\n              columns={columns}\n              paginationModel={paginationModel}\n              onPaginationModelChange={setPaginationModel}\n              pageSizeOptions={[25, 50, 150]}\n              pagination\n              paginationMode=\"client\"\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              disableVirtualization={false}\n              rowHeight={48}\n              density=\"compact\"\n              rowBuffer={5}\n              columnBuffer={2}\n              disableColumnMenu\n              disableColumnFilter\n              disableColumnSelector\n              disableDensitySelector\n              hideFooterSelectedRowCount\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAAC3B,KAAK;IAAA,GAAKuC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAG3E,KAAK,CAAC4E,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC;IAErC8E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA9E,SAAS,CAAC,MAAM;IACdiF,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC/C,MAAM;IAELyE,OAAO,EAAEG,WAAY;IACrBE,OAAO,EAAEJ,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDO,KAAK,EAAC,SAAS;IACfC,IAAI,EAAC,OAAO;IACZC,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,sBAAsB;MAClCC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EAEDlB,OAAO,CAACH,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAAC6B,GAAA,GA5CG3B,UAAU;AA8ChB,MAAM4B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnH,QAAQ,CAAC,MAAM;IACzD,MAAMoH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGpD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC2H,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC+H,UAAU,EAAEC,aAAa,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACmI,YAAY,EAAEC,eAAe,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoI,KAAK,GAAGtE,UAAU,CAAC,MAAM;MAC7BmE,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMjE,YAAY,CAACuE,KAAK,CAAC;EAClC,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACA9H,SAAS,CAAC,MAAM;IACdqI,kBAAkB,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAACP,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2I,UAAU,EAAEC,aAAa,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC6I,eAAe,EAAEP,kBAAkB,CAAC,GAAGtI,QAAQ,CAAC,MAAM;IAC3D,MAAM8I,KAAK,GAAGzB,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLkB,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACA7I,SAAS,CAAC,MAAM;IACdoH,YAAY,CAAC4B,OAAO,CAAC,kBAAkB,EAAEJ,eAAe,CAACE,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC;IAC7EC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAI9B;EACA,MAAM,CAACM,YAAY,EAAEC,eAAe,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdoH,YAAY,CAAC4B,OAAO,CAAC,gBAAgB,EAAE1B,IAAI,CAACgC,SAAS,CAACrC,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMsC,aAAa,GAAG,CAAClD,IAAI,IAAI,EAAE,EAAEmD,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/J,QAAQ,CAAC,MAAM;IAC7CmJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C1C,aAAa,GAAG,IAAIA,aAAa,CAACsD,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAItD,aAAa,IAAIA,aAAa,CAACsD,MAAM,GAAG,CAAC,EAAE;MAC7Cb,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMa,aAAa,GAAGvD,aAAa,CAAC+C,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKK,SAAS,EAAE;UAC9BR,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKM,SAAS,EAAE;UACvCR,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMS,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAC1D,GAAGV,GAAG;QACNW,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAd,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMe,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;MAC1D,GAAGV,GAAG;MACNW,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGrK,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMsK,iBAAiB,GAAGtK,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMuK,gBAAgB,GAAGvK,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMwK,mBAAmB,GAAGxK,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMyK,UAAU,GAAItE,IAAI,IAAKA,IAAI,CAACmD,GAAG,CAACC,GAAG,KAAK;IAC5CW,EAAE,EAAEX,GAAG,CAACW,EAAE;IACVE,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVV,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCiB,UAAU,EAAEnB,GAAG,CAACmB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA5K,SAAS,CAAC,MAAM;IACd,IAAI0G,YAAY,IAAImD,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGzD,IAAI,CAACgC,SAAS,CAACqB,UAAU,CAACd,QAAQ,CAAC,CAAC;MACpD,MAAMmB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BpH,YAAY,CAAC4G,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGnH,UAAU,CAAC,MAAM;UAC1CyG,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCnE,YAAY,CAAC,CAAC,GAAGmD,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIY,gBAAgB,CAACQ,OAAO,EAAE;QAC5BpH,YAAY,CAAC4G,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACpB,QAAQ,EAAEnD,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpL,QAAQ,CAAC;IACjDqL,IAAI,EAAE,KAAK;IACXxG,KAAK,EAAE,IAAI;IACXyG,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACArL,SAAS,CAAC,MAAM;IACd,IAAIoJ,YAAY,CAACW,MAAM,KAAK,CAAC,IAAIF,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACpDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,QAAQ,CAACE,MAAM,CAAC;MACpDV,eAAe,CAAC,CAAC,GAAGQ,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAET,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMkC,SAAS,GAAGrL,WAAW,CAAC,CAACsL,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC3D,QAAQA,IAAI;MACV,KAAK,SAAS;QACZrI,KAAK,CAACsI,OAAO,CAACF,OAAO,EAAE;UACrBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBxG,KAAK,EAAE,OAAO;YACdyG,YAAY,EAAE,MAAM;YACpBpG,QAAQ,EAAE,MAAM;YAChBqG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,OAAO;QACV9I,KAAK,CAAC+I,KAAK,CAACX,OAAO,EAAE;UACnBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBxG,KAAK,EAAE,OAAO;YACdyG,YAAY,EAAE,MAAM;YACpBpG,QAAQ,EAAE,MAAM;YAChBqG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,MAAM;QACT9I,KAAK,CAACoI,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBxG,KAAK,EAAE,OAAO;YACdyG,YAAY,EAAE,MAAM;YACpBpG,QAAQ,EAAE,MAAM;YAChBqG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZ9I,KAAK,CAACoI,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBxG,KAAK,EAAE,OAAO;YACdyG,YAAY,EAAE,MAAM;YACpBpG,QAAQ,EAAE,MAAM;YAChBqG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF;QACE9I,KAAK,CAACoI,OAAO,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI7F,MAAM,IAAIA,MAAM,CAAC8F,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C5F,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM6F,WAAW,GAAG,GAAGpJ,OAAO,aAAaqD,MAAM,EAAE;MACnD,MAAMgG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAI5B,IAAI,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhB,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC;IACxC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1F,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMwG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMhK,KAAK,CAACiK,MAAM,CAAC,GAAGhK,OAAO,YAAYqD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO4F,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA3F,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM2G,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC1D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8C,kBAAkB,GAAGnN,WAAW,CAAC,CAACoG,IAAI,EAAEgH,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGjH,IAAI,IAAIwD,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC0D,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClD8D,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAK;MACpB,IAAI4D,UAAU,IAAI5D,GAAG,CAACW,EAAE,KAAKiD,UAAU,CAACjD,EAAE,EAAE;QAC1C,OAAOuD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACzC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO+C,GAAG,IAAIC,MAAM,CAACnE,GAAG,CAACmB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgE,gBAAgB,GAAG5N,WAAW,CAAEoG,IAAI,IAAK;IAC7C,MAAMyH,QAAQ,GAAGzH,IAAI,CAAC0H,IAAI,CAACtE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIwD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG3H,IAAI,CAClBoH,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClD8D,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAKkE,GAAG,IAAIC,MAAM,CAACnE,GAAG,CAACmB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DkD,QAAQ,CAAClD,UAAU,GAAGoD,QAAQ;IAChC;IACA,OAAO3H,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4H,2BAA2B,GAAGhO,WAAW,CAC7CqD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAAC4B,OAAO,CAAC,eAAe,EAAE1B,IAAI,CAACgC,SAAS,CAACjD,IAAI,CAAC,CAAC;MAC3D6C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9C,IAAI,CAAC0D,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGjO,WAAW,CACvCqD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvB6C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACzC,YAAY,CACf,CAAC;;EAED;EACA,MAAMyH,gBAAgB,GAAGlO,WAAW,CAAEmO,MAAM,IAAK;IAC/CtE,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAI+F,UAAU,GAAGjB,kBAAkB,CAAC9E,IAAI,EAAE8F,MAAM,CAAC;MACjD,MAAME,WAAW,GAAGhG,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACW,EAAE,KAAKgE,MAAM,CAAChE,EAAE,EAAE,OAAO;UAAE,GAAGX,GAAG;UAAE,GAAG2E;QAAO,CAAC;QACtD,IAAI3E,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAEmB,UAAU,EAAEyD;QAAW,CAAC;QACjE,OAAO5E,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAwE,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMK,uBAAuB,GAAIrC,KAAK,IAAK;IACzCZ,SAAS,CAAC,SAASY,KAAK,CAACX,OAAO,EAAE,EAAE,OAAO,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAG1O,KAAK,CAACG,WAAW,CAAC,CAAC2E,KAAK,EAAEyG,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVxG,KAAK;MACLyG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoD,kBAAkB,GAAGxO,WAAW,CAAC,MAAM;IAC3CkL,gBAAgB,CAAC7C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP8C,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACA3C,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+F,kBAAkB,GAAGzO,WAAW,CAAE0O,MAAM,IAAK;IACjD,MAAM;MAAE/J;IAAM,CAAC,GAAGsG,aAAa;IAC/B,IAAItG,KAAK,KAAK,IAAI,EAAE;MAClB;MACA6J,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC/E,WAAW,CAACgF,QAAQ,IAAI;UACtB,IAAIR,WAAW,GAAGQ,QAAQ,CAACtF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACW,EAAE,KAAKxF,KAAK,EAAE;cACpB,IAAI+J,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGlF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIoF,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAIxG,aAAa,EAAE;kBACjBwG,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAIvG,UAAU,EAAE;kBACdsG,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAACjF,MAAM,GAAG,CAAC,EAAE;kBACvBgF,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAGzF,GAAG;kBAAEC,OAAO,EAAEqF,WAAW;kBAAEpF,iBAAiB,EAAEoF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOtF,GAAG;UACZ,CAAC,CAAC;UAEF6E,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAxK,UAAU,CAAC,MAAM;UACfwH,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC;QACpC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEuD,kBAAkB,EAAEZ,gBAAgB,EAAEvC,SAAS,EAAE9C,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAE/F;EACA,MAAMyG,mBAAmB,GAAGlP,WAAW,CAAC,MAAM;IAC5C0H,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyH,oBAAoB,GAAGnP,WAAW,CAAC,MAAM;IAC7C0H,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4H,YAAY,GAAGpP,WAAW,CAAC,MAAM;IACrC,IAAIuH,SAAS,CAAC8H,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACrI,cAAc,CAACsI,QAAQ,CAAC/H,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEpI,iBAAiB,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEd,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDhE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B8D,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAInI,cAAc,CAACsI,QAAQ,CAAC/H,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDhE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC9D,SAAS,EAAEP,cAAc,EAAEmI,oBAAoB,EAAE9D,SAAS,CAAC,CAAC;;EAEhE;EACA,MAAMkE,YAAY,GAAGvP,WAAW,CAAE0O,MAAM,IAAK;IAC3CzH,iBAAiB,CAACoB,IAAI,IAAIA,IAAI,CAACmF,MAAM,CAACgC,IAAI,IAAIA,IAAI,KAAKd,MAAM,CAAC,CAAC;IAC/DrD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMoE,eAAe,GAAGzP,WAAW,CAAEmK,EAAE,IAAK;IAC1CN,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAIgG,WAAW,GAAGhG,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA,IAAIkG,SAAS,GAAG,CAAC;MACjBrB,WAAW,CAACsB,OAAO,CAACnG,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGqF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFrB,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFhD,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;EAChC,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMuE,aAAa,GAAG5P,WAAW,CAAEmK,EAAE,IAAK;IACxCN,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAIgG,WAAW,GAAGhG,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;;MAEpF;MACA,IAAIkG,SAAS,GAAG,CAAC;MACjBrB,WAAW,CAACsB,OAAO,CAACnG,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGqF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFrB,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFxK,UAAU,CAAC,MAAM;MACfwH,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMwE,eAAe,GAAG7P,WAAW,CAAEmK,EAAE,IAAK;IAC1CN,WAAW,CAACxB,IAAI,IAAI;MAClB;MACA,MAAMyH,YAAY,GAAGzH,IAAI,CAACmF,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAIuF,SAAS,GAAG,CAAC;MACjBI,YAAY,CAACH,OAAO,CAACnG,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGqF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMrB,WAAW,GAAGT,gBAAgB,CAACkC,YAAY,CAAC;;MAElD;MACA9B,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElChD,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B,OAAOgD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAM0E,YAAY,GAAG/P,WAAW,CAAEgQ,UAAU,IAAK;IAC/CnG,WAAW,CAACxB,IAAI,IAAI;MAClB;MACA,MAAM4H,WAAW,GAAG5H,IAAI,CAAC6H,SAAS,CAAC1G,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAK6F,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO5H,IAAI;;MAEnC;MACA,MAAM8H,KAAK,GAAGtF,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAMwF,UAAU,GAAG/H,IAAI,CAAC4H,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAAC/F,EAAE,KAAK,QAAQ,GAAG+F,UAAU,CAAC/F,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM8D,MAAM,GAAG;QACbhE,EAAE,EAAEgG,KAAK;QACT9F,EAAE,EAAEgG,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACN9G,OAAO,EAAE,EAAE;QACX+G,QAAQ,EAAE,EAAE;QACZ7F,UAAU,EAAE,CAAC;QACbjB,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,KAAK;QACfS,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAMqG,OAAO,GAAG,CAAC,GAAGpI,IAAI,CAAC;MACzBoI,OAAO,CAACC,MAAM,CAACT,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE9B,MAAM,CAAC;;MAE1C;MACA,IAAIuB,SAAS,GAAG,CAAC;MACjBe,OAAO,CAACd,OAAO,CAACnG,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGqF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMrB,WAAW,GAAGT,gBAAgB,CAAC6C,OAAO,CAAC;;MAE7C;MACAzC,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElChD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;MAC7B,OAAOgD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAMsF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF/I,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMgJ,YAAY,GAAG,CAAChH,QAAQ,IAAI,EAAE,EACjC4D,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAiH,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACzG,EAAE,KAAK,QAAQ,IAAI,OAAO0G,CAAC,CAAC1G,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOyG,CAAC,CAACzG,EAAE,GAAG0G,CAAC,CAAC1G,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM2G,OAAO,GAAGJ,YAAY,CAACrH,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACboG,IAAI,EAAE9G,GAAG,CAAC8G,IAAI,GAAI,OAAO9G,GAAG,CAAC8G,IAAI,KAAK,QAAQ,IAAI9G,GAAG,CAAC8G,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAG9F,GAAG,CAAC8G,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGzH,GAAG,CAAC8G,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE9G,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG0H,IAAI,CAACC,KAAK,CAAC3H,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF+G,EAAE,EAAE,OAAO/G,GAAG,CAAC+G,EAAE,KAAK,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAAC3H,GAAG,CAAC+G,EAAE,CAAC,GAAG/G,GAAG,CAAC+G,EAAE,IAAI,EAAE;QAClE9G,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG0H,KAAK,EAAE,OAAO5H,GAAG,CAACgH,QAAQ,KAAK,QAAQ,GACpChH,GAAG,CAACgH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGhH,GAAG,CAACgH,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAAG7H,GAAG,CAACgH,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAC3E7H,GAAG,CAACgH,QAAQ,IAAI,EAAE;QACpBc,MAAM,EAAE,OAAO9H,GAAG,CAACmB,UAAU,KAAK,QAAQ,GAAGnB,GAAG,CAACmB,UAAU,CAAC0G,OAAO,CAAC,CAAC,CAAC,GAAG7H,GAAG,CAACmB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM4G,WAAW,GAAG,CAAC3H,QAAQ,IAAI,EAAE,EAChC4D,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACmB,UAAU,CAAC,CACpE8C,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAKkE,GAAG,IAAI,OAAOlE,GAAG,CAACmB,UAAU,KAAK,QAAQ,GAAGnB,GAAG,CAACmB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAM6G,YAAY,GAAInL,MAAM,IAAIA,MAAM,CAAC8F,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG9F,MAAM;MAE5F,MAAMoL,QAAQ,GAAG,MAAM1O,KAAK,CAAC2O,IAAI,CAAC,GAAG1O,OAAO,oBAAoB,EAAE;QAChEoD,IAAI,EAAE4K,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnChL,MAAM,EAAEmL;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACrL,IAAI,IAAIqL,QAAQ,CAACrL,IAAI,CAACuL,KAAK,EAAE;QACxC;QACA,MAAMvF,WAAW,GAAG,GAAGpJ,OAAO,CAACiO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACrL,IAAI,CAACwL,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAvG,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC;;QAErC;QACAxH,UAAU,CAAC,MAAM;UACf,MAAMgO,MAAM,GAAGvF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CsF,MAAM,CAAClG,KAAK,CAACmG,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAG3F,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACiF,MAAM,CAAC;UACjChO,UAAU,CAAC,MAAM;YACfyI,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC+E,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BZ,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;IAClC,CAAC,SAAS;MACRzD,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMqK,kBAAkB,GAAGjS,WAAW,CAAC,CAAC2E,KAAK,EAAEuN,KAAK,KAAK;IACvD3D,iBAAiB,CAAC5J,KAAK,EAAEuN,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC3D,iBAAiB,CAAC,CAAC;EAEvB,MAAM4D,OAAO,GAAGjS,OAAO,CAAC,MAAOyG,WAAW,CAAC4C,GAAG,CAAC6I,GAAG,IAAI;IACpD,IAAI,EAAExI,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACyI,cAAc,CAACD,GAAG,CAACxL,KAAK,CAAC,CAAC,IAAIwL,GAAG,CAACxL,KAAK,KAAK,SAAS,IAAIwL,GAAG,CAACxL,KAAK,KAAK,QAAQ,IAAIwL,GAAG,CAACxL,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIwL,GAAG,CAACxL,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEwL,GAAG,CAACxL,KAAK;QAChBC,UAAU,EAAEuL,GAAG,CAACvL,UAAU;QAC1ByL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVzL,QAAQ,EAAE,KAAK;QACf0L,UAAU,EAAGtF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI6C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAM8I,iBAAiB,GAAGvF,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEtG,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAExF,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACiJ,KAAK;cAACC,SAAS,EAAC,KAAK;cAAA3M,QAAA,eACvE7C,OAAA,CAAChC,IAAI;gBACHyR,KAAK,EAAEJ,iBAAkB;gBACzBrN,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAEsN,OAAO,EAAE,GAAG;kBAAEhN,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiM,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA5N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAI0O,UAAU,GAAG,MAAM;UACvB,IAAIlO,UAAU,GAAG,KAAK;UAEtB,IAAIqI,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAIwD,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EqJ,UAAU,GAAG7F,MAAM,CAAC1D,GAAG,CAACE,iBAAiB;YACzC7E,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAEuI,MAAM,CAAC1D,GAAG,CAACW,EAAG;YACrBvF,IAAI,EAAEmO,UAAW;YACjBlO,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEmN;UAAmB;YAAA/N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI+N,GAAG,CAACxL,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEwL,GAAG,CAACxL,KAAK;QAChBC,UAAU,EAAEuL,GAAG,CAACvL,UAAU;QAC1ByL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVzL,QAAQ,EAAE,KAAK;QACf0L,UAAU,EAAGtF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEjH,OAAA,CAACjD,GAAG;YAACmF,EAAE,EAAE;cAAEwM,OAAO,EAAE,MAAM;cAAEkB,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAhN,QAAA,gBAE3D7C,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAC,wDAAW;cAAAzM,QAAA,eACxB7C,OAAA,CAAClC,UAAU;gBACTmE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,SAAS;gBACfN,OAAO,EAAEA,CAAA,KAAMiL,YAAY,CAAC7C,MAAM,CAAC1D,GAAG,CAACW,EAAE,CAAE;gBAC3C7E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACT4N,eAAe,EAAE,cAAc;oBAC/B9N,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACP,oBAAoB;kBAAC4C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVjB,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAC,0EAAc;cAAAzM,QAAA,eAC3B7C,OAAA,CAAClC,UAAU;gBACTmE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,OAAO;gBACbN,OAAO,EAAEA,CAAA,KAAM+K,eAAe,CAAC3C,MAAM,CAAC1D,GAAG,CAACW,EAAE,CAAE;gBAC9C7E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACT4N,eAAe,EAAE,YAAY;oBAC7B9N,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACN,uBAAuB;kBAAC2C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGT6I,MAAM,CAAC1D,GAAG,CAACG,QAAQ,gBAClBvG,OAAA,CAAC/C,MAAM;cAEL8E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZ8N,SAAS,eAAE/P,OAAA,CAACd,QAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAM8K,aAAa,CAAC1C,MAAM,CAAC1D,GAAG,CAACW,EAAE,CAAE;cAC5C7E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,MAAM;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETjB,OAAA,CAAC/C,MAAM;cAEL8E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,OAAO;cACbC,IAAI,EAAC,OAAO;cACZ8N,SAAS,eAAE/P,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BS,OAAO,EAAEA,CAAA,KAAM2K,eAAe,CAACvC,MAAM,CAAC1D,GAAG,CAACW,EAAE,CAAE;cAC9C7E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,QAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG+N,GAAG;MACNtL,QAAQ,EAAEoG,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI6C,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOyI,GAAG,CAACtL,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD0L,UAAU,EAAGtF,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI+H,GAAG,CAACxL,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACExD,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAAC2G,UAAU,EAAC,MAAM;YAAC1G,KAAK,EAAC,SAAS;YAAAa,QAAA,EAC1D,OAAOiH,MAAM,CAACgF,KAAK,KAAK,QAAQ,GAAGhF,MAAM,CAACgF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOnE,MAAM,CAACgF,KAAK,KAAK,QAAQ,IAAI,CAACkB,KAAK,CAACzF,MAAM,CAACT,MAAM,CAACgF,KAAK,CAAC,CAAC,GAAGvE,MAAM,CAACT,MAAM,CAACgF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGnE,MAAM,CAACgF;UAAK;YAAAhO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI6I,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEvG,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAE+N,cAAc,EAAE;YAAe,CAAE;YAAApN,QAAA,EACtFiH,MAAM,CAACgF;UAAK;YAAAhO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI+N,GAAG,CAACxL,KAAK,KAAK,MAAM,IAAIsG,MAAM,CAACgF,KAAK,EAAE;UACxC,OAAOhF,MAAM,CAACgF,KAAK,CAACjB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAImB,GAAG,CAACxL,KAAK,KAAK,IAAI,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAACjE,MAAM,CAACgF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,OAAO,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOhB,IAAI,CAACC,KAAK,CAACjE,MAAM,CAACgF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,IAAI,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAACjE,MAAM,CAACgF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,UAAU,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOhF,MAAM,CAACgF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGhF,MAAM,CAACgF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGnE,MAAM,CAACgF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAACxL,KAAK,KAAK,YAAY,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOhF,MAAM,CAACgF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAACxL,KAAK,KAAK,YAAY,IAAI,OAAOsG,MAAM,CAACgF,KAAK,KAAK,QAAQ,IAAI,CAACkB,KAAK,CAACzF,MAAM,CAACT,MAAM,CAACgF,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOvE,MAAM,CAACT,MAAM,CAACgF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOnE,MAAM,CAACgF,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOhF,MAAM,CAACgF,KAAK;QACrB;QACA,OAAOhF,MAAM,CAACgF,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC1E,MAAM,CAAC8F,OAAO,CAAE,EAAE,CAAC3M,WAAW,EAAEsL,kBAAkB,EAAExC,eAAe,EAAEG,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAM0D,gBAAgB,GAAGrT,OAAO,CAAC,MAAM;IACrC,IAAI,CAAC6H,mBAAmB,CAACsH,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAOzF,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAM4J,WAAW,GAAGzL,mBAAmB,CAAC0L,WAAW,CAAC,CAAC;IACrD,OAAO,CAAC7J,QAAQ,IAAI,EAAE,EAAE4D,MAAM,CAAChE,GAAG,IAAI;MACpC,IAAIvB,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOyL,MAAM,CAACC,MAAM,CAACnK,GAAG,CAAC,CAACoK,IAAI,CAAC1B,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAAClJ,QAAQ,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC,CAACnE,QAAQ,CAACkE,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMK,SAAS,GAAGrK,GAAG,CAACvB,YAAY,CAAC;QACnC,OAAO4L,SAAS,IAAIA,SAAS,CAAC7K,QAAQ,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC,CAACnE,QAAQ,CAACkE,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5J,QAAQ,EAAE7B,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAM6L,YAAY,GAAG5T,OAAO,CAAC,MAAMqT,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACAxT,SAAS,CAAC,MAAM;IACdkJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBL,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1ByL,UAAU,EAAED,YAAY,CAAChK;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAEwL,YAAY,CAAChK,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE1G,OAAA,CAACjD,GAAG;MAACmF,EAAE,EAAE;QAAE0O,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAhO,QAAA,gBACtC7C,OAAA,CAAChD,UAAU;QAAC+E,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAa,QAAA,EAAC;MAEhD;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC/C,MAAM;QACL8E,OAAO,EAAC,WAAW;QACnBL,OAAO,EAAEwB,OAAQ;QACjBhB,EAAE,EAAE;UAAE4O,EAAE,EAAE;QAAE,CAAE;QAAAjO,QAAA,EACf;MAED;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAACjD,GAAG;IAAA8F,QAAA,gBAEF7C,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE6O,EAAE,EAAE;MAAE,CAAE;MAAAlO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,eACV7C,OAAA,CAACjD,GAAG;UAACmF,EAAE,EAAE;YAAEwM,OAAO,EAAE,MAAM;YAAEmB,UAAU,EAAE,QAAQ;YAAEmB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAErB,GAAG,EAAE;UAAE,CAAE;UAAA/M,QAAA,gBAC5G7C,OAAA,CAACjD,GAAG;YAACmF,EAAE,EAAE;cAAEwM,OAAO,EAAE,MAAM;cAAEmB,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAA/M,QAAA,gBACzD7C,OAAA,CAACZ,cAAc;cAAC8C,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACjD,GAAG;cAAA8F,QAAA,gBACF7C,OAAA,CAAChD,UAAU;gBAAC+E,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAEwG,UAAU,EAAE,GAAG;kBAAE1G,KAAK,EAAE,cAAc;kBAAE+O,EAAE,EAAE;gBAAI,CAAE;gBAAAlO,QAAA,EAAC;cAElF;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAChD,UAAU;gBAAC+E,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAa,QAAA,EAAC;cAE7D;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAAC5B,KAAK;YAACyC,SAAS,EAAC,KAAK;YAACqQ,OAAO,EAAE,CAAE;YAAChP,EAAE,EAAE;cAAE+O,QAAQ,EAAE,MAAM;cAAErB,GAAG,EAAE;YAAE,CAAE;YAAA/M,QAAA,gBAClE7C,OAAA,CAAChC,IAAI;cACHmT,IAAI,eAAEnR,OAAA,CAACX,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBwO,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAEtG,MAAM,CAAChE,GAAG,IAAI,CAACA,GAAG,CAACY,OAAO,IAAI,CAACZ,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,MAAO;cACzF1E,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAChC,IAAI;cACHmT,IAAI,eAAEnR,OAAA,CAACV,cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBwO,KAAK,EAAE,WAAW1F,kBAAkB,CAAC2G,YAAY,CAAC,CAACzC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEjM,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACyP,YAAY,IAAI,EAAE,EAAEtG,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,GAAG,CAAC,iBAC1D1G,OAAA,CAAChC,IAAI;cACHmT,IAAI,eAAEnR,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBwO,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAEtG,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,OAAQ;cACzE1E,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE6O,EAAE,EAAE;MAAE,CAAE;MAAAlO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,gBACV7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEwG,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAAlO,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAEuQ,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAArO,QAAA,gBACxD7C,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACf+N,SAAS,eAAE/P,OAAA,CAAClB,YAAY;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAEoH,cAAe;YAAAjG,QAAA,EACzB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACf+N,SAAS,EAAExL,oBAAoB,gBAAGvE,OAAA,CAACjC,gBAAgB;cAACkE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACb,gBAAgB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAE6L,gBAAiB;YAC1B+D,QAAQ,EAAE/M,oBAAqB;YAAA1B,QAAA,EAE9B0B,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACb+N,SAAS,eAAE/P,OAAA,CAACjB,cAAc;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEiI,aAAc;YAAA9G,QAAA,EACxB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE6O,EAAE,EAAE;MAAE,CAAE;MAAAlO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,gBACV7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEwG,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAAlO,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAEuQ,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACrB,UAAU,EAAC,QAAQ;UAAAhN,QAAA,gBAC5E7C,OAAA,CAAC1B,WAAW;YAAC2D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAU,QAAA,gBAC9C7C,OAAA,CAACzB,UAAU;cAAAsE,QAAA,EAAC;YAAI;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACxB,MAAM;cACLsQ,KAAK,EAAEjK,YAAa;cACpB4K,KAAK,EAAC,0BAAM;cACZ8B,QAAQ,EAAGzP,CAAC,IAAKgD,eAAe,CAAChD,CAAC,CAAC0P,MAAM,CAAC1C,KAAK,CAAE;cAAAjM,QAAA,gBAEjD7C,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,KAAK;gBAAAjM,QAAA,EAAC;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,IAAI;gBAAAjM,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,MAAM;gBAAAjM,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,YAAY;gBAAAjM,QAAA,EAAC;cAAU;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,OAAO;gBAAAjM,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,IAAI;gBAAAjM,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,SAAS;gBAAAjM,QAAA,EAAC;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,UAAU;gBAAAjM,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,YAAY;gBAAAjM,QAAA,EAAC;cAAM;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAACnC,SAAS;YACRoE,IAAI,EAAC,OAAO;YACZwP,WAAW,EAAC,yCAAW;YACvB3C,KAAK,EAAErK,UAAW;YAClB8M,QAAQ,EAAGzP,CAAC,IAAK4C,aAAa,CAAC5C,CAAC,CAAC0P,MAAM,CAAC1C,KAAK,CAAE;YAC/C5M,EAAE,EAAE;cAAEwP,QAAQ,EAAE,CAAC;cAAEvP,QAAQ,EAAE;YAAI,CAAE;YACnCwP,UAAU,EAAE;cACVC,cAAc,eACZ5R,OAAA,CAACtB,cAAc;gBAAC4J,QAAQ,EAAC,OAAO;gBAAAzF,QAAA,eAC9B7C,OAAA,CAACT,UAAU;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACD4Q,YAAY,EAAEpN,UAAU,iBACtBzE,OAAA,CAACtB,cAAc;gBAAC4J,QAAQ,EAAC,KAAK;gBAAAzF,QAAA,eAC5B7C,OAAA,CAAClC,UAAU;kBACTmE,IAAI,EAAC,OAAO;kBACZP,OAAO,EAAEA,CAAA,KAAMgD,aAAa,CAAC,EAAE,CAAE;kBACjCoN,IAAI,EAAC,KAAK;kBAAAjP,QAAA,eAEV7C,OAAA,CAACR,SAAS;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDwD,UAAU,iBACTzE,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAa,QAAA,GAAC,eAC9C,EAACsN,gBAAgB,CAAC/F,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9C,KAAK;MAACgF,EAAE,EAAE;QAAEiN,KAAK,EAAE,MAAM;QAAE5M,QAAQ,EAAE;MAAS,CAAE;MAAAM,QAAA,eAC7C7C,OAAA,CAACjD,GAAG;QAACmF,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAEwM,KAAK,EAAE;QAAO,CAAE;QAAAtM,QAAA,eACzC7C,OAAA,CAACnB,QAAQ;UAEPkT,IAAI,EAAErB,YAAa;UACnB3B,OAAO,EAAEA,OAAQ;UACjBxJ,eAAe,EAAEA,eAAgB;UACjCyM,uBAAuB,EAAEhN,kBAAmB;UAC5CiN,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAC/BC,UAAU;UACVC,cAAc,EAAC,QAAQ;UACvBC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,qBAAqB,EAAE,KAAM;UAC7BC,SAAS,EAAE,EAAG;UACdC,OAAO,EAAC,SAAS;UACjBC,SAAS,EAAE,CAAE;UACbC,YAAY,EAAE,CAAE;UAChBC,iBAAiB;UACjBC,mBAAmB;UACnBC,qBAAqB;UACrBC,sBAAsB;UACtBC,0BAA0B;UAC1BC,eAAe,EAAGnJ,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC1D,GAAG,CAACY,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI8C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACF2M,cAAc,EAAGpJ,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC1D,GAAG,CAACY,OAAO,IAAI8C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOuD,MAAM,CAACqJ,MAAM,CAACzP,QAAQ,IAAI,OAAOoG,MAAM,CAACqJ,MAAM,CAACzP,QAAQ,KAAK,UAAU,GAC3EoG,MAAM,CAACqJ,MAAM,CAACzP,QAAQ,CAACoG,MAAM,CAAC,GAAGA,MAAM,CAACqJ,MAAM,CAACzP,QAAQ;UAC3D,CAAE;UACFoH,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAACxD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOmE,MAAM,CAACxD,UAAU,KAAK,QAAQ,EAAE;gBACzCwD,MAAM,CAACxD,UAAU,GAAGgD,MAAM,CAACQ,MAAM,CAACxD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOuD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDhJ,EAAE,EAAE;YACF,cAAc,EAAE;cACd4N,eAAe,EAAE,0BAA0B;cAC3CpH,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBoH,eAAe,EAAE,0BAA0B;cAC3C9N,KAAK,EAAE,eAAe;cACtBiO,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrBxN,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpB+F,OAAO,EAAE,KAAK;cACdyK,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9BvD,eAAe,EAAE,oBAAoB;cACrCsD,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7BvD,eAAe,EAAE,oBAAoB;cACrC9N,KAAK,EAAE,cAAc;cACrBsR,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClC5K,UAAU,EAAE,MAAM;cAClB1G,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChCqM,OAAO,EAAE;YACX,CAAC;YACD6E,SAAS,EAAE;UACb;QAAE,GAlFG,YAAYhO,eAAe,CAACE,QAAQ,IAAIiL,YAAY,CAAChK,MAAM,EAAE;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAAC3C,MAAM;MACL0K,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzByL,OAAO,EAAEpI,kBAAmB;MAC5BqI,SAAS;MACTrR,QAAQ,EAAC,IAAI;MACbsR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAhR,QAAA,gBAEnB7C,OAAA,CAAC1C,WAAW;QAAAuF,QAAA,eACV7C,OAAA,CAACjD,GAAG;UAACmF,EAAE,EAAE;YAAEwM,OAAO,EAAE,MAAM;YAAEsC,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE;UAAS,CAAE;UAAAhN,QAAA,gBAClF7C,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,IAAI;YAAAc,QAAA,EAAC;UAAS;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC/C,MAAM;YACL8S,SAAS,eAAE/P,OAAA,CAAChB,OAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEoK,mBAAoB;YAC7B9J,KAAK,EAAC,SAAS;YAAAa,QAAA,EAChB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjB,OAAA,CAACjD,GAAG;QAACmF,EAAE,EAAE;UAAE4R,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlR,QAAA,gBACxB7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAE6O,EAAE,EAAE;UAAE,CAAE;UAAAlO,QAAA,EAAC;QAElE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAC,KAAK;UAACqQ,OAAO,EAAE,CAAE;UAAArO,QAAA,gBAChC7C,OAAA,CAACpB,gBAAgB;YACfoV,OAAO,eACLhU,OAAA,CAACrB,QAAQ;cACPsV,OAAO,EAAE9O,aAAc;cACvBoM,QAAQ,EAAGzP,CAAC,IAAKsD,gBAAgB,CAACtD,CAAC,CAAC0P,MAAM,CAACyC,OAAO,CAAE;cACpDhS,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDwO,KAAK,EAAC;UAAS;YAAA3O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjB,OAAA,CAACpB,gBAAgB;YACfoV,OAAO,eACLhU,OAAA,CAACrB,QAAQ;cACPsV,OAAO,EAAE5O,UAAW;cACpBkM,QAAQ,EAAGzP,CAAC,IAAKwD,aAAa,CAACxD,CAAC,CAAC0P,MAAM,CAACyC,OAAO,CAAE;cACjDhS,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDwO,KAAK,EAAC;UAAK;YAAA3O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjB,OAAA,CAACzC,aAAa;QAAC2W,QAAQ;QAAChS,EAAE,EAAE;UAAEiS,CAAC,EAAE;QAAE,CAAE;QAAAtR,QAAA,eACnC7C,OAAA,CAACH,aAAa;UACZ8C,MAAM,EAAE,GAAI;UACZyR,SAAS,EAAExQ,cAAc,CAAC8C,MAAO;UACjC2N,QAAQ,EAAE,EAAG;UACblF,KAAK,EAAC,MAAM;UAAAtM,QAAA,EAEXA,CAAC;YAAEiE,KAAK;YAAEyB;UAAM,CAAC,KAAK;YACrB,MAAM+C,MAAM,GAAG1H,cAAc,CAACkD,KAAK,CAAC;YACpC,oBACE9G,OAAA,CAACtC,QAAQ;cAEP6K,KAAK,EAAEA,KAAM;cACb+L,cAAc;cACdC,eAAe,EAAGjJ,MAAM,KAAK,MAAM,iBACjCtL,OAAA,CAAClC,UAAU;gBACTgU,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBpQ,OAAO,EAAEA,CAAA,KAAMyK,YAAY,CAACb,MAAM,CAAE;gBAAAzI,QAAA,eAEpC7C,OAAA,CAACf,UAAU;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAA4B,QAAA,eAEF7C,OAAA,CAACrC,cAAc;gBAAC+D,OAAO,EAAEA,CAAA,KAAM2J,kBAAkB,CAACC,MAAM,CAAE;gBAAAzI,QAAA,eACxD7C,OAAA,CAACpC,YAAY;kBAAC4W,OAAO,EAAElJ;gBAAO;kBAAAxK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZqK,MAAM;cAAAxK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAAqF,QAAA,eACZ7C,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAE0J,kBAAmB;UAAAvI,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAAC3C,MAAM;MACL0K,IAAI,EAAE1D,eAAgB;MACtBmP,OAAO,EAAEzH,oBAAqB;MAC9B0H,SAAS;MACTrR,QAAQ,EAAC,IAAI;MACbsR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAhR,QAAA,gBAEnB7C,OAAA,CAAC1C,WAAW;QAAAuF,QAAA,EAAC;MAAK;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACzC,aAAa;QAAAsF,QAAA,eACZ7C,OAAA,CAACnC,SAAS;UACR4W,SAAS;UACTC,MAAM,EAAC,OAAO;UACd3N,EAAE,EAAC,MAAM;UACT0I,KAAK,EAAC,0BAAM;UACZtH,IAAI,EAAC,MAAM;UACXsL,SAAS;UACT1R,OAAO,EAAC,UAAU;UAClB+M,KAAK,EAAE3K,SAAU;UACjBoN,QAAQ,EAAGzP,CAAC,IAAKsC,YAAY,CAACtC,CAAC,CAAC0P,MAAM,CAAC1C,KAAK;QAAE;UAAAhO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAAqF,QAAA,gBACZ7C,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAEqK,oBAAqB;UAAAlJ,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAEsK,YAAa;UAAChK,KAAK,EAAC,SAAS;UAAAa,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACqC,GAAA,CAhvCIP,aAAa;AAAA4R,GAAA,GAAb5R,aAAa;AAkvCnB,eAAeA,aAAa;AAAC,IAAA7B,EAAA,EAAAI,GAAA,EAAAwB,GAAA,EAAA6R,GAAA;AAAAC,YAAA,CAAA1T,EAAA;AAAA0T,YAAA,CAAAtT,GAAA;AAAAsT,YAAA,CAAA9R,GAAA;AAAA8R,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}