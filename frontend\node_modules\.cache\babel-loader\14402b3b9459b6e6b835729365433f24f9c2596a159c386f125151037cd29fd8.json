{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\FileUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Box, Typography, Paper, Button, CircularProgress } from '@mui/material';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport { uploadExcelFile, VisuallyHiddenInput } from '../utils/fileUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  onFileUpload,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = React.useState(false);\n  const handleFileUpload = useCallback(async file => {\n    if (!file) return;\n    setLoading(true);\n    try {\n      const response = await uploadExcelFile(file);\n      onFileUpload(response);\n    } catch (error) {\n      console.error('上传文件出错:', error);\n      onError(error.message || '上传文件失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  }, [onFileUpload, onError]);\n  const onDrop = useCallback(async acceptedFiles => {\n    // 只处理第一个文件\n    const file = acceptedFiles[0];\n    if (file) {\n      handleFileUpload(file);\n    }\n  }, [handleFileUpload]);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\n      'application/vnd.ms-excel': ['.xls']\n    },\n    multiple: false\n  });\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"\\u8BF7\\u4E0A\\u4F20Excel\\u6587\\u4EF6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      ...getRootProps(),\n      sx: {\n        p: 5,\n        mt: 2,\n        mb: 3,\n        border: '2px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.400',\n        backgroundColor: isDragActive ? 'rgba(25, 118, 210, 0.04)' : 'background.paper',\n        cursor: 'pointer',\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          borderColor: 'primary.main',\n          backgroundColor: 'rgba(25, 118, 210, 0.04)'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 60,\n            color: 'primary.main',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: isDragActive ? '释放文件以上传' : '拖拽文件到此处，或点击选择文件'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"\\u652F\\u6301 .xlsx \\u548C .xls \\u683C\\u5F0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      component: \"label\",\n      disabled: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 20\n      }, this),\n      children: [\"\\u9009\\u62E9\\u6587\\u4EF6\", /*#__PURE__*/_jsxDEV(VisuallyHiddenInput, {\n        type: \"file\",\n        accept: \".xlsx,.xls\",\n        onChange: e => {\n          if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files[0]);\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"udDysj41aGZ7oXIJRyD2f/2wuxM=\", false, function () {\n  return [useDropzone];\n});\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useCallback", "useDropzone", "Box", "Typography", "Paper", "<PERSON><PERSON>", "CircularProgress", "CloudUploadIcon", "uploadExcelFile", "VisuallyHiddenInput", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUpload", "onFileUpload", "onError", "_s", "loading", "setLoading", "useState", "handleFileUpload", "file", "response", "error", "console", "message", "onDrop", "acceptedFiles", "getRootProps", "getInputProps", "isDragActive", "accept", "multiple", "sx", "textAlign", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "mt", "mb", "border", "borderColor", "backgroundColor", "cursor", "transition", "fontSize", "color", "component", "disabled", "startIcon", "type", "onChange", "e", "target", "files", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/FileUpload.js"], "sourcesContent": ["import React, { useCallback } from 'react';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Paper, \r\n  Button,\r\n  CircularProgress\r\n} from '@mui/material';\r\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\r\nimport { uploadExcelFile, VisuallyHiddenInput } from '../utils/fileUtils';\r\n\r\nconst FileUpload = ({ onFileUpload, onError }) => {\r\n  const [loading, setLoading] = React.useState(false);\r\n\r\n  const handleFileUpload = useCallback(async (file) => {\r\n    if (!file) return;\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      const response = await uploadExcelFile(file);\r\n      onFileUpload(response);\r\n    } catch (error) {\r\n      console.error('上传文件出错:', error);\r\n      onError(error.message || '上传文件失败，请重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [onFileUpload, onError]);\r\n\r\n  const onDrop = useCallback(async (acceptedFiles) => {\r\n    // 只处理第一个文件\r\n    const file = acceptedFiles[0];\r\n    if (file) {\r\n      handleFileUpload(file);\r\n    }\r\n  }, [handleFileUpload]);\r\n  \r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\r\n      'application/vnd.ms-excel': ['.xls']\r\n    },\r\n    multiple: false\r\n  });\r\n  \r\n  return (\r\n    <Box sx={{ textAlign: 'center' }}>\r\n      <Typography variant=\"h6\" gutterBottom>\r\n        请上传Excel文件\r\n      </Typography>\r\n      \r\n      <Paper \r\n        {...getRootProps()} \r\n        sx={{\r\n          p: 5,\r\n          mt: 2,\r\n          mb: 3,\r\n          border: '2px dashed',\r\n          borderColor: isDragActive ? 'primary.main' : 'grey.400',\r\n          backgroundColor: isDragActive ? 'rgba(25, 118, 210, 0.04)' : 'background.paper',\r\n          cursor: 'pointer',\r\n          transition: 'all 0.3s ease',\r\n          '&:hover': {\r\n            borderColor: 'primary.main',\r\n            backgroundColor: 'rgba(25, 118, 210, 0.04)'\r\n          }\r\n        }}\r\n      >\r\n        <input {...getInputProps()} />\r\n        \r\n        {loading ? (\r\n          <CircularProgress />\r\n        ) : (\r\n          <>\r\n            <CloudUploadIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              {isDragActive ? '释放文件以上传' : '拖拽文件到此处，或点击选择文件'}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">\r\n              支持 .xlsx 和 .xls 格式\r\n            </Typography>\r\n          </>\r\n        )}\r\n      </Paper>\r\n      \r\n      <Button \r\n        variant=\"contained\" \r\n        component=\"label\"\r\n        disabled={loading}\r\n        startIcon={<CloudUploadIcon />}\r\n      >\r\n        选择文件\r\n        <VisuallyHiddenInput\r\n          type=\"file\"\r\n          accept=\".xlsx,.xls\"\r\n          onChange={(e) => {\r\n            if (e.target.files && e.target.files[0]) {\r\n              handleFileUpload(e.target.files[0]);\r\n            }\r\n          }}\r\n        />\r\n      </Button>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FileUpload; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,WAAW,QAAQ,OAAO;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1E,MAAMC,UAAU,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMC,gBAAgB,GAAGrB,WAAW,CAAC,MAAOsB,IAAI,IAAK;IACnD,IAAI,CAACA,IAAI,EAAE;IAEXH,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMf,eAAe,CAACc,IAAI,CAAC;MAC5CP,YAAY,CAACQ,QAAQ,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BR,OAAO,CAACQ,KAAK,CAACE,OAAO,IAAI,YAAY,CAAC;IACxC,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,YAAY,EAAEC,OAAO,CAAC,CAAC;EAE3B,MAAMW,MAAM,GAAG3B,WAAW,CAAC,MAAO4B,aAAa,IAAK;IAClD;IACA,MAAMN,IAAI,GAAGM,aAAa,CAAC,CAAC,CAAC;IAC7B,IAAIN,IAAI,EAAE;MACRD,gBAAgB,CAACC,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,gBAAgB,CAAC,CAAC;EAEtB,MAAM;IAAEQ,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAG9B,WAAW,CAAC;IAChE0B,MAAM;IACNK,MAAM,EAAE;MACN,mEAAmE,EAAE,CAAC,OAAO,CAAC;MAC9E,0BAA0B,EAAE,CAAC,MAAM;IACrC,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,oBACEtB,OAAA,CAACT,GAAG;IAACgC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAC/BzB,OAAA,CAACR,UAAU;MAACkC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb/B,OAAA,CAACP,KAAK;MAAA,GACAyB,YAAY,CAAC,CAAC;MAClBK,EAAE,EAAE;QACFS,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAEhB,YAAY,GAAG,cAAc,GAAG,UAAU;QACvDiB,eAAe,EAAEjB,YAAY,GAAG,0BAA0B,GAAG,kBAAkB;QAC/EkB,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,eAAe;QAC3B,SAAS,EAAE;UACTH,WAAW,EAAE,cAAc;UAC3BC,eAAe,EAAE;QACnB;MACF,CAAE;MAAAZ,QAAA,gBAEFzB,OAAA;QAAA,GAAWmB,aAAa,CAAC;MAAC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAE7BxB,OAAO,gBACNP,OAAA,CAACL,gBAAgB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEpB/B,OAAA,CAAAE,SAAA;QAAAuB,QAAA,gBACEzB,OAAA,CAACJ,eAAe;UAAC2B,EAAE,EAAE;YAAEiB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEP,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvE/B,OAAA,CAACR,UAAU;UAACkC,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAClCL,YAAY,GAAG,SAAS,GAAG;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACb/B,OAAA,CAACR,UAAU;UAACkC,OAAO,EAAC,OAAO;UAACe,KAAK,EAAC,eAAe;UAAAhB,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA,eACb,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAER/B,OAAA,CAACN,MAAM;MACLgC,OAAO,EAAC,WAAW;MACnBgB,SAAS,EAAC,OAAO;MACjBC,QAAQ,EAAEpC,OAAQ;MAClBqC,SAAS,eAAE5C,OAAA,CAACJ,eAAe;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAN,QAAA,GAChC,0BAEC,eAAAzB,OAAA,CAACF,mBAAmB;QAClB+C,IAAI,EAAC,MAAM;QACXxB,MAAM,EAAC,YAAY;QACnByB,QAAQ,EAAGC,CAAC,IAAK;UACf,IAAIA,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;YACvCvC,gBAAgB,CAACqC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;UACrC;QACF;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACzB,EAAA,CA/FIH,UAAU;EAAA,QA2BwCb,WAAW;AAAA;AAAA4D,EAAA,GA3B7D/C,UAAU;AAiGhB,eAAeA,UAAU;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}