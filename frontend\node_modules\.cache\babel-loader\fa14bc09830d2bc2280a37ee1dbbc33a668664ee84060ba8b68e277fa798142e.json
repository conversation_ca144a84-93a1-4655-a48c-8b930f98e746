{"ast": null, "code": "export const buildWarning = function (message) {\n  let gravity = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'warning';\n  let alreadyWarned = false;\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return () => {\n    if (!alreadyWarned) {\n      alreadyWarned = true;\n      if (gravity === 'error') {\n        console.error(cleanMessage);\n      } else {\n        console.warn(cleanMessage);\n      }\n    }\n  };\n};\nexport const wrapWithWarningOnCall = (method, message) => {\n  if (process.env.NODE_ENV === 'production') {\n    return method;\n  }\n  const warning = buildWarning(message);\n  return function () {\n    warning();\n    return method(...arguments);\n  };\n};", "map": {"version": 3, "names": ["buildWarning", "message", "gravity", "arguments", "length", "undefined", "alreadyWarned", "cleanMessage", "Array", "isArray", "join", "console", "error", "warn", "wrapWithWarningOnCall", "method", "process", "env", "NODE_ENV", "warning"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/utils/warning.js"], "sourcesContent": ["export const buildWarning = (message, gravity = 'warning') => {\n  let alreadyWarned = false;\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return () => {\n    if (!alreadyWarned) {\n      alreadyWarned = true;\n      if (gravity === 'error') {\n        console.error(cleanMessage);\n      } else {\n        console.warn(cleanMessage);\n      }\n    }\n  };\n};\nexport const wrapWithWarningOnCall = (method, message) => {\n  if (process.env.NODE_ENV === 'production') {\n    return method;\n  }\n  const warning = buildWarning(message);\n  return (...args) => {\n    warning();\n    return method(...args);\n  };\n};"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAG,SAAAA,CAACC,OAAO,EAA0B;EAAA,IAAxBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;EACvD,IAAIG,aAAa,GAAG,KAAK;EACzB,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,GAAGA,OAAO,CAACS,IAAI,CAAC,IAAI,CAAC,GAAGT,OAAO;EAC1E,OAAO,MAAM;IACX,IAAI,CAACK,aAAa,EAAE;MAClBA,aAAa,GAAG,IAAI;MACpB,IAAIJ,OAAO,KAAK,OAAO,EAAE;QACvBS,OAAO,CAACC,KAAK,CAACL,YAAY,CAAC;MAC7B,CAAC,MAAM;QACLI,OAAO,CAACE,IAAI,CAACN,YAAY,CAAC;MAC5B;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMO,qBAAqB,GAAGA,CAACC,MAAM,EAAEd,OAAO,KAAK;EACxD,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOH,MAAM;EACf;EACA,MAAMI,OAAO,GAAGnB,YAAY,CAACC,OAAO,CAAC;EACrC,OAAO,YAAa;IAClBkB,OAAO,CAAC,CAAC;IACT,OAAOJ,MAAM,CAAC,GAAAZ,SAAO,CAAC;EACxB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}