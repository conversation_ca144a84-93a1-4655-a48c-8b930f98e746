{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = 'http://localhost:5000/api';\n\n// 预定义的REMARKS选项\nconst REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"CBU CAR\", \"MAXCHECK PLUS\", \"MAXCHECK STANDARD\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError\n}) => {\n  _s();\n  var _gridData$find;\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串\n    return {\n      ...row,\n      REMARKS: ''\n    };\n  });\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  const handleDownload = async () => {\n    try {\n      // 使用浏览器的下载功能\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL').reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框\n  const openRemarksDialog = (rowId, currentValue) => {\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  };\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = () => {\n    setRemarksDialog({\n      ...remarksDialog,\n      open: false\n    });\n  };\n\n  // 选择REMARKS选项\n  const selectRemarkOption = option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      const updatedData = gridData.map(row => {\n        if (row.id === rowId) {\n          return {\n            ...row,\n            REMARKS: option\n          };\n        }\n        return row;\n      });\n      setGridData(updatedData);\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true\n  }];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n\n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        minWidth: 180,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              cursor: 'pointer',\n              '&:hover': {\n                textDecoration: 'underline',\n                color: 'primary.main'\n              }\n            },\n            onClick: () => openRemarksDialog(params.row.id, params.value || ''),\n            children: params.value || '请选择'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          getRowClassName: params => params.row.isTotal ? 'total-row' : '',\n          isCellEditable: handleCellEdit,\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this), gridData.some(row => 'COMMISSION' in row) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u9879\\u76EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u91D1\\u989D (RM)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u603B\\u4F63\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: ((_gridData$find = gridData.find(row => row.NO === 'TOTAL')) === null || _gridData$find === void 0 ? void 0 : _gridData$find.COMMISSION.toFixed(2)) || '0.00',\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u9009\\u62E9REMARKS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: REMARKS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"845rdyTvqwtCfZfmbW9JMEZQZtw=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "DataGrid", "DownloadIcon", "RestartAltIcon", "axios", "jsxDEV", "_jsxDEV", "API_URL", "REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "_s", "_gridData$find", "processedData", "map", "row", "REMARKS", "gridData", "setGridData", "index", "id", "isTotal", "NO", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "window", "error", "console", "handleCleanup", "delete", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "undefined", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "length", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "columns", "col", "hasOwnProperty", "flex", "min<PERSON><PERSON><PERSON>", "renderCell", "cursor", "textDecoration", "value", "fontWeight", "toFixed", "split", "Math", "floor", "Boolean", "display", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "width", "overflow", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "getRowClassName", "isCellEditable", "experimentalFeatures", "newEditingApi", "backgroundColor", "some", "component", "align", "label", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "dividers", "disablePadding", "primary", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Button,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Chip,\r\n  Snackbar,\r\n  Alert,\r\n  Menu,\r\n  MenuItem,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  List,\r\n  ListItem,\r\n  ListItemButton,\r\n  ListItemText\r\n} from '@mui/material';\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport DownloadIcon from '@mui/icons-material/Download';\r\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\r\nimport axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:5000/api';\r\n\r\n// 预定义的REMARKS选项\r\nconst REMARKS_OPTIONS = [\r\n  \"MAXCHECK ADVANCE\",\r\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\r\n  \"MAXCHECK BASIC\",\r\n  \"CBU CAR\",\r\n  \"MAXCHECK PLUS\",\r\n  \"MAXCHECK STANDARD\"\r\n];\r\n\r\nconst ResultDisplay = ({ data, fileId, onReset, onError }) => {\r\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\r\n  const processedData = data.map(row => {\r\n    // 无论后端是否传来REMARKS值，都设置为空字符串\r\n    return { ...row, REMARKS: '' };\r\n  });\r\n\r\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\r\n    ...row,\r\n    id: index,\r\n    isTotal: row.NO === 'TOTAL'\r\n  })));\r\n  \r\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\r\n  const [remarksDialog, setRemarksDialog] = useState({\r\n    open: false,\r\n    rowId: null,\r\n    currentValue: ''\r\n  });\r\n\r\n  const handleDownload = async () => {\r\n    try {\r\n      // 使用浏览器的下载功能\r\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\r\n    } catch (error) {\r\n      console.error('下载文件出错:', error);\r\n      onError('下载文件失败，请重试');\r\n    }\r\n  };\r\n  \r\n  const handleCleanup = async () => {\r\n    try {\r\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\r\n    } catch (error) {\r\n      console.error('清理文件出错:', error);\r\n    }\r\n    \r\n    onReset();\r\n  };\r\n\r\n  const handleCellEdit = (params) => {\r\n    // 阻止总计行被编辑\r\n    if (params.row.NO === 'TOTAL') {\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const processRowUpdate = (newRow) => {\r\n    // 更新行数据\r\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\r\n    \r\n    // 重新计算总计\r\n    if (newRow.COMMISSION !== undefined) {\r\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\r\n      if (totalRow) {\r\n        const newTotal = updatedData\r\n          .filter(row => row.NO !== 'TOTAL')\r\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\r\n        \r\n        totalRow.COMMISSION = newTotal;\r\n      }\r\n    }\r\n    \r\n    setGridData(updatedData);\r\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\r\n    return newRow;\r\n  };\r\n\r\n  const onProcessRowUpdateError = (error) => {\r\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\r\n  };\r\n\r\n  const handleCloseSnackbar = () => {\r\n    setSnackbar(prev => ({ ...prev, open: false }));\r\n  };\r\n\r\n  // 打开REMARKS选择对话框\r\n  const openRemarksDialog = (rowId, currentValue) => {\r\n    setRemarksDialog({\r\n      open: true,\r\n      rowId,\r\n      currentValue\r\n    });\r\n  };\r\n\r\n  // 关闭REMARKS选择对话框\r\n  const closeRemarksDialog = () => {\r\n    setRemarksDialog({\r\n      ...remarksDialog,\r\n      open: false\r\n    });\r\n  };\r\n\r\n  // 选择REMARKS选项\r\n  const selectRemarkOption = (option) => {\r\n    const { rowId } = remarksDialog;\r\n    if (rowId !== null) {\r\n      const updatedData = gridData.map(row => {\r\n        if (row.id === rowId) {\r\n          return { ...row, REMARKS: option };\r\n        }\r\n        return row;\r\n      });\r\n      setGridData(updatedData);\r\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\r\n    }\r\n    closeRemarksDialog();\r\n  };\r\n  \r\n  // 如果数据为空\r\n  if (!gridData || gridData.length === 0) {\r\n    return (\r\n      <Box sx={{ textAlign: 'center', py: 3 }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">\r\n          没有找到数据\r\n        </Typography>\r\n        <Button \r\n          variant=\"contained\" \r\n          onClick={onReset}\r\n          sx={{ mt: 2 }}\r\n        >\r\n          重新开始\r\n        </Button>\r\n      </Box>\r\n    );\r\n  }\r\n  \r\n  // 定义列的显示顺序和标题\r\n  const columnOrder = [\r\n    { field: 'NO', headerName: 'NO', editable: true },\r\n    { field: 'DATE', headerName: 'DATE', editable: true },\r\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true },\r\n    { field: 'RO NO', headerName: 'RO NO', editable: true },\r\n    { field: 'KM', headerName: 'KM', editable: true },\r\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false },  // 新添加的REMARKS列\r\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true },\r\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true }\r\n  ];\r\n  \r\n  // 过滤和排序列\r\n  const columns = columnOrder.map(col => {\r\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\r\n      // 如果COMMISSION列不存在，跳过\r\n      return null;\r\n    }\r\n    \r\n    // 特殊处理REMARKS列\r\n    if (col.field === 'REMARKS') {\r\n      return {\r\n        field: col.field,\r\n        headerName: col.headerName,\r\n        flex: 1.5,\r\n        minWidth: 180,\r\n        editable: false,\r\n        renderCell: (params) => {\r\n          // 总计行不显示REMARKS选项\r\n          if (params.row.NO === 'TOTAL') {\r\n            return '';\r\n          }\r\n          \r\n          return (\r\n            <Box \r\n              sx={{ \r\n                cursor: 'pointer',\r\n                '&:hover': {\r\n                  textDecoration: 'underline',\r\n                  color: 'primary.main'\r\n                }\r\n              }}\r\n              onClick={() => openRemarksDialog(params.row.id, params.value || '')}\r\n            >\r\n              {params.value || '请选择'}\r\n            </Box>\r\n          );\r\n        }\r\n      };\r\n    }\r\n    \r\n    return {\r\n      field: col.field,\r\n      headerName: col.headerName,\r\n      flex: 1,\r\n      minWidth: 120,\r\n      editable: col.editable,\r\n      renderCell: (params) => {\r\n        // 特殊处理总计行\r\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\r\n          return (\r\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\r\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\r\n            </Typography>\r\n          );\r\n        }\r\n        \r\n        // 处理日期格式\r\n        if (col.field === 'DATE' && params.value) {\r\n          return params.value.split('T')[0]; // 只显示日期部分\r\n        }\r\n        \r\n        // NO列不显示浮点数\r\n        if (col.field === 'NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // RO NO列不显示浮点数\r\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // KM列不显示浮点数\r\n        if (col.field === 'KM' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\r\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\r\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\r\n        }\r\n        \r\n        // COMMISSION(AMOUNT)列保留2位小数\r\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\r\n          return params.value.toFixed(2);\r\n        }\r\n        \r\n        // 其他数字格式\r\n        if (typeof params.value === 'number') {\r\n          return params.value;\r\n        }\r\n        \r\n        return params.value;\r\n      }\r\n    };\r\n  }).filter(Boolean); // 过滤掉null值\r\n  \r\n  return (\r\n    <Box>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          处理结果\r\n        </Typography>\r\n        \r\n        <Box>\r\n          <Button \r\n            variant=\"contained\" \r\n            startIcon={<DownloadIcon />}\r\n            onClick={handleDownload}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            下载Excel\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"outlined\" \r\n            startIcon={<RestartAltIcon />}\r\n            onClick={handleCleanup}\r\n          >\r\n            重新开始\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n      \r\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n        <Box sx={{ height: 400, width: '100%' }}>\r\n          <DataGrid\r\n            rows={gridData}\r\n            columns={columns}\r\n            pageSize={10}\r\n            rowsPerPageOptions={[10, 25, 50]}\r\n            disableSelectionOnClick\r\n            getRowClassName={(params) => params.row.isTotal ? 'total-row' : ''}\r\n            isCellEditable={handleCellEdit}\r\n            processRowUpdate={processRowUpdate}\r\n            onProcessRowUpdateError={onProcessRowUpdateError}\r\n            experimentalFeatures={{ newEditingApi: true }}\r\n            sx={{\r\n              '& .total-row': {\r\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\r\n                fontWeight: 'bold',\r\n              },\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n      \r\n      {gridData.some(row => 'COMMISSION' in row) && (\r\n        <Box sx={{ mt: 3 }}>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            佣金计算结果\r\n          </Typography>\r\n          \r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>项目</TableCell>\r\n                  <TableCell align=\"right\">金额 (RM)</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell>总佣金</TableCell>\r\n                  <TableCell align=\"right\">\r\n                    <Chip \r\n                      label={gridData.find(row => row.NO === 'TOTAL')?.COMMISSION.toFixed(2) || '0.00'} \r\n                      color=\"primary\" \r\n                      variant=\"outlined\"\r\n                    />\r\n                  </TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n        </Box>\r\n      )}\r\n      \r\n      {/* REMARKS选择对话框 */}\r\n      <Dialog \r\n        open={remarksDialog.open} \r\n        onClose={closeRemarksDialog}\r\n        fullWidth\r\n        maxWidth=\"xs\"\r\n      >\r\n        <DialogTitle>选择REMARKS</DialogTitle>\r\n        <DialogContent dividers>\r\n          <List>\r\n            {REMARKS_OPTIONS.map((option) => (\r\n              <ListItem key={option} disablePadding>\r\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\r\n                  <ListItemText primary={option} />\r\n                </ListItemButton>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeRemarksDialog}>取消</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      \r\n      <Snackbar\r\n        open={snackbar.open}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSnackbar}\r\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n      >\r\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\r\n          {snackbar.message}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,QACP,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,eAAe,GAAG,CACtB,kBAAkB,EAClB,iCAAiC,EACjC,gBAAgB,EAChB,SAAS,EACT,eAAe,EACf,mBAAmB,CACpB;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC5D;EACA,MAAMC,aAAa,GAAGN,IAAI,CAACO,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE;IAAG,CAAC;EAChC,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAACuC,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEI,KAAK,MAAM;IAC1E,GAAGJ,GAAG;IACNK,EAAE,EAAED,KAAK;IACTE,OAAO,EAAEN,GAAG,CAACO,EAAE,KAAK;EACtB,CAAC,CAAC,CAAC,CAAC;EAEJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC;IAAEmD,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC;IACjDmD,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACAC,MAAM,CAACR,IAAI,CAAC,GAAGrB,OAAO,aAAaI,MAAM,EAAE,EAAE,QAAQ,CAAC;IACxD,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxB,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMnC,KAAK,CAACoC,MAAM,CAAC,GAAGjC,OAAO,YAAYI,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAzB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM6B,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACxB,GAAG,CAACO,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMkB,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACA,MAAMC,WAAW,GAAGzB,QAAQ,CAACH,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACK,EAAE,KAAKqB,MAAM,CAACrB,EAAE,GAAGqB,MAAM,GAAG1B,GAAI,CAAC;;IAE9E;IACA,IAAI0B,MAAM,CAACE,UAAU,KAAKC,SAAS,EAAE;MACnC,MAAMC,QAAQ,GAAGH,WAAW,CAACI,IAAI,CAAC/B,GAAG,IAAIA,GAAG,CAACO,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAIuB,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGL,WAAW,CACzBM,MAAM,CAACjC,GAAG,IAAIA,GAAG,CAACO,EAAE,KAAK,OAAO,CAAC,CACjC2B,MAAM,CAAC,CAACC,GAAG,EAAEnC,GAAG,KAAKmC,GAAG,IAAInC,GAAG,CAAC4B,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDE,QAAQ,CAACF,UAAU,GAAGI,QAAQ;MAChC;IACF;IAEA7B,WAAW,CAACwB,WAAW,CAAC;IACxBlB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOc,MAAM;EACf,CAAC;EAED,MAAMU,uBAAuB,GAAIjB,KAAK,IAAK;IACzCV,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASQ,KAAK,CAACR,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMyB,mBAAmB,GAAGA,CAAA,KAAM;IAChC5B,WAAW,CAAC6B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5B,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAGA,CAACxB,KAAK,EAAEC,YAAY,KAAK;IACjDF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1B,gBAAgB,CAAC;MACf,GAAGD,aAAa;MAChBH,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+B,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAM;MAAE3B;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMY,WAAW,GAAGzB,QAAQ,CAACH,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACK,EAAE,KAAKU,KAAK,EAAE;UACpB,OAAO;YAAE,GAAGf,GAAG;YAAEC,OAAO,EAAEyC;UAAO,CAAC;QACpC;QACA,OAAO1C,GAAG;MACZ,CAAC,CAAC;MACFG,WAAW,CAACwB,WAAW,CAAC;MACxBlB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACA4B,kBAAkB,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,IAAI,CAACtC,QAAQ,IAAIA,QAAQ,CAACyC,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEvD,OAAA,CAAC5B,GAAG;MAACoF,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtC3D,OAAA,CAAC3B,UAAU;QAACuF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC1B,MAAM;QACLsF,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAE5D,OAAQ;QACjBkD,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAAG;EAC/D;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9D;;EAED;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAACzD,GAAG,CAAC8D,GAAG,IAAI;IACrC,IAAI,CAAC3D,QAAQ,CAAC,CAAC,CAAC,CAAC4D,cAAc,CAACD,GAAG,CAACJ,KAAK,CAAC,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,IAAII,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;MACnG;MACA,OAAO,IAAI;IACb;;IAEA;IACA,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEI,GAAG,CAACJ,KAAK;QAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;QAC1BK,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,GAAG;QACbL,QAAQ,EAAE,KAAK;QACfM,UAAU,EAAGzC,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAACxB,GAAG,CAACO,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;UAEA,oBACEnB,OAAA,CAAC5B,GAAG;YACFoF,EAAE,EAAE;cACFsB,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTC,cAAc,EAAE,WAAW;gBAC3BlB,KAAK,EAAE;cACT;YACF,CAAE;YACFK,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAACf,MAAM,CAACxB,GAAG,CAACK,EAAE,EAAEmB,MAAM,CAAC4C,KAAK,IAAI,EAAE,CAAE;YAAArB,QAAA,EAEnEvB,MAAM,CAAC4C,KAAK,IAAI;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAEV;MACF,CAAC;IACH;IAEA,OAAO;MACLI,KAAK,EAAEI,GAAG,CAACJ,KAAK;MAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;MAC1BK,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbL,QAAQ,EAAEE,GAAG,CAACF,QAAQ;MACtBM,UAAU,EAAGzC,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAACxB,GAAG,CAACO,EAAE,KAAK,OAAO,IAAIsD,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACErE,OAAA,CAAC3B,UAAU;YAACuF,OAAO,EAAC,OAAO;YAACqB,UAAU,EAAC,MAAM;YAACpB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOvB,MAAM,CAAC4C,KAAK,KAAK,QAAQ,GAAG5C,MAAM,CAAC4C,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG9C,MAAM,CAAC4C;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIQ,GAAG,CAACJ,KAAK,KAAK,MAAM,IAAIjC,MAAM,CAAC4C,KAAK,EAAE;UACxC,OAAO5C,MAAM,CAAC4C,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAIV,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAOjC,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOI,IAAI,CAACC,KAAK,CAACjD,MAAM,CAAC4C,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,OAAO,IAAI,OAAOjC,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOI,IAAI,CAACC,KAAK,CAACjD,MAAM,CAAC4C,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAOjC,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOI,IAAI,CAACC,KAAK,CAACjD,MAAM,CAAC4C,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,UAAU,IAAI,OAAOjC,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO5C,MAAM,CAAC4C,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG5C,MAAM,CAAC4C,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG9C,MAAM,CAAC4C,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAIT,GAAG,CAACJ,KAAK,KAAK,YAAY,IAAI,OAAOjC,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO5C,MAAM,CAAC4C,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAO9C,MAAM,CAAC4C,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO5C,MAAM,CAAC4C,KAAK;QACrB;QAEA,OAAO5C,MAAM,CAAC4C,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACnC,MAAM,CAACyC,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACEtF,OAAA,CAAC5B,GAAG;IAAAuF,QAAA,gBACF3D,OAAA,CAAC5B,GAAG;MAACoF,EAAE,EAAE;QAAE+B,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,gBACzF3D,OAAA,CAAC3B,UAAU;QAACuF,OAAO,EAAC,IAAI;QAAC+B,YAAY;QAAAhC,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjE,OAAA,CAAC5B,GAAG;QAAAuF,QAAA,gBACF3D,OAAA,CAAC1B,MAAM;UACLsF,OAAO,EAAC,WAAW;UACnBgC,SAAS,eAAE5F,OAAA,CAACJ,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAErC,cAAe;UACxB2B,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE,CAAE;UAAAlC,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETjE,OAAA,CAAC1B,MAAM;UACLsF,OAAO,EAAC,UAAU;UAClBgC,SAAS,eAAE5F,OAAA,CAACH,cAAc;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAEjC,aAAc;UAAA0B,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA,CAACzB,KAAK;MAACiF,EAAE,EAAE;QAAEsC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAApC,QAAA,eAC/C3D,OAAA,CAAC5B,GAAG;QAACoF,EAAE,EAAE;UAAEwC,MAAM,EAAE,GAAG;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAnC,QAAA,eACtC3D,OAAA,CAACL,QAAQ;UACPsG,IAAI,EAAEnF,QAAS;UACf0D,OAAO,EAAEA,OAAQ;UACjB0B,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,eAAe,EAAGjE,MAAM,IAAKA,MAAM,CAACxB,GAAG,CAACM,OAAO,GAAG,WAAW,GAAG,EAAG;UACnEoF,cAAc,EAAEnE,cAAe;UAC/BE,gBAAgB,EAAEA,gBAAiB;UACnCW,uBAAuB,EAAEA,uBAAwB;UACjDuD,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9ChD,EAAE,EAAE;YACF,cAAc,EAAE;cACdiD,eAAe,EAAE,0BAA0B;cAC3CxB,UAAU,EAAE;YACd;UACF;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEPnD,QAAQ,CAAC4F,IAAI,CAAC9F,GAAG,IAAI,YAAY,IAAIA,GAAG,CAAC,iBACxCZ,OAAA,CAAC5B,GAAG;MAACoF,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACjB3D,OAAA,CAAC3B,UAAU;QAACuF,OAAO,EAAC,WAAW;QAAC+B,YAAY;QAAAhC,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjE,OAAA,CAACrB,cAAc;QAACgI,SAAS,EAAEpI,KAAM;QAAAoF,QAAA,eAC/B3D,OAAA,CAACxB,KAAK;UAAAmF,QAAA,gBACJ3D,OAAA,CAACpB,SAAS;YAAA+E,QAAA,eACR3D,OAAA,CAACnB,QAAQ;cAAA8E,QAAA,gBACP3D,OAAA,CAACtB,SAAS;gBAAAiF,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzBjE,OAAA,CAACtB,SAAS;gBAACkI,KAAK,EAAC,OAAO;gBAAAjD,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZjE,OAAA,CAACvB,SAAS;YAAAkF,QAAA,eACR3D,OAAA,CAACnB,QAAQ;cAAA8E,QAAA,gBACP3D,OAAA,CAACtB,SAAS;gBAAAiF,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1BjE,OAAA,CAACtB,SAAS;gBAACkI,KAAK,EAAC,OAAO;gBAAAjD,QAAA,eACtB3D,OAAA,CAAClB,IAAI;kBACH+H,KAAK,EAAE,EAAApG,cAAA,GAAAK,QAAQ,CAAC6B,IAAI,CAAC/B,GAAG,IAAIA,GAAG,CAACO,EAAE,KAAK,OAAO,CAAC,cAAAV,cAAA,uBAAxCA,cAAA,CAA0C+B,UAAU,CAAC0C,OAAO,CAAC,CAAC,CAAC,KAAI,MAAO;kBACjFrB,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDjE,OAAA,CAACb,MAAM;MACLmC,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzBwF,OAAO,EAAE1D,kBAAmB;MAC5B2D,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAArD,QAAA,gBAEb3D,OAAA,CAACZ,WAAW;QAAAuE,QAAA,EAAC;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACpCjE,OAAA,CAACX,aAAa;QAAC4H,QAAQ;QAAAtD,QAAA,eACrB3D,OAAA,CAACT,IAAI;UAAAoE,QAAA,EACFzD,eAAe,CAACS,GAAG,CAAE2C,MAAM,iBAC1BtD,OAAA,CAACR,QAAQ;YAAc0H,cAAc;YAAAvD,QAAA,eACnC3D,OAAA,CAACP,cAAc;cAACyE,OAAO,EAAEA,CAAA,KAAMb,kBAAkB,CAACC,MAAM,CAAE;cAAAK,QAAA,eACxD3D,OAAA,CAACN,YAAY;gBAACyH,OAAO,EAAE7D;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAHJX,MAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBjE,OAAA,CAACV,aAAa;QAAAqE,QAAA,eACZ3D,OAAA,CAAC1B,MAAM;UAAC4F,OAAO,EAAEd,kBAAmB;UAAAO,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETjE,OAAA,CAACjB,QAAQ;MACPuC,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB8F,gBAAgB,EAAE,IAAK;MACvBN,OAAO,EAAE7D,mBAAoB;MAC7BoE,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAA5D,QAAA,eAE1D3D,OAAA,CAAChB,KAAK;QAAC8H,OAAO,EAAE7D,mBAAoB;QAACzB,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAAmC,QAAA,EAC9DvC,QAAQ,CAACG;MAAO;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACzD,EAAA,CAhWIL,aAAa;AAAAqH,EAAA,GAAbrH,aAAa;AAkWnB,eAAeA,aAAa;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}