{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel, Grid } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c2 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01',\n            'FEB': '02',\n            'MAR': '03',\n            'ARP': '04',\n            'MAY': '05',\n            'JUNE': '06',\n            'JULY': '07',\n            'AUG': '08',\n            'SEP': '09',\n            'OCT': '10',\n            'NOV': '11',\n            'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            console.log(`解析工作表名称: ${selectedWorksheet} -> ${year}-${monthNumber}`);\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) {\n          // 确保更新时保持原始数据类型\n          const updatedRow = {\n            ...row\n          };\n          Object.keys(newRow).forEach(key => {\n            if (newRow[key] !== undefined) {\n              updatedRow[key] = newRow[key];\n            }\n          });\n          return updatedRow;\n        }\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 991,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 990,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1009,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1008,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 17\n              }, this), \"\\u6570\\u636E\\u641C\\u7D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: searchColumn,\n                  label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n                  onChange: e => setSearchColumn(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u5168\\u90E8\\u5217\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"NO\",\n                    children: \"NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DATE\",\n                    children: \"DATE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"VEHICLE NO\",\n                    children: \"VEHICLE NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"RO NO\",\n                    children: \"RO NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"KM\",\n                    children: \"KM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1077,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"REMARKS\",\n                    children: \"REMARKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1078,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"MAXCHECK\",\n                    children: \"HOURS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMMISSION\",\n                    children: \"AMOUNT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1080,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n                value: searchText,\n                onChange: e => setSearchText(e.target.value),\n                sx: {\n                  flexGrow: 1,\n                  minWidth: 200\n                },\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1093,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setSearchText(''),\n                      edge: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1103,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 17\n              }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1058,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 17\n              }, this), \"\\u64CD\\u4F5C\\u9009\\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"success\",\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1129,\n                  columnNumber: 30\n                }, this),\n                onClick: handleDownload,\n                children: \"\\u4E0B\\u8F7DExcel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 53\n                }, this) : /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 102\n                }, this),\n                onClick: generateDocument,\n                disabled: isGeneratingDocument,\n                children: isGeneratingDocument ? '生成中...' : '生成Excel文档'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 30\n                }, this),\n                onClick: handleCleanup,\n                children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1055,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1054,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          paginationModel: paginationModel,\n          onPaginationModelChange: setPaginationModel,\n          pageSizeOptions: [25, 50, 100],\n          pagination: true,\n          paginationMode: \"client\",\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          disableVirtualization: false,\n          rowHeight: 48,\n          density: \"compact\",\n          rowBuffer: 5,\n          columnBuffer: 2,\n          disableColumnMenu: true,\n          disableColumnFilter: true,\n          disableColumnSelector: true,\n          disableDensitySelector: true,\n          hideFooterSelectedRowCount: true,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            // 保持数字字段的正确类型\n            const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n            numericFields.forEach(field => {\n              if (newRow[field] !== undefined && newRow[field] !== null && newRow[field] !== '') {\n                if (typeof newRow[field] === 'string') {\n                  const numValue = Number(newRow[field]);\n                  if (!isNaN(numValue)) {\n                    newRow[field] = numValue;\n                  }\n                }\n              }\n            });\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, `datagrid-${paginationModel.pageSize}-${memoGridData.length}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1161,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1273,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1300,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1331,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1326,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1321,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1372,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1349,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1006,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"8Dlq3lHDgcKFRjPOQF+IsoHVFTU=\");\n_c3 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RemarkChip$React.memo\");\n$RefreshReg$(_c2, \"RemarkChip\");\n$RefreshReg$(_c3, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "Grid", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "SettingsIcon", "axios", "API_URL", "FixedSizeList", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "RemarkChip", "_s", "memo", "_c", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "getSelectedMonth", "commissionState", "selectedWorksheet", "match", "monthName", "year", "monthMap", "monthNumber", "toUpperCase", "console", "log", "error", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "timer", "setPaginationModel", "prev", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "setItem", "toString", "originalData", "setOriginalData", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "COMMISSION", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "totalValue", "updatedData", "updatedRow", "Object", "keys", "for<PERSON>ach", "key", "onProcessRowUpdateError", "message", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "noCounter", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "newData", "splice", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "params", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "backgroundColor", "startIcon", "fontWeight", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "values", "some", "cellValue", "memoGridData", "dataLength", "textAlign", "py", "mt", "mb", "justifyContent", "flexWrap", "direction", "spacing", "icon", "container", "xs", "md", "sm", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "disabled", "rows", "onPaginationModelChange", "pageSizeOptions", "pagination", "paginationMode", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "disableVirtualization", "rowHeight", "density", "<PERSON><PERSON><PERSON><PERSON>", "columnBuffer", "disableColumnMenu", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnSelector", "disableDensitySelector", "hideFooterSelectedRowCount", "getRowClassName", "isCellEditable", "colDef", "numericFields", "numValue", "padding", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "type", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel,\n  Grid\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\n\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01', 'FEB': '02', 'MAR': '03', 'ARP': '04',\n            'MAY': '05', 'JUNE': '06', 'JULY': '07', 'AUG': '08',\n            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            console.log(`解析工作表名称: ${selectedWorksheet} -> ${year}-${monthNumber}`);\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) {\n          // 确保更新时保持原始数据类型\n          const updatedRow = { ...row };\n          Object.keys(newRow).forEach(key => {\n            if (newRow[key] !== undefined) {\n              updatedRow[key] = newRow[key];\n            }\n          });\n          return updatedRow;\n        }\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 处理结果概览 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 数据搜索和操作选项 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={3}>\n            {/* 数据搜索 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SearchIcon sx={{ color: 'primary.main' }} />\n                数据搜索\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n                <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n                  <InputLabel>搜索范围</InputLabel>\n                  <Select\n                    value={searchColumn}\n                    label=\"搜索范围\"\n                    onChange={(e) => setSearchColumn(e.target.value)}\n                  >\n                    <MenuItem value=\"all\">全部列</MenuItem>\n                    <MenuItem value=\"NO\">NO</MenuItem>\n                    <MenuItem value=\"DATE\">DATE</MenuItem>\n                    <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                    <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                    <MenuItem value=\"KM\">KM</MenuItem>\n                    <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                    <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                    <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n                  </Select>\n                </FormControl>\n\n                <TextField\n                  size=\"small\"\n                  placeholder=\"输入搜索内容...\"\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  sx={{ flexGrow: 1, minWidth: 200 }}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    ),\n                    endAdornment: searchText && (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => setSearchText('')}\n                          edge=\"end\"\n                        >\n                          <ClearIcon />\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {searchText && (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n                  </Typography>\n                )}\n              </Stack>\n            </Grid>\n\n            {/* 操作选项 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SettingsIcon sx={{ color: 'primary.main' }} />\n                操作选项\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>\n                <Button\n                  variant=\"contained\"\n                  color=\"success\"\n                  startIcon={<DownloadIcon />}\n                  onClick={handleDownload}\n                >\n                  下载Excel\n                </Button>\n\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <TableViewIcon />}\n                  onClick={generateDocument}\n                  disabled={isGeneratingDocument}\n                >\n                  {isGeneratingDocument ? '生成中...' : '生成Excel文档'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<RestartAltIcon />}\n                  onClick={handleCleanup}\n                >\n                  重新开始\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              key={`datagrid-${paginationModel.pageSize}-${memoGridData.length}`}\n              rows={memoGridData}\n              columns={columns}\n              paginationModel={paginationModel}\n              onPaginationModelChange={setPaginationModel}\n              pageSizeOptions={[25, 50, 100]}\n              pagination\n              paginationMode=\"client\"\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              disableVirtualization={false}\n              rowHeight={48}\n              density=\"compact\"\n              rowBuffer={5}\n              columnBuffer={2}\n              disableColumnMenu\n              disableColumnFilter\n              disableColumnSelector\n              disableDensitySelector\n              hideFooterSelectedRowCount\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                // 保持数字字段的正确类型\n                const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n\n                numericFields.forEach(field => {\n                  if (newRow[field] !== undefined && newRow[field] !== null && newRow[field] !== '') {\n                    if (typeof newRow[field] === 'string') {\n                      const numValue = Number(newRow[field]);\n                      if (!isNaN(numValue)) {\n                        newRow[field] = numValue;\n                      }\n                    }\n                  }\n                });\n\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,QACC,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;;AAG5C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAID;AACA,MAAMC,UAAU,gBAAAC,EAAA,cAAG/D,KAAK,CAACgE,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC;IAErCkE,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACAlE,SAAS,CAAC,MAAM;IACdqE,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEf,OAAA,CAAC3C,MAAM;IAEL6D,OAAO,EAAEG,WAAY;IACrBE,OAAO,EAAEJ,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDO,KAAK,EAAC,SAAS;IACfC,IAAI,EAAC,OAAO;IACZC,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,sBAAsB;MAClCC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EAEDlB,OAAO,CAACH,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAACC,GAAA,GA5CG/B,UAAU;AA8ChB,MAAMgC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,MAAM;IACzD,MAAM4G,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGhD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACmH,eAAe,EAAEC,kBAAkB,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAMuH,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMC,eAAe,GAAGT,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACnF,MAAMW,iBAAiB,GAAGD,eAAe,CAACC,iBAAiB;MAE3D,IAAIA,iBAAiB,EAAE;QACrB;QACA,MAAMC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAAC,eAAe,CAAC;QACtD,IAAIA,KAAK,EAAE;UACT,MAAM,GAAGC,SAAS,EAAEC,IAAI,CAAC,GAAGF,KAAK;UACjC,MAAMG,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAClD,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YACpD,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAChD,CAAC;UACD,MAAMC,WAAW,GAAGD,QAAQ,CAACF,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC;UACrD,IAAID,WAAW,EAAE;YACfE,OAAO,CAACC,GAAG,CAAC,YAAYR,iBAAiB,OAAOG,IAAI,IAAIE,WAAW,EAAE,CAAC;YACtE,OAAO,GAAGF,IAAI,IAAIE,WAAW,EAAE;UACjC;QACF;MACF;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;;IAEA;IACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,OAAO,GAAGD,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9E,CAAC;EAED,MAAMC,aAAa,GAAGlB,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC8I,YAAY,EAAEC,eAAe,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+I,KAAK,GAAGrF,UAAU,CAAC,MAAM;MAC7BkF,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMhF,YAAY,CAACsF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACAzI,SAAS,CAAC,MAAM;IACdgJ,kBAAkB,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAACP,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGrJ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsJ,UAAU,EAAEC,aAAa,CAAC,GAAGvJ,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwJ,eAAe,EAAEP,kBAAkB,CAAC,GAAGjJ,QAAQ,CAAC,MAAM;IAC3D,MAAMyJ,KAAK,GAAG5C,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLqC,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACAxJ,SAAS,CAAC,MAAM;IACd4G,YAAY,CAAC+C,OAAO,CAAC,kBAAkB,EAAEJ,eAAe,CAACE,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC;IAC7E7B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuB,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAI9B;EACA,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAG/J,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd4G,YAAY,CAAC+C,OAAO,CAAC,gBAAgB,EAAE7C,IAAI,CAACiD,SAAS,CAACtD,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMuD,aAAa,GAAG,CAACnE,IAAI,IAAI,EAAE,EAAEoE,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxK,QAAQ,CAAC,MAAM;IAC7CgI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C/B,aAAa,GAAG,IAAIA,aAAa,CAACuE,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIvE,aAAa,IAAIA,aAAa,CAACuE,MAAM,GAAG,CAAC,EAAE;MAC7CzC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMyC,aAAa,GAAGxE,aAAa,CAACgE,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKK,SAAS,EAAE;UAC9BR,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKM,SAAS,EAAE;UACvCR,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMS,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAC1D,GAAGV,GAAG;QACNW,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACA1C,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAM2C,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;MAC1D,GAAGV,GAAG;MACNW,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG9K,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM+K,iBAAiB,GAAG/K,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMgL,gBAAgB,GAAGhL,MAAM,CAAC,IAAI,CAAC;;EAIrC;EACA,MAAMiL,UAAU,GAAItF,IAAI,IAAKA,IAAI,CAACoE,GAAG,CAACC,GAAG,KAAK;IAC5CW,EAAE,EAAEX,GAAG,CAACW,EAAE;IACVE,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVV,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCgB,UAAU,EAAElB,GAAG,CAACkB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACApL,SAAS,CAAC,MAAM;IACd,IAAIkG,YAAY,IAAIoE,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMa,OAAO,GAAGvE,IAAI,CAACiD,SAAS,CAACoB,UAAU,CAACb,QAAQ,CAAC,CAAC;MACpD,MAAMgB,WAAW,GAAGN,mBAAmB,CAACO,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIH,gBAAgB,CAACK,OAAO,EAAE;UAC5B9H,YAAY,CAACyH,gBAAgB,CAACK,OAAO,CAAC;QACxC;QACAL,gBAAgB,CAACK,OAAO,GAAG7H,UAAU,CAAC,MAAM;UAC1CsH,mBAAmB,CAACO,OAAO,GAAGF,OAAO;UACrCJ,iBAAiB,CAACM,OAAO,GAAGpD,IAAI,CAACD,GAAG,CAAC,CAAC;UACtChC,YAAY,CAAC,CAAC,GAAGoE,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIY,gBAAgB,CAACK,OAAO,EAAE;QAC5B9H,YAAY,CAACyH,gBAAgB,CAACK,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,QAAQ,EAAEpE,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAG1L,QAAQ,CAAC;IACjD2L,IAAI,EAAE,KAAK;IACX1H,KAAK,EAAE,IAAI;IACX2H,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA3L,SAAS,CAAC,MAAM;IACd,IAAI6J,YAAY,CAACW,MAAM,KAAK,CAAC,IAAIF,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACpDzC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsC,QAAQ,CAACE,MAAM,CAAC;MACpDV,eAAe,CAAC,CAAC,GAAGQ,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAET,YAAY,CAAC,CAAC;EAI5B,MAAM+B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI9F,MAAM,IAAIA,MAAM,CAAC+F,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C7F,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM8F,WAAW,GAAG,GAAGhJ,OAAO,aAAagD,MAAM,EAAE;MACnD,MAAMiG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIhE,IAAI,CAAC,CAAC,CAACiE,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjC,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMyG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM5J,KAAK,CAAC6J,MAAM,CAAC,GAAG5J,OAAO,YAAYgD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAlC,OAAO,CAAC,CAAC;EACX,CAAC;;EAID;EACA,MAAM4G,kBAAkB,GAAG1M,WAAW,CAAC,CAAC4F,IAAI,EAAE+G,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGhH,IAAI,IAAIyE,QAAQ,IAAI,EAAE;IACxC,IAAI,CAACwC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClD4C,MAAM,CAAC,CAACC,GAAG,EAAEhD,GAAG,KAAK;MACpB,IAAI0C,UAAU,IAAI1C,GAAG,CAACW,EAAE,KAAK+B,UAAU,CAAC/B,EAAE,EAAE;QAC1C,OAAOqC,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACxB,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO8B,GAAG,IAAIC,MAAM,CAACjD,GAAG,CAACkB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM8C,gBAAgB,GAAGnN,WAAW,CAAE4F,IAAI,IAAK;IAC7C,MAAMwH,QAAQ,GAAGxH,IAAI,CAACyH,IAAI,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIsC,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG1H,IAAI,CAClBmH,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClD4C,MAAM,CAAC,CAACC,GAAG,EAAEhD,GAAG,KAAKgD,GAAG,IAAIC,MAAM,CAACjD,GAAG,CAACkB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DiC,QAAQ,CAACjC,UAAU,GAAGmC,QAAQ;IAChC;IACA,OAAO1H,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2H,2BAA2B,GAAGvN,WAAW,CAC7CiD,QAAQ,CAAE2C,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAAC+C,OAAO,CAAC,eAAe,EAAE7C,IAAI,CAACiD,SAAS,CAAClE,IAAI,CAAC,CAAC;MAC3DkC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnC,IAAI,CAAC2E,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMwF,qBAAqB,GAAGxN,WAAW,CACvCiD,QAAQ,CAAE2C,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvBkC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAAC9B,YAAY,CACf,CAAC;;EAED;EACA,MAAMwH,gBAAgB,GAAGzN,WAAW,CAAE0N,MAAM,IAAK;IAC/CpD,WAAW,CAACtB,IAAI,IAAI;MAClB,IAAI2E,UAAU,GAAGjB,kBAAkB,CAAC1D,IAAI,EAAE0E,MAAM,CAAC;MACjD,MAAME,WAAW,GAAG5E,IAAI,CAACgB,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACW,EAAE,KAAK8C,MAAM,CAAC9C,EAAE,EAAE;UACxB;UACA,MAAMiD,UAAU,GAAG;YAAE,GAAG5D;UAAI,CAAC;UAC7B6D,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;YACjC,IAAIP,MAAM,CAACO,GAAG,CAAC,KAAKxD,SAAS,EAAE;cAC7BoD,UAAU,CAACI,GAAG,CAAC,GAAGP,MAAM,CAACO,GAAG,CAAC;YAC/B;UACF,CAAC,CAAC;UACF,OAAOJ,UAAU;QACnB;QACA,IAAI5D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAEkB,UAAU,EAAEwC;QAAW,CAAC;QACjE,OAAO1D,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAsD,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMU,uBAAuB,GAAIlG,KAAK,IAAK;IACzCF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACmG,OAAO,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGvO,KAAK,CAACG,WAAW,CAAC,CAAC+D,KAAK,EAAE2H,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV1H,KAAK;MACL2H;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,kBAAkB,GAAGrO,WAAW,CAAC,MAAM;IAC3CwL,gBAAgB,CAACxC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPyC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAtC,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiF,kBAAkB,GAAGtO,WAAW,CAAEuO,MAAM,IAAK;IACjD,MAAM;MAAExK;IAAM,CAAC,GAAGwH,aAAa;IAC/B,IAAIxH,KAAK,KAAK,IAAI,EAAE;MAClB;MACAsK,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCnE,WAAW,CAACoE,QAAQ,IAAI;UACtB,IAAId,WAAW,GAAGc,QAAQ,CAAC1E,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACW,EAAE,KAAK7G,KAAK,EAAE;cACpB,IAAIwK,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGtE,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIwE,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAI1F,aAAa,EAAE;kBACjB0F,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAIzF,UAAU,EAAE;kBACdwF,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAACrE,MAAM,GAAG,CAAC,EAAE;kBACvBoE,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG7E,GAAG;kBAAEC,OAAO,EAAEyE,WAAW;kBAAExE,iBAAiB,EAAEwE;gBAAY,CAAC;cACzE;YACF;YACA,OAAO1E,GAAG;UACZ,CAAC,CAAC;UAEF2D,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAnK,UAAU,CAAC,MAAM;UACfqE,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACwD,aAAa,EAAE8C,kBAAkB,EAAElB,gBAAgB,EAAEjE,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAM2F,mBAAmB,GAAG/O,WAAW,CAAC,MAAM;IAC5CkH,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8H,oBAAoB,GAAGhP,WAAW,CAAC,MAAM;IAC7CkH,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiI,YAAY,GAAGjP,WAAW,CAAC,MAAM;IACrC,IAAI+G,SAAS,CAACmI,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC1I,cAAc,CAAC2I,QAAQ,CAACpI,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEzI,iBAAiB,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEjC,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDpH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrBiH,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIxI,cAAc,CAAC2I,QAAQ,CAACpI,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDpH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAAChB,SAAS,EAAEP,cAAc,EAAEwI,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAGpP,WAAW,CAAEuO,MAAM,IAAK;IAC3C9H,iBAAiB,CAACuC,IAAI,IAAIA,IAAI,CAAC+D,MAAM,CAACsC,IAAI,IAAIA,IAAI,KAAKd,MAAM,CAAC,CAAC;IAC/DzG,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuH,eAAe,GAAGtP,WAAW,CAAE4K,EAAE,IAAK;IAC1CN,WAAW,CAACtB,IAAI,IAAI;MAClB,IAAI4E,WAAW,GAAG5E,IAAI,CAACgB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA,IAAIsF,SAAS,GAAG,CAAC;MACjB3B,WAAW,CAACI,OAAO,CAAC/D,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGyE,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF3B,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEF9F,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACoF,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMqC,aAAa,GAAGxP,WAAW,CAAE4K,EAAE,IAAK;IACxCN,WAAW,CAACtB,IAAI,IAAI;MAClB,IAAI4E,WAAW,GAAG5E,IAAI,CAACgB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;;MAEpF;MACA,IAAIsF,SAAS,GAAG,CAAC;MACjB3B,WAAW,CAACI,OAAO,CAAC/D,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGyE,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF3B,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFnK,UAAU,CAAC,MAAM;MACfqE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACoF,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMsC,eAAe,GAAGzP,WAAW,CAAE4K,EAAE,IAAK;IAC1CN,WAAW,CAACtB,IAAI,IAAI;MAClB;MACA,MAAM0G,YAAY,GAAG1G,IAAI,CAAC+D,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAI2E,SAAS,GAAG,CAAC;MACjBG,YAAY,CAAC1B,OAAO,CAAC/D,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGyE,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM3B,WAAW,GAAGT,gBAAgB,CAACuC,YAAY,CAAC;;MAElD;MACAnC,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElC9F,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrB,OAAO6F,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMmC,YAAY,GAAG3P,WAAW,CAAE4P,UAAU,IAAK;IAC/CtF,WAAW,CAACtB,IAAI,IAAI;MAClB;MACA,MAAM6G,WAAW,GAAG7G,IAAI,CAAC8G,SAAS,CAAC7F,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKgF,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO7G,IAAI;;MAEnC;MACA,MAAM+G,KAAK,GAAG7H,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAM+H,UAAU,GAAGhH,IAAI,CAAC6G,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAAClF,EAAE,KAAK,QAAQ,GAAGkF,UAAU,CAAClF,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM4C,MAAM,GAAG;QACb9C,EAAE,EAAEmF,KAAK;QACTjF,EAAE,EAAEmF,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNjG,OAAO,EAAE,EAAE;QACXkG,QAAQ,EAAE,EAAE;QACZjF,UAAU,EAAE,CAAC;QACbhB,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,KAAK;QACfS,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAMwF,OAAO,GAAG,CAAC,GAAGrH,IAAI,CAAC;MACzBqH,OAAO,CAACC,MAAM,CAACT,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEnC,MAAM,CAAC;;MAE1C;MACA,IAAI6B,SAAS,GAAG,CAAC;MACjBc,OAAO,CAACrC,OAAO,CAAC/D,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAGyE,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM3B,WAAW,GAAGT,gBAAgB,CAACkD,OAAO,CAAC;;MAE7C;MACA9C,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElC9F,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;MACpB,OAAO6F,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAM+C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFnJ,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMoJ,YAAY,GAAG,CAACnG,QAAQ,IAAI,EAAE,EACjC0C,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAoG,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC5F,EAAE,KAAK,QAAQ,IAAI,OAAO6F,CAAC,CAAC7F,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO4F,CAAC,CAAC5F,EAAE,GAAG6F,CAAC,CAAC7F,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM8F,OAAO,GAAGJ,YAAY,CAACxG,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbuF,IAAI,EAAEjG,GAAG,CAACiG,IAAI,GAAI,OAAOjG,GAAG,CAACiG,IAAI,KAAK,QAAQ,IAAIjG,GAAG,CAACiG,IAAI,CAACf,QAAQ,CAAC,GAAG,CAAC,GAAGlF,GAAG,CAACiG,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG5G,GAAG,CAACiG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEjG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG6G,IAAI,CAACC,KAAK,CAAC9G,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFkG,EAAE,EAAE,OAAOlG,GAAG,CAACkG,EAAE,KAAK,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAAC9G,GAAG,CAACkG,EAAE,CAAC,GAAGlG,GAAG,CAACkG,EAAE,IAAI,EAAE;QAClEjG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG6G,KAAK,EAAE,OAAO/G,GAAG,CAACmG,QAAQ,KAAK,QAAQ,GACpCnG,GAAG,CAACmG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGnG,GAAG,CAACmG,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAAGhH,GAAG,CAACmG,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAC3EhH,GAAG,CAACmG,QAAQ,IAAI,EAAE;QACpBc,MAAM,EAAE,OAAOjH,GAAG,CAACkB,UAAU,KAAK,QAAQ,GAAGlB,GAAG,CAACkB,UAAU,CAAC8F,OAAO,CAAC,CAAC,CAAC,GAAGhH,GAAG,CAACkB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMgG,WAAW,GAAG,CAAC9G,QAAQ,IAAI,EAAE,EAChC0C,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACkB,UAAU,CAAC,CACpE6B,MAAM,CAAC,CAACC,GAAG,EAAEhD,GAAG,KAAKgD,GAAG,IAAI,OAAOhD,GAAG,CAACkB,UAAU,KAAK,QAAQ,GAAGlB,GAAG,CAACkB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMiG,YAAY,GAAIvL,MAAM,IAAIA,MAAM,CAAC+F,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG/F,MAAM;MAE5F,MAAMwL,QAAQ,GAAG,MAAMzO,KAAK,CAAC0O,IAAI,CAAC,GAAGzO,OAAO,oBAAoB,EAAE;QAChE+C,IAAI,EAAEgL,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCpL,MAAM,EAAEuL,YAAY;QACpB7I,aAAa,EAAEA;MACjB,CAAC,CAAC;MAEF,IAAI8I,QAAQ,CAACzL,IAAI,IAAIyL,QAAQ,CAACzL,IAAI,CAAC2L,KAAK,EAAE;QACxC;QACA,MAAM1F,WAAW,GAAG,GAAGhJ,OAAO,CAACgO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACzL,IAAI,CAAC4L,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA1J,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACAtE,UAAU,CAAC,MAAM;UACf,MAAMgO,MAAM,GAAG1F,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CyF,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG/F,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACoF,MAAM,CAAC;UACjChO,UAAU,CAAC,MAAM;YACfsI,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACkF,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO7J,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRZ,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM0K,kBAAkB,GAAG9R,WAAW,CAAC,CAAC+D,KAAK,EAAEgO,KAAK,KAAK;IACvD3D,iBAAiB,CAACrK,KAAK,EAAEgO,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC3D,iBAAiB,CAAC,CAAC;EAEvB,MAAM4D,OAAO,GAAG9R,OAAO,CAAC,MAAOiG,WAAW,CAAC6D,GAAG,CAACiI,GAAG,IAAI;IACpD,IAAI,EAAE5H,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC6H,cAAc,CAACD,GAAG,CAAC7L,KAAK,CAAC,CAAC,IAAI6L,GAAG,CAAC7L,KAAK,KAAK,SAAS,IAAI6L,GAAG,CAAC7L,KAAK,KAAK,QAAQ,IAAI6L,GAAG,CAAC7L,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAI6L,GAAG,CAAC7L,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAE6L,GAAG,CAAC7L,KAAK;QAChBC,UAAU,EAAE4L,GAAG,CAAC5L,UAAU;QAC1B8L,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV9L,QAAQ,EAAE,KAAK;QACf+L,UAAU,EAAGC,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrI,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIwH,MAAM,CAACrI,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMmI,iBAAiB,GAAGD,MAAM,CAACrI,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEnH,OAAA,CAAC9B,OAAO;cAACsR,KAAK,EAAEF,MAAM,CAACrI,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACsI,KAAK;cAACC,SAAS,EAAC,KAAK;cAAArN,QAAA,eACvErC,OAAA,CAAC/B,IAAI;gBACH0R,KAAK,EAAEJ,iBAAkB;gBACzB/N,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAEgO,OAAO,EAAE,GAAG;kBAAE1N,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAE0M,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAArM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIoN,UAAU,GAAG,MAAM;UACvB,IAAI5O,UAAU,GAAG,KAAK;UAEtB,IAAIqO,MAAM,CAACrI,GAAG,CAACE,iBAAiB,IAAImI,MAAM,CAACrI,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3E0I,UAAU,GAAGP,MAAM,CAACrI,GAAG,CAACE,iBAAiB;YACzClG,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEjB,OAAA,CAACW,UAAU;YACTI,KAAK,EAAEuO,MAAM,CAACrI,GAAG,CAACW,EAAG;YACrB5G,IAAI,EAAE6O,UAAW;YACjB5O,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAE4N;UAAmB;YAAAxM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIwM,GAAG,CAAC7L,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAE6L,GAAG,CAAC7L,KAAK;QAChBC,UAAU,EAAE4L,GAAG,CAAC5L,UAAU;QAC1B8L,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV9L,QAAQ,EAAE,KAAK;QACf+L,UAAU,EAAGC,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrI,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACE9H,OAAA,CAAC7C,GAAG;YAACuE,EAAE,EAAE;cAAEiN,OAAO,EAAE,MAAM;cAAEmB,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAA1N,QAAA,gBAE3DrC,OAAA,CAAC9B,OAAO;cAACsR,KAAK,EAAC,wDAAW;cAAAnN,QAAA,eACxBrC,OAAA,CAACjC,UAAU;gBACT0D,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,SAAS;gBACfN,OAAO,EAAEA,CAAA,KAAMyL,YAAY,CAAC2C,MAAM,CAACrI,GAAG,CAACW,EAAE,CAAE;gBAC3ClG,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTsO,eAAe,EAAE,cAAc;oBAC/BxO,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEFrC,OAAA,CAACP,oBAAoB;kBAACoC,QAAQ,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVzC,OAAA,CAAC9B,OAAO;cAACsR,KAAK,EAAC,0EAAc;cAAAnN,QAAA,eAC3BrC,OAAA,CAACjC,UAAU;gBACT0D,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,OAAO;gBACbN,OAAO,EAAEA,CAAA,KAAMuL,eAAe,CAAC6C,MAAM,CAACrI,GAAG,CAACW,EAAE,CAAE;gBAC9ClG,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTsO,eAAe,EAAE,YAAY;oBAC7BxO,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEFrC,OAAA,CAACN,uBAAuB;kBAACmC,QAAQ,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGT6M,MAAM,CAACrI,GAAG,CAACG,QAAQ,gBAClBpH,OAAA,CAAC3C,MAAM;cAELkE,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZwO,SAAS,eAAEjQ,OAAA,CAACb,QAAQ;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBvB,OAAO,EAAEA,CAAA,KAAMsL,aAAa,CAAC8C,MAAM,CAACrI,GAAG,CAACW,EAAE,CAAE;cAC5ClG,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,MAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETzC,OAAA,CAAC3C,MAAM;cAELkE,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,OAAO;cACbC,IAAI,EAAC,OAAO;cACZwO,SAAS,eAAEjQ,OAAA,CAACd,UAAU;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BvB,OAAO,EAAEA,CAAA,KAAMoL,eAAe,CAACgD,MAAM,CAACrI,GAAG,CAACW,EAAE,CAAE;cAC9ClG,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,QAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGwM,GAAG;MACN3L,QAAQ,EAAEgM,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACrI,GAAG,IAAIqI,MAAM,CAACrI,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIwH,MAAM,CAACrI,GAAG,IAAIqI,MAAM,CAACrI,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAO6H,GAAG,CAAC3L,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD+L,UAAU,EAAGC,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACrI,GAAG,CAACa,EAAE,KAAK,OAAO,IAAImH,GAAG,CAAC7L,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEpD,OAAA,CAAC5C,UAAU;YAACmE,OAAO,EAAC,OAAO;YAAC2O,UAAU,EAAC,MAAM;YAAC1O,KAAK,EAAC,SAAS;YAAAa,QAAA,EAC1D,OAAOiN,MAAM,CAACP,KAAK,KAAK,QAAQ,GAAGO,MAAM,CAACP,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOqB,MAAM,CAACP,KAAK,KAAK,QAAQ,IAAI,CAACoB,KAAK,CAACjG,MAAM,CAACoF,MAAM,CAACP,KAAK,CAAC,CAAC,GAAG7E,MAAM,CAACoF,MAAM,CAACP,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGqB,MAAM,CAACP;UAAK;YAAAzM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI6M,MAAM,CAACrI,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEpH,OAAA,CAAC5C,UAAU;YAACmE,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAE0O,cAAc,EAAE;YAAe,CAAE;YAAA/N,QAAA,EACtFiN,MAAM,CAACP;UAAK;YAAAzM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIwM,GAAG,CAAC7L,KAAK,KAAK,MAAM,IAAIkM,MAAM,CAACP,KAAK,EAAE;UACxC,OAAOO,MAAM,CAACP,KAAK,CAAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIoB,GAAG,CAAC7L,KAAK,KAAK,IAAI,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAACuB,MAAM,CAACP,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7L,KAAK,KAAK,OAAO,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOjB,IAAI,CAACC,KAAK,CAACuB,MAAM,CAACP,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7L,KAAK,KAAK,IAAI,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAACuB,MAAM,CAACP,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7L,KAAK,KAAK,UAAU,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOO,MAAM,CAACP,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGO,MAAM,CAACP,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGqB,MAAM,CAACP,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIgB,GAAG,CAAC7L,KAAK,KAAK,YAAY,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOO,MAAM,CAACP,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIgB,GAAG,CAAC7L,KAAK,KAAK,YAAY,IAAI,OAAOkM,MAAM,CAACP,KAAK,KAAK,QAAQ,IAAI,CAACoB,KAAK,CAACjG,MAAM,CAACoF,MAAM,CAACP,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO7E,MAAM,CAACoF,MAAM,CAACP,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOqB,MAAM,CAACP,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOO,MAAM,CAACP,KAAK;QACrB;QACA,OAAOO,MAAM,CAACP,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAChF,MAAM,CAACsG,OAAO,CAAE,EAAE,CAAClN,WAAW,EAAE2L,kBAAkB,EAAExC,eAAe,EAAEE,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAM6D,gBAAgB,GAAGpT,OAAO,CAAC,MAAM;IACrC,IAAI,CAACwI,mBAAmB,CAACwG,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAO7E,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAMkJ,WAAW,GAAG7K,mBAAmB,CAAC8K,WAAW,CAAC,CAAC;IACrD,OAAO,CAACnJ,QAAQ,IAAI,EAAE,EAAE0C,MAAM,CAAC9C,GAAG,IAAI;MACpC,IAAIrB,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOkF,MAAM,CAAC2F,MAAM,CAACxJ,GAAG,CAAC,CAACyJ,IAAI,CAAC3B,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACpI,QAAQ,CAAC,CAAC,CAAC6J,WAAW,CAAC,CAAC,CAACrE,QAAQ,CAACoE,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMI,SAAS,GAAG1J,GAAG,CAACrB,YAAY,CAAC;QACnC,OAAO+K,SAAS,IAAIA,SAAS,CAAChK,QAAQ,CAAC,CAAC,CAAC6J,WAAW,CAAC,CAAC,CAACrE,QAAQ,CAACoE,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClJ,QAAQ,EAAE3B,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAMgL,YAAY,GAAG1T,OAAO,CAAC,MAAMoT,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACAvT,SAAS,CAAC,MAAM;IACd+H,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrByB,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1B4K,UAAU,EAAED,YAAY,CAACrJ;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjB,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAE2K,YAAY,CAACrJ,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEvH,OAAA,CAAC7C,GAAG;MAACuE,EAAE,EAAE;QAAEoP,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1O,QAAA,gBACtCrC,OAAA,CAAC5C,UAAU;QAACmE,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAa,QAAA,EAAC;MAEhD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzC,OAAA,CAAC3C,MAAM;QACLkE,OAAO,EAAC,WAAW;QACnBL,OAAO,EAAE4B,OAAQ;QACjBpB,EAAE,EAAE;UAAEsP,EAAE,EAAE;QAAE,CAAE;QAAA3O,QAAA,EACf;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEzC,OAAA,CAAC7C,GAAG;IAAAkF,QAAA,gBAEFrC,OAAA,CAAC7B,IAAI;MAACuD,EAAE,EAAE;QAAEuP,EAAE,EAAE;MAAE,CAAE;MAAA5O,QAAA,eAClBrC,OAAA,CAAC5B,WAAW;QAAAiE,QAAA,eACVrC,OAAA,CAAC7C,GAAG;UAACuE,EAAE,EAAE;YAAEiN,OAAO,EAAE,MAAM;YAAEoB,UAAU,EAAE,QAAQ;YAAEmB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAErB,GAAG,EAAE;UAAE,CAAE;UAAAzN,QAAA,gBAC5GrC,OAAA,CAAC7C,GAAG;YAACuE,EAAE,EAAE;cAAEiN,OAAO,EAAE,MAAM;cAAEoB,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAAzN,QAAA,gBACzDrC,OAAA,CAACZ,cAAc;cAACsC,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DzC,OAAA,CAAC7C,GAAG;cAAAkF,QAAA,gBACFrC,OAAA,CAAC5C,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAEwO,UAAU,EAAE,GAAG;kBAAE1O,KAAK,EAAE,cAAc;kBAAEyP,EAAE,EAAE;gBAAI,CAAE;gBAAA5O,QAAA,EAAC;cAElF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAC5C,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAa,QAAA,EAAC;cAE7D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzC,OAAA,CAAC3B,KAAK;YAAC+S,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAAC3P,EAAE,EAAE;cAAEyP,QAAQ,EAAE,MAAM;cAAErB,GAAG,EAAE;YAAE,CAAE;YAAAzN,QAAA,gBAClErC,OAAA,CAAC/B,IAAI;cACHqT,IAAI,eAAEtR,OAAA,CAACX,aAAa;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBkN,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAE7G,MAAM,CAAC9C,GAAG,IAAI,CAACA,GAAG,CAACY,OAAO,IAAI,CAACZ,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,MAAO;cACzF/F,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFzC,OAAA,CAAC/B,IAAI;cACHqT,IAAI,eAAEtR,OAAA,CAACV,cAAc;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBkN,KAAK,EAAE,WAAWjG,kBAAkB,CAACkH,YAAY,CAAC,CAAC3C,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEzM,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACmO,YAAY,IAAI,EAAE,EAAE7G,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,GAAG,CAAC,iBAC1DvH,OAAA,CAAC/B,IAAI;cACHqT,IAAI,eAAEtR,OAAA,CAACd,UAAU;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBkN,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAE7G,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,OAAQ;cACzE/F,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPzC,OAAA,CAAC7B,IAAI;MAACuD,EAAE,EAAE;QAAEuP,EAAE,EAAE;MAAE,CAAE;MAAA5O,QAAA,eAClBrC,OAAA,CAAC5B,WAAW;QAAAiE,QAAA,eACVrC,OAAA,CAACnB,IAAI;UAAC0S,SAAS;UAACF,OAAO,EAAE,CAAE;UAAAhP,QAAA,gBAEzBrC,OAAA,CAACnB,IAAI;YAACwN,IAAI;YAACmF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApP,QAAA,gBACvBrC,OAAA,CAAC5C,UAAU;cAACmE,OAAO,EAAC,WAAW;cAACG,EAAE,EAAE;gBAAEwO,UAAU,EAAE,GAAG;gBAAEe,EAAE,EAAE,CAAC;gBAAEtC,OAAO,EAAE,MAAM;gBAAEoB,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAAzN,QAAA,gBAC5GrC,OAAA,CAACT,UAAU;gBAACmC,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAe;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAAC3B,KAAK;cAAC+S,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAACtB,UAAU,EAAC,QAAQ;cAAA1N,QAAA,gBAC5ErC,OAAA,CAAC1B,WAAW;gBAACmD,IAAI,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAU,QAAA,gBAC9CrC,OAAA,CAACzB,UAAU;kBAAA8D,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7BzC,OAAA,CAACxB,MAAM;kBACLuQ,KAAK,EAAEnJ,YAAa;kBACpB+J,KAAK,EAAC,0BAAM;kBACZgC,QAAQ,EAAGrQ,CAAC,IAAKuE,eAAe,CAACvE,CAAC,CAACsQ,MAAM,CAAC7C,KAAK,CAAE;kBAAA1M,QAAA,gBAEjDrC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,KAAK;oBAAA1M,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,IAAI;oBAAA1M,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,MAAM;oBAAA1M,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtCzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,YAAY;oBAAA1M,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClDzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,OAAO;oBAAA1M,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxCzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,IAAI;oBAAA1M,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,SAAS;oBAAA1M,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,UAAU;oBAAA1M,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC3CzC,OAAA,CAACvB,QAAQ;oBAACsQ,KAAK,EAAC,YAAY;oBAAA1M,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEdzC,OAAA,CAAClC,SAAS;gBACR2D,IAAI,EAAC,OAAO;gBACZoQ,WAAW,EAAC,yCAAW;gBACvB9C,KAAK,EAAEvJ,UAAW;gBAClBmM,QAAQ,EAAGrQ,CAAC,IAAKmE,aAAa,CAACnE,CAAC,CAACsQ,MAAM,CAAC7C,KAAK,CAAE;gBAC/CrN,EAAE,EAAE;kBAAEoQ,QAAQ,EAAE,CAAC;kBAAEnQ,QAAQ,EAAE;gBAAI,CAAE;gBACnCoQ,UAAU,EAAE;kBACVC,cAAc,eACZhS,OAAA,CAACtB,cAAc;oBAACuT,QAAQ,EAAC,OAAO;oBAAA5P,QAAA,eAC9BrC,OAAA,CAACT,UAAU;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBACDyP,YAAY,EAAE1M,UAAU,iBACtBxF,OAAA,CAACtB,cAAc;oBAACuT,QAAQ,EAAC,KAAK;oBAAA5P,QAAA,eAC5BrC,OAAA,CAACjC,UAAU;sBACT0D,IAAI,EAAC,OAAO;sBACZP,OAAO,EAAEA,CAAA,KAAMuE,aAAa,CAAC,EAAE,CAAE;sBACjC0M,IAAI,EAAC,KAAK;sBAAA9P,QAAA,eAEVrC,OAAA,CAACR,SAAS;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAED+C,UAAU,iBACTxF,OAAA,CAAC5C,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAa,QAAA,GAAC,eAC9C,EAACiO,gBAAgB,CAACvG,MAAM,CAAC9C,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;cAAA;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPzC,OAAA,CAACnB,IAAI;YAACwN,IAAI;YAACmF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAApP,QAAA,gBACvBrC,OAAA,CAAC5C,UAAU;cAACmE,OAAO,EAAC,WAAW;cAACG,EAAE,EAAE;gBAAEwO,UAAU,EAAE,GAAG;gBAAEe,EAAE,EAAE,CAAC;gBAAEtC,OAAO,EAAE,MAAM;gBAAEoB,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAAzN,QAAA,gBAC5GrC,OAAA,CAACL,YAAY;gBAAC+B,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAe;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAAC3B,KAAK;cAAC+S,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAAhP,QAAA,gBACxDrC,OAAA,CAAC3C,MAAM;gBACLkE,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,SAAS;gBACfyO,SAAS,eAAEjQ,OAAA,CAACjB,YAAY;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BvB,OAAO,EAAEyH,cAAe;gBAAAtG,QAAA,EACzB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETzC,OAAA,CAAC3C,MAAM;gBACLkE,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,SAAS;gBACfyO,SAAS,EAAE9L,oBAAoB,gBAAGnE,OAAA,CAAChC,gBAAgB;kBAACyD,IAAI,EAAE,EAAG;kBAACD,KAAK,EAAC;gBAAS;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACX,aAAa;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrGvB,OAAO,EAAEqM,gBAAiB;gBAC1B6E,QAAQ,EAAEjO,oBAAqB;gBAAA9B,QAAA,EAE9B8B,oBAAoB,GAAG,QAAQ,GAAG;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAETzC,OAAA,CAAC3C,MAAM;gBACLkE,OAAO,EAAC,UAAU;gBAClBC,KAAK,EAAC,OAAO;gBACbyO,SAAS,eAAEjQ,OAAA,CAAChB,cAAc;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BvB,OAAO,EAAEsI,aAAc;gBAAAnH,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPzC,OAAA,CAAC1C,KAAK;MAACoE,EAAE,EAAE;QAAE0N,KAAK,EAAE,MAAM;QAAErN,QAAQ,EAAE;MAAS,CAAE;MAAAM,QAAA,eAC7CrC,OAAA,CAAC7C,GAAG;QAACuE,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAEiN,KAAK,EAAE;QAAO,CAAE;QAAA/M,QAAA,eACzCrC,OAAA,CAAClB,QAAQ;UAEPuT,IAAI,EAAEzB,YAAa;UACnB5B,OAAO,EAAEA,OAAQ;UACjB1I,eAAe,EAAEA,eAAgB;UACjCgM,uBAAuB,EAAEvM,kBAAmB;UAC5CwM,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAC/BC,UAAU;UACVC,cAAc,EAAC,QAAQ;UACvBC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,qBAAqB,EAAE,KAAM;UAC7BC,SAAS,EAAE,EAAG;UACdC,OAAO,EAAC,SAAS;UACjBC,SAAS,EAAE,CAAE;UACbC,YAAY,EAAE,CAAE;UAChBC,iBAAiB;UACjBC,mBAAmB;UACnBC,qBAAqB;UACrBC,sBAAsB;UACtBC,0BAA0B;UAC1BC,eAAe,EAAGjE,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAACrI,GAAG,CAACY,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIyH,MAAM,CAACrI,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFoM,cAAc,EAAGlE,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAACrI,GAAG,CAACY,OAAO,IAAIyH,MAAM,CAACrI,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOkI,MAAM,CAACmE,MAAM,CAACnQ,QAAQ,IAAI,OAAOgM,MAAM,CAACmE,MAAM,CAACnQ,QAAQ,KAAK,UAAU,GAC3EgM,MAAM,CAACmE,MAAM,CAACnQ,QAAQ,CAACgM,MAAM,CAAC,GAAGA,MAAM,CAACmE,MAAM,CAACnQ,QAAQ;UAC3D,CAAE;UACFmH,gBAAgB,EAAGC,MAAM,IAAK;YAC5B;YACA,MAAMgJ,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;YAErGA,aAAa,CAAC1I,OAAO,CAAC5H,KAAK,IAAI;cAC7B,IAAIsH,MAAM,CAACtH,KAAK,CAAC,KAAKqE,SAAS,IAAIiD,MAAM,CAACtH,KAAK,CAAC,KAAK,IAAI,IAAIsH,MAAM,CAACtH,KAAK,CAAC,KAAK,EAAE,EAAE;gBACjF,IAAI,OAAOsH,MAAM,CAACtH,KAAK,CAAC,KAAK,QAAQ,EAAE;kBACrC,MAAMuQ,QAAQ,GAAGzJ,MAAM,CAACQ,MAAM,CAACtH,KAAK,CAAC,CAAC;kBACtC,IAAI,CAAC+M,KAAK,CAACwD,QAAQ,CAAC,EAAE;oBACpBjJ,MAAM,CAACtH,KAAK,CAAC,GAAGuQ,QAAQ;kBAC1B;gBACF;cACF;YACF,CAAC,CAAC;YAEF,OAAOlJ,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFQ,uBAAuB,EAAEA,uBAAwB;UACjDxJ,EAAE,EAAE;YACF,cAAc,EAAE;cACdsO,eAAe,EAAE,0BAA0B;cAC3CE,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBF,eAAe,EAAE,0BAA0B;cAC3CxO,KAAK,EAAE,eAAe;cACtB4O,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrBnO,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpBwR,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9B9D,eAAe,EAAE,oBAAoB;cACrC6D,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7B9D,eAAe,EAAE,oBAAoB;cACrCxO,KAAK,EAAE,cAAc;cACrBuS,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClC7D,UAAU,EAAE,MAAM;cAClB1O,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChC8M,OAAO,EAAE;YACX,CAAC;YACDqF,SAAS,EAAE;UACb;QAAE,GA3FG,YAAY1N,eAAe,CAACE,QAAQ,IAAIoK,YAAY,CAACrJ,MAAM,EAAE;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4FnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVzC,OAAA,CAACzC,MAAM;MACLkL,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBwL,OAAO,EAAE5I,kBAAmB;MAC5B6I,SAAS;MACTtS,QAAQ,EAAC,IAAI;MACbuS,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjS,QAAA,gBAEnBrC,OAAA,CAACxC,WAAW;QAAA6E,QAAA,eACVrC,OAAA,CAAC7C,GAAG;UAACuE,EAAE,EAAE;YAAEiN,OAAO,EAAE,MAAM;YAAEuC,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE;UAAS,CAAE;UAAA1N,QAAA,gBAClFrC,OAAA,CAAC5C,UAAU;YAACmE,OAAO,EAAC,IAAI;YAAAc,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CzC,OAAA,CAAC3C,MAAM;YACL4S,SAAS,eAAEjQ,OAAA,CAACf,OAAO;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBvB,OAAO,EAAE6K,mBAAoB;YAC7BvK,KAAK,EAAC,SAAS;YAAAa,QAAA,EAChB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdzC,OAAA,CAAC7C,GAAG;QAACuE,EAAE,EAAE;UAAE6S,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnS,QAAA,gBACxBrC,OAAA,CAAC5C,UAAU;UAACmE,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAEuP,EAAE,EAAE;UAAE,CAAE;UAAA5O,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAAC3B,KAAK;UAAC+S,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAAhP,QAAA,gBAChCrC,OAAA,CAACpB,gBAAgB;YACf6V,OAAO,eACLzU,OAAA,CAACrB,QAAQ;cACP+V,OAAO,EAAExO,aAAc;cACvByL,QAAQ,EAAGrQ,CAAC,IAAK6E,gBAAgB,CAAC7E,CAAC,CAACsQ,MAAM,CAAC8C,OAAO,CAAE;cACpDjT,IAAI,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDkN,KAAK,EAAC;UAAS;YAAArN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFzC,OAAA,CAACpB,gBAAgB;YACf6V,OAAO,eACLzU,OAAA,CAACrB,QAAQ;cACP+V,OAAO,EAAEtO,UAAW;cACpBuL,QAAQ,EAAGrQ,CAAC,IAAK+E,aAAa,CAAC/E,CAAC,CAACsQ,MAAM,CAAC8C,OAAO,CAAE;cACjDjT,IAAI,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDkN,KAAK,EAAC;UAAK;YAAArN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENzC,OAAA,CAACvC,aAAa;QAACkX,QAAQ;QAACjT,EAAE,EAAE;UAAEkT,CAAC,EAAE;QAAE,CAAE;QAAAvS,QAAA,eACnCrC,OAAA,CAACF,aAAa;UACZqC,MAAM,EAAE,GAAI;UACZ0S,SAAS,EAAErR,cAAc,CAAC+D,MAAO;UACjCuN,QAAQ,EAAE,EAAG;UACb1F,KAAK,EAAC,MAAM;UAAA/M,QAAA,EAEXA,CAAC;YAAEsF,KAAK;YAAE+G;UAAM,CAAC,KAAK;YACrB,MAAMnD,MAAM,GAAG/H,cAAc,CAACmE,KAAK,CAAC;YACpC,oBACE3H,OAAA,CAACrC,QAAQ;cAEP+Q,KAAK,EAAEA,KAAM;cACbqG,cAAc;cACdC,eAAe,EAAGzJ,MAAM,KAAK,MAAM,iBACjCvL,OAAA,CAACjC,UAAU;gBACToU,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBjR,OAAO,EAAEA,CAAA,KAAMkL,YAAY,CAACb,MAAM,CAAE;gBAAAlJ,QAAA,eAEpCrC,OAAA,CAACd,UAAU;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAJ,QAAA,eAEFrC,OAAA,CAACpC,cAAc;gBAACsD,OAAO,EAAEA,CAAA,KAAMoK,kBAAkB,CAACC,MAAM,CAAE;gBAAAlJ,QAAA,eACxDrC,OAAA,CAACnC,YAAY;kBAACoX,OAAO,EAAE1J;gBAAO;kBAAAjJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ8I,MAAM;cAAAjJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBzC,OAAA,CAACtC,aAAa;QAAA2E,QAAA,eACZrC,OAAA,CAAC3C,MAAM;UAAC6D,OAAO,EAAEmK,kBAAmB;UAAAhJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzC,OAAA,CAACzC,MAAM;MACLkL,IAAI,EAAExE,eAAgB;MACtBgQ,OAAO,EAAEjI,oBAAqB;MAC9BkI,SAAS;MACTtS,QAAQ,EAAC,IAAI;MACbuS,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjS,QAAA,gBAEnBrC,OAAA,CAACxC,WAAW;QAAA6E,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCzC,OAAA,CAACvC,aAAa;QAAA4E,QAAA,eACZrC,OAAA,CAAClC,SAAS;UACRoX,SAAS;UACTC,MAAM,EAAC,OAAO;UACdvN,EAAE,EAAC,MAAM;UACT+H,KAAK,EAAC,0BAAM;UACZyF,IAAI,EAAC,MAAM;UACXlB,SAAS;UACT3S,OAAO,EAAC,UAAU;UAClBwN,KAAK,EAAEhL,SAAU;UACjB4N,QAAQ,EAAGrQ,CAAC,IAAK0C,YAAY,CAAC1C,CAAC,CAACsQ,MAAM,CAAC7C,KAAK;QAAE;UAAAzM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBzC,OAAA,CAACtC,aAAa;QAAA2E,QAAA,gBACZrC,OAAA,CAAC3C,MAAM;UAAC6D,OAAO,EAAE8K,oBAAqB;UAAA3J,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDzC,OAAA,CAAC3C,MAAM;UAAC6D,OAAO,EAAE+K,YAAa;UAACzK,KAAK,EAAC,SAAS;UAAAa,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACS,GAAA,CAluCIP,aAAa;AAAA0S,GAAA,GAAb1S,aAAa;AAouCnB,eAAeA,aAAa;AAAC,IAAA7B,EAAA,EAAA4B,GAAA,EAAA2S,GAAA;AAAAC,YAAA,CAAAxU,EAAA;AAAAwU,YAAA,CAAA5S,GAAA;AAAA4S,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}