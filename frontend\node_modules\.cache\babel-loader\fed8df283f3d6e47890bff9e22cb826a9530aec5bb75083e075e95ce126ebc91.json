{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 10\n  }, this);\n}\n_c = SlideDownTransition;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    return data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, []);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n\n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    console.log('删除行:', id);\n\n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n\n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    console.log('恢复行:', id);\n\n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n\n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasSelectedRemark ? remarkText : '',\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: remarkText,\n              color: hasSelectedRemark ? 'primary' : 'default',\n              variant: hasSelectedRemark ? 'filled' : 'outlined',\n              size: \"small\",\n              onClick: () => handleRemarksClick(params.row.id, params.value || ''),\n              clickable: true,\n              sx: {\n                maxWidth: '100%',\n                cursor: 'pointer',\n                '& .MuiChip-label': {\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  display: 'block'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: (newRow, oldRow) => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 770,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 1000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      TransitionComponent: SlideDownTransition,\n      sx: {\n        '& .MuiAlert-root': {\n          borderRadius: 10,\n          border: 4\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 636,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"AiJh6ToU0fs/bgPUE4PGMIlimgU=\");\n_c2 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "Slide", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "processRowUpdate", "newRow", "prev", "totalValue", "onProcessRowUpdateError", "handleCloseSnackbar", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "prevData", "updatedData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "rowToRemove", "commissionValue", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "rowToRestore", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "sx", "max<PERSON><PERSON><PERSON>", "opacity", "overflow", "textOverflow", "whiteSpace", "remarkText", "hasSelectedRemark", "onClick", "clickable", "cursor", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "memoGridData", "textAlign", "py", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "backgroundColor", "lineHeight", "padding", "overflowX", "border", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "TransitionComponent", "borderRadius", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    return data\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, []);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n        \n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    console.log('删除行:', id);\n    \n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n    \n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    console.log('恢复行:', id);\n    \n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n    \n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return (\n            <Tooltip title={hasSelectedRemark ? remarkText : ''} arrow placement=\"top\">\n              <Chip\n                label={remarkText}\n                color={hasSelectedRemark ? 'primary' : 'default'}\n                variant={hasSelectedRemark ? 'filled' : 'outlined'}\n                size=\"small\"\n                onClick={() => handleRemarksClick(params.row.id, params.value || '')}\n                clickable\n                sx={{ maxWidth: '100%', cursor: 'pointer', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n              />\n            </Tooltip>\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Chip\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          return (\n            <Chip\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <DataGrid\n            rows={memoGridData}\n            columns={columns}\n            pageSize={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            disableSelectionOnClick\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n            }}\n            processRowUpdate={(newRow, oldRow) => {\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            }}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={1000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n        TransitionComponent={SlideDownTransition} \n        sx={{\n          '& .MuiAlert-root': {\n            borderRadius: 10,\n            border: 4,\n          },\n        }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,qBAAqB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOH,OAAA,CAACF,KAAK;IAAA,GAAKK,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;AAACC,EAAA,GAFQP,mBAAmB;AAI5B,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,MAAM;IACzD,MAAM6D,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGxB,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd6D,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAG7B,IAAI,CAAC8B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnF,QAAQ,CAAC,MAAM;IAC7CoF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7ClC,aAAa,GAAG,IAAIA,aAAa,CAACmC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAInC,aAAa,IAAIA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGpC,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG3F,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM4F,iBAAiB,GAAG5F,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM6F,gBAAgB,GAAG7F,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM8F,UAAU,GAAIlD,IAAI,IAAKA,IAAI,CAAC8B,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCkB,UAAU,EAAEpB,GAAG,CAACoB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAjG,SAAS,CAAC,MAAM;IACd,IAAImD,YAAY,IAAI8B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMa,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGrC,IAAI,CAACW,SAAS,CAACsB,UAAU,CAACf,QAAQ,CAAC,CAAC;MACpD,MAAMoB,WAAW,GAAGR,mBAAmB,CAACS,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIL,gBAAgB,CAACO,OAAO,EAAE;UAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;QACxC;QACAP,gBAAgB,CAACO,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CX,mBAAmB,CAACS,OAAO,GAAGF,OAAO;UACrCN,iBAAiB,CAACQ,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC/C,YAAY,CAAC,CAAC,GAAG8B,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACO,OAAO,EAAE;QAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACrB,QAAQ,EAAE9B,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC;IAAE4G,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhH,QAAQ,CAAC;IACjD4G,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAjH,SAAS,CAAC,MAAM;IACd,IAAIuE,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAGpF,OAAO,aAAagB,MAAM,EAAE;;MAEnD;MACA,MAAMqE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAV,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7E,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM8E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMjG,KAAK,CAACkG,MAAM,CAAC,GAAGjG,OAAO,YAAYgB,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+E,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9E,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMiF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMuC,kBAAkB,GAAGlI,WAAW,CAAC,CAAC6C,IAAI,EAAEsF,UAAU,KAAK;IAC3D,OAAOtF,IAAI,CACRuF,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDsD,MAAM,CAAC,CAACC,GAAG,EAAE1D,GAAG,KAAK;MACpB,IAAIuD,UAAU,IAAIvD,GAAG,CAACa,EAAE,KAAK0C,UAAU,CAAC1C,EAAE,EAAE;QAC1C,OAAO6C,GAAG,IAAIC,MAAM,CAACJ,UAAU,CAACnC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAOsC,GAAG,IAAIC,MAAM,CAAC3D,GAAG,CAACoB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwC,gBAAgB,GAAGxI,WAAW,CAAE6C,IAAI,IAAK;IAC7C,MAAM4F,QAAQ,GAAG5F,IAAI,CAAC6F,IAAI,CAAC9D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAI8C,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG9F,IAAI,CAClBuF,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDsD,MAAM,CAAC,CAACC,GAAG,EAAE1D,GAAG,KAAK0D,GAAG,IAAIC,MAAM,CAAC3D,GAAG,CAACoB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DyC,QAAQ,CAACzC,UAAU,GAAG2C,QAAQ;IAChC;IACA,OAAO9F,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+F,gBAAgB,GAAG5I,WAAW,CAAE6I,MAAM,IAAK;IAC/C5D,WAAW,CAAC6D,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGb,kBAAkB,CAACY,IAAI,EAAED,MAAM,CAAC;MACjD,OAAOC,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAKoD,MAAM,CAACpD,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAGiE;QAAO,CAAC;QACtD,IAAIjE,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEoB,UAAU,EAAE+C;QAAW,CAAC;QACjE,OAAOnE,GAAG;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOiE,MAAM;EACf,CAAC,EAAE,CAACX,kBAAkB,CAAC,CAAC;EAExB,MAAMc,uBAAuB,GAAInB,KAAK,IAAK;IACzCpB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASkB,KAAK,CAAClB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxC,WAAW,CAACqC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMwC,iBAAiB,GAAGrJ,KAAK,CAACG,WAAW,CAAC,CAAC+G,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,kBAAkB,GAAGnJ,WAAW,CAAC,MAAM;IAC3C8G,gBAAgB,CAACgC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPpC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0C,kBAAkB,GAAGpJ,WAAW,CAAEqJ,MAAM,IAAK;IACjD,MAAM;MAAEtC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB7B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkE,MAAM,EAAE,MAAM,EAAEtC,KAAK,CAAC;MAChD9B,WAAW,CAACqE,QAAQ,IAAI;QACtB,IAAIC,WAAW,GAAGD,QAAQ,CAAC3E,GAAG,CAACC,GAAG,IAAI;UACpC,IAAIA,GAAG,CAACa,EAAE,KAAKsB,KAAK,EAAE;YACpB;YACA,IAAIsC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAGzE,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAEwE,MAAM;gBAAEvE,iBAAiB,EAAEuE;cAAO,CAAC;YAC/D;UACF;UACA,OAAOzE,GAAG;QACZ,CAAC,CAAC;;QAEF;QACA2E,WAAW,GAAGf,gBAAgB,CAACe,WAAW,CAAC;;QAE3C;QACA,OAAOA,WAAW;MACpB,CAAC,CAAC;MACF9C,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAuC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACtC,aAAa,EAAEsC,kBAAkB,EAAEX,gBAAgB,CAAC,CAAC;;EAEzD;EACA,MAAMgB,mBAAmB,GAAGxJ,WAAW,CAAC,MAAM;IAC5CmE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsF,oBAAoB,GAAGzJ,WAAW,CAAC,MAAM;IAC7CmE,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyF,YAAY,GAAG1J,WAAW,CAAC,MAAM;IACrC,IAAIgE,SAAS,CAAC2F,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAClG,cAAc,CAACmG,QAAQ,CAAC5F,SAAS,CAAC2F,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEjG,iBAAiB,CAACoF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE9E,SAAS,CAAC2F,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDlD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE6C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIhG,cAAc,CAACmG,QAAQ,CAAC5F,SAAS,CAAC2F,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDlD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAAC5C,SAAS,EAAEP,cAAc,EAAEgG,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG7J,WAAW,CAAEqJ,MAAM,IAAK;IAC3C3F,iBAAiB,CAACoF,IAAI,IAAIA,IAAI,CAACV,MAAM,CAAC0B,IAAI,IAAIA,IAAI,KAAKT,MAAM,CAAC,CAAC;IAC/D5C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmD,eAAe,GAAG/J,WAAW,CAAEyF,EAAE,IAAK;IAC1CP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAMuE,WAAW,GAAGhF,QAAQ,CAAC0D,IAAI,CAAC9D,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACvD,MAAMwE,eAAe,GAAGD,WAAW,GAAGA,WAAW,CAAChE,UAAU,GAAG,CAAC;IAChEd,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8E,eAAe,CAAC;;IAE/C;IACAhF,WAAW,CAAC6D,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnF2E,WAAW,GAAGf,gBAAgB,CAACe,WAAW,CAAC;MAC3C;MACA,MAAMW,cAAc,GAAGX,WAAW,CAACnB,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHuE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3E,EAAE,GAAG4E,CAAC,CAAC5E,EAAE,CAAC;MAC1CyE,cAAc,CAACI,OAAO,CAAC,CAAC1F,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAO+D,WAAW;IACpB,CAAC,CAAC;IACF9C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC,EAAE,CAAC4B,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM+B,aAAa,GAAGvK,WAAW,CAAEyF,EAAE,IAAK;IACxCP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAM+E,YAAY,GAAGxF,QAAQ,CAAC0D,IAAI,CAAC9D,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACxD,MAAMwE,eAAe,GAAGO,YAAY,GAAGA,YAAY,CAACxE,UAAU,GAAG,CAAC;IAClEd,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8E,eAAe,CAAC;;IAE/C;IACAhF,WAAW,CAAC6D,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAACnE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpF2E,WAAW,GAAGf,gBAAgB,CAACe,WAAW,CAAC;MAC3C,MAAMW,cAAc,GAAGX,WAAW,CAACnB,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHuE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3E,EAAE,GAAG4E,CAAC,CAAC5E,EAAE,CAAC;MAC1CyE,cAAc,CAACI,OAAO,CAAC,CAAC1F,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAO+D,WAAW;IACpB,CAAC,CAAC;IACF9C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC4B,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpG,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMqG,YAAY,GAAG1F,QAAQ,CAC1BoD,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACA2F,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACzE,EAAE,KAAK,QAAQ,IAAI,OAAO0E,CAAC,CAAC1E,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOyE,CAAC,CAACzE,EAAE,GAAG0E,CAAC,CAAC1E,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMgF,OAAO,GAAGD,YAAY,CAAC/F,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACboF,IAAI,EAAEhG,GAAG,CAACgG,IAAI,GAAI,OAAOhG,GAAG,CAACgG,IAAI,KAAK,QAAQ,IAAIhG,GAAG,CAACgG,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAGhF,GAAG,CAACgG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGjG,GAAG,CAACgG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEhG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGkG,IAAI,CAACC,KAAK,CAACnG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFoG,EAAE,EAAE,OAAOpG,GAAG,CAACoG,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACnG,GAAG,CAACoG,EAAE,CAAC,GAAGpG,GAAG,CAACoG,EAAE,IAAI,EAAE;QAClEnG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGmG,KAAK,EAAE,OAAOrG,GAAG,CAACsG,QAAQ,KAAK,QAAQ,GACpCtG,GAAG,CAACsG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGtG,GAAG,CAACsG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGvG,GAAG,CAACsG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EvG,GAAG,CAACsG,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOxG,GAAG,CAACoB,UAAU,KAAK,QAAQ,GAAGpB,GAAG,CAACoB,UAAU,CAACmF,OAAO,CAAC,CAAC,CAAC,GAAGvG,GAAG,CAACoB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMqF,WAAW,GAAGrG,QAAQ,CACzBoD,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACoB,UAAU,CAAC,CACpEqC,MAAM,CAAC,CAACC,GAAG,EAAE1D,GAAG,KAAK0D,GAAG,IAAI,OAAO1D,GAAG,CAACoB,UAAU,KAAK,QAAQ,GAAGpB,GAAG,CAACoB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMsF,QAAQ,GAAG,MAAMzJ,KAAK,CAAC0J,IAAI,CAAC,GAAGzJ,OAAO,oBAAoB,EAAE;QAChEe,IAAI,EAAE8H,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCrI,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIwI,QAAQ,CAACzI,IAAI,IAAIyI,QAAQ,CAACzI,IAAI,CAAC2I,KAAK,EAAE;QACxC;QACA,MAAMtE,WAAW,GAAG,GAAGpF,OAAO,CAAC+I,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAACzI,IAAI,CAAC4I,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAhF,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACAL,UAAU,CAAC,MAAM;UACf,MAAMmF,MAAM,GAAGtE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CqE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG3E,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACgE,MAAM,CAAC;UACjCnF,UAAU,CAAC,MAAM;YACfa,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC8D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRvC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM0H,kBAAkB,GAAG/L,WAAW,CAAC,CAAC+G,KAAK,EAAEiF,KAAK,KAAK;IACvD9C,iBAAiB,CAACnC,KAAK,EAAEiF,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC9C,iBAAiB,CAAC,CAAC;EAEvB,MAAM+C,OAAO,GAAG/L,OAAO,CAAC,MAAOkD,WAAW,CAACuB,GAAG,CAACuH,GAAG,IAAI;IACpD,IAAI,EAAElH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACmH,cAAc,CAACD,GAAG,CAAC7I,KAAK,CAAC,CAAC,IAAI6I,GAAG,CAAC7I,KAAK,KAAK,SAAS,IAAI6I,GAAG,CAAC7I,KAAK,KAAK,QAAQ,IAAI6I,GAAG,CAAC7I,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAI6I,GAAG,CAAC7I,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAE6I,GAAG,CAAC7I,KAAK;QAChBC,UAAU,EAAE4I,GAAG,CAAC5I,UAAU;QAC1B8I,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV9I,QAAQ,EAAE,KAAK;QACf+I,UAAU,EAAGrE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIsC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMwH,iBAAiB,GAAGtE,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE5C,OAAA,CAACb,OAAO;cAACmL,KAAK,EAAEvE,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAAC2H,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvEzK,OAAA,CAACd,IAAI;gBACHwL,KAAK,EAAEL,iBAAkB;gBACzBM,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,OAAO,EAAE,GAAG;kBAAE,kBAAkB,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEzB,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UACA,IAAI4K,UAAU,GAAG,MAAM;UACvB,IAAIC,iBAAiB,GAAG,KAAK;UAC7B,IAAItF,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAImD,MAAM,CAACrD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EwI,UAAU,GAAGrF,MAAM,CAACrD,GAAG,CAACE,iBAAiB;YACzCyI,iBAAiB,GAAG,IAAI;UAC1B;UACA,oBACErL,OAAA,CAACb,OAAO;YAACmL,KAAK,EAAEe,iBAAiB,GAAGD,UAAU,GAAG,EAAG;YAACb,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAC,QAAA,eACxEzK,OAAA,CAACd,IAAI;cACHwL,KAAK,EAAEU,UAAW;cAClBT,KAAK,EAAEU,iBAAiB,GAAG,SAAS,GAAG,SAAU;cACjDT,OAAO,EAAES,iBAAiB,GAAG,QAAQ,GAAG,UAAW;cACnDR,IAAI,EAAC,OAAO;cACZS,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAAC9D,MAAM,CAACrD,GAAG,CAACa,EAAE,EAAEwC,MAAM,CAAC+D,KAAK,IAAI,EAAE,CAAE;cACrEyB,SAAS;cACTT,EAAE,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAES,MAAM,EAAE,SAAS;gBAAE,kBAAkB,EAAE;kBAAEP,QAAQ,EAAE,QAAQ;kBAAEC,YAAY,EAAE,UAAU;kBAAEC,UAAU,EAAE,QAAQ;kBAAEzB,OAAO,EAAE;gBAAQ;cAAE;YAAE;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAEd;MACF,CAAC;IACH;IACA,IAAIwJ,GAAG,CAAC7I,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAE6I,GAAG,CAAC7I,KAAK;QAChBC,UAAU,EAAE4I,GAAG,CAAC5I,UAAU;QAC1B8I,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV9I,QAAQ,EAAE,KAAK;QACf+I,UAAU,EAAGrE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIsC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACE7C,OAAA,CAACd,IAAI;cACHwL,KAAK,EAAC,cAAI;cACVC,KAAK,EAAC,SAAS;cACfE,IAAI,EAAC,OAAO;cACZY,IAAI,eAAEzL,OAAA,CAACP,QAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnB8K,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACtC,MAAM,CAACrD,GAAG,CAACa,EAAE,CAAE;cAC5CuH,EAAE,EAAE;gBAAEU,MAAM,EAAE;cAAU;YAAE;cAAAnL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAEN;UACA,oBACER,OAAA,CAACd,IAAI;YACHwL,KAAK,EAAC,cAAI;YACVC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZY,IAAI,eAAEzL,OAAA,CAACR,UAAU;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrB8K,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC9B,MAAM,CAACrD,GAAG,CAACa,EAAE,CAAE;YAC9CuH,EAAE,EAAE;cAAEU,MAAM,EAAE;YAAU;UAAE;YAAAnL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAEN;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGwJ,GAAG;MACN3I,QAAQ,EAAE0E,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACrD,GAAG,IAAIqD,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIsC,MAAM,CAACrD,GAAG,IAAIqD,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOmH,GAAG,CAAC3I,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD+I,UAAU,EAAGrE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIuG,GAAG,CAAC7I,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEnB,OAAA,CAAC9B,UAAU;YAAC0M,OAAO,EAAC,OAAO;YAACc,UAAU,EAAC,MAAM;YAACf,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAO1E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,GAAG/D,MAAM,CAAC+D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOlD,MAAM,CAAC+D,KAAK,KAAK,QAAQ,IAAI,CAAC6B,KAAK,CAACtF,MAAM,CAACN,MAAM,CAAC+D,KAAK,CAAC,CAAC,GAAGzD,MAAM,CAACN,MAAM,CAAC+D,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGlD,MAAM,CAAC+D;UAAK;YAAAzJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIuF,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE7C,OAAA,CAAC9B,UAAU;YAAC0M,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,eAAe;YAACG,EAAE,EAAE;cAAEc,cAAc,EAAE;YAAe,CAAE;YAAAnB,QAAA,EACtF1E,MAAM,CAAC+D;UAAK;YAAAzJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIwJ,GAAG,CAAC7I,KAAK,KAAK,MAAM,IAAI4E,MAAM,CAAC+D,KAAK,EAAE;UACxC,OAAO/D,MAAM,CAAC+D,KAAK,CAACnB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIqB,GAAG,CAAC7I,KAAK,KAAK,IAAI,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAAC9C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7I,KAAK,KAAK,OAAO,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOlB,IAAI,CAACC,KAAK,CAAC9C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7I,KAAK,KAAK,IAAI,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAAC9C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC7I,KAAK,KAAK,UAAU,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO/D,MAAM,CAAC+D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG/D,MAAM,CAAC+D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGlD,MAAM,CAAC+D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAAC7I,KAAK,KAAK,YAAY,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO/D,MAAM,CAAC+D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAAC7I,KAAK,KAAK,YAAY,IAAI,OAAO4E,MAAM,CAAC+D,KAAK,KAAK,QAAQ,IAAI,CAAC6B,KAAK,CAACtF,MAAM,CAACN,MAAM,CAAC+D,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOzD,MAAM,CAACN,MAAM,CAAC+D,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOlD,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO/D,MAAM,CAAC+D,KAAK;QACrB;QACA,OAAO/D,MAAM,CAAC+D,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC5D,MAAM,CAAC2F,OAAO,CAAE,EAAE,CAAC3K,WAAW,EAAE4B,QAAQ,EAAE+G,kBAAkB,EAAEhC,eAAe,EAAEQ,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAMyD,YAAY,GAAG9N,OAAO,CAAC,MAAM8E,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAExD;EACA,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACElD,OAAA,CAAC/B,GAAG;MAAC6M,EAAE,EAAE;QAAEiB,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACtCzK,OAAA,CAAC9B,UAAU;QAAC0M,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAAC7B,MAAM;QACLyM,OAAO,EAAC,WAAW;QACnBU,OAAO,EAAEzK,OAAQ;QACjBiK,EAAE,EAAE;UAAEmB,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,EACf;MAED;QAAApK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACER,OAAA,CAAC/B,GAAG;IAAAwM,QAAA,gBACFzK,OAAA,CAAC/B,GAAG;MAAC6M,EAAE,EAAE;QAAEpB,OAAO,EAAE,MAAM;QAAEwC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBACzFzK,OAAA,CAAC9B,UAAU;QAAC0M,OAAO,EAAC,IAAI;QAACyB,YAAY;QAAA5B,QAAA,EAAC;MAEtC;QAAApK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbR,OAAA,CAAC/B,GAAG;QAAAwM,QAAA,gBACFzK,OAAA,CAAC7B,MAAM;UACLyM,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf2B,SAAS,eAAEtM,OAAA,CAACX,YAAY;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B8K,OAAO,EAAEvG,cAAe;UACxB+F,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EACf;QAED;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETR,OAAA,CAAC7B,MAAM;UACLyM,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf2B,SAAS,eAAEtM,OAAA,CAACN,gBAAgB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChC8K,OAAO,EAAE/C,gBAAiB;UAC1BiE,QAAQ,EAAEtK,oBAAqB;UAC/B4I,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EAEbvI,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETR,OAAA,CAAC7B,MAAM;UACLyM,OAAO,EAAC,UAAU;UAClB0B,SAAS,eAAEtM,OAAA,CAACV,cAAc;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9B8K,OAAO,EAAE1F,aAAc;UAAA6E,QAAA,EACxB;QAED;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAAC5B,KAAK;MAAC0M,EAAE,EAAE;QAAEX,KAAK,EAAE,MAAM;QAAEc,QAAQ,EAAE;MAAS,CAAE;MAAAR,QAAA,eAC/CzK,OAAA,CAAC/B,GAAG;QAAC6M,EAAE,EAAE;UAAE2B,MAAM,EAAE,MAAM;UAAEtC,KAAK,EAAE;QAAO,CAAE;QAAAM,QAAA,eACzCzK,OAAA,CAACZ,QAAQ;UACPsN,IAAI,EAAEZ,YAAa;UACnB/B,OAAO,EAAEA,OAAQ;UACjB4C,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGjH,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAACrD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIuC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFoK,cAAc,EAAGlH,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAACrD,GAAG,CAACc,OAAO,IAAIuC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOkD,MAAM,CAACmH,MAAM,CAAC7L,QAAQ,IAAI,OAAO0E,MAAM,CAACmH,MAAM,CAAC7L,QAAQ,KAAK,UAAU,GAC3E0E,MAAM,CAACmH,MAAM,CAAC7L,QAAQ,CAAC0E,MAAM,CAAC,GAAGA,MAAM,CAACmH,MAAM,CAAC7L,QAAQ;UAC3D,CAAE;UACFqF,gBAAgB,EAAEA,CAACC,MAAM,EAAEwG,MAAM,KAAK;YACpC,IAAIxG,MAAM,CAAC7C,UAAU,KAAKV,SAAS,EAAE;cACnC,IAAI,OAAOuD,MAAM,CAAC7C,UAAU,KAAK,QAAQ,EAAE;gBACzC6C,MAAM,CAAC7C,UAAU,GAAGuC,MAAM,CAACM,MAAM,CAAC7C,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAO4C,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDgE,EAAE,EAAE;YACF,cAAc,EAAE;cACdsC,eAAe,EAAE,0BAA0B;cAC3C1B,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChB0B,eAAe,EAAE,0BAA0B;cAC3CzC,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrBQ,UAAU,EAAE,QAAQ;cACpBkC,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCG,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrBtC,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnBuC,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BF,OAAO,EAAE,OAAO;cAChBnC,UAAU,EAAE,QAAQ;cACpBkC,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClClC,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnBoC,UAAU,EAAE,MAAM;cAClB3B,UAAU,EAAE;YACd;UACF;QAAE;UAAArL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRR,OAAA,CAACzB,MAAM;MACLiG,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzBiJ,OAAO,EAAExG,kBAAmB;MAC5ByG,SAAS;MACT3C,QAAQ,EAAC,IAAI;MACb4C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAArD,QAAA,gBAEnBzK,OAAA,CAACxB,WAAW;QAAAiM,QAAA,eACVzK,OAAA,CAAC/B,GAAG;UAAC6M,EAAE,EAAE;YAAEpB,OAAO,EAAE,MAAM;YAAEwC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA1B,QAAA,gBAClFzK,OAAA,CAAC9B,UAAU;YAAC0M,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAApK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CR,OAAA,CAAC7B,MAAM;YACLmO,SAAS,eAAEtM,OAAA,CAACT,OAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB8K,OAAO,EAAEhE,mBAAoB;YAC7BqD,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdR,OAAA,CAACvB,aAAa;QAACsP,QAAQ;QAACjD,EAAE,EAAE;UAAEkD,CAAC,EAAE;QAAE,CAAE;QAAAvD,QAAA,eACnCzK,OAAA,CAACH,aAAa;UACZ4M,MAAM,EAAE,GAAI;UACZwB,SAAS,EAAE1M,cAAc,CAAC2B,MAAO;UACjCgL,QAAQ,EAAE,EAAG;UACb/D,KAAK,EAAC,MAAM;UAAAM,QAAA,EAEXA,CAAC;YAAEnH,KAAK;YAAEmG;UAAM,CAAC,KAAK;YACrB,MAAMtC,MAAM,GAAG5F,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACEtD,OAAA,CAACpB,QAAQ;cAEP6K,KAAK,EAAEA,KAAM;cACb0E,cAAc;cACdC,eAAe,eACbpO,OAAA,CAAChB,UAAU;gBACTqP,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB/C,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACR,MAAM,CAAE;gBAAAsD,QAAA,eAEpCzK,OAAA,CAACR,UAAU;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiK,QAAA,eAEDzK,OAAA,CAACnB,cAAc;gBAACyM,OAAO,EAAEA,CAAA,KAAMpE,kBAAkB,CAACC,MAAM,CAAE;gBAAAsD,QAAA,eACxDzK,OAAA,CAAClB,YAAY;kBAACwP,OAAO,EAAEnH;gBAAO;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ2G,MAAM;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBR,OAAA,CAACtB,aAAa;QAAA+L,QAAA,eACZzK,OAAA,CAAC7B,MAAM;UAACmN,OAAO,EAAErE,kBAAmB;UAAAwD,QAAA,EAAC;QAAE;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTR,OAAA,CAACzB,MAAM;MACLiG,IAAI,EAAExC,eAAgB;MACtByL,OAAO,EAAElG,oBAAqB;MAC9BmG,SAAS;MACT3C,QAAQ,EAAC,IAAI;MACb4C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAArD,QAAA,gBAEnBzK,OAAA,CAACxB,WAAW;QAAAiM,QAAA,EAAC;MAAK;QAAApK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCR,OAAA,CAACvB,aAAa;QAAAgM,QAAA,eACZzK,OAAA,CAACjB,SAAS;UACRwP,SAAS;UACTC,MAAM,EAAC,OAAO;UACdjL,EAAE,EAAC,MAAM;UACTmH,KAAK,EAAC,0BAAM;UACZ+D,IAAI,EAAC,MAAM;UACXf,SAAS;UACT9C,OAAO,EAAC,UAAU;UAClBd,KAAK,EAAEhI,SAAU;UACjB4M,QAAQ,EAAGC,CAAC,IAAK5M,YAAY,CAAC4M,CAAC,CAACC,MAAM,CAAC9E,KAAK;QAAE;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBR,OAAA,CAACtB,aAAa;QAAA+L,QAAA,gBACZzK,OAAA,CAAC7B,MAAM;UAACmN,OAAO,EAAE/D,oBAAqB;UAAAkD,QAAA,EAAC;QAAE;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDR,OAAA,CAAC7B,MAAM;UAACmN,OAAO,EAAE9D,YAAa;UAACmD,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETR,OAAA,CAAC3B,QAAQ;MACPmG,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBqK,gBAAgB,EAAE,IAAK;MACvBpB,OAAO,EAAE1G,mBAAoB;MAC7B+H,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MACxDC,mBAAmB,EAAE/O,mBAAoB;MACzC4K,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBoE,YAAY,EAAE,EAAE;UAChB1B,MAAM,EAAE;QACV;MACF,CAAE;MAAA/C,QAAA,eAEFzK,OAAA,CAAC1B,KAAK;QAACmP,OAAO,EAAE1G,mBAAoB;QAACrC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAA+F,QAAA,EAC9DnG,QAAQ,CAACG;MAAO;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACS,EAAA,CApyBIP,aAAa;AAAAyO,GAAA,GAAbzO,aAAa;AAsyBnB,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAA0O,GAAA;AAAAC,YAAA,CAAA3O,EAAA;AAAA2O,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}