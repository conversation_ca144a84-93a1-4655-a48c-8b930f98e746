{"ast": null, "code": "export { ClickAwayListener as default } from './ClickAwayListener';", "map": {"version": 3, "names": ["ClickAwayListener", "default"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/material/ClickAwayListener/index.js"], "sourcesContent": ["export { ClickAwayListener as default } from './ClickAwayListener';"], "mappings": "AAAA,SAASA,iBAAiB,IAAIC,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}