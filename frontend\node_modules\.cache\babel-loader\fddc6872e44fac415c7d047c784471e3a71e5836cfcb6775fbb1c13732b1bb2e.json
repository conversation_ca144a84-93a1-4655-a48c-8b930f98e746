{"ast": null, "code": "import * as React from 'react';\nimport { isFunction } from '../../utils/utils';\nimport { useGridLogger } from './useGridLogger';\nexport const useGridNativeEventListener = (apiRef, ref, eventName, handler, options) => {\n  const logger = useGridLogger(apiRef, 'useNativeEventListener');\n  const [added, setAdded] = React.useState(false);\n  const handlerRef = React.useRef(handler);\n  const wrapHandler = React.useCallback(event => {\n    return handlerRef.current && handlerRef.current(event);\n  }, []);\n  React.useEffect(() => {\n    handlerRef.current = handler;\n  }, [handler]);\n  React.useEffect(() => {\n    let targetElement;\n    if (isFunction(ref)) {\n      targetElement = ref();\n    } else {\n      targetElement = ref && ref.current ? ref.current : null;\n    }\n    if (targetElement && eventName && !added) {\n      logger.debug(`Binding native ${eventName} event`);\n      targetElement.addEventListener(eventName, wrapHandler, options);\n      const boundElem = targetElement;\n      setAdded(true);\n      const unsubscribe = () => {\n        logger.debug(`Clearing native ${eventName} event`);\n        boundElem.removeEventListener(eventName, wrapHandler, options);\n      };\n      apiRef.current.subscribeEvent('unmount', unsubscribe);\n    }\n  }, [ref, wrapHandler, eventName, added, logger, options, apiRef]);\n};", "map": {"version": 3, "names": ["React", "isFunction", "useGridLogger", "useGridNativeEventListener", "apiRef", "ref", "eventName", "handler", "options", "logger", "added", "setAdded", "useState", "handler<PERSON>ef", "useRef", "wrapHandler", "useCallback", "event", "current", "useEffect", "targetElement", "debug", "addEventListener", "boundElem", "unsubscribe", "removeEventListener", "subscribeEvent"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/utils/useGridNativeEventListener.js"], "sourcesContent": ["import * as React from 'react';\nimport { isFunction } from '../../utils/utils';\nimport { useGridLogger } from './useGridLogger';\nexport const useGridNativeEventListener = (apiRef, ref, eventName, handler, options) => {\n  const logger = useGridLogger(apiRef, 'useNativeEventListener');\n  const [added, setAdded] = React.useState(false);\n  const handlerRef = React.useRef(handler);\n  const wrapHandler = React.useCallback(event => {\n    return handlerRef.current && handlerRef.current(event);\n  }, []);\n  React.useEffect(() => {\n    handlerRef.current = handler;\n  }, [handler]);\n  React.useEffect(() => {\n    let targetElement;\n    if (isFunction(ref)) {\n      targetElement = ref();\n    } else {\n      targetElement = ref && ref.current ? ref.current : null;\n    }\n    if (targetElement && eventName && !added) {\n      logger.debug(`Binding native ${eventName} event`);\n      targetElement.addEventListener(eventName, wrapHandler, options);\n      const boundElem = targetElement;\n      setAdded(true);\n      const unsubscribe = () => {\n        logger.debug(`Clearing native ${eventName} event`);\n        boundElem.removeEventListener(eventName, wrapHandler, options);\n      };\n      apiRef.current.subscribeEvent('unmount', unsubscribe);\n    }\n  }, [ref, wrapHandler, eventName, added, logger, options, apiRef]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,KAAK;EACtF,MAAMC,MAAM,GAAGP,aAAa,CAACE,MAAM,EAAE,wBAAwB,CAAC;EAC9D,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMC,UAAU,GAAGb,KAAK,CAACc,MAAM,CAACP,OAAO,CAAC;EACxC,MAAMQ,WAAW,GAAGf,KAAK,CAACgB,WAAW,CAACC,KAAK,IAAI;IAC7C,OAAOJ,UAAU,CAACK,OAAO,IAAIL,UAAU,CAACK,OAAO,CAACD,KAAK,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;EACNjB,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpBN,UAAU,CAACK,OAAO,GAAGX,OAAO;EAC9B,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACbP,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpB,IAAIC,aAAa;IACjB,IAAInB,UAAU,CAACI,GAAG,CAAC,EAAE;MACnBe,aAAa,GAAGf,GAAG,CAAC,CAAC;IACvB,CAAC,MAAM;MACLe,aAAa,GAAGf,GAAG,IAAIA,GAAG,CAACa,OAAO,GAAGb,GAAG,CAACa,OAAO,GAAG,IAAI;IACzD;IACA,IAAIE,aAAa,IAAId,SAAS,IAAI,CAACI,KAAK,EAAE;MACxCD,MAAM,CAACY,KAAK,CAAC,kBAAkBf,SAAS,QAAQ,CAAC;MACjDc,aAAa,CAACE,gBAAgB,CAAChB,SAAS,EAAES,WAAW,EAAEP,OAAO,CAAC;MAC/D,MAAMe,SAAS,GAAGH,aAAa;MAC/BT,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMa,WAAW,GAAGA,CAAA,KAAM;QACxBf,MAAM,CAACY,KAAK,CAAC,mBAAmBf,SAAS,QAAQ,CAAC;QAClDiB,SAAS,CAACE,mBAAmB,CAACnB,SAAS,EAAES,WAAW,EAAEP,OAAO,CAAC;MAChE,CAAC;MACDJ,MAAM,CAACc,OAAO,CAACQ,cAAc,CAAC,SAAS,EAAEF,WAAW,CAAC;IACvD;EACF,CAAC,EAAE,CAACnB,GAAG,EAAEU,WAAW,EAAET,SAAS,EAAEI,KAAK,EAAED,MAAM,EAAED,OAAO,EAAEJ,MAAM,CAAC,CAAC;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}