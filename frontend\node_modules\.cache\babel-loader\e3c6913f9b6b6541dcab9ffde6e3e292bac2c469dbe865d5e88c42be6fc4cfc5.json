{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Container, Box, Typography, Stepper, Step, StepLabel, Paper, CssBaseline, ThemeProvider, createTheme, Tooltip, IconButton, Fade, Slide, useMediaQuery } from '@mui/material';\nimport DeleteSweepIcon from '@mui/icons-material/DeleteSweep';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport LightModeIcon from '@mui/icons-material/LightMode';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport FileUpload from './components/FileUpload';\nimport WorksheetSelect from './components/WorksheetSelect';\nimport ProcessForm from './components/ProcessForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport ErrorSnackbar from './components/ErrorSnackbar';\nimport { Toaster } from 'react-hot-toast';\n\n// 创建主题函数\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst createAppTheme = mode => createTheme({\n  palette: {\n    mode,\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0'\n    },\n    secondary: {\n      main: '#9c27b0',\n      light: '#ba68c8',\n      dark: '#7b1fa2'\n    },\n    background: {\n      default: mode === 'light' ? '#f5f5f5' : '#121212',\n      paper: mode === 'light' ? '#ffffff' : '#1e1e1e'\n    },\n    text: {\n      primary: mode === 'light' ? '#212121' : '#ffffff',\n      secondary: mode === 'light' ? '#757575' : '#b3b3b3'\n    }\n  },\n  typography: {\n    fontFamily: ['-apple-system', 'BlinkMacSystemFont', '\"Segoe UI\"', 'Roboto', '\"Helvetica Neue\"', 'Arial', 'sans-serif'].join(','),\n    h4: {\n      fontWeight: 600,\n      fontSize: '2rem'\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1.25rem'\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n          borderRadius: '8px',\n          padding: '8px 16px'\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: mode === 'light' ? '0 2px 8px rgba(0,0,0,0.1)' : '0 2px 8px rgba(0,0,0,0.3)'\n        }\n      }\n    }\n  }\n});\nconst steps = ['上传Excel文件', '选择工作表', '设置参数', '查看结果'];\nfunction App() {\n  _s();\n  // 主题模式状态\n  const [themeMode, setThemeMode] = useState(() => {\n    const savedMode = localStorage.getItem('themeMode');\n    return savedMode || 'light';\n  });\n\n  // 创建主题\n  const theme = createAppTheme(themeMode);\n  const [activeStep, setActiveStep] = useState(0);\n  const [fileData, setFileData] = useState(null);\n  const [selectedWorksheet, setSelectedWorksheet] = useState('');\n  const [processParams, setProcessParams] = useState({\n    startCol: '',\n    endCol: '',\n    perHourRate: '',\n    cbuCarHourRate: '',\n    commissionRate: ''\n  });\n  const [resultData, setResultData] = useState(null);\n  const [error, setError] = useState('');\n\n  // 响应式设计\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // 保存ResultDisplay组件中的表格数据\n  const [gridData, setGridData] = useState([]);\n\n  // 使用ref来避免无限循环\n  const lastGridDataRef = useRef(null);\n  const lastUpdateTimeRef = useRef(0);\n\n  // 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\n  useEffect(() => {\n    // 当gridData变化且不为空时，延迟保存到localStorage\n    if (gridData && gridData.length > 0) {\n      // 使用防抖技术，延迟1秒后保存\n      const saveTimeout = setTimeout(() => {\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(gridData));\n          // console.log('保存gridData到localStorage:', gridData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n      }, 1000);\n\n      // 清除上一个定时器\n      return () => clearTimeout(saveTimeout);\n    }\n  }, [gridData]);\n\n  // 在组件加载时，尝试从localStorage恢复数据\n  useEffect(() => {\n    // 只在初始化时执行一次\n    const savedGridDataString = localStorage.getItem('savedGridData');\n    const savedStateString = localStorage.getItem('commissionState');\n    console.log('检查localStorage恢复:', {\n      hasSavedGridData: !!savedGridDataString,\n      hasSavedState: !!savedStateString\n    });\n\n    // 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\n    if (savedGridDataString) {\n      try {\n        const savedData = JSON.parse(savedGridDataString);\n        if (savedData && savedData.length > 0) {\n          setGridData(savedData);\n          console.log('从localStorage恢复gridData:', savedData.length);\n\n          // 如果有savedGridData，说明应该在ResultDisplay页面\n          // 需要恢复所有必要的状态以支持ResultDisplay\n          if (savedStateString) {\n            try {\n              const savedState = JSON.parse(savedStateString);\n              console.log('从localStorage恢复commissionState以支持ResultDisplay');\n\n              // 恢复到ResultDisplay页面需要的所有状态\n              setActiveStep(3); // 直接跳转到ResultDisplay\n\n              if (savedState.fileData && savedState.fileData.file_id) {\n                setFileData(savedState.fileData);\n              }\n              setSelectedWorksheet(savedState.selectedWorksheet || '');\n              setProcessParams(savedState.processParams || {\n                startCol: '',\n                endCol: '',\n                perHourRate: '',\n                cbuCarHourRate: '',\n                commissionRate: ''\n              });\n              setResultData(savedState.resultData || null);\n            } catch (error) {\n              console.error('解析commissionState失败:', error);\n              // 即使commissionState解析失败，也要跳转到ResultDisplay\n              setActiveStep(3);\n            }\n          } else {\n            // 没有commissionState但有gridData，创建最小状态支持ResultDisplay\n            console.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');\n            setActiveStep(3);\n\n            // 创建最小的状态以支持ResultDisplay正常工作\n            // 使用时间戳作为fileId，确保唯一性\n            const recoveredFileId = `recovered_${Date.now()}`;\n            setFileData({\n              file_id: recoveredFileId,\n              worksheets: ['recovered']\n            });\n            setSelectedWorksheet('recovered');\n            setResultData([]); // 空的结果数据，因为实际数据在gridData中\n          }\n        }\n      } catch (error) {\n        console.error('解析savedGridData失败:', error);\n      }\n    } else if (savedStateString) {\n      // 只有commissionState，没有gridData的情况\n      try {\n        const savedState = JSON.parse(savedStateString);\n        console.log('只恢复commissionState，没有gridData');\n        setActiveStep(savedState.activeStep || 0);\n        if (savedState.fileData && savedState.fileData.file_id) {\n          setFileData(savedState.fileData);\n        }\n        setSelectedWorksheet(savedState.selectedWorksheet || '');\n        setProcessParams(savedState.processParams || {\n          startCol: '',\n          endCol: '',\n          perHourRate: '',\n          cbuCarHourRate: '',\n          commissionRate: ''\n        });\n        setResultData(savedState.resultData || null);\n      } catch (error) {\n        console.error('解析commissionState失败:', error);\n      }\n    }\n  }, []);\n  const handleFileUpload = data => {\n    setFileData(data);\n    setActiveStep(1);\n  };\n  const handleWorksheetSelect = worksheet => {\n    setSelectedWorksheet(worksheet);\n    setActiveStep(2);\n  };\n  const handleProcessSubmit = (params, result) => {\n    setProcessParams(params);\n    setResultData(result);\n    setActiveStep(3);\n    // 当获取新的处理结果时，清除之前的gridData\n    setGridData([]);\n    localStorage.removeItem('savedGridData');\n\n    // 保存当前状态到localStorage，以便页面刷新后能恢复\n    const stateToSave = {\n      activeStep: 3,\n      fileData,\n      selectedWorksheet,\n      processParams: params,\n      resultData: result\n    };\n    try {\n      localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n      console.log('保存commissionState到localStorage');\n    } catch (error) {\n      console.error('保存commissionState失败:', error);\n    }\n  };\n  const handleReset = () => {\n    setActiveStep(0);\n    setFileData(null);\n    setSelectedWorksheet('');\n    setProcessParams({\n      startCol: '',\n      endCol: '',\n      perHourRate: '',\n      cbuCarHourRate: '',\n      commissionRate: ''\n    });\n    setResultData(null);\n    setGridData([]);\n\n    // 清除localStorage中的状态\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    console.log('重置所有状态并清除localStorage');\n  };\n  const handleError = errorMessage => {\n    setError(errorMessage);\n  };\n  const handleCloseError = () => {\n    setError('');\n  };\n\n  // 处理ResultDisplay组件中数据变化的回调\n  const handleGridDataChange = newData => {\n    // 避免不必要的更新，只有当数据真正变化时才更新状态\n    if (newData && newData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次更新时间不足500ms，则跳过此次更新\n      if (now - lastUpdateTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次的数据是否相同\n      const currentDataString = JSON.stringify(newData);\n      const lastDataString = lastGridDataRef.current;\n      if (lastDataString !== currentDataString) {\n        // console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\n        lastGridDataRef.current = currentDataString;\n        lastUpdateTimeRef.current = now;\n        setGridData(newData);\n\n        // 当gridData更新时，保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(newData));\n          console.log('App.js中保存gridData到localStorage:', newData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n\n        // 同时更新commissionState以保持同步\n        const stateToSave = {\n          activeStep: 3,\n          fileData,\n          selectedWorksheet,\n          processParams,\n          resultData\n        };\n        try {\n          localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n          // console.log('更新commissionState到localStorage');\n        } catch (error) {\n          console.error('更新commissionState失败:', error);\n        }\n      } else {\n        // console.log('接收到的gridData与上次相同，跳过更新');\n      }\n    }\n  };\n\n  // 切换主题模式\n  const toggleThemeMode = () => {\n    const newMode = themeMode === 'light' ? 'dark' : 'light';\n    setThemeMode(newMode);\n    localStorage.setItem('themeMode', newMode);\n    console.log('切换主题模式到:', newMode);\n  };\n\n  // 清除所有保存的状态\n  const clearAllSavedState = () => {\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    setError('所有保存的状态已清除，请刷新页面');\n    console.log('手动清除所有保存的状态');\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"bottom-right\",\n      reverseOrder: false,\n      gutter: 8,\n      containerClassName: \"\",\n      containerStyle: {},\n      toastOptions: {\n        // 默认选项\n        className: '',\n        duration: 3000,\n        style: {\n          background: '#363636',\n          color: '#fff',\n          borderRadius: '12px',\n          fontSize: '14px',\n          fontWeight: '500',\n          padding: '12px 16px',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n        },\n        // 成功通知\n        success: {\n          duration: 3000,\n          style: {\n            background: '#4caf50'\n          }\n        },\n        // 错误通知\n        error: {\n          duration: 4000,\n          style: {\n            background: '#f44336'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '100vh',\n          backgroundColor: 'background.default',\n          py: {\n            xs: 2,\n            md: 4\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: \"lg\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalculateIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                component: \"h1\",\n                sx: {\n                  color: 'text.primary',\n                  fontWeight: 600\n                },\n                children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u5DE5\\u5177\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: `切换到${themeMode === 'light' ? '深色' : '浅色'}模式`,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: toggleThemeMode,\n                  color: \"primary\",\n                  children: themeMode === 'light' ? /*#__PURE__*/_jsxDEV(DarkModeIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 46\n                  }, this) : /*#__PURE__*/_jsxDEV(LightModeIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 65\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u6E05\\u9664\\u6240\\u6709\\u4FDD\\u5B58\\u7684\\u72B6\\u6001\\uFF08\\u5982\\u679C\\u9047\\u5230\\u95EE\\u9898\\u8BF7\\u70B9\\u51FB\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: clearAllSavedState,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteSweepIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 3,\n            sx: {\n              p: {\n                xs: 2,\n                md: 4\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(Stepper, {\n                activeStep: activeStep,\n                children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n                  children: /*#__PURE__*/_jsxDEV(StepLabel, {\n                    children: label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)\n                }, label, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minHeight: '400px'\n              },\n              children: [activeStep === 0 && /*#__PURE__*/_jsxDEV(FileUpload, {\n                onFileUpload: handleFileUpload,\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), activeStep === 1 && fileData && /*#__PURE__*/_jsxDEV(WorksheetSelect, {\n                worksheets: fileData.worksheets,\n                onSelect: handleWorksheetSelect,\n                onBack: () => setActiveStep(0),\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), activeStep === 2 && fileData && selectedWorksheet && /*#__PURE__*/_jsxDEV(ProcessForm, {\n                fileId: fileData.file_id,\n                worksheet: selectedWorksheet,\n                onSubmit: handleProcessSubmit,\n                onBack: () => setActiveStep(1),\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), activeStep === 3 && (resultData || gridData.length > 0) && /*#__PURE__*/_jsxDEV(ResultDisplay, {\n                data: resultData || [],\n                fileId: fileData === null || fileData === void 0 ? void 0 : fileData.file_id,\n                onReset: handleReset,\n                onError: handleError,\n                savedGridData: gridData,\n                onDataChange: handleGridDataChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ErrorSnackbar, {\n        open: !!error,\n        message: error,\n        onClose: handleCloseError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(App, \"LeDmHHWZy6kRGwCoLTbX4Pi5hN0=\", false, function () {\n  return [useMediaQuery];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Container", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "CssBaseline", "ThemeProvider", "createTheme", "<PERSON><PERSON><PERSON>", "IconButton", "Fade", "Slide", "useMediaQuery", "DeleteSweepIcon", "CalculateIcon", "LightModeIcon", "DarkModeIcon", "FileUpload", "WorksheetSelect", "ProcessForm", "ResultDisplay", "ErrorSnackbar", "Toaster", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "createAppTheme", "mode", "palette", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "join", "h4", "fontWeight", "fontSize", "h6", "shape", "borderRadius", "components", "MuiPaper", "styleOverrides", "root", "backgroundImage", "MuiB<PERSON>on", "textTransform", "padding", "MuiCard", "boxShadow", "steps", "App", "_s", "themeMode", "setThemeMode", "savedMode", "localStorage", "getItem", "theme", "activeStep", "setActiveStep", "fileData", "setFileData", "selectedWorksheet", "setSelectedWorksheet", "processParams", "setProcessParams", "startCol", "endCol", "perHourRate", "cbuCarHourRate", "commissionRate", "resultData", "setResultData", "error", "setError", "isMobile", "breakpoints", "down", "gridData", "setGridData", "lastGridDataRef", "lastUpdateTimeRef", "length", "saveTimeout", "setTimeout", "setItem", "JSON", "stringify", "console", "clearTimeout", "savedGridDataString", "savedStateString", "log", "hasSavedGridData", "hasSavedState", "savedData", "parse", "savedState", "file_id", "recoveredFileId", "Date", "now", "worksheets", "handleFileUpload", "data", "handleWorksheetSelect", "worksheet", "handleProcessSubmit", "params", "result", "removeItem", "stateToSave", "handleReset", "handleError", "errorMessage", "handleCloseError", "handleGridDataChange", "newData", "current", "currentDataString", "lastDataString", "toggleThemeMode", "newMode", "clearAllSavedState", "children", "position", "reverseOrder", "gutter", "containerClassName", "containerStyle", "toastOptions", "className", "duration", "style", "color", "success", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "minHeight", "backgroundColor", "py", "xs", "md", "max<PERSON><PERSON><PERSON>", "display", "justifyContent", "alignItems", "mb", "gap", "variant", "component", "title", "onClick", "elevation", "p", "map", "label", "onFileUpload", "onError", "onSelect", "onBack", "fileId", "onSubmit", "onReset", "savedGridData", "onDataChange", "open", "message", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Paper,\n  CssBaseline,\n  ThemeProvider,\n  createTheme,\n  Tooltip,\n  IconButton,\n  Fade,\n  Slide,\n  useMediaQuery\n} from '@mui/material';\nimport DeleteSweepIcon from '@mui/icons-material/DeleteSweep';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport LightModeIcon from '@mui/icons-material/LightMode';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport FileUpload from './components/FileUpload';\nimport WorksheetSelect from './components/WorksheetSelect';\nimport ProcessForm from './components/ProcessForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport ErrorSnackbar from './components/ErrorSnackbar';\nimport { Toaster } from 'react-hot-toast';\n\n// 创建主题函数\nconst createAppTheme = (mode) => createTheme({\n  palette: {\n    mode,\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n    },\n    secondary: {\n      main: '#9c27b0',\n      light: '#ba68c8',\n      dark: '#7b1fa2',\n    },\n    background: {\n      default: mode === 'light' ? '#f5f5f5' : '#121212',\n      paper: mode === 'light' ? '#ffffff' : '#1e1e1e',\n    },\n    text: {\n      primary: mode === 'light' ? '#212121' : '#ffffff',\n      secondary: mode === 'light' ? '#757575' : '#b3b3b3',\n    },\n  },\n  typography: {\n    fontFamily: [\n      '-apple-system',\n      'BlinkMacSystemFont',\n      '\"Segoe UI\"',\n      'Roboto',\n      '\"Helvetica Neue\"',\n      'Arial',\n      'sans-serif',\n    ].join(','),\n    h4: {\n      fontWeight: 600,\n      fontSize: '2rem',\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1.25rem',\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n          borderRadius: '8px',\n          padding: '8px 16px',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: mode === 'light'\n            ? '0 2px 8px rgba(0,0,0,0.1)'\n            : '0 2px 8px rgba(0,0,0,0.3)',\n        },\n      },\n    },\n  },\n});\n\nconst steps = ['上传Excel文件', '选择工作表', '设置参数', '查看结果'];\n\nfunction App() {\n  // 主题模式状态\n  const [themeMode, setThemeMode] = useState(() => {\n    const savedMode = localStorage.getItem('themeMode');\n    return savedMode || 'light';\n  });\n\n  // 创建主题\n  const theme = createAppTheme(themeMode);\n\n  const [activeStep, setActiveStep] = useState(0);\n  const [fileData, setFileData] = useState(null);\n  const [selectedWorksheet, setSelectedWorksheet] = useState('');\n  const [processParams, setProcessParams] = useState({\n    startCol: '',\n    endCol: '',\n    perHourRate: '',\n    cbuCarHourRate: '',\n    commissionRate: ''\n  });\n  const [resultData, setResultData] = useState(null);\n  const [error, setError] = useState('');\n\n  // 响应式设计\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  \n  // 保存ResultDisplay组件中的表格数据\n  const [gridData, setGridData] = useState([]);\n  \n  // 使用ref来避免无限循环\n  const lastGridDataRef = useRef(null);\n  const lastUpdateTimeRef = useRef(0);\n  \n\n\n  // 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\n  useEffect(() => {\n    // 当gridData变化且不为空时，延迟保存到localStorage\n    if (gridData && gridData.length > 0) {\n      // 使用防抖技术，延迟1秒后保存\n      const saveTimeout = setTimeout(() => {\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(gridData));\n          // console.log('保存gridData到localStorage:', gridData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n      }, 1000);\n      \n      // 清除上一个定时器\n      return () => clearTimeout(saveTimeout);\n    }\n  }, [gridData]);\n\n  // 在组件加载时，尝试从localStorage恢复数据\n  useEffect(() => {\n    // 只在初始化时执行一次\n    const savedGridDataString = localStorage.getItem('savedGridData');\n    const savedStateString = localStorage.getItem('commissionState');\n\n    console.log('检查localStorage恢复:', {\n      hasSavedGridData: !!savedGridDataString,\n      hasSavedState: !!savedStateString\n    });\n\n    // 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\n    if (savedGridDataString) {\n      try {\n        const savedData = JSON.parse(savedGridDataString);\n        if (savedData && savedData.length > 0) {\n          setGridData(savedData);\n          console.log('从localStorage恢复gridData:', savedData.length);\n\n          // 如果有savedGridData，说明应该在ResultDisplay页面\n          // 需要恢复所有必要的状态以支持ResultDisplay\n          if (savedStateString) {\n            try {\n              const savedState = JSON.parse(savedStateString);\n              console.log('从localStorage恢复commissionState以支持ResultDisplay');\n\n              // 恢复到ResultDisplay页面需要的所有状态\n              setActiveStep(3); // 直接跳转到ResultDisplay\n\n              if (savedState.fileData && savedState.fileData.file_id) {\n                setFileData(savedState.fileData);\n              }\n\n              setSelectedWorksheet(savedState.selectedWorksheet || '');\n              setProcessParams(savedState.processParams || {\n                startCol: '',\n                endCol: '',\n                perHourRate: '',\n                cbuCarHourRate: '',\n                commissionRate: ''\n              });\n\n              setResultData(savedState.resultData || null);\n            } catch (error) {\n              console.error('解析commissionState失败:', error);\n              // 即使commissionState解析失败，也要跳转到ResultDisplay\n              setActiveStep(3);\n            }\n          } else {\n            // 没有commissionState但有gridData，创建最小状态支持ResultDisplay\n            console.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');\n            setActiveStep(3);\n\n            // 创建最小的状态以支持ResultDisplay正常工作\n            // 使用时间戳作为fileId，确保唯一性\n            const recoveredFileId = `recovered_${Date.now()}`;\n            setFileData({ file_id: recoveredFileId, worksheets: ['recovered'] });\n            setSelectedWorksheet('recovered');\n            setResultData([]); // 空的结果数据，因为实际数据在gridData中\n          }\n        }\n      } catch (error) {\n        console.error('解析savedGridData失败:', error);\n      }\n    } else if (savedStateString) {\n      // 只有commissionState，没有gridData的情况\n      try {\n        const savedState = JSON.parse(savedStateString);\n        console.log('只恢复commissionState，没有gridData');\n\n        setActiveStep(savedState.activeStep || 0);\n\n        if (savedState.fileData && savedState.fileData.file_id) {\n          setFileData(savedState.fileData);\n        }\n\n        setSelectedWorksheet(savedState.selectedWorksheet || '');\n        setProcessParams(savedState.processParams || {\n          startCol: '',\n          endCol: '',\n          perHourRate: '',\n          cbuCarHourRate: '',\n          commissionRate: ''\n        });\n\n        setResultData(savedState.resultData || null);\n      } catch (error) {\n        console.error('解析commissionState失败:', error);\n      }\n    }\n  }, []);\n\n  const handleFileUpload = (data) => {\n    setFileData(data);\n    setActiveStep(1);\n  };\n\n  const handleWorksheetSelect = (worksheet) => {\n    setSelectedWorksheet(worksheet);\n    setActiveStep(2);\n  };\n\n  const handleProcessSubmit = (params, result) => {\n    setProcessParams(params);\n    setResultData(result);\n    setActiveStep(3);\n    // 当获取新的处理结果时，清除之前的gridData\n    setGridData([]);\n    localStorage.removeItem('savedGridData');\n\n    // 保存当前状态到localStorage，以便页面刷新后能恢复\n    const stateToSave = {\n      activeStep: 3,\n      fileData,\n      selectedWorksheet,\n      processParams: params,\n      resultData: result\n    };\n\n    try {\n      localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n      console.log('保存commissionState到localStorage');\n    } catch (error) {\n      console.error('保存commissionState失败:', error);\n    }\n  };\n\n  const handleReset = () => {\n    setActiveStep(0);\n    setFileData(null);\n    setSelectedWorksheet('');\n    setProcessParams({\n      startCol: '',\n      endCol: '',\n      perHourRate: '',\n      cbuCarHourRate: '',\n      commissionRate: ''\n    });\n    setResultData(null);\n    setGridData([]);\n    \n    // 清除localStorage中的状态\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    \n    console.log('重置所有状态并清除localStorage');\n  };\n\n  const handleError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleCloseError = () => {\n    setError('');\n  };\n  \n  // 处理ResultDisplay组件中数据变化的回调\n  const handleGridDataChange = (newData) => {\n    // 避免不必要的更新，只有当数据真正变化时才更新状态\n    if (newData && newData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次更新时间不足500ms，则跳过此次更新\n      if (now - lastUpdateTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次的数据是否相同\n      const currentDataString = JSON.stringify(newData);\n      const lastDataString = lastGridDataRef.current;\n\n      if (lastDataString !== currentDataString) {\n        // console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\n        lastGridDataRef.current = currentDataString;\n        lastUpdateTimeRef.current = now;\n        setGridData(newData);\n\n        // 当gridData更新时，保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(newData));\n          console.log('App.js中保存gridData到localStorage:', newData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n\n        // 同时更新commissionState以保持同步\n        const stateToSave = {\n          activeStep: 3,\n          fileData,\n          selectedWorksheet,\n          processParams,\n          resultData\n        };\n\n        try {\n          localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n          // console.log('更新commissionState到localStorage');\n        } catch (error) {\n          console.error('更新commissionState失败:', error);\n        }\n      } else {\n        // console.log('接收到的gridData与上次相同，跳过更新');\n      }\n    }\n  };\n\n\n\n  // 切换主题模式\n  const toggleThemeMode = () => {\n    const newMode = themeMode === 'light' ? 'dark' : 'light';\n    setThemeMode(newMode);\n    localStorage.setItem('themeMode', newMode);\n    console.log('切换主题模式到:', newMode);\n  };\n\n  // 清除所有保存的状态\n  const clearAllSavedState = () => {\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    setError('所有保存的状态已清除，请刷新页面');\n    console.log('手动清除所有保存的状态');\n  };\n\n  return (\n    <>\n      <Toaster\n        position=\"bottom-right\"\n        reverseOrder={false}\n        gutter={8}\n        containerClassName=\"\"\n        containerStyle={{}}\n        toastOptions={{\n          // 默认选项\n          className: '',\n          duration: 3000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n          // 成功通知\n          success: {\n            duration: 3000,\n            style: {\n              background: '#4caf50',\n            },\n          },\n          // 错误通知\n          error: {\n            duration: 4000,\n            style: {\n              background: '#f44336',\n            },\n          },\n        }}\n      />\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Box\n          sx={{\n            minHeight: '100vh',\n            backgroundColor: 'background.default',\n            py: { xs: 2, md: 4 },\n          }}\n        >\n          <Container maxWidth=\"lg\">\n            {/* 头部区域 */}\n            <Box\n              sx={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 4,\n              }}\n            >\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <CalculateIcon\n                  sx={{\n                    fontSize: 40,\n                    color: 'primary.main',\n                  }}\n                />\n                <Typography\n                  variant=\"h4\"\n                  component=\"h1\"\n                  sx={{\n                    color: 'text.primary',\n                    fontWeight: 600,\n                  }}\n                >\n                  佣金计算工具\n                </Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={`切换到${themeMode === 'light' ? '深色' : '浅色'}模式`}>\n                  <IconButton\n                    onClick={toggleThemeMode}\n                    color=\"primary\"\n                  >\n                    {themeMode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}\n                  </IconButton>\n                </Tooltip>\n\n                <Tooltip title=\"清除所有保存的状态（如果遇到问题请点击）\">\n                  <IconButton\n                    onClick={clearAllSavedState}\n                    color=\"error\"\n                  >\n                    <DeleteSweepIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n            </Box>\n\n            {/* 主要内容区域 */}\n            <Paper\n              elevation={3}\n              sx={{\n                p: { xs: 2, md: 4 },\n              }}\n            >\n              {/* 步骤指示器 */}\n              <Box sx={{ mb: 4 }}>\n                <Stepper activeStep={activeStep}>\n                  {steps.map((label) => (\n                    <Step key={label}>\n                      <StepLabel>{label}</StepLabel>\n                    </Step>\n                  ))}\n                </Stepper>\n              </Box>\n\n              {/* 步骤内容 */}\n              <Box sx={{ minHeight: '400px' }}>\n                {activeStep === 0 && (\n                  <FileUpload onFileUpload={handleFileUpload} onError={handleError} />\n                )}\n\n                {activeStep === 1 && fileData && (\n                  <WorksheetSelect\n                    worksheets={fileData.worksheets}\n                    onSelect={handleWorksheetSelect}\n                    onBack={() => setActiveStep(0)}\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 2 && fileData && selectedWorksheet && (\n                  <ProcessForm\n                    fileId={fileData.file_id}\n                    worksheet={selectedWorksheet}\n                    onSubmit={handleProcessSubmit}\n                    onBack={() => setActiveStep(1)}\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 3 && (resultData || gridData.length > 0) && (\n                  <ResultDisplay\n                    data={resultData || []}\n                    fileId={fileData?.file_id}\n                    onReset={handleReset}\n                    onError={handleError}\n                    savedGridData={gridData}\n                    onDataChange={handleGridDataChange}\n                  />\n                )}\n              </Box>\n            </Paper>\n          </Container>\n        </Box>\n\n        <ErrorSnackbar\n          open={!!error}\n          message={error}\n          onClose={handleCloseError}\n        />\n      </ThemeProvider>\n    </>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,WAAW,EACXC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,aAAa,QACR,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,IAAI,IAAKrB,WAAW,CAAC;EAC3CsB,OAAO,EAAE;IACPD,IAAI;IACJE,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAER,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;MACjDS,KAAK,EAAET,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;IACxC,CAAC;IACDU,IAAI,EAAE;MACJR,OAAO,EAAEF,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;MACjDM,SAAS,EAAEN,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;IAC5C;EACF,CAAC;EACDW,UAAU,EAAE;IACVC,UAAU,EAAE,CACV,eAAe,EACf,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,OAAO,EACP,YAAY,CACb,CAACC,IAAI,CAAC,GAAG,CAAC;IACXC,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,EAAE,EAAE;MACFF,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDE,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,QAAQ,EAAE;MACRC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,eAAe,EAAE;QACnB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,aAAa,EAAE,MAAM;UACrBX,UAAU,EAAE,GAAG;UACfI,YAAY,EAAE,KAAK;UACnBQ,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDC,OAAO,EAAE;MACPN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJM,SAAS,EAAE7B,IAAI,KAAK,OAAO,GACvB,2BAA2B,GAC3B;QACN;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAM8B,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AAEpD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,MAAM;IAC/C,MAAMoE,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,OAAOF,SAAS,IAAI,OAAO;EAC7B,CAAC,CAAC;;EAEF;EACA,MAAMG,KAAK,GAAGvC,cAAc,CAACkC,SAAS,CAAC;EAEvC,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC4E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC;IACjDgF,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuF,KAAK,EAAEC,QAAQ,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMyF,QAAQ,GAAGxE,aAAa,CAACsD,KAAK,CAACmB,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAG5D;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM8F,eAAe,GAAG5F,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM6F,iBAAiB,GAAG7F,MAAM,CAAC,CAAC,CAAC;;EAInC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,IAAI2F,QAAQ,IAAIA,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACnC;MACA,MAAMC,WAAW,GAAGC,UAAU,CAAC,MAAM;QACnC,IAAI;UACF7B,YAAY,CAAC8B,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOL,KAAK,EAAE;UACde,OAAO,CAACf,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,OAAO,MAAMgB,YAAY,CAACN,WAAW,CAAC;IACxC;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;;EAEd;EACA3F,SAAS,CAAC,MAAM;IACd;IACA,MAAMuG,mBAAmB,GAAGnC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACjE,MAAMmC,gBAAgB,GAAGpC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAEhEgC,OAAO,CAACI,GAAG,CAAC,mBAAmB,EAAE;MAC/BC,gBAAgB,EAAE,CAAC,CAACH,mBAAmB;MACvCI,aAAa,EAAE,CAAC,CAACH;IACnB,CAAC,CAAC;;IAEF;IACA,IAAID,mBAAmB,EAAE;MACvB,IAAI;QACF,MAAMK,SAAS,GAAGT,IAAI,CAACU,KAAK,CAACN,mBAAmB,CAAC;QACjD,IAAIK,SAAS,IAAIA,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;UACrCH,WAAW,CAACgB,SAAS,CAAC;UACtBP,OAAO,CAACI,GAAG,CAAC,0BAA0B,EAAEG,SAAS,CAACb,MAAM,CAAC;;UAEzD;UACA;UACA,IAAIS,gBAAgB,EAAE;YACpB,IAAI;cACF,MAAMM,UAAU,GAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC;cAC/CH,OAAO,CAACI,GAAG,CAAC,gDAAgD,CAAC;;cAE7D;cACAjC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;;cAElB,IAAIsC,UAAU,CAACrC,QAAQ,IAAIqC,UAAU,CAACrC,QAAQ,CAACsC,OAAO,EAAE;gBACtDrC,WAAW,CAACoC,UAAU,CAACrC,QAAQ,CAAC;cAClC;cAEAG,oBAAoB,CAACkC,UAAU,CAACnC,iBAAiB,IAAI,EAAE,CAAC;cACxDG,gBAAgB,CAACgC,UAAU,CAACjC,aAAa,IAAI;gBAC3CE,QAAQ,EAAE,EAAE;gBACZC,MAAM,EAAE,EAAE;gBACVC,WAAW,EAAE,EAAE;gBACfC,cAAc,EAAE,EAAE;gBAClBC,cAAc,EAAE;cAClB,CAAC,CAAC;cAEFE,aAAa,CAACyB,UAAU,CAAC1B,UAAU,IAAI,IAAI,CAAC;YAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;cACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;cAC5C;cACAd,aAAa,CAAC,CAAC,CAAC;YAClB;UACF,CAAC,MAAM;YACL;YACA6B,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC;YACjEjC,aAAa,CAAC,CAAC,CAAC;;YAEhB;YACA;YACA,MAAMwC,eAAe,GAAG,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YACjDxC,WAAW,CAAC;cAAEqC,OAAO,EAAEC,eAAe;cAAEG,UAAU,EAAE,CAAC,WAAW;YAAE,CAAC,CAAC;YACpEvC,oBAAoB,CAAC,WAAW,CAAC;YACjCS,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;UACrB;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,MAAM,IAAIkB,gBAAgB,EAAE;MAC3B;MACA,IAAI;QACF,MAAMM,UAAU,GAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC;QAC/CH,OAAO,CAACI,GAAG,CAAC,+BAA+B,CAAC;QAE5CjC,aAAa,CAACsC,UAAU,CAACvC,UAAU,IAAI,CAAC,CAAC;QAEzC,IAAIuC,UAAU,CAACrC,QAAQ,IAAIqC,UAAU,CAACrC,QAAQ,CAACsC,OAAO,EAAE;UACtDrC,WAAW,CAACoC,UAAU,CAACrC,QAAQ,CAAC;QAClC;QAEAG,oBAAoB,CAACkC,UAAU,CAACnC,iBAAiB,IAAI,EAAE,CAAC;QACxDG,gBAAgB,CAACgC,UAAU,CAACjC,aAAa,IAAI;UAC3CE,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAE,EAAE;UACVC,WAAW,EAAE,EAAE;UACfC,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE;QAClB,CAAC,CAAC;QAEFE,aAAa,CAACyB,UAAU,CAAC1B,UAAU,IAAI,IAAI,CAAC;MAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8B,gBAAgB,GAAIC,IAAI,IAAK;IACjC3C,WAAW,CAAC2C,IAAI,CAAC;IACjB7C,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAM8C,qBAAqB,GAAIC,SAAS,IAAK;IAC3C3C,oBAAoB,CAAC2C,SAAS,CAAC;IAC/B/C,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMgD,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC9C5C,gBAAgB,CAAC2C,MAAM,CAAC;IACxBpC,aAAa,CAACqC,MAAM,CAAC;IACrBlD,aAAa,CAAC,CAAC,CAAC;IAChB;IACAoB,WAAW,CAAC,EAAE,CAAC;IACfxB,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;;IAExC;IACA,MAAMC,WAAW,GAAG;MAClBrD,UAAU,EAAE,CAAC;MACbE,QAAQ;MACRE,iBAAiB;MACjBE,aAAa,EAAE4C,MAAM;MACrBrC,UAAU,EAAEsC;IACd,CAAC;IAED,IAAI;MACFtD,YAAY,CAAC8B,OAAO,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAACwB,WAAW,CAAC,CAAC;MACpEvB,OAAO,CAACI,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxBrD,aAAa,CAAC,CAAC,CAAC;IAChBE,WAAW,CAAC,IAAI,CAAC;IACjBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,gBAAgB,CAAC;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFE,aAAa,CAAC,IAAI,CAAC;IACnBO,WAAW,CAAC,EAAE,CAAC;;IAEf;IACAxB,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;IACxCvD,YAAY,CAACuD,UAAU,CAAC,iBAAiB,CAAC;IAE1CtB,OAAO,CAACI,GAAG,CAAC,uBAAuB,CAAC;EACtC,CAAC;EAED,MAAMqB,WAAW,GAAIC,YAAY,IAAK;IACpCxC,QAAQ,CAACwC,YAAY,CAAC;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzC,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAIC,OAAO,IAAK;IACxC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACnC,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMmB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIA,GAAG,GAAGpB,iBAAiB,CAACqC,OAAO,GAAG,GAAG,EAAE;QACzC;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGjC,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC;MACjD,MAAMG,cAAc,GAAGxC,eAAe,CAACsC,OAAO;MAE9C,IAAIE,cAAc,KAAKD,iBAAiB,EAAE;QACxC;QACAvC,eAAe,CAACsC,OAAO,GAAGC,iBAAiB;QAC3CtC,iBAAiB,CAACqC,OAAO,GAAGjB,GAAG;QAC/BtB,WAAW,CAACsC,OAAO,CAAC;;QAEpB;QACA,IAAI;UACF9D,YAAY,CAAC8B,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC,CAAC;UAC9D7B,OAAO,CAACI,GAAG,CAAC,iCAAiC,EAAEyB,OAAO,CAACnC,MAAM,CAAC;QAChE,CAAC,CAAC,OAAOT,KAAK,EAAE;UACde,OAAO,CAACf,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;;QAEA;QACA,MAAMsC,WAAW,GAAG;UAClBrD,UAAU,EAAE,CAAC;UACbE,QAAQ;UACRE,iBAAiB;UACjBE,aAAa;UACbO;QACF,CAAC;QAED,IAAI;UACFhB,YAAY,CAAC8B,OAAO,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAACwB,WAAW,CAAC,CAAC;UACpE;QACF,CAAC,CAAC,OAAOtC,KAAK,EAAE;UACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;MACF,CAAC,MAAM;QACL;MAAA;IAEJ;EACF,CAAC;;EAID;EACA,MAAMgD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,OAAO,GAAGtE,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACxDC,YAAY,CAACqE,OAAO,CAAC;IACrBnE,YAAY,CAAC8B,OAAO,CAAC,WAAW,EAAEqC,OAAO,CAAC;IAC1ClC,OAAO,CAACI,GAAG,CAAC,UAAU,EAAE8B,OAAO,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpE,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;IACxCvD,YAAY,CAACuD,UAAU,CAAC,iBAAiB,CAAC;IAC1CpC,QAAQ,CAAC,kBAAkB,CAAC;IAC5Bc,OAAO,CAACI,GAAG,CAAC,aAAa,CAAC;EAC5B,CAAC;EAED,oBACE7E,OAAA,CAAAE,SAAA;IAAA2G,QAAA,gBACE7G,OAAA,CAACF,OAAO;MACNgH,QAAQ,EAAC,cAAc;MACvBC,YAAY,EAAE,KAAM;MACpBC,MAAM,EAAE,CAAE;MACVC,kBAAkB,EAAC,EAAE;MACrBC,cAAc,EAAE,CAAC,CAAE;MACnBC,YAAY,EAAE;QACZ;QACAC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACL3G,UAAU,EAAE,SAAS;UACrB4G,KAAK,EAAE,MAAM;UACbhG,YAAY,EAAE,MAAM;UACpBH,QAAQ,EAAE,MAAM;UAChBD,UAAU,EAAE,KAAK;UACjBY,OAAO,EAAE,WAAW;UACpBE,SAAS,EAAE;QACb,CAAC;QACD;QACAuF,OAAO,EAAE;UACPH,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACL3G,UAAU,EAAE;UACd;QACF,CAAC;QACD;QACA+C,KAAK,EAAE;UACL2D,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACL3G,UAAU,EAAE;UACd;QACF;MACF;IAAE;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF5H,OAAA,CAAClB,aAAa;MAAC4D,KAAK,EAAEA,KAAM;MAAAmE,QAAA,gBAC1B7G,OAAA,CAACnB,WAAW;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf5H,OAAA,CAACzB,GAAG;QACFsJ,EAAE,EAAE;UACFC,SAAS,EAAE,OAAO;UAClBC,eAAe,EAAE,oBAAoB;UACrCC,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QACrB,CAAE;QAAArB,QAAA,eAEF7G,OAAA,CAAC1B,SAAS;UAAC6J,QAAQ,EAAC,IAAI;UAAAtB,QAAA,gBAEtB7G,OAAA,CAACzB,GAAG;YACFsJ,EAAE,EAAE;cACFO,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBC,EAAE,EAAE;YACN,CAAE;YAAA1B,QAAA,gBAEF7G,OAAA,CAACzB,GAAG;cAACsJ,EAAE,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAA3B,QAAA,gBACzD7G,OAAA,CAACV,aAAa;gBACZuI,EAAE,EAAE;kBACFzG,QAAQ,EAAE,EAAE;kBACZmG,KAAK,EAAE;gBACT;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5H,OAAA,CAACxB,UAAU;gBACTiK,OAAO,EAAC,IAAI;gBACZC,SAAS,EAAC,IAAI;gBACdb,EAAE,EAAE;kBACFN,KAAK,EAAE,cAAc;kBACrBpG,UAAU,EAAE;gBACd,CAAE;gBAAA0F,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN5H,OAAA,CAACzB,GAAG;cAACsJ,EAAE,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAEI,GAAG,EAAE;cAAE,CAAE;cAAA3B,QAAA,gBACnC7G,OAAA,CAAChB,OAAO;gBAAC2J,KAAK,EAAE,MAAMtG,SAAS,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,IAAK;gBAAAwE,QAAA,eAC5D7G,OAAA,CAACf,UAAU;kBACT2J,OAAO,EAAElC,eAAgB;kBACzBa,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAEdxE,SAAS,KAAK,OAAO,gBAAGrC,OAAA,CAACR,YAAY;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG5H,OAAA,CAACT,aAAa;oBAAAkI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEV5H,OAAA,CAAChB,OAAO;gBAAC2J,KAAK,EAAC,0HAAsB;gBAAA9B,QAAA,eACnC7G,OAAA,CAACf,UAAU;kBACT2J,OAAO,EAAEhC,kBAAmB;kBAC5BW,KAAK,EAAC,OAAO;kBAAAV,QAAA,eAEb7G,OAAA,CAACX,eAAe;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5H,OAAA,CAACpB,KAAK;YACJiK,SAAS,EAAE,CAAE;YACbhB,EAAE,EAAE;cACFiB,CAAC,EAAE;gBAAEb,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE;YACpB,CAAE;YAAArB,QAAA,gBAGF7G,OAAA,CAACzB,GAAG;cAACsJ,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,eACjB7G,OAAA,CAACvB,OAAO;gBAACkE,UAAU,EAAEA,UAAW;gBAAAkE,QAAA,EAC7B3E,KAAK,CAAC6G,GAAG,CAAEC,KAAK,iBACfhJ,OAAA,CAACtB,IAAI;kBAAAmI,QAAA,eACH7G,OAAA,CAACrB,SAAS;oBAAAkI,QAAA,EAAEmC;kBAAK;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC,GADrBoB,KAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGN5H,OAAA,CAACzB,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,SAAS,EAAE;cAAQ,CAAE;cAAAjB,QAAA,GAC7BlE,UAAU,KAAK,CAAC,iBACf3C,OAAA,CAACP,UAAU;gBAACwJ,YAAY,EAAEzD,gBAAiB;gBAAC0D,OAAO,EAAEhD;cAAY;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACpE,EAEAjF,UAAU,KAAK,CAAC,IAAIE,QAAQ,iBAC3B7C,OAAA,CAACN,eAAe;gBACd6F,UAAU,EAAE1C,QAAQ,CAAC0C,UAAW;gBAChC4D,QAAQ,EAAEzD,qBAAsB;gBAChC0D,MAAM,EAAEA,CAAA,KAAMxG,aAAa,CAAC,CAAC,CAAE;gBAC/BsG,OAAO,EAAEhD;cAAY;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACF,EAEAjF,UAAU,KAAK,CAAC,IAAIE,QAAQ,IAAIE,iBAAiB,iBAChD/C,OAAA,CAACL,WAAW;gBACV0J,MAAM,EAAExG,QAAQ,CAACsC,OAAQ;gBACzBQ,SAAS,EAAE5C,iBAAkB;gBAC7BuG,QAAQ,EAAE1D,mBAAoB;gBAC9BwD,MAAM,EAAEA,CAAA,KAAMxG,aAAa,CAAC,CAAC,CAAE;gBAC/BsG,OAAO,EAAEhD;cAAY;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACF,EAEAjF,UAAU,KAAK,CAAC,KAAKa,UAAU,IAAIO,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,iBACtDnE,OAAA,CAACJ,aAAa;gBACZ6F,IAAI,EAAEjC,UAAU,IAAI,EAAG;gBACvB6F,MAAM,EAAExG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC,OAAQ;gBAC1BoE,OAAO,EAAEtD,WAAY;gBACrBiD,OAAO,EAAEhD,WAAY;gBACrBsD,aAAa,EAAEzF,QAAS;gBACxB0F,YAAY,EAAEpD;cAAqB;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN5H,OAAA,CAACH,aAAa;QACZ6J,IAAI,EAAE,CAAC,CAAChG,KAAM;QACdiG,OAAO,EAAEjG,KAAM;QACfkG,OAAO,EAAExD;MAAiB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAAA,eAChB,CAAC;AAEP;AAACxF,EAAA,CAzbQD,GAAG;EAAA,QAwBO/C,aAAa;AAAA;AAAAyK,EAAA,GAxBvB1H,GAAG;AA2bZ,eAAeA,GAAG;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}