{"ast": null, "code": "import { GridFilterInputValue } from '../components/panel/filterPanel/GridFilterInputValue';\nimport { escapeRegExp } from '../utils/utils';\nimport { GridFilterInputMultipleValue } from '../components/panel/filterPanel/GridFilterInputMultipleValue';\nimport { convertLegacyOperators, tagInternalFilter } from './utils';\nimport { removeDiacritics } from '../hooks/features/filter/gridFilterUtils';\nexport const getGridStringQuickFilterFn = tagInternalFilter(value => {\n  if (!value) {\n    return null;\n  }\n  const filterRegex = new RegExp(escapeRegExp(value), 'i');\n  return (_, row, column, apiRef) => {\n    let columnValue = apiRef.current.getRowFormattedValue(row, column);\n    if (apiRef.current.ignoreDiacritics) {\n      columnValue = removeDiacritics(columnValue);\n    }\n    return columnValue != null ? filterRegex.test(columnValue.toString()) : false;\n  };\n});\nexport const getGridStringOperators = (disableTrim = false) => convertLegacyOperators([{\n  value: 'contains',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(escapeRegExp(filterItemValue), 'i');\n    return value => {\n      return value != null ? filterRegex.test(String(value)) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'equals',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const collator = new Intl.Collator(undefined, {\n      sensitivity: 'base',\n      usage: 'search'\n    });\n    return value => {\n      return value != null ? collator.compare(filterItemValue, value.toString()) === 0 : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'startsWith',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`^${escapeRegExp(filterItemValue)}.*$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'endsWith',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`.*${escapeRegExp(filterItemValue)}$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'isEmpty',\n  getApplyFilterFnV7: () => {\n    return value => {\n      return value === '' || value == null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isNotEmpty',\n  getApplyFilterFnV7: () => {\n    return value => {\n      return value !== '' && value != null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isAnyOf',\n  getApplyFilterFnV7: filterItem => {\n    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.map(val => val.trim());\n    const collator = new Intl.Collator(undefined, {\n      sensitivity: 'base',\n      usage: 'search'\n    });\n    return value => value != null ? filterItemValue.some(filterValue => {\n      return collator.compare(filterValue, value.toString() || '') === 0;\n    }) : false;\n  },\n  InputComponent: GridFilterInputMultipleValue\n}]);", "map": {"version": 3, "names": ["GridFilterInputValue", "escapeRegExp", "GridFilterInputMultipleValue", "convertLegacyOperators", "tagInternalFilter", "removeDiacritics", "getGridStringQuickFilterFn", "value", "filterRegex", "RegExp", "_", "row", "column", "apiRef", "columnValue", "current", "getRowFormattedValue", "ignoreDiacritics", "test", "toString", "getGridStringOperators", "disableTrim", "getApplyFilterFnV7", "filterItem", "filterItemValue", "trim", "String", "InputComponent", "collator", "Intl", "Collator", "undefined", "sensitivity", "usage", "compare", "requiresFilterValue", "Array", "isArray", "length", "map", "val", "some", "filterValue"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/colDef/gridStringOperators.js"], "sourcesContent": ["import { GridFilterInputValue } from '../components/panel/filterPanel/GridFilterInputValue';\nimport { escapeRegExp } from '../utils/utils';\nimport { GridFilterInputMultipleValue } from '../components/panel/filterPanel/GridFilterInputMultipleValue';\nimport { convertLegacyOperators, tagInternalFilter } from './utils';\nimport { removeDiacritics } from '../hooks/features/filter/gridFilterUtils';\nexport const getGridStringQuickFilterFn = tagInternalFilter(value => {\n  if (!value) {\n    return null;\n  }\n  const filterRegex = new RegExp(escapeRegExp(value), 'i');\n  return (_, row, column, apiRef) => {\n    let columnValue = apiRef.current.getRowFormattedValue(row, column);\n    if (apiRef.current.ignoreDiacritics) {\n      columnValue = removeDiacritics(columnValue);\n    }\n    return columnValue != null ? filterRegex.test(columnValue.toString()) : false;\n  };\n});\nexport const getGridStringOperators = (disableTrim = false) => convertLegacyOperators([{\n  value: 'contains',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(escapeRegExp(filterItemValue), 'i');\n    return value => {\n      return value != null ? filterRegex.test(String(value)) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'equals',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const collator = new Intl.Collator(undefined, {\n      sensitivity: 'base',\n      usage: 'search'\n    });\n    return value => {\n      return value != null ? collator.compare(filterItemValue, value.toString()) === 0 : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'startsWith',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`^${escapeRegExp(filterItemValue)}.*$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'endsWith',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`.*${escapeRegExp(filterItemValue)}$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'isEmpty',\n  getApplyFilterFnV7: () => {\n    return value => {\n      return value === '' || value == null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isNotEmpty',\n  getApplyFilterFnV7: () => {\n    return value => {\n      return value !== '' && value != null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isAnyOf',\n  getApplyFilterFnV7: filterItem => {\n    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.map(val => val.trim());\n    const collator = new Intl.Collator(undefined, {\n      sensitivity: 'base',\n      usage: 'search'\n    });\n    return value => value != null ? filterItemValue.some(filterValue => {\n      return collator.compare(filterValue, value.toString() || '') === 0;\n    }) : false;\n  },\n  InputComponent: GridFilterInputMultipleValue\n}]);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,4BAA4B,QAAQ,8DAA8D;AAC3G,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,SAAS;AACnE,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,OAAO,MAAMC,0BAA0B,GAAGF,iBAAiB,CAACG,KAAK,IAAI;EACnE,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAMC,WAAW,GAAG,IAAIC,MAAM,CAACR,YAAY,CAACM,KAAK,CAAC,EAAE,GAAG,CAAC;EACxD,OAAO,CAACG,CAAC,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,KAAK;IACjC,IAAIC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAACC,oBAAoB,CAACL,GAAG,EAAEC,MAAM,CAAC;IAClE,IAAIC,MAAM,CAACE,OAAO,CAACE,gBAAgB,EAAE;MACnCH,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;IAC7C;IACA,OAAOA,WAAW,IAAI,IAAI,GAAGN,WAAW,CAACU,IAAI,CAACJ,WAAW,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK;EAC/E,CAAC;AACH,CAAC,CAAC;AACF,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,WAAW,GAAG,KAAK,KAAKlB,sBAAsB,CAAC,CAAC;EACrFI,KAAK,EAAE,UAAU;EACjBe,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACA,UAAU,CAAChB,KAAK,EAAE;MACrB,OAAO,IAAI;IACb;IACA,MAAMiB,eAAe,GAAGH,WAAW,GAAGE,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAACkB,IAAI,CAAC,CAAC;IAChF,MAAMjB,WAAW,GAAG,IAAIC,MAAM,CAACR,YAAY,CAACuB,eAAe,CAAC,EAAE,GAAG,CAAC;IAClE,OAAOjB,KAAK,IAAI;MACd,OAAOA,KAAK,IAAI,IAAI,GAAGC,WAAW,CAACU,IAAI,CAACQ,MAAM,CAACnB,KAAK,CAAC,CAAC,GAAG,KAAK;IAChE,CAAC;EACH,CAAC;EACDoB,cAAc,EAAE3B;AAClB,CAAC,EAAE;EACDO,KAAK,EAAE,QAAQ;EACfe,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACA,UAAU,CAAChB,KAAK,EAAE;MACrB,OAAO,IAAI;IACb;IACA,MAAMiB,eAAe,GAAGH,WAAW,GAAGE,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAACkB,IAAI,CAAC,CAAC;IAChF,MAAMG,QAAQ,GAAG,IAAIC,IAAI,CAACC,QAAQ,CAACC,SAAS,EAAE;MAC5CC,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAO1B,KAAK,IAAI;MACd,OAAOA,KAAK,IAAI,IAAI,GAAGqB,QAAQ,CAACM,OAAO,CAACV,eAAe,EAAEjB,KAAK,CAACY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK;IAC1F,CAAC;EACH,CAAC;EACDQ,cAAc,EAAE3B;AAClB,CAAC,EAAE;EACDO,KAAK,EAAE,YAAY;EACnBe,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACA,UAAU,CAAChB,KAAK,EAAE;MACrB,OAAO,IAAI;IACb;IACA,MAAMiB,eAAe,GAAGH,WAAW,GAAGE,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAACkB,IAAI,CAAC,CAAC;IAChF,MAAMjB,WAAW,GAAG,IAAIC,MAAM,CAAC,IAAIR,YAAY,CAACuB,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC;IAC3E,OAAOjB,KAAK,IAAI;MACd,OAAOA,KAAK,IAAI,IAAI,GAAGC,WAAW,CAACU,IAAI,CAACX,KAAK,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK;IACnE,CAAC;EACH,CAAC;EACDQ,cAAc,EAAE3B;AAClB,CAAC,EAAE;EACDO,KAAK,EAAE,UAAU;EACjBe,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACA,UAAU,CAAChB,KAAK,EAAE;MACrB,OAAO,IAAI;IACb;IACA,MAAMiB,eAAe,GAAGH,WAAW,GAAGE,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAACkB,IAAI,CAAC,CAAC;IAChF,MAAMjB,WAAW,GAAG,IAAIC,MAAM,CAAC,KAAKR,YAAY,CAACuB,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1E,OAAOjB,KAAK,IAAI;MACd,OAAOA,KAAK,IAAI,IAAI,GAAGC,WAAW,CAACU,IAAI,CAACX,KAAK,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK;IACnE,CAAC;EACH,CAAC;EACDQ,cAAc,EAAE3B;AAClB,CAAC,EAAE;EACDO,KAAK,EAAE,SAAS;EAChBe,kBAAkB,EAAEA,CAAA,KAAM;IACxB,OAAOf,KAAK,IAAI;MACd,OAAOA,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAI,IAAI;IACtC,CAAC;EACH,CAAC;EACD4B,mBAAmB,EAAE;AACvB,CAAC,EAAE;EACD5B,KAAK,EAAE,YAAY;EACnBe,kBAAkB,EAAEA,CAAA,KAAM;IACxB,OAAOf,KAAK,IAAI;MACd,OAAOA,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAI,IAAI;IACtC,CAAC;EACH,CAAC;EACD4B,mBAAmB,EAAE;AACvB,CAAC,EAAE;EACD5B,KAAK,EAAE,SAAS;EAChBe,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACa,KAAK,CAACC,OAAO,CAACd,UAAU,CAAChB,KAAK,CAAC,IAAIgB,UAAU,CAAChB,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IACA,MAAMd,eAAe,GAAGH,WAAW,GAAGE,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAACgC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACf,IAAI,CAAC,CAAC,CAAC;IAChG,MAAMG,QAAQ,GAAG,IAAIC,IAAI,CAACC,QAAQ,CAACC,SAAS,EAAE;MAC5CC,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAO1B,KAAK,IAAIA,KAAK,IAAI,IAAI,GAAGiB,eAAe,CAACiB,IAAI,CAACC,WAAW,IAAI;MAClE,OAAOd,QAAQ,CAACM,OAAO,CAACQ,WAAW,EAAEnC,KAAK,CAACY,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;IACpE,CAAC,CAAC,GAAG,KAAK;EACZ,CAAC;EACDQ,cAAc,EAAEzB;AAClB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}