{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError\n}) => {\n  _s();\n  var _gridData$find;\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  const [documentDialog, setDocumentDialog] = useState({\n    open: false,\n    url: null,\n    docId: null,\n    error: null\n  });\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: ''\n    };\n  });\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL').reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框\n  const openRemarksDialog = (rowId, currentValue) => {\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  };\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = () => {\n    setRemarksDialog({\n      ...remarksDialog,\n      open: false\n    });\n  };\n\n  // 选择REMARKS选项\n  const selectRemarkOption = option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      const updatedData = gridData.map(row => {\n        if (row.id === rowId) {\n          return {\n            ...row,\n            REMARKS: option,\n            _selected_remarks: option\n          };\n        }\n        return row;\n      });\n      setGridData(updatedData);\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  };\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = () => {\n    setAddOptionDialog(true);\n  };\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = () => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  };\n\n  // 添加新选项\n  const addNewOption = () => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      const updatedOptions = [...remarksOptions, newOption.trim()];\n      setRemarksOptions(updatedOptions);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  };\n\n  // 删除选项\n  const deleteOption = option => {\n    const updatedOptions = remarksOptions.filter(item => item !== option);\n    setRemarksOptions(updatedOptions);\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  };\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行\n      const docData = gridData.filter(row => row.NO !== 'TOTAL').map(row => ({\n        NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks || '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL}/download-document/${response.data.docId}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true\n  }];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n\n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        minWidth: 180,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              cursor: 'pointer',\n              '&:hover': {\n                textDecoration: 'underline',\n                color: 'primary.main'\n              }\n            },\n            onClick: () => openRemarksDialog(params.row.id, params.value || ''),\n            children: params.row._selected_remarks || '点击选择'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          getRowClassName: params => params.row.isTotal ? 'total-row' : '',\n          isCellEditable: handleCellEdit,\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this), gridData.some(row => 'COMMISSION' in row) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u9879\\u76EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u91D1\\u989D (RM)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u603B\\u4F63\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: ((_gridData$find = gridData.find(row => row.NO === 'TOTAL')) === null || _gridData$find === void 0 ? void 0 : _gridData$find.COMMISSION.toFixed(2)) || '0.00',\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 421,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"7vh1QmKYEAnwdWOd+l2aXGNL7/Y=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "PictureAsPdfIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "_s", "_gridData$find", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "documentDialog", "setDocumentDialog", "open", "url", "docId", "error", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "gridData", "setGridData", "index", "id", "isTotal", "NO", "snackbar", "setSnackbar", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "Date", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "handleCleanup", "delete", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "undefined", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "updatedOptions", "deleteOption", "item", "generateDocument", "docData", "Math", "floor", "DATE", "split", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "setTimeout", "iframe", "style", "display", "src", "Error", "length", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "columns", "col", "hasOwnProperty", "flex", "min<PERSON><PERSON><PERSON>", "renderCell", "cursor", "textDecoration", "value", "fontWeight", "Boolean", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "width", "overflow", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "getRowClassName", "isCellEditable", "experimentalFeatures", "newEditingApi", "backgroundColor", "some", "component", "align", "label", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  Snackbar,\n  Alert,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\"\n];\n\nconst ResultDisplay = ({ data, fileId, onReset, onError }) => {\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  const [documentDialog, setDocumentDialog] = useState({\n    open: false,\n    url: null,\n    docId: null,\n    error: null\n  });\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '' };\n  });\n\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  const processRowUpdate = (newRow) => {\n    // 更新行数据\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\n    \n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData\n          .filter(row => row.NO !== 'TOTAL')\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        \n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    \n    setGridData(updatedData);\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  };\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框\n  const openRemarksDialog = (rowId, currentValue) => {\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  };\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = () => {\n    setRemarksDialog({\n      ...remarksDialog,\n      open: false\n    });\n  };\n\n  // 选择REMARKS选项\n  const selectRemarkOption = (option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      const updatedData = gridData.map(row => {\n        if (row.id === rowId) {\n          return { ...row, REMARKS: option, _selected_remarks: option };\n        }\n        return row;\n      });\n      setGridData(updatedData);\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  };\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = () => {\n    setAddOptionDialog(true);\n  };\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = () => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  };\n\n  // 添加新选项\n  const addNewOption = () => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      const updatedOptions = [...remarksOptions, newOption.trim()];\n      setRemarksOptions(updatedOptions);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  };\n\n  // 删除选项\n  const deleteOption = (option) => {\n    const updatedOptions = remarksOptions.filter(item => item !== option);\n    setRemarksOptions(updatedOptions);\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  };\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行\n      const docData = gridData\n        .filter(row => row.NO !== 'TOTAL')\n        .map(row => ({\n          NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\n          DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n          'VEHICLE NO': row['VEHICLE NO'] || '',\n          'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n          KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n          REMARKS: row._selected_remarks || '',\n          HOURS: typeof row.MAXCHECK === 'number' ? \n            (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n            row.MAXCHECK || '',\n          AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n        }));\n      \n      // 计算总金额\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n              if (response.data && response.data.docId) {\n          // 创建一个临时链接元素并模拟点击，确保下载开始\n          const downloadUrl = `${API_URL}/download-document/${response.data.docId}`;\n          \n          // 方法1：创建隐藏的a标签并触发点击\n          const link = document.createElement('a');\n          link.href = downloadUrl;\n          link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n          link.setAttribute('target', '_blank');\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          \n          // 显示成功消息\n          setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n          \n          // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n          setTimeout(() => {\n            const iframe = document.createElement('iframe');\n            iframe.style.display = 'none';\n            iframe.src = downloadUrl;\n            document.body.appendChild(iframe);\n            setTimeout(() => {\n              document.body.removeChild(iframe);\n            }, 2000);\n          }, 3000);\n        } else {\n          throw new Error('生成文档失败');\n        }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  \n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  // 定义列的显示顺序和标题\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true },\n    { field: 'DATE', headerName: 'DATE', editable: true },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true },\n    { field: 'RO NO', headerName: 'RO NO', editable: true },\n    { field: 'KM', headerName: 'KM', editable: true },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false },  // 新添加的REMARKS列\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true }\n  ];\n  \n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n    \n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        minWidth: 180,\n        editable: false,\n        renderCell: (params) => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          \n          return (\n            <Box \n              sx={{ \n                cursor: 'pointer',\n                '&:hover': {\n                  textDecoration: 'underline',\n                  color: 'primary.main'\n                }\n              }}\n              onClick={() => openRemarksDialog(params.row.id, params.value || '')}\n            >\n              {params.row._selected_remarks || '点击选择'}\n            </Box>\n          );\n        }\n      };\n    }\n    \n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: (params) => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        \n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n        \n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        \n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n        \n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        \n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\" \n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"secondary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 400, width: '100%' }}>\n          <DataGrid\n            rows={gridData}\n            columns={columns}\n            pageSize={10}\n            rowsPerPageOptions={[10, 25, 50]}\n            disableSelectionOnClick\n            getRowClassName={(params) => params.row.isTotal ? 'total-row' : ''}\n            isCellEditable={handleCellEdit}\n            processRowUpdate={processRowUpdate}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            experimentalFeatures={{ newEditingApi: true }}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {gridData.some(row => 'COMMISSION' in row) && (\n        <Box sx={{ mt: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            佣金计算结果\n          </Typography>\n          \n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>项目</TableCell>\n                  <TableCell align=\"right\">金额 (RM)</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                <TableRow>\n                  <TableCell>总佣金</TableCell>\n                  <TableCell align=\"right\">\n                    <Chip \n                      label={gridData.find(row => row.NO === 'TOTAL')?.COMMISSION.toFixed(2) || '0.00'} \n                      color=\"primary\" \n                      variant=\"outlined\"\n                    />\n                  </TableCell>\n                </TableRow>\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Box>\n      )}\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers>\n          <List>\n            {remarksOptions.map((option) => (\n              <ListItem \n                key={option} \n                disablePadding\n                secondaryAction={\n                  <IconButton \n                    edge=\"end\" \n                    aria-label=\"delete\"\n                    onClick={() => deleteOption(option)}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                }\n              >\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\n                  <ListItemText primary={option} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,CACrB;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC5D;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,MAAM;IACzD,MAAMgD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGV,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC;IACnD6D,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA/D,SAAS,CAAC,MAAM;IACdgD,YAAY,CAACgB,OAAO,CAAC,gBAAgB,EAAEd,IAAI,CAACe,SAAS,CAACpB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMqB,aAAa,GAAG3B,IAAI,CAAC4B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE;IAAG,CAAC;EACvD,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAACmE,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEK,KAAK,MAAM;IAC1E,GAAGL,GAAG;IACNM,EAAE,EAAED,KAAK;IACTE,OAAO,EAAEP,GAAG,CAACQ,EAAE,KAAK;EACtB,CAAC,CAAC,CAAC,CAAC;EAEJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC;IAAE6D,IAAI,EAAE,KAAK;IAAEmB,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC;IACjD6D,IAAI,EAAE,KAAK;IACXuB,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAGpD,OAAO,aAAaM,MAAM,EAAE;;MAEnD;MACA,MAAM+C,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;MAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;MACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;;MAE/B;MACAT,WAAW,CAAC;QAAElB,IAAI,EAAE,IAAI;QAAEmB,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrB,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMyD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMlE,KAAK,CAACmE,MAAM,CAAC,GAAGlE,OAAO,YAAYM,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAtB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM4D,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAClC,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAM2B,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACA,MAAMC,WAAW,GAAGlC,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACM,EAAE,KAAK8B,MAAM,CAAC9B,EAAE,GAAG8B,MAAM,GAAGpC,GAAI,CAAC;;IAE9E;IACA,IAAIoC,MAAM,CAACE,UAAU,KAAKC,SAAS,EAAE;MACnC,MAAMC,QAAQ,GAAGH,WAAW,CAACI,IAAI,CAACzC,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAIgC,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGL,WAAW,CACzBM,MAAM,CAAC3C,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjCoC,MAAM,CAAC,CAACC,GAAG,EAAE7C,GAAG,KAAK6C,GAAG,IAAI7C,GAAG,CAACsC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDE,QAAQ,CAACF,UAAU,GAAGI,QAAQ;MAChC;IACF;IAEAtC,WAAW,CAACiC,WAAW,CAAC;IACxB3B,WAAW,CAAC;MAAElB,IAAI,EAAE,IAAI;MAAEmB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOwB,MAAM;EACf,CAAC;EAED,MAAMU,uBAAuB,GAAInD,KAAK,IAAK;IACzCe,WAAW,CAAC;MAAElB,IAAI,EAAE,IAAI;MAAEmB,OAAO,EAAE,SAAShB,KAAK,CAACgB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMmC,mBAAmB,GAAGA,CAAA,KAAM;IAChCrC,WAAW,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMyD,iBAAiB,GAAGA,CAAClC,KAAK,EAAEC,YAAY,KAAK;IACjDF,gBAAgB,CAAC;MACftB,IAAI,EAAE,IAAI;MACVuB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpC,gBAAgB,CAAC;MACf,GAAGD,aAAa;MAChBrB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2D,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAM;MAAErC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMsB,WAAW,GAAGlC,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACM,EAAE,KAAKS,KAAK,EAAE;UACpB,OAAO;YAAE,GAAGf,GAAG;YAAEC,OAAO,EAAEmD,MAAM;YAAElD,iBAAiB,EAAEkD;UAAO,CAAC;QAC/D;QACA,OAAOpD,GAAG;MACZ,CAAC,CAAC;MACFI,WAAW,CAACiC,WAAW,CAAC;MACxB3B,WAAW,CAAC;QAAElB,IAAI,EAAE,IAAI;QAAEmB,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAsC,kBAAkB,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChClE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmE,oBAAoB,GAAGA,CAAA,KAAM;IACjCnE,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvE,SAAS,CAACwE,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC/E,cAAc,CAACgF,QAAQ,CAACzE,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE,MAAME,cAAc,GAAG,CAAC,GAAGjF,cAAc,EAAEO,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC;MAC5D9E,iBAAiB,CAACgF,cAAc,CAAC;MACjChD,WAAW,CAAC;QAAElB,IAAI,EAAE,IAAI;QAAEmB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE0C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI7E,cAAc,CAACgF,QAAQ,CAACzE,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC,EAAE;MACpD9C,WAAW,CAAC;QAAElB,IAAI,EAAE,IAAI;QAAEmB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAIP,MAAM,IAAK;IAC/B,MAAMM,cAAc,GAAGjF,cAAc,CAACkE,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC;IACrE1E,iBAAiB,CAACgF,cAAc,CAAC;IACjChD,WAAW,CAAC;MAAElB,IAAI,EAAE,IAAI;MAAEmB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFxE,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMyE,OAAO,GAAG3D,QAAQ,CACrBwC,MAAM,CAAC3C,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjCT,GAAG,CAACC,GAAG,KAAK;QACXQ,EAAE,EAAE,OAAOR,GAAG,CAACQ,EAAE,KAAK,QAAQ,GAAGuD,IAAI,CAACC,KAAK,CAAChE,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAACQ,EAAE;QAC5DyD,IAAI,EAAEjE,GAAG,CAACiE,IAAI,GAAI,OAAOjE,GAAG,CAACiE,IAAI,KAAK,QAAQ,IAAIjE,GAAG,CAACiE,IAAI,CAACR,QAAQ,CAAC,GAAG,CAAC,GAAGzD,GAAG,CAACiE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGlE,GAAG,CAACiE,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEjE,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG+D,IAAI,CAACC,KAAK,CAAChE,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFmE,EAAE,EAAE,OAAOnE,GAAG,CAACmE,EAAE,KAAK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAAChE,GAAG,CAACmE,EAAE,CAAC,GAAGnE,GAAG,CAACmE,EAAE,IAAI,EAAE;QAClElE,OAAO,EAAED,GAAG,CAACE,iBAAiB,IAAI,EAAE;QACpCkE,KAAK,EAAE,OAAOpE,GAAG,CAACqE,QAAQ,KAAK,QAAQ,GACpCrE,GAAG,CAACqE,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGrE,GAAG,CAACqE,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGtE,GAAG,CAACqE,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EtE,GAAG,CAACqE,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOvE,GAAG,CAACsC,UAAU,KAAK,QAAQ,GAAGtC,GAAG,CAACsC,UAAU,CAACgC,OAAO,CAAC,CAAC,CAAC,GAAGtE,GAAG,CAACsC,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEL;MACA,MAAMkC,WAAW,GAAGrE,QAAQ,CACzBwC,MAAM,CAAC3C,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAIR,GAAG,CAACsC,UAAU,CAAC,CACnDM,MAAM,CAAC,CAACC,GAAG,EAAE7C,GAAG,KAAK6C,GAAG,IAAI,OAAO7C,GAAG,CAACsC,UAAU,KAAK,QAAQ,GAAGtC,GAAG,CAACsC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMmC,QAAQ,GAAG,MAAM5G,KAAK,CAAC6G,IAAI,CAAC,GAAG5G,OAAO,oBAAoB,EAAE;QAChEK,IAAI,EAAE2F,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnClG,MAAM,EAAEA;MACV,CAAC,CAAC;MAEM,IAAIqG,QAAQ,CAACtG,IAAI,IAAIsG,QAAQ,CAACtG,IAAI,CAACuB,KAAK,EAAE;QAC9C;QACA,MAAMwB,WAAW,GAAG,GAAGpD,OAAO,sBAAsB2G,QAAQ,CAACtG,IAAI,CAACuB,KAAK,EAAE;;QAEzE;QACA,MAAMyB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEN,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;;QAE/B;QACAT,WAAW,CAAC;UAAElB,IAAI,EAAE,IAAI;UAAEmB,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACA+D,UAAU,CAAC,MAAM;UACf,MAAMC,MAAM,GAAGxD,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CuD,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG7D,WAAW;UACxBE,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACiD,MAAM,CAAC;UACjCD,UAAU,CAAC,MAAM;YACfvD,QAAQ,CAACM,IAAI,CAACG,WAAW,CAAC+C,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/Be,WAAW,CAAC;QAAElB,IAAI,EAAE,IAAI;QAAEmB,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRvB,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAGD;EACA,IAAI,CAACc,QAAQ,IAAIA,QAAQ,CAAC8E,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEjH,OAAA,CAACnC,GAAG;MAACqJ,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtCrH,OAAA,CAAClC,UAAU;QAACwJ,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3H,OAAA,CAACjC,MAAM;QACLuJ,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAEvH,OAAQ;QACjB6G,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAAG;EAC/D;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9D;;EAED;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAAC/F,GAAG,CAACoG,GAAG,IAAI;IACrC,IAAI,CAAChG,QAAQ,CAAC,CAAC,CAAC,CAACiG,cAAc,CAACD,GAAG,CAACJ,KAAK,CAAC,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,IAAII,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;MACnG;MACA,OAAO,IAAI;IACb;;IAEA;IACA,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEI,GAAG,CAACJ,KAAK;QAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;QAC1BK,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,GAAG;QACbL,QAAQ,EAAE,KAAK;QACfM,UAAU,EAAGrE,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAAClC,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;UAEA,oBACExC,OAAA,CAACnC,GAAG;YACFqJ,EAAE,EAAE;cACFsB,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTC,cAAc,EAAE,WAAW;gBAC3BlB,KAAK,EAAE;cACT;YACF,CAAE;YACFK,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAACf,MAAM,CAAClC,GAAG,CAACM,EAAE,EAAE4B,MAAM,CAACwE,KAAK,IAAI,EAAE,CAAE;YAAArB,QAAA,EAEnEnD,MAAM,CAAClC,GAAG,CAACE,iBAAiB,IAAI;UAAM;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAEV;MACF,CAAC;IACH;IAEA,OAAO;MACLI,KAAK,EAAEI,GAAG,CAACJ,KAAK;MAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;MAC1BK,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbL,QAAQ,EAAEE,GAAG,CAACF,QAAQ;MACtBM,UAAU,EAAGrE,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAAClC,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAI2F,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE/H,OAAA,CAAClC,UAAU;YAACwJ,OAAO,EAAC,OAAO;YAACqB,UAAU,EAAC,MAAM;YAACpB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOnD,MAAM,CAACwE,KAAK,KAAK,QAAQ,GAAGxE,MAAM,CAACwE,KAAK,CAACpC,OAAO,CAAC,CAAC,CAAC,GAAGpC,MAAM,CAACwE;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIQ,GAAG,CAACJ,KAAK,KAAK,MAAM,IAAI7D,MAAM,CAACwE,KAAK,EAAE;UACxC,OAAOxE,MAAM,CAACwE,KAAK,CAACxC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAIiC,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO7D,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAO3C,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACwE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,OAAO,IAAI,OAAO7D,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAO3C,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACwE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO7D,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAO3C,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACwE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,UAAU,IAAI,OAAO7D,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOxE,MAAM,CAACwE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGxE,MAAM,CAACwE,KAAK,CAACpC,OAAO,CAAC,CAAC,CAAC,GAAGpC,MAAM,CAACwE,KAAK,CAACpC,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAI6B,GAAG,CAACJ,KAAK,KAAK,YAAY,IAAI,OAAO7D,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOxE,MAAM,CAACwE,KAAK,CAACpC,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAOpC,MAAM,CAACwE,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOxE,MAAM,CAACwE,KAAK;QACrB;QAEA,OAAOxE,MAAM,CAACwE,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC/D,MAAM,CAACiE,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACE5I,OAAA,CAACnC,GAAG;IAAAwJ,QAAA,gBACFrH,OAAA,CAACnC,GAAG;MAACqJ,EAAE,EAAE;QAAEJ,OAAO,EAAE,MAAM;QAAE+B,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,gBACzFrH,OAAA,CAAClC,UAAU;QAACwJ,OAAO,EAAC,IAAI;QAAC0B,YAAY;QAAA3B,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3H,OAAA,CAACnC,GAAG;QAAAwJ,QAAA,gBACFrH,OAAA,CAACjC,MAAM;UACLuJ,OAAO,EAAC,WAAW;UACnB2B,SAAS,eAAEjJ,OAAA,CAACR,YAAY;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAE3E,cAAe;UACxBiE,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3H,OAAA,CAACjC,MAAM;UACLuJ,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,WAAW;UACjB0B,SAAS,eAAEjJ,OAAA,CAACJ,gBAAgB;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCC,OAAO,EAAE/B,gBAAiB;UAC1BsD,QAAQ,EAAE/H,oBAAqB;UAC/B8F,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAEbjG,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAET3H,OAAA,CAACjC,MAAM;UACLuJ,OAAO,EAAC,UAAU;UAClB2B,SAAS,eAAEjJ,OAAA,CAACP,cAAc;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAE7D,aAAc;UAAAsD,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3H,OAAA,CAAChC,KAAK;MAACkJ,EAAE,EAAE;QAAEkC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAhC,QAAA,eAC/CrH,OAAA,CAACnC,GAAG;QAACqJ,EAAE,EAAE;UAAEoC,MAAM,EAAE,GAAG;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAA/B,QAAA,eACtCrH,OAAA,CAACT,QAAQ;UACPgK,IAAI,EAAEpH,QAAS;UACf+F,OAAO,EAAEA,OAAQ;UACjBsB,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,eAAe,EAAGzF,MAAM,IAAKA,MAAM,CAAClC,GAAG,CAACO,OAAO,GAAG,WAAW,GAAG,EAAG;UACnEqH,cAAc,EAAE3F,cAAe;UAC/BE,gBAAgB,EAAEA,gBAAiB;UACnCW,uBAAuB,EAAEA,uBAAwB;UACjD+E,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9C5C,EAAE,EAAE;YACF,cAAc,EAAE;cACd6C,eAAe,EAAE,0BAA0B;cAC3CpB,UAAU,EAAE;YACd;UACF;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEPxF,QAAQ,CAAC6H,IAAI,CAAChI,GAAG,IAAI,YAAY,IAAIA,GAAG,CAAC,iBACxChC,OAAA,CAACnC,GAAG;MAACqJ,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACjBrH,OAAA,CAAClC,UAAU;QAACwJ,OAAO,EAAC,WAAW;QAAC0B,YAAY;QAAA3B,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3H,OAAA,CAAC5B,cAAc;QAAC6L,SAAS,EAAEjM,KAAM;QAAAqJ,QAAA,eAC/BrH,OAAA,CAAC/B,KAAK;UAAAoJ,QAAA,gBACJrH,OAAA,CAAC3B,SAAS;YAAAgJ,QAAA,eACRrH,OAAA,CAAC1B,QAAQ;cAAA+I,QAAA,gBACPrH,OAAA,CAAC7B,SAAS;gBAAAkJ,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzB3H,OAAA,CAAC7B,SAAS;gBAAC+L,KAAK,EAAC,OAAO;gBAAA7C,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ3H,OAAA,CAAC9B,SAAS;YAAAmJ,QAAA,eACRrH,OAAA,CAAC1B,QAAQ;cAAA+I,QAAA,gBACPrH,OAAA,CAAC7B,SAAS;gBAAAkJ,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1B3H,OAAA,CAAC7B,SAAS;gBAAC+L,KAAK,EAAC,OAAO;gBAAA7C,QAAA,eACtBrH,OAAA,CAACzB,IAAI;kBACH4L,KAAK,EAAE,EAAA3J,cAAA,GAAA2B,QAAQ,CAACsC,IAAI,CAACzC,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,cAAAhC,cAAA,uBAAxCA,cAAA,CAA0C8D,UAAU,CAACgC,OAAO,CAAC,CAAC,CAAC,KAAI,MAAO;kBACjFiB,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGD3H,OAAA,CAACpB,MAAM;MACL4C,IAAI,EAAEqB,aAAa,CAACrB,IAAK;MACzB4I,OAAO,EAAElF,kBAAmB;MAC5BmF,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAjD,QAAA,gBAEbrH,OAAA,CAACnB,WAAW;QAAAwI,QAAA,eACVrH,OAAA,CAACnC,GAAG;UAACqJ,EAAE,EAAE;YAAEJ,OAAO,EAAE,MAAM;YAAE+B,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBAClFrH,OAAA,CAAClC,UAAU;YAACwJ,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C3H,OAAA,CAACjC,MAAM;YACLkL,SAAS,eAAEjJ,OAAA,CAACN,OAAO;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBC,OAAO,EAAEvC,mBAAoB;YAC7BkC,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3H,OAAA,CAAClB,aAAa;QAACyL,QAAQ;QAAAlD,QAAA,eACrBrH,OAAA,CAAChB,IAAI;UAAAqI,QAAA,EACF5G,cAAc,CAACsB,GAAG,CAAEqD,MAAM,iBACzBpF,OAAA,CAACf,QAAQ;YAEPuL,cAAc;YACdC,eAAe,eACbzK,OAAA,CAACX,UAAU;cACTqL,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnB9C,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAACP,MAAM,CAAE;cAAAiC,QAAA,eAEpCrH,OAAA,CAACL,UAAU;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAN,QAAA,eAEDrH,OAAA,CAACd,cAAc;cAAC0I,OAAO,EAAEA,CAAA,KAAMzC,kBAAkB,CAACC,MAAM,CAAE;cAAAiC,QAAA,eACxDrH,OAAA,CAACb,YAAY;gBAACwL,OAAO,EAAEvF;cAAO;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZvC,MAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB3H,OAAA,CAACjB,aAAa;QAAAsI,QAAA,eACZrH,OAAA,CAACjC,MAAM;UAAC6J,OAAO,EAAE1C,kBAAmB;UAAAmC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3H,OAAA,CAACpB,MAAM;MACL4C,IAAI,EAAEN,eAAgB;MACtBkJ,OAAO,EAAE9E,oBAAqB;MAC9B+E,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAjD,QAAA,gBAEbrH,OAAA,CAACnB,WAAW;QAAAwI,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC3H,OAAA,CAAClB,aAAa;QAAAuI,QAAA,eACZrH,OAAA,CAACZ,SAAS;UACRwL,SAAS;UACTC,MAAM,EAAC,OAAO;UACdvI,EAAE,EAAC,MAAM;UACT6H,KAAK,EAAC,0BAAM;UACZW,IAAI,EAAC,MAAM;UACXT,SAAS;UACT/C,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAE1H,SAAU;UACjB+J,QAAQ,EAAGC,CAAC,IAAK/J,YAAY,CAAC+J,CAAC,CAACC,MAAM,CAACvC,KAAK;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB3H,OAAA,CAACjB,aAAa;QAAAsI,QAAA,gBACZrH,OAAA,CAACjC,MAAM;UAAC6J,OAAO,EAAEtC,oBAAqB;UAAA+B,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD3H,OAAA,CAACjC,MAAM;UAAC6J,OAAO,EAAErC,YAAa;UAACgC,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3H,OAAA,CAACxB,QAAQ;MACPgD,IAAI,EAAEiB,QAAQ,CAACjB,IAAK;MACpB0J,gBAAgB,EAAE,IAAK;MACvBd,OAAO,EAAErF,mBAAoB;MAC7BoG,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAhE,QAAA,eAE1DrH,OAAA,CAACvB,KAAK;QAAC2L,OAAO,EAAErF,mBAAoB;QAACnC,QAAQ,EAAEH,QAAQ,CAACG,QAAS;QAAAyE,QAAA,EAC9D5E,QAAQ,CAACE;MAAO;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACpH,EAAA,CAriBIL,aAAa;AAAAoL,EAAA,GAAbpL,aAAa;AAuiBnB,eAAeA,aAAa;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}