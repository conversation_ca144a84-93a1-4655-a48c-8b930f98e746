{"ast": null, "code": "import useThemeProps from '../styles/useThemeProps';\nexport { default as styled } from '../styles/styled';\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function createUseThemeProps(name) {\n  return useThemeProps;\n}", "map": {"version": 3, "names": ["useThemeProps", "default", "styled", "createUseThemeProps", "name"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/zero-styled/index.js"], "sourcesContent": ["import useThemeProps from '../styles/useThemeProps';\nexport { default as styled } from '../styles/styled';\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function createUseThemeProps(name) {\n  return useThemeProps;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yBAAyB;AACnD,SAASC,OAAO,IAAIC,MAAM,QAAQ,kBAAkB;;AAEpD;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOJ,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}