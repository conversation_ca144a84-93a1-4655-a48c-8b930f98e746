2025-08-03 21:57:52.960 +08:00 [INF] 启动佣金计算系统
2025-08-03 21:57:53.272 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-03 21:57:53.299 +08:00 [FTL] 佣金计算系统启动失败
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at CommissionCalculation.Backend.Program.Main(String[] args) in C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Program.cs:line 76
2025-08-03 21:59:01.159 +08:00 [INF] 启动佣金计算系统
2025-08-03 21:59:45.114 +08:00 [INF] 启动佣金计算系统
2025-08-03 21:59:55.916 +08:00 [WRN] Failed to determine the https port for redirect.
2025-08-03 21:59:56.004 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 21:59:56.068 +08:00 [INF] 接收到文件上传请求: ELITE RECORD 2025 - Copy.xlsx
2025-08-03 21:59:56.072 +08:00 [INF] 生成文件ID: b91804cb-91c8-481e-b19a-b90b491c69ac
2025-08-03 21:59:56.081 +08:00 [INF] 保存文件: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads\b91804cb-91c8-481e-b19a-b90b491c69ac_52ec11fa-2509-4d76-a521-60d6e0c8dcba_ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:00:00.004 +08:00 [INF] 工作表: JAN'2025, FEB'2025, MAR'2025, APR'2025, MAY'2025, JUNE'2025, JULY'2025, AUG'2025, SEP'2025, OCT'2025, NOV'2025, DEC'2025
2025-08-03 22:00:00.007 +08:00 [INF] 文件上传成功: b91804cb-91c8-481e-b19a-b90b491c69ac
2025-08-03 22:00:05.795 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:00:05.873 +08:00 [INF] 接收到文件处理请求: b91804cb-91c8-481e-b19a-b90b491c69ac, 工作表: MAY'2025
2025-08-03 22:00:05.886 +08:00 [INF] 开始处理文件ID: b91804cb-91c8-481e-b19a-b90b491c69ac, 工作表: MAY'2025, 列范围: AN-BJ
2025-08-03 22:00:08.932 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:00:08.935 +08:00 [INF] 接收到文件处理请求: b91804cb-91c8-481e-b19a-b90b491c69ac, 工作表: MAY'2025
2025-08-03 22:00:08.939 +08:00 [INF] 开始处理文件ID: b91804cb-91c8-481e-b19a-b90b491c69ac, 工作表: MAY'2025, 列范围: AN-BJ
2025-08-03 22:01:26.966 +08:00 [INF] 启动佣金计算系统
2025-08-03 22:03:18.104 +08:00 [INF] 启动佣金计算系统
2025-08-03 22:03:21.904 +08:00 [WRN] Failed to determine the https port for redirect.
2025-08-03 22:03:21.960 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:03:22.021 +08:00 [INF] 接收到文件上传请求: ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:03:22.024 +08:00 [INF] 生成文件ID: 28d607b8-55cb-4abc-9c58-5cd441e2daaa
2025-08-03 22:03:22.043 +08:00 [INF] 保存文件: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads\28d607b8-55cb-4abc-9c58-5cd441e2daaa_ffbe2df7-5c2f-49cb-b193-6792ef86b862_ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:03:25.867 +08:00 [INF] 工作表: JAN'2025, FEB'2025, MAR'2025, APR'2025, MAY'2025, JUNE'2025, JULY'2025, AUG'2025, SEP'2025, OCT'2025, NOV'2025, DEC'2025
2025-08-03 22:03:25.870 +08:00 [INF] 文件上传成功: 28d607b8-55cb-4abc-9c58-5cd441e2daaa
2025-08-03 22:03:29.568 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:03:29.607 +08:00 [INF] 接收到文件处理请求: 28d607b8-55cb-4abc-9c58-5cd441e2daaa, 工作表: MAY'2025
2025-08-03 22:03:29.617 +08:00 [INF] 开始处理文件ID: 28d607b8-55cb-4abc-9c58-5cd441e2daaa, 工作表: MAY'2025, 列范围: AN-BJ
2025-08-03 22:03:31.435 +08:00 [INF] 成功从磁盘重建文件信息: 28d607b8-55cb-4abc-9c58-5cd441e2daaa, 原始文件名: ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:03:32.992 +08:00 [INF] 找到标题行: 6, 列映射: NO:0, DATE:2, VEHICLE NO:3, RO NO:4, KM:5, EGN OIL:6, O/FLTER:7, ATF:8, BT:9, EGN FLUSH:10, FR BR PAD:11, R BR PAD:12, D FILTER:13, P/SHAFT:14, W/RUBBER:15, SPARK PLUG:16, OTHERS:17, MAXCHECK:20, REMARKS:21, PRICE:22
2025-08-03 22:03:33.312 +08:00 [INF] 完成VEHICLE NO列颜色检测
2025-08-03 22:03:33.314 +08:00 [INF] 开始计算佣金，费率: 普通=65, CBU CAR=130, 佣金率: 普通=0.6, WTY=0.85
2025-08-03 22:03:33.324 +08:00 [INF] 从REMARKS中创建了 50 个额外行
2025-08-03 22:03:34.827 +08:00 [ERR] 更新Excel文件时出错
System.FormatException: The input string '1.8 RM 70.20' was not in a correct format.
   at System.Number.ThrowFormatException[TChar](ReadOnlySpan`1 value)
   at System.String.System.IConvertible.ToDouble(IFormatProvider provider)
   at CommissionCalculation.Backend.Services.ExcelProcessor.UpdateExcelFileWithCommissions(String filePath, String worksheetName, DataTable dataTable, Dictionary`2 headerColumns, Int32 headerRow, Int32 startIdx, Double perHourRate, Double cbuCarHourRate, Double wtyHourRate, Double commissionRate, Double wtyCommissionRate) in C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Services\ExcelProcessor.cs:line 713
2025-08-03 22:03:34.840 +08:00 [INF] 存储了文件ID 28d607b8-55cb-4abc-9c58-5cd441e2daaa 的DataTable，行数: 275
2025-08-03 22:03:34.968 +08:00 [INF] 文件处理成功: 28d607b8-55cb-4abc-9c58-5cd441e2daaa
2025-08-03 22:06:58.235 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:06:58.285 +08:00 [INF] 接收到文件上传请求: ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:06:58.287 +08:00 [INF] 生成文件ID: 055c0444-0f40-495f-9bf0-07bdab5a70d7
2025-08-03 22:06:58.304 +08:00 [INF] 保存文件: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads\055c0444-0f40-495f-9bf0-07bdab5a70d7_2f31f031-c62a-47bb-a12a-0bd83c2499b6_ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:07:01.808 +08:00 [INF] 工作表: JAN'2025, FEB'2025, MAR'2025, APR'2025, MAY'2025, JUNE'2025, JULY'2025, AUG'2025, SEP'2025, OCT'2025, NOV'2025, DEC'2025
2025-08-03 22:07:01.810 +08:00 [INF] 文件上传成功: 055c0444-0f40-495f-9bf0-07bdab5a70d7
2025-08-03 22:07:04.870 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:07:04.874 +08:00 [INF] 接收到文件处理请求: 055c0444-0f40-495f-9bf0-07bdab5a70d7, 工作表: MAY'2025
2025-08-03 22:07:04.875 +08:00 [INF] 开始处理文件ID: 055c0444-0f40-495f-9bf0-07bdab5a70d7, 工作表: MAY'2025, 列范围: AN-BJ
2025-08-03 22:07:06.148 +08:00 [INF] 成功从磁盘重建文件信息: 055c0444-0f40-495f-9bf0-07bdab5a70d7, 原始文件名: ELITE RECORD 2025 - Copy.xlsx
2025-08-03 22:07:07.316 +08:00 [INF] 找到标题行: 6, 列映射: NO:0, DATE:2, VEHICLE NO:3, RO NO:4, KM:5, EGN OIL:6, O/FLTER:7, ATF:8, BT:9, EGN FLUSH:10, FR BR PAD:11, R BR PAD:12, D FILTER:13, P/SHAFT:14, W/RUBBER:15, SPARK PLUG:16, OTHERS:17, MAXCHECK:20, REMARKS:21, PRICE:22
2025-08-03 22:07:07.352 +08:00 [INF] 完成VEHICLE NO列颜色检测
2025-08-03 22:07:07.353 +08:00 [INF] 开始计算佣金，费率: 普通=65, CBU CAR=130, 佣金率: 普通=0.6, WTY=0.85
2025-08-03 22:07:07.365 +08:00 [INF] 从REMARKS中创建了 50 个额外行
2025-08-03 22:07:08.891 +08:00 [ERR] 更新Excel文件时出错
System.FormatException: The input string '1.8 RM 70.20' was not in a correct format.
   at System.Number.ThrowFormatException[TChar](ReadOnlySpan`1 value)
   at System.String.System.IConvertible.ToDouble(IFormatProvider provider)
   at CommissionCalculation.Backend.Services.ExcelProcessor.UpdateExcelFileWithCommissions(String filePath, String worksheetName, DataTable dataTable, Dictionary`2 headerColumns, Int32 headerRow, Int32 startIdx, Double perHourRate, Double cbuCarHourRate, Double wtyHourRate, Double commissionRate, Double wtyCommissionRate) in C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Services\ExcelProcessor.cs:line 713
2025-08-03 22:07:08.902 +08:00 [INF] 存储了文件ID 055c0444-0f40-495f-9bf0-07bdab5a70d7 的DataTable，行数: 275
2025-08-03 22:07:08.990 +08:00 [INF] 文件处理成功: 055c0444-0f40-495f-9bf0-07bdab5a70d7
2025-08-03 22:09:06.852 +08:00 [INF] 上传文件夹路径: C:\Users\<USER>\Code\My Project\commision-calculation\backend-new\backend-new\Uploads
2025-08-03 22:09:06.864 +08:00 [INF] 接收到文件清理请求: 055c0444-0f40-495f-9bf0-07bdab5a70d7
2025-08-03 22:09:06.867 +08:00 [ERR] 文件不存在: 055c0444-0f40-495f-9bf0-07bdab5a70d7
