{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'center'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'center'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n\n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次通知时间不足300ms，则跳过此次通知\n      if (now - lastNotifyTimeRef.current < 300) {\n        return;\n      }\n\n      // 比较新数据和上一次通知的数据是否相同\n      const currentDataString = JSON.stringify(gridData);\n      const lastDataString = lastNotifiedDataRef.current;\n      if (lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        lastNotifiedDataRef.current = currentDataString;\n        lastNotifyTimeRef.current = now;\n        onDataChange([...gridData]); // 确保传递深拷贝\n      }\n    }\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === newRow.id ? newRow : row);\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  }, [recalculateTotal]);\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n\n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    console.log('删除行:', id);\n\n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n\n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    console.log('恢复行:', id);\n\n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n\n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasSelectedRemark ? remarkText : '',\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: remarkText,\n              color: hasSelectedRemark ? 'primary' : 'default',\n              variant: hasSelectedRemark ? 'filled' : 'outlined',\n              size: \"small\",\n              onClick: () => handleRemarksClick(params.row.id, params.value || ''),\n              clickable: true,\n              sx: {\n                maxWidth: '100%',\n                cursor: 'pointer',\n                '& .MuiChip-label': {\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  display: 'block'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: (newRow, oldRow) => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 602,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"7/dzXTxlDYBc20+Wq6V9Cu+zyn8=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "now", "Date", "current", "currentDataString", "lastDataString", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "recalculateTotal", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "processRowUpdate", "newRow", "prev", "updatedData", "onProcessRowUpdateError", "handleCloseSnackbar", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "rowToRemove", "commissionValue", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "rowToRestore", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "setTimeout", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "sx", "max<PERSON><PERSON><PERSON>", "opacity", "overflow", "textOverflow", "whiteSpace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "remarkText", "hasSelectedRemark", "onClick", "clickable", "cursor", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "textAlign", "py", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "experimentalFeatures", "newEditingApi", "backgroundColor", "lineHeight", "padding", "overflowX", "border", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'center' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'center' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'center' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'center' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'center' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'center' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'center' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'center' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'center' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  \n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      \n      // 如果距离上次通知时间不足300ms，则跳过此次通知\n      if (now - lastNotifyTimeRef.current < 300) {\n        return;\n      }\n      \n      // 比较新数据和上一次通知的数据是否相同\n      const currentDataString = JSON.stringify(gridData);\n      const lastDataString = lastNotifiedDataRef.current;\n      \n      if (lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        lastNotifiedDataRef.current = currentDataString;\n        lastNotifyTimeRef.current = now;\n        onDataChange([...gridData]); // 确保传递深拷贝\n      }\n    }\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      \n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === newRow.id ? newRow : row);\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  }, [recalculateTotal]);\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n        \n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    console.log('删除行:', id);\n    \n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n    \n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    console.log('恢复行:', id);\n    \n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n    \n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return (\n            <Tooltip title={hasSelectedRemark ? remarkText : ''} arrow placement=\"top\">\n              <Chip\n                label={remarkText}\n                color={hasSelectedRemark ? 'primary' : 'default'}\n                variant={hasSelectedRemark ? 'filled' : 'outlined'}\n                size=\"small\"\n                onClick={() => handleRemarksClick(params.row.id, params.value || '')}\n                clickable\n                sx={{ maxWidth: '100%', cursor: 'pointer', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n              />\n            </Tooltip>\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Chip\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          return (\n            <Chip\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <DataGrid\n            rows={gridData}\n            columns={columns}\n            pageSize={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            disableSelectionOnClick\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n            }}\n            processRowUpdate={(newRow, oldRow) => {\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            }}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            experimentalFeatures={{ newEditingApi: true }}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;;AAE5C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC5E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC9E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC,EACnF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACjF;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACpF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC,CAClF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,MAAM;IACzD,MAAMoD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGhB,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdoD,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAG7B,IAAI,CAAC8B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,MAAM;IAC7C2E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7ClC,aAAa,GAAG,IAAIA,aAAa,CAACmC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAInC,aAAa,IAAIA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGpC,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGlF,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMmF,iBAAiB,GAAGnF,MAAM,CAAC,CAAC,CAAC;;EAEnC;EACAF,SAAS,CAAC,MAAM;IACd,IAAI0C,YAAY,IAAI8B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMU,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIA,GAAG,GAAGD,iBAAiB,CAACG,OAAO,GAAG,GAAG,EAAE;QACzC;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGnC,IAAI,CAACW,SAAS,CAACO,QAAQ,CAAC;MAClD,MAAMkB,cAAc,GAAGN,mBAAmB,CAACI,OAAO;MAElD,IAAIE,cAAc,KAAKD,iBAAiB,EAAE;QACxCf,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,QAAQ,CAACI,MAAM,CAAC;QAC9DQ,mBAAmB,CAACI,OAAO,GAAGC,iBAAiB;QAC/CJ,iBAAiB,CAACG,OAAO,GAAGF,GAAG;QAC/B5C,YAAY,CAAC,CAAC,GAAG8B,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;EACF,CAAC,EAAE,CAACA,QAAQ,EAAE9B,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC;IAAE8F,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlG,QAAQ,CAAC;IACjD8F,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAnG,SAAS,CAAC,MAAM;IACd,IAAI8D,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAMsC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAGtE,OAAO,aAAaO,MAAM,EAAE;;MAEnD;MACA,MAAMgE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAInB,IAAI,CAAC,CAAC,CAACoB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAV,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxE,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMyE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMnF,KAAK,CAACoF,MAAM,CAAC,GAAGnF,OAAO,YAAYO,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO0E,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAzE,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM4E,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAChD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAGpH,WAAW,CAAEoC,IAAI,IAAK;IAC7C,MAAMiF,QAAQ,GAAGjF,IAAI,CAACkF,IAAI,CAACnD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAImC,QAAQ,EAAE;MACZ;MACA,MAAME,QAAQ,GAAGnF,IAAI,CAClBoF,MAAM,CAACrD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDmD,MAAM,CAAC,CAACC,GAAG,EAAEvD,GAAG,KAAKuD,GAAG,IAAIC,MAAM,CAACxD,GAAG,CAACyD,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/DnD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6C,QAAQ,CAAC;MACnCF,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAOnF,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyF,gBAAgB,GAAG7H,WAAW,CAAE8H,MAAM,IAAK;IAC/CtD,WAAW,CAACuD,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAAC7D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK8C,MAAM,CAAC9C,EAAE,GAAG8C,MAAM,GAAG3D,GAAG,CAAC;MACtE6D,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOgC,MAAM;EACf,CAAC,EAAE,CAACV,gBAAgB,CAAC,CAAC;EAEtB,MAAMa,uBAAuB,GAAIlB,KAAK,IAAK;IACzCpB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASkB,KAAK,CAAClB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMoC,mBAAmB,GAAGA,CAAA,KAAM;IAChCvC,WAAW,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMuC,iBAAiB,GAAGtI,KAAK,CAACG,WAAW,CAAC,CAACiG,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkC,kBAAkB,GAAGpI,WAAW,CAAC,MAAM;IAC3CgG,gBAAgB,CAAC+B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPnC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyC,kBAAkB,GAAGrI,WAAW,CAAEsI,MAAM,IAAK;IACjD,MAAM;MAAErC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClBxB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4D,MAAM,EAAE,MAAM,EAAErC,KAAK,CAAC;MAChDzB,WAAW,CAAC+D,QAAQ,IAAI;QACtB,IAAIP,WAAW,GAAGO,QAAQ,CAACrE,GAAG,CAACC,GAAG,IAAI;UACpC,IAAIA,GAAG,CAACa,EAAE,KAAKiB,KAAK,EAAE;YACpB;YACA,IAAIqC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAGnE,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAEkE,MAAM;gBAAEjE,iBAAiB,EAAEiE;cAAO,CAAC;YAC/D;UACF;UACA,OAAOnE,GAAG;QACZ,CAAC,CAAC;;QAEF;QACA6D,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;;QAE3C;QACA,OAAOA,WAAW;MACpB,CAAC,CAAC;MACFrC,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAsC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACrC,aAAa,EAAEqC,kBAAkB,EAAEhB,gBAAgB,CAAC,CAAC;;EAEzD;EACA,MAAMoB,mBAAmB,GAAGxI,WAAW,CAAC,MAAM;IAC5C0D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+E,oBAAoB,GAAGzI,WAAW,CAAC,MAAM;IAC7C0D,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkF,YAAY,GAAG1I,WAAW,CAAC,MAAM;IACrC,IAAIuD,SAAS,CAACoF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC3F,cAAc,CAAC4F,QAAQ,CAACrF,SAAS,CAACoF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE1F,iBAAiB,CAAC8E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExE,SAAS,CAACoF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE2C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIzF,cAAc,CAAC4F,QAAQ,CAACrF,SAAS,CAACoF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAACvC,SAAS,EAAEP,cAAc,EAAEyF,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG7I,WAAW,CAAEsI,MAAM,IAAK;IAC3CrF,iBAAiB,CAAC8E,IAAI,IAAIA,IAAI,CAACP,MAAM,CAACsB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC,CAAC;IAC/D3C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,eAAe,GAAG/I,WAAW,CAAEgF,EAAE,IAAK;IAC1CP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAMgE,WAAW,GAAGzE,QAAQ,CAAC+C,IAAI,CAACnD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACvD,MAAMiE,eAAe,GAAGD,WAAW,GAAGA,WAAW,CAACpB,UAAU,GAAG,CAAC;IAChEnD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEuE,eAAe,CAAC;;IAE/C;IACAzE,WAAW,CAACuD,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAAC7D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnF6D,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;MAC3C;MACA,MAAMkB,cAAc,GAAGlB,WAAW,CAACR,MAAM,CAACrD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHgE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpE,EAAE,GAAGqE,CAAC,CAACrE,EAAE,CAAC;MAC1CkE,cAAc,CAACI,OAAO,CAAC,CAACnF,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOiD,WAAW;IACpB,CAAC,CAAC;IACFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMmC,aAAa,GAAGvJ,WAAW,CAAEgF,EAAE,IAAK;IACxCP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAMwE,YAAY,GAAGjF,QAAQ,CAAC+C,IAAI,CAACnD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACxD,MAAMiE,eAAe,GAAGO,YAAY,GAAGA,YAAY,CAAC5B,UAAU,GAAG,CAAC;IAClEnD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEuE,eAAe,CAAC;;IAE/C;IACAzE,WAAW,CAACuD,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAAC7D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpF6D,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;MAC3C,MAAMkB,cAAc,GAAGlB,WAAW,CAACR,MAAM,CAACrD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHgE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpE,EAAE,GAAGqE,CAAC,CAACrE,EAAE,CAAC;MAC1CkE,cAAc,CAACI,OAAO,CAAC,CAACnF,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOiD,WAAW;IACpB,CAAC,CAAC;IACFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMqC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF7F,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM8F,YAAY,GAAGnF,QAAQ,CAC1BiD,MAAM,CAACrD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAoF,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAClE,EAAE,KAAK,QAAQ,IAAI,OAAOmE,CAAC,CAACnE,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOkE,CAAC,CAAClE,EAAE,GAAGmE,CAAC,CAACnE,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMyE,OAAO,GAAGD,YAAY,CAACxF,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb6E,IAAI,EAAEzF,GAAG,CAACyF,IAAI,GAAI,OAAOzF,GAAG,CAACyF,IAAI,KAAK,QAAQ,IAAIzF,GAAG,CAACyF,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAGzE,GAAG,CAACyF,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG1F,GAAG,CAACyF,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEzF,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG2F,IAAI,CAACC,KAAK,CAAC5F,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF6F,EAAE,EAAE,OAAO7F,GAAG,CAAC6F,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAC5F,GAAG,CAAC6F,EAAE,CAAC,GAAG7F,GAAG,CAAC6F,EAAE,IAAI,EAAE;QAClE5F,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG4F,KAAK,EAAE,OAAO9F,GAAG,CAAC+F,QAAQ,KAAK,QAAQ,GACpC/F,GAAG,CAAC+F,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG/F,GAAG,CAAC+F,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGhG,GAAG,CAAC+F,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EhG,GAAG,CAAC+F,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOjG,GAAG,CAACyD,UAAU,KAAK,QAAQ,GAAGzD,GAAG,CAACyD,UAAU,CAACuC,OAAO,CAAC,CAAC,CAAC,GAAGhG,GAAG,CAACyD,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMyC,WAAW,GAAG9F,QAAQ,CACzBiD,MAAM,CAACrD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACyD,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAEvD,GAAG,KAAKuD,GAAG,IAAI,OAAOvD,GAAG,CAACyD,UAAU,KAAK,QAAQ,GAAGzD,GAAG,CAACyD,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM0C,QAAQ,GAAG,MAAMzI,KAAK,CAAC0I,IAAI,CAAC,GAAGzI,OAAO,oBAAoB,EAAE;QAChEM,IAAI,EAAEuH,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC9H,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIiI,QAAQ,CAAClI,IAAI,IAAIkI,QAAQ,CAAClI,IAAI,CAACoI,KAAK,EAAE;QACxC;QACA,MAAMpE,WAAW,GAAG,GAAGtE,OAAO,CAAC+H,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAClI,IAAI,CAACqI,MAAM,EAAE;;QAExE;QACA,MAAMpE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAInB,IAAI,CAAC,CAAC,CAACoB,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEL,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;QAE/B;QACAV,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACA4E,UAAU,CAAC,MAAM;UACf,MAAMC,MAAM,GAAGrE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CoE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG1E,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC+D,MAAM,CAAC;UACjCD,UAAU,CAAC,MAAM;YACfpE,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC6D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRlC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMoH,kBAAkB,GAAGhL,WAAW,CAAC,CAACiG,KAAK,EAAEgF,KAAK,KAAK;IACvD9C,iBAAiB,CAAClC,KAAK,EAAEgF,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC9C,iBAAiB,CAAC,CAAC;EAEvB,MAAM+C,OAAO,GAAGhL,OAAO,CAAC,MAAOyC,WAAW,CAACuB,GAAG,CAACiH,GAAG,IAAI;IACpD,IAAI,EAAE5G,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC6G,cAAc,CAACD,GAAG,CAACvI,KAAK,CAAC,CAAC,IAAIuI,GAAG,CAACvI,KAAK,KAAK,SAAS,IAAIuI,GAAG,CAACvI,KAAK,KAAK,QAAQ,IAAIuI,GAAG,CAACvI,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIuI,GAAG,CAACvI,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEuI,GAAG,CAACvI,KAAK;QAChBC,UAAU,EAAEsI,GAAG,CAACtI,UAAU;QAC1BwI,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVxI,QAAQ,EAAE,KAAK;QACfyI,UAAU,EAAGpE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAChD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIiC,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMkH,iBAAiB,GAAGrE,MAAM,CAAChD,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEpC,OAAA,CAACZ,OAAO;cAACoK,KAAK,EAAEtE,MAAM,CAAChD,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACqH,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvE3J,OAAA,CAACb,IAAI;gBACHyK,KAAK,EAAEL,iBAAkB;gBACzBM,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,OAAO,EAAE,GAAG;kBAAE,kBAAkB,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEzB,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UACA,IAAIC,UAAU,GAAG,MAAM;UACvB,IAAIC,iBAAiB,GAAG,KAAK;UAC7B,IAAIzF,MAAM,CAAChD,GAAG,CAACE,iBAAiB,IAAI8C,MAAM,CAAChD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EsI,UAAU,GAAGxF,MAAM,CAAChD,GAAG,CAACE,iBAAiB;YACzCuI,iBAAiB,GAAG,IAAI;UAC1B;UACA,oBACE3K,OAAA,CAACZ,OAAO;YAACoK,KAAK,EAAEmB,iBAAiB,GAAGD,UAAU,GAAG,EAAG;YAACjB,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAC,QAAA,eACxE3J,OAAA,CAACb,IAAI;cACHyK,KAAK,EAAEc,UAAW;cAClBb,KAAK,EAAEc,iBAAiB,GAAG,SAAS,GAAG,SAAU;cACjDb,OAAO,EAAEa,iBAAiB,GAAG,QAAQ,GAAG,UAAW;cACnDZ,IAAI,EAAC,OAAO;cACZa,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAAC7D,MAAM,CAAChD,GAAG,CAACa,EAAE,EAAEmC,MAAM,CAAC8D,KAAK,IAAI,EAAE,CAAE;cACrE6B,SAAS;cACTb,EAAE,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEa,MAAM,EAAE,SAAS;gBAAE,kBAAkB,EAAE;kBAAEX,QAAQ,EAAE,QAAQ;kBAAEC,YAAY,EAAE,UAAU;kBAAEC,UAAU,EAAE,QAAQ;kBAAEzB,OAAO,EAAE;gBAAQ;cAAE;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAEd;MACF,CAAC;IACH;IACA,IAAIvB,GAAG,CAACvI,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEuI,GAAG,CAACvI,KAAK;QAChBC,UAAU,EAAEsI,GAAG,CAACtI,UAAU;QAC1BwI,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVxI,QAAQ,EAAE,KAAK;QACfyI,UAAU,EAAGpE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAChD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIiC,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACErC,OAAA,CAACb,IAAI;cACHyK,KAAK,EAAC,cAAI;cACVC,KAAK,EAAC,SAAS;cACfE,IAAI,EAAC,OAAO;cACZgB,IAAI,eAAE/K,OAAA,CAACN,QAAQ;gBAAA4K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBG,OAAO,EAAEA,CAAA,KAAMtD,aAAa,CAACpC,MAAM,CAAChD,GAAG,CAACa,EAAE,CAAE;cAC5CiH,EAAE,EAAE;gBAAEc,MAAM,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAEN;UACA,oBACEzK,OAAA,CAACb,IAAI;YACHyK,KAAK,EAAC,cAAI;YACVC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZgB,IAAI,eAAE/K,OAAA,CAACP,UAAU;cAAA6K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBG,OAAO,EAAEA,CAAA,KAAM9D,eAAe,CAAC5B,MAAM,CAAChD,GAAG,CAACa,EAAE,CAAE;YAC9CiH,EAAE,EAAE;cAAEc,MAAM,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAEN;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGvB,GAAG;MACNrI,QAAQ,EAAEqE,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAChD,GAAG,IAAIgD,MAAM,CAAChD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIiC,MAAM,CAAChD,GAAG,IAAIgD,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAO6G,GAAG,CAACrI,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDyI,UAAU,EAAGpE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAChD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIiG,GAAG,CAACvI,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEX,OAAA,CAAC7B,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAACkB,UAAU,EAAC,MAAM;YAACnB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOzE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,GAAG9D,MAAM,CAAC8D,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOhD,MAAM,CAAC8D,KAAK,KAAK,QAAQ,IAAI,CAACiC,KAAK,CAACvF,MAAM,CAACR,MAAM,CAAC8D,KAAK,CAAC,CAAC,GAAGtD,MAAM,CAACR,MAAM,CAAC8D,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGhD,MAAM,CAAC8D;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIvF,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACErC,OAAA,CAAC7B,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,eAAe;YAACG,EAAE,EAAE;cAAEkB,cAAc,EAAE;YAAe,CAAE;YAAAvB,QAAA,EACtFzE,MAAM,CAAC8D;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIvB,GAAG,CAACvI,KAAK,KAAK,MAAM,IAAIuE,MAAM,CAAC8D,KAAK,EAAE;UACxC,OAAO9D,MAAM,CAAC8D,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIsB,GAAG,CAACvI,KAAK,KAAK,IAAI,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC8D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACvI,KAAK,KAAK,OAAO,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOnB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC8D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACvI,KAAK,KAAK,IAAI,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnB,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC8D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACvI,KAAK,KAAK,UAAU,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO9D,MAAM,CAAC8D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG9D,MAAM,CAAC8D,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGhD,MAAM,CAAC8D,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIgB,GAAG,CAACvI,KAAK,KAAK,YAAY,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO9D,MAAM,CAAC8D,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIgB,GAAG,CAACvI,KAAK,KAAK,YAAY,IAAI,OAAOuE,MAAM,CAAC8D,KAAK,KAAK,QAAQ,IAAI,CAACiC,KAAK,CAACvF,MAAM,CAACR,MAAM,CAAC8D,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOtD,MAAM,CAACR,MAAM,CAAC8D,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOhD,MAAM,CAAC8D,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO9D,MAAM,CAAC8D,KAAK;QACrB;QACA,OAAO9D,MAAM,CAAC8D,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACzD,MAAM,CAAC4F,OAAO,CAAE,EAAE,CAACzK,WAAW,EAAE4B,QAAQ,EAAEyG,kBAAkB,EAAEjC,eAAe,EAAEQ,aAAa,CAAC,CAAC;;EAEjG;EACA,IAAI,CAAChF,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE1C,OAAA,CAAC9B,GAAG;MAAC8L,EAAE,EAAE;QAAEoB,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,gBACtC3J,OAAA,CAAC7B,UAAU;QAAC2L,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzK,OAAA,CAAC5B,MAAM;QACL0L,OAAO,EAAC,WAAW;QACnBc,OAAO,EAAEvK,OAAQ;QACjB2J,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,EACf;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEzK,OAAA,CAAC9B,GAAG;IAAAyL,QAAA,gBACF3J,OAAA,CAAC9B,GAAG;MAAC8L,EAAE,EAAE;QAAEpB,OAAO,EAAE,MAAM;QAAE2C,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9B,QAAA,gBACzF3J,OAAA,CAAC7B,UAAU;QAAC2L,OAAO,EAAC,IAAI;QAAC4B,YAAY;QAAA/B,QAAA,EAAC;MAEtC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzK,OAAA,CAAC9B,GAAG;QAAAyL,QAAA,gBACF3J,OAAA,CAAC5B,MAAM;UACL0L,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf8B,SAAS,eAAE3L,OAAA,CAACV,YAAY;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BG,OAAO,EAAE1G,cAAe;UACxB8F,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACf;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzK,OAAA,CAAC5B,MAAM;UACL0L,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf8B,SAAS,eAAE3L,OAAA,CAACL,gBAAgB;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCG,OAAO,EAAEpD,gBAAiB;UAC1BqE,QAAQ,EAAEnK,oBAAqB;UAC/BsI,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EAEbjI,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETzK,OAAA,CAAC5B,MAAM;UACL0L,OAAO,EAAC,UAAU;UAClB6B,SAAS,eAAE3L,OAAA,CAACT,cAAc;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BG,OAAO,EAAE7F,aAAc;UAAA4E,QAAA,EACxB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzK,OAAA,CAAC3B,KAAK;MAAC2L,EAAE,EAAE;QAAEX,KAAK,EAAE,MAAM;QAAEc,QAAQ,EAAE;MAAS,CAAE;MAAAR,QAAA,eAC/C3J,OAAA,CAAC9B,GAAG;QAAC8L,EAAE,EAAE;UAAE8B,MAAM,EAAE,MAAM;UAAEzC,KAAK,EAAE;QAAO,CAAE;QAAAM,QAAA,eACzC3J,OAAA,CAACX,QAAQ;UACP0M,IAAI,EAAEzJ,QAAS;UACf2G,OAAO,EAAEA,OAAQ;UACjB+C,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGnH,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAChD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIkC,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFiK,cAAc,EAAGpH,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAChD,GAAG,CAACc,OAAO,IAAIkC,MAAM,CAAChD,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAO6C,MAAM,CAACqH,MAAM,CAAC1L,QAAQ,IAAI,OAAOqE,MAAM,CAACqH,MAAM,CAAC1L,QAAQ,KAAK,UAAU,GAC3EqE,MAAM,CAACqH,MAAM,CAAC1L,QAAQ,CAACqE,MAAM,CAAC,GAAGA,MAAM,CAACqH,MAAM,CAAC1L,QAAQ;UAC3D,CAAE;UACF+E,gBAAgB,EAAEA,CAACC,MAAM,EAAE2G,MAAM,KAAK;YACpC,IAAI3G,MAAM,CAACF,UAAU,KAAK/C,SAAS,EAAE;cACnC,IAAI,OAAOiD,MAAM,CAACF,UAAU,KAAK,QAAQ,EAAE;gBACzCE,MAAM,CAACF,UAAU,GAAGD,MAAM,CAACG,MAAM,CAACF,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOC,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDyG,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9C1C,EAAE,EAAE;YACF,cAAc,EAAE;cACd2C,eAAe,EAAE,0BAA0B;cAC3C3B,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChB2B,eAAe,EAAE,0BAA0B;cAC3C9C,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrBQ,UAAU,EAAE,QAAQ;cACpBuC,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCG,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrB3C,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnB4C,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BF,OAAO,EAAE,OAAO;cAChBxC,UAAU,EAAE,QAAQ;cACpBuC,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClCvC,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnByC,UAAU,EAAE,MAAM;cAClB5B,UAAU,EAAE;YACd;UACF;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRzK,OAAA,CAACxB,MAAM;MACLmF,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzBqJ,OAAO,EAAE7G,kBAAmB;MAC5B8G,SAAS;MACThD,QAAQ,EAAC,IAAI;MACbiD,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA1D,QAAA,gBAEnB3J,OAAA,CAACvB,WAAW;QAAAkL,QAAA,eACV3J,OAAA,CAAC9B,GAAG;UAAC8L,EAAE,EAAE;YAAEpB,OAAO,EAAE,MAAM;YAAE2C,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBAClF3J,OAAA,CAAC7B,UAAU;YAAC2L,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CzK,OAAA,CAAC5B,MAAM;YACLuN,SAAS,eAAE3L,OAAA,CAACR,OAAO;cAAA8K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBG,OAAO,EAAErE,mBAAoB;YAC7BsD,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdzK,OAAA,CAACtB,aAAa;QAAC4O,QAAQ;QAACtD,EAAE,EAAE;UAAEuD,CAAC,EAAE;QAAE,CAAE;QAAA5D,QAAA,eACnC3J,OAAA,CAACF,aAAa;UACZgM,MAAM,EAAE,GAAI;UACZ0B,SAAS,EAAEzM,cAAc,CAAC2B,MAAO;UACjC+K,QAAQ,EAAE,EAAG;UACbpE,KAAK,EAAC,MAAM;UAAAM,QAAA,EAEXA,CAAC;YAAE7G,KAAK;YAAE6F;UAAM,CAAC,KAAK;YACrB,MAAMtC,MAAM,GAAGtF,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACE9C,OAAA,CAACnB,QAAQ;cAEP8J,KAAK,EAAEA,KAAM;cACb+E,cAAc;cACdC,eAAe,eACb3N,OAAA,CAACf,UAAU;gBACT2O,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBhD,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAACP,MAAM,CAAE;gBAAAsD,QAAA,eAEpC3J,OAAA,CAACP,UAAU;kBAAA6K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAd,QAAA,eAED3J,OAAA,CAAClB,cAAc;gBAAC8L,OAAO,EAAEA,CAAA,KAAMxE,kBAAkB,CAACC,MAAM,CAAE;gBAAAsD,QAAA,eACxD3J,OAAA,CAACjB,YAAY;kBAAC8O,OAAO,EAAExH;gBAAO;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZpE,MAAM;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBzK,OAAA,CAACrB,aAAa;QAAAgL,QAAA,eACZ3J,OAAA,CAAC5B,MAAM;UAACwM,OAAO,EAAEzE,kBAAmB;UAAAwD,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzK,OAAA,CAACxB,MAAM;MACLmF,IAAI,EAAEnC,eAAgB;MACtBwL,OAAO,EAAExG,oBAAqB;MAC9ByG,SAAS;MACThD,QAAQ,EAAC,IAAI;MACbiD,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA1D,QAAA,gBAEnB3J,OAAA,CAACvB,WAAW;QAAAkL,QAAA,EAAC;MAAK;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCzK,OAAA,CAACtB,aAAa;QAAAiL,QAAA,eACZ3J,OAAA,CAAChB,SAAS;UACR8O,SAAS;UACTC,MAAM,EAAC,OAAO;UACdhL,EAAE,EAAC,MAAM;UACT6G,KAAK,EAAC,0BAAM;UACZoE,IAAI,EAAC,MAAM;UACXf,SAAS;UACTnD,OAAO,EAAC,UAAU;UAClBd,KAAK,EAAE1H,SAAU;UACjB2M,QAAQ,EAAGC,CAAC,IAAK3M,YAAY,CAAC2M,CAAC,CAACC,MAAM,CAACnF,KAAK;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBzK,OAAA,CAACrB,aAAa;QAAAgL,QAAA,gBACZ3J,OAAA,CAAC5B,MAAM;UAACwM,OAAO,EAAEpE,oBAAqB;UAAAmD,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDzK,OAAA,CAAC5B,MAAM;UAACwM,OAAO,EAAEnE,YAAa;UAACoD,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETzK,OAAA,CAAC1B,QAAQ;MACPqF,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpByK,gBAAgB,EAAE,IAAK;MACvBpB,OAAO,EAAE/G,mBAAoB;MAC7BoI,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAA5E,QAAA,eAE1D3J,OAAA,CAACzB,KAAK;QAACyO,OAAO,EAAE/G,mBAAoB;QAACpC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAA8F,QAAA,EAC9DlG,QAAQ,CAACG;MAAO;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAChK,EAAA,CAlwBIP,aAAa;AAAAsO,EAAA,GAAbtO,aAAa;AAowBnB,eAAeA,aAAa;AAAC,IAAAsO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}