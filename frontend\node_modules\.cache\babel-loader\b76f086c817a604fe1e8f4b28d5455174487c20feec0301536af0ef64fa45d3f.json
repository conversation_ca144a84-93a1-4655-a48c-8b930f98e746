{"ast": null, "code": "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    lastArgs = args;\n    if (frameId) {\n      return;\n    }\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n  return wrapperFn;\n};\nexport default rafSchd;", "map": {"version": 3, "names": ["rafSchd", "fn", "lastArgs", "frameId", "wrapperFn", "_len", "arguments", "length", "args", "Array", "_key", "requestAnimationFrame", "apply", "cancel", "cancelAnimationFrame"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/raf-schd/dist/raf-schd.esm.js"], "sourcesContent": ["var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAG,SAASA,OAAOA,CAACC,EAAE,EAAE;EACjC,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAElB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAR,QAAQ,GAAGM,IAAI;IAEf,IAAIL,OAAO,EAAE;MACX;IACF;IAEAA,OAAO,GAAGQ,qBAAqB,CAAC,YAAY;MAC1CR,OAAO,GAAG,IAAI;MACdF,EAAE,CAACW,KAAK,CAAC,KAAK,CAAC,EAAEV,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAEDE,SAAS,CAACS,MAAM,GAAG,YAAY;IAC7B,IAAI,CAACV,OAAO,EAAE;MACZ;IACF;IAEAW,oBAAoB,CAACX,OAAO,CAAC;IAC7BA,OAAO,GAAG,IAAI;EAChB,CAAC;EAED,OAAOC,SAAS;AAClB,CAAC;AAED,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}