{"ast": null, "code": "import { GridFilterInputBoolean } from '../components/panel/filterPanel/GridFilterInputBoolean';\nimport { convertLegacyOperators } from './utils';\nexport const getGridBooleanOperators = () => convertLegacyOperators([{\n  value: 'is',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const valueAsBoolean = filterItem.value === 'true';\n    return value => {\n      return Boolean(value) === valueAsBoolean;\n    };\n  },\n  InputComponent: GridFilterInputBoolean\n}]);", "map": {"version": 3, "names": ["GridFilterInputBoolean", "convertLegacyOperators", "getGridBooleanOperators", "value", "getApplyFilterFnV7", "filterItem", "valueAsBoolean", "Boolean", "InputComponent"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/colDef/gridBooleanOperators.js"], "sourcesContent": ["import { GridFilterInputBoolean } from '../components/panel/filterPanel/GridFilterInputBoolean';\nimport { convertLegacyOperators } from './utils';\nexport const getGridBooleanOperators = () => convertLegacyOperators([{\n  value: 'is',\n  getApplyFilterFnV7: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const valueAsBoolean = filterItem.value === 'true';\n    return value => {\n      return Boolean(value) === valueAsBoolean;\n    };\n  },\n  InputComponent: GridFilterInputBoolean\n}]);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,sBAAsB,QAAQ,SAAS;AAChD,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAMD,sBAAsB,CAAC,CAAC;EACnEE,KAAK,EAAE,IAAI;EACXC,kBAAkB,EAAEC,UAAU,IAAI;IAChC,IAAI,CAACA,UAAU,CAACF,KAAK,EAAE;MACrB,OAAO,IAAI;IACb;IACA,MAAMG,cAAc,GAAGD,UAAU,CAACF,KAAK,KAAK,MAAM;IAClD,OAAOA,KAAK,IAAI;MACd,OAAOI,OAAO,CAACJ,KAAK,CAAC,KAAKG,cAAc;IAC1C,CAAC;EACH,CAAC;EACDE,cAAc,EAAER;AAClB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}