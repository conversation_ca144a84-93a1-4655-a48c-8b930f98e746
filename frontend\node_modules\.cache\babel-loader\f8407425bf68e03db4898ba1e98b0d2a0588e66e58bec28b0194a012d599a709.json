{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ProcessForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Grid, InputAdornment, CircularProgress, Card, CardContent, Chip, Alert, Tooltip } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SendIcon from '@mui/icons-material/Send';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport InfoIcon from '@mui/icons-material/Info';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// localStorage的键名\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STORAGE_KEY = 'processFormParams';\nconst ProcessForm = ({\n  fileId,\n  worksheet,\n  onSubmit,\n  onBack,\n  onError\n}) => {\n  _s();\n  // 从localStorage获取上次保存的参数\n  const [startCol, setStartCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).startCol || '' : '';\n  });\n  const [endCol, setEndCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).endCol || '' : '';\n  });\n  const [perHourRate, setPerHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).perHourRate || '' : '';\n  });\n  const [cbuCarHourRate, setCbuCarHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).cbuCarHourRate || '' : '';\n  });\n  const [wtyHourRate, setWtyHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyHourRate || '' : '';\n  });\n  const [wtyCommissionRate, setWtyCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyCommissionRate || '' : '';\n  });\n  const [commissionRate, setCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).commissionRate || '' : '';\n  });\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // 验证函数\n  const validateColumn = value => {\n    const colRegex = /^[A-Za-z]+$/;\n    if (!value) return '请输入列名';\n    if (!colRegex.test(value)) return '列名格式无效，请使用字母 (例如: A, AB, AT)';\n    return '';\n  };\n  const validateRate = (value, fieldName) => {\n    if (value && (isNaN(value) || parseFloat(value) < 0)) {\n      return `${fieldName}必须是正数`;\n    }\n    return '';\n  };\n\n  // 当参数变化时保存到localStorage\n  useEffect(() => {\n    const paramsToSave = {\n      startCol,\n      endCol,\n      perHourRate,\n      cbuCarHourRate,\n      wtyHourRate,\n      wtyCommissionRate,\n      commissionRate\n    };\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(paramsToSave));\n  }, [startCol, endCol, perHourRate, cbuCarHourRate, wtyHourRate, wtyCommissionRate, commissionRate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // 验证输入\n    const newErrors = {};\n    const startColError = validateColumn(startCol);\n    const endColError = validateColumn(endCol);\n    const perHourRateError = validateRate(perHourRate, '小时费率');\n    const cbuCarHourRateError = validateRate(cbuCarHourRate, 'CBU CAR小时费率');\n    const wtyHourRateError = validateRate(wtyHourRate, 'WTY小时费率');\n    const wtyCommissionRateError = validateRate(wtyCommissionRate, 'WTY佣金率');\n    const commissionRateError = validateRate(commissionRate, '佣金率');\n    if (startColError) newErrors.startCol = startColError;\n    if (endColError) newErrors.endCol = endColError;\n    if (perHourRateError) newErrors.perHourRate = perHourRateError;\n    if (cbuCarHourRateError) newErrors.cbuCarHourRate = cbuCarHourRateError;\n    if (wtyHourRateError) newErrors.wtyHourRate = wtyHourRateError;\n    if (wtyCommissionRateError) newErrors.wtyCommissionRate = wtyCommissionRateError;\n    if (commissionRateError) newErrors.commissionRate = commissionRateError;\n    if (commissionRate && parseFloat(commissionRate) > 1) {\n      newErrors.commissionRate = '佣金率应在0-1之间';\n    }\n    if (wtyCommissionRate && parseFloat(wtyCommissionRate) > 1) {\n      newErrors.wtyCommissionRate = 'WTY佣金率应在0-1之间';\n    }\n    setErrors(newErrors);\n    if (Object.keys(newErrors).length > 0) {\n      onError('请检查输入的参数');\n      return;\n    }\n    setLoading(true);\n    try {\n      const requestData = {\n        fileId,\n        worksheet,\n        startCol,\n        endCol,\n        perHourRate: perHourRate || undefined,\n        cbuCarHourRate: cbuCarHourRate || undefined,\n        commissionRate: commissionRate || undefined\n      };\n      const response = await axios.post(`${API_URL}/process`, requestData);\n      if (response.data && response.data.success) {\n        onSubmit({\n          startCol,\n          endCol,\n          perHourRate,\n          cbuCarHourRate,\n          commissionRate\n        }, response.data.data);\n      } else {\n        onError('处理数据失败，请重试');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('处理数据出错:', error);\n      onError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || '处理数据失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mb: 2,\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            fontSize: 32,\n            color: 'primary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: 'text.primary'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: 1,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary'\n          },\n          children: \"\\u5F53\\u524D\\u5DE5\\u4F5C\\u8868:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: worksheet,\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 38\n        }, this),\n        sx: {\n          textAlign: 'left'\n        },\n        children: \"\\u8BF7\\u8BBE\\u7F6E\\u6570\\u636E\\u5904\\u7406\\u7684\\u5217\\u8303\\u56F4\\u548C\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\\u3002\\u5217\\u8303\\u56F4\\u4E3A\\u5FC5\\u586B\\u9879\\uFF0C\\u4F63\\u91D1\\u53C2\\u6570\\u4E3A\\u53EF\\u9009\\u9879\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(TableViewIcon, {\n            sx: {\n              color: 'primary.main',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"\\u6570\\u636E\\u5217\\u8303\\u56F4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u5FC5\\u586B\",\n            size: \"small\",\n            color: \"error\",\n            sx: {\n              ml: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              required: true,\n              fullWidth: true,\n              label: \"\\u8D77\\u59CB\\u5217\",\n              value: startCol,\n              onChange: e => {\n                const value = e.target.value.toUpperCase();\n                setStartCol(value);\n                if (errors.startCol) {\n                  setErrors(prev => ({\n                    ...prev,\n                    startCol: ''\n                  }));\n                }\n              },\n              placeholder: \"\\u4F8B\\u5982: AT\",\n              error: !!errors.startCol,\n              helperText: errors.startCol || \"请输入起始列的字母\",\n              inputProps: {\n                maxLength: 3\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              required: true,\n              fullWidth: true,\n              label: \"\\u7ED3\\u675F\\u5217\",\n              value: endCol,\n              onChange: e => {\n                const value = e.target.value.toUpperCase();\n                setEndCol(value);\n                if (errors.endCol) {\n                  setErrors(prev => ({\n                    ...prev,\n                    endCol: ''\n                  }));\n                }\n              },\n              placeholder: \"\\u4F8B\\u5982: BP\",\n              error: !!errors.endCol,\n              helperText: errors.endCol || \"请输入结束列的字母\",\n              inputProps: {\n                maxLength: 3\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(MonetizationOnIcon, {\n            sx: {\n              color: 'secondary.main',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u53EF\\u9009\",\n            size: \"small\",\n            color: \"success\",\n            sx: {\n              ml: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"\\u5C0F\\u65F6\\u8D39\\u7387\",\n              type: \"number\",\n              value: perHourRate,\n              onChange: e => {\n                setPerHourRate(e.target.value);\n                if (errors.perHourRate) {\n                  setErrors(prev => ({\n                    ...prev,\n                    perHourRate: ''\n                  }));\n                }\n              },\n              placeholder: \"\\u4F8B\\u5982: 65\",\n              error: !!errors.perHourRate,\n              helperText: errors.perHourRate || \"每小时费率 (RM)\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: \"RM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 37\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"CBU CAR\\u5C0F\\u65F6\\u8D39\\u7387\",\n              type: \"number\",\n              value: cbuCarHourRate,\n              onChange: e => {\n                setCbuCarHourRate(e.target.value);\n                if (errors.cbuCarHourRate) {\n                  setErrors(prev => ({\n                    ...prev,\n                    cbuCarHourRate: ''\n                  }));\n                }\n              },\n              placeholder: \"\\u4F8B\\u5982: 80\",\n              error: !!errors.cbuCarHourRate,\n              helperText: errors.cbuCarHourRate || \"CBU CAR每小时费率 (RM)\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: \"RM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 37\n                }, this)\n              },\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u4F63\\u91D1\\u6BD4\\u4F8B\\uFF0C\\u8303\\u56F4\\u57280\\u52301\\u4E4B\\u95F4\\uFF0C\\u4F8B\\u59820.6\\u8868\\u793A60%\",\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"\\u4F63\\u91D1\\u7387\",\n                type: \"number\",\n                value: commissionRate,\n                onChange: e => {\n                  setCommissionRate(e.target.value);\n                  if (errors.commissionRate) {\n                    setErrors(prev => ({\n                      ...prev,\n                      commissionRate: ''\n                    }));\n                  }\n                },\n                placeholder: \"\\u4F8B\\u5982: 0.6\",\n                error: !!errors.commissionRate,\n                helperText: errors.commissionRate || \"佣金比例 (0-1 之间)\",\n                inputProps: {\n                  step: \"0.01\",\n                  min: \"0\",\n                  max: \"1\"\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 2\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 22\n        }, this),\n        onClick: onBack,\n        disabled: loading,\n        children: \"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u6B65\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"contained\",\n        endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20,\n          color: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 79\n        }, this),\n        disabled: loading,\n        children: loading ? '处理中...' : '开始处理'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessForm, \"aaZMYVbgq/Mn5VSVfdKpX66stbw=\");\n_c = ProcessForm;\nexport default ProcessForm;\nvar _c;\n$RefreshReg$(_c, \"ProcessForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "InputAdornment", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ArrowBackIcon", "SendIcon", "SettingsIcon", "InfoIcon", "MonetizationOnIcon", "TableViewIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "STORAGE_KEY", "ProcessForm", "fileId", "worksheet", "onSubmit", "onBack", "onError", "_s", "startCol", "setStartCol", "savedParams", "localStorage", "getItem", "JSON", "parse", "endCol", "setEndCol", "perHourRate", "setPerHourRate", "cbuCarHourRate", "setCbuCarHourRate", "wtyHourRate", "setWtyHourRate", "wtyCommissionRate", "setWtyCommissionRate", "commissionRate", "setCommissionRate", "loading", "setLoading", "errors", "setErrors", "validateColumn", "value", "colRegex", "test", "validateRate", "fieldName", "isNaN", "parseFloat", "paramsToSave", "setItem", "stringify", "handleSubmit", "e", "preventDefault", "newErrors", "startColError", "endColError", "perHourRateError", "cbuCarHourRateError", "wtyHourRateError", "wtyCommissionRateError", "commissionRateError", "Object", "keys", "length", "requestData", "undefined", "response", "post", "data", "success", "error", "_error$response", "_error$response$data", "console", "component", "noValidate", "children", "sx", "textAlign", "mb", "display", "alignItems", "justifyContent", "gap", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "severity", "icon", "mr", "ml", "container", "spacing", "item", "xs", "sm", "required", "fullWidth", "onChange", "target", "toUpperCase", "prev", "placeholder", "helperText", "inputProps", "max<PERSON><PERSON><PERSON>", "borderRadius", "md", "type", "InputProps", "startAdornment", "position", "title", "step", "min", "max", "startIcon", "onClick", "disabled", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ProcessForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  TextField,\n  Button,\n  Grid,\n  InputAdornment,\n  CircularProgress,\n  Card,\n  CardContent,\n  Chip,\n  Alert,\n  Tooltip\n} from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SendIcon from '@mui/icons-material/Send';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport InfoIcon from '@mui/icons-material/Info';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// localStorage的键名\nconst STORAGE_KEY = 'processFormParams';\n\nconst ProcessForm = ({ fileId, worksheet, onSubmit, onBack, onError }) => {\n\n  // 从localStorage获取上次保存的参数\n  const [startCol, setStartCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).startCol || '' : '';\n  });\n\n  const [endCol, setEndCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).endCol || '' : '';\n  });\n\n  const [perHourRate, setPerHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).perHourRate || '' : '';\n  });\n\n  const [cbuCarHourRate, setCbuCarHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).cbuCarHourRate || '' : '';\n  });\n\n  const [wtyHourRate, setWtyHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyHourRate || '' : '';\n  });\n\n  const [wtyCommissionRate, setWtyCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).wtyCommissionRate || '' : '';\n  });\n\n  const [commissionRate, setCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).commissionRate || '' : '';\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // 验证函数\n  const validateColumn = (value) => {\n    const colRegex = /^[A-Za-z]+$/;\n    if (!value) return '请输入列名';\n    if (!colRegex.test(value)) return '列名格式无效，请使用字母 (例如: A, AB, AT)';\n    return '';\n  };\n\n  const validateRate = (value, fieldName) => {\n    if (value && (isNaN(value) || parseFloat(value) < 0)) {\n      return `${fieldName}必须是正数`;\n    }\n    return '';\n  };\n  \n  // 当参数变化时保存到localStorage\n  useEffect(() => {\n    const paramsToSave = {\n      startCol,\n      endCol,\n      perHourRate,\n      cbuCarHourRate,\n      wtyHourRate,\n      wtyCommissionRate,\n      commissionRate\n    };\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(paramsToSave));\n  }, [startCol, endCol, perHourRate, cbuCarHourRate, wtyHourRate, wtyCommissionRate, commissionRate]);\n  \n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // 验证输入\n    const newErrors = {};\n\n    const startColError = validateColumn(startCol);\n    const endColError = validateColumn(endCol);\n    const perHourRateError = validateRate(perHourRate, '小时费率');\n    const cbuCarHourRateError = validateRate(cbuCarHourRate, 'CBU CAR小时费率');\n    const wtyHourRateError = validateRate(wtyHourRate, 'WTY小时费率');\n    const wtyCommissionRateError = validateRate(wtyCommissionRate, 'WTY佣金率');\n    const commissionRateError = validateRate(commissionRate, '佣金率');\n\n    if (startColError) newErrors.startCol = startColError;\n    if (endColError) newErrors.endCol = endColError;\n    if (perHourRateError) newErrors.perHourRate = perHourRateError;\n    if (cbuCarHourRateError) newErrors.cbuCarHourRate = cbuCarHourRateError;\n    if (wtyHourRateError) newErrors.wtyHourRate = wtyHourRateError;\n    if (wtyCommissionRateError) newErrors.wtyCommissionRate = wtyCommissionRateError;\n    if (commissionRateError) newErrors.commissionRate = commissionRateError;\n\n    if (commissionRate && (parseFloat(commissionRate) > 1)) {\n      newErrors.commissionRate = '佣金率应在0-1之间';\n    }\n\n    if (wtyCommissionRate && (parseFloat(wtyCommissionRate) > 1)) {\n      newErrors.wtyCommissionRate = 'WTY佣金率应在0-1之间';\n    }\n\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      onError('请检查输入的参数');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const requestData = {\n        fileId,\n        worksheet,\n        startCol,\n        endCol,\n        perHourRate: perHourRate || undefined,\n        cbuCarHourRate: cbuCarHourRate || undefined,\n        commissionRate: commissionRate || undefined\n      };\n\n      const response = await axios.post(`${API_URL}/process`, requestData);\n\n      if (response.data && response.data.success) {\n        onSubmit(\n          { startCol, endCol, perHourRate, cbuCarHourRate, commissionRate },\n          response.data.data\n        );\n      } else {\n        onError('处理数据失败，请重试');\n      }\n    } catch (error) {\n      console.error('处理数据出错:', error);\n      onError(error.response?.data?.error || '处理数据失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {/* 标题区域 */}\n      <Box sx={{ textAlign: 'center', mb: 4 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2, gap: 2 }}>\n          <SettingsIcon sx={{ fontSize: 32, color: 'primary.main' }} />\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary' }}>\n            参数设置\n          </Typography>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>\n          <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n            当前工作表:\n          </Typography>\n          <Chip label={worksheet} size=\"small\" color=\"primary\" variant=\"outlined\" />\n        </Box>\n\n        <Alert severity=\"info\" icon={<InfoIcon />} sx={{ textAlign: 'left' }}>\n          请设置数据处理的列范围和佣金计算参数。列范围为必填项，佣金参数为可选项。\n        </Alert>\n      </Box>\n\n      {/* 列范围设置 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <TableViewIcon sx={{ color: 'primary.main', mr: 1 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n              数据列范围\n            </Typography>\n            <Chip label=\"必填\" size=\"small\" color=\"error\" sx={{ ml: 2 }} />\n          </Box>\n\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"起始列\"\n                  value={startCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setStartCol(value);\n                    if (errors.startCol) {\n                      setErrors(prev => ({ ...prev, startCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: AT\"\n                  error={!!errors.startCol}\n                  helperText={errors.startCol || \"请输入起始列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"结束列\"\n                  value={endCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setEndCol(value);\n                    if (errors.endCol) {\n                      setErrors(prev => ({ ...prev, endCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: BP\"\n                  error={!!errors.endCol}\n                  helperText={errors.endCol || \"请输入结束列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n\n      {/* 佣金计算参数 */}\n      <Card sx={{ mb: 4 }}>\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <MonetizationOnIcon sx={{ color: 'secondary.main', mr: 1 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                佣金计算参数\n              </Typography>\n              <Chip label=\"可选\" size=\"small\" color=\"success\" sx={{ ml: 2 }} />\n            </Box>\n\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  fullWidth\n                  label=\"小时费率\"\n                  type=\"number\"\n                  value={perHourRate}\n                  onChange={(e) => {\n                    setPerHourRate(e.target.value);\n                    if (errors.perHourRate) {\n                      setErrors(prev => ({ ...prev, perHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 65\"\n                  error={!!errors.perHourRate}\n                  helperText={errors.perHourRate || \"每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  fullWidth\n                  label=\"CBU CAR小时费率\"\n                  type=\"number\"\n                  value={cbuCarHourRate}\n                  onChange={(e) => {\n                    setCbuCarHourRate(e.target.value);\n                    if (errors.cbuCarHourRate) {\n                      setErrors(prev => ({ ...prev, cbuCarHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 80\"\n                  error={!!errors.cbuCarHourRate}\n                  helperText={errors.cbuCarHourRate || \"CBU CAR每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6} md={4}>\n                <Tooltip title=\"佣金比例，范围在0到1之间，例如0.6表示60%\">\n                  <TextField\n                    fullWidth\n                    label=\"佣金率\"\n                    type=\"number\"\n                    value={commissionRate}\n                    onChange={(e) => {\n                      setCommissionRate(e.target.value);\n                      if (errors.commissionRate) {\n                        setErrors(prev => ({ ...prev, commissionRate: '' }));\n                      }\n                    }}\n                    placeholder=\"例如: 0.6\"\n                    error={!!errors.commissionRate}\n                    helperText={errors.commissionRate || \"佣金比例 (0-1 之间)\"}\n                    inputProps={{ step: \"0.01\", min: \"0\", max: \"1\" }}\n                    sx={{\n                      '& .MuiOutlinedInput-root': {\n                        borderRadius: 2,\n                      },\n                    }}\n                  />\n                </Tooltip>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n\n      {/* 操作按钮 */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>\n        <Button\n          variant=\"outlined\"\n          startIcon={<ArrowBackIcon />}\n          onClick={onBack}\n          disabled={loading}\n        >\n          返回上一步\n        </Button>\n\n        <Button\n          type=\"submit\"\n          variant=\"contained\"\n          endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SendIcon />}\n          disabled={loading}\n        >\n          {loading ? '处理中...' : '开始处理'}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default ProcessForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,mBAAmB;AAEvC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,QAAQ;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAExE;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,MAAM;IAC7C,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACF,QAAQ,IAAI,EAAE,GAAG,EAAE;EAClE,CAAC,CAAC;EAEF,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,MAAM;IACzC,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACK,MAAM,IAAI,EAAE,GAAG,EAAE;EAChE,CAAC,CAAC;EAEF,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,MAAM;IACnD,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACO,WAAW,IAAI,EAAE,GAAG,EAAE;EACrE,CAAC,CAAC;EAEF,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,MAAM;IACzD,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACS,cAAc,IAAI,EAAE,GAAG,EAAE;EACxE,CAAC,CAAC;EAEF,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,MAAM;IACnD,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACW,WAAW,IAAI,EAAE,GAAG,EAAE;EACrE,CAAC,CAAC;EAEF,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhD,QAAQ,CAAC,MAAM;IAC/D,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACa,iBAAiB,IAAI,EAAE,GAAG,EAAE;EAC3E,CAAC,CAAC;EAEF,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IACzD,MAAMkC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACZ,WAAW,CAAC;IACrD,OAAOU,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACe,cAAc,IAAI,EAAE,GAAG,EAAE;EACxE,CAAC,CAAC;EAEF,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMuD,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMC,QAAQ,GAAG,aAAa;IAC9B,IAAI,CAACD,KAAK,EAAE,OAAO,OAAO;IAC1B,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE,OAAO,8BAA8B;IAChE,OAAO,EAAE;EACX,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACH,KAAK,EAAEI,SAAS,KAAK;IACzC,IAAIJ,KAAK,KAAKK,KAAK,CAACL,KAAK,CAAC,IAAIM,UAAU,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;MACpD,OAAO,GAAGI,SAAS,OAAO;IAC5B;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA3D,SAAS,CAAC,MAAM;IACd,MAAM8D,YAAY,GAAG;MACnB/B,QAAQ;MACRO,MAAM;MACNE,WAAW;MACXE,cAAc;MACdE,WAAW;MACXE,iBAAiB;MACjBE;IACF,CAAC;IACDd,YAAY,CAAC6B,OAAO,CAACxC,WAAW,EAAEa,IAAI,CAAC4B,SAAS,CAACF,YAAY,CAAC,CAAC;EACjE,CAAC,EAAE,CAAC/B,QAAQ,EAAEO,MAAM,EAAEE,WAAW,EAAEE,cAAc,EAAEE,WAAW,EAAEE,iBAAiB,EAAEE,cAAc,CAAC,CAAC;EAEnG,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,MAAMC,aAAa,GAAGf,cAAc,CAACvB,QAAQ,CAAC;IAC9C,MAAMuC,WAAW,GAAGhB,cAAc,CAAChB,MAAM,CAAC;IAC1C,MAAMiC,gBAAgB,GAAGb,YAAY,CAAClB,WAAW,EAAE,MAAM,CAAC;IAC1D,MAAMgC,mBAAmB,GAAGd,YAAY,CAAChB,cAAc,EAAE,aAAa,CAAC;IACvE,MAAM+B,gBAAgB,GAAGf,YAAY,CAACd,WAAW,EAAE,SAAS,CAAC;IAC7D,MAAM8B,sBAAsB,GAAGhB,YAAY,CAACZ,iBAAiB,EAAE,QAAQ,CAAC;IACxE,MAAM6B,mBAAmB,GAAGjB,YAAY,CAACV,cAAc,EAAE,KAAK,CAAC;IAE/D,IAAIqB,aAAa,EAAED,SAAS,CAACrC,QAAQ,GAAGsC,aAAa;IACrD,IAAIC,WAAW,EAAEF,SAAS,CAAC9B,MAAM,GAAGgC,WAAW;IAC/C,IAAIC,gBAAgB,EAAEH,SAAS,CAAC5B,WAAW,GAAG+B,gBAAgB;IAC9D,IAAIC,mBAAmB,EAAEJ,SAAS,CAAC1B,cAAc,GAAG8B,mBAAmB;IACvE,IAAIC,gBAAgB,EAAEL,SAAS,CAACxB,WAAW,GAAG6B,gBAAgB;IAC9D,IAAIC,sBAAsB,EAAEN,SAAS,CAACtB,iBAAiB,GAAG4B,sBAAsB;IAChF,IAAIC,mBAAmB,EAAEP,SAAS,CAACpB,cAAc,GAAG2B,mBAAmB;IAEvE,IAAI3B,cAAc,IAAKa,UAAU,CAACb,cAAc,CAAC,GAAG,CAAE,EAAE;MACtDoB,SAAS,CAACpB,cAAc,GAAG,YAAY;IACzC;IAEA,IAAIF,iBAAiB,IAAKe,UAAU,CAACf,iBAAiB,CAAC,GAAG,CAAE,EAAE;MAC5DsB,SAAS,CAACtB,iBAAiB,GAAG,eAAe;IAC/C;IAEAO,SAAS,CAACe,SAAS,CAAC;IAEpB,IAAIQ,MAAM,CAACC,IAAI,CAACT,SAAS,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MACrCjD,OAAO,CAAC,UAAU,CAAC;MACnB;IACF;IAEAsB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM4B,WAAW,GAAG;QAClBtD,MAAM;QACNC,SAAS;QACTK,QAAQ;QACRO,MAAM;QACNE,WAAW,EAAEA,WAAW,IAAIwC,SAAS;QACrCtC,cAAc,EAAEA,cAAc,IAAIsC,SAAS;QAC3ChC,cAAc,EAAEA,cAAc,IAAIgC;MACpC,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,IAAI,CAAC,GAAG9D,OAAO,UAAU,EAAE2D,WAAW,CAAC;MAEpE,IAAIE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CzD,QAAQ,CACN;UAAEI,QAAQ;UAAEO,MAAM;UAAEE,WAAW;UAAEE,cAAc;UAAEM;QAAe,CAAC,EACjEiC,QAAQ,CAACE,IAAI,CAACA,IAChB,CAAC;MACH,CAAC,MAAM;QACLtD,OAAO,CAAC,YAAY,CAAC;MACvB;IACF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxD,OAAO,CAAC,EAAAyD,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,YAAY,CAAC;IACtD,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE7B,OAAA,CAACrB,GAAG;IAACwF,SAAS,EAAC,MAAM;IAAC9D,QAAQ,EAAEsC,YAAa;IAACyB,UAAU;IAAAC,QAAA,gBAEtDrE,OAAA,CAACrB,GAAG;MAAC2F,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACtCrE,OAAA,CAACrB,GAAG;QAAC2F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,QAAQ;UAAEH,EAAE,EAAE,CAAC;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBAC1FrE,OAAA,CAACP,YAAY;UAAC6E,EAAE,EAAE;YAAEO,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DlF,OAAA,CAACpB,UAAU;UAACuG,OAAO,EAAC,IAAI;UAACb,EAAE,EAAE;YAAEc,UAAU,EAAE,GAAG;YAAEN,KAAK,EAAE;UAAe,CAAE;UAAAT,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENlF,OAAA,CAACrB,GAAG;QAAC2F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,QAAQ;UAAEC,GAAG,EAAE,CAAC;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAC1FrE,OAAA,CAACpB,UAAU;UAACuG,OAAO,EAAC,OAAO;UAACb,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAiB,CAAE;UAAAT,QAAA,EAAC;QAE7D;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblF,OAAA,CAACZ,IAAI;UAACiG,KAAK,EAAEjF,SAAU;UAACkF,IAAI,EAAC,OAAO;UAACR,KAAK,EAAC,SAAS;UAACK,OAAO,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAENlF,OAAA,CAACX,KAAK;QAACkG,QAAQ,EAAC,MAAM;QAACC,IAAI,eAAExF,OAAA,CAACN,QAAQ;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACZ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEtE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlF,OAAA,CAACd,IAAI;MAACoF,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClBrE,OAAA,CAACb,WAAW;QAAAkF,QAAA,gBACVrE,OAAA,CAACrB,GAAG;UAAC2F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBACxDrE,OAAA,CAACJ,aAAa;YAAC0E,EAAE,EAAE;cAAEQ,KAAK,EAAE,cAAc;cAAEW,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDlF,OAAA,CAACpB,UAAU;YAACuG,OAAO,EAAC,IAAI;YAACb,EAAE,EAAE;cAAEc,UAAU,EAAE;YAAI,CAAE;YAAAf,QAAA,EAAC;UAElD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblF,OAAA,CAACZ,IAAI;YAACiG,KAAK,EAAC,cAAI;YAACC,IAAI,EAAC,OAAO;YAACR,KAAK,EAAC,OAAO;YAACR,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEJlF,OAAA,CAACjB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAvB,QAAA,gBACzBrE,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvBrE,OAAA,CAACnB,SAAS;cACRmH,QAAQ;cACRC,SAAS;cACTZ,KAAK,EAAC,oBAAK;cACXpD,KAAK,EAAExB,QAAS;cAChByF,QAAQ,EAAGtD,CAAC,IAAK;gBACf,MAAMX,KAAK,GAAGW,CAAC,CAACuD,MAAM,CAAClE,KAAK,CAACmE,WAAW,CAAC,CAAC;gBAC1C1F,WAAW,CAACuB,KAAK,CAAC;gBAClB,IAAIH,MAAM,CAACrB,QAAQ,EAAE;kBACnBsB,SAAS,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5F,QAAQ,EAAE;kBAAG,CAAC,CAAC,CAAC;gBAChD;cACF,CAAE;cACF6F,WAAW,EAAC,kBAAQ;cACpBvC,KAAK,EAAE,CAAC,CAACjC,MAAM,CAACrB,QAAS;cACzB8F,UAAU,EAAEzE,MAAM,CAACrB,QAAQ,IAAI,WAAY;cAC3C+F,UAAU,EAAE;gBAAEC,SAAS,EAAE;cAAE,CAAE;cAC7BnC,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1BoC,YAAY,EAAE;gBAChB;cACF;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPlF,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvBrE,OAAA,CAACnB,SAAS;cACRmH,QAAQ;cACRC,SAAS;cACTZ,KAAK,EAAC,oBAAK;cACXpD,KAAK,EAAEjB,MAAO;cACdkF,QAAQ,EAAGtD,CAAC,IAAK;gBACf,MAAMX,KAAK,GAAGW,CAAC,CAACuD,MAAM,CAAClE,KAAK,CAACmE,WAAW,CAAC,CAAC;gBAC1CnF,SAAS,CAACgB,KAAK,CAAC;gBAChB,IAAIH,MAAM,CAACd,MAAM,EAAE;kBACjBe,SAAS,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErF,MAAM,EAAE;kBAAG,CAAC,CAAC,CAAC;gBAC9C;cACF,CAAE;cACFsF,WAAW,EAAC,kBAAQ;cACpBvC,KAAK,EAAE,CAAC,CAACjC,MAAM,CAACd,MAAO;cACvBuF,UAAU,EAAEzE,MAAM,CAACd,MAAM,IAAI,WAAY;cACzCwF,UAAU,EAAE;gBAAEC,SAAS,EAAE;cAAE,CAAE;cAC7BnC,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1BoC,YAAY,EAAE;gBAChB;cACF;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlF,OAAA,CAACd,IAAI;MAACoF,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAChBrE,OAAA,CAACb,WAAW;QAAAkF,QAAA,gBACVrE,OAAA,CAACrB,GAAG;UAAC2F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBACxDrE,OAAA,CAACL,kBAAkB;YAAC2E,EAAE,EAAE;cAAEQ,KAAK,EAAE,gBAAgB;cAAEW,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DlF,OAAA,CAACpB,UAAU;YAACuG,OAAO,EAAC,IAAI;YAACb,EAAE,EAAE;cAAEc,UAAU,EAAE;YAAI,CAAE;YAAAf,QAAA,EAAC;UAElD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblF,OAAA,CAACZ,IAAI;YAACiG,KAAK,EAAC,cAAI;YAACC,IAAI,EAAC,OAAO;YAACR,KAAK,EAAC,SAAS;YAACR,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENlF,OAAA,CAACjB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAvB,QAAA,gBACzBrE,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAtC,QAAA,eAC9BrE,OAAA,CAACnB,SAAS;cACRoH,SAAS;cACTZ,KAAK,EAAC,0BAAM;cACZuB,IAAI,EAAC,QAAQ;cACb3E,KAAK,EAAEf,WAAY;cACnBgF,QAAQ,EAAGtD,CAAC,IAAK;gBACfzB,cAAc,CAACyB,CAAC,CAACuD,MAAM,CAAClE,KAAK,CAAC;gBAC9B,IAAIH,MAAM,CAACZ,WAAW,EAAE;kBACtBa,SAAS,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnF,WAAW,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACnD;cACF,CAAE;cACFoF,WAAW,EAAC,kBAAQ;cACpBvC,KAAK,EAAE,CAAC,CAACjC,MAAM,CAACZ,WAAY;cAC5BqF,UAAU,EAAEzE,MAAM,CAACZ,WAAW,IAAI,YAAa;cAC/C2F,UAAU,EAAE;gBACVC,cAAc,eAAE9G,OAAA,CAAChB,cAAc;kBAAC+H,QAAQ,EAAC,OAAO;kBAAA1C,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB;cACrE,CAAE;cACFZ,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1BoC,YAAY,EAAE;gBAChB;cACF;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPlF,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAtC,QAAA,eAC9BrE,OAAA,CAACnB,SAAS;cACRoH,SAAS;cACTZ,KAAK,EAAC,iCAAa;cACnBuB,IAAI,EAAC,QAAQ;cACb3E,KAAK,EAAEb,cAAe;cACtB8E,QAAQ,EAAGtD,CAAC,IAAK;gBACfvB,iBAAiB,CAACuB,CAAC,CAACuD,MAAM,CAAClE,KAAK,CAAC;gBACjC,IAAIH,MAAM,CAACV,cAAc,EAAE;kBACzBW,SAAS,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjF,cAAc,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACtD;cACF,CAAE;cACFkF,WAAW,EAAC,kBAAQ;cACpBvC,KAAK,EAAE,CAAC,CAACjC,MAAM,CAACV,cAAe;cAC/BmF,UAAU,EAAEzE,MAAM,CAACV,cAAc,IAAI,mBAAoB;cACzDyF,UAAU,EAAE;gBACVC,cAAc,eAAE9G,OAAA,CAAChB,cAAc;kBAAC+H,QAAQ,EAAC,OAAO;kBAAA1C,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB;cACrE,CAAE;cACFZ,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1BoC,YAAY,EAAE;gBAChB;cACF;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPlF,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAtC,QAAA,eAC9BrE,OAAA,CAACV,OAAO;cAAC0H,KAAK,EAAC,0GAA0B;cAAA3C,QAAA,eACvCrE,OAAA,CAACnB,SAAS;gBACRoH,SAAS;gBACTZ,KAAK,EAAC,oBAAK;gBACXuB,IAAI,EAAC,QAAQ;gBACb3E,KAAK,EAAEP,cAAe;gBACtBwE,QAAQ,EAAGtD,CAAC,IAAK;kBACfjB,iBAAiB,CAACiB,CAAC,CAACuD,MAAM,CAAClE,KAAK,CAAC;kBACjC,IAAIH,MAAM,CAACJ,cAAc,EAAE;oBACzBK,SAAS,CAACsE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE3E,cAAc,EAAE;oBAAG,CAAC,CAAC,CAAC;kBACtD;gBACF,CAAE;gBACF4E,WAAW,EAAC,mBAAS;gBACrBvC,KAAK,EAAE,CAAC,CAACjC,MAAM,CAACJ,cAAe;gBAC/B6E,UAAU,EAAEzE,MAAM,CAACJ,cAAc,IAAI,eAAgB;gBACrD8E,UAAU,EAAE;kBAAES,IAAI,EAAE,MAAM;kBAAEC,GAAG,EAAE,GAAG;kBAAEC,GAAG,EAAE;gBAAI,CAAE;gBACjD7C,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BoC,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlF,OAAA,CAACrB,GAAG;MAAC2F,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEE,cAAc,EAAE,eAAe;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACpErE,OAAA,CAAClB,MAAM;QACLqG,OAAO,EAAC,UAAU;QAClBiC,SAAS,eAAEpH,OAAA,CAACT,aAAa;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BmC,OAAO,EAAE/G,MAAO;QAChBgH,QAAQ,EAAE1F,OAAQ;QAAAyC,QAAA,EACnB;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETlF,OAAA,CAAClB,MAAM;QACL8H,IAAI,EAAC,QAAQ;QACbzB,OAAO,EAAC,WAAW;QACnBoC,OAAO,EAAE3F,OAAO,gBAAG5B,OAAA,CAACf,gBAAgB;UAACqG,IAAI,EAAE,EAAG;UAACR,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlF,OAAA,CAACR,QAAQ;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjFoC,QAAQ,EAAE1F,OAAQ;QAAAyC,QAAA,EAEjBzC,OAAO,GAAG,QAAQ,GAAG;MAAM;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1E,EAAA,CArVIN,WAAW;AAAAsH,EAAA,GAAXtH,WAAW;AAuVjB,eAAeA,WAAW;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}