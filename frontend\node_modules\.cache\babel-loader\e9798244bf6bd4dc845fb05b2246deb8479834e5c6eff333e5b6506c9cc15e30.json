{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"components\", \"componentsProps\"];\nimport * as React from 'react';\n/** Gathers props for the root element into a single `.forwardedProps` field */\nfunction groupForwardedProps(props) {\n  var _props$forwardedProps;\n  const keys = Object.keys(props);\n  if (!keys.some(key => key.startsWith('aria-') || key.startsWith('data-'))) {\n    return props;\n  }\n  const newProps = {};\n  const forwardedProps = (_props$forwardedProps = props.forwardedProps) != null ? _props$forwardedProps : {};\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    if (key.startsWith('aria-') || key.startsWith('data-')) {\n      forwardedProps[key] = props[key];\n    } else {\n      newProps[key] = props[key];\n    }\n  }\n  newProps.forwardedProps = forwardedProps;\n  return newProps;\n}\nexport function useProps(allProps) {\n  return React.useMemo(() => {\n    const {\n        components,\n        componentsProps\n      } = allProps,\n      themedProps = _objectWithoutPropertiesLoose(allProps, _excluded);\n    return [components, componentsProps, groupForwardedProps(themedProps)];\n  }, [allProps]);\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "React", "groupForwardedProps", "props", "_props$forwardedProps", "keys", "Object", "some", "key", "startsWith", "newProps", "forwardedProps", "i", "length", "useProps", "allProps", "useMemo", "components", "componentsProps", "themedProps"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/internals/utils/useProps.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"components\", \"componentsProps\"];\nimport * as React from 'react';\n/** Gathers props for the root element into a single `.forwardedProps` field */\nfunction groupForwardedProps(props) {\n  var _props$forwardedProps;\n  const keys = Object.keys(props);\n  if (!keys.some(key => key.startsWith('aria-') || key.startsWith('data-'))) {\n    return props;\n  }\n  const newProps = {};\n  const forwardedProps = (_props$forwardedProps = props.forwardedProps) != null ? _props$forwardedProps : {};\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    if (key.startsWith('aria-') || key.startsWith('data-')) {\n      forwardedProps[key] = props[key];\n    } else {\n      newProps[key] = props[key];\n    }\n  }\n  newProps.forwardedProps = forwardedProps;\n  return newProps;\n}\nexport function useProps(allProps) {\n  return React.useMemo(() => {\n    const {\n        components,\n        componentsProps\n      } = allProps,\n      themedProps = _objectWithoutPropertiesLoose(allProps, _excluded);\n    return [components, componentsProps, groupForwardedProps(themedProps)];\n  }, [allProps]);\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,iBAAiB,CAAC;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,IAAIC,qBAAqB;EACzB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,KAAK,CAAC;EAC/B,IAAI,CAACE,IAAI,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;IACzE,OAAON,KAAK;EACd;EACA,MAAMO,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,cAAc,GAAG,CAACP,qBAAqB,GAAGD,KAAK,CAACQ,cAAc,KAAK,IAAI,GAAGP,qBAAqB,GAAG,CAAC,CAAC;EAC1G,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,MAAMJ,GAAG,GAAGH,IAAI,CAACO,CAAC,CAAC;IACnB,IAAIJ,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MACtDE,cAAc,CAACH,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;IAClC,CAAC,MAAM;MACLE,QAAQ,CAACF,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;IAC5B;EACF;EACAE,QAAQ,CAACC,cAAc,GAAGA,cAAc;EACxC,OAAOD,QAAQ;AACjB;AACA,OAAO,SAASI,QAAQA,CAACC,QAAQ,EAAE;EACjC,OAAOd,KAAK,CAACe,OAAO,CAAC,MAAM;IACzB,MAAM;QACFC,UAAU;QACVC;MACF,CAAC,GAAGH,QAAQ;MACZI,WAAW,GAAGpB,6BAA6B,CAACgB,QAAQ,EAAEf,SAAS,CAAC;IAClE,OAAO,CAACiB,UAAU,EAAEC,eAAe,EAAEhB,mBAAmB,CAACiB,WAAW,CAAC,CAAC;EACxE,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}