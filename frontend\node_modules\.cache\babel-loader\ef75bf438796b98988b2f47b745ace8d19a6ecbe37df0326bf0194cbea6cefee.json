{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '点击选择备注',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleClick,\n      variant: uiState.isSelected ? 'contained' : 'outlined',\n      color: \"primary\",\n      size: \"small\",\n      sx: {\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      },\n      children: uiState.text || '点击选择'\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-center',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-center',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-center',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-center',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 1000),\n  // 1秒防抖\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 800),\n  // 0.8秒防抖\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"error\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              fontSize: '0.75rem',\n              textTransform: 'none',\n              minWidth: '70px'\n            },\n            children: \"\\u79FB\\u9664\"\n          }, \"remove\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 859,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 855,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1001,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 957,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1018,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1095,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1184,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 871,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"iaD8OwQSRZwC9dRbAurRuny0Pb4=\");\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "axios", "API_URL", "FixedSizeList", "toast", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "searchColumn", "setSearchColumn", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showToast", "message", "type", "success", "duration", "position", "style", "background", "borderRadius", "fontWeight", "padding", "boxShadow", "error", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "prev", "totalValue", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "showSnackbar", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "label", "opacity", "remarkText", "startIcon", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "toString", "cellValue", "memoGridData", "textAlign", "py", "mt", "mb", "alignItems", "justifyContent", "flexWrap", "gap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "endAdornment", "edge", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "backgroundColor", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : '点击选择备注'} arrow placement=\"top\">\n      <Button\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        onClick={handleClick}\n        variant={uiState.isSelected ? 'contained' : 'outlined'}\n        color=\"primary\"\n        size=\"small\"\n        sx={{\n          minWidth: '150px',\n          maxWidth: '300px',\n          fontSize: '0.75rem',\n          textTransform: 'none',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          transition: 'all 0.2s ease-in-out',\n          height: 'auto',\n          lineHeight: 1.2\n        }}\n      >\n        {uiState.text || '点击选择'}\n      </Button>\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-center',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-center',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-center',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-center',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 1000), // 1秒防抖\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 800), // 0.8秒防抖\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Button\n                key=\"undo\"\n                variant=\"contained\"\n                color=\"success\"\n                size=\"small\"\n                startIcon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{\n                  fontSize: '0.75rem',\n                  textTransform: 'none',\n                  minWidth: '70px'\n                }}\n              >\n                恢复\n              </Button>\n            );\n          }\n          return (\n            <Button\n              key=\"remove\"\n              variant=\"contained\"\n              color=\"error\"\n              size=\"small\"\n              startIcon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              }}\n            >\n              移除\n            </Button>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!searchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = searchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, searchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              rows={memoGridData}\n              columns={columns}\n              pageSize={100}\n              rowsPerPageOptions={[100, 200, 500]}\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAACzB,KAAK;IAAA,GAAKqC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAGzE,KAAK,CAAC0E,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC;IAErC4E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA5E,SAAS,CAAC,MAAM;IACd+E,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC7B,OAAO;IAAC4D,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,QAAS;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eACjFlC,OAAA,CAAC7C,MAAM;MAELuE,OAAO,EAAEG,WAAY;MACrBM,OAAO,EAAER,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;MACvDW,KAAK,EAAC,SAAS;MACfC,IAAI,EAAC,OAAO;MACZC,EAAE,EAAE;QACFC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,SAAS;QACnBC,aAAa,EAAE,MAAM;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,sBAAsB;QAClCC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,EAEDP,OAAO,CAACH,IAAI,IAAI;IAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC,kCAAC;AAACgC,GAAA,GA9CG9B,UAAU;AAgDhB,MAAM+B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpH,QAAQ,CAAC,MAAM;IACzD,MAAMqH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGvD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC4H,eAAe,EAAEC,kBAAkB,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACgI,UAAU,EAAEC,aAAa,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkI,YAAY,EAAEC,eAAe,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACoI,aAAa,EAAEC,gBAAgB,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsI,UAAU,EAAEC,aAAa,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwI,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdqH,YAAY,CAACoB,OAAO,CAAC,gBAAgB,EAAElB,IAAI,CAACmB,SAAS,CAACxB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMyB,aAAa,GAAG,CAACrC,IAAI,IAAI,EAAE,EAAEsC,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnJ,QAAQ,CAAC,MAAM;IAC7CoJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C1C,aAAa,GAAG,IAAIA,aAAa,CAAC2C,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI3C,aAAa,IAAIA,aAAa,CAAC2C,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG5C,aAAa,CAACkC,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG3J,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM4J,iBAAiB,GAAG5J,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM6J,gBAAgB,GAAG7J,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM8J,mBAAmB,GAAG9J,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM+J,UAAU,GAAI3D,IAAI,IAAKA,IAAI,CAACsC,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAlK,SAAS,CAAC,MAAM;IACd,IAAI2G,YAAY,IAAIsC,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAG9C,IAAI,CAACmB,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5B5G,YAAY,CAACoG,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAG3G,UAAU,CAAC,MAAM;UAC1CiG,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCxD,YAAY,CAAC,CAAC,GAAGsC,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5B5G,YAAY,CAACoG,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAEtC,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG1K,QAAQ,CAAC;IACjD2K,IAAI,EAAE,KAAK;IACXhG,KAAK,EAAE,IAAI;IACXiG,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA3K,SAAS,CAAC,MAAM;IACd,IAAIuI,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMqC,SAAS,GAAG3K,WAAW,CAAC,CAAC4K,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC3D,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ7H,KAAK,CAAC8H,OAAO,CAACF,OAAO,EAAE;UACrBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,eAAe;UACzBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,OAAO;QACVtI,KAAK,CAACuI,KAAK,CAACX,OAAO,EAAE;UACnBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,eAAe;UACzBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,MAAM;QACTtI,KAAK,CAAC4H,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,eAAe;UACzBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZtI,KAAK,CAAC4H,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,eAAe;UACzBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrB5F,KAAK,EAAE,OAAO;YACd6F,YAAY,EAAE,MAAM;YACpBxF,QAAQ,EAAE,MAAM;YAChByF,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF;QACEtI,KAAK,CAAC4H,OAAO,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAIlF,MAAM,IAAIA,MAAM,CAACmF,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7CjF,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAMkF,WAAW,GAAG,GAAG5I,OAAO,aAAawD,MAAM,EAAE;MACnD,MAAMqF,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAI5B,IAAI,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhB,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC;IACxC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/E,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM6F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMxJ,KAAK,CAACyJ,MAAM,CAAC,GAAGxJ,OAAO,YAAYwD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOiF,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAhF,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMgG,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8C,kBAAkB,GAAGzM,WAAW,CAAC,CAACqG,IAAI,EAAEqG,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGtG,IAAI,IAAI2C,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC4D,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAK;MACpB,IAAI8D,UAAU,IAAI9D,GAAG,CAACa,EAAE,KAAKiD,UAAU,CAACjD,EAAE,EAAE;QAC1C,OAAOuD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACzC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO+C,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkE,gBAAgB,GAAGlN,WAAW,CAAEqG,IAAI,IAAK;IAC7C,MAAM8G,QAAQ,GAAG9G,IAAI,CAAC+G,IAAI,CAACxE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIwD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGhH,IAAI,CAClByG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DkD,QAAQ,CAAClD,UAAU,GAAGoD,QAAQ;IAChC;IACA,OAAOhH,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiH,2BAA2B,GAAGtN,WAAW,CAC7CmD,QAAQ,CAAEkD,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAACoB,OAAO,CAAC,eAAe,EAAElB,IAAI,CAACmB,SAAS,CAACpC,IAAI,CAAC,CAAC;MAC3D6C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9C,IAAI,CAAC+C,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGvN,WAAW,CACvCmD,QAAQ,CAAEkD,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvB6C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACzC,YAAY,CACf,CAAC;;EAED;EACA,MAAM8G,gBAAgB,GAAGxN,WAAW,CAAEyN,MAAM,IAAK;IAC/CxE,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGlB,kBAAkB,CAACiB,IAAI,EAAED,MAAM,CAAC;MACjD,MAAMG,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACa,EAAE,KAAKgE,MAAM,CAAChE,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAG6E;QAAO,CAAC;QACtD,IAAI7E,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAE0D;QAAW,CAAC;QACjE,OAAO/E,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA0E,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOH,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMM,uBAAuB,GAAItC,KAAK,IAAK;IACzCZ,SAAS,CAAC,SAASY,KAAK,CAACX,OAAO,EAAE,EAAE,OAAO,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGjO,KAAK,CAACG,WAAW,CAAC,CAACyE,KAAK,EAAEiG,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVhG,KAAK;MACLiG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqD,kBAAkB,GAAG/N,WAAW,CAAC,MAAM;IAC3CwK,gBAAgB,CAACkD,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPjD,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAtC,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2F,kBAAkB,GAAGhO,WAAW,CAAEiO,MAAM,IAAK;IACjD,MAAM;MAAExJ;IAAM,CAAC,GAAG8F,aAAa;IAC/B,IAAI9F,KAAK,KAAK,IAAI,EAAE;MAClB;MACAsJ,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjClF,WAAW,CAACmF,QAAQ,IAAI;UACtB,IAAIR,WAAW,GAAGQ,QAAQ,CAACzF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKhF,KAAK,EAAE;cACpB,IAAIwJ,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGrF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIuF,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAIpG,aAAa,EAAE;kBACjBoG,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAInG,UAAU,EAAE;kBACdkG,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAClF,MAAM,GAAG,CAAC,EAAE;kBACvBiF,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG5F,GAAG;kBAAEC,OAAO,EAAEwF,WAAW;kBAAEvF,iBAAiB,EAAEuF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOzF,GAAG;UACZ,CAAC,CAAC;UAEFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAjK,UAAU,CAAC,MAAM;UACfgH,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC;QACpC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEwD,kBAAkB,EAAEb,gBAAgB,EAAEuB,YAAY,EAAEvG,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAElG;EACA,MAAMsG,mBAAmB,GAAG1O,WAAW,CAAC,MAAM;IAC5C2H,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgH,oBAAoB,GAAG3O,WAAW,CAAC,MAAM;IAC7C2H,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmH,YAAY,GAAG5O,WAAW,CAAC,MAAM;IACrC,IAAIwH,SAAS,CAACqH,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC5H,cAAc,CAAC6H,QAAQ,CAACtH,SAAS,CAACqH,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE3H,iBAAiB,CAACwG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAElG,SAAS,CAACqH,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDlE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9BgE,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI1H,cAAc,CAAC6H,QAAQ,CAACtH,SAAS,CAACqH,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDlE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACnD,SAAS,EAAEP,cAAc,EAAE0H,oBAAoB,EAAEhE,SAAS,CAAC,CAAC;;EAEhE;EACA,MAAMoE,YAAY,GAAG/O,WAAW,CAAEiO,MAAM,IAAK;IAC3C/G,iBAAiB,CAACwG,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAACkC,IAAI,IAAIA,IAAI,KAAKf,MAAM,CAAC,CAAC;IAC/DtD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMsE,eAAe,GAAGjP,WAAW,CAAEyJ,EAAE,IAAK;IAC1CR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,MAAMsB,cAAc,GAAGtB,WAAW,CAACd,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHuF,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3F,EAAE,GAAG4F,CAAC,CAAC5F,EAAE,CAAC;MAC1CyF,cAAc,CAACI,OAAO,CAAC,CAAC1G,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOoE,WAAW;IACpB,CAAC,CAAC;IAEFjD,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;EAChC,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAM4E,aAAa,GAAGvP,WAAW,CAAEyJ,EAAE,IAAK;IACxCR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,MAAMsB,cAAc,GAAGtB,WAAW,CAACd,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHuF,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3F,EAAE,GAAG4F,CAAC,CAAC5F,EAAE,CAAC;MAC1CyF,cAAc,CAACI,OAAO,CAAC,CAAC1G,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOoE,WAAW;IACpB,CAAC,CAAC;IACFjK,UAAU,CAAC,MAAM;MACfgH,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAM6E,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF3H,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM4H,YAAY,GAAG,CAACzG,QAAQ,IAAI,EAAE,EACjC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACA0G,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACzF,EAAE,KAAK,QAAQ,IAAI,OAAO0F,CAAC,CAAC1F,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOyF,CAAC,CAACzF,EAAE,GAAG0F,CAAC,CAAC1F,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM+F,OAAO,GAAGD,YAAY,CAAC9G,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbmG,IAAI,EAAE/G,GAAG,CAAC+G,IAAI,GAAI,OAAO/G,GAAG,CAAC+G,IAAI,KAAK,QAAQ,IAAI/G,GAAG,CAAC+G,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGlG,GAAG,CAAC+G,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGhH,GAAG,CAAC+G,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE/G,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGiH,IAAI,CAACC,KAAK,CAAClH,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFmH,EAAE,EAAE,OAAOnH,GAAG,CAACmH,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAClH,GAAG,CAACmH,EAAE,CAAC,GAAGnH,GAAG,CAACmH,EAAE,IAAI,EAAE;QAClElH,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGkH,KAAK,EAAE,OAAOpH,GAAG,CAACqH,QAAQ,KAAK,QAAQ,GACpCrH,GAAG,CAACqH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGrH,GAAG,CAACqH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGtH,GAAG,CAACqH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EtH,GAAG,CAACqH,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOvH,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAACiG,OAAO,CAAC,CAAC,CAAC,GAAGtH,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMmG,WAAW,GAAG,CAACpH,QAAQ,IAAI,EAAE,EAChC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE8C,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAI,OAAOpE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMoG,YAAY,GAAI/J,MAAM,IAAIA,MAAM,CAACmF,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAGnF,MAAM;MAE5F,MAAMgK,QAAQ,GAAG,MAAMzN,KAAK,CAAC0N,IAAI,CAAC,GAAGzN,OAAO,oBAAoB,EAAE;QAChEuD,IAAI,EAAEqJ,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC5J,MAAM,EAAE+J;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACjK,IAAI,IAAIiK,QAAQ,CAACjK,IAAI,CAACmK,KAAK,EAAE;QACxC;QACA,MAAM9E,WAAW,GAAG,GAAG5I,OAAO,CAAC8M,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGU,QAAQ,CAACjK,IAAI,CAACoK,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAhC,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACA9K,UAAU,CAAC,MAAM;UACf,MAAM+M,MAAM,GAAG9E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C6E,MAAM,CAACzF,KAAK,CAAC0F,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAGlF,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACwE,MAAM,CAAC;UACjC/M,UAAU,CAAC,MAAM;YACfiI,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACsE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BkD,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACR5G,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMiJ,kBAAkB,GAAG9Q,WAAW,CAAC,CAACyE,KAAK,EAAEsM,KAAK,KAAK;IACvDjD,iBAAiB,CAACrJ,KAAK,EAAEsM,KAAK,CAAC;EACjC,CAAC,EAAE,CAACjD,iBAAiB,CAAC,CAAC;EAEvB,MAAMkD,OAAO,GAAG9Q,OAAO,CAAC,MAAO0G,WAAW,CAAC+B,GAAG,CAACsI,GAAG,IAAI;IACpD,IAAI,EAAEjI,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACkI,cAAc,CAACD,GAAG,CAACpK,KAAK,CAAC,CAAC,IAAIoK,GAAG,CAACpK,KAAK,KAAK,SAAS,IAAIoK,GAAG,CAACpK,KAAK,KAAK,QAAQ,IAAIoK,GAAG,CAACpK,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIoK,GAAG,CAACpK,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEoK,GAAG,CAACpK,KAAK;QAChBC,UAAU,EAAEmK,GAAG,CAACnK,UAAU;QAC1BqK,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVrK,QAAQ,EAAE,KAAK;QACfsK,UAAU,EAAG7E,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI6C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMuI,iBAAiB,GAAG9E,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE5F,OAAA,CAAC7B,OAAO;cAAC4D,KAAK,EAAEuH,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAAC5D,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvElC,OAAA,CAAC9B,IAAI;gBACHmQ,KAAK,EAAED,iBAAkB;gBACzBhM,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAE8L,OAAO,EAAE,GAAG;kBAAExL,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAE4K,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA3M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIsN,UAAU,GAAG,MAAM;UACvB,IAAI9M,UAAU,GAAG,KAAK;UAEtB,IAAI6H,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI0D,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3E2I,UAAU,GAAGjF,MAAM,CAAC5D,GAAG,CAACE,iBAAiB;YACzCnE,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAE+H,MAAM,CAAC5D,GAAG,CAACa,EAAG;YACrB/E,IAAI,EAAE+M,UAAW;YACjB9M,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEkM;UAAmB;YAAA9M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI8M,GAAG,CAACpK,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEoK,GAAG,CAACpK,KAAK;QAChBC,UAAU,EAAEmK,GAAG,CAACnK,UAAU;QAC1BqK,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVrK,QAAQ,EAAE,KAAK;QACfsK,UAAU,EAAG7E,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI6C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACE7F,OAAA,CAAC7C,MAAM;cAELgF,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZmM,SAAS,eAAExO,OAAA,CAACZ,QAAQ;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAM2K,aAAa,CAAC/C,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;cAC5CjE,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAL,QAAA,EACH;YAED,GAbM,MAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC;UAEb;UACA,oBACEjB,OAAA,CAAC7C,MAAM;YAELgF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,OAAO;YACbC,IAAI,EAAC,OAAO;YACZmM,SAAS,eAAExO,OAAA,CAACb,UAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BS,OAAO,EAAEA,CAAA,KAAMqK,eAAe,CAACzC,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;YAC9CjE,EAAE,EAAE;cACFG,QAAQ,EAAE,SAAS;cACnBC,aAAa,EAAE,MAAM;cACrBH,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,EACH;UAED,GAbM,QAAQ;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaN,CAAC;QAEb;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG8M,GAAG;MACNlK,QAAQ,EAAEyF,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI6C,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOkI,GAAG,CAAClK,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDsK,UAAU,EAAG7E,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIsH,GAAG,CAACpK,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE3D,OAAA,CAAC9C,UAAU;YAACiF,OAAO,EAAC,OAAO;YAAC+F,UAAU,EAAC,MAAM;YAAC9F,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOoH,MAAM,CAACuE,KAAK,KAAK,QAAQ,GAAGvE,MAAM,CAACuE,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO1D,MAAM,CAACuE,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAC1E,MAAM,CAACT,MAAM,CAACuE,KAAK,CAAC,CAAC,GAAG9D,MAAM,CAACT,MAAM,CAACuE,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG1D,MAAM,CAACuE;UAAK;YAAA/M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIqI,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE7F,OAAA,CAAC9C,UAAU;YAACiF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAEoM,cAAc,EAAE;YAAe,CAAE;YAAAxM,QAAA,EACtFoH,MAAM,CAACuE;UAAK;YAAA/M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI8M,GAAG,CAACpK,KAAK,KAAK,MAAM,IAAI2F,MAAM,CAACuE,KAAK,EAAE;UACxC,OAAOvE,MAAM,CAACuE,KAAK,CAACnB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIqB,GAAG,CAACpK,KAAK,KAAK,IAAI,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAACtD,MAAM,CAACuE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACpK,KAAK,KAAK,OAAO,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOlB,IAAI,CAACC,KAAK,CAACtD,MAAM,CAACuE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACpK,KAAK,KAAK,IAAI,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAACtD,MAAM,CAACuE,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACpK,KAAK,KAAK,UAAU,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOvE,MAAM,CAACuE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGvE,MAAM,CAACuE,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG1D,MAAM,CAACuE,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAACpK,KAAK,KAAK,YAAY,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOvE,MAAM,CAACuE,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAACpK,KAAK,KAAK,YAAY,IAAI,OAAO2F,MAAM,CAACuE,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAC1E,MAAM,CAACT,MAAM,CAACuE,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO9D,MAAM,CAACT,MAAM,CAACuE,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAO1D,MAAM,CAACuE,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOvE,MAAM,CAACuE,KAAK;QACrB;QACA,OAAOvE,MAAM,CAACuE,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACjE,MAAM,CAAC+E,OAAO,CAAE,EAAE,CAACjL,WAAW,EAAEoC,QAAQ,EAAE8H,kBAAkB,EAAE7B,eAAe,EAAEM,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAMuC,gBAAgB,GAAG5R,OAAO,CAAC,MAAM;IACrC,IAAI,CAAC4H,UAAU,CAAC+G,IAAI,CAAC,CAAC,EAAE;MACtB,OAAO7F,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAM+I,WAAW,GAAGjK,UAAU,CAACkK,WAAW,CAAC,CAAC;IAC5C,OAAO,CAAChJ,QAAQ,IAAI,EAAE,EAAE8D,MAAM,CAAClE,GAAG,IAAI;MACpC,IAAIZ,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOiK,MAAM,CAACC,MAAM,CAACtJ,GAAG,CAAC,CAACuJ,IAAI,CAACpB,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAClD,QAAQ,CAACiD,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMM,SAAS,GAAGzJ,GAAG,CAACZ,YAAY,CAAC;QACnC,OAAOqK,SAAS,IAAIA,SAAS,CAACD,QAAQ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAClD,QAAQ,CAACiD,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/I,QAAQ,EAAElB,UAAU,EAAEE,YAAY,CAAC,CAAC;;EAExC;EACA,MAAMsK,YAAY,GAAGpS,OAAO,CAAC,MAAM4R,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,IAAI,CAAC9I,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACElG,OAAA,CAAC/C,GAAG;MAACqF,EAAE,EAAE;QAAE+M,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAApN,QAAA,gBACtClC,OAAA,CAAC9C,UAAU;QAACiF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC7C,MAAM;QACLgF,OAAO,EAAC,WAAW;QACnBT,OAAO,EAAE2B,OAAQ;QACjBf,EAAE,EAAE;UAAEiN,EAAE,EAAE;QAAE,CAAE;QAAArN,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAAC/C,GAAG;IAAAiF,QAAA,gBAEFlC,OAAA,CAAC5B,IAAI;MAACkE,EAAE,EAAE;QAAEkN,EAAE,EAAE;MAAE,CAAE;MAAAtN,QAAA,eAClBlC,OAAA,CAAC3B,WAAW;QAAA6D,QAAA,eACVlC,OAAA,CAAC/C,GAAG;UAACqF,EAAE,EAAE;YAAEmL,OAAO,EAAE,MAAM;YAAEgC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAA1N,QAAA,gBAC5GlC,OAAA,CAAC/C,GAAG;YAACqF,EAAE,EAAE;cAAEmL,OAAO,EAAE,MAAM;cAAEgC,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAE,CAAE;YAAA1N,QAAA,gBACzDlC,OAAA,CAACV,cAAc;cAACgD,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAAC/C,GAAG;cAAAiF,QAAA,gBACFlC,OAAA,CAAC9C,UAAU;gBAACiF,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAE4F,UAAU,EAAE,GAAG;kBAAE9F,KAAK,EAAE,cAAc;kBAAEoN,EAAE,EAAE;gBAAI,CAAE;gBAAAtN,QAAA,EAAC;cAElF;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAC9C,UAAU;gBAACiF,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAF,QAAA,EAAC;cAE7D;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAAC1B,KAAK;YAACuC,SAAS,EAAC,KAAK;YAACgP,OAAO,EAAE,CAAE;YAACvN,EAAE,EAAE;cAAEqN,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA1N,QAAA,gBAClElC,OAAA,CAAC9B,IAAI;cACH4R,IAAI,eAAE9P,OAAA,CAACT,aAAa;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBoN,KAAK,EAAE,GAAG,CAACe,YAAY,IAAI,EAAE,EAAExF,MAAM,CAAClE,GAAG,IAAI,CAACA,GAAG,CAACc,OAAO,IAAI,CAACd,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,MAAO;cACzF9D,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAC9B,IAAI;cACH4R,IAAI,eAAE9P,OAAA,CAACR,cAAc;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBoN,KAAK,EAAE,WAAW9E,kBAAkB,CAAC6F,YAAY,CAAC,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChE5K,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACmO,YAAY,IAAI,EAAE,EAAExF,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,iBAC1DlG,OAAA,CAAC9B,IAAI;cACH4R,IAAI,eAAE9P,OAAA,CAACb,UAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBoN,KAAK,EAAE,GAAG,CAACe,YAAY,IAAI,EAAE,EAAExF,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,OAAQ;cACzE9D,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC5B,IAAI;MAACkE,EAAE,EAAE;QAAEkN,EAAE,EAAE;MAAE,CAAE;MAAAtN,QAAA,eAClBlC,OAAA,CAAC3B,WAAW;QAAA6D,QAAA,gBACVlC,OAAA,CAAC9C,UAAU;UAACiF,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAE4F,UAAU,EAAE,GAAG;YAAEsH,EAAE,EAAE;UAAE,CAAE;UAAAtN,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC1B,KAAK;UAACuC,SAAS,EAAE;YAAEkP,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAA3N,QAAA,gBACxDlC,OAAA,CAAC7C,MAAM;YACLgF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfoM,SAAS,eAAExO,OAAA,CAAChB,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAE4G,cAAe;YAAApG,QAAA,EACzB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC7C,MAAM;YACLgF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfoM,SAAS,EAAE9J,oBAAoB,gBAAG1E,OAAA,CAAC/B,gBAAgB;cAACoE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACX,gBAAgB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAE4K,gBAAiB;YAC1B2D,QAAQ,EAAEvL,oBAAqB;YAAAxC,QAAA,EAE9BwC,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC7C,MAAM;YACLgF,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACboM,SAAS,eAAExO,OAAA,CAACf,cAAc;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEyH,aAAc;YAAAjH,QAAA,EACxB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC5B,IAAI;MAACkE,EAAE,EAAE;QAAEkN,EAAE,EAAE;MAAE,CAAE;MAAAtN,QAAA,eAClBlC,OAAA,CAAC3B,WAAW;QAAA6D,QAAA,gBACVlC,OAAA,CAAC9C,UAAU;UAACiF,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAE4F,UAAU,EAAE,GAAG;YAAEsH,EAAE,EAAE;UAAE,CAAE;UAAAtN,QAAA,EAAC;QAEhE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC1B,KAAK;UAACuC,SAAS,EAAE;YAAEkP,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACJ,UAAU,EAAC,QAAQ;UAAAvN,QAAA,gBAC5ElC,OAAA,CAACxB,WAAW;YAAC6D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAL,QAAA,gBAC9ClC,OAAA,CAACvB,UAAU;cAAAyD,QAAA,EAAC;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACtB,MAAM;cACLmP,KAAK,EAAE/I,YAAa;cACpBuJ,KAAK,EAAC,0BAAM;cACZ6B,QAAQ,EAAGpO,CAAC,IAAKiD,eAAe,CAACjD,CAAC,CAACqO,MAAM,CAACtC,KAAK,CAAE;cAAA3L,QAAA,gBAEjDlC,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,KAAK;gBAAA3L,QAAA,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,IAAI;gBAAA3L,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,MAAM;gBAAA3L,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,YAAY;gBAAA3L,QAAA,EAAC;cAAU;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,OAAO;gBAAA3L,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,IAAI;gBAAA3L,QAAA,EAAC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,SAAS;gBAAA3L,QAAA,EAAC;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,UAAU;gBAAA3L,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACrB,QAAQ;gBAACkP,KAAK,EAAC,YAAY;gBAAA3L,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAACjC,SAAS;YACRsE,IAAI,EAAC,OAAO;YACZ+N,WAAW,EAAC,yCAAW;YACvBvC,KAAK,EAAEjJ,UAAW;YAClBsL,QAAQ,EAAGpO,CAAC,IAAK+C,aAAa,CAAC/C,CAAC,CAACqO,MAAM,CAACtC,KAAK,CAAE;YAC/CvL,EAAE,EAAE;cAAE+N,QAAQ,EAAE,CAAC;cAAE9N,QAAQ,EAAE;YAAI,CAAE;YACnC+N,UAAU,EAAE;cACVC,cAAc,eACZvQ,OAAA,CAACpB,cAAc;gBAACkJ,QAAQ,EAAC,OAAO;gBAAA5F,QAAA,eAC9BlC,OAAA,CAACP,UAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACDuP,YAAY,EAAE5L,UAAU,iBACtB5E,OAAA,CAACpB,cAAc;gBAACkJ,QAAQ,EAAC,KAAK;gBAAA5F,QAAA,eAC5BlC,OAAA,CAAChC,UAAU;kBACTqE,IAAI,EAAC,OAAO;kBACZX,OAAO,EAAEA,CAAA,KAAMmD,aAAa,CAAC,EAAE,CAAE;kBACjC4L,IAAI,EAAC,KAAK;kBAAAvO,QAAA,eAEVlC,OAAA,CAACN,SAAS;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAED2D,UAAU,iBACT5E,OAAA,CAAC9C,UAAU;YAACiF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,eAC9C,EAAC0M,gBAAgB,CAAChF,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC5C,KAAK;MAACkF,EAAE,EAAE;QAAE4L,KAAK,EAAE,MAAM;QAAEvL,QAAQ,EAAE;MAAS,CAAE;MAAAT,QAAA,eAC7ClC,OAAA,CAAC/C,GAAG;QAACqF,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAEmL,KAAK,EAAE;QAAO,CAAE;QAAAhM,QAAA,eACzClC,OAAA,CAACjB,QAAQ;UACP2R,IAAI,EAAEtB,YAAa;UACnBtB,OAAO,EAAEA,OAAQ;UACjB6C,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAG1H,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFoL,cAAc,EAAG3H,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOyD,MAAM,CAAC4H,MAAM,CAACrN,QAAQ,IAAI,OAAOyF,MAAM,CAAC4H,MAAM,CAACrN,QAAQ,KAAK,UAAU,GAC3EyF,MAAM,CAAC4H,MAAM,CAACrN,QAAQ,CAACyF,MAAM,CAAC,GAAGA,MAAM,CAAC4H,MAAM,CAACrN,QAAQ;UAC3D,CAAE;UACFyG,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAACxD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOmE,MAAM,CAACxD,UAAU,KAAK,QAAQ,EAAE;gBACzCwD,MAAM,CAACxD,UAAU,GAAGgD,MAAM,CAACQ,MAAM,CAACxD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOuD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFI,uBAAuB,EAAEA,uBAAwB;UACjDrI,EAAE,EAAE;YACF,cAAc,EAAE;cACd6O,eAAe,EAAE,0BAA0B;cAC3CjJ,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBiJ,eAAe,EAAE,0BAA0B;cAC3C/O,KAAK,EAAE,eAAe;cACtBsM,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrB7L,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpBmF,OAAO,EAAE,KAAK;cACdiJ,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE,oBAAoB;cACrCC,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7BF,eAAe,EAAE,oBAAoB;cACrC/O,KAAK,EAAE,cAAc;cACrBkP,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClCpJ,UAAU,EAAE,MAAM;cAClB9F,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChCgL,OAAO,EAAE;YACX,CAAC;YACD8D,SAAS,EAAE;UACb;QAAE;UAAAzQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAACzC,MAAM;MACLgK,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBiK,OAAO,EAAE3G,kBAAmB;MAC5B4G,SAAS;MACTjP,QAAQ,EAAC,IAAI;MACbkP,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA3P,QAAA,gBAEnBlC,OAAA,CAACxC,WAAW;QAAA0E,QAAA,eACVlC,OAAA,CAAC/C,GAAG;UAACqF,EAAE,EAAE;YAAEmL,OAAO,EAAE,MAAM;YAAEiC,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAAvN,QAAA,gBAClFlC,OAAA,CAAC9C,UAAU;YAACiF,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC7C,MAAM;YACLqR,SAAS,eAAExO,OAAA,CAACd,OAAO;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAE8J,mBAAoB;YAC7BpJ,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjB,OAAA,CAAC/C,GAAG;QAACqF,EAAE,EAAE;UAAEwP,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA7P,QAAA,gBACxBlC,OAAA,CAAC9C,UAAU;UAACiF,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAEkN,EAAE,EAAE;UAAE,CAAE;UAAAtN,QAAA,EAAC;QAElE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC1B,KAAK;UAACuC,SAAS,EAAC,KAAK;UAACgP,OAAO,EAAE,CAAE;UAAA3N,QAAA,gBAChClC,OAAA,CAAClB,gBAAgB;YACfkT,OAAO,eACLhS,OAAA,CAACnB,QAAQ;cACPoT,OAAO,EAAEjN,aAAc;cACvBkL,QAAQ,EAAGpO,CAAC,IAAKmD,gBAAgB,CAACnD,CAAC,CAACqO,MAAM,CAAC8B,OAAO,CAAE;cACpD5P,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDoN,KAAK,EAAC;UAAS;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjB,OAAA,CAAClB,gBAAgB;YACfkT,OAAO,eACLhS,OAAA,CAACnB,QAAQ;cACPoT,OAAO,EAAE/M,UAAW;cACpBgL,QAAQ,EAAGpO,CAAC,IAAKqD,aAAa,CAACrD,CAAC,CAACqO,MAAM,CAAC8B,OAAO,CAAE;cACjD5P,IAAI,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDoN,KAAK,EAAC;UAAK;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjB,OAAA,CAACvC,aAAa;QAACyU,QAAQ;QAAC5P,EAAE,EAAE;UAAE6P,CAAC,EAAE;QAAE,CAAE;QAAAjQ,QAAA,eACnClC,OAAA,CAACH,aAAa;UACZkD,MAAM,EAAE,GAAI;UACZqP,SAAS,EAAErO,cAAc,CAACmC,MAAO;UACjCmM,QAAQ,EAAE,EAAG;UACbnE,KAAK,EAAC,MAAM;UAAAhM,QAAA,EAEXA,CAAC;YAAEoE,KAAK;YAAEyB;UAAM,CAAC,KAAK;YACrB,MAAMgD,MAAM,GAAGhH,cAAc,CAACuC,KAAK,CAAC;YACpC,oBACEtG,OAAA,CAACpC,QAAQ;cAEPmK,KAAK,EAAEA,KAAM;cACbuK,cAAc;cACdC,eAAe,EAAGxH,MAAM,KAAK,MAAM,iBACjC/K,OAAA,CAAChC,UAAU;gBACTyS,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB/O,OAAO,EAAEA,CAAA,KAAMmK,YAAY,CAACd,MAAM,CAAE;gBAAA7I,QAAA,eAEpClC,OAAA,CAACb,UAAU;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAiB,QAAA,eAEFlC,OAAA,CAACnC,cAAc;gBAAC6D,OAAO,EAAEA,CAAA,KAAMoJ,kBAAkB,CAACC,MAAM,CAAE;gBAAA7I,QAAA,eACxDlC,OAAA,CAAClC,YAAY;kBAAC0U,OAAO,EAAEzH;gBAAO;kBAAAjK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ8J,MAAM;cAAAjK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACtC,aAAa;QAAAwE,QAAA,eACZlC,OAAA,CAAC7C,MAAM;UAACuE,OAAO,EAAEmJ,kBAAmB;UAAA3I,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAACzC,MAAM;MACLgK,IAAI,EAAE/C,eAAgB;MACtBgN,OAAO,EAAE/F,oBAAqB;MAC9BgG,SAAS;MACTjP,QAAQ,EAAC,IAAI;MACbkP,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA3P,QAAA,gBAEnBlC,OAAA,CAACxC,WAAW;QAAA0E,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACvC,aAAa;QAAAyE,QAAA,eACZlC,OAAA,CAACjC,SAAS;UACR0U,SAAS;UACTC,MAAM,EAAC,OAAO;UACdnM,EAAE,EAAC,MAAM;UACT8H,KAAK,EAAC,0BAAM;UACZ1G,IAAI,EAAC,MAAM;UACX8J,SAAS;UACTtP,OAAO,EAAC,UAAU;UAClB0L,KAAK,EAAEvJ,SAAU;UACjB4L,QAAQ,EAAGpO,CAAC,IAAKyC,YAAY,CAACzC,CAAC,CAACqO,MAAM,CAACtC,KAAK;QAAE;UAAA/M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACtC,aAAa;QAAAwE,QAAA,gBACZlC,OAAA,CAAC7C,MAAM;UAACuE,OAAO,EAAE+J,oBAAqB;UAAAvJ,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC7C,MAAM;UAACuE,OAAO,EAAEgK,YAAa;UAACtJ,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACwC,GAAA,CAxjCIP,aAAa;AAAAyP,GAAA,GAAbzP,aAAa;AA0jCnB,eAAeA,aAAa;AAAC,IAAAhC,EAAA,EAAAI,GAAA,EAAA2B,GAAA,EAAA0P,GAAA;AAAAC,YAAA,CAAA1R,EAAA;AAAA0R,YAAA,CAAAtR,GAAA;AAAAsR,YAAA,CAAA3P,GAAA;AAAA2P,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}