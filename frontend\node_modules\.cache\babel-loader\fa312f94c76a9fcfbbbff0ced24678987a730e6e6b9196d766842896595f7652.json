{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridRowCountSelector, gridRowsLookupSelector, gridRowTreeSelector, gridRowGroupingNameSelector, gridRowTreeDepthsSelector, gridDataRowIdsSelector, gridRowsDataRowIdToIdLookupSelector, gridRowMaximumTreeDepthSelector } from './gridRowsSelector';\nimport { useTimeout } from '../../utils/useTimeout';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { gridSortedRowIdsSelector } from '../sorting/gridSortingSelector';\nimport { gridFilteredRowsLookupSelector } from '../filter/gridFilterSelector';\nimport { getTreeNodeDescendants, createRowsInternalCache, getRowsStateFromCache, isAutoGeneratedRow, GRID_ROOT_GROUP_ID, GRID_ID_AUTOGENERATED, updateCacheWithNewRows, getTopLevelRowCount, getRowIdFromRowModel } from './gridRowsUtils';\nimport { useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nexport const rowsStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.rows = createRowsInternalCache({\n    rows: props.rows,\n    getRowId: props.getRowId,\n    loading: props.loading,\n    rowCount: props.rowCount\n  });\n  return _extends({}, state, {\n    rows: getRowsStateFromCache({\n      apiRef,\n      rowCountProp: props.rowCount,\n      loadingProp: props.loading,\n      previousTree: null,\n      previousTreeDepths: null\n    })\n  });\n};\nexport const useGridRows = (apiRef, props) => {\n  if (process.env.NODE_ENV !== 'production') {\n    try {\n      // Freeze the `rows` prop so developers have a fast failure if they try to use Array.prototype.push().\n      Object.freeze(props.rows);\n    } catch (error) {\n      // Sometimes, it's impossible to freeze, so we give up on it.\n    }\n  }\n  const logger = useGridLogger(apiRef, 'useGridRows');\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const lastUpdateMs = React.useRef(Date.now());\n  const timeout = useTimeout();\n  const getRow = React.useCallback(id => {\n    const model = gridRowsLookupSelector(apiRef)[id];\n    if (model) {\n      return model;\n    }\n    const node = apiRef.current.getRowNode(id);\n    if (node && isAutoGeneratedRow(node)) {\n      return {\n        [GRID_ID_AUTOGENERATED]: id\n      };\n    }\n    return null;\n  }, [apiRef]);\n  const getRowIdProp = props.getRowId;\n  const getRowId = React.useCallback(row => {\n    if (GRID_ID_AUTOGENERATED in row) {\n      return row[GRID_ID_AUTOGENERATED];\n    }\n    if (getRowIdProp) {\n      return getRowIdProp(row);\n    }\n    return row.id;\n  }, [getRowIdProp]);\n  const lookup = React.useMemo(() => currentPage.rows.reduce((acc, {\n    id\n  }, index) => {\n    acc[id] = index;\n    return acc;\n  }, {}), [currentPage.rows]);\n  const throttledRowsChange = React.useCallback(({\n    cache,\n    throttle\n  }) => {\n    const run = () => {\n      lastUpdateMs.current = Date.now();\n      apiRef.current.setState(state => _extends({}, state, {\n        rows: getRowsStateFromCache({\n          apiRef,\n          rowCountProp: props.rowCount,\n          loadingProp: props.loading,\n          previousTree: gridRowTreeSelector(apiRef),\n          previousTreeDepths: gridRowTreeDepthsSelector(apiRef)\n        })\n      }));\n      apiRef.current.publishEvent('rowsSet');\n      apiRef.current.forceUpdate();\n    };\n    timeout.clear();\n    apiRef.current.caches.rows = cache;\n    if (!throttle) {\n      run();\n      return;\n    }\n    const throttleRemainingTimeMs = props.throttleRowsMs - (Date.now() - lastUpdateMs.current);\n    if (throttleRemainingTimeMs > 0) {\n      timeout.start(throttleRemainingTimeMs, run);\n      return;\n    }\n    run();\n  }, [props.throttleRowsMs, props.rowCount, props.loading, apiRef, timeout]);\n\n  /**\n   * API METHODS\n   */\n  const setRows = React.useCallback(rows => {\n    logger.debug(`Updating all rows, new length ${rows.length}`);\n    const cache = createRowsInternalCache({\n      rows,\n      getRowId: props.getRowId,\n      loading: props.loading,\n      rowCount: props.rowCount\n    });\n    const prevCache = apiRef.current.caches.rows;\n    cache.rowsBeforePartialUpdates = prevCache.rowsBeforePartialUpdates;\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [logger, props.getRowId, props.loading, props.rowCount, throttledRowsChange, apiRef]);\n  const updateRows = React.useCallback(updates => {\n    if (props.signature === GridSignature.DataGrid && updates.length > 1) {\n      throw new Error([\"MUI: You can't update several rows at once in `apiRef.current.updateRows` on the DataGrid.\", 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    const nonPinnedRowsUpdates = [];\n    updates.forEach(update => {\n      const id = getRowIdFromRowModel(update, props.getRowId, 'A row was provided without id when calling updateRows():');\n      const rowNode = apiRef.current.getRowNode(id);\n      if ((rowNode == null ? void 0 : rowNode.type) === 'pinnedRow') {\n        // @ts-ignore because otherwise `release:build` doesn't work\n        const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n        const prevModel = pinnedRowsCache.idLookup[id];\n        if (prevModel) {\n          pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n        }\n      } else {\n        nonPinnedRowsUpdates.push(update);\n      }\n    });\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows\n    });\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [props.signature, props.getRowId, throttledRowsChange, apiRef]);\n  const getRowModels = React.useCallback(() => {\n    const dataRows = gridDataRowIdsSelector(apiRef);\n    const idRowsLookup = gridRowsLookupSelector(apiRef);\n    return new Map(dataRows.map(id => {\n      var _idRowsLookup$id;\n      return [id, (_idRowsLookup$id = idRowsLookup[id]) != null ? _idRowsLookup$id : {}];\n    }));\n  }, [apiRef]);\n  const getRowsCount = React.useCallback(() => gridRowCountSelector(apiRef), [apiRef]);\n  const getAllRowIds = React.useCallback(() => gridDataRowIdsSelector(apiRef), [apiRef]);\n  const getRowIndexRelativeToVisibleRows = React.useCallback(id => lookup[id], [lookup]);\n  const setRowChildrenExpansion = React.useCallback((id, isExpanded) => {\n    const currentNode = apiRef.current.getRowNode(id);\n    if (!currentNode) {\n      throw new Error(`MUI: No row with id #${id} found`);\n    }\n    if (currentNode.type !== 'group') {\n      throw new Error('MUI: Only group nodes can be expanded or collapsed');\n    }\n    const newNode = _extends({}, currentNode, {\n      childrenExpanded: isExpanded\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [id]: newNode\n          })\n        })\n      });\n    });\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('rowExpansionChange', newNode);\n  }, [apiRef]);\n  const getRowNode = React.useCallback(id => {\n    var _ref;\n    return (_ref = gridRowTreeSelector(apiRef)[id]) != null ? _ref : null;\n  }, [apiRef]);\n  const getRowGroupChildren = React.useCallback(({\n    skipAutoGeneratedRows = true,\n    groupId,\n    applySorting,\n    applyFiltering\n  }) => {\n    const tree = gridRowTreeSelector(apiRef);\n    let children;\n    if (applySorting) {\n      const groupNode = tree[groupId];\n      if (!groupNode) {\n        return [];\n      }\n      const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n      children = [];\n      const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n      for (let index = startIndex; index < sortedRowIds.length && tree[sortedRowIds[index]].depth > groupNode.depth; index += 1) {\n        const id = sortedRowIds[index];\n        if (!skipAutoGeneratedRows || !isAutoGeneratedRow(tree[id])) {\n          children.push(id);\n        }\n      }\n    } else {\n      children = getTreeNodeDescendants(tree, groupId, skipAutoGeneratedRows);\n    }\n    if (applyFiltering) {\n      const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n      children = children.filter(childId => filteredRowsLookup[childId] !== false);\n    }\n    return children;\n  }, [apiRef]);\n  const setRowIndex = React.useCallback((rowId, targetIndex) => {\n    const node = apiRef.current.getRowNode(rowId);\n    if (!node) {\n      throw new Error(`MUI: No row with id #${rowId} found`);\n    }\n    if (node.parent !== GRID_ROOT_GROUP_ID) {\n      throw new Error(`MUI: The row reordering do not support reordering of grouped rows yet`);\n    }\n    if (node.type !== 'leaf') {\n      throw new Error(`MUI: The row reordering do not support reordering of footer or grouping rows`);\n    }\n    apiRef.current.setState(state => {\n      const group = gridRowTreeSelector(state, apiRef.current.instanceId)[GRID_ROOT_GROUP_ID];\n      const allRows = group.children;\n      const oldIndex = allRows.findIndex(row => row === rowId);\n      if (oldIndex === -1 || oldIndex === targetIndex) {\n        return state;\n      }\n      logger.debug(`Moving row ${rowId} to index ${targetIndex}`);\n      const updatedRows = [...allRows];\n      updatedRows.splice(targetIndex, 0, updatedRows.splice(oldIndex, 1)[0]);\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [GRID_ROOT_GROUP_ID]: _extends({}, group, {\n              children: updatedRows\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, logger]);\n  const replaceRows = React.useCallback((firstRowToRender, newRows) => {\n    if (props.signature === GridSignature.DataGrid && newRows.length > 1) {\n      throw new Error([\"MUI: You can't replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.\", 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (newRows.length === 0) {\n      return;\n    }\n    const treeDepth = gridRowMaximumTreeDepthSelector(apiRef);\n    if (treeDepth > 1) {\n      throw new Error('`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping');\n    }\n    const tree = _extends({}, gridRowTreeSelector(apiRef));\n    const dataRowIdToModelLookup = _extends({}, gridRowsLookupSelector(apiRef));\n    const dataRowIdToIdLookup = _extends({}, gridRowsDataRowIdToIdLookupSelector(apiRef));\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const seenIds = new Set();\n    for (let i = 0; i < newRows.length; i += 1) {\n      const rowModel = newRows[i];\n      const rowId = getRowIdFromRowModel(rowModel, props.getRowId, 'A row was provided without id when calling replaceRows().');\n      const [removedRowId] = rootGroupChildren.splice(firstRowToRender + i, 1, rowId);\n      if (!seenIds.has(removedRowId)) {\n        delete dataRowIdToModelLookup[removedRowId];\n        delete dataRowIdToIdLookup[removedRowId];\n        delete tree[removedRowId];\n      }\n      const rowTreeNodeConfig = {\n        id: rowId,\n        depth: 0,\n        parent: GRID_ROOT_GROUP_ID,\n        type: 'leaf',\n        groupingKey: null\n      };\n      dataRowIdToModelLookup[rowId] = rowModel;\n      dataRowIdToIdLookup[rowId] = rowId;\n      tree[rowId] = rowTreeNodeConfig;\n      seenIds.add(rowId);\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n\n    // Removes potential remaining skeleton rows from the dataRowIds.\n    const dataRowIds = rootGroupChildren.filter(childId => tree[childId].type === 'leaf');\n    apiRef.current.caches.rows.dataRowIdToModelLookup = dataRowIdToModelLookup;\n    apiRef.current.caches.rows.dataRowIdToIdLookup = dataRowIdToIdLookup;\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        dataRowIdToModelLookup,\n        dataRowIdToIdLookup,\n        dataRowIds,\n        tree\n      })\n    }));\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.signature, props.getRowId]);\n  const rowApi = {\n    getRow,\n    getRowId,\n    getRowModels,\n    getRowsCount,\n    getAllRowIds,\n    setRows,\n    updateRows,\n    getRowNode,\n    getRowIndexRelativeToVisibleRows,\n    unstable_replaceRows: replaceRows\n  };\n  const rowProApi = {\n    setRowIndex,\n    setRowChildrenExpansion,\n    getRowGroupChildren\n  };\n\n  /**\n   * EVENTS\n   */\n  const groupRows = React.useCallback(() => {\n    logger.info(`Row grouping pre-processing have changed, regenerating the row tree`);\n    let cache;\n    if (apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows) {\n      // The `props.rows` did not change since the last row grouping\n      // We can use the current rows cache which contains the partial updates done recently.\n      cache = _extends({}, apiRef.current.caches.rows, {\n        updates: {\n          type: 'full',\n          rows: gridDataRowIdsSelector(apiRef)\n        }\n      });\n    } else {\n      // The `props.rows` has changed since the last row grouping\n      // We must use the new `props.rows` on the new grouping\n      // This occurs because this event is triggered before the `useEffect` on the rows when both the grouping pre-processing and the rows changes on the same render\n      cache = createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      });\n    }\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [logger, apiRef, props.rows, props.getRowId, props.loading, props.rowCount, throttledRowsChange]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'rowTreeCreation') {\n      groupRows();\n    }\n  }, [groupRows]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    // `rowTreeCreation` is the only processor ran when `strategyAvailabilityChange` is fired.\n    // All the other processors listen to `rowsSet` which will be published by the `groupRows` method below.\n    if (apiRef.current.getActiveStrategy('rowTree') !== gridRowGroupingNameSelector(apiRef)) {\n      groupRows();\n    }\n  }, [apiRef, groupRows]);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n\n  /**\n   * APPLIERS\n   */\n  const applyHydrateRowsProcessor = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const response = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n        tree: gridRowTreeSelector(state, apiRef.current.instanceId),\n        treeDepths: gridRowTreeDepthsSelector(state, apiRef.current.instanceId),\n        dataRowIds: gridDataRowIdsSelector(state, apiRef.current.instanceId),\n        dataRowIdToModelLookup: gridRowsLookupSelector(state, apiRef.current.instanceId),\n        dataRowIdToIdLookup: gridRowsDataRowIdToIdLookupSelector(state, apiRef.current.instanceId)\n      });\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, response, {\n          totalTopLevelRowCount: getTopLevelRowCount({\n            tree: response.tree,\n            rowCountProp: props.rowCount\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n    apiRef.current.forceUpdate();\n  }, [apiRef, props.rowCount]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateRows', applyHydrateRowsProcessor);\n  useGridApiMethod(apiRef, rowApi, 'public');\n  useGridApiMethod(apiRef, rowProApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridRows`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    const areNewRowsAlreadyInState = apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows;\n    const isNewLoadingAlreadyInState = apiRef.current.caches.rows.loadingPropBeforePartialUpdates === props.loading;\n    const isNewRowCountAlreadyInState = apiRef.current.caches.rows.rowCountPropBeforePartialUpdates === props.rowCount;\n\n    // The new rows have already been applied (most likely in the `'rowGroupsPreProcessingChange'` listener)\n    if (areNewRowsAlreadyInState) {\n      // If the loading prop has changed, we need to update its value in the state because it won't be done by `throttledRowsChange`\n      if (!isNewLoadingAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            loading: props.loading\n          })\n        }));\n        apiRef.current.caches.rows.loadingPropBeforePartialUpdates = props.loading;\n        apiRef.current.forceUpdate();\n      }\n      if (!isNewRowCountAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            totalRowCount: Math.max(props.rowCount || 0, state.rows.totalRowCount),\n            totalTopLevelRowCount: Math.max(props.rowCount || 0, state.rows.totalTopLevelRowCount)\n          })\n        }));\n        apiRef.current.caches.rows.rowCountPropBeforePartialUpdates = props.rowCount;\n        apiRef.current.forceUpdate();\n      }\n      return;\n    }\n    logger.debug(`Updating all rows, new length ${props.rows.length}`);\n    throttledRowsChange({\n      cache: createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      }),\n      throttle: false\n    });\n  }, [props.rows, props.rowCount, props.getRowId, props.loading, logger, throttledRowsChange, apiRef]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "gridRowCountSelector", "gridRowsLookupSelector", "gridRowTreeSelector", "gridRowGroupingNameSelector", "gridRowTreeDepthsSelector", "gridDataRowIdsSelector", "gridRowsDataRowIdToIdLookupSelector", "gridRowMaximumTreeDepthSelector", "useTimeout", "GridSignature", "useGridApiEventHandler", "useGridVisibleRows", "gridSortedRowIdsSelector", "gridFilteredRowsLookupSelector", "getTreeNodeDescendants", "createRowsInternalCache", "getRowsStateFromCache", "isAutoGeneratedRow", "GRID_ROOT_GROUP_ID", "GRID_ID_AUTOGENERATED", "updateCacheWithNewRows", "getTopLevelRowCount", "getRowIdFromRowModel", "useGridRegisterPipeApplier", "rowsStateInitializer", "state", "props", "apiRef", "current", "caches", "rows", "getRowId", "loading", "rowCount", "rowCountProp", "loadingProp", "previousTree", "previousTreeDepths", "useGridRows", "process", "env", "NODE_ENV", "Object", "freeze", "error", "logger", "currentPage", "lastUpdateMs", "useRef", "Date", "now", "timeout", "getRow", "useCallback", "id", "model", "node", "getRowNode", "getRowIdProp", "row", "lookup", "useMemo", "reduce", "acc", "index", "throttledRowsChange", "cache", "throttle", "run", "setState", "publishEvent", "forceUpdate", "clear", "throttleRemainingTimeMs", "throttleRowsMs", "start", "setRows", "debug", "length", "prevCache", "rowsBeforePartialUpdates", "updateRows", "updates", "signature", "DataGrid", "Error", "join", "nonPinnedRowsUpdates", "for<PERSON>ach", "update", "rowNode", "type", "pinnedRowsCache", "pinnedRows", "prevModel", "idLookup", "push", "previousCache", "getRowModels", "dataRows", "idRowsLookup", "Map", "map", "_idRowsLookup$id", "getRowsCount", "getAllRowIds", "getRowIndexRelativeToVisibleRows", "setRowChildrenExpansion", "isExpanded", "currentNode", "newNode", "childrenExpanded", "tree", "_ref", "getRowGroupChildren", "skipAutoGeneratedRows", "groupId", "applySorting", "applyFiltering", "children", "groupNode", "sortedRowIds", "startIndex", "findIndex", "depth", "filteredRowsLookup", "filter", "childId", "setRowIndex", "rowId", "targetIndex", "parent", "group", "instanceId", "allRows", "oldIndex", "updatedRows", "splice", "replaceRows", "firstRowToRender", "newRows", "<PERSON><PERSON><PERSON><PERSON>", "dataRowIdToModelLookup", "dataRowIdToIdLookup", "rootGroup", "rootGroupChildren", "seenIds", "Set", "i", "rowModel", "removedRowId", "has", "rowTreeNodeConfig", "grouping<PERSON>ey", "add", "dataRowIds", "row<PERSON><PERSON>", "unstable_replaceRows", "rowProApi", "groupRows", "info", "handleStrategyProcessorChange", "methodName", "handleStrategyActivityChange", "getActiveStrategy", "applyHydrateRowsProcessor", "response", "unstable_applyPipeProcessors", "treeDepths", "totalTopLevelRowCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "areNewRowsAlreadyInState", "isNewLoadingAlreadyInState", "loadingPropBeforePartialUpdates", "isNewRowCountAlreadyInState", "rowCountPropBeforePartialUpdates", "totalRowCount", "Math", "max"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/features/rows/useGridRows.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridRowCountSelector, gridRowsLookupSelector, gridRowTreeSelector, gridRowGroupingNameSelector, gridRowTreeDepthsSelector, gridDataRowIdsSelector, gridRowsDataRowIdToIdLookupSelector, gridRowMaximumTreeDepthSelector } from './gridRowsSelector';\nimport { useTimeout } from '../../utils/useTimeout';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { gridSortedRowIdsSelector } from '../sorting/gridSortingSelector';\nimport { gridFilteredRowsLookupSelector } from '../filter/gridFilterSelector';\nimport { getTreeNodeDescendants, createRowsInternalCache, getRowsStateFromCache, isAutoGeneratedRow, GRID_ROOT_GROUP_ID, GRID_ID_AUTOGENERATED, updateCacheWithNewRows, getTopLevelRowCount, getRowIdFromRowModel } from './gridRowsUtils';\nimport { useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nexport const rowsStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.rows = createRowsInternalCache({\n    rows: props.rows,\n    getRowId: props.getRowId,\n    loading: props.loading,\n    rowCount: props.rowCount\n  });\n  return _extends({}, state, {\n    rows: getRowsStateFromCache({\n      apiRef,\n      rowCountProp: props.rowCount,\n      loadingProp: props.loading,\n      previousTree: null,\n      previousTreeDepths: null\n    })\n  });\n};\nexport const useGridRows = (apiRef, props) => {\n  if (process.env.NODE_ENV !== 'production') {\n    try {\n      // Freeze the `rows` prop so developers have a fast failure if they try to use Array.prototype.push().\n      Object.freeze(props.rows);\n    } catch (error) {\n      // Sometimes, it's impossible to freeze, so we give up on it.\n    }\n  }\n  const logger = useGridLogger(apiRef, 'useGridRows');\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const lastUpdateMs = React.useRef(Date.now());\n  const timeout = useTimeout();\n  const getRow = React.useCallback(id => {\n    const model = gridRowsLookupSelector(apiRef)[id];\n    if (model) {\n      return model;\n    }\n    const node = apiRef.current.getRowNode(id);\n    if (node && isAutoGeneratedRow(node)) {\n      return {\n        [GRID_ID_AUTOGENERATED]: id\n      };\n    }\n    return null;\n  }, [apiRef]);\n  const getRowIdProp = props.getRowId;\n  const getRowId = React.useCallback(row => {\n    if (GRID_ID_AUTOGENERATED in row) {\n      return row[GRID_ID_AUTOGENERATED];\n    }\n    if (getRowIdProp) {\n      return getRowIdProp(row);\n    }\n    return row.id;\n  }, [getRowIdProp]);\n  const lookup = React.useMemo(() => currentPage.rows.reduce((acc, {\n    id\n  }, index) => {\n    acc[id] = index;\n    return acc;\n  }, {}), [currentPage.rows]);\n  const throttledRowsChange = React.useCallback(({\n    cache,\n    throttle\n  }) => {\n    const run = () => {\n      lastUpdateMs.current = Date.now();\n      apiRef.current.setState(state => _extends({}, state, {\n        rows: getRowsStateFromCache({\n          apiRef,\n          rowCountProp: props.rowCount,\n          loadingProp: props.loading,\n          previousTree: gridRowTreeSelector(apiRef),\n          previousTreeDepths: gridRowTreeDepthsSelector(apiRef)\n        })\n      }));\n      apiRef.current.publishEvent('rowsSet');\n      apiRef.current.forceUpdate();\n    };\n    timeout.clear();\n    apiRef.current.caches.rows = cache;\n    if (!throttle) {\n      run();\n      return;\n    }\n    const throttleRemainingTimeMs = props.throttleRowsMs - (Date.now() - lastUpdateMs.current);\n    if (throttleRemainingTimeMs > 0) {\n      timeout.start(throttleRemainingTimeMs, run);\n      return;\n    }\n    run();\n  }, [props.throttleRowsMs, props.rowCount, props.loading, apiRef, timeout]);\n\n  /**\n   * API METHODS\n   */\n  const setRows = React.useCallback(rows => {\n    logger.debug(`Updating all rows, new length ${rows.length}`);\n    const cache = createRowsInternalCache({\n      rows,\n      getRowId: props.getRowId,\n      loading: props.loading,\n      rowCount: props.rowCount\n    });\n    const prevCache = apiRef.current.caches.rows;\n    cache.rowsBeforePartialUpdates = prevCache.rowsBeforePartialUpdates;\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [logger, props.getRowId, props.loading, props.rowCount, throttledRowsChange, apiRef]);\n  const updateRows = React.useCallback(updates => {\n    if (props.signature === GridSignature.DataGrid && updates.length > 1) {\n      throw new Error([\"MUI: You can't update several rows at once in `apiRef.current.updateRows` on the DataGrid.\", 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    const nonPinnedRowsUpdates = [];\n    updates.forEach(update => {\n      const id = getRowIdFromRowModel(update, props.getRowId, 'A row was provided without id when calling updateRows():');\n      const rowNode = apiRef.current.getRowNode(id);\n      if ((rowNode == null ? void 0 : rowNode.type) === 'pinnedRow') {\n        // @ts-ignore because otherwise `release:build` doesn't work\n        const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n        const prevModel = pinnedRowsCache.idLookup[id];\n        if (prevModel) {\n          pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n        }\n      } else {\n        nonPinnedRowsUpdates.push(update);\n      }\n    });\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows\n    });\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [props.signature, props.getRowId, throttledRowsChange, apiRef]);\n  const getRowModels = React.useCallback(() => {\n    const dataRows = gridDataRowIdsSelector(apiRef);\n    const idRowsLookup = gridRowsLookupSelector(apiRef);\n    return new Map(dataRows.map(id => {\n      var _idRowsLookup$id;\n      return [id, (_idRowsLookup$id = idRowsLookup[id]) != null ? _idRowsLookup$id : {}];\n    }));\n  }, [apiRef]);\n  const getRowsCount = React.useCallback(() => gridRowCountSelector(apiRef), [apiRef]);\n  const getAllRowIds = React.useCallback(() => gridDataRowIdsSelector(apiRef), [apiRef]);\n  const getRowIndexRelativeToVisibleRows = React.useCallback(id => lookup[id], [lookup]);\n  const setRowChildrenExpansion = React.useCallback((id, isExpanded) => {\n    const currentNode = apiRef.current.getRowNode(id);\n    if (!currentNode) {\n      throw new Error(`MUI: No row with id #${id} found`);\n    }\n    if (currentNode.type !== 'group') {\n      throw new Error('MUI: Only group nodes can be expanded or collapsed');\n    }\n    const newNode = _extends({}, currentNode, {\n      childrenExpanded: isExpanded\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [id]: newNode\n          })\n        })\n      });\n    });\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('rowExpansionChange', newNode);\n  }, [apiRef]);\n  const getRowNode = React.useCallback(id => {\n    var _ref;\n    return (_ref = gridRowTreeSelector(apiRef)[id]) != null ? _ref : null;\n  }, [apiRef]);\n  const getRowGroupChildren = React.useCallback(({\n    skipAutoGeneratedRows = true,\n    groupId,\n    applySorting,\n    applyFiltering\n  }) => {\n    const tree = gridRowTreeSelector(apiRef);\n    let children;\n    if (applySorting) {\n      const groupNode = tree[groupId];\n      if (!groupNode) {\n        return [];\n      }\n      const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n      children = [];\n      const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n      for (let index = startIndex; index < sortedRowIds.length && tree[sortedRowIds[index]].depth > groupNode.depth; index += 1) {\n        const id = sortedRowIds[index];\n        if (!skipAutoGeneratedRows || !isAutoGeneratedRow(tree[id])) {\n          children.push(id);\n        }\n      }\n    } else {\n      children = getTreeNodeDescendants(tree, groupId, skipAutoGeneratedRows);\n    }\n    if (applyFiltering) {\n      const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n      children = children.filter(childId => filteredRowsLookup[childId] !== false);\n    }\n    return children;\n  }, [apiRef]);\n  const setRowIndex = React.useCallback((rowId, targetIndex) => {\n    const node = apiRef.current.getRowNode(rowId);\n    if (!node) {\n      throw new Error(`MUI: No row with id #${rowId} found`);\n    }\n    if (node.parent !== GRID_ROOT_GROUP_ID) {\n      throw new Error(`MUI: The row reordering do not support reordering of grouped rows yet`);\n    }\n    if (node.type !== 'leaf') {\n      throw new Error(`MUI: The row reordering do not support reordering of footer or grouping rows`);\n    }\n    apiRef.current.setState(state => {\n      const group = gridRowTreeSelector(state, apiRef.current.instanceId)[GRID_ROOT_GROUP_ID];\n      const allRows = group.children;\n      const oldIndex = allRows.findIndex(row => row === rowId);\n      if (oldIndex === -1 || oldIndex === targetIndex) {\n        return state;\n      }\n      logger.debug(`Moving row ${rowId} to index ${targetIndex}`);\n      const updatedRows = [...allRows];\n      updatedRows.splice(targetIndex, 0, updatedRows.splice(oldIndex, 1)[0]);\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [GRID_ROOT_GROUP_ID]: _extends({}, group, {\n              children: updatedRows\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, logger]);\n  const replaceRows = React.useCallback((firstRowToRender, newRows) => {\n    if (props.signature === GridSignature.DataGrid && newRows.length > 1) {\n      throw new Error([\"MUI: You can't replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.\", 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (newRows.length === 0) {\n      return;\n    }\n    const treeDepth = gridRowMaximumTreeDepthSelector(apiRef);\n    if (treeDepth > 1) {\n      throw new Error('`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping');\n    }\n    const tree = _extends({}, gridRowTreeSelector(apiRef));\n    const dataRowIdToModelLookup = _extends({}, gridRowsLookupSelector(apiRef));\n    const dataRowIdToIdLookup = _extends({}, gridRowsDataRowIdToIdLookupSelector(apiRef));\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const seenIds = new Set();\n    for (let i = 0; i < newRows.length; i += 1) {\n      const rowModel = newRows[i];\n      const rowId = getRowIdFromRowModel(rowModel, props.getRowId, 'A row was provided without id when calling replaceRows().');\n      const [removedRowId] = rootGroupChildren.splice(firstRowToRender + i, 1, rowId);\n      if (!seenIds.has(removedRowId)) {\n        delete dataRowIdToModelLookup[removedRowId];\n        delete dataRowIdToIdLookup[removedRowId];\n        delete tree[removedRowId];\n      }\n      const rowTreeNodeConfig = {\n        id: rowId,\n        depth: 0,\n        parent: GRID_ROOT_GROUP_ID,\n        type: 'leaf',\n        groupingKey: null\n      };\n      dataRowIdToModelLookup[rowId] = rowModel;\n      dataRowIdToIdLookup[rowId] = rowId;\n      tree[rowId] = rowTreeNodeConfig;\n      seenIds.add(rowId);\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n\n    // Removes potential remaining skeleton rows from the dataRowIds.\n    const dataRowIds = rootGroupChildren.filter(childId => tree[childId].type === 'leaf');\n    apiRef.current.caches.rows.dataRowIdToModelLookup = dataRowIdToModelLookup;\n    apiRef.current.caches.rows.dataRowIdToIdLookup = dataRowIdToIdLookup;\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        dataRowIdToModelLookup,\n        dataRowIdToIdLookup,\n        dataRowIds,\n        tree\n      })\n    }));\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.signature, props.getRowId]);\n  const rowApi = {\n    getRow,\n    getRowId,\n    getRowModels,\n    getRowsCount,\n    getAllRowIds,\n    setRows,\n    updateRows,\n    getRowNode,\n    getRowIndexRelativeToVisibleRows,\n    unstable_replaceRows: replaceRows\n  };\n  const rowProApi = {\n    setRowIndex,\n    setRowChildrenExpansion,\n    getRowGroupChildren\n  };\n\n  /**\n   * EVENTS\n   */\n  const groupRows = React.useCallback(() => {\n    logger.info(`Row grouping pre-processing have changed, regenerating the row tree`);\n    let cache;\n    if (apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows) {\n      // The `props.rows` did not change since the last row grouping\n      // We can use the current rows cache which contains the partial updates done recently.\n      cache = _extends({}, apiRef.current.caches.rows, {\n        updates: {\n          type: 'full',\n          rows: gridDataRowIdsSelector(apiRef)\n        }\n      });\n    } else {\n      // The `props.rows` has changed since the last row grouping\n      // We must use the new `props.rows` on the new grouping\n      // This occurs because this event is triggered before the `useEffect` on the rows when both the grouping pre-processing and the rows changes on the same render\n      cache = createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      });\n    }\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [logger, apiRef, props.rows, props.getRowId, props.loading, props.rowCount, throttledRowsChange]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'rowTreeCreation') {\n      groupRows();\n    }\n  }, [groupRows]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    // `rowTreeCreation` is the only processor ran when `strategyAvailabilityChange` is fired.\n    // All the other processors listen to `rowsSet` which will be published by the `groupRows` method below.\n    if (apiRef.current.getActiveStrategy('rowTree') !== gridRowGroupingNameSelector(apiRef)) {\n      groupRows();\n    }\n  }, [apiRef, groupRows]);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n\n  /**\n   * APPLIERS\n   */\n  const applyHydrateRowsProcessor = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const response = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n        tree: gridRowTreeSelector(state, apiRef.current.instanceId),\n        treeDepths: gridRowTreeDepthsSelector(state, apiRef.current.instanceId),\n        dataRowIds: gridDataRowIdsSelector(state, apiRef.current.instanceId),\n        dataRowIdToModelLookup: gridRowsLookupSelector(state, apiRef.current.instanceId),\n        dataRowIdToIdLookup: gridRowsDataRowIdToIdLookupSelector(state, apiRef.current.instanceId)\n      });\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, response, {\n          totalTopLevelRowCount: getTopLevelRowCount({\n            tree: response.tree,\n            rowCountProp: props.rowCount\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n    apiRef.current.forceUpdate();\n  }, [apiRef, props.rowCount]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateRows', applyHydrateRowsProcessor);\n  useGridApiMethod(apiRef, rowApi, 'public');\n  useGridApiMethod(apiRef, rowProApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridRows`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    const areNewRowsAlreadyInState = apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows;\n    const isNewLoadingAlreadyInState = apiRef.current.caches.rows.loadingPropBeforePartialUpdates === props.loading;\n    const isNewRowCountAlreadyInState = apiRef.current.caches.rows.rowCountPropBeforePartialUpdates === props.rowCount;\n\n    // The new rows have already been applied (most likely in the `'rowGroupsPreProcessingChange'` listener)\n    if (areNewRowsAlreadyInState) {\n      // If the loading prop has changed, we need to update its value in the state because it won't be done by `throttledRowsChange`\n      if (!isNewLoadingAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            loading: props.loading\n          })\n        }));\n        apiRef.current.caches.rows.loadingPropBeforePartialUpdates = props.loading;\n        apiRef.current.forceUpdate();\n      }\n      if (!isNewRowCountAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            totalRowCount: Math.max(props.rowCount || 0, state.rows.totalRowCount),\n            totalTopLevelRowCount: Math.max(props.rowCount || 0, state.rows.totalTopLevelRowCount)\n          })\n        }));\n        apiRef.current.caches.rows.rowCountPropBeforePartialUpdates = props.rowCount;\n        apiRef.current.forceUpdate();\n      }\n      return;\n    }\n    logger.debug(`Updating all rows, new length ${props.rows.length}`);\n    throttledRowsChange({\n      cache: createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      }),\n      throttle: false\n    });\n  }, [props.rows, props.rowCount, props.getRowId, props.loading, logger, throttledRowsChange, apiRef]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,oBAAoB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,sBAAsB,EAAEC,mCAAmC,EAAEC,+BAA+B,QAAQ,oBAAoB;AAC5P,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,oCAAoC;AAC1F,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,wBAAwB,QAAQ,gCAAgC;AACzE,SAASC,8BAA8B,QAAQ,8BAA8B;AAC7E,SAASC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAC1O,SAASC,0BAA0B,QAAQ,2BAA2B;AACtE,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC5DA,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,GAAGf,uBAAuB,CAAC;IACnDe,IAAI,EAAEJ,KAAK,CAACI,IAAI;IAChBC,QAAQ,EAAEL,KAAK,CAACK,QAAQ;IACxBC,OAAO,EAAEN,KAAK,CAACM,OAAO;IACtBC,QAAQ,EAAEP,KAAK,CAACO;EAClB,CAAC,CAAC;EACF,OAAOrC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACzBK,IAAI,EAAEd,qBAAqB,CAAC;MAC1BW,MAAM;MACNO,YAAY,EAAER,KAAK,CAACO,QAAQ;MAC5BE,WAAW,EAAET,KAAK,CAACM,OAAO;MAC1BI,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE;IACtB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAACX,MAAM,EAAED,KAAK,KAAK;EAC5C,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI;MACF;MACAC,MAAM,CAACC,MAAM,CAACjB,KAAK,CAACI,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd;IAAA;EAEJ;EACA,MAAMC,MAAM,GAAG9C,aAAa,CAAC4B,MAAM,EAAE,aAAa,CAAC;EACnD,MAAMmB,WAAW,GAAGnC,kBAAkB,CAACgB,MAAM,EAAED,KAAK,CAAC;EACrD,MAAMqB,YAAY,GAAGlD,KAAK,CAACmD,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC7C,MAAMC,OAAO,GAAG3C,UAAU,CAAC,CAAC;EAC5B,MAAM4C,MAAM,GAAGvD,KAAK,CAACwD,WAAW,CAACC,EAAE,IAAI;IACrC,MAAMC,KAAK,GAAGtD,sBAAsB,CAAC0B,MAAM,CAAC,CAAC2B,EAAE,CAAC;IAChD,IAAIC,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;IACA,MAAMC,IAAI,GAAG7B,MAAM,CAACC,OAAO,CAAC6B,UAAU,CAACH,EAAE,CAAC;IAC1C,IAAIE,IAAI,IAAIvC,kBAAkB,CAACuC,IAAI,CAAC,EAAE;MACpC,OAAO;QACL,CAACrC,qBAAqB,GAAGmC;MAC3B,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAAC3B,MAAM,CAAC,CAAC;EACZ,MAAM+B,YAAY,GAAGhC,KAAK,CAACK,QAAQ;EACnC,MAAMA,QAAQ,GAAGlC,KAAK,CAACwD,WAAW,CAACM,GAAG,IAAI;IACxC,IAAIxC,qBAAqB,IAAIwC,GAAG,EAAE;MAChC,OAAOA,GAAG,CAACxC,qBAAqB,CAAC;IACnC;IACA,IAAIuC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACC,GAAG,CAAC;IAC1B;IACA,OAAOA,GAAG,CAACL,EAAE;EACf,CAAC,EAAE,CAACI,YAAY,CAAC,CAAC;EAClB,MAAME,MAAM,GAAG/D,KAAK,CAACgE,OAAO,CAAC,MAAMf,WAAW,CAAChB,IAAI,CAACgC,MAAM,CAAC,CAACC,GAAG,EAAE;IAC/DT;EACF,CAAC,EAAEU,KAAK,KAAK;IACXD,GAAG,CAACT,EAAE,CAAC,GAAGU,KAAK;IACf,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAACjB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC3B,MAAMmC,mBAAmB,GAAGpE,KAAK,CAACwD,WAAW,CAAC,CAAC;IAC7Ca,KAAK;IACLC;EACF,CAAC,KAAK;IACJ,MAAMC,GAAG,GAAGA,CAAA,KAAM;MAChBrB,YAAY,CAACnB,OAAO,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC;MACjCvB,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACnDK,IAAI,EAAEd,qBAAqB,CAAC;UAC1BW,MAAM;UACNO,YAAY,EAAER,KAAK,CAACO,QAAQ;UAC5BE,WAAW,EAAET,KAAK,CAACM,OAAO;UAC1BI,YAAY,EAAElC,mBAAmB,CAACyB,MAAM,CAAC;UACzCU,kBAAkB,EAAEjC,yBAAyB,CAACuB,MAAM;QACtD,CAAC;MACH,CAAC,CAAC,CAAC;MACHA,MAAM,CAACC,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;MACtC3C,MAAM,CAACC,OAAO,CAAC2C,WAAW,CAAC,CAAC;IAC9B,CAAC;IACDpB,OAAO,CAACqB,KAAK,CAAC,CAAC;IACf7C,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,GAAGoC,KAAK;IAClC,IAAI,CAACC,QAAQ,EAAE;MACbC,GAAG,CAAC,CAAC;MACL;IACF;IACA,MAAMK,uBAAuB,GAAG/C,KAAK,CAACgD,cAAc,IAAIzB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,YAAY,CAACnB,OAAO,CAAC;IAC1F,IAAI6C,uBAAuB,GAAG,CAAC,EAAE;MAC/BtB,OAAO,CAACwB,KAAK,CAACF,uBAAuB,EAAEL,GAAG,CAAC;MAC3C;IACF;IACAA,GAAG,CAAC,CAAC;EACP,CAAC,EAAE,CAAC1C,KAAK,CAACgD,cAAc,EAAEhD,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACM,OAAO,EAAEL,MAAM,EAAEwB,OAAO,CAAC,CAAC;;EAE1E;AACF;AACA;EACE,MAAMyB,OAAO,GAAG/E,KAAK,CAACwD,WAAW,CAACvB,IAAI,IAAI;IACxCe,MAAM,CAACgC,KAAK,CAAC,iCAAiC/C,IAAI,CAACgD,MAAM,EAAE,CAAC;IAC5D,MAAMZ,KAAK,GAAGnD,uBAAuB,CAAC;MACpCe,IAAI;MACJC,QAAQ,EAAEL,KAAK,CAACK,QAAQ;MACxBC,OAAO,EAAEN,KAAK,CAACM,OAAO;MACtBC,QAAQ,EAAEP,KAAK,CAACO;IAClB,CAAC,CAAC;IACF,MAAM8C,SAAS,GAAGpD,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI;IAC5CoC,KAAK,CAACc,wBAAwB,GAAGD,SAAS,CAACC,wBAAwB;IACnEf,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,MAAM,EAAEnB,KAAK,CAACK,QAAQ,EAAEL,KAAK,CAACM,OAAO,EAAEN,KAAK,CAACO,QAAQ,EAAEgC,mBAAmB,EAAEtC,MAAM,CAAC,CAAC;EACxF,MAAMsD,UAAU,GAAGpF,KAAK,CAACwD,WAAW,CAAC6B,OAAO,IAAI;IAC9C,IAAIxD,KAAK,CAACyD,SAAS,KAAK1E,aAAa,CAAC2E,QAAQ,IAAIF,OAAO,CAACJ,MAAM,GAAG,CAAC,EAAE;MACpE,MAAM,IAAIO,KAAK,CAAC,CAAC,4FAA4F,EAAE,yFAAyF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvN;IACA,MAAMC,oBAAoB,GAAG,EAAE;IAC/BL,OAAO,CAACM,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMnC,EAAE,GAAGhC,oBAAoB,CAACmE,MAAM,EAAE/D,KAAK,CAACK,QAAQ,EAAE,0DAA0D,CAAC;MACnH,MAAM2D,OAAO,GAAG/D,MAAM,CAACC,OAAO,CAAC6B,UAAU,CAACH,EAAE,CAAC;MAC7C,IAAI,CAACoC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,IAAI,MAAM,WAAW,EAAE;QAC7D;QACA,MAAMC,eAAe,GAAGjE,MAAM,CAACC,OAAO,CAACC,MAAM,CAACgE,UAAU;QACxD,MAAMC,SAAS,GAAGF,eAAe,CAACG,QAAQ,CAACzC,EAAE,CAAC;QAC9C,IAAIwC,SAAS,EAAE;UACbF,eAAe,CAACG,QAAQ,CAACzC,EAAE,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,EAAEkG,SAAS,EAAEL,MAAM,CAAC;QAChE;MACF,CAAC,MAAM;QACLF,oBAAoB,CAACS,IAAI,CAACP,MAAM,CAAC;MACnC;IACF,CAAC,CAAC;IACF,MAAMvB,KAAK,GAAG9C,sBAAsB,CAAC;MACnC8D,OAAO,EAAEK,oBAAoB;MAC7BxD,QAAQ,EAAEL,KAAK,CAACK,QAAQ;MACxBkE,aAAa,EAAEtE,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC;IACvC,CAAC,CAAC;IACFmC,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzC,KAAK,CAACyD,SAAS,EAAEzD,KAAK,CAACK,QAAQ,EAAEkC,mBAAmB,EAAEtC,MAAM,CAAC,CAAC;EAClE,MAAMuE,YAAY,GAAGrG,KAAK,CAACwD,WAAW,CAAC,MAAM;IAC3C,MAAM8C,QAAQ,GAAG9F,sBAAsB,CAACsB,MAAM,CAAC;IAC/C,MAAMyE,YAAY,GAAGnG,sBAAsB,CAAC0B,MAAM,CAAC;IACnD,OAAO,IAAI0E,GAAG,CAACF,QAAQ,CAACG,GAAG,CAAChD,EAAE,IAAI;MAChC,IAAIiD,gBAAgB;MACpB,OAAO,CAACjD,EAAE,EAAE,CAACiD,gBAAgB,GAAGH,YAAY,CAAC9C,EAAE,CAAC,KAAK,IAAI,GAAGiD,gBAAgB,GAAG,CAAC,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC5E,MAAM,CAAC,CAAC;EACZ,MAAM6E,YAAY,GAAG3G,KAAK,CAACwD,WAAW,CAAC,MAAMrD,oBAAoB,CAAC2B,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACpF,MAAM8E,YAAY,GAAG5G,KAAK,CAACwD,WAAW,CAAC,MAAMhD,sBAAsB,CAACsB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACtF,MAAM+E,gCAAgC,GAAG7G,KAAK,CAACwD,WAAW,CAACC,EAAE,IAAIM,MAAM,CAACN,EAAE,CAAC,EAAE,CAACM,MAAM,CAAC,CAAC;EACtF,MAAM+C,uBAAuB,GAAG9G,KAAK,CAACwD,WAAW,CAAC,CAACC,EAAE,EAAEsD,UAAU,KAAK;IACpE,MAAMC,WAAW,GAAGlF,MAAM,CAACC,OAAO,CAAC6B,UAAU,CAACH,EAAE,CAAC;IACjD,IAAI,CAACuD,WAAW,EAAE;MAChB,MAAM,IAAIxB,KAAK,CAAC,wBAAwB/B,EAAE,QAAQ,CAAC;IACrD;IACA,IAAIuD,WAAW,CAAClB,IAAI,KAAK,OAAO,EAAE;MAChC,MAAM,IAAIN,KAAK,CAAC,oDAAoD,CAAC;IACvE;IACA,MAAMyB,OAAO,GAAGlH,QAAQ,CAAC,CAAC,CAAC,EAAEiH,WAAW,EAAE;MACxCE,gBAAgB,EAAEH;IACpB,CAAC,CAAC;IACFjF,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI;MAC/B,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACzBK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAE;UAC7BkF,IAAI,EAAEpH,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,CAACkF,IAAI,EAAE;YAClC,CAAC1D,EAAE,GAAGwD;UACR,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnF,MAAM,CAACC,OAAO,CAAC2C,WAAW,CAAC,CAAC;IAC5B5C,MAAM,CAACC,OAAO,CAAC0C,YAAY,CAAC,oBAAoB,EAAEwC,OAAO,CAAC;EAC5D,CAAC,EAAE,CAACnF,MAAM,CAAC,CAAC;EACZ,MAAM8B,UAAU,GAAG5D,KAAK,CAACwD,WAAW,CAACC,EAAE,IAAI;IACzC,IAAI2D,IAAI;IACR,OAAO,CAACA,IAAI,GAAG/G,mBAAmB,CAACyB,MAAM,CAAC,CAAC2B,EAAE,CAAC,KAAK,IAAI,GAAG2D,IAAI,GAAG,IAAI;EACvE,CAAC,EAAE,CAACtF,MAAM,CAAC,CAAC;EACZ,MAAMuF,mBAAmB,GAAGrH,KAAK,CAACwD,WAAW,CAAC,CAAC;IAC7C8D,qBAAqB,GAAG,IAAI;IAC5BC,OAAO;IACPC,YAAY;IACZC;EACF,CAAC,KAAK;IACJ,MAAMN,IAAI,GAAG9G,mBAAmB,CAACyB,MAAM,CAAC;IACxC,IAAI4F,QAAQ;IACZ,IAAIF,YAAY,EAAE;MAChB,MAAMG,SAAS,GAAGR,IAAI,CAACI,OAAO,CAAC;MAC/B,IAAI,CAACI,SAAS,EAAE;QACd,OAAO,EAAE;MACX;MACA,MAAMC,YAAY,GAAG7G,wBAAwB,CAACe,MAAM,CAAC;MACrD4F,QAAQ,GAAG,EAAE;MACb,MAAMG,UAAU,GAAGD,YAAY,CAACE,SAAS,CAACrE,EAAE,IAAIA,EAAE,KAAK8D,OAAO,CAAC,GAAG,CAAC;MACnE,KAAK,IAAIpD,KAAK,GAAG0D,UAAU,EAAE1D,KAAK,GAAGyD,YAAY,CAAC3C,MAAM,IAAIkC,IAAI,CAACS,YAAY,CAACzD,KAAK,CAAC,CAAC,CAAC4D,KAAK,GAAGJ,SAAS,CAACI,KAAK,EAAE5D,KAAK,IAAI,CAAC,EAAE;QACzH,MAAMV,EAAE,GAAGmE,YAAY,CAACzD,KAAK,CAAC;QAC9B,IAAI,CAACmD,qBAAqB,IAAI,CAAClG,kBAAkB,CAAC+F,IAAI,CAAC1D,EAAE,CAAC,CAAC,EAAE;UAC3DiE,QAAQ,CAACvB,IAAI,CAAC1C,EAAE,CAAC;QACnB;MACF;IACF,CAAC,MAAM;MACLiE,QAAQ,GAAGzG,sBAAsB,CAACkG,IAAI,EAAEI,OAAO,EAAED,qBAAqB,CAAC;IACzE;IACA,IAAIG,cAAc,EAAE;MAClB,MAAMO,kBAAkB,GAAGhH,8BAA8B,CAACc,MAAM,CAAC;MACjE4F,QAAQ,GAAGA,QAAQ,CAACO,MAAM,CAACC,OAAO,IAAIF,kBAAkB,CAACE,OAAO,CAAC,KAAK,KAAK,CAAC;IAC9E;IACA,OAAOR,QAAQ;EACjB,CAAC,EAAE,CAAC5F,MAAM,CAAC,CAAC;EACZ,MAAMqG,WAAW,GAAGnI,KAAK,CAACwD,WAAW,CAAC,CAAC4E,KAAK,EAAEC,WAAW,KAAK;IAC5D,MAAM1E,IAAI,GAAG7B,MAAM,CAACC,OAAO,CAAC6B,UAAU,CAACwE,KAAK,CAAC;IAC7C,IAAI,CAACzE,IAAI,EAAE;MACT,MAAM,IAAI6B,KAAK,CAAC,wBAAwB4C,KAAK,QAAQ,CAAC;IACxD;IACA,IAAIzE,IAAI,CAAC2E,MAAM,KAAKjH,kBAAkB,EAAE;MACtC,MAAM,IAAImE,KAAK,CAAC,uEAAuE,CAAC;IAC1F;IACA,IAAI7B,IAAI,CAACmC,IAAI,KAAK,MAAM,EAAE;MACxB,MAAM,IAAIN,KAAK,CAAC,8EAA8E,CAAC;IACjG;IACA1D,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI;MAC/B,MAAM2G,KAAK,GAAGlI,mBAAmB,CAACuB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU,CAAC,CAACnH,kBAAkB,CAAC;MACvF,MAAMoH,OAAO,GAAGF,KAAK,CAACb,QAAQ;MAC9B,MAAMgB,QAAQ,GAAGD,OAAO,CAACX,SAAS,CAAChE,GAAG,IAAIA,GAAG,KAAKsE,KAAK,CAAC;MACxD,IAAIM,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,KAAKL,WAAW,EAAE;QAC/C,OAAOzG,KAAK;MACd;MACAoB,MAAM,CAACgC,KAAK,CAAC,cAAcoD,KAAK,aAAaC,WAAW,EAAE,CAAC;MAC3D,MAAMM,WAAW,GAAG,CAAC,GAAGF,OAAO,CAAC;MAChCE,WAAW,CAACC,MAAM,CAACP,WAAW,EAAE,CAAC,EAAEM,WAAW,CAACC,MAAM,CAACF,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,OAAO3I,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACzBK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAE;UAC7BkF,IAAI,EAAEpH,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,CAACkF,IAAI,EAAE;YAClC,CAAC9F,kBAAkB,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEwI,KAAK,EAAE;cACxCb,QAAQ,EAAEiB;YACZ,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACF7G,MAAM,CAACC,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;EACxC,CAAC,EAAE,CAAC3C,MAAM,EAAEkB,MAAM,CAAC,CAAC;EACpB,MAAM6F,WAAW,GAAG7I,KAAK,CAACwD,WAAW,CAAC,CAACsF,gBAAgB,EAAEC,OAAO,KAAK;IACnE,IAAIlH,KAAK,CAACyD,SAAS,KAAK1E,aAAa,CAAC2E,QAAQ,IAAIwD,OAAO,CAAC9D,MAAM,GAAG,CAAC,EAAE;MACpE,MAAM,IAAIO,KAAK,CAAC,CAAC,0FAA0F,EAAE,yFAAyF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrN;IACA,IAAIsD,OAAO,CAAC9D,MAAM,KAAK,CAAC,EAAE;MACxB;IACF;IACA,MAAM+D,SAAS,GAAGtI,+BAA+B,CAACoB,MAAM,CAAC;IACzD,IAAIkH,SAAS,GAAG,CAAC,EAAE;MACjB,MAAM,IAAIxD,KAAK,CAAC,yFAAyF,CAAC;IAC5G;IACA,MAAM2B,IAAI,GAAGpH,QAAQ,CAAC,CAAC,CAAC,EAAEM,mBAAmB,CAACyB,MAAM,CAAC,CAAC;IACtD,MAAMmH,sBAAsB,GAAGlJ,QAAQ,CAAC,CAAC,CAAC,EAAEK,sBAAsB,CAAC0B,MAAM,CAAC,CAAC;IAC3E,MAAMoH,mBAAmB,GAAGnJ,QAAQ,CAAC,CAAC,CAAC,EAAEU,mCAAmC,CAACqB,MAAM,CAAC,CAAC;IACrF,MAAMqH,SAAS,GAAGhC,IAAI,CAAC9F,kBAAkB,CAAC;IAC1C,MAAM+H,iBAAiB,GAAG,CAAC,GAAGD,SAAS,CAACzB,QAAQ,CAAC;IACjD,MAAM2B,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,OAAO,CAAC9D,MAAM,EAAEsE,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMC,QAAQ,GAAGT,OAAO,CAACQ,CAAC,CAAC;MAC3B,MAAMnB,KAAK,GAAG3G,oBAAoB,CAAC+H,QAAQ,EAAE3H,KAAK,CAACK,QAAQ,EAAE,2DAA2D,CAAC;MACzH,MAAM,CAACuH,YAAY,CAAC,GAAGL,iBAAiB,CAACR,MAAM,CAACE,gBAAgB,GAAGS,CAAC,EAAE,CAAC,EAAEnB,KAAK,CAAC;MAC/E,IAAI,CAACiB,OAAO,CAACK,GAAG,CAACD,YAAY,CAAC,EAAE;QAC9B,OAAOR,sBAAsB,CAACQ,YAAY,CAAC;QAC3C,OAAOP,mBAAmB,CAACO,YAAY,CAAC;QACxC,OAAOtC,IAAI,CAACsC,YAAY,CAAC;MAC3B;MACA,MAAME,iBAAiB,GAAG;QACxBlG,EAAE,EAAE2E,KAAK;QACTL,KAAK,EAAE,CAAC;QACRO,MAAM,EAAEjH,kBAAkB;QAC1ByE,IAAI,EAAE,MAAM;QACZ8D,WAAW,EAAE;MACf,CAAC;MACDX,sBAAsB,CAACb,KAAK,CAAC,GAAGoB,QAAQ;MACxCN,mBAAmB,CAACd,KAAK,CAAC,GAAGA,KAAK;MAClCjB,IAAI,CAACiB,KAAK,CAAC,GAAGuB,iBAAiB;MAC/BN,OAAO,CAACQ,GAAG,CAACzB,KAAK,CAAC;IACpB;IACAjB,IAAI,CAAC9F,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEoJ,SAAS,EAAE;MACjDzB,QAAQ,EAAE0B;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMU,UAAU,GAAGV,iBAAiB,CAACnB,MAAM,CAACC,OAAO,IAAIf,IAAI,CAACe,OAAO,CAAC,CAACpC,IAAI,KAAK,MAAM,CAAC;IACrFhE,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACgH,sBAAsB,GAAGA,sBAAsB;IAC1EnH,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACiH,mBAAmB,GAAGA,mBAAmB;IACpEpH,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;MACnDK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAE;QAC7BgH,sBAAsB;QACtBC,mBAAmB;QACnBY,UAAU;QACV3C;MACF,CAAC;IACH,CAAC,CAAC,CAAC;IACHrF,MAAM,CAACC,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;EACxC,CAAC,EAAE,CAAC3C,MAAM,EAAED,KAAK,CAACyD,SAAS,EAAEzD,KAAK,CAACK,QAAQ,CAAC,CAAC;EAC7C,MAAM6H,MAAM,GAAG;IACbxG,MAAM;IACNrB,QAAQ;IACRmE,YAAY;IACZM,YAAY;IACZC,YAAY;IACZ7B,OAAO;IACPK,UAAU;IACVxB,UAAU;IACViD,gCAAgC;IAChCmD,oBAAoB,EAAEnB;EACxB,CAAC;EACD,MAAMoB,SAAS,GAAG;IAChB9B,WAAW;IACXrB,uBAAuB;IACvBO;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM6C,SAAS,GAAGlK,KAAK,CAACwD,WAAW,CAAC,MAAM;IACxCR,MAAM,CAACmH,IAAI,CAAC,qEAAqE,CAAC;IAClF,IAAI9F,KAAK;IACT,IAAIvC,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACkD,wBAAwB,KAAKtD,KAAK,CAACI,IAAI,EAAE;MACtE;MACA;MACAoC,KAAK,GAAGtE,QAAQ,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,EAAE;QAC/CoD,OAAO,EAAE;UACPS,IAAI,EAAE,MAAM;UACZ7D,IAAI,EAAEzB,sBAAsB,CAACsB,MAAM;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA;MACA;MACAuC,KAAK,GAAGnD,uBAAuB,CAAC;QAC9Be,IAAI,EAAEJ,KAAK,CAACI,IAAI;QAChBC,QAAQ,EAAEL,KAAK,CAACK,QAAQ;QACxBC,OAAO,EAAEN,KAAK,CAACM,OAAO;QACtBC,QAAQ,EAAEP,KAAK,CAACO;MAClB,CAAC,CAAC;IACJ;IACAgC,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,MAAM,EAAElB,MAAM,EAAED,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACK,QAAQ,EAAEL,KAAK,CAACM,OAAO,EAAEN,KAAK,CAACO,QAAQ,EAAEgC,mBAAmB,CAAC,CAAC;EACpG,MAAMgG,6BAA6B,GAAGpK,KAAK,CAACwD,WAAW,CAAC6G,UAAU,IAAI;IACpE,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MACpCH,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACf,MAAMI,4BAA4B,GAAGtK,KAAK,CAACwD,WAAW,CAAC,MAAM;IAC3D;IACA;IACA,IAAI1B,MAAM,CAACC,OAAO,CAACwI,iBAAiB,CAAC,SAAS,CAAC,KAAKjK,2BAA2B,CAACwB,MAAM,CAAC,EAAE;MACvFoI,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACpI,MAAM,EAAEoI,SAAS,CAAC,CAAC;EACvBrJ,sBAAsB,CAACiB,MAAM,EAAE,+BAA+B,EAAEsI,6BAA6B,CAAC;EAC9FvJ,sBAAsB,CAACiB,MAAM,EAAE,4BAA4B,EAAEwI,4BAA4B,CAAC;;EAE1F;AACF;AACA;EACE,MAAME,yBAAyB,GAAGxK,KAAK,CAACwD,WAAW,CAAC,MAAM;IACxD1B,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI;MAC/B,MAAM6I,QAAQ,GAAG3I,MAAM,CAACC,OAAO,CAAC2I,4BAA4B,CAAC,aAAa,EAAE;QAC1EvD,IAAI,EAAE9G,mBAAmB,CAACuB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU,CAAC;QAC3DmC,UAAU,EAAEpK,yBAAyB,CAACqB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU,CAAC;QACvEsB,UAAU,EAAEtJ,sBAAsB,CAACoB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU,CAAC;QACpES,sBAAsB,EAAE7I,sBAAsB,CAACwB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU,CAAC;QAChFU,mBAAmB,EAAEzI,mCAAmC,CAACmB,KAAK,EAAEE,MAAM,CAACC,OAAO,CAACyG,UAAU;MAC3F,CAAC,CAAC;MACF,OAAOzI,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACzBK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAEwI,QAAQ,EAAE;UACvCG,qBAAqB,EAAEpJ,mBAAmB,CAAC;YACzC2F,IAAI,EAAEsD,QAAQ,CAACtD,IAAI;YACnB9E,YAAY,EAAER,KAAK,CAACO;UACtB,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFN,MAAM,CAACC,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;IACtC3C,MAAM,CAACC,OAAO,CAAC2C,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC5C,MAAM,EAAED,KAAK,CAACO,QAAQ,CAAC,CAAC;EAC5BV,0BAA0B,CAACI,MAAM,EAAE,aAAa,EAAE0I,yBAAyB,CAAC;EAC5EvK,gBAAgB,CAAC6B,MAAM,EAAEiI,MAAM,EAAE,QAAQ,CAAC;EAC1C9J,gBAAgB,CAAC6B,MAAM,EAAEmI,SAAS,EAAEpI,KAAK,CAACyD,SAAS,KAAK1E,aAAa,CAAC2E,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAEtG;EACA;EACA,MAAMsF,aAAa,GAAG7K,KAAK,CAACmD,MAAM,CAAC,IAAI,CAAC;EACxCnD,KAAK,CAAC8K,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAAC9I,OAAO,EAAE;MACzB8I,aAAa,CAAC9I,OAAO,GAAG,KAAK;MAC7B;IACF;IACA,MAAMgJ,wBAAwB,GAAGjJ,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACkD,wBAAwB,KAAKtD,KAAK,CAACI,IAAI;IACnG,MAAM+I,0BAA0B,GAAGlJ,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACgJ,+BAA+B,KAAKpJ,KAAK,CAACM,OAAO;IAC/G,MAAM+I,2BAA2B,GAAGpJ,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACkJ,gCAAgC,KAAKtJ,KAAK,CAACO,QAAQ;;IAElH;IACA,IAAI2I,wBAAwB,EAAE;MAC5B;MACA,IAAI,CAACC,0BAA0B,EAAE;QAC/BlJ,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;UACnDK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAE;YAC7BE,OAAO,EAAEN,KAAK,CAACM;UACjB,CAAC;QACH,CAAC,CAAC,CAAC;QACHL,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACgJ,+BAA+B,GAAGpJ,KAAK,CAACM,OAAO;QAC1EL,MAAM,CAACC,OAAO,CAAC2C,WAAW,CAAC,CAAC;MAC9B;MACA,IAAI,CAACwG,2BAA2B,EAAE;QAChCpJ,MAAM,CAACC,OAAO,CAACyC,QAAQ,CAAC5C,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;UACnDK,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACK,IAAI,EAAE;YAC7BmJ,aAAa,EAAEC,IAAI,CAACC,GAAG,CAACzJ,KAAK,CAACO,QAAQ,IAAI,CAAC,EAAER,KAAK,CAACK,IAAI,CAACmJ,aAAa,CAAC;YACtER,qBAAqB,EAAES,IAAI,CAACC,GAAG,CAACzJ,KAAK,CAACO,QAAQ,IAAI,CAAC,EAAER,KAAK,CAACK,IAAI,CAAC2I,qBAAqB;UACvF,CAAC;QACH,CAAC,CAAC,CAAC;QACH9I,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,IAAI,CAACkJ,gCAAgC,GAAGtJ,KAAK,CAACO,QAAQ;QAC5EN,MAAM,CAACC,OAAO,CAAC2C,WAAW,CAAC,CAAC;MAC9B;MACA;IACF;IACA1B,MAAM,CAACgC,KAAK,CAAC,iCAAiCnD,KAAK,CAACI,IAAI,CAACgD,MAAM,EAAE,CAAC;IAClEb,mBAAmB,CAAC;MAClBC,KAAK,EAAEnD,uBAAuB,CAAC;QAC7Be,IAAI,EAAEJ,KAAK,CAACI,IAAI;QAChBC,QAAQ,EAAEL,KAAK,CAACK,QAAQ;QACxBC,OAAO,EAAEN,KAAK,CAACM,OAAO;QACtBC,QAAQ,EAAEP,KAAK,CAACO;MAClB,CAAC,CAAC;MACFkC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzC,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACK,QAAQ,EAAEL,KAAK,CAACM,OAAO,EAAEa,MAAM,EAAEoB,mBAAmB,EAAEtC,MAAM,CAAC,CAAC;AACtG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}