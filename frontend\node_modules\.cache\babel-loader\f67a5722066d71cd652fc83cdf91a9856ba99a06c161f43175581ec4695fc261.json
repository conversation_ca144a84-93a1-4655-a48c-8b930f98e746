{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TableSortLabel } from '@mui/material';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  row,\n  index,\n  columns,\n  onCellEdit,\n  onNavigateCell\n}) => {\n  _s();\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n  const handleCellSave = columnField => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n  const handleKeyPress = (e, columnField) => {\n    // 我要达到的效果是按了一次enter是保存，然后第二次enter才是换行\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      // 保存数据\n      handleCellSave(columnField);\n      // 导航到下一行同列\n      if (onNavigateCell && e.enterCount === 1) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'up' : 'down');\n        }, 100);\n      }\n    } else if (e.key === 'Tab') {\n      e.preventDefault();\n      handleCellSave(columnField);\n      // 导航到下一列\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'left' : 'right');\n        }, 100);\n      }\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Draggable, {\n    draggableId: row.id.toString(),\n    index: index,\n    children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableRow, {\n      ref: provided.innerRef,\n      ...provided.draggableProps,\n      sx: {\n        backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n        transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n        boxShadow: snapshot.isDragging ? 3 : 0,\n        transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n        '&.removed-row': {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        },\n        ...(snapshot.isDragging && {\n          zIndex: 1000\n        }),\n        ...(row._removed && {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        })\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        ...provided.dragHandleProps,\n        sx: {\n          width: 40,\n          cursor: 'grab',\n          '&:active': {\n            cursor: 'grabbing'\n          },\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n          sx: {\n            color: 'text.secondary'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), columns.map(column => {\n        const value = row[column.field];\n        const isEditing = editingCell === column.field;\n        return /*#__PURE__*/_jsxDEV(TableCell, {\n          \"data-row-id\": row.id,\n          \"data-field\": column.field,\n          sx: {\n            padding: '8px',\n            cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default'\n          }\n          // onClick={() => handleCellClick(column.field, value)}\n          ,\n          onDoubleClick: () => handleCellClick(column.field, value),\n          children: isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            value: editValue,\n            onChange: e => setEditValue(e.target.value),\n            onKeyDown: e => handleKeyPress(e, column.field),\n            onBlur: () => handleCellSave(column.field),\n            autoFocus: true,\n            fullWidth: true,\n            variant: \"standard\",\n            sx: {\n              '& .MuiInput-root': {\n                fontSize: 'inherit',\n                '&:before': {\n                  borderBottom: '1px solid rgba(0, 0, 0, 0.12)'\n                },\n                '&:hover:before': {\n                  borderBottom: '2px solid rgba(0, 0, 0, 0.87)'\n                },\n                '&:after': {\n                  borderBottom: '2px solid #1976d2'\n                }\n              },\n              '& .MuiInput-input': {\n                padding: '4px 0',\n                fontSize: 'inherit',\n                fontFamily: 'inherit'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 19\n          }, this) : column.renderCell ? column.renderCell({\n            row,\n            value\n          }) : value\n        }, column.field, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 15\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}, \"vUzEbioNkVLGJwl96b8AAiFyV68=\")), \"vUzEbioNkVLGJwl96b8AAiFyV68=\");\n\n// 默认的REMARKS选项\n_c2 = DraggableTableRow;\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c3 = _s2(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c4 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s3();\n  // 先声明columnOrder\n  const columnOrder = useMemo(() => [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }], []);\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01',\n            'FEB': '02',\n            'MAR': '03',\n            'APR': '04',\n            'MAY': '05',\n            'JUNE': '06',\n            'JULY': '07',\n            'AUG': '08',\n            'SEP': '09',\n            'OCT': '10',\n            'NOV': '11',\n            'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 排序状态\n  const [sortConfig, setSortConfig] = useState({\n    field: null,\n    direction: 'asc'\n  });\n\n  // 排序处理函数\n  const handleSort = field => {\n    const isAsc = sortConfig.field === field && sortConfig.direction === 'asc';\n    const newDirection = isAsc ? 'desc' : 'asc';\n    setSortConfig({\n      field,\n      direction: newDirection\n    });\n  };\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback(result => {\n    if (!result.destination) {\n      return;\n    }\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = {\n            ...row,\n            [field]: newValue\n          };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n          return updatedRow;\n        }\n        return row;\n      });\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 搜索和排序过滤逻辑\n  const filteredGridData = useMemo(() => {\n    let data = gridData || [];\n\n    // 搜索过滤\n    if (debouncedSearchText.trim()) {\n      const searchLower = debouncedSearchText.toLowerCase();\n      data = data.filter(row => {\n        if (searchColumn === 'all') {\n          // 搜索所有列\n          return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n        } else {\n          // 搜索特定列\n          const cellValue = row[searchColumn];\n          return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n        }\n      });\n    }\n\n    // 排序处理\n    if (sortConfig.field) {\n      data = [...data].sort((a, b) => {\n        // TOTAL行始终在最后\n        if (a.NO === 'TOTAL') return 1;\n        if (b.NO === 'TOTAL') return -1;\n        const aValue = a[sortConfig.field];\n        const bValue = b[sortConfig.field];\n\n        // 处理数字类型\n        if (typeof aValue === 'number' && typeof bValue === 'number') {\n          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n        }\n\n        // 处理字符串类型\n        const aStr = String(aValue || '').toLowerCase();\n        const bStr = String(bValue || '').toLowerCase();\n        if (sortConfig.direction === 'asc') {\n          return aStr.localeCompare(bStr);\n        } else {\n          return bStr.localeCompare(aStr);\n        }\n      });\n    }\n    return data;\n  }, [gridData, debouncedSearchText, searchColumn, sortConfig]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 处理单元格导航\n  const handleNavigateCell = useCallback((currentRowId, currentField, direction) => {\n    const editableColumns = columnOrder.filter(col => col.editable && col.field !== 'NO' && col.field !== 'ACTION' && col.field !== 'REMARKS');\n    const dataRows = memoGridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n    const currentRowIndex = dataRows.findIndex(row => row.id === currentRowId);\n    const currentColIndex = editableColumns.findIndex(col => col.field === currentField);\n    if (currentRowIndex === -1 || currentColIndex === -1) return;\n    let targetRowIndex = currentRowIndex;\n    let targetColIndex = currentColIndex;\n    switch (direction) {\n      case 'down':\n        targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n        break;\n      case 'up':\n        targetRowIndex = Math.max(currentRowIndex - 1, 0);\n        break;\n      case 'right':\n        // Tab键：如果已经是最后一列，跳到下一行的第一个可编辑列\n        if (currentColIndex >= editableColumns.length - 1) {\n          // 跳到下一行的第一列\n          targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n          targetColIndex = 0;\n        } else {\n          // 否则跳到同行的下一列\n          targetColIndex = currentColIndex + 1;\n        }\n        break;\n      case 'left':\n        // Shift+Tab：如果已经是第一列，跳到上一行的最后一个可编辑列\n        if (currentColIndex <= 0) {\n          // 跳到上一行的最后一列\n          targetRowIndex = Math.max(currentRowIndex - 1, 0);\n          targetColIndex = editableColumns.length - 1;\n        } else {\n          // 否则跳到同行的上一列\n          targetColIndex = currentColIndex - 1;\n        }\n        break;\n      default:\n        return;\n    }\n    const targetRow = dataRows[targetRowIndex];\n    const targetColumn = editableColumns[targetColIndex];\n    if (targetRow && targetColumn) {\n      // 触发目标单元格的编辑\n      setTimeout(() => {\n        const targetElement = document.querySelector(`[data-row-id=\"${targetRow.id}\"][data-field=\"${targetColumn.field}\"]`);\n        if (targetElement) {\n          targetElement.dispatchEvent(new Event('dblclick', {\n            bubbles: true\n          }));\n        }\n      }, 50);\n    }\n  }, [columnOrder, memoGridData]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1189,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1236,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1243,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1288,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1324,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1330,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 17\n              }, this), \"\\u6570\\u636E\\u641C\\u7D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1364,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: searchColumn,\n                  label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n                  onChange: e => setSearchColumn(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u5168\\u90E8\\u5217\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1370,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"NO\",\n                    children: \"NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DATE\",\n                    children: \"DATE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1372,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"VEHICLE NO\",\n                    children: \"VEHICLE NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"RO NO\",\n                    children: \"RO NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"KM\",\n                    children: \"KM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"REMARKS\",\n                    children: \"REMARKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"MAXCHECK\",\n                    children: \"HOURS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1377,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMMISSION\",\n                    children: \"AMOUNT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1365,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n                value: searchText,\n                onChange: e => setSearchText(e.target.value),\n                sx: {\n                  flexGrow: 1,\n                  minWidth: 200\n                },\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1391,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setSearchText(''),\n                      edge: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1401,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1396,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1395,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 17\n              }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1419,\n                columnNumber: 17\n              }, this), \"\\u64CD\\u4F5C\\u9009\\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"success\",\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1427,\n                  columnNumber: 30\n                }, this),\n                onClick: handleDownload,\n                children: \"\\u4E0B\\u8F7DExcel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 53\n                }, this) : /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 102\n                }, this),\n                onClick: generateDocument,\n                disabled: isGeneratingDocument,\n                children: isGeneratingDocument ? '生成中...' : '生成Excel文档'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 30\n                }, this),\n                onClick: handleCleanup,\n                children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1353,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragDropContext, {\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease'\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  height: 48\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    width: 40,\n                    fontWeight: 'bold',\n                    height: 48,\n                    padding: '8px'\n                  },\n                  children: \"\\u62D6\\u62FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1481,\n                  columnNumber: 19\n                }, this), columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'bold',\n                    backgroundColor: 'background.default',\n                    borderRight: '2px solid',\n                    borderColor: 'divider',\n                    height: 48,\n                    padding: '8px',\n                    '&:last-child': {\n                      borderRight: 'none'\n                    }\n                  },\n                  children: column.field !== 'ACTION' && column.field !== 'REMARKS' ? /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                    active: sortConfig.field === column.field,\n                    direction: sortConfig.field === column.field ? sortConfig.direction : 'asc',\n                    onClick: () => handleSort(column.field),\n                    sx: {\n                      '& .MuiTableSortLabel-icon': {\n                        fontSize: '1rem'\n                      }\n                    },\n                    children: column.headerName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1506,\n                    columnNumber: 25\n                  }, this) : column.headerName\n                }, column.field, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1479,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Droppable, {\n              droppableId: \"table-body\",\n              children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableBody, {\n                ref: provided.innerRef,\n                ...provided.droppableProps,\n                sx: {\n                  backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                  transition: 'background-color 0.2s ease',\n                  borderTop: '2px solid',\n                  borderColor: 'divider'\n                },\n                children: [memoGridData.filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                .slice(paginationModel.page * paginationModel.pageSize, (paginationModel.page + 1) * paginationModel.pageSize).map((row, index) => /*#__PURE__*/_jsxDEV(DraggableTableRow, {\n                  row: row,\n                  index: index,\n                  columns: columns,\n                  onCellEdit: handleCellEdit,\n                  onNavigateCell: handleNavigateCell\n                }, row.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 25\n                }, this)), memoGridData.find(row => row.NO === 'TOTAL') && /*#__PURE__*/_jsxDEV(TableRow, {\n                  className: \"total-row\",\n                  sx: {\n                    backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                    fontWeight: 'bold',\n                    '& .MuiTableCell-root': {\n                      fontWeight: 'bold',\n                      borderTop: '2px solid',\n                      borderColor: 'primary.main'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    sx: {\n                      width: 40\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1567,\n                    columnNumber: 25\n                  }, this), columns.map(column => {\n                    const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                    const value = totalRow ? totalRow[column.field] : '';\n                    return /*#__PURE__*/_jsxDEV(TableCell, {\n                      sx: {\n                        padding: '8px'\n                      },\n                      children: column.renderCell ? column.renderCell({\n                        row: totalRow,\n                        value\n                      }) : value\n                    }, column.field, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1574,\n                      columnNumber: 29\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 23\n                }, this), provided.placeholder]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1528,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1477,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          component: \"div\",\n          count: memoGridData.filter(row => row.NO !== 'TOTAL').length,\n          page: paginationModel.page,\n          onPageChange: (event, newPage) => {\n            setPaginationModel(prev => ({\n              ...prev,\n              page: newPage\n            }));\n          },\n          rowsPerPage: paginationModel.pageSize,\n          onRowsPerPageChange: event => {\n            const newPageSize = parseInt(event.target.value, 10);\n            setPaginationModel({\n              page: 0,\n              pageSize: newPageSize\n            });\n            localStorage.setItem('dataGridPageSize', newPageSize.toString());\n          },\n          rowsPerPageOptions: [25, 50, 100],\n          labelRowsPerPage: \"\\u6BCF\\u9875\\u884C\\u6570:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} 共 ${count} 条`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1459,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1458,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1622,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1624,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1623,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1621,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1620,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1641,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1651,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1649,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1634,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1682,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1677,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1687,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1686,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1672,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1663,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1695,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1694,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1611,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1709,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1711,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1710,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1725,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1723,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1700,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1304,\n    columnNumber: 5\n  }, this);\n};\n_s3(ResultDisplay, \"QKeMqrPP+sllZoPKYfonqv95TDI=\");\n_c5 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DraggableTableRow$React.memo\");\n$RefreshReg$(_c2, \"DraggableTableRow\");\n$RefreshReg$(_c3, \"RemarkChip$React.memo\");\n$RefreshReg$(_c4, \"RemarkChip\");\n$RefreshReg$(_c5, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "TableSortLabel", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "SettingsIcon", "axios", "API_URL", "FixedSizeList", "DragDropContext", "Droppable", "Draggable", "DragIndicatorIcon", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DraggableTableRow", "_s", "memo", "_c", "row", "index", "columns", "onCellEdit", "onNavigateCell", "editingCell", "setEditingCell", "editValue", "setEditValue", "handleCellClick", "columnField", "currentValue", "column", "find", "col", "field", "editable", "isTotal", "_removed", "handleCellSave", "id", "handleKeyPress", "e", "key", "preventDefault", "enterCount", "shift<PERSON>ey", "draggableId", "toString", "children", "provided", "snapshot", "ref", "innerRef", "draggableProps", "sx", "backgroundColor", "isDragging", "transform", "boxShadow", "transition", "color", "textDecoration", "zIndex", "dragHandleProps", "width", "cursor", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "value", "isEditing", "padding", "onDoubleClick", "size", "onChange", "target", "onKeyDown", "onBlur", "autoFocus", "fullWidth", "variant", "fontSize", "borderBottom", "fontFamily", "renderCell", "_c2", "DEFAULT_REMARKS_OPTIONS", "RemarkChip", "_s2", "_c3", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "textTransform", "overflow", "textOverflow", "whiteSpace", "height", "lineHeight", "_c4", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s3", "columnOrder", "headerName", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "getSelectedMonth", "commissionState", "selectedWorksheet", "match", "monthName", "year", "monthMap", "monthNumber", "toUpperCase", "error", "console", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "sortConfig", "setSortConfig", "direction", "handleSort", "isAsc", "newDirection", "debouncedSaveToLocalStorage", "setItem", "stringify", "log", "length", "debouncedNotifyParent", "recalculateTotal", "totalRow", "NO", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "handleDragEnd", "result", "destination", "sourceIndex", "source", "destinationIndex", "setGridData", "prev", "newData", "nonTotalRows", "removed", "splice", "for<PERSON>ach", "reorderedData", "handleCellEdit", "newValue", "updatedData", "updatedRow", "numericFields", "includes", "undefined", "numValue", "isNaN", "originalData", "setOriginalData", "processedData", "REMARKS", "_selected_remarks", "gridData", "validatedData", "processedWithIds", "filteredGridData", "trim", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "sort", "a", "b", "aValue", "bValue", "aStr", "bStr", "localeCompare", "memoGridData", "handleNavigateCell", "currentRowId", "current<PERSON><PERSON>", "editableColumns", "dataRows", "currentRowIndex", "findIndex", "currentColIndex", "targetRowIndex", "targetColIndex", "Math", "min", "max", "targetRow", "targetColumn", "targetElement", "document", "querySelector", "dispatchEvent", "Event", "bubbles", "timer", "setPaginationModel", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "handleDownload", "startsWith", "downloadUrl", "link", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "deleteOption", "item", "handleRemoveRow", "noCounter", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "newId", "currentRow", "newRowNo", "newRow", "DATE", "KM", "MAXCHECK", "generateDocument", "filteredRows", "docData", "split", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "hasOwnProperty", "flex", "params", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "startIcon", "fontWeight", "Boolean", "dataLength", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "container", "xs", "md", "sm", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "disabled", "onDragEnd", "borderRight", "borderColor", "<PERSON><PERSON><PERSON><PERSON>", "active", "droppableId", "droppableProps", "isDraggingOver", "borderTop", "slice", "className", "component", "count", "onPageChange", "event", "newPage", "rowsPerPage", "onRowsPerPageChange", "newPageSize", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "onClose", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "margin", "type", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel,\n  Grid,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  TableSortLabel\n} from '@mui/material';\n\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\n\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = React.memo(({ row, index, columns, onCellEdit, onNavigateCell }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n\n  const handleCellSave = (columnField) => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n\n  const handleKeyPress = (e, columnField) => {\n    // 我要达到的效果是按了一次enter是保存，然后第二次enter才是换行\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      // 保存数据\n      handleCellSave(columnField);\n      // 导航到下一行同列\n      if (onNavigateCell && e.enterCount === 1) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'up' : 'down');\n        }, 100);\n      }\n    } else if (e.key === 'Tab') {\n      e.preventDefault();\n      handleCellSave(columnField);\n      // 导航到下一列\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'left' : 'right');\n        }, 100);\n      }\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n\n  return (\n    <Draggable draggableId={row.id.toString()} index={index}>\n      {(provided, snapshot) => (\n        <TableRow\n          ref={provided.innerRef}\n          {...provided.draggableProps}\n          sx={{\n            backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n            transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n            boxShadow: snapshot.isDragging ? 3 : 0,\n            transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n            '&.removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            },\n            ...(snapshot.isDragging && {\n              zIndex: 1000,\n            }),\n            ...(row._removed && {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            }),\n          }}\n        >\n          {/* 拖拽手柄 */}\n          <TableCell\n            {...provided.dragHandleProps}\n            sx={{\n              width: 40,\n              cursor: 'grab',\n              '&:active': { cursor: 'grabbing' },\n              textAlign: 'center',\n            }}\n          >\n            <DragIndicatorIcon sx={{ color: 'text.secondary' }} />\n          </TableCell>\n\n          {/* 数据列 */}\n          {columns.map((column) => {\n            const value = row[column.field];\n            const isEditing = editingCell === column.field;\n\n            return (\n              <TableCell\n                key={column.field}\n                data-row-id={row.id}\n                data-field={column.field}\n                sx={{\n                  padding: '8px',\n                  cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default',\n                }}\n                // onClick={() => handleCellClick(column.field, value)}\n                onDoubleClick={() => handleCellClick(column.field, value)}\n              >\n                {isEditing ? (\n                  <TextField\n                    size=\"small\"\n                    value={editValue}\n                    onChange={(e) => setEditValue(e.target.value)}\n                    onKeyDown={(e) => handleKeyPress(e, column.field)}\n                    onBlur={() => handleCellSave(column.field)}\n                    autoFocus\n                    fullWidth\n                    variant=\"standard\"\n                    sx={{\n                      '& .MuiInput-root': {\n                        fontSize: 'inherit',\n                        '&:before': {\n                          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',\n                        },\n                        '&:hover:before': {\n                          borderBottom: '2px solid rgba(0, 0, 0, 0.87)',\n                        },\n                        '&:after': {\n                          borderBottom: '2px solid #1976d2',\n                        },\n                      },\n                      '& .MuiInput-input': {\n                        padding: '4px 0',\n                        fontSize: 'inherit',\n                        fontFamily: 'inherit',\n                      },\n                    }}\n                  />\n                ) : (\n                  column.renderCell ? column.renderCell({ row, value }) : value\n                )}\n              </TableCell>\n            );\n          })}\n        </TableRow>\n      )}\n    </Draggable>\n  );\n});\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = useMemo(() => [\n    { field: 'NO', headerName: 'NO', editable: false, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ], []);\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',\n            'MAY': '05', 'JUNE': '06', 'JULY': '07', 'AUG': '08',\n            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 排序状态\n  const [sortConfig, setSortConfig] = useState({ field: null, direction: 'asc' });\n\n  // 排序处理函数\n  const handleSort = (field) => {\n    const isAsc = sortConfig.field === field && sortConfig.direction === 'asc';\n    const newDirection = isAsc ? 'desc' : 'asc';\n    setSortConfig({ field, direction: newDirection });\n  };\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback((result) => {\n    if (!result.destination) {\n      return;\n    }\n\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = { ...row, [field]: newValue };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n\n          return updatedRow;\n        }\n        return row;\n      });\n\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 搜索和排序过滤逻辑\n  const filteredGridData = useMemo(() => {\n    let data = gridData || [];\n\n    // 搜索过滤\n    if (debouncedSearchText.trim()) {\n      const searchLower = debouncedSearchText.toLowerCase();\n      data = data.filter(row => {\n        if (searchColumn === 'all') {\n          // 搜索所有列\n          return Object.values(row).some(value =>\n            value && value.toString().toLowerCase().includes(searchLower)\n          );\n        } else {\n          // 搜索特定列\n          const cellValue = row[searchColumn];\n          return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n        }\n      });\n    }\n\n    // 排序处理\n    if (sortConfig.field) {\n      data = [...data].sort((a, b) => {\n        // TOTAL行始终在最后\n        if (a.NO === 'TOTAL') return 1;\n        if (b.NO === 'TOTAL') return -1;\n\n        const aValue = a[sortConfig.field];\n        const bValue = b[sortConfig.field];\n\n        // 处理数字类型\n        if (typeof aValue === 'number' && typeof bValue === 'number') {\n          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n        }\n\n        // 处理字符串类型\n        const aStr = String(aValue || '').toLowerCase();\n        const bStr = String(bValue || '').toLowerCase();\n\n        if (sortConfig.direction === 'asc') {\n          return aStr.localeCompare(bStr);\n        } else {\n          return bStr.localeCompare(aStr);\n        }\n      });\n    }\n\n    return data;\n  }, [gridData, debouncedSearchText, searchColumn, sortConfig]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 处理单元格导航\n  const handleNavigateCell = useCallback((currentRowId, currentField, direction) => {\n    const editableColumns = columnOrder.filter(col =>\n      col.editable && col.field !== 'NO' && col.field !== 'ACTION' && col.field !== 'REMARKS'\n    );\n    const dataRows = memoGridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n    const currentRowIndex = dataRows.findIndex(row => row.id === currentRowId);\n    const currentColIndex = editableColumns.findIndex(col => col.field === currentField);\n\n    if (currentRowIndex === -1 || currentColIndex === -1) return;\n\n    let targetRowIndex = currentRowIndex;\n    let targetColIndex = currentColIndex;\n\n    switch (direction) {\n      case 'down':\n        targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n        break;\n      case 'up':\n        targetRowIndex = Math.max(currentRowIndex - 1, 0);\n        break;\n      case 'right':\n        // Tab键：如果已经是最后一列，跳到下一行的第一个可编辑列\n        if (currentColIndex >= editableColumns.length - 1) {\n          // 跳到下一行的第一列\n          targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n          targetColIndex = 0;\n        } else {\n          // 否则跳到同行的下一列\n          targetColIndex = currentColIndex + 1;\n        }\n        break;\n      case 'left':\n        // Shift+Tab：如果已经是第一列，跳到上一行的最后一个可编辑列\n        if (currentColIndex <= 0) {\n          // 跳到上一行的最后一列\n          targetRowIndex = Math.max(currentRowIndex - 1, 0);\n          targetColIndex = editableColumns.length - 1;\n        } else {\n          // 否则跳到同行的上一列\n          targetColIndex = currentColIndex - 1;\n        }\n        break;\n      default:\n        return;\n    }\n\n    const targetRow = dataRows[targetRowIndex];\n    const targetColumn = editableColumns[targetColIndex];\n\n    if (targetRow && targetColumn) {\n      // 触发目标单元格的编辑\n      setTimeout(() => {\n        const targetElement = document.querySelector(\n          `[data-row-id=\"${targetRow.id}\"][data-field=\"${targetColumn.field}\"]`\n        );\n        if (targetElement) {\n          targetElement.dispatchEvent(new Event('dblclick', { bubbles: true }));\n        }\n      }, 50);\n    }\n  }, [columnOrder, memoGridData]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 处理结果概览 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 数据搜索和操作选项 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={3}>\n            {/* 数据搜索 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SearchIcon sx={{ color: 'primary.main' }} />\n                数据搜索\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n                <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n                  <InputLabel>搜索范围</InputLabel>\n                  <Select\n                    value={searchColumn}\n                    label=\"搜索范围\"\n                    onChange={(e) => setSearchColumn(e.target.value)}\n                  >\n                    <MenuItem value=\"all\">全部列</MenuItem>\n                    <MenuItem value=\"NO\">NO</MenuItem>\n                    <MenuItem value=\"DATE\">DATE</MenuItem>\n                    <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                    <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                    <MenuItem value=\"KM\">KM</MenuItem>\n                    <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                    <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                    <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n                  </Select>\n                </FormControl>\n\n                <TextField\n                  size=\"small\"\n                  placeholder=\"输入搜索内容...\"\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  sx={{ flexGrow: 1, minWidth: 200 }}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    ),\n                    endAdornment: searchText && (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => setSearchText('')}\n                          edge=\"end\"\n                        >\n                          <ClearIcon />\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {searchText && (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n                  </Typography>\n                )}\n              </Stack>\n            </Grid>\n\n            {/* 操作选项 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SettingsIcon sx={{ color: 'primary.main' }} />\n                操作选项\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>\n                <Button\n                  variant=\"contained\"\n                  color=\"success\"\n                  startIcon={<DownloadIcon />}\n                  onClick={handleDownload}\n                >\n                  下载Excel\n                </Button>\n\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <TableViewIcon />}\n                  onClick={generateDocument}\n                  disabled={isGeneratingDocument}\n                >\n                  {isGeneratingDocument ? '生成中...' : '生成Excel文档'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<RestartAltIcon />}\n                  onClick={handleCleanup}\n                >\n                  重新开始\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n      \n      {/* 拖拽数据表格 */}\n      <DragDropContext onDragEnd={handleDragEnd}>\n        <Paper sx={{\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000,\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease',\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider',\n          },\n        }}>\n          <TableContainer>\n            <Table stickyHeader>\n              <TableHead>\n                <TableRow sx={{ height: 48 }}>\n                  {/* 拖拽手柄列 */}\n                  <TableCell sx={{\n                    width: 40,\n                    fontWeight: 'bold',\n                    height: 48,\n                    padding: '8px',\n                  }}>\n                    拖拽\n                  </TableCell>\n                  {columns.map((column) => (\n                    <TableCell\n                      key={column.field}\n                      sx={{\n                        fontWeight: 'bold',\n                        backgroundColor: 'background.default',\n                        borderRight: '2px solid',\n                        borderColor: 'divider',\n                        height: 48,\n                        padding: '8px',\n                        '&:last-child': {\n                          borderRight: 'none',\n                        },\n                      }}\n                    >\n                      {/* 可排序的列添加排序功能 */}\n                      {column.field !== 'ACTION' && column.field !== 'REMARKS' ? (\n                        <TableSortLabel\n                          active={sortConfig.field === column.field}\n                          direction={sortConfig.field === column.field ? sortConfig.direction : 'asc'}\n                          onClick={() => handleSort(column.field)}\n                          sx={{\n                            '& .MuiTableSortLabel-icon': {\n                              fontSize: '1rem',\n                            },\n                          }}\n                        >\n                          {column.headerName}\n                        </TableSortLabel>\n                      ) : (\n                        column.headerName\n                      )}\n                    </TableCell>\n                  ))}\n                </TableRow>\n              </TableHead>\n\n              <Droppable droppableId=\"table-body\">\n                {(provided, snapshot) => (\n                  <TableBody\n                    ref={provided.innerRef}\n                    {...provided.droppableProps}\n                    sx={{\n                      backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                      transition: 'background-color 0.2s ease',\n                      borderTop: '2px solid',\n                      borderColor: 'divider',\n                    }}\n                  >\n                    {/* 只显示当前页的数据 */}\n                    {memoGridData\n                      .filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                      .slice(\n                        paginationModel.page * paginationModel.pageSize,\n                        (paginationModel.page + 1) * paginationModel.pageSize\n                      )\n                      .map((row, index) => (\n                        <DraggableTableRow\n                          key={row.id}\n                          row={row}\n                          index={index}\n                          columns={columns}\n                          onCellEdit={handleCellEdit}\n                          onNavigateCell={handleNavigateCell}\n                        />\n                      ))}\n\n                    {/* TOTAL行单独显示在最后 */}\n                    {memoGridData.find(row => row.NO === 'TOTAL') && (\n                      <TableRow className=\"total-row\" sx={{\n                        backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                        fontWeight: 'bold',\n                        '& .MuiTableCell-root': {\n                          fontWeight: 'bold',\n                          borderTop: '2px solid',\n                          borderColor: 'primary.main',\n                        },\n                      }}>\n                        <TableCell sx={{ width: 40 }}>\n                          {/* TOTAL行不显示拖拽手柄 */}\n                        </TableCell>\n                        {columns.map((column) => {\n                          const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                          const value = totalRow ? totalRow[column.field] : '';\n                          return (\n                            <TableCell key={column.field} sx={{ padding: '8px' }}>\n                              {column.renderCell ? column.renderCell({ row: totalRow, value }) : value}\n                            </TableCell>\n                          );\n                        })}\n                      </TableRow>\n                    )}\n\n                    {provided.placeholder}\n                  </TableBody>\n                )}\n              </Droppable>\n            </Table>\n          </TableContainer>\n\n          {/* 分页控件 */}\n          <TablePagination\n            component=\"div\"\n            count={memoGridData.filter(row => row.NO !== 'TOTAL').length}\n            page={paginationModel.page}\n            onPageChange={(event, newPage) => {\n              setPaginationModel(prev => ({ ...prev, page: newPage }));\n            }}\n            rowsPerPage={paginationModel.pageSize}\n            onRowsPerPageChange={(event) => {\n              const newPageSize = parseInt(event.target.value, 10);\n              setPaginationModel({ page: 0, pageSize: newPageSize });\n              localStorage.setItem('dataGridPageSize', newPageSize.toString());\n            }}\n            rowsPerPageOptions={[25, 50, 100]}\n            labelRowsPerPage=\"每页行数:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}\n          />\n        </Paper>\n      </DragDropContext>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,cAAc,QACT,eAAe;AAEtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,eAAe,EAAEC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACzE,OAAOC,iBAAiB,MAAM,mCAAmC;;AAGjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,iBAAiB,gBAAAC,EAAA,cAAGzE,KAAK,CAAC0E,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,GAAG;EAAEC,KAAK;EAAEC,OAAO;EAAEC,UAAU;EAAEC;AAAe,CAAC,KAAK;EAAAP,EAAA;EAC5F,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMoF,eAAe,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACrD;IACA,MAAMC,MAAM,GAAGV,OAAO,CAACW,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,WAAW,CAAC;IAC7D,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACI,QAAQ,IAAIhB,GAAG,CAACiB,OAAO,IAAIjB,GAAG,CAACkB,QAAQ,IAAIR,WAAW,KAAK,IAAI,EAAE;MACtF;IACF;IAEAJ,cAAc,CAACI,WAAW,CAAC;IAC3BF,YAAY,CAACG,YAAY,IAAI,EAAE,CAAC;EAClC,CAAC;EAED,MAAMQ,cAAc,GAAIT,WAAW,IAAK;IACtC,IAAIP,UAAU,EAAE;MACdA,UAAU,CAACH,GAAG,CAACoB,EAAE,EAAEV,WAAW,EAAEH,SAAS,CAAC;IAC5C;IACAD,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACC,CAAC,EAAEZ,WAAW,KAAK;IACzC;IACA,IAAIY,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClB;MACAL,cAAc,CAACT,WAAW,CAAC;MAC3B;MACA,IAAIN,cAAc,IAAIkB,CAAC,CAACG,UAAU,KAAK,CAAC,EAAE;QACxC9B,UAAU,CAAC,MAAM;UACfS,cAAc,CAACJ,GAAG,CAACoB,EAAE,EAAEV,WAAW,EAAEY,CAAC,CAACI,QAAQ,GAAG,IAAI,GAAG,MAAM,CAAC;QACjE,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,MAAM,IAAIJ,CAAC,CAACC,GAAG,KAAK,KAAK,EAAE;MAC1BD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClBL,cAAc,CAACT,WAAW,CAAC;MAC3B;MACA,IAAIN,cAAc,EAAE;QAClBT,UAAU,CAAC,MAAM;UACfS,cAAc,CAACJ,GAAG,CAACoB,EAAE,EAAEV,WAAW,EAAEY,CAAC,CAACI,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;QACpE,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,MAAM,IAAIJ,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BjB,cAAc,CAAC,IAAI,CAAC;MACpBE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EAED,oBACEtB,OAAA,CAACH,SAAS;IAAC4C,WAAW,EAAE3B,GAAG,CAACoB,EAAE,CAACQ,QAAQ,CAAC,CAAE;IAAC3B,KAAK,EAAEA,KAAM;IAAA4B,QAAA,EACrDA,CAACC,QAAQ,EAAEC,QAAQ,kBAClB7C,OAAA,CAACxB,QAAQ;MACPsE,GAAG,EAAEF,QAAQ,CAACG,QAAS;MAAA,GACnBH,QAAQ,CAACI,cAAc;MAC3BC,EAAE,EAAE;QACFC,eAAe,EAAEL,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,SAAS;QACjEC,SAAS,EAAEP,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,MAAM;QACxDE,SAAS,EAAER,QAAQ,CAACM,UAAU,GAAG,CAAC,GAAG,CAAC;QACtCG,UAAU,EAAET,QAAQ,CAACM,UAAU,GAAG,MAAM,GAAG,eAAe;QAC1D,eAAe,EAAE;UACfD,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;QACD,IAAIX,QAAQ,CAACM,UAAU,IAAI;UACzBM,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAI3C,GAAG,CAACkB,QAAQ,IAAI;UAClBkB,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;MACH,CAAE;MAAAb,QAAA,gBAGF3C,OAAA,CAAC3B,SAAS;QAAA,GACJuE,QAAQ,CAACc,eAAe;QAC5BT,EAAE,EAAE;UACFU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YAAEA,MAAM,EAAE;UAAW,CAAC;UAClCC,SAAS,EAAE;QACb,CAAE;QAAAlB,QAAA,eAEF3C,OAAA,CAACF,iBAAiB;UAACmD,EAAE,EAAE;YAAEM,KAAK,EAAE;UAAiB;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAGXjD,OAAO,CAACkD,GAAG,CAAExC,MAAM,IAAK;QACvB,MAAMyC,KAAK,GAAGrD,GAAG,CAACY,MAAM,CAACG,KAAK,CAAC;QAC/B,MAAMuC,SAAS,GAAGjD,WAAW,KAAKO,MAAM,CAACG,KAAK;QAE9C,oBACE7B,OAAA,CAAC3B,SAAS;UAER,eAAayC,GAAG,CAACoB,EAAG;UACpB,cAAYR,MAAM,CAACG,KAAM;UACzBoB,EAAE,EAAE;YACFoB,OAAO,EAAE,KAAK;YACdT,MAAM,EAAElC,MAAM,CAACI,QAAQ,IAAI,CAAChB,GAAG,CAACiB,OAAO,IAAI,CAACjB,GAAG,CAACkB,QAAQ,IAAIN,MAAM,CAACG,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG;UAClG;UACA;UAAA;UACAyC,aAAa,EAAEA,CAAA,KAAM/C,eAAe,CAACG,MAAM,CAACG,KAAK,EAAEsC,KAAK,CAAE;UAAAxB,QAAA,EAEzDyB,SAAS,gBACRpE,OAAA,CAAC7C,SAAS;YACRoH,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAE9C,SAAU;YACjBmD,QAAQ,EAAGpC,CAAC,IAAKd,YAAY,CAACc,CAAC,CAACqC,MAAM,CAACN,KAAK,CAAE;YAC9CO,SAAS,EAAGtC,CAAC,IAAKD,cAAc,CAACC,CAAC,EAAEV,MAAM,CAACG,KAAK,CAAE;YAClD8C,MAAM,EAAEA,CAAA,KAAM1C,cAAc,CAACP,MAAM,CAACG,KAAK,CAAE;YAC3C+C,SAAS;YACTC,SAAS;YACTC,OAAO,EAAC,UAAU;YAClB7B,EAAE,EAAE;cACF,kBAAkB,EAAE;gBAClB8B,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE;kBACVC,YAAY,EAAE;gBAChB,CAAC;gBACD,gBAAgB,EAAE;kBAChBA,YAAY,EAAE;gBAChB,CAAC;gBACD,SAAS,EAAE;kBACTA,YAAY,EAAE;gBAChB;cACF,CAAC;cACD,mBAAmB,EAAE;gBACnBX,OAAO,EAAE,OAAO;gBAChBU,QAAQ,EAAE,SAAS;gBACnBE,UAAU,EAAE;cACd;YACF;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAEFvC,MAAM,CAACwD,UAAU,GAAGxD,MAAM,CAACwD,UAAU,CAAC;YAAEpE,GAAG;YAAEqD;UAAM,CAAC,CAAC,GAAGA;QACzD,GA1CIzC,MAAM,CAACG,KAAK;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CR,CAAC;MAEhB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC,kCAAC;;AAEF;AAAAkB,GAAA,GApJMzE,iBAAiB;AAqJvB,MAAM0E,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAID;AACA,MAAMC,UAAU,gBAAAC,GAAA,cAAGpJ,KAAK,CAAC0E,IAAI,CAAA2E,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAL,GAAA;EACtE;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG1J,QAAQ,CAAC;IAErCsJ,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACAtJ,SAAS,CAAC,MAAM;IACdyJ,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAI1D,CAAC,IAAK;IACzBuD,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACExF,OAAA,CAACtD,MAAM;IAELiJ,OAAO,EAAEG,WAAY;IACrBhB,OAAO,EAAEc,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDnC,KAAK,EAAC,SAAS;IACfgB,IAAI,EAAC,OAAO;IACZtB,EAAE,EAAE;MACF8C,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBjB,QAAQ,EAAE,SAAS;MACnBkB,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpB9C,UAAU,EAAE,sBAAsB;MAClC+C,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAA3D,QAAA,EAEDiD,OAAO,CAACH,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;IAAA5B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAACsC,GAAA,GA5CGlB,UAAU;AA8ChB,MAAMmB,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAGzK,OAAO,CAAC,MAAM,CAChC;IAAEsF,KAAK,EAAE,IAAI;IAAEoF,UAAU,EAAE,IAAI;IAAEnF,QAAQ,EAAE,KAAK;IAAEoF,WAAW,EAAE;EAAO,CAAC,EACvE;IAAErF,KAAK,EAAE,MAAM;IAAEoF,UAAU,EAAE,MAAM;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAErF,KAAK,EAAE,YAAY;IAAEoF,UAAU,EAAE,YAAY;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EACtF;IAAErF,KAAK,EAAE,OAAO;IAAEoF,UAAU,EAAE,OAAO;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAErF,KAAK,EAAE,IAAI;IAAEoF,UAAU,EAAE,IAAI;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EACtE;IAAErF,KAAK,EAAE,SAAS;IAAEoF,UAAU,EAAE,SAAS;IAAEnF,QAAQ,EAAE,KAAK;IAAEoF,WAAW,EAAE;EAAO,CAAC,EACjF;IAAErF,KAAK,EAAE,UAAU;IAAEoF,UAAU,EAAE,OAAO;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAErF,KAAK,EAAE,YAAY;IAAEoF,UAAU,EAAE,QAAQ;IAAEnF,QAAQ,EAAE,IAAI;IAAEoF,WAAW,EAAE;EAAO,CAAC,EAClF;IAAErF,KAAK,EAAE,QAAQ;IAAEoF,UAAU,EAAE,QAAQ;IAAEnF,QAAQ,EAAE,KAAK;IAAEoF,WAAW,EAAE;EAAO,CAAC,CAChF,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjL,QAAQ,CAAC,MAAM;IACzD,MAAMkL,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGjC,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGxL,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACyL,eAAe,EAAEC,kBAAkB,CAAC,GAAG1L,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2L,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5L,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM6L,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMC,eAAe,GAAGT,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACnF,MAAMW,iBAAiB,GAAGD,eAAe,CAACC,iBAAiB;MAE3D,IAAIA,iBAAiB,EAAE;QACrB;QACA,MAAMC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAAC,eAAe,CAAC;QACtD,IAAIA,KAAK,EAAE;UACT,MAAM,GAAGC,SAAS,EAAEC,IAAI,CAAC,GAAGF,KAAK;UACjC,MAAMG,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAClD,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YACpD,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAChD,CAAC;UACD,MAAMC,WAAW,GAAGD,QAAQ,CAACF,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC;UACrD,IAAID,WAAW,EAAE;YACf,OAAO,GAAGF,IAAI,IAAIE,WAAW,EAAE;UACjC;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;;IAEA;IACA,MAAME,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,OAAO,GAAGD,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9E,CAAC;EAED,MAAMC,aAAa,GAAGjB,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGhN,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiN,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlN,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACmN,YAAY,EAAEC,eAAe,CAAC,GAAGpN,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACqN,UAAU,EAAEC,aAAa,CAAC,GAAGtN,QAAQ,CAAC;IAAE0F,KAAK,EAAE,IAAI;IAAE6H,SAAS,EAAE;EAAM,CAAC,CAAC;;EAE/E;EACA,MAAMC,UAAU,GAAI9H,KAAK,IAAK;IAC5B,MAAM+H,KAAK,GAAGJ,UAAU,CAAC3H,KAAK,KAAKA,KAAK,IAAI2H,UAAU,CAACE,SAAS,KAAK,KAAK;IAC1E,MAAMG,YAAY,GAAGD,KAAK,GAAG,MAAM,GAAG,KAAK;IAC3CH,aAAa,CAAC;MAAE5H,KAAK;MAAE6H,SAAS,EAAEG;IAAa,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAGzN,WAAW,CAC7C4D,QAAQ,CAAEwG,IAAI,IAAK;IACjB,IAAI;MACFa,YAAY,CAACyC,OAAO,CAAC,eAAe,EAAEvC,IAAI,CAACwC,SAAS,CAACvD,IAAI,CAAC,CAAC;MAC3DiC,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAExD,IAAI,CAACyD,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAM0B,qBAAqB,GAAG9N,WAAW,CACvC4D,QAAQ,CAAEwG,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvBiC,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACnD,YAAY,CACf,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAG/N,WAAW,CAAEoK,IAAI,IAAK;IAC7C,MAAM4D,QAAQ,GAAG5D,IAAI,CAAC9E,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC;IACrD,IAAID,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG9D,IAAI,CAClB+D,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,CAAC,CAClDyI,MAAM,CAAC,CAACC,GAAG,EAAE5J,GAAG,KAAK4J,GAAG,IAAIC,MAAM,CAAC7J,GAAG,CAAC8J,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DP,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAO9D,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoE,aAAa,GAAGxO,WAAW,CAAEyO,MAAM,IAAK;IAC5C,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;MACvB;IACF;IAEA,MAAMC,WAAW,GAAGF,MAAM,CAACG,MAAM,CAAClK,KAAK;IACvC,MAAMmK,gBAAgB,GAAGJ,MAAM,CAACC,WAAW,CAAChK,KAAK;IAEjD,IAAIiK,WAAW,KAAKE,gBAAgB,EAAE;MACpC;IACF;IAEAC,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;;MAEzB;MACA,MAAME,YAAY,GAAGD,OAAO,CAACb,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC;MAC9D,MAAMD,QAAQ,GAAGgB,OAAO,CAAC1J,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC;;MAExD;MACA,MAAM,CAACiB,OAAO,CAAC,GAAGD,YAAY,CAACE,MAAM,CAACR,WAAW,EAAE,CAAC,CAAC;MACrDM,YAAY,CAACE,MAAM,CAACN,gBAAgB,EAAE,CAAC,EAAEK,OAAO,CAAC;;MAEjD;MACAD,YAAY,CAACG,OAAO,CAAC,CAAC3K,GAAG,EAAEC,KAAK,KAAK;QACnC,IAAI,OAAOD,GAAG,CAACwJ,EAAE,KAAK,QAAQ,EAAE;UAC9BxJ,GAAG,CAACwJ,EAAE,GAAGvJ,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM2K,aAAa,GAAGrB,QAAQ,GAAG,CAAC,GAAGiB,YAAY,EAAEjB,QAAQ,CAAC,GAAGiB,YAAY;MAE3E5C,OAAO,CAACuB,GAAG,CAAC,UAAU,CAAC;MACvB,OAAOG,gBAAgB,CAACsB,aAAa,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMuB,cAAc,GAAGtP,WAAW,CAAC,CAACmJ,KAAK,EAAE3D,KAAK,EAAE+J,QAAQ,KAAK;IAC7DT,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMS,WAAW,GAAGT,IAAI,CAAClH,GAAG,CAACpD,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACoB,EAAE,KAAKsD,KAAK,EAAE;UACpB,MAAMsG,UAAU,GAAG;YAAE,GAAGhL,GAAG;YAAE,CAACe,KAAK,GAAG+J;UAAS,CAAC;;UAEhD;UACA,MAAMG,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;UACrG,IAAIA,aAAa,CAACC,QAAQ,CAACnK,KAAK,CAAC,IAAI+J,QAAQ,KAAKK,SAAS,IAAIL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;YACnG,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAChC,MAAMM,QAAQ,GAAGvB,MAAM,CAACiB,QAAQ,CAAC;cACjC,IAAI,CAACO,KAAK,CAACD,QAAQ,CAAC,EAAE;gBACpBJ,UAAU,CAACjK,KAAK,CAAC,GAAGqK,QAAQ;cAC9B;YACF;UACF;UAEA,OAAOJ,UAAU;QACnB;QACA,OAAOhL,GAAG;MACZ,CAAC,CAAC;MAEF,MAAMgK,MAAM,GAAGV,gBAAgB,CAACyB,WAAW,CAAC;;MAE5C;MACA/B,2BAA2B,CAACgB,MAAM,CAAC;MACnCX,qBAAqB,CAACW,MAAM,CAAC;MAE7B,OAAOA,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlQ,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMmQ,aAAa,GAAG,CAAC7F,IAAI,IAAI,EAAE,EAAEvC,GAAG,CAACpD,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEyL,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAExK,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACyK,QAAQ,EAAEtB,WAAW,CAAC,GAAGhP,QAAQ,CAAC,MAAM;IAC7CuM,OAAO,CAACuB,GAAG,CAAC,mCAAmC,EAC7CpD,aAAa,GAAG,IAAIA,aAAa,CAACqD,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIrD,aAAa,IAAIA,aAAa,CAACqD,MAAM,GAAG,CAAC,EAAE;MAC7CxB,OAAO,CAACuB,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMyC,aAAa,GAAG7F,aAAa,CAAC3C,GAAG,CAACpD,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACkB,QAAQ,KAAKiK,SAAS,EAAE;UAC9BnL,GAAG,CAACkB,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIlB,GAAG,CAAC0L,iBAAiB,KAAKP,SAAS,EAAE;UACvCnL,GAAG,CAAC0L,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAO1L,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM6L,gBAAgB,GAAGL,aAAa,CAACpI,GAAG,CAAC,CAACpD,GAAG,EAAEC,KAAK,MAAM;QAC1D,GAAGD,GAAG;QACNoB,EAAE,EAAEnB,KAAK;QACTgB,OAAO,EAAEjB,GAAG,CAACwJ,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACH+B,eAAe,CAAC,CAAC,GAAGM,gBAAgB,CAAC,CAAC;MAEtC,OAAOD,aAAa;IACtB;;IAEA;IACAhE,OAAO,CAACuB,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAM0C,gBAAgB,GAAGL,aAAa,CAACpI,GAAG,CAAC,CAACpD,GAAG,EAAEC,KAAK,MAAM;MAC1D,GAAGD,GAAG;MACNoB,EAAE,EAAEnB,KAAK;MACTgB,OAAO,EAAEjB,GAAG,CAACwJ,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACH+B,eAAe,CAAC,CAAC,GAAGM,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAGrQ,OAAO,CAAC,MAAM;IACrC,IAAIkK,IAAI,GAAGgG,QAAQ,IAAI,EAAE;;IAEzB;IACA,IAAIrD,mBAAmB,CAACyD,IAAI,CAAC,CAAC,EAAE;MAC9B,MAAMC,WAAW,GAAG1D,mBAAmB,CAAC2D,WAAW,CAAC,CAAC;MACrDtG,IAAI,GAAGA,IAAI,CAAC+D,MAAM,CAAC1J,GAAG,IAAI;QACxB,IAAIwI,YAAY,KAAK,KAAK,EAAE;UAC1B;UACA,OAAO0D,MAAM,CAACC,MAAM,CAACnM,GAAG,CAAC,CAACoM,IAAI,CAAC/I,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACzB,QAAQ,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,CAACf,QAAQ,CAACc,WAAW,CAC9D,CAAC;QACH,CAAC,MAAM;UACL;UACA,MAAMK,SAAS,GAAGrM,GAAG,CAACwI,YAAY,CAAC;UACnC,OAAO6D,SAAS,IAAIA,SAAS,CAACzK,QAAQ,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,CAACf,QAAQ,CAACc,WAAW,CAAC;QAC9E;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAItD,UAAU,CAAC3H,KAAK,EAAE;MACpB4E,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC,CAAC2G,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,IAAID,CAAC,CAAC/C,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC;QAC9B,IAAIgD,CAAC,CAAChD,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;QAE/B,MAAMiD,MAAM,GAAGF,CAAC,CAAC7D,UAAU,CAAC3H,KAAK,CAAC;QAClC,MAAM2L,MAAM,GAAGF,CAAC,CAAC9D,UAAU,CAAC3H,KAAK,CAAC;;QAElC;QACA,IAAI,OAAO0L,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;UAC5D,OAAOhE,UAAU,CAACE,SAAS,KAAK,KAAK,GAAG6D,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;QAC3E;;QAEA;QACA,MAAME,IAAI,GAAG3E,MAAM,CAACyE,MAAM,IAAI,EAAE,CAAC,CAACR,WAAW,CAAC,CAAC;QAC/C,MAAMW,IAAI,GAAG5E,MAAM,CAAC0E,MAAM,IAAI,EAAE,CAAC,CAACT,WAAW,CAAC,CAAC;QAE/C,IAAIvD,UAAU,CAACE,SAAS,KAAK,KAAK,EAAE;UAClC,OAAO+D,IAAI,CAACE,aAAa,CAACD,IAAI,CAAC;QACjC,CAAC,MAAM;UACL,OAAOA,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC;QACjC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOhH,IAAI;EACb,CAAC,EAAE,CAACgG,QAAQ,EAAErD,mBAAmB,EAAEE,YAAY,EAAEE,UAAU,CAAC,CAAC;;EAE7D;EACA,MAAMoE,YAAY,GAAGrR,OAAO,CAAC,MAAMqQ,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,MAAMiB,kBAAkB,GAAGxR,WAAW,CAAC,CAACyR,YAAY,EAAEC,YAAY,EAAErE,SAAS,KAAK;IAChF,MAAMsE,eAAe,GAAGhH,WAAW,CAACwD,MAAM,CAAC5I,GAAG,IAC5CA,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACC,KAAK,KAAK,IAAI,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,SAChF,CAAC;IACD,MAAMoM,QAAQ,GAAGL,YAAY,CAACpD,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,CAAC;IAEhF,MAAMkM,eAAe,GAAGD,QAAQ,CAACE,SAAS,CAACrN,GAAG,IAAIA,GAAG,CAACoB,EAAE,KAAK4L,YAAY,CAAC;IAC1E,MAAMM,eAAe,GAAGJ,eAAe,CAACG,SAAS,CAACvM,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKkM,YAAY,CAAC;IAEpF,IAAIG,eAAe,KAAK,CAAC,CAAC,IAAIE,eAAe,KAAK,CAAC,CAAC,EAAE;IAEtD,IAAIC,cAAc,GAAGH,eAAe;IACpC,IAAII,cAAc,GAAGF,eAAe;IAEpC,QAAQ1E,SAAS;MACf,KAAK,MAAM;QACT2E,cAAc,GAAGE,IAAI,CAACC,GAAG,CAACN,eAAe,GAAG,CAAC,EAAED,QAAQ,CAAC/D,MAAM,GAAG,CAAC,CAAC;QACnE;MACF,KAAK,IAAI;QACPmE,cAAc,GAAGE,IAAI,CAACE,GAAG,CAACP,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;QACjD;MACF,KAAK,OAAO;QACV;QACA,IAAIE,eAAe,IAAIJ,eAAe,CAAC9D,MAAM,GAAG,CAAC,EAAE;UACjD;UACAmE,cAAc,GAAGE,IAAI,CAACC,GAAG,CAACN,eAAe,GAAG,CAAC,EAAED,QAAQ,CAAC/D,MAAM,GAAG,CAAC,CAAC;UACnEoE,cAAc,GAAG,CAAC;QACpB,CAAC,MAAM;UACL;UACAA,cAAc,GAAGF,eAAe,GAAG,CAAC;QACtC;QACA;MACF,KAAK,MAAM;QACT;QACA,IAAIA,eAAe,IAAI,CAAC,EAAE;UACxB;UACAC,cAAc,GAAGE,IAAI,CAACE,GAAG,CAACP,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;UACjDI,cAAc,GAAGN,eAAe,CAAC9D,MAAM,GAAG,CAAC;QAC7C,CAAC,MAAM;UACL;UACAoE,cAAc,GAAGF,eAAe,GAAG,CAAC;QACtC;QACA;MACF;QACE;IACJ;IAEA,MAAMM,SAAS,GAAGT,QAAQ,CAACI,cAAc,CAAC;IAC1C,MAAMM,YAAY,GAAGX,eAAe,CAACM,cAAc,CAAC;IAEpD,IAAII,SAAS,IAAIC,YAAY,EAAE;MAC7B;MACAlO,UAAU,CAAC,MAAM;QACf,MAAMmO,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiBJ,SAAS,CAACxM,EAAE,kBAAkByM,YAAY,CAAC9M,KAAK,IACnE,CAAC;QACD,IAAI+M,aAAa,EAAE;UACjBA,aAAa,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,EAAE;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAC,CAAC;QACvE;MACF,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC,EAAE,CAACjI,WAAW,EAAE4G,YAAY,CAAC,CAAC;;EAE/B;EACAxR,SAAS,CAAC,MAAM;IACd,MAAM8S,KAAK,GAAGzO,UAAU,CAAC,MAAM;MAC7B4I,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM1I,YAAY,CAAC0O,KAAK,CAAC;EAClC,CAAC,EAAE,CAAChG,UAAU,CAAC,CAAC;;EAEhB;EACA9M,SAAS,CAAC,MAAM;IACd+S,kBAAkB,CAAC/D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEgE,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAAChG,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGnT,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoT,UAAU,EAAEC,aAAa,CAAC,GAAGrT,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACsT,eAAe,EAAEN,kBAAkB,CAAC,GAAGhT,QAAQ,CAAC,MAAM;IAC3D,MAAMuT,KAAK,GAAGpI,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACL6H,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACAtT,SAAS,CAAC,MAAM;IACdkL,YAAY,CAACyC,OAAO,CAAC,kBAAkB,EAAE0F,eAAe,CAACE,QAAQ,CAACjN,QAAQ,CAAC,CAAC,CAAC;IAC7EgG,OAAO,CAACuB,GAAG,CAAC,UAAU,EAAEwF,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAE9B;EACAvT,SAAS,CAAC,MAAM;IACdkL,YAAY,CAACyC,OAAO,CAAC,gBAAgB,EAAEvC,IAAI,CAACwC,SAAS,CAAC7C,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM0I,mBAAmB,GAAGvT,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwT,iBAAiB,GAAGxT,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMyT,gBAAgB,GAAGzT,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM0T,UAAU,GAAIvJ,IAAI,IAAKA,IAAI,CAACvC,GAAG,CAACpD,GAAG,KAAK;IAC5CoB,EAAE,EAAEpB,GAAG,CAACoB,EAAE;IACVoI,EAAE,EAAExJ,GAAG,CAACwJ,EAAE;IACVtI,QAAQ,EAAElB,GAAG,CAACkB,QAAQ;IACtBuK,OAAO,EAAEzL,GAAG,CAACyL,OAAO;IACpBC,iBAAiB,EAAE1L,GAAG,CAAC0L,iBAAiB;IACxC5B,UAAU,EAAE9J,GAAG,CAAC8J;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAxO,SAAS,CAAC,MAAM;IACd,IAAI0K,YAAY,IAAI2F,QAAQ,CAACvC,MAAM,GAAG,CAAC,EAAE;MACvC,MAAM+F,OAAO,GAAGzI,IAAI,CAACwC,SAAS,CAACgG,UAAU,CAACvD,QAAQ,CAAC,CAAC;MACpD,MAAMyD,WAAW,GAAGL,mBAAmB,CAACM,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIF,gBAAgB,CAACI,OAAO,EAAE;UAC5B3P,YAAY,CAACuP,gBAAgB,CAACI,OAAO,CAAC;QACxC;QACAJ,gBAAgB,CAACI,OAAO,GAAG1P,UAAU,CAAC,MAAM;UAC1CoP,mBAAmB,CAACM,OAAO,GAAGF,OAAO;UACrCH,iBAAiB,CAACK,OAAO,GAAGvH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC7B,YAAY,CAAC,CAAC,GAAG2F,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IACA,OAAO,MAAM;MACX,IAAIsD,gBAAgB,CAACI,OAAO,EAAE;QAC5B3P,YAAY,CAACuP,gBAAgB,CAACI,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAAC1D,QAAQ,EAAE3F,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACsJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGlU,QAAQ,CAAC;IACjDmU,IAAI,EAAE,KAAK;IACX9K,KAAK,EAAE,IAAI;IACX/D,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACArF,SAAS,CAAC,MAAM;IACd,IAAIgQ,YAAY,CAAClC,MAAM,KAAK,CAAC,IAAIuC,QAAQ,CAACvC,MAAM,GAAG,CAAC,EAAE;MACpDxB,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAEwC,QAAQ,CAACvC,MAAM,CAAC;MACpDmC,eAAe,CAAC,CAAC,GAAGI,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEL,YAAY,CAAC,CAAC;EAI5B,MAAMmE,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI7J,MAAM,IAAIA,MAAM,CAAC8J,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C5J,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM6J,WAAW,GAAG,GAAGhR,OAAO,aAAaiH,MAAM,EAAE;MACnD,MAAMgK,IAAI,GAAG7B,QAAQ,CAAC8B,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGH,WAAW;MACvBC,IAAI,CAACG,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIjI,IAAI,CAAC,CAAC,CAACkI,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EjC,QAAQ,CAACkC,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZpC,QAAQ,CAACkC,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7B,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMuK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM3R,KAAK,CAAC4R,MAAM,CAAC,GAAG3R,OAAO,YAAYiH,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9B,OAAO,CAAC,CAAC;EACX,CAAC;;EAID;EACA,MAAM0K,kBAAkB,GAAGhV,WAAW,CAAC,CAACoK,IAAI,EAAE6K,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAG9K,IAAI,IAAIgG,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC+E,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACb/G,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,CAAC,CAClDyI,MAAM,CAAC,CAACC,GAAG,EAAE5J,GAAG,KAAK;MACpB,IAAIwQ,UAAU,IAAIxQ,GAAG,CAACoB,EAAE,KAAKoP,UAAU,CAACpP,EAAE,EAAE;QAC1C,OAAOwI,GAAG,IAAIC,MAAM,CAAC2G,UAAU,CAAC1G,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAOF,GAAG,IAAIC,MAAM,CAAC7J,GAAG,CAAC8J,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAAC6B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiF,iBAAiB,GAAGxV,KAAK,CAACG,WAAW,CAAC,CAACmJ,KAAK,EAAE/D,YAAY,KAAK;IACnE;IACA4O,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV9K,KAAK;MACL/D;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkQ,kBAAkB,GAAGtV,WAAW,CAAC,MAAM;IAC3CgU,gBAAgB,CAACjF,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPkF,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAhB,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoC,kBAAkB,GAAGvV,WAAW,CAAEwV,MAAM,IAAK;IACjD,MAAM;MAAErM;IAAM,CAAC,GAAG4K,aAAa;IAC/B,IAAI5K,KAAK,KAAK,IAAI,EAAE;MAClB;MACAmM,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC5G,WAAW,CAAC6G,QAAQ,IAAI;UACtB,IAAInG,WAAW,GAAGmG,QAAQ,CAAC9N,GAAG,CAACpD,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACoB,EAAE,KAAKsD,KAAK,EAAE;cACpB,IAAIqM,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAG/Q,GAAG;kBAAEyL,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIyF,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAI7C,aAAa,EAAE;kBACjB6C,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAI5C,UAAU,EAAE;kBACd2C,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAChI,MAAM,GAAG,CAAC,EAAE;kBACvB+H,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAGtR,GAAG;kBAAEyL,OAAO,EAAE0F,WAAW;kBAAEzF,iBAAiB,EAAEyF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOnR,GAAG;UACZ,CAAC,CAAC;UAEF+K,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACApL,UAAU,CAAC,MAAM;UACfiI,OAAO,CAACuB,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACmG,aAAa,EAAEuB,kBAAkB,EAAEvH,gBAAgB,EAAEiF,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAM8C,mBAAmB,GAAGhW,WAAW,CAAC,MAAM;IAC5CwL,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyK,oBAAoB,GAAGjW,WAAW,CAAC,MAAM;IAC7CwL,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4K,YAAY,GAAGlW,WAAW,CAAC,MAAM;IACrC,IAAIqL,SAAS,CAACmF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC1F,cAAc,CAAC6E,QAAQ,CAACtE,SAAS,CAACmF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEzF,iBAAiB,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE1D,SAAS,CAACmF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDnE,OAAO,CAACuB,GAAG,CAAC,QAAQ,CAAC;MACrBqI,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAInL,cAAc,CAAC6E,QAAQ,CAACtE,SAAS,CAACmF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDnE,OAAO,CAACuB,GAAG,CAAC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACvC,SAAS,EAAEP,cAAc,EAAEmL,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAME,YAAY,GAAGnW,WAAW,CAAEwV,MAAM,IAAK;IAC3CzK,iBAAiB,CAACgE,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAACiI,IAAI,IAAIA,IAAI,KAAKZ,MAAM,CAAC,CAAC;IAC/DnJ,OAAO,CAACuB,GAAG,CAAC,OAAO,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyI,eAAe,GAAGrW,WAAW,CAAE6F,EAAE,IAAK;IAC1CiJ,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAAClH,GAAG,CAACpD,GAAG,IAAIA,GAAG,CAACoB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGpB,GAAG;QAAEkB,QAAQ,EAAE;MAAK,CAAC,GAAGlB,GAAG,CAAC;;MAEnF;MACA,IAAI6R,SAAS,GAAG,CAAC;MACjB9G,WAAW,CAACJ,OAAO,CAAC3K,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,IAAI,OAAOlB,GAAG,CAACwJ,EAAE,KAAK,QAAQ,EAAE;UACrExJ,GAAG,CAACwJ,EAAE,GAAGqI,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF9G,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFnD,OAAO,CAACuB,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMwI,aAAa,GAAGvW,WAAW,CAAE6F,EAAE,IAAK;IACxCiJ,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAAClH,GAAG,CAACpD,GAAG,IAAIA,GAAG,CAACoB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGpB,GAAG;QAAEkB,QAAQ,EAAE;MAAM,CAAC,GAAGlB,GAAG,CAAC;;MAEpF;MACA,IAAI6R,SAAS,GAAG,CAAC;MACjB9G,WAAW,CAACJ,OAAO,CAAC3K,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,IAAI,OAAOlB,GAAG,CAACwJ,EAAE,KAAK,QAAQ,EAAE;UACrExJ,GAAG,CAACwJ,EAAE,GAAGqI,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF9G,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFpL,UAAU,CAAC,MAAM;MACfiI,OAAO,CAACuB,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMyI,eAAe,GAAGxW,WAAW,CAAE6F,EAAE,IAAK;IAC1CiJ,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAM0H,YAAY,GAAG1H,IAAI,CAACZ,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACoB,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAIyQ,SAAS,GAAG,CAAC;MACjBG,YAAY,CAACrH,OAAO,CAAC3K,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,IAAI,OAAOlB,GAAG,CAACwJ,EAAE,KAAK,QAAQ,EAAE;UACrExJ,GAAG,CAACwJ,EAAE,GAAGqI,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM9G,WAAW,GAAGzB,gBAAgB,CAAC0I,YAAY,CAAC;;MAElD;MACAhJ,2BAA2B,CAAC+B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElCnD,OAAO,CAACuB,GAAG,CAAC,QAAQ,CAAC;MACrB,OAAO4B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAM4I,YAAY,GAAG1W,WAAW,CAAE2W,UAAU,IAAK;IAC/C7H,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAM6H,WAAW,GAAG7H,IAAI,CAAC+C,SAAS,CAACrN,GAAG,IAAIA,GAAG,CAACoB,EAAE,KAAK8Q,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO7H,IAAI;;MAEnC;MACA,MAAM8H,KAAK,GAAGtK,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAMwK,UAAU,GAAG/H,IAAI,CAAC6H,WAAW,CAAC;MACpC,MAAMG,QAAQ,GAAG,OAAOD,UAAU,CAAC7I,EAAE,KAAK,QAAQ,GAAG6I,UAAU,CAAC7I,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM+I,MAAM,GAAG;QACbnR,EAAE,EAAEgR,KAAK;QACT5I,EAAE,EAAE8I,QAAQ;QACZE,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNhH,OAAO,EAAE,EAAE;QACXiH,QAAQ,EAAE,EAAE;QACZ5I,UAAU,EAAE,CAAC;QACb4B,iBAAiB,EAAE,EAAE;QACrBxK,QAAQ,EAAE,KAAK;QACfD,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAMsJ,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;MACzBC,OAAO,CAACG,MAAM,CAACyH,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEI,MAAM,CAAC;;MAE1C;MACA,IAAIV,SAAS,GAAG,CAAC;MACjBtH,OAAO,CAACI,OAAO,CAAC3K,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,IAAI,OAAOlB,GAAG,CAACwJ,EAAE,KAAK,QAAQ,EAAE;UACrExJ,GAAG,CAACwJ,EAAE,GAAGqI,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM9G,WAAW,GAAGzB,gBAAgB,CAACiB,OAAO,CAAC;;MAE7C;MACAvB,2BAA2B,CAAC+B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElCnD,OAAO,CAACuB,GAAG,CAAC,OAAO,CAAC;MACpB,OAAO4B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMsJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF1L,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM2L,YAAY,GAAG,CAACjH,QAAQ,IAAI,EAAE,EACjCjC,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,CAAC;;MAErD;MACA0R,YAAY,CAACtG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC/C,EAAE,KAAK,QAAQ,IAAI,OAAOgD,CAAC,CAAChD,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO+C,CAAC,CAAC/C,EAAE,GAAGgD,CAAC,CAAChD,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMqJ,OAAO,GAAGD,YAAY,CAACxP,GAAG,CAAC,CAACpD,GAAG,EAAEC,KAAK,MAAM;QAChD;QACAuJ,EAAE,EAAEvJ,KAAK,GAAG,CAAC;QACbuS,IAAI,EAAExS,GAAG,CAACwS,IAAI,GAAI,OAAOxS,GAAG,CAACwS,IAAI,KAAK,QAAQ,IAAIxS,GAAG,CAACwS,IAAI,CAACtH,QAAQ,CAAC,GAAG,CAAC,GAAGlL,GAAG,CAACwS,IAAI,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9S,GAAG,CAACwS,IAAI,GAAI,EAAE;QAClH,YAAY,EAAExS,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGyN,IAAI,CAACsF,KAAK,CAAC/S,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFyS,EAAE,EAAE,OAAOzS,GAAG,CAACyS,EAAE,KAAK,QAAQ,GAAGhF,IAAI,CAACsF,KAAK,CAAC/S,GAAG,CAACyS,EAAE,CAAC,GAAGzS,GAAG,CAACyS,EAAE,IAAI,EAAE;QAClEhH,OAAO,EAAGzL,GAAG,CAAC0L,iBAAiB,IAAI1L,GAAG,CAAC0L,iBAAiB,KAAK,MAAM,GAAI1L,GAAG,CAAC0L,iBAAiB,GAAG,EAAE;QACjGsH,KAAK,EAAE,OAAOhT,GAAG,CAAC0S,QAAQ,KAAK,QAAQ,GACpC1S,GAAG,CAAC0S,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG1S,GAAG,CAAC0S,QAAQ,CAACO,OAAO,CAAC,CAAC,CAAC,GAAGjT,GAAG,CAAC0S,QAAQ,CAACO,OAAO,CAAC,CAAC,CAAC,GAC3EjT,GAAG,CAAC0S,QAAQ,IAAI,EAAE;QACpBQ,MAAM,EAAE,OAAOlT,GAAG,CAAC8J,UAAU,KAAK,QAAQ,GAAG9J,GAAG,CAAC8J,UAAU,CAACmJ,OAAO,CAAC,CAAC,CAAC,GAAGjT,GAAG,CAAC8J,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMqJ,WAAW,GAAG,CAACxH,QAAQ,IAAI,EAAE,EAChCjC,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI,CAACxJ,GAAG,CAACkB,QAAQ,IAAIlB,GAAG,CAAC8J,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAE5J,GAAG,KAAK4J,GAAG,IAAI,OAAO5J,GAAG,CAAC8J,UAAU,KAAK,QAAQ,GAAG9J,GAAG,CAAC8J,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMsJ,YAAY,GAAIxN,MAAM,IAAIA,MAAM,CAAC8J,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG9J,MAAM;MAE5F,MAAMyN,QAAQ,GAAG,MAAM3U,KAAK,CAAC4U,IAAI,CAAC,GAAG3U,OAAO,oBAAoB,EAAE;QAChEgH,IAAI,EAAEkN,OAAO;QACbM,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCrN,MAAM,EAAEwN,YAAY;QACpBjL,aAAa,EAAEA;MACjB,CAAC,CAAC;MAEF,IAAIkL,QAAQ,CAAC1N,IAAI,IAAI0N,QAAQ,CAAC1N,IAAI,CAAC4N,KAAK,EAAE;QACxC;QACA,MAAM5D,WAAW,GAAG,GAAGhR,OAAO,CAACmU,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGO,QAAQ,CAAC1N,IAAI,CAAC6N,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA5L,OAAO,CAACuB,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACAxJ,UAAU,CAAC,MAAM;UACf,MAAM8T,MAAM,GAAG1F,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC;UAC/C4D,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGjE,WAAW;UACxB5B,QAAQ,CAACkC,IAAI,CAACC,WAAW,CAACuD,MAAM,CAAC;UACjC9T,UAAU,CAAC,MAAM;YACfoO,QAAQ,CAACkC,IAAI,CAACG,WAAW,CAACqD,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOlM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRV,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM6M,kBAAkB,GAAGvY,WAAW,CAAC,CAACmJ,KAAK,EAAErB,KAAK,KAAK;IACvDuN,iBAAiB,CAAClM,KAAK,EAAErB,KAAK,CAAC;EACjC,CAAC,EAAE,CAACuN,iBAAiB,CAAC,CAAC;EAEvB,MAAM1Q,OAAO,GAAGzE,OAAO,CAAC,MAAOyK,WAAW,CAAC9C,GAAG,CAACtC,GAAG,IAAI;IACpD,IAAI,EAAE6K,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACoI,cAAc,CAACjT,GAAG,CAACC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChBoF,UAAU,EAAErF,GAAG,CAACqF,UAAU;QAC1B6N,IAAI,EAAE,GAAG;QACTnR,KAAK,EAAE,GAAG;QACV7B,QAAQ,EAAE,KAAK;QACfoD,UAAU,EAAG6P,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACjU,GAAG,CAACwJ,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIyK,MAAM,CAACjU,GAAG,CAACkB,QAAQ,EAAE;YACvB,MAAMgT,iBAAiB,GAAGD,MAAM,CAACjU,GAAG,CAAC0L,iBAAiB,IAAI,KAAK;YAC/D,oBACExM,OAAA,CAACzC,OAAO;cAAC0X,KAAK,EAAEF,MAAM,CAACjU,GAAG,CAAC0L,iBAAiB,IAAI,EAAG;cAAC0I,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAxS,QAAA,eACvE3C,OAAA,CAAC1C,IAAI;gBACH8X,KAAK,EAAEJ,iBAAkB;gBACzBzR,KAAK,EAAC,SAAS;gBACfuB,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACZtB,EAAE,EAAE;kBAAE+C,QAAQ,EAAE,MAAM;kBAAEqP,OAAO,EAAE,GAAG;kBAAE/R,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAE4C,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEqO,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA3Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIqR,UAAU,GAAG,MAAM;UACvB,IAAI5P,UAAU,GAAG,KAAK;UAEtB,IAAIqP,MAAM,CAACjU,GAAG,CAAC0L,iBAAiB,IAAIuI,MAAM,CAACjU,GAAG,CAAC0L,iBAAiB,KAAK,MAAM,EAAE;YAC3E8I,UAAU,GAAGP,MAAM,CAACjU,GAAG,CAAC0L,iBAAiB;YACzC9G,UAAU,GAAG,IAAI;UACnB;UAEA,oBACE1F,OAAA,CAACqF,UAAU;YACTG,KAAK,EAAEuP,MAAM,CAACjU,GAAG,CAACoB,EAAG;YACrBuD,IAAI,EAAE6P,UAAW;YACjB5P,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEiP;UAAmB;YAAA9Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIrC,GAAG,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChBoF,UAAU,EAAErF,GAAG,CAACqF,UAAU;QAC1B6N,IAAI,EAAE,GAAG;QACTnR,KAAK,EAAE,GAAG;QACV7B,QAAQ,EAAE,KAAK;QACfoD,UAAU,EAAG6P,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACjU,GAAG,CAACwJ,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEtK,OAAA,CAACxD,GAAG;YAACyG,EAAE,EAAE;cAAEwR,OAAO,EAAE,MAAM;cAAEc,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAA7S,QAAA,gBAE3D3C,OAAA,CAACzC,OAAO;cAAC0X,KAAK,EAAC,wDAAW;cAAAtS,QAAA,eACxB3C,OAAA,CAAC5C,UAAU;gBACTmH,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,SAAS;gBACfoC,OAAO,EAAEA,CAAA,KAAMoN,YAAY,CAACgC,MAAM,CAACjU,GAAG,CAACoB,EAAE,CAAE;gBAC3Ce,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,cAAc;oBAC/BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEF3C,OAAA,CAACX,oBAAoB;kBAAC0F,QAAQ,EAAC;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVjE,OAAA,CAACzC,OAAO;cAAC0X,KAAK,EAAC,0EAAc;cAAAtS,QAAA,eAC3B3C,OAAA,CAAC5C,UAAU;gBACTmH,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,OAAO;gBACboC,OAAO,EAAEA,CAAA,KAAMkN,eAAe,CAACkC,MAAM,CAACjU,GAAG,CAACoB,EAAE,CAAE;gBAC9Ce,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,YAAY;oBAC7BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEF3C,OAAA,CAACV,uBAAuB;kBAACyF,QAAQ,EAAC;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGT8Q,MAAM,CAACjU,GAAG,CAACkB,QAAQ,gBAClBhC,OAAA,CAACtD,MAAM;cAELoI,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,SAAS;cACfgB,IAAI,EAAC,OAAO;cACZkR,SAAS,eAAEzV,OAAA,CAACjB,QAAQ;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxB0B,OAAO,EAAEA,CAAA,KAAMiN,aAAa,CAACmC,MAAM,CAACjU,GAAG,CAACoB,EAAE,CAAE;cAC5Ce,EAAE,EAAE;gBACF8B,QAAQ,EAAE,SAAS;gBACnBkB,aAAa,EAAE,MAAM;gBACrBF,QAAQ,EAAE;cACZ,CAAE;cAAApD,QAAA,EACH;YAED,GAbM,MAAM;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETjE,OAAA,CAACtD,MAAM;cAELoI,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,OAAO;cACbgB,IAAI,EAAC,OAAO;cACZkR,SAAS,eAAEzV,OAAA,CAAClB,UAAU;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAM+M,eAAe,CAACqC,MAAM,CAACjU,GAAG,CAACoB,EAAE,CAAE;cAC9Ce,EAAE,EAAE;gBACF8B,QAAQ,EAAE,SAAS;gBACnBkB,aAAa,EAAE,MAAM;gBACrBF,QAAQ,EAAE;cACZ,CAAE;cAAApD,QAAA,EACH;YAED,GAbM,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGrC,GAAG;MACNE,QAAQ,EAAEiT,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACjU,GAAG,IAAIiU,MAAM,CAACjU,GAAG,CAACwJ,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIyK,MAAM,CAACjU,GAAG,IAAIiU,MAAM,CAACjU,GAAG,CAACkB,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOJ,GAAG,CAACE,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDoD,UAAU,EAAG6P,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACjU,GAAG,CAACwJ,EAAE,KAAK,OAAO,IAAI1I,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE7B,OAAA,CAACvD,UAAU;YAACqI,OAAO,EAAC,OAAO;YAAC4Q,UAAU,EAAC,MAAM;YAACnS,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAC1D,OAAOoS,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,GAAG4Q,MAAM,CAAC5Q,KAAK,CAAC4P,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOgB,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,IAAI,CAACgI,KAAK,CAACxB,MAAM,CAACoK,MAAM,CAAC5Q,KAAK,CAAC,CAAC,GAAGwG,MAAM,CAACoK,MAAM,CAAC5Q,KAAK,CAAC,CAAC4P,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAAC5Q;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI8Q,MAAM,CAACjU,GAAG,CAACkB,QAAQ,EAAE;UACvB,oBACEhC,OAAA,CAACvD,UAAU;YAACqI,OAAO,EAAC,OAAO;YAACvB,KAAK,EAAC,eAAe;YAACN,EAAE,EAAE;cAAEO,cAAc,EAAE;YAAe,CAAE;YAAAb,QAAA,EACtFoS,MAAM,CAAC5Q;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIrC,GAAG,CAACC,KAAK,KAAK,MAAM,IAAIkT,MAAM,CAAC5Q,KAAK,EAAE;UACxC,OAAO4Q,MAAM,CAAC5Q,KAAK,CAACyP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIhS,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOoK,IAAI,CAACsF,KAAK,CAACkB,MAAM,CAAC5Q,KAAK,CAAC;QACjC;QACA,IAAIvC,GAAG,CAACC,KAAK,KAAK,OAAO,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOoK,IAAI,CAACsF,KAAK,CAACkB,MAAM,CAAC5Q,KAAK,CAAC;QACjC;QACA,IAAIvC,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOoK,IAAI,CAACsF,KAAK,CAACkB,MAAM,CAAC5Q,KAAK,CAAC;QACjC;QACA,IAAIvC,GAAG,CAACC,KAAK,KAAK,UAAU,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO4Q,MAAM,CAAC5Q,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG4Q,MAAM,CAAC5Q,KAAK,CAAC4P,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAAC5Q,KAAK,CAAC4P,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAInS,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO4Q,MAAM,CAAC5Q,KAAK,CAAC4P,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAInS,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOkT,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,IAAI,CAACgI,KAAK,CAACxB,MAAM,CAACoK,MAAM,CAAC5Q,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOwG,MAAM,CAACoK,MAAM,CAAC5Q,KAAK,CAAC,CAAC4P,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOgB,MAAM,CAAC5Q,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO4Q,MAAM,CAAC5Q,KAAK;QACrB;QACA,OAAO4Q,MAAM,CAAC5Q,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACqG,MAAM,CAACmL,OAAO,CAAE,EAAE,CAAC3O,WAAW,EAAEyF,QAAQ,EAAEmI,kBAAkB,EAAElC,eAAe,EAAEE,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEhI;EACAzW,SAAS,CAAC,MAAM;IACdsM,OAAO,CAACuB,GAAG,CAAC,SAAS,EAAE;MACrB0F,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1BwG,UAAU,EAAEhI,YAAY,CAAC1D;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACuF,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAExB,YAAY,CAAC1D,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACuC,QAAQ,IAAIA,QAAQ,CAACvC,MAAM,KAAK,CAAC,EAAE;IACtC,oBACElK,OAAA,CAACxD,GAAG;MAACyG,EAAE,EAAE;QAAEY,SAAS,EAAE,QAAQ;QAAEgS,EAAE,EAAE;MAAE,CAAE;MAAAlT,QAAA,gBACtC3C,OAAA,CAACvD,UAAU;QAACqI,OAAO,EAAC,IAAI;QAACvB,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAAC;MAEhD;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACtD,MAAM;QACLoI,OAAO,EAAC,WAAW;QACnBa,OAAO,EAAEgB,OAAQ;QACjB1D,EAAE,EAAE;UAAE6S,EAAE,EAAE;QAAE,CAAE;QAAAnT,QAAA,EACf;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjE,OAAA,CAACxD,GAAG;IAAAmG,QAAA,gBAEF3C,OAAA,CAACxC,IAAI;MAACyF,EAAE,EAAE;QAAE8S,EAAE,EAAE;MAAE,CAAE;MAAApT,QAAA,eAClB3C,OAAA,CAACvC,WAAW;QAAAkF,QAAA,eACV3C,OAAA,CAACxD,GAAG;UAACyG,EAAE,EAAE;YAAEwR,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE,QAAQ;YAAEQ,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEV,GAAG,EAAE;UAAE,CAAE;UAAA5S,QAAA,gBAC5G3C,OAAA,CAACxD,GAAG;YAACyG,EAAE,EAAE;cAAEwR,OAAO,EAAE,MAAM;cAAEe,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAA5S,QAAA,gBACzD3C,OAAA,CAAChB,cAAc;cAACiE,EAAE,EAAE;gBAAEM,KAAK,EAAE,cAAc;gBAAEwB,QAAQ,EAAE;cAAG;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACxD,GAAG;cAAAmG,QAAA,gBACF3C,OAAA,CAACvD,UAAU;gBAACqI,OAAO,EAAC,IAAI;gBAAC7B,EAAE,EAAE;kBAAEyS,UAAU,EAAE,GAAG;kBAAEnS,KAAK,EAAE,cAAc;kBAAEwS,EAAE,EAAE;gBAAI,CAAE;gBAAApT,QAAA,EAAC;cAElF;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAACvD,UAAU;gBAACqI,OAAO,EAAC,OAAO;gBAAC7B,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAiB,CAAE;gBAAAZ,QAAA,EAAC;cAE7D;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjE,OAAA,CAACtC,KAAK;YAACgM,SAAS,EAAC,KAAK;YAACwM,OAAO,EAAE,CAAE;YAACjT,EAAE,EAAE;cAAEgT,QAAQ,EAAE,MAAM;cAAEV,GAAG,EAAE;YAAE,CAAE;YAAA5S,QAAA,gBAClE3C,OAAA,CAAC1C,IAAI;cACH6Y,IAAI,eAAEnW,OAAA,CAACf,aAAa;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBmR,KAAK,EAAE,GAAG,CAACxH,YAAY,IAAI,EAAE,EAAEpD,MAAM,CAAC1J,GAAG,IAAI,CAACA,GAAG,CAACiB,OAAO,IAAI,CAACjB,GAAG,CAACkB,QAAQ,CAAC,CAACkI,MAAM,MAAO;cACzF3G,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjE,OAAA,CAAC1C,IAAI;cACH6Y,IAAI,eAAEnW,OAAA,CAACd,cAAc;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBmR,KAAK,EAAE,WAAW/D,kBAAkB,CAACzD,YAAY,CAAC,CAACmG,OAAO,CAAC,CAAC,CAAC,EAAG;cAChExQ,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAAC2J,YAAY,IAAI,EAAE,EAAEpD,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACkB,QAAQ,CAAC,CAACkI,MAAM,GAAG,CAAC,iBAC1DlK,OAAA,CAAC1C,IAAI;cACH6Y,IAAI,eAAEnW,OAAA,CAAClB,UAAU;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBmR,KAAK,EAAE,GAAG,CAACxH,YAAY,IAAI,EAAE,EAAEpD,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACkB,QAAQ,CAAC,CAACkI,MAAM,OAAQ;cACzE3G,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjE,OAAA,CAACxC,IAAI;MAACyF,EAAE,EAAE;QAAE8S,EAAE,EAAE;MAAE,CAAE;MAAApT,QAAA,eAClB3C,OAAA,CAACvC,WAAW;QAAAkF,QAAA,eACV3C,OAAA,CAAC9B,IAAI;UAACkY,SAAS;UAACF,OAAO,EAAE,CAAE;UAAAvT,QAAA,gBAEzB3C,OAAA,CAAC9B,IAAI;YAACuU,IAAI;YAAC4D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3T,QAAA,gBACvB3C,OAAA,CAACvD,UAAU;cAACqI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEyS,UAAU,EAAE,GAAG;gBAAEK,EAAE,EAAE,CAAC;gBAAEtB,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAA5S,QAAA,gBAC5G3C,OAAA,CAACb,UAAU;gBAAC8D,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbjE,OAAA,CAACtC,KAAK;cAACgM,SAAS,EAAE;gBAAE2M,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAACV,UAAU,EAAC,QAAQ;cAAA7S,QAAA,gBAC5E3C,OAAA,CAACrC,WAAW;gBAAC4G,IAAI,EAAC,OAAO;gBAACtB,EAAE,EAAE;kBAAE8C,QAAQ,EAAE;gBAAI,CAAE;gBAAApD,QAAA,gBAC9C3C,OAAA,CAACpC,UAAU;kBAAA+E,QAAA,EAAC;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7BjE,OAAA,CAACnC,MAAM;kBACLsG,KAAK,EAAEmF,YAAa;kBACpB8L,KAAK,EAAC,0BAAM;kBACZ5Q,QAAQ,EAAGpC,CAAC,IAAKmH,eAAe,CAACnH,CAAC,CAACqC,MAAM,CAACN,KAAK,CAAE;kBAAAxB,QAAA,gBAEjD3C,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,KAAK;oBAAAxB,QAAA,EAAC;kBAAG;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,MAAM;oBAAAxB,QAAA,EAAC;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtCjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAU;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClDjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,OAAO;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxCjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,UAAU;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC3CjE,OAAA,CAAClC,QAAQ;oBAACqG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAM;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEdjE,OAAA,CAAC7C,SAAS;gBACRoH,IAAI,EAAC,OAAO;gBACZiS,WAAW,EAAC,yCAAW;gBACvBrS,KAAK,EAAE+E,UAAW;gBAClB1E,QAAQ,EAAGpC,CAAC,IAAK+G,aAAa,CAAC/G,CAAC,CAACqC,MAAM,CAACN,KAAK,CAAE;gBAC/ClB,EAAE,EAAE;kBAAEwT,QAAQ,EAAE,CAAC;kBAAE1Q,QAAQ,EAAE;gBAAI,CAAE;gBACnC2Q,UAAU,EAAE;kBACVC,cAAc,eACZ3W,OAAA,CAACjC,cAAc;oBAAC6Y,QAAQ,EAAC,OAAO;oBAAAjU,QAAA,eAC9B3C,OAAA,CAACb,UAAU;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBACD4S,YAAY,EAAE3N,UAAU,iBACtBlJ,OAAA,CAACjC,cAAc;oBAAC6Y,QAAQ,EAAC,KAAK;oBAAAjU,QAAA,eAC5B3C,OAAA,CAAC5C,UAAU;sBACTmH,IAAI,EAAC,OAAO;sBACZoB,OAAO,EAAEA,CAAA,KAAMwD,aAAa,CAAC,EAAE,CAAE;sBACjC2N,IAAI,EAAC,KAAK;sBAAAnU,QAAA,eAEV3C,OAAA,CAACZ,SAAS;wBAAA0E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEDiF,UAAU,iBACTlJ,OAAA,CAACvD,UAAU;gBAACqI,OAAO,EAAC,OAAO;gBAACvB,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,GAAC,eAC9C,EAACiK,gBAAgB,CAACpC,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAM,EAAC,qBAChE;cAAA;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPjE,OAAA,CAAC9B,IAAI;YAACuU,IAAI;YAAC4D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3T,QAAA,gBACvB3C,OAAA,CAACvD,UAAU;cAACqI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEyS,UAAU,EAAE,GAAG;gBAAEK,EAAE,EAAE,CAAC;gBAAEtB,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAA5S,QAAA,gBAC5G3C,OAAA,CAACT,YAAY;gBAAC0D,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbjE,OAAA,CAACtC,KAAK;cAACgM,SAAS,EAAE;gBAAE2M,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAAvT,QAAA,gBACxD3C,OAAA,CAACtD,MAAM;gBACLoI,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACfkS,SAAS,eAAEzV,OAAA,CAACrB,YAAY;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5B0B,OAAO,EAAE4K,cAAe;gBAAA5N,QAAA,EACzB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETjE,OAAA,CAACtD,MAAM;gBACLoI,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACfkS,SAAS,EAAE3N,oBAAoB,gBAAG9H,OAAA,CAAC3C,gBAAgB;kBAACkH,IAAI,EAAE,EAAG;kBAAChB,KAAK,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACf,aAAa;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrG0B,OAAO,EAAE8N,gBAAiB;gBAC1BsD,QAAQ,EAAEjP,oBAAqB;gBAAAnF,QAAA,EAE9BmF,oBAAoB,GAAG,QAAQ,GAAG;cAAW;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAETjE,OAAA,CAACtD,MAAM;gBACLoI,OAAO,EAAC,UAAU;gBAClBvB,KAAK,EAAC,OAAO;gBACbkS,SAAS,eAAEzV,OAAA,CAACpB,cAAc;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9B0B,OAAO,EAAEwL,aAAc;gBAAAxO,QAAA,EACxB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjE,OAAA,CAACL,eAAe;MAACqX,SAAS,EAAEnM,aAAc;MAAAlI,QAAA,eACxC3C,OAAA,CAACrD,KAAK;QAACsG,EAAE,EAAE;UACTU,KAAK,EAAE,MAAM;UACbuC,QAAQ,EAAE,QAAQ;UAClB,iBAAiB,EAAE;YACjB9C,SAAS,EAAE,cAAc;YACzBC,SAAS,EAAE,CAAC;YACZI,MAAM,EAAE;UACV,CAAC;UACD,qBAAqB,EAAE;YACrBH,UAAU,EAAE;UACd,CAAC;UACD,sBAAsB,EAAE;YACtB0B,YAAY,EAAE,WAAW;YACzBiS,WAAW,EAAE,WAAW;YACxBC,WAAW,EAAE;UACf;QACF,CAAE;QAAAvU,QAAA,gBACA3C,OAAA,CAAC1B,cAAc;UAAAqE,QAAA,eACb3C,OAAA,CAAC7B,KAAK;YAACgZ,YAAY;YAAAxU,QAAA,gBACjB3C,OAAA,CAACzB,SAAS;cAAAoE,QAAA,eACR3C,OAAA,CAACxB,QAAQ;gBAACyE,EAAE,EAAE;kBAAEoD,MAAM,EAAE;gBAAG,CAAE;gBAAA1D,QAAA,gBAE3B3C,OAAA,CAAC3B,SAAS;kBAAC4E,EAAE,EAAE;oBACbU,KAAK,EAAE,EAAE;oBACT+R,UAAU,EAAE,MAAM;oBAClBrP,MAAM,EAAE,EAAE;oBACVhC,OAAO,EAAE;kBACX,CAAE;kBAAA1B,QAAA,EAAC;gBAEH;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EACXjD,OAAO,CAACkD,GAAG,CAAExC,MAAM,iBAClB1B,OAAA,CAAC3B,SAAS;kBAER4E,EAAE,EAAE;oBACFyS,UAAU,EAAE,MAAM;oBAClBxS,eAAe,EAAE,oBAAoB;oBACrC+T,WAAW,EAAE,WAAW;oBACxBC,WAAW,EAAE,SAAS;oBACtB7Q,MAAM,EAAE,EAAE;oBACVhC,OAAO,EAAE,KAAK;oBACd,cAAc,EAAE;sBACd4S,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAtU,QAAA,EAGDjB,MAAM,CAACG,KAAK,KAAK,QAAQ,IAAIH,MAAM,CAACG,KAAK,KAAK,SAAS,gBACtD7B,OAAA,CAACtB,cAAc;oBACb0Y,MAAM,EAAE5N,UAAU,CAAC3H,KAAK,KAAKH,MAAM,CAACG,KAAM;oBAC1C6H,SAAS,EAAEF,UAAU,CAAC3H,KAAK,KAAKH,MAAM,CAACG,KAAK,GAAG2H,UAAU,CAACE,SAAS,GAAG,KAAM;oBAC5E/D,OAAO,EAAEA,CAAA,KAAMgE,UAAU,CAACjI,MAAM,CAACG,KAAK,CAAE;oBACxCoB,EAAE,EAAE;sBACF,2BAA2B,EAAE;wBAC3B8B,QAAQ,EAAE;sBACZ;oBACF,CAAE;oBAAApC,QAAA,EAEDjB,MAAM,CAACuF;kBAAU;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,GAEjBvC,MAAM,CAACuF;gBACR,GA7BIvF,MAAM,CAACG,KAAK;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BR,CACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEZjE,OAAA,CAACJ,SAAS;cAACyX,WAAW,EAAC,YAAY;cAAA1U,QAAA,EAChCA,CAACC,QAAQ,EAAEC,QAAQ,kBAClB7C,OAAA,CAAC5B,SAAS;gBACR0E,GAAG,EAAEF,QAAQ,CAACG,QAAS;gBAAA,GACnBH,QAAQ,CAAC0U,cAAc;gBAC3BrU,EAAE,EAAE;kBACFC,eAAe,EAAEL,QAAQ,CAAC0U,cAAc,GAAG,cAAc,GAAG,SAAS;kBACrEjU,UAAU,EAAE,4BAA4B;kBACxCkU,SAAS,EAAE,WAAW;kBACtBN,WAAW,EAAE;gBACf,CAAE;gBAAAvU,QAAA,GAGDiL,YAAY,CACVpD,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC,CAAC;gBAAA,CAClCmN,KAAK,CACJhI,eAAe,CAACL,IAAI,GAAGK,eAAe,CAACE,QAAQ,EAC/C,CAACF,eAAe,CAACL,IAAI,GAAG,CAAC,IAAIK,eAAe,CAACE,QAC/C,CAAC,CACAzL,GAAG,CAAC,CAACpD,GAAG,EAAEC,KAAK,kBACdf,OAAA,CAACU,iBAAiB;kBAEhBI,GAAG,EAAEA,GAAI;kBACTC,KAAK,EAAEA,KAAM;kBACbC,OAAO,EAAEA,OAAQ;kBACjBC,UAAU,EAAE0K,cAAe;kBAC3BzK,cAAc,EAAE2M;gBAAmB,GAL9B/M,GAAG,CAACoB,EAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMZ,CACF,CAAC,EAGH2J,YAAY,CAACjM,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC,iBAC3CtK,OAAA,CAACxB,QAAQ;kBAACkZ,SAAS,EAAC,WAAW;kBAACzU,EAAE,EAAE;oBAClCC,eAAe,EAAE,0BAA0B;oBAC3CwS,UAAU,EAAE,MAAM;oBAClB,sBAAsB,EAAE;sBACtBA,UAAU,EAAE,MAAM;sBAClB8B,SAAS,EAAE,WAAW;sBACtBN,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAvU,QAAA,gBACA3C,OAAA,CAAC3B,SAAS;oBAAC4E,EAAE,EAAE;sBAAEU,KAAK,EAAE;oBAAG;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElB,CAAC,EACXjD,OAAO,CAACkD,GAAG,CAAExC,MAAM,IAAK;oBACvB,MAAM2I,QAAQ,GAAGuD,YAAY,CAACjM,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC;oBAC7D,MAAMnG,KAAK,GAAGkG,QAAQ,GAAGA,QAAQ,CAAC3I,MAAM,CAACG,KAAK,CAAC,GAAG,EAAE;oBACpD,oBACE7B,OAAA,CAAC3B,SAAS;sBAAoB4E,EAAE,EAAE;wBAAEoB,OAAO,EAAE;sBAAM,CAAE;sBAAA1B,QAAA,EAClDjB,MAAM,CAACwD,UAAU,GAAGxD,MAAM,CAACwD,UAAU,CAAC;wBAAEpE,GAAG,EAAEuJ,QAAQ;wBAAElG;sBAAM,CAAC,CAAC,GAAGA;oBAAK,GAD1DzC,MAAM,CAACG,KAAK;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEjB,CAAC;kBAEhB,CAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CACX,EAEArB,QAAQ,CAAC4T,WAAW;cAAA;gBAAA1S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACZ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjBjE,OAAA,CAACvB,eAAe;UACdkZ,SAAS,EAAC,KAAK;UACfC,KAAK,EAAEhK,YAAY,CAACpD,MAAM,CAAC1J,GAAG,IAAIA,GAAG,CAACwJ,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAO;UAC7DkF,IAAI,EAAEK,eAAe,CAACL,IAAK;UAC3ByI,YAAY,EAAEA,CAACC,KAAK,EAAEC,OAAO,KAAK;YAChC5I,kBAAkB,CAAC/D,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEgE,IAAI,EAAE2I;YAAQ,CAAC,CAAC,CAAC;UAC1D,CAAE;UACFC,WAAW,EAAEvI,eAAe,CAACE,QAAS;UACtCsI,mBAAmB,EAAGH,KAAK,IAAK;YAC9B,MAAMI,WAAW,GAAGtI,QAAQ,CAACkI,KAAK,CAACrT,MAAM,CAACN,KAAK,EAAE,EAAE,CAAC;YACpDgL,kBAAkB,CAAC;cAAEC,IAAI,EAAE,CAAC;cAAEO,QAAQ,EAAEuI;YAAY,CAAC,CAAC;YACtD5Q,YAAY,CAACyC,OAAO,CAAC,kBAAkB,EAAEmO,WAAW,CAACxV,QAAQ,CAAC,CAAC,CAAC;UAClE,CAAE;UACFyV,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAClCC,gBAAgB,EAAC,2BAAO;UACxBC,kBAAkB,EAAEA,CAAC;YAAEC,IAAI;YAAEC,EAAE;YAAEX;UAAM,CAAC,KAAK,GAAGU,IAAI,IAAIC,EAAE,MAAMX,KAAK;QAAK;UAAA9T,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGlBjE,OAAA,CAACpD,MAAM;MACL0T,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBkI,OAAO,EAAE7G,kBAAmB;MAC5B9M,SAAS;MACTmB,QAAQ,EAAC,IAAI;MACbyS,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjW,QAAA,gBAEnB3C,OAAA,CAACnD,WAAW;QAAA8F,QAAA,eACV3C,OAAA,CAACxD,GAAG;UAACyG,EAAE,EAAE;YAAEwR,OAAO,EAAE,MAAM;YAAEuB,cAAc,EAAE,eAAe;YAAER,UAAU,EAAE;UAAS,CAAE;UAAA7S,QAAA,gBAClF3C,OAAA,CAACvD,UAAU;YAACqI,OAAO,EAAC,IAAI;YAAAnC,QAAA,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjE,OAAA,CAACtD,MAAM;YACL+Y,SAAS,eAAEzV,OAAA,CAACnB,OAAO;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAE0M,mBAAoB;YAC7B9O,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAChB;UAED;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjE,OAAA,CAACxD,GAAG;QAACyG,EAAE,EAAE;UAAE4V,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnW,QAAA,gBACxB3C,OAAA,CAACvD,UAAU;UAACqI,OAAO,EAAC,OAAO;UAACvB,KAAK,EAAC,gBAAgB;UAACN,EAAE,EAAE;YAAE8S,EAAE,EAAE;UAAE,CAAE;UAAApT,QAAA,EAAC;QAElE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjE,OAAA,CAACtC,KAAK;UAACgM,SAAS,EAAC,KAAK;UAACwM,OAAO,EAAE,CAAE;UAAAvT,QAAA,gBAChC3C,OAAA,CAAC/B,gBAAgB;YACf8a,OAAO,eACL/Y,OAAA,CAAChC,QAAQ;cACPgb,OAAO,EAAE3J,aAAc;cACvB7K,QAAQ,EAAGpC,CAAC,IAAKkN,gBAAgB,CAAClN,CAAC,CAACqC,MAAM,CAACuU,OAAO,CAAE;cACpDzU,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDmR,KAAK,EAAC;UAAS;YAAAtR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjE,OAAA,CAAC/B,gBAAgB;YACf8a,OAAO,eACL/Y,OAAA,CAAChC,QAAQ;cACPgb,OAAO,EAAEzJ,UAAW;cACpB/K,QAAQ,EAAGpC,CAAC,IAAKoN,aAAa,CAACpN,CAAC,CAACqC,MAAM,CAACuU,OAAO,CAAE;cACjDzU,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDmR,KAAK,EAAC;UAAK;YAAAtR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjE,OAAA,CAAClD,aAAa;QAACmc,QAAQ;QAAChW,EAAE,EAAE;UAAEiW,CAAC,EAAE;QAAE,CAAE;QAAAvW,QAAA,eACnC3C,OAAA,CAACN,aAAa;UACZ2G,MAAM,EAAE,GAAI;UACZ8S,SAAS,EAAEhS,cAAc,CAAC+C,MAAO;UACjCkP,QAAQ,EAAE,EAAG;UACbzV,KAAK,EAAC,MAAM;UAAAhB,QAAA,EAEXA,CAAC;YAAE5B,KAAK;YAAEyT;UAAM,CAAC,KAAK;YACrB,MAAM3C,MAAM,GAAG1K,cAAc,CAACpG,KAAK,CAAC;YACpC,oBACEf,OAAA,CAAChD,QAAQ;cAEPwX,KAAK,EAAEA,KAAM;cACb6E,cAAc;cACdC,eAAe,EAAGzH,MAAM,KAAK,MAAM,iBACjC7R,OAAA,CAAC5C,UAAU;gBACT0Z,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBnR,OAAO,EAAEA,CAAA,KAAM6M,YAAY,CAACX,MAAM,CAAE;gBAAAlP,QAAA,eAEpC3C,OAAA,CAAClB,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAtB,QAAA,eAEF3C,OAAA,CAAC/C,cAAc;gBAAC0I,OAAO,EAAEA,CAAA,KAAMiM,kBAAkB,CAACC,MAAM,CAAE;gBAAAlP,QAAA,eACxD3C,OAAA,CAAC9C,YAAY;kBAACqc,OAAO,EAAE1H;gBAAO;kBAAA/N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ4N,MAAM;cAAA/N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjE,OAAA,CAACjD,aAAa;QAAA4F,QAAA,eACZ3C,OAAA,CAACtD,MAAM;UAACiJ,OAAO,EAAEgM,kBAAmB;UAAAhP,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjE,OAAA,CAACpD,MAAM;MACL0T,IAAI,EAAE1I,eAAgB;MACtB4Q,OAAO,EAAElG,oBAAqB;MAC9BzN,SAAS;MACTmB,QAAQ,EAAC,IAAI;MACbyS,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjW,QAAA,gBAEnB3C,OAAA,CAACnD,WAAW;QAAA8F,QAAA,EAAC;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjE,OAAA,CAAClD,aAAa;QAAA6F,QAAA,eACZ3C,OAAA,CAAC7C,SAAS;UACRyH,SAAS;UACT4U,MAAM,EAAC,OAAO;UACdtX,EAAE,EAAC,MAAM;UACTkT,KAAK,EAAC,0BAAM;UACZqE,IAAI,EAAC,MAAM;UACX5U,SAAS;UACTC,OAAO,EAAC,UAAU;UAClBX,KAAK,EAAEuD,SAAU;UACjBlD,QAAQ,EAAGpC,CAAC,IAAKuF,YAAY,CAACvF,CAAC,CAACqC,MAAM,CAACN,KAAK;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjE,OAAA,CAACjD,aAAa;QAAA4F,QAAA,gBACZ3C,OAAA,CAACtD,MAAM;UAACiJ,OAAO,EAAE2M,oBAAqB;UAAA3P,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjE,OAAA,CAACtD,MAAM;UAACiJ,OAAO,EAAE4M,YAAa;UAAChP,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC8C,GAAA,CAl6CIP,aAAa;AAAAkT,GAAA,GAAblT,aAAa;AAo6CnB,eAAeA,aAAa;AAAC,IAAA3F,EAAA,EAAAsE,GAAA,EAAAI,GAAA,EAAAgB,GAAA,EAAAmT,GAAA;AAAAC,YAAA,CAAA9Y,EAAA;AAAA8Y,YAAA,CAAAxU,GAAA;AAAAwU,YAAA,CAAApU,GAAA;AAAAoU,YAAA,CAAApT,GAAA;AAAAoT,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}