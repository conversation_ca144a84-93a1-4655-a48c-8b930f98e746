{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TableSortLabel } from '@mui/material';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 可拖拽的REMARKS选项组件\nconst DraggableRemarksOption = /*#__PURE__*/React.memo(_c = ({\n  option,\n  index,\n  onSelect,\n  onDelete\n}) => {\n  return /*#__PURE__*/_jsxDEV(Draggable, {\n    draggableId: `remarks-${option}-${index}`,\n    index: index,\n    children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(ListItem, {\n      ref: provided.innerRef,\n      ...provided.draggableProps,\n      disablePadding: true,\n      sx: {\n        transform: snapshot.isDragging ? 'rotate(2deg)' : 'rotate(0deg)',\n        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n        boxShadow: snapshot.isDragging ? '0 8px 16px rgba(0,0,0,0.15)' : 'none',\n        zIndex: snapshot.isDragging ? 1000 : 'auto',\n        backgroundColor: snapshot.isDragging ? 'action.hover' : 'transparent',\n        borderRadius: snapshot.isDragging ? 1 : 0,\n        '&:hover': {\n          backgroundColor: 'action.hover'\n        }\n      },\n      secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n        edge: \"end\",\n        \"aria-label\": \"delete\",\n        onClick: () => onDelete(option),\n        sx: {\n          mr: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 13\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        ...provided.dragHandleProps,\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          px: 1,\n          cursor: 'grab',\n          '&:active': {\n            cursor: 'grabbing'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n          sx: {\n            color: 'text.secondary',\n            fontSize: '1.2rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: () => onSelect(option),\n        sx: {\n          pl: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: option\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n});\n\n// 拖拽表格行组件\n_c2 = DraggableRemarksOption;\nconst DraggableTableRow = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c3 = _s(({\n  row,\n  index,\n  columns,\n  onCellEdit,\n  onNavigateCell\n}) => {\n  _s();\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n  const handleCellSave = columnField => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      // 保存数据\n      handleCellSave(columnField);\n      // 导航到下一行同列\n      console.log(e);\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'up' : 'down');\n        }, 100);\n      }\n    } else if (e.key === 'Tab') {\n      e.preventDefault();\n      handleCellSave(columnField);\n      // 导航到下一列\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'left' : 'right');\n        }, 100);\n      }\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Draggable, {\n    draggableId: row.id.toString(),\n    index: index,\n    children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableRow, {\n      ref: provided.innerRef,\n      ...provided.draggableProps,\n      sx: {\n        backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n        transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n        boxShadow: snapshot.isDragging ? 3 : 0,\n        transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n        '&.removed-row': {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        },\n        ...(snapshot.isDragging && {\n          zIndex: 1000\n        }),\n        ...(row._removed && {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        })\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        ...provided.dragHandleProps,\n        sx: {\n          width: 40,\n          cursor: 'grab',\n          '&:active': {\n            cursor: 'grabbing'\n          },\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n          sx: {\n            color: 'text.secondary'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), columns.map(column => {\n        const value = row[column.field];\n        const isEditing = editingCell === column.field;\n        return /*#__PURE__*/_jsxDEV(TableCell, {\n          \"data-row-id\": row.id,\n          \"data-field\": column.field,\n          sx: {\n            padding: '8px',\n            cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default'\n          }\n          // onClick={() => handleCellClick(column.field, value)}\n          ,\n          onDoubleClick: () => handleCellClick(column.field, value),\n          children: isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            value: editValue,\n            onChange: e => setEditValue(e.target.value),\n            onKeyDown: e => handleKeyPress(e, column.field),\n            onBlur: () => handleCellSave(column.field),\n            autoFocus: true,\n            fullWidth: true,\n            variant: \"standard\",\n            sx: {\n              '& .MuiInput-root': {\n                fontSize: 'inherit',\n                '&:before': {\n                  borderBottom: '1px solid rgba(0, 0, 0, 0.12)'\n                },\n                '&:hover:before': {\n                  borderBottom: '2px solid rgba(0, 0, 0, 0.87)'\n                },\n                '&:after': {\n                  borderBottom: '2px solid #1976d2'\n                }\n              },\n              '& .MuiInput-input': {\n                padding: '4px 0',\n                fontSize: 'inherit',\n                fontFamily: 'inherit'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 19\n          }, this) : column.renderCell ? column.renderCell({\n            row,\n            value\n          }) : value\n        }, column.field, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 15\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n}, \"vUzEbioNkVLGJwl96b8AAiFyV68=\")), \"vUzEbioNkVLGJwl96b8AAiFyV68=\");\n\n// 默认的REMARKS选项\n_c4 = DraggableTableRow;\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK\", \"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"DVR SD CARD\", \"FRONT DISC BRAKE PAD (BOTH SIDES)\", \"REAR DISC BRAKE PAD (BOTH SIDES)\", \"FUEL FILLER OPENING LID HINGE SPRING\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c5 = _s2(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c6 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s3();\n  // 先声明columnOrder\n  const columnOrder = useMemo(() => [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }], []);\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01',\n            'FEB': '02',\n            'MAR': '03',\n            'APR': '04',\n            'MAY': '05',\n            'JUNE': '06',\n            'JULY': '07',\n            'AUG': '08',\n            'SEP': '09',\n            'OCT': '10',\n            'NOV': '11',\n            'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 排序状态\n  const [sortConfig, setSortConfig] = useState({\n    field: null,\n    direction: 'asc'\n  });\n\n  // 排序处理函数\n  const handleSort = field => {\n    const isAsc = sortConfig.field === field && sortConfig.direction === 'asc';\n    const newDirection = isAsc ? 'desc' : 'asc';\n    setSortConfig({\n      field,\n      direction: newDirection\n    });\n  };\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback(result => {\n    if (!result.destination) {\n      return;\n    }\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = {\n            ...row,\n            [field]: newValue\n          };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n          return updatedRow;\n        }\n        return row;\n      });\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 自动填充_selected_remarks的函数\n  const autoFillSelectedRemarks = row => {\n    const maxcheck = parseFloat(row.MAXCHECK) || 0;\n    const km = parseFloat(row.KM) || 0;\n    const remarks = row.REMARKS || '';\n\n    // 首先检查KM值的特殊情况\n    if (km === 1) {\n      return 'MAXCHECK COMPULSORY 1ST SERVICE';\n    }\n    if (km === 5) {\n      return 'MAXCHECK COMPULSORY 2ND SERVICE';\n    }\n\n    // 如果有REMARKS内容，根据内容判断\n    if (remarks.trim()) {\n      const remarksUpper = remarks.toUpperCase();\n\n      // 检查是否包含特定关键词（按优先级排序，更具体的在前面）\n      if (remarksUpper.includes('FUEL FILLER OPENING LID HINGE SPRING')) return 'FUEL FILLER OPENING LID HINGE SPRING';\n      if (remarksUpper.includes('DVR')) return 'DVR SD CARD';\n      if (remarksUpper.includes('ATF')) return 'ATF REPLACEMENT';\n      if (remarksUpper.includes('SPARK PLUG')) return 'SPARK PLUG';\n\n      // 刹车片相关 - 更精确的匹配\n      if (remarksUpper.includes('FR B/PAD') || remarksUpper.includes('FRONT DISC BRAKE PAD')) return 'FRONT DISC BRAKE PAD (BOTH SIDES)';\n      if (remarksUpper.includes('REAR B/PAD') || remarksUpper.includes('REAR DISC BRAKE PAD')) return 'REAR DISC BRAKE PAD (BOTH SIDES)';\n      if (remarksUpper.includes('B/PAD') && !remarksUpper.includes('FR') && !remarksUpper.includes('REAR')) return 'REPLACE BRAKE PADS';\n      if (remarksUpper.includes('BRAKE PAD')) return 'REPLACE BRAKE PADS';\n\n      // 电池相关\n      if (remarksUpper.includes('BT ') || remarksUpper.includes('BATTERY')) return 'REPLACE BATTERY';\n\n      // 雨刷相关\n      if (remarksUpper.includes('W/RUBBER') || remarksUpper.includes('WIPER')) return 'REPLACE WIPER RUBBER';\n\n      // CBU CAR 相关 - 如果包含CBU CAR但没有其他明确的项目，可能是MAXCHECK相关\n      if (remarksUpper.includes('CBU CAR') && !remarksUpper.includes('B/PAD') && !remarksUpper.includes('ATF') && !remarksUpper.includes('BT')) {\n        // 根据MAXCHECK值判断CBU CAR的类型\n        if (maxcheck >= 1.0 && maxcheck <= 1.6) {\n          return 'MAXCHECK';\n        } else if (maxcheck >= 1.7 && maxcheck <= 2.2) {\n          return 'MAXCHECK ADVANCE';\n        } else if (maxcheck >= 2.3 && maxcheck <= 4.0) {\n          return 'MAXCHECK ADVANCE PLUS';\n        }\n      }\n    }\n\n    // 检查后端返回的字段\n    if (row['EGN OIL']) return 'EGN OIL';\n    if (row['O/FLTER']) return 'O/FLTER';\n    if (row['ATF']) return 'ATF REPLACEMENT';\n    if (row['BT']) return 'REPLACE BATTERY';\n    if (row['EGN FLUSH']) return 'EGN FLUSH';\n    if (row['FR BR PAD']) return 'FRONT DISC BRAKE PAD (BOTH SIDES)';\n    if (row['R BR PAD']) return 'REAR DISC BRAKE PAD (BOTH SIDES)';\n    if (row['D FILTER']) return 'D FILTER';\n    if (row['P/SHAFT']) return 'P/SHAFT';\n    if (row['W/RUBBER']) return 'REPLACE WIPER RUBBER';\n    if (row['SPARK PLUG']) return 'SPARK PLUG';\n    if (row['OTHERS']) return row['OTHERS']; // OTHERS返回原始值\n\n    // 根据MAXCHECK值判断\n    if (maxcheck >= 1.0 && maxcheck <= 1.6) {\n      return 'MAXCHECK';\n    } else if (maxcheck >= 1.7 && maxcheck <= 2.2) {\n      return 'MAXCHECK ADVANCE';\n    } else if (maxcheck >= 2.3 && maxcheck <= 4.0) {\n      return 'MAXCHECK ADVANCE PLUS';\n    }\n\n    // 默认返回空\n    return '';\n  };\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 检查是否需要移除（is_havent_close_job为true）\n    const shouldRemove = row.is_havent_close_job === true;\n\n    // 自动填充_selected_remarks\n    const autoSelectedRemarks = autoFillSelectedRemarks(row);\n    return {\n      ...row,\n      _selected_remarks: autoSelectedRemarks,\n      _removed: shouldRemove\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在，如果为空则自动填充\n        if (row._selected_remarks === undefined || row._selected_remarks === '') {\n          row._selected_remarks = autoFillSelectedRemarks(row);\n        }\n\n        // 检查是否需要移除（is_havent_close_job为true）\n        if (row.is_havent_close_job === true && !row._removed) {\n          row._removed = true;\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 搜索和排序过滤逻辑\n  const filteredGridData = useMemo(() => {\n    let data = gridData || [];\n\n    // 搜索过滤\n    if (debouncedSearchText.trim()) {\n      const searchLower = debouncedSearchText.toLowerCase();\n      data = data.filter(row => {\n        if (searchColumn === 'all') {\n          // 搜索所有列\n          return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n        } else {\n          // 搜索特定列\n          const cellValue = row[searchColumn];\n          return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n        }\n      });\n    }\n\n    // 排序处理\n    if (sortConfig.field) {\n      data = [...data].sort((a, b) => {\n        // TOTAL行始终在最后\n        if (a.NO === 'TOTAL') return 1;\n        if (b.NO === 'TOTAL') return -1;\n        const aValue = a[sortConfig.field];\n        const bValue = b[sortConfig.field];\n\n        // 处理数字类型\n        if (typeof aValue === 'number' && typeof bValue === 'number') {\n          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n        }\n\n        // 处理字符串类型\n        const aStr = String(aValue || '').toLowerCase();\n        const bStr = String(bValue || '').toLowerCase();\n        if (sortConfig.direction === 'asc') {\n          return aStr.localeCompare(bStr);\n        } else {\n          return bStr.localeCompare(aStr);\n        }\n      });\n    }\n    return data;\n  }, [gridData, debouncedSearchText, searchColumn, sortConfig]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 处理单元格导航\n  const handleNavigateCell = useCallback((currentRowId, currentField, direction) => {\n    const editableColumns = columnOrder.filter(col => col.editable && col.field !== 'NO' && col.field !== 'ACTION' && col.field !== 'REMARKS');\n    const dataRows = memoGridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n    const currentRowIndex = dataRows.findIndex(row => row.id === currentRowId);\n    const currentColIndex = editableColumns.findIndex(col => col.field === currentField);\n    if (currentRowIndex === -1 || currentColIndex === -1) return;\n    let targetRowIndex = currentRowIndex;\n    let targetColIndex = currentColIndex;\n    switch (direction) {\n      case 'down':\n        targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n        break;\n      case 'up':\n        targetRowIndex = Math.max(currentRowIndex - 1, 0);\n        break;\n      case 'right':\n        // Tab键：如果已经是最后一列，跳到下一行的第一个可编辑列\n        if (currentColIndex >= editableColumns.length - 1) {\n          // 跳到下一行的第一列\n          targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n          targetColIndex = 0;\n        } else {\n          // 否则跳到同行的下一列\n          targetColIndex = currentColIndex + 1;\n        }\n        break;\n      case 'left':\n        // Shift+Tab：如果已经是第一列，跳到上一行的最后一个可编辑列\n        if (currentColIndex <= 0) {\n          // 跳到上一行的最后一列\n          targetRowIndex = Math.max(currentRowIndex - 1, 0);\n          targetColIndex = editableColumns.length - 1;\n        } else {\n          // 否则跳到同行的上一列\n          targetColIndex = currentColIndex - 1;\n        }\n        break;\n      default:\n        return;\n    }\n    const targetRow = dataRows[targetRowIndex];\n    const targetColumn = editableColumns[targetColIndex];\n    if (targetRow && targetColumn) {\n      // 触发目标单元格的编辑\n      setTimeout(() => {\n        const targetElement = document.querySelector(`[data-row-id=\"${targetRow.id}\"][data-field=\"${targetColumn.field}\"]`);\n        if (targetElement) {\n          targetElement.dispatchEvent(new Event('dblclick', {\n            bubbles: true\n          }));\n        }\n      }, 50);\n    }\n  }, [columnOrder, memoGridData]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 当新数据到来时，应用自动填充和移除逻辑\n  useEffect(() => {\n    if (data && data.length > 0) {\n      console.log('检测到新数据，应用自动填充逻辑');\n\n      // 应用自动填充逻辑到新数据\n      const updatedData = data.map((row, index) => {\n        // 检查是否需要移除（is_havent_close_job为true）\n        const shouldRemove = row.is_havent_close_job === true;\n\n        // 自动填充_selected_remarks\n        const autoSelectedRemarks = autoFillSelectedRemarks(row);\n        return {\n          ...row,\n          id: index,\n          isTotal: row.NO === 'TOTAL',\n          _selected_remarks: autoSelectedRemarks,\n          _removed: shouldRemove\n        };\n      });\n      setGridData(updatedData);\n      setOriginalData([...updatedData]);\n      console.log('自动填充完成，移除的行数:', updatedData.filter(row => row._removed).length);\n    }\n  }, [data]);\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 处理REMARKS选项拖拽\n  const handleRemarksOptionDragEnd = useCallback(result => {\n    if (!result.destination) {\n      return;\n    }\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n    setRemarksOptions(prev => {\n      const newOptions = Array.from(prev);\n      const [reorderedItem] = newOptions.splice(sourceIndex, 1);\n      newOptions.splice(destinationIndex, 0, reorderedItem);\n      console.log('REMARKS选项已重新排序');\n      return newOptions;\n    });\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row.REMARKS || '',\n        // 保持后端传来的原始REMARKS数据\n        _selected_remarks: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        // 用于生成文档的选择的remarks\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1312,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1311,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1384,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1390,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1411,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1437,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1444,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1493,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1489,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1513,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1516,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1512,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1525,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1524,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1532,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1531,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1540,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1539,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1523,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1509,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1508,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1507,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1559,\n                columnNumber: 17\n              }, this), \"\\u6570\\u636E\\u641C\\u7D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1558,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1565,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: searchColumn,\n                  label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n                  onChange: e => setSearchColumn(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u5168\\u90E8\\u5217\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1571,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"NO\",\n                    children: \"NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1572,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DATE\",\n                    children: \"DATE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1573,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"VEHICLE NO\",\n                    children: \"VEHICLE NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1574,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"RO NO\",\n                    children: \"RO NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1575,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"KM\",\n                    children: \"KM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1576,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"REMARKS\",\n                    children: \"REMARKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1577,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"MAXCHECK\",\n                    children: \"HOURS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1578,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMMISSION\",\n                    children: \"AMOUNT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1579,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1566,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n                value: searchText,\n                onChange: e => setSearchText(e.target.value),\n                sx: {\n                  flexGrow: 1,\n                  minWidth: 200\n                },\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1592,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1591,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setSearchText(''),\n                      edge: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1602,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1597,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1596,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1583,\n                columnNumber: 17\n              }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1610,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1563,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1620,\n                columnNumber: 17\n              }, this), \"\\u64CD\\u4F5C\\u9009\\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"success\",\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1628,\n                  columnNumber: 30\n                }, this),\n                onClick: handleDownload,\n                children: \"\\u4E0B\\u8F7DExcel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1637,\n                  columnNumber: 53\n                }, this) : /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1637,\n                  columnNumber: 102\n                }, this),\n                onClick: generateDocument,\n                disabled: isGeneratingDocument,\n                children: isGeneratingDocument ? '生成中...' : '生成Excel文档'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1634,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1647,\n                  columnNumber: 30\n                }, this),\n                onClick: handleCleanup,\n                children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1644,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1555,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1554,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragDropContext, {\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease'\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  height: 48\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    width: 40,\n                    fontWeight: 'bold',\n                    height: 48,\n                    padding: '8px'\n                  },\n                  children: \"\\u62D6\\u62FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1682,\n                  columnNumber: 19\n                }, this), columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'bold',\n                    backgroundColor: 'background.default',\n                    borderRight: '2px solid',\n                    borderColor: 'divider',\n                    height: 48,\n                    padding: '8px',\n                    '&:last-child': {\n                      borderRight: 'none'\n                    }\n                  },\n                  children: column.field !== 'ACTION' && column.field !== 'REMARKS' ? /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                    active: sortConfig.field === column.field,\n                    direction: sortConfig.field === column.field ? sortConfig.direction : 'asc',\n                    onClick: () => handleSort(column.field),\n                    sx: {\n                      '& .MuiTableSortLabel-icon': {\n                        fontSize: '1rem'\n                      }\n                    },\n                    children: column.headerName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1707,\n                    columnNumber: 25\n                  }, this) : column.headerName\n                }, column.field, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1691,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1680,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Droppable, {\n              droppableId: \"table-body\",\n              children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableBody, {\n                ref: provided.innerRef,\n                ...provided.droppableProps,\n                sx: {\n                  backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                  transition: 'background-color 0.2s ease',\n                  borderTop: '2px solid',\n                  borderColor: 'divider'\n                },\n                children: [memoGridData.filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                .slice(paginationModel.page * paginationModel.pageSize, (paginationModel.page + 1) * paginationModel.pageSize).map((row, index) => /*#__PURE__*/_jsxDEV(DraggableTableRow, {\n                  row: row,\n                  index: index,\n                  columns: columns,\n                  onCellEdit: handleCellEdit,\n                  onNavigateCell: handleNavigateCell\n                }, row.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 25\n                }, this)), memoGridData.find(row => row.NO === 'TOTAL') && /*#__PURE__*/_jsxDEV(TableRow, {\n                  className: \"total-row\",\n                  sx: {\n                    backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                    fontWeight: 'bold',\n                    '& .MuiTableCell-root': {\n                      fontWeight: 'bold',\n                      borderTop: '2px solid',\n                      borderColor: 'primary.main'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    sx: {\n                      width: 40\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1768,\n                    columnNumber: 25\n                  }, this), columns.map(column => {\n                    const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                    const value = totalRow ? totalRow[column.field] : '';\n                    return /*#__PURE__*/_jsxDEV(TableCell, {\n                      sx: {\n                        padding: '8px'\n                      },\n                      children: column.renderCell ? column.renderCell({\n                        row: totalRow,\n                        value\n                      }) : value\n                    }, column.field, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1775,\n                      columnNumber: 29\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1759,\n                  columnNumber: 23\n                }, this), provided.placeholder]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1729,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1727,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1678,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1677,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          component: \"div\",\n          count: memoGridData.filter(row => row.NO !== 'TOTAL').length,\n          page: paginationModel.page,\n          onPageChange: (event, newPage) => {\n            setPaginationModel(prev => ({\n              ...prev,\n              page: newPage\n            }));\n          },\n          rowsPerPage: paginationModel.pageSize,\n          onRowsPerPageChange: event => {\n            const newPageSize = parseInt(event.target.value, 10);\n            setPaginationModel({\n              page: 0,\n              pageSize: newPageSize\n            });\n            localStorage.setItem('dataGridPageSize', newPageSize.toString());\n          },\n          rowsPerPageOptions: [25, 50, 100],\n          labelRowsPerPage: \"\\u6BCF\\u9875\\u884C\\u6570:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} 共 ${count} 条`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1791,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1660,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1659,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1823,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1825,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1824,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1822,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1821,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1836,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1842,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1840,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1852,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1850,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1839,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1835,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(DragDropContext, {\n          onDragEnd: handleRemarksOptionDragEnd,\n          children: /*#__PURE__*/_jsxDEV(Droppable, {\n            droppableId: \"remarks-options\",\n            children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(Box, {\n              ref: provided.innerRef,\n              ...provided.droppableProps,\n              sx: {\n                height: 300,\n                overflowY: 'auto',\n                backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'transparent',\n                transition: 'background-color 0.2s ease'\n              },\n              children: [remarksOptions.map((option, index) => /*#__PURE__*/_jsxDEV(DraggableRemarksOption, {\n                option: option,\n                index: index,\n                onSelect: selectRemarkOption,\n                onDelete: deleteOption\n              }, `${option}-${index}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1878,\n                columnNumber: 21\n              }, this)), provided.placeholder]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1867,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1865,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1864,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1863,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1893,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1892,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1812,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1907,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1909,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1908,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1922,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1923,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1921,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1898,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1505,\n    columnNumber: 5\n  }, this);\n};\n_s3(ResultDisplay, \"Co1cmnYC/tvz7vi1rq5HacyejHc=\");\n_c7 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"DraggableRemarksOption$React.memo\");\n$RefreshReg$(_c2, \"DraggableRemarksOption\");\n$RefreshReg$(_c3, \"DraggableTableRow$React.memo\");\n$RefreshReg$(_c4, \"DraggableTableRow\");\n$RefreshReg$(_c5, \"RemarkChip$React.memo\");\n$RefreshReg$(_c6, \"RemarkChip\");\n$RefreshReg$(_c7, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "TableSortLabel", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "SettingsIcon", "axios", "API_URL", "FixedSizeList", "DragDropContext", "Droppable", "Draggable", "DragIndicatorIcon", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DraggableRemarksOption", "memo", "_c", "option", "index", "onSelect", "onDelete", "draggableId", "children", "provided", "snapshot", "ref", "innerRef", "draggableProps", "disablePadding", "sx", "transform", "isDragging", "transition", "boxShadow", "zIndex", "backgroundColor", "borderRadius", "secondaryAction", "edge", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dragHandleProps", "display", "alignItems", "px", "cursor", "color", "fontSize", "pl", "primary", "_c2", "DraggableTableRow", "_s", "_c3", "row", "columns", "onCellEdit", "onNavigateCell", "editingCell", "setEditingCell", "editValue", "setEditValue", "handleCellClick", "columnField", "currentValue", "column", "find", "col", "field", "editable", "isTotal", "_removed", "handleCellSave", "id", "handleKeyPress", "e", "key", "preventDefault", "console", "log", "shift<PERSON>ey", "toString", "textDecoration", "width", "textAlign", "map", "value", "isEditing", "padding", "onDoubleClick", "size", "onChange", "target", "onKeyDown", "onBlur", "autoFocus", "fullWidth", "variant", "borderBottom", "fontFamily", "renderCell", "_c4", "DEFAULT_REMARKS_OPTIONS", "RemarkChip", "_s2", "_c5", "rowId", "text", "isSelected", "uiState", "setUiState", "handleClick", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "textTransform", "overflow", "textOverflow", "whiteSpace", "height", "lineHeight", "_c6", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s3", "columnOrder", "headerName", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "getSelectedMonth", "commissionState", "selectedWorksheet", "match", "monthName", "year", "monthMap", "monthNumber", "toUpperCase", "error", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "sortConfig", "setSortConfig", "direction", "handleSort", "isAsc", "newDirection", "debouncedSaveToLocalStorage", "setItem", "stringify", "length", "debouncedNotifyParent", "recalculateTotal", "totalRow", "NO", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "handleDragEnd", "result", "destination", "sourceIndex", "source", "destinationIndex", "setGridData", "prev", "newData", "nonTotalRows", "removed", "splice", "for<PERSON>ach", "reorderedData", "handleCellEdit", "newValue", "updatedData", "updatedRow", "numericFields", "includes", "undefined", "numValue", "isNaN", "originalData", "setOriginalData", "autoFillSelectedRemarks", "maxcheck", "parseFloat", "MAXCHECK", "km", "KM", "remarks", "REMARKS", "trim", "remarksUpper", "processedData", "<PERSON><PERSON><PERSON><PERSON>", "is_havent_close_job", "autoSelectedRemarks", "_selected_remarks", "gridData", "validatedData", "processedWithIds", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "sort", "a", "b", "aValue", "bValue", "aStr", "bStr", "localeCompare", "memoGridData", "handleNavigateCell", "currentRowId", "current<PERSON><PERSON>", "editableColumns", "dataRows", "currentRowIndex", "findIndex", "currentColIndex", "targetRowIndex", "targetColIndex", "Math", "min", "max", "targetRow", "targetColumn", "targetElement", "document", "querySelector", "dispatchEvent", "Event", "bubbles", "timer", "setPaginationModel", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "handleDownload", "startsWith", "downloadUrl", "link", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "deleteOption", "item", "handleRemarksOptionDragEnd", "newOptions", "from", "reorderedItem", "handleRemoveRow", "noCounter", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "newId", "currentRow", "newRowNo", "newRow", "DATE", "generateDocument", "filteredRows", "docData", "split", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "src", "Error", "handleRemarksClick", "hasOwnProperty", "flex", "params", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "startIcon", "fontWeight", "Boolean", "dataLength", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "container", "xs", "md", "sm", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "disabled", "onDragEnd", "borderRight", "borderColor", "<PERSON><PERSON><PERSON><PERSON>", "active", "droppableId", "droppableProps", "isDraggingOver", "borderTop", "slice", "className", "component", "count", "onPageChange", "event", "newPage", "rowsPerPage", "onRowsPerPageChange", "newPageSize", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "to", "onClose", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pb", "control", "checked", "dividers", "p", "overflowY", "margin", "type", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel,\n  Grid,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  TableSortLabel\n} from '@mui/material';\n\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\n\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 可拖拽的REMARKS选项组件\nconst DraggableRemarksOption = React.memo(({ option, index, onSelect, onDelete }) => {\n  return (\n    <Draggable draggableId={`remarks-${option}-${index}`} index={index}>\n      {(provided, snapshot) => (\n        <ListItem\n          ref={provided.innerRef}\n          {...provided.draggableProps}\n          disablePadding\n          sx={{\n            transform: snapshot.isDragging ? 'rotate(2deg)' : 'rotate(0deg)',\n            transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n            boxShadow: snapshot.isDragging ? '0 8px 16px rgba(0,0,0,0.15)' : 'none',\n            zIndex: snapshot.isDragging ? 1000 : 'auto',\n            backgroundColor: snapshot.isDragging ? 'action.hover' : 'transparent',\n            borderRadius: snapshot.isDragging ? 1 : 0,\n            '&:hover': {\n              backgroundColor: 'action.hover',\n            },\n          }}\n          secondaryAction={(option !== 'None') && (\n            <IconButton\n              edge=\"end\"\n              aria-label=\"delete\"\n              onClick={() => onDelete(option)}\n              sx={{ mr: 1 }}\n            >\n              <DeleteIcon />\n            </IconButton>\n          )}\n        >\n          {/* 拖拽手柄 */}\n          <Box\n            {...provided.dragHandleProps}\n            sx={{\n              display: 'flex',\n              alignItems: 'center',\n              px: 1,\n              cursor: 'grab',\n              '&:active': {\n                cursor: 'grabbing',\n              },\n            }}\n          >\n            <DragIndicatorIcon sx={{ color: 'text.secondary', fontSize: '1.2rem' }} />\n          </Box>\n\n          <ListItemButton onClick={() => onSelect(option)} sx={{ pl: 0 }}>\n            <ListItemText primary={option} />\n          </ListItemButton>\n        </ListItem>\n      )}\n    </Draggable>\n  );\n});\n\n// 拖拽表格行组件\nconst DraggableTableRow = React.memo(({ row, index, columns, onCellEdit, onNavigateCell }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n\n  const handleCellSave = (columnField) => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      // 保存数据\n      handleCellSave(columnField);\n      // 导航到下一行同列\n      console.log(e);\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'up' : 'down');\n        }, 100);\n      }\n    } else if (e.key === 'Tab') {\n      e.preventDefault();\n      handleCellSave(columnField);\n      // 导航到下一列\n      if (onNavigateCell) {\n        setTimeout(() => {\n          onNavigateCell(row.id, columnField, e.shiftKey ? 'left' : 'right');\n        }, 100);\n      }\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n\n  return (\n    <Draggable draggableId={row.id.toString()} index={index}>\n      {(provided, snapshot) => (\n        <TableRow\n          ref={provided.innerRef}\n          {...provided.draggableProps}\n          sx={{\n            backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n            transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n            boxShadow: snapshot.isDragging ? 3 : 0,\n            transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n            '&.removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            },\n            ...(snapshot.isDragging && {\n              zIndex: 1000,\n            }),\n            ...(row._removed && {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            }),\n          }}\n        >\n          {/* 拖拽手柄 */}\n          <TableCell\n            {...provided.dragHandleProps}\n            sx={{\n              width: 40,\n              cursor: 'grab',\n              '&:active': { cursor: 'grabbing' },\n              textAlign: 'center',\n            }}\n          >\n            <DragIndicatorIcon sx={{ color: 'text.secondary' }} />\n          </TableCell>\n\n          {/* 数据列 */}\n          {columns.map((column) => {\n            const value = row[column.field];\n            const isEditing = editingCell === column.field;\n\n            return (\n              <TableCell\n                key={column.field}\n                data-row-id={row.id}\n                data-field={column.field}\n                sx={{\n                  padding: '8px',\n                  cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default',\n                }}\n                // onClick={() => handleCellClick(column.field, value)}\n                onDoubleClick={() => handleCellClick(column.field, value)}\n              >\n                {isEditing ? (\n                  <TextField\n                    size=\"small\"\n                    value={editValue}\n                    onChange={(e) => setEditValue(e.target.value)}\n                    onKeyDown={(e) => handleKeyPress(e, column.field)}\n                    onBlur={() => handleCellSave(column.field)}\n                    autoFocus\n                    fullWidth\n                    variant=\"standard\"\n                    sx={{\n                      '& .MuiInput-root': {\n                        fontSize: 'inherit',\n                        '&:before': {\n                          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',\n                        },\n                        '&:hover:before': {\n                          borderBottom: '2px solid rgba(0, 0, 0, 0.87)',\n                        },\n                        '&:after': {\n                          borderBottom: '2px solid #1976d2',\n                        },\n                      },\n                      '& .MuiInput-input': {\n                        padding: '4px 0',\n                        fontSize: 'inherit',\n                        fontFamily: 'inherit',\n                      },\n                    }}\n                  />\n                ) : (\n                  column.renderCell ? column.renderCell({ row, value }) : value\n                )}\n              </TableCell>\n            );\n          })}\n        </TableRow>\n      )}\n    </Draggable>\n  );\n});\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK\",\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"DVR SD CARD\",\n  \"FRONT DISC BRAKE PAD (BOTH SIDES)\",\n  \"REAR DISC BRAKE PAD (BOTH SIDES)\",\n  \"FUEL FILLER OPENING LID HINGE SPRING\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = useMemo(() => [\n    { field: 'NO', headerName: 'NO', editable: false, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ], []);\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',\n            'MAY': '05', 'JUNE': '06', 'JULY': '07', 'AUG': '08',\n            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 排序状态\n  const [sortConfig, setSortConfig] = useState({ field: null, direction: 'asc' });\n\n  // 排序处理函数\n  const handleSort = (field) => {\n    const isAsc = sortConfig.field === field && sortConfig.direction === 'asc';\n    const newDirection = isAsc ? 'desc' : 'asc';\n    setSortConfig({ field, direction: newDirection });\n  };\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback((result) => {\n    if (!result.destination) {\n      return;\n    }\n\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = { ...row, [field]: newValue };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n\n          return updatedRow;\n        }\n        return row;\n      });\n\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 自动填充_selected_remarks的函数\n  const autoFillSelectedRemarks = (row) => {\n    const maxcheck = parseFloat(row.MAXCHECK) || 0;\n    const km = parseFloat(row.KM) || 0;\n    const remarks = row.REMARKS || '';\n\n    // 首先检查KM值的特殊情况\n    if (km === 1) {\n      return 'MAXCHECK COMPULSORY 1ST SERVICE';\n    }\n    if (km === 5) {\n      return 'MAXCHECK COMPULSORY 2ND SERVICE';\n    }\n\n    // 如果有REMARKS内容，根据内容判断\n    if (remarks.trim()) {\n      const remarksUpper = remarks.toUpperCase();\n\n      // 检查是否包含特定关键词（按优先级排序，更具体的在前面）\n      if (remarksUpper.includes('FUEL FILLER OPENING LID HINGE SPRING')) return 'FUEL FILLER OPENING LID HINGE SPRING';\n      if (remarksUpper.includes('DVR')) return 'DVR SD CARD';\n      if (remarksUpper.includes('ATF')) return 'ATF REPLACEMENT';\n      if (remarksUpper.includes('SPARK PLUG')) return 'SPARK PLUG';\n\n      // 刹车片相关 - 更精确的匹配\n      if (remarksUpper.includes('FR B/PAD') || remarksUpper.includes('FRONT DISC BRAKE PAD')) return 'FRONT DISC BRAKE PAD (BOTH SIDES)';\n      if (remarksUpper.includes('REAR B/PAD') || remarksUpper.includes('REAR DISC BRAKE PAD')) return 'REAR DISC BRAKE PAD (BOTH SIDES)';\n      if (remarksUpper.includes('B/PAD') && !remarksUpper.includes('FR') && !remarksUpper.includes('REAR')) return 'REPLACE BRAKE PADS';\n      if (remarksUpper.includes('BRAKE PAD')) return 'REPLACE BRAKE PADS';\n\n      // 电池相关\n      if (remarksUpper.includes('BT ') || remarksUpper.includes('BATTERY')) return 'REPLACE BATTERY';\n\n      // 雨刷相关\n      if (remarksUpper.includes('W/RUBBER') || remarksUpper.includes('WIPER')) return 'REPLACE WIPER RUBBER';\n\n      // CBU CAR 相关 - 如果包含CBU CAR但没有其他明确的项目，可能是MAXCHECK相关\n      if (remarksUpper.includes('CBU CAR') && !remarksUpper.includes('B/PAD') && !remarksUpper.includes('ATF') && !remarksUpper.includes('BT')) {\n        // 根据MAXCHECK值判断CBU CAR的类型\n        if (maxcheck >= 1.0 && maxcheck <= 1.6) {\n          return 'MAXCHECK';\n        } else if (maxcheck >= 1.7 && maxcheck <= 2.2) {\n          return 'MAXCHECK ADVANCE';\n        } else if (maxcheck >= 2.3 && maxcheck <= 4.0) {\n          return 'MAXCHECK ADVANCE PLUS';\n        }\n      }\n    }\n\n    // 检查后端返回的字段\n    if (row['EGN OIL']) return 'EGN OIL';\n    if (row['O/FLTER']) return 'O/FLTER';\n    if (row['ATF']) return 'ATF REPLACEMENT';\n    if (row['BT']) return 'REPLACE BATTERY';\n    if (row['EGN FLUSH']) return 'EGN FLUSH';\n    if (row['FR BR PAD']) return 'FRONT DISC BRAKE PAD (BOTH SIDES)';\n    if (row['R BR PAD']) return 'REAR DISC BRAKE PAD (BOTH SIDES)';\n    if (row['D FILTER']) return 'D FILTER';\n    if (row['P/SHAFT']) return 'P/SHAFT';\n    if (row['W/RUBBER']) return 'REPLACE WIPER RUBBER';\n    if (row['SPARK PLUG']) return 'SPARK PLUG';\n    if (row['OTHERS']) return row['OTHERS']; // OTHERS返回原始值\n\n    // 根据MAXCHECK值判断\n    if (maxcheck >= 1.0 && maxcheck <= 1.6) {\n      return 'MAXCHECK';\n    } else if (maxcheck >= 1.7 && maxcheck <= 2.2) {\n      return 'MAXCHECK ADVANCE';\n    } else if (maxcheck >= 2.3 && maxcheck <= 4.0) {\n      return 'MAXCHECK ADVANCE PLUS';\n    }\n\n    // 默认返回空\n    return '';\n  };\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 检查是否需要移除（is_havent_close_job为true）\n    const shouldRemove = row.is_havent_close_job === true;\n\n    // 自动填充_selected_remarks\n    const autoSelectedRemarks = autoFillSelectedRemarks(row);\n\n    return {\n      ...row,\n      _selected_remarks: autoSelectedRemarks,\n      _removed: shouldRemove\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在，如果为空则自动填充\n        if (row._selected_remarks === undefined || row._selected_remarks === '') {\n          row._selected_remarks = autoFillSelectedRemarks(row);\n        }\n\n        // 检查是否需要移除（is_havent_close_job为true）\n        if (row.is_havent_close_job === true && !row._removed) {\n          row._removed = true;\n        }\n\n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 搜索和排序过滤逻辑\n  const filteredGridData = useMemo(() => {\n    let data = gridData || [];\n\n    // 搜索过滤\n    if (debouncedSearchText.trim()) {\n      const searchLower = debouncedSearchText.toLowerCase();\n      data = data.filter(row => {\n        if (searchColumn === 'all') {\n          // 搜索所有列\n          return Object.values(row).some(value =>\n            value && value.toString().toLowerCase().includes(searchLower)\n          );\n        } else {\n          // 搜索特定列\n          const cellValue = row[searchColumn];\n          return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n        }\n      });\n    }\n\n    // 排序处理\n    if (sortConfig.field) {\n      data = [...data].sort((a, b) => {\n        // TOTAL行始终在最后\n        if (a.NO === 'TOTAL') return 1;\n        if (b.NO === 'TOTAL') return -1;\n\n        const aValue = a[sortConfig.field];\n        const bValue = b[sortConfig.field];\n\n        // 处理数字类型\n        if (typeof aValue === 'number' && typeof bValue === 'number') {\n          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n        }\n\n        // 处理字符串类型\n        const aStr = String(aValue || '').toLowerCase();\n        const bStr = String(bValue || '').toLowerCase();\n\n        if (sortConfig.direction === 'asc') {\n          return aStr.localeCompare(bStr);\n        } else {\n          return bStr.localeCompare(aStr);\n        }\n      });\n    }\n\n    return data;\n  }, [gridData, debouncedSearchText, searchColumn, sortConfig]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 处理单元格导航\n  const handleNavigateCell = useCallback((currentRowId, currentField, direction) => {\n    const editableColumns = columnOrder.filter(col =>\n      col.editable && col.field !== 'NO' && col.field !== 'ACTION' && col.field !== 'REMARKS'\n    );\n    const dataRows = memoGridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n    const currentRowIndex = dataRows.findIndex(row => row.id === currentRowId);\n    const currentColIndex = editableColumns.findIndex(col => col.field === currentField);\n\n    if (currentRowIndex === -1 || currentColIndex === -1) return;\n\n    let targetRowIndex = currentRowIndex;\n    let targetColIndex = currentColIndex;\n\n    switch (direction) {\n      case 'down':\n        targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n        break;\n      case 'up':\n        targetRowIndex = Math.max(currentRowIndex - 1, 0);\n        break;\n      case 'right':\n        // Tab键：如果已经是最后一列，跳到下一行的第一个可编辑列\n        if (currentColIndex >= editableColumns.length - 1) {\n          // 跳到下一行的第一列\n          targetRowIndex = Math.min(currentRowIndex + 1, dataRows.length - 1);\n          targetColIndex = 0;\n        } else {\n          // 否则跳到同行的下一列\n          targetColIndex = currentColIndex + 1;\n        }\n        break;\n      case 'left':\n        // Shift+Tab：如果已经是第一列，跳到上一行的最后一个可编辑列\n        if (currentColIndex <= 0) {\n          // 跳到上一行的最后一列\n          targetRowIndex = Math.max(currentRowIndex - 1, 0);\n          targetColIndex = editableColumns.length - 1;\n        } else {\n          // 否则跳到同行的上一列\n          targetColIndex = currentColIndex - 1;\n        }\n        break;\n      default:\n        return;\n    }\n\n    const targetRow = dataRows[targetRowIndex];\n    const targetColumn = editableColumns[targetColIndex];\n\n    if (targetRow && targetColumn) {\n      // 触发目标单元格的编辑\n      setTimeout(() => {\n        const targetElement = document.querySelector(\n          `[data-row-id=\"${targetRow.id}\"][data-field=\"${targetColumn.field}\"]`\n        );\n        if (targetElement) {\n          targetElement.dispatchEvent(new Event('dblclick', { bubbles: true }));\n        }\n      }, 50);\n    }\n  }, [columnOrder, memoGridData]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 当新数据到来时，应用自动填充和移除逻辑\n  useEffect(() => {\n    if (data && data.length > 0) {\n      console.log('检测到新数据，应用自动填充逻辑');\n\n      // 应用自动填充逻辑到新数据\n      const updatedData = data.map((row, index) => {\n        // 检查是否需要移除（is_havent_close_job为true）\n        const shouldRemove = row.is_havent_close_job === true;\n\n        // 自动填充_selected_remarks\n        const autoSelectedRemarks = autoFillSelectedRemarks(row);\n\n        return {\n          ...row,\n          id: index,\n          isTotal: row.NO === 'TOTAL',\n          _selected_remarks: autoSelectedRemarks,\n          _removed: shouldRemove\n        };\n      });\n\n      setGridData(updatedData);\n      setOriginalData([...updatedData]);\n      console.log('自动填充完成，移除的行数:', updatedData.filter(row => row._removed).length);\n    }\n  }, [data]);\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 处理REMARKS选项拖拽\n  const handleRemarksOptionDragEnd = useCallback((result) => {\n    if (!result.destination) {\n      return;\n    }\n\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    setRemarksOptions(prev => {\n      const newOptions = Array.from(prev);\n      const [reorderedItem] = newOptions.splice(sourceIndex, 1);\n      newOptions.splice(destinationIndex, 0, reorderedItem);\n      console.log('REMARKS选项已重新排序');\n      return newOptions;\n    });\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row.REMARKS || '', // 保持后端传来的原始REMARKS数据\n        _selected_remarks: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '', // 用于生成文档的选择的remarks\n        HOURS: typeof row.MAXCHECK === 'number' ?\n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) :\n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 处理结果概览 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 数据搜索和操作选项 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={3}>\n            {/* 数据搜索 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SearchIcon sx={{ color: 'primary.main' }} />\n                数据搜索\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n                <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n                  <InputLabel>搜索范围</InputLabel>\n                  <Select\n                    value={searchColumn}\n                    label=\"搜索范围\"\n                    onChange={(e) => setSearchColumn(e.target.value)}\n                  >\n                    <MenuItem value=\"all\">全部列</MenuItem>\n                    <MenuItem value=\"NO\">NO</MenuItem>\n                    <MenuItem value=\"DATE\">DATE</MenuItem>\n                    <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                    <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                    <MenuItem value=\"KM\">KM</MenuItem>\n                    <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                    <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                    <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n                  </Select>\n                </FormControl>\n\n                <TextField\n                  size=\"small\"\n                  placeholder=\"输入搜索内容...\"\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  sx={{ flexGrow: 1, minWidth: 200 }}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    ),\n                    endAdornment: searchText && (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => setSearchText('')}\n                          edge=\"end\"\n                        >\n                          <ClearIcon />\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {searchText && (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n                  </Typography>\n                )}\n              </Stack>\n            </Grid>\n\n            {/* 操作选项 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SettingsIcon sx={{ color: 'primary.main' }} />\n                操作选项\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>\n                <Button\n                  variant=\"contained\"\n                  color=\"success\"\n                  startIcon={<DownloadIcon />}\n                  onClick={handleDownload}\n                >\n                  下载Excel\n                </Button>\n\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <TableViewIcon />}\n                  onClick={generateDocument}\n                  disabled={isGeneratingDocument}\n                >\n                  {isGeneratingDocument ? '生成中...' : '生成Excel文档'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<RestartAltIcon />}\n                  onClick={handleCleanup}\n                >\n                  重新开始\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n      \n      {/* 拖拽数据表格 */}\n      <DragDropContext onDragEnd={handleDragEnd}>\n        <Paper sx={{\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000,\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease',\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider',\n          },\n        }}>\n          <TableContainer>\n            <Table stickyHeader>\n              <TableHead>\n                <TableRow sx={{ height: 48 }}>\n                  {/* 拖拽手柄列 */}\n                  <TableCell sx={{\n                    width: 40,\n                    fontWeight: 'bold',\n                    height: 48,\n                    padding: '8px',\n                  }}>\n                    拖拽\n                  </TableCell>\n                  {columns.map((column) => (\n                    <TableCell\n                      key={column.field}\n                      sx={{\n                        fontWeight: 'bold',\n                        backgroundColor: 'background.default',\n                        borderRight: '2px solid',\n                        borderColor: 'divider',\n                        height: 48,\n                        padding: '8px',\n                        '&:last-child': {\n                          borderRight: 'none',\n                        },\n                      }}\n                    >\n                      {/* 可排序的列添加排序功能 */}\n                      {column.field !== 'ACTION' && column.field !== 'REMARKS' ? (\n                        <TableSortLabel\n                          active={sortConfig.field === column.field}\n                          direction={sortConfig.field === column.field ? sortConfig.direction : 'asc'}\n                          onClick={() => handleSort(column.field)}\n                          sx={{\n                            '& .MuiTableSortLabel-icon': {\n                              fontSize: '1rem',\n                            },\n                          }}\n                        >\n                          {column.headerName}\n                        </TableSortLabel>\n                      ) : (\n                        column.headerName\n                      )}\n                    </TableCell>\n                  ))}\n                </TableRow>\n              </TableHead>\n\n              <Droppable droppableId=\"table-body\">\n                {(provided, snapshot) => (\n                  <TableBody\n                    ref={provided.innerRef}\n                    {...provided.droppableProps}\n                    sx={{\n                      backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                      transition: 'background-color 0.2s ease',\n                      borderTop: '2px solid',\n                      borderColor: 'divider',\n                    }}\n                  >\n                    {/* 只显示当前页的数据 */}\n                    {memoGridData\n                      .filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                      .slice(\n                        paginationModel.page * paginationModel.pageSize,\n                        (paginationModel.page + 1) * paginationModel.pageSize\n                      )\n                      .map((row, index) => (\n                        <DraggableTableRow\n                          key={row.id}\n                          row={row}\n                          index={index}\n                          columns={columns}\n                          onCellEdit={handleCellEdit}\n                          onNavigateCell={handleNavigateCell}\n                        />\n                      ))}\n\n                    {/* TOTAL行单独显示在最后 */}\n                    {memoGridData.find(row => row.NO === 'TOTAL') && (\n                      <TableRow className=\"total-row\" sx={{\n                        backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                        fontWeight: 'bold',\n                        '& .MuiTableCell-root': {\n                          fontWeight: 'bold',\n                          borderTop: '2px solid',\n                          borderColor: 'primary.main',\n                        },\n                      }}>\n                        <TableCell sx={{ width: 40 }}>\n                          {/* TOTAL行不显示拖拽手柄 */}\n                        </TableCell>\n                        {columns.map((column) => {\n                          const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                          const value = totalRow ? totalRow[column.field] : '';\n                          return (\n                            <TableCell key={column.field} sx={{ padding: '8px' }}>\n                              {column.renderCell ? column.renderCell({ row: totalRow, value }) : value}\n                            </TableCell>\n                          );\n                        })}\n                      </TableRow>\n                    )}\n\n                    {provided.placeholder}\n                  </TableBody>\n                )}\n              </Droppable>\n            </Table>\n          </TableContainer>\n\n          {/* 分页控件 */}\n          <TablePagination\n            component=\"div\"\n            count={memoGridData.filter(row => row.NO !== 'TOTAL').length}\n            page={paginationModel.page}\n            onPageChange={(event, newPage) => {\n              setPaginationModel(prev => ({ ...prev, page: newPage }));\n            }}\n            rowsPerPage={paginationModel.pageSize}\n            onRowsPerPageChange={(event) => {\n              const newPageSize = parseInt(event.target.value, 10);\n              setPaginationModel({ page: 0, pageSize: newPageSize });\n              localStorage.setItem('dataGridPageSize', newPageSize.toString());\n            }}\n            rowsPerPageOptions={[25, 50, 100]}\n            labelRowsPerPage=\"每页行数:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}\n          />\n        </Paper>\n      </DragDropContext>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <DragDropContext onDragEnd={handleRemarksOptionDragEnd}>\n            <Droppable droppableId=\"remarks-options\">\n              {(provided, snapshot) => (\n                <Box\n                  ref={provided.innerRef}\n                  {...provided.droppableProps}\n                  sx={{\n                    height: 300,\n                    overflowY: 'auto',\n                    backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'transparent',\n                    transition: 'background-color 0.2s ease',\n                  }}\n                >\n                  {remarksOptions.map((option, index) => (\n                    <DraggableRemarksOption\n                      key={`${option}-${index}`}\n                      option={option}\n                      index={index}\n                      onSelect={selectRemarkOption}\n                      onDelete={deleteOption}\n                    />\n                  ))}\n                  {provided.placeholder}\n                </Box>\n              )}\n            </Droppable>\n          </DragDropContext>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,cAAc,QACT,eAAe;AAEtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,eAAe,EAAEC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACzE,OAAOC,iBAAiB,MAAM,mCAAmC;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,sBAAsB,gBAAGxE,KAAK,CAACyE,IAAI,CAAAC,EAAA,GAACA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EACnF,oBACEhB,OAAA,CAACH,SAAS;IAACoB,WAAW,EAAE,WAAWJ,MAAM,IAAIC,KAAK,EAAG;IAACA,KAAK,EAAEA,KAAM;IAAAI,QAAA,EAChEA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBpB,OAAA,CAAChD,QAAQ;MACPqE,GAAG,EAAEF,QAAQ,CAACG,QAAS;MAAA,GACnBH,QAAQ,CAACI,cAAc;MAC3BC,cAAc;MACdC,EAAE,EAAE;QACFC,SAAS,EAAEN,QAAQ,CAACO,UAAU,GAAG,cAAc,GAAG,cAAc;QAChEC,UAAU,EAAE,2CAA2C;QACvDC,SAAS,EAAET,QAAQ,CAACO,UAAU,GAAG,6BAA6B,GAAG,MAAM;QACvEG,MAAM,EAAEV,QAAQ,CAACO,UAAU,GAAG,IAAI,GAAG,MAAM;QAC3CI,eAAe,EAAEX,QAAQ,CAACO,UAAU,GAAG,cAAc,GAAG,aAAa;QACrEK,YAAY,EAAEZ,QAAQ,CAACO,UAAU,GAAG,CAAC,GAAG,CAAC;QACzC,SAAS,EAAE;UACTI,eAAe,EAAE;QACnB;MACF,CAAE;MACFE,eAAe,EAAGpB,MAAM,KAAK,MAAM,iBACjCb,OAAA,CAAC5C,UAAU;QACT8E,IAAI,EAAC,KAAK;QACV,cAAW,QAAQ;QACnBC,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAACH,MAAM,CAAE;QAChCY,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,eAEdlB,OAAA,CAAClB,UAAU;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACZ;MAAAtB,QAAA,gBAGFlB,OAAA,CAACxD,GAAG;QAAA,GACE2E,QAAQ,CAACsB,eAAe;QAC5BhB,EAAE,EAAE;UACFiB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YACVA,MAAM,EAAE;UACV;QACF,CAAE;QAAA3B,QAAA,eAEFlB,OAAA,CAACF,iBAAiB;UAAC2B,EAAE,EAAE;YAAEqB,KAAK,EAAE,gBAAgB;YAAEC,QAAQ,EAAE;UAAS;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAENxC,OAAA,CAAC/C,cAAc;QAACkF,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAACF,MAAM,CAAE;QAACY,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAA9B,QAAA,eAC7DlB,OAAA,CAAC9C,YAAY;UAAC+F,OAAO,EAAEpC;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC,CAAC;;AAEF;AAAAU,GAAA,GAvDMxC,sBAAsB;AAwD5B,MAAMyC,iBAAiB,gBAAAC,EAAA,cAAGlH,KAAK,CAACyE,IAAI,CAAA0C,GAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,GAAG;EAAExC,KAAK;EAAEyC,OAAO;EAAEC,UAAU;EAAEC;AAAe,CAAC,KAAK;EAAAL,EAAA;EAC5F,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAGxH,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyH,SAAS,EAAEC,YAAY,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM2H,eAAe,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACrD;IACA,MAAMC,MAAM,GAAGV,OAAO,CAACW,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,WAAW,CAAC;IAC7D,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACI,QAAQ,IAAIf,GAAG,CAACgB,OAAO,IAAIhB,GAAG,CAACiB,QAAQ,IAAIR,WAAW,KAAK,IAAI,EAAE;MACtF;IACF;IAEAJ,cAAc,CAACI,WAAW,CAAC;IAC3BF,YAAY,CAACG,YAAY,IAAI,EAAE,CAAC;EAClC,CAAC;EAED,MAAMQ,cAAc,GAAIT,WAAW,IAAK;IACtC,IAAIP,UAAU,EAAE;MACdA,UAAU,CAACF,GAAG,CAACmB,EAAE,EAAEV,WAAW,EAAEH,SAAS,CAAC;IAC5C;IACAD,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACC,CAAC,EAAEZ,WAAW,KAAK;IACzC,IAAIY,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClB;MACAL,cAAc,CAACT,WAAW,CAAC;MAC3B;MACAe,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAIlB,cAAc,EAAE;QAClBhD,UAAU,CAAC,MAAM;UACfgD,cAAc,CAACH,GAAG,CAACmB,EAAE,EAAEV,WAAW,EAAEY,CAAC,CAACK,QAAQ,GAAG,IAAI,GAAG,MAAM,CAAC;QACjE,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,MAAM,IAAIL,CAAC,CAACC,GAAG,KAAK,KAAK,EAAE;MAC1BD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClBL,cAAc,CAACT,WAAW,CAAC;MAC3B;MACA,IAAIN,cAAc,EAAE;QAClBhD,UAAU,CAAC,MAAM;UACfgD,cAAc,CAACH,GAAG,CAACmB,EAAE,EAAEV,WAAW,EAAEY,CAAC,CAACK,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;QACpE,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,MAAM,IAAIL,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BjB,cAAc,CAAC,IAAI,CAAC;MACpBE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EAED,oBACE7D,OAAA,CAACH,SAAS;IAACoB,WAAW,EAAEqC,GAAG,CAACmB,EAAE,CAACQ,QAAQ,CAAC,CAAE;IAACnE,KAAK,EAAEA,KAAM;IAAAI,QAAA,EACrDA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBpB,OAAA,CAACxB,QAAQ;MACP6C,GAAG,EAAEF,QAAQ,CAACG,QAAS;MAAA,GACnBH,QAAQ,CAACI,cAAc;MAC3BE,EAAE,EAAE;QACFM,eAAe,EAAEX,QAAQ,CAACO,UAAU,GAAG,cAAc,GAAG,SAAS;QACjED,SAAS,EAAEN,QAAQ,CAACO,UAAU,GAAG,cAAc,GAAG,MAAM;QACxDE,SAAS,EAAET,QAAQ,CAACO,UAAU,GAAG,CAAC,GAAG,CAAC;QACtCC,UAAU,EAAER,QAAQ,CAACO,UAAU,GAAG,MAAM,GAAG,eAAe;QAC1D,eAAe,EAAE;UACfI,eAAe,EAAE,0BAA0B;UAC3Ce,KAAK,EAAE,eAAe;UACtBoC,cAAc,EAAE;QAClB,CAAC;QACD,IAAI9D,QAAQ,CAACO,UAAU,IAAI;UACzBG,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAIwB,GAAG,CAACiB,QAAQ,IAAI;UAClBxC,eAAe,EAAE,0BAA0B;UAC3Ce,KAAK,EAAE,eAAe;UACtBoC,cAAc,EAAE;QAClB,CAAC;MACH,CAAE;MAAAhE,QAAA,gBAGFlB,OAAA,CAAC3B,SAAS;QAAA,GACJ8C,QAAQ,CAACsB,eAAe;QAC5BhB,EAAE,EAAE;UACF0D,KAAK,EAAE,EAAE;UACTtC,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YAAEA,MAAM,EAAE;UAAW,CAAC;UAClCuC,SAAS,EAAE;QACb,CAAE;QAAAlE,QAAA,eAEFlB,OAAA,CAACF,iBAAiB;UAAC2B,EAAE,EAAE;YAAEqB,KAAK,EAAE;UAAiB;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAGXe,OAAO,CAAC8B,GAAG,CAAEpB,MAAM,IAAK;QACvB,MAAMqB,KAAK,GAAGhC,GAAG,CAACW,MAAM,CAACG,KAAK,CAAC;QAC/B,MAAMmB,SAAS,GAAG7B,WAAW,KAAKO,MAAM,CAACG,KAAK;QAE9C,oBACEpE,OAAA,CAAC3B,SAAS;UAER,eAAaiF,GAAG,CAACmB,EAAG;UACpB,cAAYR,MAAM,CAACG,KAAM;UACzB3C,EAAE,EAAE;YACF+D,OAAO,EAAE,KAAK;YACd3C,MAAM,EAAEoB,MAAM,CAACI,QAAQ,IAAI,CAACf,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,IAAIN,MAAM,CAACG,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG;UAClG;UACA;UAAA;UACAqB,aAAa,EAAEA,CAAA,KAAM3B,eAAe,CAACG,MAAM,CAACG,KAAK,EAAEkB,KAAK,CAAE;UAAApE,QAAA,EAEzDqE,SAAS,gBACRvF,OAAA,CAAC7C,SAAS;YACRuI,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAE1B,SAAU;YACjB+B,QAAQ,EAAGhB,CAAC,IAAKd,YAAY,CAACc,CAAC,CAACiB,MAAM,CAACN,KAAK,CAAE;YAC9CO,SAAS,EAAGlB,CAAC,IAAKD,cAAc,CAACC,CAAC,EAAEV,MAAM,CAACG,KAAK,CAAE;YAClD0B,MAAM,EAAEA,CAAA,KAAMtB,cAAc,CAACP,MAAM,CAACG,KAAK,CAAE;YAC3C2B,SAAS;YACTC,SAAS;YACTC,OAAO,EAAC,UAAU;YAClBxE,EAAE,EAAE;cACF,kBAAkB,EAAE;gBAClBsB,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE;kBACVmD,YAAY,EAAE;gBAChB,CAAC;gBACD,gBAAgB,EAAE;kBAChBA,YAAY,EAAE;gBAChB,CAAC;gBACD,SAAS,EAAE;kBACTA,YAAY,EAAE;gBAChB;cACF,CAAC;cACD,mBAAmB,EAAE;gBACnBV,OAAO,EAAE,OAAO;gBAChBzC,QAAQ,EAAE,SAAS;gBACnBoD,UAAU,EAAE;cACd;YACF;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAEFyB,MAAM,CAACmC,UAAU,GAAGnC,MAAM,CAACmC,UAAU,CAAC;YAAE9C,GAAG;YAAEgC;UAAM,CAAC,CAAC,GAAGA;QACzD,GA1CIrB,MAAM,CAACG,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CR,CAAC;MAEhB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC,kCAAC;;AAEF;AAAA6D,GAAA,GApJMlD,iBAAiB;AAqJvB,MAAMmD,uBAAuB,GAAG,CAC9B,UAAU,EACV,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,aAAa,EACb,mCAAmC,EACnC,kCAAkC,EAClC,sCAAsC,EACtC,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAID;AACA,MAAMC,UAAU,gBAAAC,GAAA,cAAGtK,KAAK,CAACyE,IAAI,CAAA8F,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEzE;AAAQ,CAAC,KAAK;EAAAqE,GAAA;EACtE;EACA,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAG3K,QAAQ,CAAC;IAErCwK,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACAxK,SAAS,CAAC,MAAM;IACd0K,UAAU,CAAC;MACTH,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMG,WAAW,GAAIpC,CAAC,IAAK;IACzBxC,OAAO,CAACuE,KAAK,CAAC;EAChB,CAAC;EAED,oBACE1G,OAAA,CAACtD,MAAM;IAELyF,OAAO,EAAE4E,WAAY;IACrBd,OAAO,EAAEY,OAAO,CAACD,UAAU,GAAG,WAAW,GAAG,UAAW;IACvD9D,KAAK,EAAC,SAAS;IACf4C,IAAI,EAAC,OAAO;IACZjE,EAAE,EAAE;MACFuF,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBlE,QAAQ,EAAE,SAAS;MACnBmE,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBzF,UAAU,EAAE,sBAAsB;MAClC0F,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAArG,QAAA,EAED2F,OAAO,CAACF,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAIG,OAAO,CAACD,UAAU,EAAE;IAAAvE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAACgF,GAAA,GA5CGjB,UAAU;AA8ChB,MAAMkB,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG1L,OAAO,CAAC,MAAM,CAChC;IAAE6H,KAAK,EAAE,IAAI;IAAE8D,UAAU,EAAE,IAAI;IAAE7D,QAAQ,EAAE,KAAK;IAAE8D,WAAW,EAAE;EAAO,CAAC,EACvE;IAAE/D,KAAK,EAAE,MAAM;IAAE8D,UAAU,EAAE,MAAM;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAE/D,KAAK,EAAE,YAAY;IAAE8D,UAAU,EAAE,YAAY;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EACtF;IAAE/D,KAAK,EAAE,OAAO;IAAE8D,UAAU,EAAE,OAAO;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAE/D,KAAK,EAAE,IAAI;IAAE8D,UAAU,EAAE,IAAI;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EACtE;IAAE/D,KAAK,EAAE,SAAS;IAAE8D,UAAU,EAAE,SAAS;IAAE7D,QAAQ,EAAE,KAAK;IAAE8D,WAAW,EAAE;EAAO,CAAC,EACjF;IAAE/D,KAAK,EAAE,UAAU;IAAE8D,UAAU,EAAE,OAAO;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAE/D,KAAK,EAAE,YAAY;IAAE8D,UAAU,EAAE,QAAQ;IAAE7D,QAAQ,EAAE,IAAI;IAAE8D,WAAW,EAAE;EAAO,CAAC,EAClF;IAAE/D,KAAK,EAAE,QAAQ;IAAE8D,UAAU,EAAE,QAAQ;IAAE7D,QAAQ,EAAE,KAAK;IAAE8D,WAAW,EAAE;EAAO,CAAC,CAChF,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlM,QAAQ,CAAC,MAAM;IACzD,MAAMmM,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGhC,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGzM,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC0M,eAAe,EAAEC,kBAAkB,CAAC,GAAG3M,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4M,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7M,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM8M,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMC,eAAe,GAAGT,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACnF,MAAMW,iBAAiB,GAAGD,eAAe,CAACC,iBAAiB;MAE3D,IAAIA,iBAAiB,EAAE;QACrB;QACA,MAAMC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAAC,eAAe,CAAC;QACtD,IAAIA,KAAK,EAAE;UACT,MAAM,GAAGC,SAAS,EAAEC,IAAI,CAAC,GAAGF,KAAK;UACjC,MAAMG,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAClD,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YACpD,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAChD,CAAC;UACD,MAAMC,WAAW,GAAGD,QAAQ,CAACF,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC;UACrD,IAAID,WAAW,EAAE;YACf,OAAO,GAAGF,IAAI,IAAIE,WAAW,EAAE;UACjC;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;;IAEA;IACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,OAAO,GAAGD,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9E,CAAC;EAED,MAAMC,aAAa,GAAGhB,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGhO,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiO,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlO,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACmO,YAAY,EAAEC,eAAe,CAAC,GAAGpO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACqO,UAAU,EAAEC,aAAa,CAAC,GAAGtO,QAAQ,CAAC;IAAEiI,KAAK,EAAE,IAAI;IAAEsG,SAAS,EAAE;EAAM,CAAC,CAAC;;EAE/E;EACA,MAAMC,UAAU,GAAIvG,KAAK,IAAK;IAC5B,MAAMwG,KAAK,GAAGJ,UAAU,CAACpG,KAAK,KAAKA,KAAK,IAAIoG,UAAU,CAACE,SAAS,KAAK,KAAK;IAC1E,MAAMG,YAAY,GAAGD,KAAK,GAAG,MAAM,GAAG,KAAK;IAC3CH,aAAa,CAAC;MAAErG,KAAK;MAAEsG,SAAS,EAAEG;IAAa,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAGzO,WAAW,CAC7C4D,QAAQ,CAAEyH,IAAI,IAAK;IACjB,IAAI;MACFa,YAAY,CAACwC,OAAO,CAAC,eAAe,EAAEtC,IAAI,CAACuC,SAAS,CAACtD,IAAI,CAAC,CAAC;MAC3D5C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2C,IAAI,CAACuD,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAG7O,WAAW,CACvC4D,QAAQ,CAAEyH,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvB5C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACgD,YAAY,CACf,CAAC;;EAED;EACA,MAAMoD,gBAAgB,GAAG9O,WAAW,CAAEqL,IAAI,IAAK;IAC7C,MAAM0D,QAAQ,GAAG1D,IAAI,CAACxD,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC;IACrD,IAAID,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG5D,IAAI,CAClB6D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,CAAC,CAClDiH,MAAM,CAAC,CAACC,GAAG,EAAEnI,GAAG,KAAKmI,GAAG,IAAIC,MAAM,CAACpI,GAAG,CAACqI,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DP,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAO5D,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkE,aAAa,GAAGvP,WAAW,CAAEwP,MAAM,IAAK;IAC5C,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;MACvB;IACF;IAEA,MAAMC,WAAW,GAAGF,MAAM,CAACG,MAAM,CAAClL,KAAK;IACvC,MAAMmL,gBAAgB,GAAGJ,MAAM,CAACC,WAAW,CAAChL,KAAK;IAEjD,IAAIiL,WAAW,KAAKE,gBAAgB,EAAE;MACpC;IACF;IAEAC,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;;MAEzB;MACA,MAAME,YAAY,GAAGD,OAAO,CAACb,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC;MAC9D,MAAMD,QAAQ,GAAGgB,OAAO,CAAClI,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC;;MAExD;MACA,MAAM,CAACiB,OAAO,CAAC,GAAGD,YAAY,CAACE,MAAM,CAACR,WAAW,EAAE,CAAC,CAAC;MACrDM,YAAY,CAACE,MAAM,CAACN,gBAAgB,EAAE,CAAC,EAAEK,OAAO,CAAC;;MAEjD;MACAD,YAAY,CAACG,OAAO,CAAC,CAAClJ,GAAG,EAAExC,KAAK,KAAK;QACnC,IAAI,OAAOwC,GAAG,CAAC+H,EAAE,KAAK,QAAQ,EAAE;UAC9B/H,GAAG,CAAC+H,EAAE,GAAGvK,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM2L,aAAa,GAAGrB,QAAQ,GAAG,CAAC,GAAGiB,YAAY,EAAEjB,QAAQ,CAAC,GAAGiB,YAAY;MAE3EvH,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvB,OAAOoG,gBAAgB,CAACsB,aAAa,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMuB,cAAc,GAAGrQ,WAAW,CAAC,CAACqK,KAAK,EAAEtC,KAAK,EAAEuI,QAAQ,KAAK;IAC7DT,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMS,WAAW,GAAGT,IAAI,CAAC9G,GAAG,CAAC/B,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACmB,EAAE,KAAKiC,KAAK,EAAE;UACpB,MAAMmG,UAAU,GAAG;YAAE,GAAGvJ,GAAG;YAAE,CAACc,KAAK,GAAGuI;UAAS,CAAC;;UAEhD;UACA,MAAMG,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;UACrG,IAAIA,aAAa,CAACC,QAAQ,CAAC3I,KAAK,CAAC,IAAIuI,QAAQ,KAAKK,SAAS,IAAIL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;YACnG,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAChC,MAAMM,QAAQ,GAAGvB,MAAM,CAACiB,QAAQ,CAAC;cACjC,IAAI,CAACO,KAAK,CAACD,QAAQ,CAAC,EAAE;gBACpBJ,UAAU,CAACzI,KAAK,CAAC,GAAG6I,QAAQ;cAC9B;YACF;UACF;UAEA,OAAOJ,UAAU;QACnB;QACA,OAAOvJ,GAAG;MACZ,CAAC,CAAC;MAEF,MAAMuI,MAAM,GAAGV,gBAAgB,CAACyB,WAAW,CAAC;;MAE5C;MACA9B,2BAA2B,CAACe,MAAM,CAAC;MACnCX,qBAAqB,CAACW,MAAM,CAAC;MAE7B,OAAOA,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEL,2BAA2B,EAAEI,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGjR,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMkR,uBAAuB,GAAI/J,GAAG,IAAK;IACvC,MAAMgK,QAAQ,GAAGC,UAAU,CAACjK,GAAG,CAACkK,QAAQ,CAAC,IAAI,CAAC;IAC9C,MAAMC,EAAE,GAAGF,UAAU,CAACjK,GAAG,CAACoK,EAAE,CAAC,IAAI,CAAC;IAClC,MAAMC,OAAO,GAAGrK,GAAG,CAACsK,OAAO,IAAI,EAAE;;IAEjC;IACA,IAAIH,EAAE,KAAK,CAAC,EAAE;MACZ,OAAO,iCAAiC;IAC1C;IACA,IAAIA,EAAE,KAAK,CAAC,EAAE;MACZ,OAAO,iCAAiC;IAC1C;;IAEA;IACA,IAAIE,OAAO,CAACE,IAAI,CAAC,CAAC,EAAE;MAClB,MAAMC,YAAY,GAAGH,OAAO,CAAClE,WAAW,CAAC,CAAC;;MAE1C;MACA,IAAIqE,YAAY,CAACf,QAAQ,CAAC,sCAAsC,CAAC,EAAE,OAAO,sCAAsC;MAChH,IAAIe,YAAY,CAACf,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;MACtD,IAAIe,YAAY,CAACf,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;MAC1D,IAAIe,YAAY,CAACf,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,YAAY;;MAE5D;MACA,IAAIe,YAAY,CAACf,QAAQ,CAAC,UAAU,CAAC,IAAIe,YAAY,CAACf,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,mCAAmC;MAClI,IAAIe,YAAY,CAACf,QAAQ,CAAC,YAAY,CAAC,IAAIe,YAAY,CAACf,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,kCAAkC;MAClI,IAAIe,YAAY,CAACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACe,YAAY,CAACf,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACe,YAAY,CAACf,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,oBAAoB;MACjI,IAAIe,YAAY,CAACf,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,oBAAoB;;MAEnE;MACA,IAAIe,YAAY,CAACf,QAAQ,CAAC,KAAK,CAAC,IAAIe,YAAY,CAACf,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,iBAAiB;;MAE9F;MACA,IAAIe,YAAY,CAACf,QAAQ,CAAC,UAAU,CAAC,IAAIe,YAAY,CAACf,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,sBAAsB;;MAEtG;MACA,IAAIe,YAAY,CAACf,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACe,YAAY,CAACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACe,YAAY,CAACf,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACe,YAAY,CAACf,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxI;QACA,IAAIO,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;UACtC,OAAO,UAAU;QACnB,CAAC,MAAM,IAAIA,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;UAC7C,OAAO,kBAAkB;QAC3B,CAAC,MAAM,IAAIA,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;UAC7C,OAAO,uBAAuB;QAChC;MACF;IACF;;IAEA;IACA,IAAIhK,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IACpC,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IACpC,IAAIA,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACvC,IAAIA,GAAG,CAAC,WAAW,CAAC,EAAE,OAAO,WAAW;IACxC,IAAIA,GAAG,CAAC,WAAW,CAAC,EAAE,OAAO,mCAAmC;IAChE,IAAIA,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO,kCAAkC;IAC9D,IAAIA,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IACpC,IAAIA,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO,sBAAsB;IAClD,IAAIA,GAAG,CAAC,YAAY,CAAC,EAAE,OAAO,YAAY;IAC1C,IAAIA,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAOA,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;;IAEzC;IACA,IAAIgK,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;MACtC,OAAO,UAAU;IACnB,CAAC,MAAM,IAAIA,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;MAC7C,OAAO,kBAAkB;IAC3B,CAAC,MAAM,IAAIA,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;MAC7C,OAAO,uBAAuB;IAChC;;IAEA;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMS,aAAa,GAAG,CAACrG,IAAI,IAAI,EAAE,EAAErC,GAAG,CAAC/B,GAAG,IAAI;IAC5C;IACA,MAAM0K,YAAY,GAAG1K,GAAG,CAAC2K,mBAAmB,KAAK,IAAI;;IAErD;IACA,MAAMC,mBAAmB,GAAGb,uBAAuB,CAAC/J,GAAG,CAAC;IAExD,OAAO;MACL,GAAGA,GAAG;MACN6K,iBAAiB,EAAED,mBAAmB;MACtC3J,QAAQ,EAAEyJ;IACZ,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAM,CAACI,QAAQ,EAAElC,WAAW,CAAC,GAAG/P,QAAQ,CAAC,MAAM;IAC7C2I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C+C,aAAa,GAAG,IAAIA,aAAa,CAACmD,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAInD,aAAa,IAAIA,aAAa,CAACmD,MAAM,GAAG,CAAC,EAAE;MAC7CnG,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMsJ,aAAa,GAAGvG,aAAa,CAACzC,GAAG,CAAC/B,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACiB,QAAQ,KAAKyI,SAAS,EAAE;UAC9B1J,GAAG,CAACiB,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIjB,GAAG,CAAC6K,iBAAiB,KAAKnB,SAAS,IAAI1J,GAAG,CAAC6K,iBAAiB,KAAK,EAAE,EAAE;UACvE7K,GAAG,CAAC6K,iBAAiB,GAAGd,uBAAuB,CAAC/J,GAAG,CAAC;QACtD;;QAEA;QACA,IAAIA,GAAG,CAAC2K,mBAAmB,KAAK,IAAI,IAAI,CAAC3K,GAAG,CAACiB,QAAQ,EAAE;UACrDjB,GAAG,CAACiB,QAAQ,GAAG,IAAI;QACrB;QAEA,OAAOjB,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMgL,gBAAgB,GAAGP,aAAa,CAAC1I,GAAG,CAAC,CAAC/B,GAAG,EAAExC,KAAK,MAAM;QAC1D,GAAGwC,GAAG;QACNmB,EAAE,EAAE3D,KAAK;QACTwD,OAAO,EAAEhB,GAAG,CAAC+H,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACH+B,eAAe,CAAC,CAAC,GAAGkB,gBAAgB,CAAC,CAAC;MAEtC,OAAOD,aAAa;IACtB;;IAEA;IACAvJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMuJ,gBAAgB,GAAGP,aAAa,CAAC1I,GAAG,CAAC,CAAC/B,GAAG,EAAExC,KAAK,MAAM;MAC1D,GAAGwC,GAAG;MACNmB,EAAE,EAAE3D,KAAK;MACTwD,OAAO,EAAEhB,GAAG,CAAC+H,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACH+B,eAAe,CAAC,CAAC,GAAGkB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAGhS,OAAO,CAAC,MAAM;IACrC,IAAImL,IAAI,GAAG0G,QAAQ,IAAI,EAAE;;IAEzB;IACA,IAAIhE,mBAAmB,CAACyD,IAAI,CAAC,CAAC,EAAE;MAC9B,MAAMW,WAAW,GAAGpE,mBAAmB,CAACqE,WAAW,CAAC,CAAC;MACrD/G,IAAI,GAAGA,IAAI,CAAC6D,MAAM,CAACjI,GAAG,IAAI;QACxB,IAAIgH,YAAY,KAAK,KAAK,EAAE;UAC1B;UACA,OAAOoE,MAAM,CAACC,MAAM,CAACrL,GAAG,CAAC,CAACsL,IAAI,CAACtJ,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACL,QAAQ,CAAC,CAAC,CAACwJ,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,WAAW,CAC9D,CAAC;QACH,CAAC,MAAM;UACL;UACA,MAAMK,SAAS,GAAGvL,GAAG,CAACgH,YAAY,CAAC;UACnC,OAAOuE,SAAS,IAAIA,SAAS,CAAC5J,QAAQ,CAAC,CAAC,CAACwJ,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,WAAW,CAAC;QAC9E;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIhE,UAAU,CAACpG,KAAK,EAAE;MACpBsD,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC,CAACoH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,IAAID,CAAC,CAAC1D,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC;QAC9B,IAAI2D,CAAC,CAAC3D,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;QAE/B,MAAM4D,MAAM,GAAGF,CAAC,CAACvE,UAAU,CAACpG,KAAK,CAAC;QAClC,MAAM8K,MAAM,GAAGF,CAAC,CAACxE,UAAU,CAACpG,KAAK,CAAC;;QAElC;QACA,IAAI,OAAO6K,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;UAC5D,OAAO1E,UAAU,CAACE,SAAS,KAAK,KAAK,GAAGuE,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;QAC3E;;QAEA;QACA,MAAME,IAAI,GAAGrF,MAAM,CAACmF,MAAM,IAAI,EAAE,CAAC,CAACR,WAAW,CAAC,CAAC;QAC/C,MAAMW,IAAI,GAAGtF,MAAM,CAACoF,MAAM,IAAI,EAAE,CAAC,CAACT,WAAW,CAAC,CAAC;QAE/C,IAAIjE,UAAU,CAACE,SAAS,KAAK,KAAK,EAAE;UAClC,OAAOyE,IAAI,CAACE,aAAa,CAACD,IAAI,CAAC;QACjC,CAAC,MAAM;UACL,OAAOA,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC;QACjC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOzH,IAAI;EACb,CAAC,EAAE,CAAC0G,QAAQ,EAAEhE,mBAAmB,EAAEE,YAAY,EAAEE,UAAU,CAAC,CAAC;;EAE7D;EACA,MAAM8E,YAAY,GAAG/S,OAAO,CAAC,MAAMgS,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,MAAMgB,kBAAkB,GAAGlT,WAAW,CAAC,CAACmT,YAAY,EAAEC,YAAY,EAAE/E,SAAS,KAAK;IAChF,MAAMgF,eAAe,GAAGzH,WAAW,CAACsD,MAAM,CAACpH,GAAG,IAC5CA,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACC,KAAK,KAAK,IAAI,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,SAChF,CAAC;IACD,MAAMuL,QAAQ,GAAGL,YAAY,CAAC/D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,CAAC;IAEhF,MAAMqL,eAAe,GAAGD,QAAQ,CAACE,SAAS,CAACvM,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAK+K,YAAY,CAAC;IAC1E,MAAMM,eAAe,GAAGJ,eAAe,CAACG,SAAS,CAAC1L,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKqL,YAAY,CAAC;IAEpF,IAAIG,eAAe,KAAK,CAAC,CAAC,IAAIE,eAAe,KAAK,CAAC,CAAC,EAAE;IAEtD,IAAIC,cAAc,GAAGH,eAAe;IACpC,IAAII,cAAc,GAAGF,eAAe;IAEpC,QAAQpF,SAAS;MACf,KAAK,MAAM;QACTqF,cAAc,GAAGE,IAAI,CAACC,GAAG,CAACN,eAAe,GAAG,CAAC,EAAED,QAAQ,CAAC1E,MAAM,GAAG,CAAC,CAAC;QACnE;MACF,KAAK,IAAI;QACP8E,cAAc,GAAGE,IAAI,CAACE,GAAG,CAACP,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;QACjD;MACF,KAAK,OAAO;QACV;QACA,IAAIE,eAAe,IAAIJ,eAAe,CAACzE,MAAM,GAAG,CAAC,EAAE;UACjD;UACA8E,cAAc,GAAGE,IAAI,CAACC,GAAG,CAACN,eAAe,GAAG,CAAC,EAAED,QAAQ,CAAC1E,MAAM,GAAG,CAAC,CAAC;UACnE+E,cAAc,GAAG,CAAC;QACpB,CAAC,MAAM;UACL;UACAA,cAAc,GAAGF,eAAe,GAAG,CAAC;QACtC;QACA;MACF,KAAK,MAAM;QACT;QACA,IAAIA,eAAe,IAAI,CAAC,EAAE;UACxB;UACAC,cAAc,GAAGE,IAAI,CAACE,GAAG,CAACP,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;UACjDI,cAAc,GAAGN,eAAe,CAACzE,MAAM,GAAG,CAAC;QAC7C,CAAC,MAAM;UACL;UACA+E,cAAc,GAAGF,eAAe,GAAG,CAAC;QACtC;QACA;MACF;QACE;IACJ;IAEA,MAAMM,SAAS,GAAGT,QAAQ,CAACI,cAAc,CAAC;IAC1C,MAAMM,YAAY,GAAGX,eAAe,CAACM,cAAc,CAAC;IAEpD,IAAII,SAAS,IAAIC,YAAY,EAAE;MAC7B;MACA5P,UAAU,CAAC,MAAM;QACf,MAAM6P,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiBJ,SAAS,CAAC3L,EAAE,kBAAkB4L,YAAY,CAACjM,KAAK,IACnE,CAAC;QACD,IAAIkM,aAAa,EAAE;UACjBA,aAAa,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,EAAE;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAC,CAAC;QACvE;MACF,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC,EAAE,CAAC1I,WAAW,EAAEqH,YAAY,CAAC,CAAC;;EAE/B;EACAlT,SAAS,CAAC,MAAM;IACd,MAAMwU,KAAK,GAAGnQ,UAAU,CAAC,MAAM;MAC7B4J,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM1J,YAAY,CAACoQ,KAAK,CAAC;EAClC,CAAC,EAAE,CAAC1G,UAAU,CAAC,CAAC;;EAEhB;EACA9N,SAAS,CAAC,MAAM;IACdyU,kBAAkB,CAAC1E,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE2E,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAAC1G,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACyG,aAAa,EAAEC,gBAAgB,CAAC,GAAG7U,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8U,UAAU,EAAEC,aAAa,CAAC,GAAG/U,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACgV,eAAe,EAAEN,kBAAkB,CAAC,GAAG1U,QAAQ,CAAC,MAAM;IAC3D,MAAMiV,KAAK,GAAG7I,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLsI,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACAhV,SAAS,CAAC,MAAM;IACdmM,YAAY,CAACwC,OAAO,CAAC,kBAAkB,EAAEoG,eAAe,CAACE,QAAQ,CAACpM,QAAQ,CAAC,CAAC,CAAC;IAC7EH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoM,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAE9B;EACAjV,SAAS,CAAC,MAAM;IACdmM,YAAY,CAACwC,OAAO,CAAC,gBAAgB,EAAEtC,IAAI,CAACuC,SAAS,CAAC5C,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmJ,mBAAmB,GAAGjV,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMkV,iBAAiB,GAAGlV,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMmV,gBAAgB,GAAGnV,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMoV,UAAU,GAAIhK,IAAI,IAAKA,IAAI,CAACrC,GAAG,CAAC/B,GAAG,KAAK;IAC5CmB,EAAE,EAAEnB,GAAG,CAACmB,EAAE;IACV4G,EAAE,EAAE/H,GAAG,CAAC+H,EAAE;IACV9G,QAAQ,EAAEjB,GAAG,CAACiB,QAAQ;IACtBqJ,OAAO,EAAEtK,GAAG,CAACsK,OAAO;IACpBO,iBAAiB,EAAE7K,GAAG,CAAC6K,iBAAiB;IACxCxC,UAAU,EAAErI,GAAG,CAACqI;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAvP,SAAS,CAAC,MAAM;IACd,IAAI2L,YAAY,IAAIqG,QAAQ,CAACnD,MAAM,GAAG,CAAC,EAAE;MACvC,MAAM0G,OAAO,GAAGlJ,IAAI,CAACuC,SAAS,CAAC0G,UAAU,CAACtD,QAAQ,CAAC,CAAC;MACpD,MAAMwD,WAAW,GAAGL,mBAAmB,CAACM,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIF,gBAAgB,CAACI,OAAO,EAAE;UAC5BrR,YAAY,CAACiR,gBAAgB,CAACI,OAAO,CAAC;QACxC;QACAJ,gBAAgB,CAACI,OAAO,GAAGpR,UAAU,CAAC,MAAM;UAC1C8Q,mBAAmB,CAACM,OAAO,GAAGF,OAAO;UACrCH,iBAAiB,CAACK,OAAO,GAAGjI,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC5B,YAAY,CAAC,CAAC,GAAGqG,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IACA,OAAO,MAAM;MACX,IAAIqD,gBAAgB,CAACI,OAAO,EAAE;QAC5BrR,YAAY,CAACiR,gBAAgB,CAACI,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACzD,QAAQ,EAAErG,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC+J,aAAa,EAAEC,gBAAgB,CAAC,GAAG5V,QAAQ,CAAC;IACjD6V,IAAI,EAAE,KAAK;IACXtL,KAAK,EAAE,IAAI;IACX1C,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA5H,SAAS,CAAC,MAAM;IACd,IAAIsL,IAAI,IAAIA,IAAI,CAACuD,MAAM,GAAG,CAAC,EAAE;MAC3BnG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;MAE9B;MACA,MAAM6H,WAAW,GAAGlF,IAAI,CAACrC,GAAG,CAAC,CAAC/B,GAAG,EAAExC,KAAK,KAAK;QAC3C;QACA,MAAMkN,YAAY,GAAG1K,GAAG,CAAC2K,mBAAmB,KAAK,IAAI;;QAErD;QACA,MAAMC,mBAAmB,GAAGb,uBAAuB,CAAC/J,GAAG,CAAC;QAExD,OAAO;UACL,GAAGA,GAAG;UACNmB,EAAE,EAAE3D,KAAK;UACTwD,OAAO,EAAEhB,GAAG,CAAC+H,EAAE,KAAK,OAAO;UAC3B8C,iBAAiB,EAAED,mBAAmB;UACtC3J,QAAQ,EAAEyJ;QACZ,CAAC;MACH,CAAC,CAAC;MAEF9B,WAAW,CAACU,WAAW,CAAC;MACxBQ,eAAe,CAAC,CAAC,GAAGR,WAAW,CAAC,CAAC;MACjC9H,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6H,WAAW,CAACrB,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAAC0G,MAAM,CAAC;IAC9E;EACF,CAAC,EAAE,CAACvD,IAAI,CAAC,CAAC;;EAEV;EACAtL,SAAS,CAAC,MAAM;IACd,IAAI+Q,YAAY,CAAClC,MAAM,KAAK,CAAC,IAAImD,QAAQ,CAACnD,MAAM,GAAG,CAAC,EAAE;MACpDnG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqJ,QAAQ,CAACnD,MAAM,CAAC;MACpDmC,eAAe,CAAC,CAAC,GAAGgB,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEjB,YAAY,CAAC,CAAC;EAI5B,MAAM8E,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAItK,MAAM,IAAIA,MAAM,CAACuK,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7CrK,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAMsK,WAAW,GAAG,GAAG1S,OAAO,aAAakI,MAAM,EAAE;MACnD,MAAMyK,IAAI,GAAG7B,QAAQ,CAAC8B,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGH,WAAW;MACvBC,IAAI,CAACG,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAI3I,IAAI,CAAC,CAAC,CAAC4I,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EjC,QAAQ,CAACkC,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZpC,QAAQ,CAACkC,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAO1I,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7B,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMgL,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMrT,KAAK,CAACsT,MAAM,CAAC,GAAGrT,OAAO,YAAYkI,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9B,OAAO,CAAC,CAAC;EACX,CAAC;;EAID;EACA,MAAMmL,kBAAkB,GAAG1W,WAAW,CAAC,CAACqL,IAAI,EAAEsL,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGvL,IAAI,IAAI0G,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC8E,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACb1H,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,CAAC,CAClDiH,MAAM,CAAC,CAACC,GAAG,EAAEnI,GAAG,KAAK;MACpB,IAAI0P,UAAU,IAAI1P,GAAG,CAACmB,EAAE,KAAKuO,UAAU,CAACvO,EAAE,EAAE;QAC1C,OAAOgH,GAAG,IAAIC,MAAM,CAACsH,UAAU,CAACrH,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAOF,GAAG,IAAIC,MAAM,CAACpI,GAAG,CAACqI,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACyC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgF,iBAAiB,GAAGlX,KAAK,CAACG,WAAW,CAAC,CAACqK,KAAK,EAAE1C,YAAY,KAAK;IACnE;IACA+N,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVtL,KAAK;MACL1C;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqP,kBAAkB,GAAGhX,WAAW,CAAC,MAAM;IAC3C0V,gBAAgB,CAAC5F,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP6F,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAhB,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoC,kBAAkB,GAAGjX,WAAW,CAAEwE,MAAM,IAAK;IACjD,MAAM;MAAE6F;IAAM,CAAC,GAAGoL,aAAa;IAC/B,IAAIpL,KAAK,KAAK,IAAI,EAAE;MAClB;MACA2M,kBAAkB,CAAC,CAAC;;MAEpB;MACAE,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCtH,WAAW,CAACuH,QAAQ,IAAI;UACtB,IAAI7G,WAAW,GAAG6G,QAAQ,CAACpO,GAAG,CAAC/B,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACmB,EAAE,KAAKiC,KAAK,EAAE;cACpB,IAAI7F,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGyC,GAAG;kBAAEsK,OAAO,EAAE,EAAE;kBAAEO,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIuF,WAAW,GAAG7S,MAAM;gBACxB,MAAM8S,QAAQ,GAAG,EAAE;gBAEnB,IAAI5C,aAAa,EAAE;kBACjB4C,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAI3C,UAAU,EAAE;kBACd0C,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAC1I,MAAM,GAAG,CAAC,EAAE;kBACvByI,WAAW,GAAG,GAAG7S,MAAM,KAAK8S,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAGvQ,GAAG;kBAAEsK,OAAO,EAAE8F,WAAW;kBAAEvF,iBAAiB,EAAEuF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOpQ,GAAG;UACZ,CAAC,CAAC;UAEFsJ,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAnM,UAAU,CAAC,MAAM;UACfqE,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC+M,aAAa,EAAEuB,kBAAkB,EAAElI,gBAAgB,EAAE4F,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAM6C,mBAAmB,GAAGzX,WAAW,CAAC,MAAM;IAC5CyM,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiL,oBAAoB,GAAG1X,WAAW,CAAC,MAAM;IAC7CyM,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoL,YAAY,GAAG3X,WAAW,CAAC,MAAM;IACrC,IAAIsM,SAAS,CAACkF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACzF,cAAc,CAAC2E,QAAQ,CAACpE,SAAS,CAACkF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzExF,iBAAiB,CAAC8D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExD,SAAS,CAACkF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD/I,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrBgP,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI3L,cAAc,CAAC2E,QAAQ,CAACpE,SAAS,CAACkF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpD/I,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAAC4D,SAAS,EAAEP,cAAc,EAAE2L,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAME,YAAY,GAAG5X,WAAW,CAAEwE,MAAM,IAAK;IAC3CwH,iBAAiB,CAAC8D,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAAC2I,IAAI,IAAIA,IAAI,KAAKrT,MAAM,CAAC,CAAC;IAC/DiE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoP,0BAA0B,GAAG9X,WAAW,CAAEwP,MAAM,IAAK;IACzD,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;MACvB;IACF;IAEA,MAAMC,WAAW,GAAGF,MAAM,CAACG,MAAM,CAAClL,KAAK;IACvC,MAAMmL,gBAAgB,GAAGJ,MAAM,CAACC,WAAW,CAAChL,KAAK;IAEjD,IAAIiL,WAAW,KAAKE,gBAAgB,EAAE;MACpC;IACF;IAEA5D,iBAAiB,CAAC8D,IAAI,IAAI;MACxB,MAAMiI,UAAU,GAAGlB,KAAK,CAACmB,IAAI,CAAClI,IAAI,CAAC;MACnC,MAAM,CAACmI,aAAa,CAAC,GAAGF,UAAU,CAAC7H,MAAM,CAACR,WAAW,EAAE,CAAC,CAAC;MACzDqI,UAAU,CAAC7H,MAAM,CAACN,gBAAgB,EAAE,CAAC,EAAEqI,aAAa,CAAC;MACrDxP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,OAAOqP,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,eAAe,GAAGlY,WAAW,CAAEoI,EAAE,IAAK;IAC1CyH,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAAC9G,GAAG,CAAC/B,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAK,CAAC,GAAGjB,GAAG,CAAC;;MAEnF;MACA,IAAIkR,SAAS,GAAG,CAAC;MACjB5H,WAAW,CAACJ,OAAO,CAAClJ,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+H,EAAE,KAAK,QAAQ,EAAE;UACrE/H,GAAG,CAAC+H,EAAE,GAAGmJ,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF5H,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEF9H,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACoG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMsJ,aAAa,GAAGpY,WAAW,CAAEoI,EAAE,IAAK;IACxCyH,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAAC9G,GAAG,CAAC/B,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAM,CAAC,GAAGjB,GAAG,CAAC;;MAEpF;MACA,IAAIkR,SAAS,GAAG,CAAC;MACjB5H,WAAW,CAACJ,OAAO,CAAClJ,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+H,EAAE,KAAK,QAAQ,EAAE;UACrE/H,GAAG,CAAC+H,EAAE,GAAGmJ,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF5H,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFnM,UAAU,CAAC,MAAM;MACfqE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACoG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMuJ,eAAe,GAAGrY,WAAW,CAAEoI,EAAE,IAAK;IAC1CyH,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAMwI,YAAY,GAAGxI,IAAI,CAACZ,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAI+P,SAAS,GAAG,CAAC;MACjBG,YAAY,CAACnI,OAAO,CAAClJ,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+H,EAAE,KAAK,QAAQ,EAAE;UACrE/H,GAAG,CAAC+H,EAAE,GAAGmJ,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM5H,WAAW,GAAGzB,gBAAgB,CAACwJ,YAAY,CAAC;;MAElD;MACA7J,2BAA2B,CAAC8B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElC9H,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrB,OAAO6H,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEL,2BAA2B,EAAEI,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAM0J,YAAY,GAAGvY,WAAW,CAAEwY,UAAU,IAAK;IAC/C3I,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAM2I,WAAW,GAAG3I,IAAI,CAAC0D,SAAS,CAACvM,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKoQ,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO3I,IAAI;;MAEnC;MACA,MAAM4I,KAAK,GAAGnL,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAMqL,UAAU,GAAG7I,IAAI,CAAC2I,WAAW,CAAC;MACpC,MAAMG,QAAQ,GAAG,OAAOD,UAAU,CAAC3J,EAAE,KAAK,QAAQ,GAAG2J,UAAU,CAAC3J,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM6J,MAAM,GAAG;QACbzQ,EAAE,EAAEsQ,KAAK;QACT1J,EAAE,EAAE4J,QAAQ;QACZE,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXzH,EAAE,EAAE,EAAE;QACNE,OAAO,EAAE,EAAE;QACXJ,QAAQ,EAAE,EAAE;QACZ7B,UAAU,EAAE,CAAC;QACbwC,iBAAiB,EAAE,EAAE;QACrB5J,QAAQ,EAAE,KAAK;QACfD,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAM8H,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;MACzBC,OAAO,CAACG,MAAM,CAACuI,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEI,MAAM,CAAC;;MAE1C;MACA,IAAIV,SAAS,GAAG,CAAC;MACjBpI,OAAO,CAACI,OAAO,CAAClJ,GAAG,IAAI;QACrB,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC+H,EAAE,KAAK,QAAQ,EAAE;UACrE/H,GAAG,CAAC+H,EAAE,GAAGmJ,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM5H,WAAW,GAAGzB,gBAAgB,CAACiB,OAAO,CAAC;;MAE7C;MACAtB,2BAA2B,CAAC8B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElC9H,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;MACpB,OAAO6H,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEL,2BAA2B,EAAEI,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMkK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpM,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMqM,YAAY,GAAG,CAACjH,QAAQ,IAAI,EAAE,EACjC7C,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,CAAC;;MAErD;MACA8Q,YAAY,CAACvG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC1D,EAAE,KAAK,QAAQ,IAAI,OAAO2D,CAAC,CAAC3D,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO0D,CAAC,CAAC1D,EAAE,GAAG2D,CAAC,CAAC3D,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMiK,OAAO,GAAGD,YAAY,CAAChQ,GAAG,CAAC,CAAC/B,GAAG,EAAExC,KAAK,MAAM;QAChD;QACAuK,EAAE,EAAEvK,KAAK,GAAG,CAAC;QACbqU,IAAI,EAAE7R,GAAG,CAAC6R,IAAI,GAAI,OAAO7R,GAAG,CAAC6R,IAAI,KAAK,QAAQ,IAAI7R,GAAG,CAAC6R,IAAI,CAACpI,QAAQ,CAAC,GAAG,CAAC,GAAGzJ,GAAG,CAAC6R,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGjS,GAAG,CAAC6R,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE7R,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG2M,IAAI,CAACuF,KAAK,CAAClS,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFoK,EAAE,EAAE,OAAOpK,GAAG,CAACoK,EAAE,KAAK,QAAQ,GAAGuC,IAAI,CAACuF,KAAK,CAAClS,GAAG,CAACoK,EAAE,CAAC,GAAGpK,GAAG,CAACoK,EAAE,IAAI,EAAE;QAClEE,OAAO,EAAEtK,GAAG,CAACsK,OAAO,IAAI,EAAE;QAAE;QAC5BO,iBAAiB,EAAG7K,GAAG,CAAC6K,iBAAiB,IAAI7K,GAAG,CAAC6K,iBAAiB,KAAK,MAAM,GAAI7K,GAAG,CAAC6K,iBAAiB,GAAG,EAAE;QAAE;QAC7GsH,KAAK,EAAE,OAAOnS,GAAG,CAACkK,QAAQ,KAAK,QAAQ,GACpClK,GAAG,CAACkK,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGlK,GAAG,CAACkK,QAAQ,CAACkI,OAAO,CAAC,CAAC,CAAC,GAAGpS,GAAG,CAACkK,QAAQ,CAACkI,OAAO,CAAC,CAAC,CAAC,GAC3EpS,GAAG,CAACkK,QAAQ,IAAI,EAAE;QACpBmI,MAAM,EAAE,OAAOrS,GAAG,CAACqI,UAAU,KAAK,QAAQ,GAAGrI,GAAG,CAACqI,UAAU,CAAC+J,OAAO,CAAC,CAAC,CAAC,GAAGpS,GAAG,CAACqI,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMiK,WAAW,GAAG,CAACxH,QAAQ,IAAI,EAAE,EAChC7C,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAI,CAAC/H,GAAG,CAACiB,QAAQ,IAAIjB,GAAG,CAACqI,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAEnI,GAAG,KAAKmI,GAAG,IAAI,OAAOnI,GAAG,CAACqI,UAAU,KAAK,QAAQ,GAAGrI,GAAG,CAACqI,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMkK,YAAY,GAAIlO,MAAM,IAAIA,MAAM,CAACuK,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAGvK,MAAM;MAE5F,MAAMmO,QAAQ,GAAG,MAAMtW,KAAK,CAACuW,IAAI,CAAC,GAAGtW,OAAO,oBAAoB,EAAE;QAChEiI,IAAI,EAAE4N,OAAO;QACbM,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC/N,MAAM,EAAEkO,YAAY;QACpB5L,aAAa,EAAEA;MACjB,CAAC,CAAC;MAEF,IAAI6L,QAAQ,CAACpO,IAAI,IAAIoO,QAAQ,CAACpO,IAAI,CAACsO,KAAK,EAAE;QACxC;QACA,MAAM7D,WAAW,GAAG,GAAG1S,OAAO,CAAC8V,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGO,QAAQ,CAACpO,IAAI,CAACuO,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAnR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACAtE,UAAU,CAAC,MAAM;UACf,MAAMyV,MAAM,GAAG3F,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC;UAC/C6D,MAAM,CAACC,KAAK,CAACzT,OAAO,GAAG,MAAM;UAC7BwT,MAAM,CAACE,GAAG,GAAGjE,WAAW;UACxB5B,QAAQ,CAACkC,IAAI,CAACC,WAAW,CAACwD,MAAM,CAAC;UACjCzV,UAAU,CAAC,MAAM;YACf8P,QAAQ,CAACkC,IAAI,CAACG,WAAW,CAACsD,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO3M,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B5E,OAAO,CAAC4E,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRV,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMsN,kBAAkB,GAAGja,WAAW,CAAC,CAACqK,KAAK,EAAEpB,KAAK,KAAK;IACvD8N,iBAAiB,CAAC1M,KAAK,EAAEpB,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC8N,iBAAiB,CAAC,CAAC;EAEvB,MAAM7P,OAAO,GAAGhH,OAAO,CAAC,MAAO0L,WAAW,CAAC5C,GAAG,CAAClB,GAAG,IAAI;IACpD,IAAI,EAAEiK,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACmI,cAAc,CAACpS,GAAG,CAACC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8D,UAAU,EAAE/D,GAAG,CAAC+D,UAAU;QAC1BsO,IAAI,EAAE,GAAG;QACTrR,KAAK,EAAE,GAAG;QACVd,QAAQ,EAAE,KAAK;QACf+B,UAAU,EAAGqQ,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACnT,GAAG,CAAC+H,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIoL,MAAM,CAACnT,GAAG,CAACiB,QAAQ,EAAE;YACvB,MAAMmS,iBAAiB,GAAGD,MAAM,CAACnT,GAAG,CAAC6K,iBAAiB,IAAI,KAAK;YAC/D,oBACEnO,OAAA,CAACzC,OAAO;cAACoZ,KAAK,EAAEF,MAAM,CAACnT,GAAG,CAAC6K,iBAAiB,IAAI,EAAG;cAACyI,KAAK;cAACC,SAAS,EAAC,KAAK;cAAA3V,QAAA,eACvElB,OAAA,CAAC1C,IAAI;gBACHwZ,KAAK,EAAEJ,iBAAkB;gBACzB5T,KAAK,EAAC,SAAS;gBACfmD,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACZjE,EAAE,EAAE;kBAAEwF,QAAQ,EAAE,MAAM;kBAAE8P,OAAO,EAAE,GAAG;kBAAEnV,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEuF,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAE3E,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIwU,UAAU,GAAG,MAAM;UACvB,IAAIpQ,UAAU,GAAG,KAAK;UAEtB,IAAI6P,MAAM,CAACnT,GAAG,CAAC6K,iBAAiB,IAAIsI,MAAM,CAACnT,GAAG,CAAC6K,iBAAiB,KAAK,MAAM,EAAE;YAC3E6I,UAAU,GAAGP,MAAM,CAACnT,GAAG,CAAC6K,iBAAiB;YACzCvH,UAAU,GAAG,IAAI;UACnB;UAEA,oBACE5G,OAAA,CAACuG,UAAU;YACTG,KAAK,EAAE+P,MAAM,CAACnT,GAAG,CAACmB,EAAG;YACrBkC,IAAI,EAAEqQ,UAAW;YACjBpQ,UAAU,EAAEA,UAAW;YACvBzE,OAAO,EAAEmU;UAAmB;YAAAjU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI2B,GAAG,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8D,UAAU,EAAE/D,GAAG,CAAC+D,UAAU;QAC1BsO,IAAI,EAAE,GAAG;QACTrR,KAAK,EAAE,GAAG;QACVd,QAAQ,EAAE,KAAK;QACf+B,UAAU,EAAGqQ,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACnT,GAAG,CAAC+H,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACErL,OAAA,CAACxD,GAAG;YAACiF,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEuU,GAAG,EAAE,GAAG;cAAEtU,UAAU,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBAE3DlB,OAAA,CAACzC,OAAO;cAACoZ,KAAK,EAAC,wDAAW;cAAAzV,QAAA,eACxBlB,OAAA,CAAC5C,UAAU;gBACTsI,IAAI,EAAC,OAAO;gBACZ5C,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAEA,CAAA,KAAMyS,YAAY,CAAC6B,MAAM,CAACnT,GAAG,CAACmB,EAAE,CAAE;gBAC3ChD,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTM,eAAe,EAAE,cAAc;oBAC/Be,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAA5B,QAAA,eAEFlB,OAAA,CAACX,oBAAoB;kBAAC0D,QAAQ,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVxC,OAAA,CAACzC,OAAO;cAACoZ,KAAK,EAAC,0EAAc;cAAAzV,QAAA,eAC3BlB,OAAA,CAAC5C,UAAU;gBACTsI,IAAI,EAAC,OAAO;gBACZ5C,KAAK,EAAC,OAAO;gBACbX,OAAO,EAAEA,CAAA,KAAMuS,eAAe,CAAC+B,MAAM,CAACnT,GAAG,CAACmB,EAAE,CAAE;gBAC9ChD,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTM,eAAe,EAAE,YAAY;oBAC7Be,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAA5B,QAAA,eAEFlB,OAAA,CAACV,uBAAuB;kBAACyD,QAAQ,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGTiU,MAAM,CAACnT,GAAG,CAACiB,QAAQ,gBAClBvE,OAAA,CAACtD,MAAM;cAELuJ,OAAO,EAAC,WAAW;cACnBnD,KAAK,EAAC,SAAS;cACf4C,IAAI,EAAC,OAAO;cACZwR,SAAS,eAAElX,OAAA,CAACjB,QAAQ;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBL,OAAO,EAAEA,CAAA,KAAMsS,aAAa,CAACgC,MAAM,CAACnT,GAAG,CAACmB,EAAE,CAAE;cAC5ChD,EAAE,EAAE;gBACFsB,QAAQ,EAAE,SAAS;gBACnBmE,aAAa,EAAE,MAAM;gBACrBF,QAAQ,EAAE;cACZ,CAAE;cAAA9F,QAAA,EACH;YAED,GAbM,MAAM;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETxC,OAAA,CAACtD,MAAM;cAELuJ,OAAO,EAAC,WAAW;cACnBnD,KAAK,EAAC,OAAO;cACb4C,IAAI,EAAC,OAAO;cACZwR,SAAS,eAAElX,OAAA,CAAClB,UAAU;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BL,OAAO,EAAEA,CAAA,KAAMoS,eAAe,CAACkC,MAAM,CAACnT,GAAG,CAACmB,EAAE,CAAE;cAC9ChD,EAAE,EAAE;gBACFsB,QAAQ,EAAE,SAAS;gBACnBmE,aAAa,EAAE,MAAM;gBACrBF,QAAQ,EAAE;cACZ,CAAE;cAAA9F,QAAA,EACH;YAED,GAbM,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG2B,GAAG;MACNE,QAAQ,EAAEoS,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACnT,GAAG,IAAImT,MAAM,CAACnT,GAAG,CAAC+H,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIoL,MAAM,CAACnT,GAAG,IAAImT,MAAM,CAACnT,GAAG,CAACiB,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOJ,GAAG,CAACE,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD+B,UAAU,EAAGqQ,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACnT,GAAG,CAAC+H,EAAE,KAAK,OAAO,IAAIlH,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEpE,OAAA,CAACvD,UAAU;YAACwJ,OAAO,EAAC,OAAO;YAACkR,UAAU,EAAC,MAAM;YAACrU,KAAK,EAAC,SAAS;YAAA5B,QAAA,EAC1D,OAAOuV,MAAM,CAACnR,KAAK,KAAK,QAAQ,GAAGmR,MAAM,CAACnR,KAAK,CAACoQ,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOe,MAAM,CAACnR,KAAK,KAAK,QAAQ,IAAI,CAAC4H,KAAK,CAACxB,MAAM,CAAC+K,MAAM,CAACnR,KAAK,CAAC,CAAC,GAAGoG,MAAM,CAAC+K,MAAM,CAACnR,KAAK,CAAC,CAACoQ,OAAO,CAAC,CAAC,CAAC,GAAGe,MAAM,CAACnR;UAAK;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIiU,MAAM,CAACnT,GAAG,CAACiB,QAAQ,EAAE;UACvB,oBACEvE,OAAA,CAACvD,UAAU;YAACwJ,OAAO,EAAC,OAAO;YAACnD,KAAK,EAAC,eAAe;YAACrB,EAAE,EAAE;cAAEyD,cAAc,EAAE;YAAe,CAAE;YAAAhE,QAAA,EACtFuV,MAAM,CAACnR;UAAK;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI2B,GAAG,CAACC,KAAK,KAAK,MAAM,IAAIqS,MAAM,CAACnR,KAAK,EAAE;UACxC,OAAOmR,MAAM,CAACnR,KAAK,CAACiQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIpR,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAO2K,IAAI,CAACuF,KAAK,CAACiB,MAAM,CAACnR,KAAK,CAAC;QACjC;QACA,IAAInB,GAAG,CAACC,KAAK,KAAK,OAAO,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAO2K,IAAI,CAACuF,KAAK,CAACiB,MAAM,CAACnR,KAAK,CAAC;QACjC;QACA,IAAInB,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAO2K,IAAI,CAACuF,KAAK,CAACiB,MAAM,CAACnR,KAAK,CAAC;QACjC;QACA,IAAInB,GAAG,CAACC,KAAK,KAAK,UAAU,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOmR,MAAM,CAACnR,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGmR,MAAM,CAACnR,KAAK,CAACoQ,OAAO,CAAC,CAAC,CAAC,GAAGe,MAAM,CAACnR,KAAK,CAACoQ,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIvR,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOmR,MAAM,CAACnR,KAAK,CAACoQ,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIvR,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOqS,MAAM,CAACnR,KAAK,KAAK,QAAQ,IAAI,CAAC4H,KAAK,CAACxB,MAAM,CAAC+K,MAAM,CAACnR,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOoG,MAAM,CAAC+K,MAAM,CAACnR,KAAK,CAAC,CAACoQ,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOe,MAAM,CAACnR,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOmR,MAAM,CAACnR,KAAK;QACrB;QACA,OAAOmR,MAAM,CAACnR,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACiG,MAAM,CAAC6L,OAAO,CAAE,EAAE,CAACnP,WAAW,EAAEmG,QAAQ,EAAEkI,kBAAkB,EAAE/B,eAAe,EAAEE,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEhI;EACAtY,SAAS,CAAC,MAAM;IACd0I,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBsM,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1BuG,UAAU,EAAE/H,YAAY,CAACrE;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACkG,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAExB,YAAY,CAACrE,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACmD,QAAQ,IAAIA,QAAQ,CAACnD,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEjL,OAAA,CAACxD,GAAG;MAACiF,EAAE,EAAE;QAAE2D,SAAS,EAAE,QAAQ;QAAEkS,EAAE,EAAE;MAAE,CAAE;MAAApW,QAAA,gBACtClB,OAAA,CAACvD,UAAU;QAACwJ,OAAO,EAAC,IAAI;QAACnD,KAAK,EAAC,gBAAgB;QAAA5B,QAAA,EAAC;MAEhD;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxC,OAAA,CAACtD,MAAM;QACLuJ,OAAO,EAAC,WAAW;QACnB9D,OAAO,EAAEyF,OAAQ;QACjBnG,EAAE,EAAE;UAAE8V,EAAE,EAAE;QAAE,CAAE;QAAArW,QAAA,EACf;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACExC,OAAA,CAACxD,GAAG;IAAA0E,QAAA,gBAEFlB,OAAA,CAACxC,IAAI;MAACiE,EAAE,EAAE;QAAE+V,EAAE,EAAE;MAAE,CAAE;MAAAtW,QAAA,eAClBlB,OAAA,CAACvC,WAAW;QAAAyD,QAAA,eACVlB,OAAA,CAACxD,GAAG;UAACiF,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE8U,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAET,GAAG,EAAE;UAAE,CAAE;UAAA/V,QAAA,gBAC5GlB,OAAA,CAACxD,GAAG;YAACiF,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEsU,GAAG,EAAE;YAAE,CAAE;YAAA/V,QAAA,gBACzDlB,OAAA,CAAChB,cAAc;cAACyC,EAAE,EAAE;gBAAEqB,KAAK,EAAE,cAAc;gBAAEC,QAAQ,EAAE;cAAG;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DxC,OAAA,CAACxD,GAAG;cAAA0E,QAAA,gBACFlB,OAAA,CAACvD,UAAU;gBAACwJ,OAAO,EAAC,IAAI;gBAACxE,EAAE,EAAE;kBAAE0V,UAAU,EAAE,GAAG;kBAAErU,KAAK,EAAE,cAAc;kBAAE0U,EAAE,EAAE;gBAAI,CAAE;gBAAAtW,QAAA,EAAC;cAElF;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbxC,OAAA,CAACvD,UAAU;gBAACwJ,OAAO,EAAC,OAAO;gBAACxE,EAAE,EAAE;kBAAEqB,KAAK,EAAE;gBAAiB,CAAE;gBAAA5B,QAAA,EAAC;cAE7D;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxC,OAAA,CAACtC,KAAK;YAACgN,SAAS,EAAC,KAAK;YAACiN,OAAO,EAAE,CAAE;YAAClW,EAAE,EAAE;cAAEiW,QAAQ,EAAE,MAAM;cAAET,GAAG,EAAE;YAAE,CAAE;YAAA/V,QAAA,gBAClElB,OAAA,CAAC1C,IAAI;cACHsa,IAAI,eAAE5X,OAAA,CAACf,aAAa;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBsU,KAAK,EAAE,GAAG,CAACxH,YAAY,IAAI,EAAE,EAAE/D,MAAM,CAACjI,GAAG,IAAI,CAACA,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,CAAC,CAAC0G,MAAM,MAAO;cACzFnI,KAAK,EAAC,SAAS;cACfmD,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFxC,OAAA,CAAC1C,IAAI;cACHsa,IAAI,eAAE5X,OAAA,CAACd,cAAc;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBsU,KAAK,EAAE,WAAW/D,kBAAkB,CAACzD,YAAY,CAAC,CAACoG,OAAO,CAAC,CAAC,CAAC,EAAG;cAChE5S,KAAK,EAAC,SAAS;cACfmD,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAAC8M,YAAY,IAAI,EAAE,EAAE/D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAAC0G,MAAM,GAAG,CAAC,iBAC1DjL,OAAA,CAAC1C,IAAI;cACHsa,IAAI,eAAE5X,OAAA,CAAClB,UAAU;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBsU,KAAK,EAAE,GAAG,CAACxH,YAAY,IAAI,EAAE,EAAE/D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAAC0G,MAAM,OAAQ;cACzEnI,KAAK,EAAC,SAAS;cACfmD,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPxC,OAAA,CAACxC,IAAI;MAACiE,EAAE,EAAE;QAAE+V,EAAE,EAAE;MAAE,CAAE;MAAAtW,QAAA,eAClBlB,OAAA,CAACvC,WAAW;QAAAyD,QAAA,eACVlB,OAAA,CAAC9B,IAAI;UAAC2Z,SAAS;UAACF,OAAO,EAAE,CAAE;UAAAzW,QAAA,gBAEzBlB,OAAA,CAAC9B,IAAI;YAACgW,IAAI;YAAC4D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7W,QAAA,gBACvBlB,OAAA,CAACvD,UAAU;cAACwJ,OAAO,EAAC,WAAW;cAACxE,EAAE,EAAE;gBAAE0V,UAAU,EAAE,GAAG;gBAAEK,EAAE,EAAE,CAAC;gBAAE9U,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEsU,GAAG,EAAE;cAAE,CAAE;cAAA/V,QAAA,gBAC5GlB,OAAA,CAACb,UAAU;gBAACsC,EAAE,EAAE;kBAAEqB,KAAK,EAAE;gBAAe;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbxC,OAAA,CAACtC,KAAK;cAACgN,SAAS,EAAE;gBAAEoN,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAChV,UAAU,EAAC,QAAQ;cAAAzB,QAAA,gBAC5ElB,OAAA,CAACrC,WAAW;gBAAC+H,IAAI,EAAC,OAAO;gBAACjE,EAAE,EAAE;kBAAEuF,QAAQ,EAAE;gBAAI,CAAE;gBAAA9F,QAAA,gBAC9ClB,OAAA,CAACpC,UAAU;kBAAAsD,QAAA,EAAC;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7BxC,OAAA,CAACnC,MAAM;kBACLyH,KAAK,EAAEgF,YAAa;kBACpBwM,KAAK,EAAC,0BAAM;kBACZnR,QAAQ,EAAGhB,CAAC,IAAK4F,eAAe,CAAC5F,CAAC,CAACiB,MAAM,CAACN,KAAK,CAAE;kBAAApE,QAAA,gBAEjDlB,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,KAAK;oBAAApE,QAAA,EAAC;kBAAG;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,IAAI;oBAAApE,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,MAAM;oBAAApE,QAAA,EAAC;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtCxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,YAAY;oBAAApE,QAAA,EAAC;kBAAU;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClDxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,OAAO;oBAAApE,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxCxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,IAAI;oBAAApE,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClCxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,SAAS;oBAAApE,QAAA,EAAC;kBAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,UAAU;oBAAApE,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC3CxC,OAAA,CAAClC,QAAQ;oBAACwH,KAAK,EAAC,YAAY;oBAAApE,QAAA,EAAC;kBAAM;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEdxC,OAAA,CAAC7C,SAAS;gBACRuI,IAAI,EAAC,OAAO;gBACZuS,WAAW,EAAC,yCAAW;gBACvB3S,KAAK,EAAE4E,UAAW;gBAClBvE,QAAQ,EAAGhB,CAAC,IAAKwF,aAAa,CAACxF,CAAC,CAACiB,MAAM,CAACN,KAAK,CAAE;gBAC/C7D,EAAE,EAAE;kBAAEyW,QAAQ,EAAE,CAAC;kBAAElR,QAAQ,EAAE;gBAAI,CAAE;gBACnCmR,UAAU,EAAE;kBACVC,cAAc,eACZpY,OAAA,CAACjC,cAAc;oBAACsa,QAAQ,EAAC,OAAO;oBAAAnX,QAAA,eAC9BlB,OAAA,CAACb,UAAU;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBACD8V,YAAY,EAAEpO,UAAU,iBACtBlK,OAAA,CAACjC,cAAc;oBAACsa,QAAQ,EAAC,KAAK;oBAAAnX,QAAA,eAC5BlB,OAAA,CAAC5C,UAAU;sBACTsI,IAAI,EAAC,OAAO;sBACZvD,OAAO,EAAEA,CAAA,KAAMgI,aAAa,CAAC,EAAE,CAAE;sBACjCjI,IAAI,EAAC,KAAK;sBAAAhB,QAAA,eAEVlB,OAAA,CAACZ,SAAS;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAED0H,UAAU,iBACTlK,OAAA,CAACvD,UAAU;gBAACwJ,OAAO,EAAC,OAAO;gBAACnD,KAAK,EAAC,gBAAgB;gBAAA5B,QAAA,GAAC,eAC9C,EAACqN,gBAAgB,CAAChD,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAM,EAAC,qBAChE;cAAA;gBAAA5I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPxC,OAAA,CAAC9B,IAAI;YAACgW,IAAI;YAAC4D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7W,QAAA,gBACvBlB,OAAA,CAACvD,UAAU;cAACwJ,OAAO,EAAC,WAAW;cAACxE,EAAE,EAAE;gBAAE0V,UAAU,EAAE,GAAG;gBAAEK,EAAE,EAAE,CAAC;gBAAE9U,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEsU,GAAG,EAAE;cAAE,CAAE;cAAA/V,QAAA,gBAC5GlB,OAAA,CAACT,YAAY;gBAACkC,EAAE,EAAE;kBAAEqB,KAAK,EAAE;gBAAe;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbxC,OAAA,CAACtC,KAAK;cAACgN,SAAS,EAAE;gBAAEoN,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAAzW,QAAA,gBACxDlB,OAAA,CAACtD,MAAM;gBACLuJ,OAAO,EAAC,WAAW;gBACnBnD,KAAK,EAAC,SAAS;gBACfoU,SAAS,eAAElX,OAAA,CAACrB,YAAY;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BL,OAAO,EAAE8P,cAAe;gBAAA/Q,QAAA,EACzB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETxC,OAAA,CAACtD,MAAM;gBACLuJ,OAAO,EAAC,WAAW;gBACnBnD,KAAK,EAAC,SAAS;gBACfoU,SAAS,EAAEnO,oBAAoB,gBAAG/I,OAAA,CAAC3C,gBAAgB;kBAACqI,IAAI,EAAE,EAAG;kBAAC5C,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxC,OAAA,CAACf,aAAa;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrGL,OAAO,EAAEiT,gBAAiB;gBAC1BmD,QAAQ,EAAExP,oBAAqB;gBAAA7H,QAAA,EAE9B6H,oBAAoB,GAAG,QAAQ,GAAG;cAAW;gBAAA1G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAETxC,OAAA,CAACtD,MAAM;gBACLuJ,OAAO,EAAC,UAAU;gBAClBnD,KAAK,EAAC,OAAO;gBACboU,SAAS,eAAElX,OAAA,CAACpB,cAAc;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BL,OAAO,EAAE0Q,aAAc;gBAAA3R,QAAA,EACxB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPxC,OAAA,CAACL,eAAe;MAAC6Y,SAAS,EAAE5M,aAAc;MAAA1K,QAAA,eACxClB,OAAA,CAACrD,KAAK;QAAC8E,EAAE,EAAE;UACT0D,KAAK,EAAE,MAAM;UACbgC,QAAQ,EAAE,QAAQ;UAClB,iBAAiB,EAAE;YACjBzF,SAAS,EAAE,cAAc;YACzBG,SAAS,EAAE,CAAC;YACZC,MAAM,EAAE;UACV,CAAC;UACD,qBAAqB,EAAE;YACrBF,UAAU,EAAE;UACd,CAAC;UACD,sBAAsB,EAAE;YACtBsE,YAAY,EAAE,WAAW;YACzBuS,WAAW,EAAE,WAAW;YACxBC,WAAW,EAAE;UACf;QACF,CAAE;QAAAxX,QAAA,gBACAlB,OAAA,CAAC1B,cAAc;UAAA4C,QAAA,eACblB,OAAA,CAAC7B,KAAK;YAACwa,YAAY;YAAAzX,QAAA,gBACjBlB,OAAA,CAACzB,SAAS;cAAA2C,QAAA,eACRlB,OAAA,CAACxB,QAAQ;gBAACiD,EAAE,EAAE;kBAAE6F,MAAM,EAAE;gBAAG,CAAE;gBAAApG,QAAA,gBAE3BlB,OAAA,CAAC3B,SAAS;kBAACoD,EAAE,EAAE;oBACb0D,KAAK,EAAE,EAAE;oBACTgS,UAAU,EAAE,MAAM;oBAClB7P,MAAM,EAAE,EAAE;oBACV9B,OAAO,EAAE;kBACX,CAAE;kBAAAtE,QAAA,EAAC;gBAEH;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EACXe,OAAO,CAAC8B,GAAG,CAAEpB,MAAM,iBAClBjE,OAAA,CAAC3B,SAAS;kBAERoD,EAAE,EAAE;oBACF0V,UAAU,EAAE,MAAM;oBAClBpV,eAAe,EAAE,oBAAoB;oBACrC0W,WAAW,EAAE,WAAW;oBACxBC,WAAW,EAAE,SAAS;oBACtBpR,MAAM,EAAE,EAAE;oBACV9B,OAAO,EAAE,KAAK;oBACd,cAAc,EAAE;sBACdiT,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAvX,QAAA,EAGD+C,MAAM,CAACG,KAAK,KAAK,QAAQ,IAAIH,MAAM,CAACG,KAAK,KAAK,SAAS,gBACtDpE,OAAA,CAACtB,cAAc;oBACbka,MAAM,EAAEpO,UAAU,CAACpG,KAAK,KAAKH,MAAM,CAACG,KAAM;oBAC1CsG,SAAS,EAAEF,UAAU,CAACpG,KAAK,KAAKH,MAAM,CAACG,KAAK,GAAGoG,UAAU,CAACE,SAAS,GAAG,KAAM;oBAC5EvI,OAAO,EAAEA,CAAA,KAAMwI,UAAU,CAAC1G,MAAM,CAACG,KAAK,CAAE;oBACxC3C,EAAE,EAAE;sBACF,2BAA2B,EAAE;wBAC3BsB,QAAQ,EAAE;sBACZ;oBACF,CAAE;oBAAA7B,QAAA,EAED+C,MAAM,CAACiE;kBAAU;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,GAEjByB,MAAM,CAACiE;gBACR,GA7BIjE,MAAM,CAACG,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BR,CACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEZxC,OAAA,CAACJ,SAAS;cAACiZ,WAAW,EAAC,YAAY;cAAA3X,QAAA,EAChCA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBpB,OAAA,CAAC5B,SAAS;gBACRiD,GAAG,EAAEF,QAAQ,CAACG,QAAS;gBAAA,GACnBH,QAAQ,CAAC2X,cAAc;gBAC3BrX,EAAE,EAAE;kBACFM,eAAe,EAAEX,QAAQ,CAAC2X,cAAc,GAAG,cAAc,GAAG,SAAS;kBACrEnX,UAAU,EAAE,4BAA4B;kBACxCoX,SAAS,EAAE,WAAW;kBACtBN,WAAW,EAAE;gBACf,CAAE;gBAAAxX,QAAA,GAGDoO,YAAY,CACV/D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC,CAAC;gBAAA,CAClC4N,KAAK,CACJ9H,eAAe,CAACL,IAAI,GAAGK,eAAe,CAACE,QAAQ,EAC/C,CAACF,eAAe,CAACL,IAAI,GAAG,CAAC,IAAIK,eAAe,CAACE,QAC/C,CAAC,CACAhM,GAAG,CAAC,CAAC/B,GAAG,EAAExC,KAAK,kBACdd,OAAA,CAACmD,iBAAiB;kBAEhBG,GAAG,EAAEA,GAAI;kBACTxC,KAAK,EAAEA,KAAM;kBACbyC,OAAO,EAAEA,OAAQ;kBACjBC,UAAU,EAAEkJ,cAAe;kBAC3BjJ,cAAc,EAAE8L;gBAAmB,GAL9BjM,GAAG,CAACmB,EAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMZ,CACF,CAAC,EAGH8M,YAAY,CAACpL,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC,iBAC3CrL,OAAA,CAACxB,QAAQ;kBAAC0a,SAAS,EAAC,WAAW;kBAACzX,EAAE,EAAE;oBAClCM,eAAe,EAAE,0BAA0B;oBAC3CoV,UAAU,EAAE,MAAM;oBAClB,sBAAsB,EAAE;sBACtBA,UAAU,EAAE,MAAM;sBAClB6B,SAAS,EAAE,WAAW;sBACtBN,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAxX,QAAA,gBACAlB,OAAA,CAAC3B,SAAS;oBAACoD,EAAE,EAAE;sBAAE0D,KAAK,EAAE;oBAAG;kBAAE;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElB,CAAC,EACXe,OAAO,CAAC8B,GAAG,CAAEpB,MAAM,IAAK;oBACvB,MAAMmH,QAAQ,GAAGkE,YAAY,CAACpL,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC;oBAC7D,MAAM/F,KAAK,GAAG8F,QAAQ,GAAGA,QAAQ,CAACnH,MAAM,CAACG,KAAK,CAAC,GAAG,EAAE;oBACpD,oBACEpE,OAAA,CAAC3B,SAAS;sBAAoBoD,EAAE,EAAE;wBAAE+D,OAAO,EAAE;sBAAM,CAAE;sBAAAtE,QAAA,EAClD+C,MAAM,CAACmC,UAAU,GAAGnC,MAAM,CAACmC,UAAU,CAAC;wBAAE9C,GAAG,EAAE8H,QAAQ;wBAAE9F;sBAAM,CAAC,CAAC,GAAGA;oBAAK,GAD1DrB,MAAM,CAACG,KAAK;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEjB,CAAC;kBAEhB,CAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CACX,EAEArB,QAAQ,CAAC8W,WAAW;cAAA;gBAAA5V,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACZ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjBxC,OAAA,CAACvB,eAAe;UACd0a,SAAS,EAAC,KAAK;UACfC,KAAK,EAAE9J,YAAY,CAAC/D,MAAM,CAACjI,GAAG,IAAIA,GAAG,CAAC+H,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAO;UAC7D6F,IAAI,EAAEK,eAAe,CAACL,IAAK;UAC3BuI,YAAY,EAAEA,CAACC,KAAK,EAAEC,OAAO,KAAK;YAChC1I,kBAAkB,CAAC1E,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE2E,IAAI,EAAEyI;YAAQ,CAAC,CAAC,CAAC;UAC1D,CAAE;UACFC,WAAW,EAAErI,eAAe,CAACE,QAAS;UACtCoI,mBAAmB,EAAGH,KAAK,IAAK;YAC9B,MAAMI,WAAW,GAAGpI,QAAQ,CAACgI,KAAK,CAAC1T,MAAM,CAACN,KAAK,EAAE,EAAE,CAAC;YACpDuL,kBAAkB,CAAC;cAAEC,IAAI,EAAE,CAAC;cAAEO,QAAQ,EAAEqI;YAAY,CAAC,CAAC;YACtDnR,YAAY,CAACwC,OAAO,CAAC,kBAAkB,EAAE2O,WAAW,CAACzU,QAAQ,CAAC,CAAC,CAAC;UAClE,CAAE;UACF0U,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAClCC,gBAAgB,EAAC,2BAAO;UACxBC,kBAAkB,EAAEA,CAAC;YAAExF,IAAI;YAAEyF,EAAE;YAAEV;UAAM,CAAC,KAAK,GAAG/E,IAAI,IAAIyF,EAAE,MAAMV,KAAK;QAAK;UAAA/W,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGlBxC,OAAA,CAACpD,MAAM;MACLoV,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB+H,OAAO,EAAE1G,kBAAmB;MAC5BrN,SAAS;MACTiB,QAAQ,EAAC,IAAI;MACb+S,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjZ,QAAA,gBAEnBlB,OAAA,CAACnD,WAAW;QAAAqE,QAAA,eACVlB,OAAA,CAACxD,GAAG;UAACiF,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAE+U,cAAc,EAAE,eAAe;YAAE9U,UAAU,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBAClFlB,OAAA,CAACvD,UAAU;YAACwJ,OAAO,EAAC,IAAI;YAAA/E,QAAA,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CxC,OAAA,CAACtD,MAAM;YACLwa,SAAS,eAAElX,OAAA,CAACnB,OAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBL,OAAO,EAAE2R,mBAAoB;YAC7BhR,KAAK,EAAC,SAAS;YAAA5B,QAAA,EAChB;UAED;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdxC,OAAA,CAACxD,GAAG;QAACiF,EAAE,EAAE;UAAEmB,EAAE,EAAE,CAAC;UAAEwX,EAAE,EAAE;QAAE,CAAE;QAAAlZ,QAAA,gBACxBlB,OAAA,CAACvD,UAAU;UAACwJ,OAAO,EAAC,OAAO;UAACnD,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAE+V,EAAE,EAAE;UAAE,CAAE;UAAAtW,QAAA,EAAC;QAElE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxC,OAAA,CAACtC,KAAK;UAACgN,SAAS,EAAC,KAAK;UAACiN,OAAO,EAAE,CAAE;UAAAzW,QAAA,gBAChClB,OAAA,CAAC/B,gBAAgB;YACfoc,OAAO,eACLra,OAAA,CAAChC,QAAQ;cACPsc,OAAO,EAAEvJ,aAAc;cACvBpL,QAAQ,EAAGhB,CAAC,IAAKqM,gBAAgB,CAACrM,CAAC,CAACiB,MAAM,CAAC0U,OAAO,CAAE;cACpD5U,IAAI,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDsU,KAAK,EAAC;UAAS;YAAAzU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFxC,OAAA,CAAC/B,gBAAgB;YACfoc,OAAO,eACLra,OAAA,CAAChC,QAAQ;cACPsc,OAAO,EAAErJ,UAAW;cACpBtL,QAAQ,EAAGhB,CAAC,IAAKuM,aAAa,CAACvM,CAAC,CAACiB,MAAM,CAAC0U,OAAO,CAAE;cACjD5U,IAAI,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDsU,KAAK,EAAC;UAAK;YAAAzU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENxC,OAAA,CAAClD,aAAa;QAACyd,QAAQ;QAAC9Y,EAAE,EAAE;UAAE+Y,CAAC,EAAE;QAAE,CAAE;QAAAtZ,QAAA,eACnClB,OAAA,CAACL,eAAe;UAAC6Y,SAAS,EAAErE,0BAA2B;UAAAjT,QAAA,eACrDlB,OAAA,CAACJ,SAAS;YAACiZ,WAAW,EAAC,iBAAiB;YAAA3X,QAAA,EACrCA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBpB,OAAA,CAACxD,GAAG;cACF6E,GAAG,EAAEF,QAAQ,CAACG,QAAS;cAAA,GACnBH,QAAQ,CAAC2X,cAAc;cAC3BrX,EAAE,EAAE;gBACF6F,MAAM,EAAE,GAAG;gBACXmT,SAAS,EAAE,MAAM;gBACjB1Y,eAAe,EAAEX,QAAQ,CAAC2X,cAAc,GAAG,cAAc,GAAG,aAAa;gBACzEnX,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,GAEDkH,cAAc,CAAC/C,GAAG,CAAC,CAACxE,MAAM,EAAEC,KAAK,kBAChCd,OAAA,CAACU,sBAAsB;gBAErBG,MAAM,EAAEA,MAAO;gBACfC,KAAK,EAAEA,KAAM;gBACbC,QAAQ,EAAEuS,kBAAmB;gBAC7BtS,QAAQ,EAAEiT;cAAa,GAJlB,GAAGpT,MAAM,IAAIC,KAAK,EAAE;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAK1B,CACF,CAAC,EACDrB,QAAQ,CAAC8W,WAAW;YAAA;cAAA5V,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAChBxC,OAAA,CAACjD,aAAa;QAAAmE,QAAA,eACZlB,OAAA,CAACtD,MAAM;UAACyF,OAAO,EAAEkR,kBAAmB;UAAAnS,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxC,OAAA,CAACpD,MAAM;MACLoV,IAAI,EAAEnJ,eAAgB;MACtBkR,OAAO,EAAEhG,oBAAqB;MAC9B/N,SAAS;MACTiB,QAAQ,EAAC,IAAI;MACb+S,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjZ,QAAA,gBAEnBlB,OAAA,CAACnD,WAAW;QAAAqE,QAAA,EAAC;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCxC,OAAA,CAAClD,aAAa;QAAAoE,QAAA,eACZlB,OAAA,CAAC7C,SAAS;UACR4I,SAAS;UACT2U,MAAM,EAAC,OAAO;UACdjW,EAAE,EAAC,MAAM;UACTqS,KAAK,EAAC,0BAAM;UACZ6D,IAAI,EAAC,MAAM;UACX3U,SAAS;UACTC,OAAO,EAAC,UAAU;UAClBX,KAAK,EAAEqD,SAAU;UACjBhD,QAAQ,EAAGhB,CAAC,IAAKiE,YAAY,CAACjE,CAAC,CAACiB,MAAM,CAACN,KAAK;QAAE;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBxC,OAAA,CAACjD,aAAa;QAAAmE,QAAA,gBACZlB,OAAA,CAACtD,MAAM;UAACyF,OAAO,EAAE4R,oBAAqB;UAAA7S,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDxC,OAAA,CAACtD,MAAM;UAACyF,OAAO,EAAE6R,YAAa;UAAClR,KAAK,EAAC,SAAS;UAAA5B,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACwF,GAAA,CA5iDIP,aAAa;AAAAmT,GAAA,GAAbnT,aAAa;AA8iDnB,eAAeA,aAAa;AAAC,IAAA7G,EAAA,EAAAsC,GAAA,EAAAG,GAAA,EAAAgD,GAAA,EAAAI,GAAA,EAAAe,GAAA,EAAAoT,GAAA;AAAAC,YAAA,CAAAja,EAAA;AAAAia,YAAA,CAAA3X,GAAA;AAAA2X,YAAA,CAAAxX,GAAA;AAAAwX,YAAA,CAAAxU,GAAA;AAAAwU,YAAA,CAAApU,GAAA;AAAAoU,YAAA,CAAArT,GAAA;AAAAqT,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}