{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\WorksheetSelect.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Card, CardActionArea, Grid, Chip, useTheme } from '@mui/material';\nimport TableChartIcon from '@mui/icons-material/TableChart';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorksheetSelect = ({\n  worksheets,\n  onSelect,\n  onBack\n}) => {\n  _s();\n  const theme = useTheme();\n  const [hoveredIndex, setHoveredIndex] = useState(null);\n  const [selectedIndex, setSelectedIndex] = useState(null);\n  const handleSelect = (worksheet, index) => {\n    setSelectedIndex(index);\n    setTimeout(() => {\n      onSelect(worksheet);\n    }, 300);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 600,\n          color: 'text.primary',\n          mb: 1\n        },\n        children: \"\\u9009\\u62E9\\u5DE5\\u4F5C\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          color: 'text.secondary',\n          mb: 2\n        },\n        children: \"\\u8BF7\\u4ECE\\u4E0B\\u65B9\\u9009\\u62E9\\u8981\\u5904\\u7406\\u7684Excel\\u5DE5\\u4F5C\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: `共 ${worksheets.length} 个工作表`,\n        size: \"small\",\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 4\n      },\n      children: worksheets.map((worksheet, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 3\n            },\n            border: selectedIndex === index ? `2px solid ${theme.palette.primary.main}` : '1px solid',\n            borderColor: selectedIndex === index ? 'primary.main' : 'divider',\n            backgroundColor: selectedIndex === index ? 'primary.50' : 'background.paper'\n          },\n          onMouseEnter: () => setHoveredIndex(index),\n          onMouseLeave: () => setHoveredIndex(null),\n          children: /*#__PURE__*/_jsxDEV(CardActionArea, {\n            onClick: () => handleSelect(worksheet, index),\n            sx: {\n              height: '100%',\n              p: 3,\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              minHeight: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableChartIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: selectedIndex === index ? 'primary.main' : 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), selectedIndex === index && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                sx: {\n                  position: 'absolute',\n                  ml: 2,\n                  mt: -1,\n                  color: 'success.main',\n                  fontSize: 20\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                textAlign: 'center',\n                color: selectedIndex === index ? 'primary.main' : 'text.primary'\n              },\n              children: worksheet\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'text.secondary',\n                textAlign: 'center',\n                mt: 1\n              },\n              children: \"\\u70B9\\u51FB\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this)\n      }, worksheet, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-start'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 22\n        }, this),\n        onClick: onBack,\n        children: \"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u6B65\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(WorksheetSelect, \"JqyYhdKA/2Vip5s3B+/PU66slWs=\", false, function () {\n  return [useTheme];\n});\n_c = WorksheetSelect;\nexport default WorksheetSelect;\nvar _c;\n$RefreshReg$(_c, \"WorksheetSelect\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Card", "CardActionArea", "Grid", "Chip", "useTheme", "TableChartIcon", "ArrowBackIcon", "CheckCircleIcon", "jsxDEV", "_jsxDEV", "WorksheetSelect", "worksheets", "onSelect", "onBack", "_s", "theme", "hoveredIndex", "setHoveredIndex", "selectedIndex", "setSelectedIndex", "handleSelect", "worksheet", "index", "setTimeout", "children", "sx", "textAlign", "mb", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "length", "size", "container", "spacing", "map", "item", "xs", "sm", "md", "height", "cursor", "transition", "transform", "boxShadow", "border", "palette", "primary", "main", "borderColor", "backgroundColor", "onMouseEnter", "onMouseLeave", "onClick", "p", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "fontSize", "position", "ml", "mt", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/WorksheetSelect.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  CardActionArea,\r\n  Grid,\r\n  Chip,\r\n  useTheme\r\n} from '@mui/material';\r\nimport TableChartIcon from '@mui/icons-material/TableChart';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\r\n\r\nconst WorksheetSelect = ({ worksheets, onSelect, onBack }) => {\r\n  const theme = useTheme();\r\n  const [hoveredIndex, setHoveredIndex] = useState(null);\r\n  const [selectedIndex, setSelectedIndex] = useState(null);\r\n\r\n  const handleSelect = (worksheet, index) => {\r\n    setSelectedIndex(index);\r\n    setTimeout(() => {\r\n      onSelect(worksheet);\r\n    }, 300);\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      {/* 标题区域 */}\r\n      <Box sx={{ textAlign: 'center', mb: 4 }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            color: 'text.primary',\r\n            mb: 1,\r\n          }}\r\n        >\r\n          选择工作表\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            color: 'text.secondary',\r\n            mb: 2,\r\n          }}\r\n        >\r\n          请从下方选择要处理的Excel工作表\r\n        </Typography>\r\n        <Chip\r\n          label={`共 ${worksheets.length} 个工作表`}\r\n          size=\"small\"\r\n          color=\"primary\"\r\n          variant=\"outlined\"\r\n        />\r\n      </Box>\r\n\r\n      {/* 工作表卡片网格 */}\r\n      <Grid container spacing={2} sx={{ mb: 4 }}>\r\n        {worksheets.map((worksheet, index) => (\r\n          <Grid item xs={12} sm={6} md={4} key={worksheet}>\r\n            <Card\r\n              sx={{\r\n                height: '100%',\r\n                cursor: 'pointer',\r\n                transition: 'all 0.2s ease',\r\n                '&:hover': {\r\n                  transform: 'translateY(-4px)',\r\n                  boxShadow: 3,\r\n                },\r\n                border: selectedIndex === index\r\n                  ? `2px solid ${theme.palette.primary.main}`\r\n                  : '1px solid',\r\n                borderColor: selectedIndex === index\r\n                  ? 'primary.main'\r\n                  : 'divider',\r\n                backgroundColor: selectedIndex === index\r\n                  ? 'primary.50'\r\n                  : 'background.paper',\r\n              }}\r\n              onMouseEnter={() => setHoveredIndex(index)}\r\n              onMouseLeave={() => setHoveredIndex(null)}\r\n            >\r\n              <CardActionArea\r\n                onClick={() => handleSelect(worksheet, index)}\r\n                sx={{\r\n                  height: '100%',\r\n                  p: 3,\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  minHeight: 120,\r\n                }}\r\n              >\r\n                <Box sx={{ mb: 2 }}>\r\n                  <TableChartIcon\r\n                    sx={{\r\n                      fontSize: 40,\r\n                      color: selectedIndex === index ? 'primary.main' : 'text.secondary',\r\n                    }}\r\n                  />\r\n                  {selectedIndex === index && (\r\n                    <CheckCircleIcon\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        ml: 2,\r\n                        mt: -1,\r\n                        color: 'success.main',\r\n                        fontSize: 20,\r\n                      }}\r\n                    />\r\n                  )}\r\n                </Box>\r\n\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  sx={{\r\n                    fontWeight: 600,\r\n                    textAlign: 'center',\r\n                    color: selectedIndex === index ? 'primary.main' : 'text.primary',\r\n                  }}\r\n                >\r\n                  {worksheet}\r\n                </Typography>\r\n\r\n                <Typography\r\n                  variant=\"body2\"\r\n                  sx={{\r\n                    color: 'text.secondary',\r\n                    textAlign: 'center',\r\n                    mt: 1,\r\n                  }}\r\n                >\r\n                  点击选择\r\n                </Typography>\r\n              </CardActionArea>\r\n            </Card>\r\n          </Grid>\r\n        ))}\r\n      </Grid>\r\n\r\n      {/* 返回按钮 */}\r\n      <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>\r\n        <Button\r\n          variant=\"outlined\"\r\n          startIcon={<ArrowBackIcon />}\r\n          onClick={onBack}\r\n        >\r\n          返回上一步\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default WorksheetSelect; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAEC,QAAQ;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAMC,KAAK,GAAGX,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMwB,YAAY,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;IACzCH,gBAAgB,CAACG,KAAK,CAAC;IACvBC,UAAU,CAAC,MAAM;MACfX,QAAQ,CAACS,SAAS,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,oBACEZ,OAAA,CAACZ,GAAG;IAAA2B,QAAA,gBAEFf,OAAA,CAACZ,GAAG;MAAC4B,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACtCf,OAAA,CAACX,UAAU;QACT8B,OAAO,EAAC,IAAI;QACZH,EAAE,EAAE;UACFI,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,cAAc;UACrBH,EAAE,EAAE;QACN,CAAE;QAAAH,QAAA,EACH;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA,CAACX,UAAU;QACT8B,OAAO,EAAC,OAAO;QACfH,EAAE,EAAE;UACFK,KAAK,EAAE,gBAAgB;UACvBH,EAAE,EAAE;QACN,CAAE;QAAAH,QAAA,EACH;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA,CAACN,IAAI;QACHgC,KAAK,EAAE,KAAKxB,UAAU,CAACyB,MAAM,OAAQ;QACrCC,IAAI,EAAC,OAAO;QACZP,KAAK,EAAC,SAAS;QACfF,OAAO,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA,CAACP,IAAI;MAACoC,SAAS;MAACC,OAAO,EAAE,CAAE;MAACd,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACvCb,UAAU,CAAC6B,GAAG,CAAC,CAACnB,SAAS,EAAEC,KAAK,kBAC/Bb,OAAA,CAACP,IAAI;QAACuC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eAC9Bf,OAAA,CAACT,IAAI;UACHyB,EAAE,EAAE;YACFoB,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,MAAM,EAAEhC,aAAa,KAAKI,KAAK,GAC3B,aAAaP,KAAK,CAACoC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GACzC,WAAW;YACfC,WAAW,EAAEpC,aAAa,KAAKI,KAAK,GAChC,cAAc,GACd,SAAS;YACbiC,eAAe,EAAErC,aAAa,KAAKI,KAAK,GACpC,YAAY,GACZ;UACN,CAAE;UACFkC,YAAY,EAAEA,CAAA,KAAMvC,eAAe,CAACK,KAAK,CAAE;UAC3CmC,YAAY,EAAEA,CAAA,KAAMxC,eAAe,CAAC,IAAI,CAAE;UAAAO,QAAA,eAE1Cf,OAAA,CAACR,cAAc;YACbyD,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAACC,SAAS,EAAEC,KAAK,CAAE;YAC9CG,EAAE,EAAE;cACFoB,MAAM,EAAE,MAAM;cACdc,CAAC,EAAE,CAAC;cACJC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,SAAS,EAAE;YACb,CAAE;YAAAxC,QAAA,gBAEFf,OAAA,CAACZ,GAAG;cAAC4B,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACjBf,OAAA,CAACJ,cAAc;gBACboB,EAAE,EAAE;kBACFwC,QAAQ,EAAE,EAAE;kBACZnC,KAAK,EAAEZ,aAAa,KAAKI,KAAK,GAAG,cAAc,GAAG;gBACpD;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDhB,aAAa,KAAKI,KAAK,iBACtBb,OAAA,CAACF,eAAe;gBACdkB,EAAE,EAAE;kBACFyC,QAAQ,EAAE,UAAU;kBACpBC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC,CAAC;kBACNtC,KAAK,EAAE,cAAc;kBACrBmC,QAAQ,EAAE;gBACZ;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzB,OAAA,CAACX,UAAU;cACT8B,OAAO,EAAC,WAAW;cACnBH,EAAE,EAAE;gBACFI,UAAU,EAAE,GAAG;gBACfH,SAAS,EAAE,QAAQ;gBACnBI,KAAK,EAAEZ,aAAa,KAAKI,KAAK,GAAG,cAAc,GAAG;cACpD,CAAE;cAAAE,QAAA,EAEDH;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEbzB,OAAA,CAACX,UAAU;cACT8B,OAAO,EAAC,OAAO;cACfH,EAAE,EAAE;gBACFK,KAAK,EAAE,gBAAgB;gBACvBJ,SAAS,EAAE,QAAQ;gBACnB0C,EAAE,EAAE;cACN,CAAE;cAAA5C,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC,GA7E6Bb,SAAS;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8EzC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPzB,OAAA,CAACZ,GAAG;MAAC4B,EAAE,EAAE;QAAEmC,OAAO,EAAE,MAAM;QAAEG,cAAc,EAAE;MAAa,CAAE;MAAAvC,QAAA,eACzDf,OAAA,CAACV,MAAM;QACL6B,OAAO,EAAC,UAAU;QAClByC,SAAS,eAAE5D,OAAA,CAACH,aAAa;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BwB,OAAO,EAAE7C,MAAO;QAAAW,QAAA,EACjB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CA5IIJ,eAAe;EAAA,QACLN,QAAQ;AAAA;AAAAkE,EAAA,GADlB5D,eAAe;AA8IrB,eAAeA,eAAe;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}