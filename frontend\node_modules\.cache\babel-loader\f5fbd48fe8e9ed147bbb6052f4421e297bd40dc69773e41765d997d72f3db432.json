{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridRowsLookupSelector } from '../rows/gridRowsSelector';\nimport { gridRowSelectionStateSelector, selectedGridRowsSelector, selectedIdsLookupSelector } from './gridRowSelectionSelector';\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from '../pagination';\nimport { gridFocusCellSelector } from '../focus/gridFocusStateSelector';\nimport { gridExpandedSortedRowIdsSelector, gridFilterModelSelector } from '../filter/gridFilterSelector';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from '../../../colDef';\nimport { GridCellModes } from '../../../models/gridEditRowModel';\nimport { isKeyboardEvent, isNavigationKey } from '../../../utils/keyboardUtils';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from '../../../constants/gridDetailPanelToggleField';\nimport { gridClasses } from '../../../constants/gridClasses';\nimport { isEventTargetInPortal } from '../../../utils/domUtils';\nconst getSelectionModelPropValue = (selectionModelProp, prevSelectionModel) => {\n  if (selectionModelProp == null) {\n    return selectionModelProp;\n  }\n  if (Array.isArray(selectionModelProp)) {\n    return selectionModelProp;\n  }\n  if (prevSelectionModel && prevSelectionModel[0] === selectionModelProp) {\n    return prevSelectionModel;\n  }\n  return [selectionModelProp];\n};\nexport const rowSelectionStateInitializer = (state, props) => {\n  var _getSelectionModelPro;\n  return _extends({}, state, {\n    rowSelection: props.rowSelection ? (_getSelectionModelPro = getSelectionModelPropValue(props.rowSelectionModel)) != null ? _getSelectionModelPro : [] : []\n  });\n};\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  };\n  const propRowSelectionModel = React.useMemo(() => {\n    return getSelectionModelPropValue(props.rowSelectionModel, gridRowSelectionStateSelector(apiRef.current.state));\n  }, [apiRef, props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableMultipleRowSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = !disableMultipleRowSelection || checkboxSelection;\n  const visibleRows = useGridVisibleRows(apiRef, props);\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    var _lastRowToggled$curre;\n    let endId = id;\n    const startId = (_lastRowToggled$curre = lastRowToggled.current) != null ? _lastRowToggled$curre : id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback(model => {\n    if (props.signature === GridSignature.DataGrid && !props.checkboxSelection && Array.isArray(model) && model.length > 1) {\n      throw new Error(['MUI: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef.current.state);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : []\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, props.checkboxSelection]);\n  const isRowSelected = React.useCallback(id => gridRowSelectionStateSelector(apiRef.current.state).includes(id), [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = apiRef.current.getRowNode(id);\n    if ((rowNode == null ? void 0 : rowNode.type) === 'footer' || (rowNode == null ? void 0 : rowNode.type) === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => selectedGridRowsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      apiRef.current.setRowSelectionModel(isSelected ? [id] : []);\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selection = gridRowSelectionStateSelector(apiRef.current.state);\n      const newSelection = selection.filter(el => el !== id);\n      if (isSelected) {\n        newSelection.push(id);\n      }\n      const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, logger, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    const selectableIds = ids.filter(id => apiRef.current.isRowSelectable(id));\n    let newSelection;\n    if (resetSelection) {\n      newSelection = isSelected ? selectableIds : [];\n    } else {\n      // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n      const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n      selectableIds.forEach(id => {\n        if (isSelected) {\n          selectionLookup[id] = id;\n        } else {\n          delete selectionLookup[id];\n        }\n      });\n      newSelection = Object.values(selectionLookup);\n    }\n    const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelection);\n    }\n  }, [apiRef, logger, canHaveMultipleSelection]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * EVENTS\n   */\n  const removeOutdatedSelection = React.useCallback(() => {\n    if (props.keepNonExistentRowsSelected) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n    const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n    let hasChanged = false;\n    currentSelection.forEach(id => {\n      if (!rowsLookup[id]) {\n        delete selectionLookup[id];\n        hasChanged = true;\n      }\n    });\n    if (hasChanged) {\n      apiRef.current.setRowSelectionModel(Object.values(selectionLookup));\n    }\n  }, [apiRef, props.keepNonExistentRowsSelected]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (resetSelection) {\n      apiRef.current.selectRow(id, !isMultipleSelectionDisabled ? !isSelected : true, true);\n    } else {\n      apiRef.current.selectRow(id, !isSelected, false);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    var _closest;\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = (_closest = event.target.closest(`.${gridClasses.cell}`)) == null ? void 0 : _closest.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if ((column == null ? void 0 : column.type) === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = apiRef.current.getRowNode(params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && (canHaveMultipleSelection || checkboxSelection)) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, checkboxSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      var _window$getSelection;\n      (_window$getSelection = window.getSelection()) == null || _window$getSelection.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value);\n    }\n  }, [apiRef, expandMouseRowRangeSelection]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    const shouldLimitSelectionToCurrentPage = props.checkboxSelectionVisibleOnly && props.pagination;\n    const rowsToBeSelected = shouldLimitSelectionToCurrentPage ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    const filterModel = gridFilterModelSelector(apiRef);\n    apiRef.current.selectRows(rowsToBeSelected, params.value, (filterModel == null ? void 0 : filterModel.items.length) > 0);\n  }, [apiRef, props.checkboxSelectionVisibleOnly, props.pagination]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const rowsBetweenStartAndEnd = visibleRows.rows.slice(start, end + 1).map(row => row.id);\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      selectRows(apiRef.current.getAllRowIds(), true);\n    }\n  }, [apiRef, handleSingleRowSelection, selectRows, visibleRows.rows, canHaveMultipleSelection]);\n  useGridApiEventHandler(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(removeOutdatedSelection));\n  useGridApiEventHandler(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridApiEventHandler(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridApiEventHandler(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (propRowSelectionModel !== undefined) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n    }\n  }, [apiRef, propRowSelectionModel, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, props.rowSelection]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection) {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (isRowSelectable) {\n      const newSelection = currentSelection.filter(id => isRowSelectable(id));\n      if (newSelection.length < currentSelection.length) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (!canHaveMultipleSelection && currentSelection.length > 1) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n};", "map": {"version": 3, "names": ["_extends", "React", "GridSignature", "useGridApiEventHandler", "useGridApiMethod", "useGridLogger", "gridRowsLookupSelector", "gridRowSelectionStateSelector", "selectedGridRowsSelector", "selectedIdsLookupSelector", "gridPaginatedVisibleSortedGridRowIdsSelector", "gridFocusCellSelector", "gridExpandedSortedRowIdsSelector", "gridFilterModelSelector", "GRID_CHECKBOX_SELECTION_COL_DEF", "GRID_ACTIONS_COLUMN_TYPE", "GridCellModes", "isKeyboardEvent", "isNavigationKey", "useGridVisibleRows", "GRID_DETAIL_PANEL_TOGGLE_FIELD", "gridClasses", "isEventTargetInPortal", "getSelectionModelPropValue", "selectionModelProp", "prevSelectionModel", "Array", "isArray", "rowSelectionStateInitializer", "state", "props", "_getSelectionModelPro", "rowSelection", "rowSelectionModel", "useGridRowSelection", "apiRef", "logger", "runIfRowSelectionIsEnabled", "callback", "args", "propRowSelectionModel", "useMemo", "current", "lastRowToggled", "useRef", "registerControlState", "stateId", "propModel", "propOnChange", "onRowSelectionModelChange", "stateSelector", "changeEvent", "checkboxSelection", "disableMultipleRowSelection", "disableRowSelectionOnClick", "isRowSelectable", "propIsRowSelectable", "canHaveMultipleSelection", "visibleRows", "expandMouseRowRangeSelection", "useCallback", "id", "_lastRowToggled$curre", "endId", "startId", "isSelected", "isRowSelected", "visibleRowIds", "startIndex", "findIndex", "rowId", "endIndex", "selectRowRange", "setRowSelectionModel", "model", "signature", "DataGrid", "length", "Error", "join", "currentModel", "debug", "setState", "forceUpdate", "includes", "getRowParams", "rowNode", "getRowNode", "type", "getSelectedRows", "selectRow", "resetSelection", "selection", "newSelection", "filter", "el", "push", "isSelectionValid", "selectRows", "ids", "selectableIds", "selection<PERSON><PERSON><PERSON>", "for<PERSON>ach", "Object", "values", "getRow", "allPagesRowIds", "indexOf", "start", "end", "rowsBetweenStartAndEnd", "slice", "selectionPublicApi", "selectionPrivateApi", "removeOutdatedSelection", "keepNonExistentRowsSelected", "currentSelection", "rowsLookup", "has<PERSON><PERSON>ed", "handleSingleRowSelection", "event", "hasCtrlKey", "metaKey", "ctrl<PERSON>ey", "isMultipleSelectionDisabled", "handleRowClick", "params", "_closest", "field", "target", "closest", "cell", "getAttribute", "column", "getColumn", "shift<PERSON>ey", "preventSelectionOnShift", "_window$getSelection", "window", "getSelection", "removeAllRanges", "handleRowSelectionCheckboxChange", "nativeEvent", "value", "handleHeaderSelectionCheckboxChange", "shouldLimitSelectionToCurrentPage", "checkboxSelectionVisibleOnly", "pagination", "rowsToBeSelected", "filterModel", "items", "handleCellKeyDown", "getCellMode", "Edit", "key", "focusCell", "preventDefault", "isNextRowSelected", "newRowIndex", "getRowIndexRelativeToVisibleRows", "previousRowIndex", "rows", "map", "row", "getAllRowIds", "useEffect", "undefined", "isStateControlled"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/features/rowSelection/useGridRowSelection.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridRowsLookupSelector } from '../rows/gridRowsSelector';\nimport { gridRowSelectionStateSelector, selectedGridRowsSelector, selectedIdsLookupSelector } from './gridRowSelectionSelector';\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from '../pagination';\nimport { gridFocusCellSelector } from '../focus/gridFocusStateSelector';\nimport { gridExpandedSortedRowIdsSelector, gridFilterModelSelector } from '../filter/gridFilterSelector';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from '../../../colDef';\nimport { GridCellModes } from '../../../models/gridEditRowModel';\nimport { isKeyboardEvent, isNavigationKey } from '../../../utils/keyboardUtils';\nimport { useGridVisibleRows } from '../../utils/useGridVisibleRows';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from '../../../constants/gridDetailPanelToggleField';\nimport { gridClasses } from '../../../constants/gridClasses';\nimport { isEventTargetInPortal } from '../../../utils/domUtils';\nconst getSelectionModelPropValue = (selectionModelProp, prevSelectionModel) => {\n  if (selectionModelProp == null) {\n    return selectionModelProp;\n  }\n  if (Array.isArray(selectionModelProp)) {\n    return selectionModelProp;\n  }\n  if (prevSelectionModel && prevSelectionModel[0] === selectionModelProp) {\n    return prevSelectionModel;\n  }\n  return [selectionModelProp];\n};\nexport const rowSelectionStateInitializer = (state, props) => {\n  var _getSelectionModelPro;\n  return _extends({}, state, {\n    rowSelection: props.rowSelection ? (_getSelectionModelPro = getSelectionModelPropValue(props.rowSelectionModel)) != null ? _getSelectionModelPro : [] : []\n  });\n};\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  };\n  const propRowSelectionModel = React.useMemo(() => {\n    return getSelectionModelPropValue(props.rowSelectionModel, gridRowSelectionStateSelector(apiRef.current.state));\n  }, [apiRef, props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableMultipleRowSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = !disableMultipleRowSelection || checkboxSelection;\n  const visibleRows = useGridVisibleRows(apiRef, props);\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    var _lastRowToggled$curre;\n    let endId = id;\n    const startId = (_lastRowToggled$curre = lastRowToggled.current) != null ? _lastRowToggled$curre : id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback(model => {\n    if (props.signature === GridSignature.DataGrid && !props.checkboxSelection && Array.isArray(model) && model.length > 1) {\n      throw new Error(['MUI: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef.current.state);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : []\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, props.checkboxSelection]);\n  const isRowSelected = React.useCallback(id => gridRowSelectionStateSelector(apiRef.current.state).includes(id), [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = apiRef.current.getRowNode(id);\n    if ((rowNode == null ? void 0 : rowNode.type) === 'footer' || (rowNode == null ? void 0 : rowNode.type) === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => selectedGridRowsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      apiRef.current.setRowSelectionModel(isSelected ? [id] : []);\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selection = gridRowSelectionStateSelector(apiRef.current.state);\n      const newSelection = selection.filter(el => el !== id);\n      if (isSelected) {\n        newSelection.push(id);\n      }\n      const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, logger, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    const selectableIds = ids.filter(id => apiRef.current.isRowSelectable(id));\n    let newSelection;\n    if (resetSelection) {\n      newSelection = isSelected ? selectableIds : [];\n    } else {\n      // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n      const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n      selectableIds.forEach(id => {\n        if (isSelected) {\n          selectionLookup[id] = id;\n        } else {\n          delete selectionLookup[id];\n        }\n      });\n      newSelection = Object.values(selectionLookup);\n    }\n    const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelection);\n    }\n  }, [apiRef, logger, canHaveMultipleSelection]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * EVENTS\n   */\n  const removeOutdatedSelection = React.useCallback(() => {\n    if (props.keepNonExistentRowsSelected) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n    const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n    let hasChanged = false;\n    currentSelection.forEach(id => {\n      if (!rowsLookup[id]) {\n        delete selectionLookup[id];\n        hasChanged = true;\n      }\n    });\n    if (hasChanged) {\n      apiRef.current.setRowSelectionModel(Object.values(selectionLookup));\n    }\n  }, [apiRef, props.keepNonExistentRowsSelected]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (resetSelection) {\n      apiRef.current.selectRow(id, !isMultipleSelectionDisabled ? !isSelected : true, true);\n    } else {\n      apiRef.current.selectRow(id, !isSelected, false);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    var _closest;\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = (_closest = event.target.closest(`.${gridClasses.cell}`)) == null ? void 0 : _closest.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if ((column == null ? void 0 : column.type) === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = apiRef.current.getRowNode(params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && (canHaveMultipleSelection || checkboxSelection)) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, checkboxSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      var _window$getSelection;\n      (_window$getSelection = window.getSelection()) == null || _window$getSelection.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value);\n    }\n  }, [apiRef, expandMouseRowRangeSelection]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    const shouldLimitSelectionToCurrentPage = props.checkboxSelectionVisibleOnly && props.pagination;\n    const rowsToBeSelected = shouldLimitSelectionToCurrentPage ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    const filterModel = gridFilterModelSelector(apiRef);\n    apiRef.current.selectRows(rowsToBeSelected, params.value, (filterModel == null ? void 0 : filterModel.items.length) > 0);\n  }, [apiRef, props.checkboxSelectionVisibleOnly, props.pagination]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const rowsBetweenStartAndEnd = visibleRows.rows.slice(start, end + 1).map(row => row.id);\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      selectRows(apiRef.current.getAllRowIds(), true);\n    }\n  }, [apiRef, handleSingleRowSelection, selectRows, visibleRows.rows, canHaveMultipleSelection]);\n  useGridApiEventHandler(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(removeOutdatedSelection));\n  useGridApiEventHandler(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridApiEventHandler(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridApiEventHandler(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (propRowSelectionModel !== undefined) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n    }\n  }, [apiRef, propRowSelectionModel, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, props.rowSelection]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection) {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (isRowSelectable) {\n      const newSelection = currentSelection.filter(id => isRowSelectable(id));\n      if (newSelection.length < currentSelection.length) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (!canHaveMultipleSelection && currentSelection.length > 1) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,oCAAoC;AAC1F,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,6BAA6B,EAAEC,wBAAwB,EAAEC,yBAAyB,QAAQ,4BAA4B;AAC/H,SAASC,4CAA4C,QAAQ,eAAe;AAC5E,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,gCAAgC,EAAEC,uBAAuB,QAAQ,8BAA8B;AACxG,SAASC,+BAA+B,EAAEC,wBAAwB,QAAQ,iBAAiB;AAC3F,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,eAAe,EAAEC,eAAe,QAAQ,8BAA8B;AAC/E,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,8BAA8B,QAAQ,+CAA+C;AAC9F,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,MAAMC,0BAA0B,GAAGA,CAACC,kBAAkB,EAAEC,kBAAkB,KAAK;EAC7E,IAAID,kBAAkB,IAAI,IAAI,EAAE;IAC9B,OAAOA,kBAAkB;EAC3B;EACA,IAAIE,KAAK,CAACC,OAAO,CAACH,kBAAkB,CAAC,EAAE;IACrC,OAAOA,kBAAkB;EAC3B;EACA,IAAIC,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC,KAAKD,kBAAkB,EAAE;IACtE,OAAOC,kBAAkB;EAC3B;EACA,OAAO,CAACD,kBAAkB,CAAC;AAC7B,CAAC;AACD,OAAO,MAAMI,4BAA4B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC5D,IAAIC,qBAAqB;EACzB,OAAO/B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACzBG,YAAY,EAAEF,KAAK,CAACE,YAAY,GAAG,CAACD,qBAAqB,GAAGR,0BAA0B,CAACO,KAAK,CAACG,iBAAiB,CAAC,KAAK,IAAI,GAAGF,qBAAqB,GAAG,EAAE,GAAG;EAC1J,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,mBAAmB,GAAGA,CAACC,MAAM,EAAEL,KAAK,KAAK;EACpD,MAAMM,MAAM,GAAG/B,aAAa,CAAC8B,MAAM,EAAE,kBAAkB,CAAC;EACxD,MAAME,0BAA0B,GAAGC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IAC1D,IAAIT,KAAK,CAACE,YAAY,EAAE;MACtBM,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMC,qBAAqB,GAAGvC,KAAK,CAACwC,OAAO,CAAC,MAAM;IAChD,OAAOlB,0BAA0B,CAACO,KAAK,CAACG,iBAAiB,EAAE1B,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC,CAAC;EACjH,CAAC,EAAE,CAACM,MAAM,EAAEL,KAAK,CAACG,iBAAiB,CAAC,CAAC;EACrC,MAAMU,cAAc,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACzCT,MAAM,CAACO,OAAO,CAACG,oBAAoB,CAAC;IAClCC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAEP,qBAAqB;IAChCQ,YAAY,EAAElB,KAAK,CAACmB,yBAAyB;IAC7CC,aAAa,EAAE3C,6BAA6B;IAC5C4C,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM;IACJC,iBAAiB;IACjBC,2BAA2B;IAC3BC,0BAA0B;IAC1BC,eAAe,EAAEC;EACnB,CAAC,GAAG1B,KAAK;EACT,MAAM2B,wBAAwB,GAAG,CAACJ,2BAA2B,IAAID,iBAAiB;EAClF,MAAMM,WAAW,GAAGvC,kBAAkB,CAACgB,MAAM,EAAEL,KAAK,CAAC;EACrD,MAAM6B,4BAA4B,GAAG1D,KAAK,CAAC2D,WAAW,CAACC,EAAE,IAAI;IAC3D,IAAIC,qBAAqB;IACzB,IAAIC,KAAK,GAAGF,EAAE;IACd,MAAMG,OAAO,GAAG,CAACF,qBAAqB,GAAGnB,cAAc,CAACD,OAAO,KAAK,IAAI,GAAGoB,qBAAqB,GAAGD,EAAE;IACrG,MAAMI,UAAU,GAAG9B,MAAM,CAACO,OAAO,CAACwB,aAAa,CAACL,EAAE,CAAC;IACnD,IAAII,UAAU,EAAE;MACd,MAAME,aAAa,GAAGvD,gCAAgC,CAACuB,MAAM,CAAC;MAC9D,MAAMiC,UAAU,GAAGD,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKN,OAAO,CAAC;MACtE,MAAMO,QAAQ,GAAGJ,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKP,KAAK,CAAC;MAClE,IAAIK,UAAU,KAAKG,QAAQ,EAAE;QAC3B;MACF;MACA,IAAIH,UAAU,GAAGG,QAAQ,EAAE;QACzBR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC;IACF;IACA5B,cAAc,CAACD,OAAO,GAAGmB,EAAE;IAC3B1B,MAAM,CAACO,OAAO,CAAC8B,cAAc,CAAC;MAC5BR,OAAO;MACPD;IACF,CAAC,EAAE,CAACE,UAAU,CAAC;EACjB,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMsC,oBAAoB,GAAGxE,KAAK,CAAC2D,WAAW,CAACc,KAAK,IAAI;IACtD,IAAI5C,KAAK,CAAC6C,SAAS,KAAKzE,aAAa,CAAC0E,QAAQ,IAAI,CAAC9C,KAAK,CAACsB,iBAAiB,IAAI1B,KAAK,CAACC,OAAO,CAAC+C,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACtH,MAAM,IAAIC,KAAK,CAAC,CAAC,+DAA+D,EAAE,+FAA+F,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChM;IACA,MAAMC,YAAY,GAAGzE,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC;IACxE,IAAImD,YAAY,KAAKN,KAAK,EAAE;MAC1BtC,MAAM,CAAC6C,KAAK,CAAC,yBAAyB,CAAC;MACvC9C,MAAM,CAACO,OAAO,CAACwC,QAAQ,CAACrD,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACnDG,YAAY,EAAEF,KAAK,CAACE,YAAY,GAAG0C,KAAK,GAAG;MAC7C,CAAC,CAAC,CAAC;MACHvC,MAAM,CAACO,OAAO,CAACyC,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAChD,MAAM,EAAEC,MAAM,EAAEN,KAAK,CAACE,YAAY,EAAEF,KAAK,CAAC6C,SAAS,EAAE7C,KAAK,CAACsB,iBAAiB,CAAC,CAAC;EAClF,MAAMc,aAAa,GAAGjE,KAAK,CAAC2D,WAAW,CAACC,EAAE,IAAItD,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC,CAACuD,QAAQ,CAACvB,EAAE,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC;EACzH,MAAMoB,eAAe,GAAGtD,KAAK,CAAC2D,WAAW,CAACC,EAAE,IAAI;IAC9C,IAAIL,mBAAmB,IAAI,CAACA,mBAAmB,CAACrB,MAAM,CAACO,OAAO,CAAC2C,YAAY,CAACxB,EAAE,CAAC,CAAC,EAAE;MAChF,OAAO,KAAK;IACd;IACA,MAAMyB,OAAO,GAAGnD,MAAM,CAACO,OAAO,CAAC6C,UAAU,CAAC1B,EAAE,CAAC;IAC7C,IAAI,CAACyB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,IAAI,MAAM,QAAQ,IAAI,CAACF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,IAAI,MAAM,WAAW,EAAE;MACvH,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACrD,MAAM,EAAEqB,mBAAmB,CAAC,CAAC;EACjC,MAAMiC,eAAe,GAAGxF,KAAK,CAAC2D,WAAW,CAAC,MAAMpD,wBAAwB,CAAC2B,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3F,MAAMuD,SAAS,GAAGzF,KAAK,CAAC2D,WAAW,CAAC,CAACC,EAAE,EAAEI,UAAU,GAAG,IAAI,EAAE0B,cAAc,GAAG,KAAK,KAAK;IACrF,IAAI,CAACxD,MAAM,CAACO,OAAO,CAACa,eAAe,CAACM,EAAE,CAAC,EAAE;MACvC;IACF;IACAlB,cAAc,CAACD,OAAO,GAAGmB,EAAE;IAC3B,IAAI8B,cAAc,EAAE;MAClBvD,MAAM,CAAC6C,KAAK,CAAC,6BAA6BpB,EAAE,EAAE,CAAC;MAC/C1B,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAACR,UAAU,GAAG,CAACJ,EAAE,CAAC,GAAG,EAAE,CAAC;IAC7D,CAAC,MAAM;MACLzB,MAAM,CAAC6C,KAAK,CAAC,8BAA8BpB,EAAE,EAAE,CAAC;MAChD,MAAM+B,SAAS,GAAGrF,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC;MACrE,MAAMgE,YAAY,GAAGD,SAAS,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKlC,EAAE,CAAC;MACtD,IAAII,UAAU,EAAE;QACd4B,YAAY,CAACG,IAAI,CAACnC,EAAE,CAAC;MACvB;MACA,MAAMoC,gBAAgB,GAAGJ,YAAY,CAAChB,MAAM,GAAG,CAAC,IAAIpB,wBAAwB;MAC5E,IAAIwC,gBAAgB,EAAE;QACpB9D,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAACoB,YAAY,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC1D,MAAM,EAAEC,MAAM,EAAEqB,wBAAwB,CAAC,CAAC;EAC9C,MAAMyC,UAAU,GAAGjG,KAAK,CAAC2D,WAAW,CAAC,CAACuC,GAAG,EAAElC,UAAU,GAAG,IAAI,EAAE0B,cAAc,GAAG,KAAK,KAAK;IACvFvD,MAAM,CAAC6C,KAAK,CAAC,oCAAoC,CAAC;IAClD,MAAMmB,aAAa,GAAGD,GAAG,CAACL,MAAM,CAACjC,EAAE,IAAI1B,MAAM,CAACO,OAAO,CAACa,eAAe,CAACM,EAAE,CAAC,CAAC;IAC1E,IAAIgC,YAAY;IAChB,IAAIF,cAAc,EAAE;MAClBE,YAAY,GAAG5B,UAAU,GAAGmC,aAAa,GAAG,EAAE;IAChD,CAAC,MAAM;MACL;MACA,MAAMC,eAAe,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAES,yBAAyB,CAAC0B,MAAM,CAAC,CAAC;MACvEiE,aAAa,CAACE,OAAO,CAACzC,EAAE,IAAI;QAC1B,IAAII,UAAU,EAAE;UACdoC,eAAe,CAACxC,EAAE,CAAC,GAAGA,EAAE;QAC1B,CAAC,MAAM;UACL,OAAOwC,eAAe,CAACxC,EAAE,CAAC;QAC5B;MACF,CAAC,CAAC;MACFgC,YAAY,GAAGU,MAAM,CAACC,MAAM,CAACH,eAAe,CAAC;IAC/C;IACA,MAAMJ,gBAAgB,GAAGJ,YAAY,CAAChB,MAAM,GAAG,CAAC,IAAIpB,wBAAwB;IAC5E,IAAIwC,gBAAgB,EAAE;MACpB9D,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAACoB,YAAY,CAAC;IACnD;EACF,CAAC,EAAE,CAAC1D,MAAM,EAAEC,MAAM,EAAEqB,wBAAwB,CAAC,CAAC;EAC9C,MAAMe,cAAc,GAAGvE,KAAK,CAAC2D,WAAW,CAAC,CAAC;IACxCI,OAAO;IACPD;EACF,CAAC,EAAEE,UAAU,GAAG,IAAI,EAAE0B,cAAc,GAAG,KAAK,KAAK;IAC/C,IAAI,CAACxD,MAAM,CAACO,OAAO,CAAC+D,MAAM,CAACzC,OAAO,CAAC,IAAI,CAAC7B,MAAM,CAACO,OAAO,CAAC+D,MAAM,CAAC1C,KAAK,CAAC,EAAE;MACpE;IACF;IACA3B,MAAM,CAAC6C,KAAK,CAAC,gCAAgCjB,OAAO,WAAWD,KAAK,EAAE,CAAC;;IAEvE;IACA,MAAM2C,cAAc,GAAG9F,gCAAgC,CAACuB,MAAM,CAAC;IAC/D,MAAMiC,UAAU,GAAGsC,cAAc,CAACC,OAAO,CAAC3C,OAAO,CAAC;IAClD,MAAMO,QAAQ,GAAGmC,cAAc,CAACC,OAAO,CAAC5C,KAAK,CAAC;IAC9C,MAAM,CAAC6C,KAAK,EAAEC,GAAG,CAAC,GAAGzC,UAAU,GAAGG,QAAQ,GAAG,CAACA,QAAQ,EAAEH,UAAU,CAAC,GAAG,CAACA,UAAU,EAAEG,QAAQ,CAAC;IAC5F,MAAMuC,sBAAsB,GAAGJ,cAAc,CAACK,KAAK,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;IACnE1E,MAAM,CAACO,OAAO,CAACwD,UAAU,CAACY,sBAAsB,EAAE7C,UAAU,EAAE0B,cAAc,CAAC;EAC/E,CAAC,EAAE,CAACxD,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAM4E,kBAAkB,GAAG;IACzBtB,SAAS;IACTjB,oBAAoB;IACpBgB,eAAe;IACfvB,aAAa;IACbX;EACF,CAAC;EACD,MAAM0D,mBAAmB,GAAG;IAC1Bf,UAAU;IACV1B;EACF,CAAC;EACDpE,gBAAgB,CAAC+B,MAAM,EAAE6E,kBAAkB,EAAE,QAAQ,CAAC;EACtD5G,gBAAgB,CAAC+B,MAAM,EAAE8E,mBAAmB,EAAEnF,KAAK,CAAC6C,SAAS,KAAKzE,aAAa,CAAC0E,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAEhH;AACF;AACA;EACE,MAAMsC,uBAAuB,GAAGjH,KAAK,CAAC2D,WAAW,CAAC,MAAM;IACtD,IAAI9B,KAAK,CAACqF,2BAA2B,EAAE;MACrC;IACF;IACA,MAAMC,gBAAgB,GAAG7G,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC;IAC5E,MAAMwF,UAAU,GAAG/G,sBAAsB,CAAC6B,MAAM,CAAC;;IAEjD;IACA,MAAMkE,eAAe,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAES,yBAAyB,CAAC0B,MAAM,CAAC,CAAC;IACvE,IAAImF,UAAU,GAAG,KAAK;IACtBF,gBAAgB,CAACd,OAAO,CAACzC,EAAE,IAAI;MAC7B,IAAI,CAACwD,UAAU,CAACxD,EAAE,CAAC,EAAE;QACnB,OAAOwC,eAAe,CAACxC,EAAE,CAAC;QAC1ByD,UAAU,GAAG,IAAI;MACnB;IACF,CAAC,CAAC;IACF,IAAIA,UAAU,EAAE;MACdnF,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAAC8B,MAAM,CAACC,MAAM,CAACH,eAAe,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAAClE,MAAM,EAAEL,KAAK,CAACqF,2BAA2B,CAAC,CAAC;EAC/C,MAAMI,wBAAwB,GAAGtH,KAAK,CAAC2D,WAAW,CAAC,CAACC,EAAE,EAAE2D,KAAK,KAAK;IAChE,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,OAAO;;IAEjD;IACA;IACA;IACA;;IAEA,MAAMC,2BAA2B,GAAG,CAACxE,iBAAiB,IAAI,CAACqE,UAAU,IAAI,CAACxG,eAAe,CAACuG,KAAK,CAAC;IAChG,MAAM7B,cAAc,GAAG,CAAClC,wBAAwB,IAAImE,2BAA2B;IAC/E,MAAM3D,UAAU,GAAG9B,MAAM,CAACO,OAAO,CAACwB,aAAa,CAACL,EAAE,CAAC;IACnD,IAAI8B,cAAc,EAAE;MAClBxD,MAAM,CAACO,OAAO,CAACgD,SAAS,CAAC7B,EAAE,EAAE,CAAC+D,2BAA2B,GAAG,CAAC3D,UAAU,GAAG,IAAI,EAAE,IAAI,CAAC;IACvF,CAAC,MAAM;MACL9B,MAAM,CAACO,OAAO,CAACgD,SAAS,CAAC7B,EAAE,EAAE,CAACI,UAAU,EAAE,KAAK,CAAC;IAClD;EACF,CAAC,EAAE,CAAC9B,MAAM,EAAEsB,wBAAwB,EAAEL,iBAAiB,CAAC,CAAC;EACzD,MAAMyE,cAAc,GAAG5H,KAAK,CAAC2D,WAAW,CAAC,CAACkE,MAAM,EAAEN,KAAK,KAAK;IAC1D,IAAIO,QAAQ;IACZ,IAAIzE,0BAA0B,EAAE;MAC9B;IACF;IACA,MAAM0E,KAAK,GAAG,CAACD,QAAQ,GAAGP,KAAK,CAACS,MAAM,CAACC,OAAO,CAAC,IAAI7G,WAAW,CAAC8G,IAAI,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACK,YAAY,CAAC,YAAY,CAAC;IAC9H,IAAIJ,KAAK,KAAKlH,+BAA+B,CAACkH,KAAK,EAAE;MACnD;MACA;IACF;IACA,IAAIA,KAAK,KAAK5G,8BAA8B,EAAE;MAC5C;MACA;IACF;IACA,IAAI4G,KAAK,EAAE;MACT,MAAMK,MAAM,GAAGlG,MAAM,CAACO,OAAO,CAAC4F,SAAS,CAACN,KAAK,CAAC;MAC9C,IAAI,CAACK,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC7C,IAAI,MAAMzE,wBAAwB,EAAE;QACxE;MACF;IACF;IACA,MAAMuE,OAAO,GAAGnD,MAAM,CAACO,OAAO,CAAC6C,UAAU,CAACuC,MAAM,CAACjE,EAAE,CAAC;IACpD,IAAIyB,OAAO,CAACE,IAAI,KAAK,WAAW,EAAE;MAChC;IACF;IACA,IAAIgC,KAAK,CAACe,QAAQ,KAAK9E,wBAAwB,IAAIL,iBAAiB,CAAC,EAAE;MACrEO,4BAA4B,CAACmE,MAAM,CAACjE,EAAE,CAAC;IACzC,CAAC,MAAM;MACL0D,wBAAwB,CAACO,MAAM,CAACjE,EAAE,EAAE2D,KAAK,CAAC;IAC5C;EACF,CAAC,EAAE,CAAClE,0BAA0B,EAAEG,wBAAwB,EAAEL,iBAAiB,EAAEjB,MAAM,EAAEwB,4BAA4B,EAAE4D,wBAAwB,CAAC,CAAC;EAC7I,MAAMiB,uBAAuB,GAAGvI,KAAK,CAAC2D,WAAW,CAAC,CAACkE,MAAM,EAAEN,KAAK,KAAK;IACnE,IAAI/D,wBAAwB,IAAI+D,KAAK,CAACe,QAAQ,EAAE;MAC9C,IAAIE,oBAAoB;MACxB,CAACA,oBAAoB,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,KAAK,IAAI,IAAIF,oBAAoB,CAACG,eAAe,CAAC,CAAC;IAClG;EACF,CAAC,EAAE,CAACnF,wBAAwB,CAAC,CAAC;EAC9B,MAAMoF,gCAAgC,GAAG5I,KAAK,CAAC2D,WAAW,CAAC,CAACkE,MAAM,EAAEN,KAAK,KAAK;IAC5E,IAAIA,KAAK,CAACsB,WAAW,CAACP,QAAQ,EAAE;MAC9B5E,4BAA4B,CAACmE,MAAM,CAACjE,EAAE,CAAC;IACzC,CAAC,MAAM;MACL1B,MAAM,CAACO,OAAO,CAACgD,SAAS,CAACoC,MAAM,CAACjE,EAAE,EAAEiE,MAAM,CAACiB,KAAK,CAAC;IACnD;EACF,CAAC,EAAE,CAAC5G,MAAM,EAAEwB,4BAA4B,CAAC,CAAC;EAC1C,MAAMqF,mCAAmC,GAAG/I,KAAK,CAAC2D,WAAW,CAACkE,MAAM,IAAI;IACtE,MAAMmB,iCAAiC,GAAGnH,KAAK,CAACoH,4BAA4B,IAAIpH,KAAK,CAACqH,UAAU;IAChG,MAAMC,gBAAgB,GAAGH,iCAAiC,GAAGvI,4CAA4C,CAACyB,MAAM,CAAC,GAAGvB,gCAAgC,CAACuB,MAAM,CAAC;IAC5J,MAAMkH,WAAW,GAAGxI,uBAAuB,CAACsB,MAAM,CAAC;IACnDA,MAAM,CAACO,OAAO,CAACwD,UAAU,CAACkD,gBAAgB,EAAEtB,MAAM,CAACiB,KAAK,EAAE,CAACM,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,KAAK,CAACzE,MAAM,IAAI,CAAC,CAAC;EAC1H,CAAC,EAAE,CAAC1C,MAAM,EAAEL,KAAK,CAACoH,4BAA4B,EAAEpH,KAAK,CAACqH,UAAU,CAAC,CAAC;EAClE,MAAMI,iBAAiB,GAAGtJ,KAAK,CAAC2D,WAAW,CAAC,CAACkE,MAAM,EAAEN,KAAK,KAAK;IAC7D;IACA,IAAIrF,MAAM,CAACO,OAAO,CAAC8G,WAAW,CAAC1B,MAAM,CAACjE,EAAE,EAAEiE,MAAM,CAACE,KAAK,CAAC,KAAKhH,aAAa,CAACyI,IAAI,EAAE;MAC9E;IACF;;IAEA;IACA;IACA,IAAInI,qBAAqB,CAACkG,KAAK,CAAC,EAAE;MAChC;IACF;IACA,IAAItG,eAAe,CAACsG,KAAK,CAACkC,GAAG,CAAC,IAAIlC,KAAK,CAACe,QAAQ,EAAE;MAChD;MACA,MAAMoB,SAAS,GAAGhJ,qBAAqB,CAACwB,MAAM,CAAC;MAC/C,IAAIwH,SAAS,IAAIA,SAAS,CAAC9F,EAAE,KAAKiE,MAAM,CAACjE,EAAE,EAAE;QAC3C2D,KAAK,CAACoC,cAAc,CAAC,CAAC;QACtB,MAAMC,iBAAiB,GAAG1H,MAAM,CAACO,OAAO,CAACwB,aAAa,CAACyF,SAAS,CAAC9F,EAAE,CAAC;QACpE,IAAI,CAACJ,wBAAwB,EAAE;UAC7BtB,MAAM,CAACO,OAAO,CAACgD,SAAS,CAACiE,SAAS,CAAC9F,EAAE,EAAE,CAACgG,iBAAiB,EAAE,IAAI,CAAC;UAChE;QACF;QACA,MAAMC,WAAW,GAAG3H,MAAM,CAACO,OAAO,CAACqH,gCAAgC,CAACJ,SAAS,CAAC9F,EAAE,CAAC;QACjF,MAAMmG,gBAAgB,GAAG7H,MAAM,CAACO,OAAO,CAACqH,gCAAgC,CAACjC,MAAM,CAACjE,EAAE,CAAC;QACnF,IAAI+C,KAAK;QACT,IAAIC,GAAG;QACP,IAAIiD,WAAW,GAAGE,gBAAgB,EAAE;UAClC,IAAIH,iBAAiB,EAAE;YACrB;YACAjD,KAAK,GAAGoD,gBAAgB;YACxBnD,GAAG,GAAGiD,WAAW,GAAG,CAAC;UACvB,CAAC,MAAM;YACL;YACAlD,KAAK,GAAGoD,gBAAgB;YACxBnD,GAAG,GAAGiD,WAAW;UACnB;QACF,CAAC,MAAM;UACL;UACA,IAAID,iBAAiB,EAAE;YACrB;YACAjD,KAAK,GAAGkD,WAAW,GAAG,CAAC;YACvBjD,GAAG,GAAGmD,gBAAgB;UACxB,CAAC,MAAM;YACL;YACApD,KAAK,GAAGkD,WAAW;YACnBjD,GAAG,GAAGmD,gBAAgB;UACxB;QACF;QACA,MAAMlD,sBAAsB,GAAGpD,WAAW,CAACuG,IAAI,CAAClD,KAAK,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC,CAACqD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACtG,EAAE,CAAC;QACxF1B,MAAM,CAACO,OAAO,CAACwD,UAAU,CAACY,sBAAsB,EAAE,CAAC+C,iBAAiB,CAAC;QACrE;MACF;IACF;IACA,IAAIrC,KAAK,CAACkC,GAAG,KAAK,GAAG,IAAIlC,KAAK,CAACe,QAAQ,EAAE;MACvCf,KAAK,CAACoC,cAAc,CAAC,CAAC;MACtBrC,wBAAwB,CAACO,MAAM,CAACjE,EAAE,EAAE2D,KAAK,CAAC;MAC1C;IACF;IACA,IAAIA,KAAK,CAACkC,GAAG,KAAK,GAAG,KAAKlC,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACE,OAAO,CAAC,EAAE;MACzDF,KAAK,CAACoC,cAAc,CAAC,CAAC;MACtB1D,UAAU,CAAC/D,MAAM,CAACO,OAAO,CAAC0H,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;IACjD;EACF,CAAC,EAAE,CAACjI,MAAM,EAAEoF,wBAAwB,EAAErB,UAAU,EAAExC,WAAW,CAACuG,IAAI,EAAExG,wBAAwB,CAAC,CAAC;EAC9FtD,sBAAsB,CAACgC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAAC6E,uBAAuB,CAAC,CAAC;EACpG/G,sBAAsB,CAACgC,MAAM,EAAE,UAAU,EAAEE,0BAA0B,CAACwF,cAAc,CAAC,CAAC;EACtF1H,sBAAsB,CAACgC,MAAM,EAAE,4BAA4B,EAAEE,0BAA0B,CAACwG,gCAAgC,CAAC,CAAC;EAC1H1I,sBAAsB,CAACgC,MAAM,EAAE,+BAA+B,EAAE6G,mCAAmC,CAAC;EACpG7I,sBAAsB,CAACgC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAACmG,uBAAuB,CAAC,CAAC;EACpGrI,sBAAsB,CAACgC,MAAM,EAAE,aAAa,EAAEE,0BAA0B,CAACkH,iBAAiB,CAAC,CAAC;;EAE5F;AACF;AACA;EACEtJ,KAAK,CAACoK,SAAS,CAAC,MAAM;IACpB,IAAI7H,qBAAqB,KAAK8H,SAAS,EAAE;MACvCnI,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAACjC,qBAAqB,CAAC;IAC5D;EACF,CAAC,EAAE,CAACL,MAAM,EAAEK,qBAAqB,EAAEV,KAAK,CAACE,YAAY,CAAC,CAAC;EACvD/B,KAAK,CAACoK,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvI,KAAK,CAACE,YAAY,EAAE;MACvBG,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAAC,EAAE,CAAC;IACzC;EACF,CAAC,EAAE,CAACtC,MAAM,EAAEL,KAAK,CAACE,YAAY,CAAC,CAAC;EAChC,MAAMuI,iBAAiB,GAAG/H,qBAAqB,IAAI,IAAI;EACvDvC,KAAK,CAACoK,SAAS,CAAC,MAAM;IACpB,IAAIE,iBAAiB,IAAI,CAACzI,KAAK,CAACE,YAAY,EAAE;MAC5C;IACF;;IAEA;IACA,MAAMoF,gBAAgB,GAAG7G,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC;IAC5E,IAAI0B,eAAe,EAAE;MACnB,MAAMsC,YAAY,GAAGuB,gBAAgB,CAACtB,MAAM,CAACjC,EAAE,IAAIN,eAAe,CAACM,EAAE,CAAC,CAAC;MACvE,IAAIgC,YAAY,CAAChB,MAAM,GAAGuC,gBAAgB,CAACvC,MAAM,EAAE;QACjD1C,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAACoB,YAAY,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC1D,MAAM,EAAEoB,eAAe,EAAEgH,iBAAiB,EAAEzI,KAAK,CAACE,YAAY,CAAC,CAAC;EACpE/B,KAAK,CAACoK,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvI,KAAK,CAACE,YAAY,IAAIuI,iBAAiB,EAAE;MAC5C;IACF;IACA,MAAMnD,gBAAgB,GAAG7G,6BAA6B,CAAC4B,MAAM,CAACO,OAAO,CAACb,KAAK,CAAC;IAC5E,IAAI,CAAC4B,wBAAwB,IAAI2D,gBAAgB,CAACvC,MAAM,GAAG,CAAC,EAAE;MAC5D;MACA1C,MAAM,CAACO,OAAO,CAAC+B,oBAAoB,CAAC,EAAE,CAAC;IACzC;EACF,CAAC,EAAE,CAACtC,MAAM,EAAEsB,wBAAwB,EAAEL,iBAAiB,EAAEmH,iBAAiB,EAAEzI,KAAK,CAACE,YAAY,CAAC,CAAC;AAClG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}