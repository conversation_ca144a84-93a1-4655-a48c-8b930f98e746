{"ast": null, "code": "// This file contains copies of the core locales for `MuiTablePagination` released\n// after the `@mui/material` package `v5.4.1` (peer dependency of `@mui/x-data-grid`).\n// This allows not to bump the minimal version of `@mui/material` in peerDependencies which results\n// in broader compatibility between the packages.\n// See https://github.com/mui/mui-x/pull/7646#issuecomment-1404605556 for additional context.\n\nexport const beBYCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перайсці на першую старонку';\n          }\n          if (type === 'last') {\n            return 'Перайсці на апошнюю старонку';\n          }\n          if (type === 'next') {\n            return 'Перайсці на наступную старонку';\n          }\n          // if (type === 'previous') {\n          return 'Перайсці на папярэднюю старонку';\n        },\n        labelRowsPerPage: 'Радкоў на старонцы:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} з ${count !== -1 ? count : `больш чым ${to}`}`\n      }\n    }\n  }\n};\nexport const urPKCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'پہلے صفحے پر جائیں';\n          }\n          if (type === 'last') {\n            return 'آخری صفحے پر جائیں';\n          }\n          if (type === 'next') {\n            return 'اگلے صفحے پر جائیں';\n          }\n          // if (type === 'previous') {\n          return 'پچھلے صفحے پر جائیں';\n        },\n        labelRowsPerPage: 'ایک صفحے پر قطاریں:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${count !== -1 ? `${count} میں سے` : `${to} سے ذیادہ میں سے`} ${from} سے ${to} قطاریں`\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["beBYCore", "components", "MuiTablePagination", "defaultProps", "getItemAriaLabel", "type", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "count", "urPKCore"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/locales/coreLocales.js"], "sourcesContent": ["// This file contains copies of the core locales for `MuiTablePagination` released\n// after the `@mui/material` package `v5.4.1` (peer dependency of `@mui/x-data-grid`).\n// This allows not to bump the minimal version of `@mui/material` in peerDependencies which results\n// in broader compatibility between the packages.\n// See https://github.com/mui/mui-x/pull/7646#issuecomment-1404605556 for additional context.\n\nexport const beBYCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перайсці на першую старонку';\n          }\n          if (type === 'last') {\n            return 'Перайсці на апошнюю старонку';\n          }\n          if (type === 'next') {\n            return 'Перайсці на наступную старонку';\n          }\n          // if (type === 'previous') {\n          return 'Перайсці на папярэднюю старонку';\n        },\n        labelRowsPerPage: 'Радкоў на старонцы:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} з ${count !== -1 ? count : `больш чым ${to}`}`\n      }\n    }\n  }\n};\nexport const urPKCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'پہلے صفحے پر جائیں';\n          }\n          if (type === 'last') {\n            return 'آخری صفحے پر جائیں';\n          }\n          if (type === 'next') {\n            return 'اگلے صفحے پر جائیں';\n          }\n          // if (type === 'previous') {\n          return 'پچھلے صفحے پر جائیں';\n        },\n        labelRowsPerPage: 'ایک صفحے پر قطاریں:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${count !== -1 ? `${count} میں سے` : `${to} سے ذیادہ میں سے`} ${from} سے ${to} قطاریں`\n      }\n    }\n  }\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,QAAQ,GAAG;EACtBC,UAAU,EAAE;IACVC,kBAAkB,EAAE;MAClBC,YAAY,EAAE;QACZC,gBAAgB,EAAEC,IAAI,IAAI;UACxB,IAAIA,IAAI,KAAK,OAAO,EAAE;YACpB,OAAO,6BAA6B;UACtC;UACA,IAAIA,IAAI,KAAK,MAAM,EAAE;YACnB,OAAO,8BAA8B;UACvC;UACA,IAAIA,IAAI,KAAK,MAAM,EAAE;YACnB,OAAO,gCAAgC;UACzC;UACA;UACA,OAAO,iCAAiC;QAC1C,CAAC;QACDC,gBAAgB,EAAE,qBAAqB;QACvCC,kBAAkB,EAAEA,CAAC;UACnBC,IAAI;UACJC,EAAE;UACFC;QACF,CAAC,KAAK,GAAGF,IAAI,IAAIC,EAAE,MAAMC,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAG,aAAaD,EAAE,EAAE;MACnE;IACF;EACF;AACF,CAAC;AACD,OAAO,MAAME,QAAQ,GAAG;EACtBV,UAAU,EAAE;IACVC,kBAAkB,EAAE;MAClBC,YAAY,EAAE;QACZC,gBAAgB,EAAEC,IAAI,IAAI;UACxB,IAAIA,IAAI,KAAK,OAAO,EAAE;YACpB,OAAO,oBAAoB;UAC7B;UACA,IAAIA,IAAI,KAAK,MAAM,EAAE;YACnB,OAAO,qBAAqB;UAC9B;UACA,IAAIA,IAAI,KAAK,MAAM,EAAE;YACnB,OAAO,oBAAoB;UAC7B;UACA;UACA,OAAO,qBAAqB;QAC9B,CAAC;QACDC,gBAAgB,EAAE,qBAAqB;QACvCC,kBAAkB,EAAEA,CAAC;UACnBC,IAAI;UACJC,EAAE;UACFC;QACF,CAAC,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAC,GAAG,GAAGA,KAAK,SAAS,GAAG,GAAGD,EAAE,kBAAkB,IAAID,IAAI,OAAOC,EAAE;MACtF;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}