{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Container, Box, Typography, Stepper, Step, StepLabel, Paper, CssBaseline, ThemeProvider, createTheme, Tooltip, IconButton } from '@mui/material';\nimport DeleteSweepIcon from '@mui/icons-material/DeleteSweep';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport LightModeIcon from '@mui/icons-material/LightMode';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport FileUpload from './components/FileUpload';\nimport WorksheetSelect from './components/WorksheetSelect';\nimport ProcessForm from './components/ProcessForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport ErrorSnackbar from './components/ErrorSnackbar';\n\n// 创建主题函数\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst createAppTheme = mode => createTheme({\n  palette: {\n    mode,\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0'\n    },\n    secondary: {\n      main: '#9c27b0',\n      light: '#ba68c8',\n      dark: '#7b1fa2'\n    },\n    background: {\n      default: mode === 'light' ? '#f5f5f5' : '#121212',\n      paper: mode === 'light' ? '#ffffff' : '#1e1e1e'\n    },\n    text: {\n      primary: mode === 'light' ? '#212121' : '#ffffff',\n      secondary: mode === 'light' ? '#757575' : '#b3b3b3'\n    }\n  },\n  typography: {\n    fontFamily: ['-apple-system', 'BlinkMacSystemFont', '\"Segoe UI\"', 'Roboto', '\"Helvetica Neue\"', 'Arial', 'sans-serif'].join(','),\n    h4: {\n      fontWeight: 600,\n      fontSize: '2rem'\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1.25rem'\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n          borderRadius: '8px',\n          padding: '8px 16px'\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: mode === 'light' ? '0 2px 8px rgba(0,0,0,0.1)' : '0 2px 8px rgba(0,0,0,0.3)'\n        }\n      }\n    }\n  }\n});\nconst steps = ['上传Excel文件', '选择工作表', '设置参数', '查看结果'];\nfunction App() {\n  _s();\n  // 主题模式状态\n  const [themeMode, setThemeMode] = useState(() => {\n    const savedMode = localStorage.getItem('themeMode');\n    return savedMode || 'light';\n  });\n\n  // 创建主题\n  const theme = createAppTheme(themeMode);\n  const [activeStep, setActiveStep] = useState(0);\n  const [fileData, setFileData] = useState(null);\n  const [selectedWorksheet, setSelectedWorksheet] = useState('');\n  const [processParams, setProcessParams] = useState({\n    startCol: '',\n    endCol: '',\n    perHourRate: '',\n    cbuCarHourRate: '',\n    commissionRate: ''\n  });\n  const [resultData, setResultData] = useState(null);\n  const [error, setError] = useState('');\n\n  // 保存ResultDisplay组件中的表格数据\n  const [gridData, setGridData] = useState([]);\n\n  // 使用ref来避免无限循环\n  const lastGridDataRef = useRef(null);\n  const lastUpdateTimeRef = useRef(0);\n\n  // 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\n  useEffect(() => {\n    // 当gridData变化且不为空时，延迟保存到localStorage\n    if (gridData && gridData.length > 0) {\n      // 使用防抖技术，延迟1秒后保存\n      const saveTimeout = setTimeout(() => {\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(gridData));\n          // console.log('保存gridData到localStorage:', gridData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n      }, 1000);\n\n      // 清除上一个定时器\n      return () => clearTimeout(saveTimeout);\n    }\n  }, [gridData]);\n\n  // 在组件加载时，尝试从localStorage恢复数据\n  useEffect(() => {\n    // 只在初始化时执行一次\n    const savedGridDataString = localStorage.getItem('savedGridData');\n    const savedStateString = localStorage.getItem('commissionState');\n    console.log('检查localStorage恢复:', {\n      hasSavedGridData: !!savedGridDataString,\n      hasSavedState: !!savedStateString\n    });\n\n    // 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\n    if (savedGridDataString) {\n      try {\n        const savedData = JSON.parse(savedGridDataString);\n        if (savedData && savedData.length > 0) {\n          setGridData(savedData);\n          console.log('从localStorage恢复gridData:', savedData.length);\n\n          // 如果有savedGridData，说明应该在ResultDisplay页面\n          // 需要恢复所有必要的状态以支持ResultDisplay\n          if (savedStateString) {\n            try {\n              const savedState = JSON.parse(savedStateString);\n              console.log('从localStorage恢复commissionState以支持ResultDisplay');\n\n              // 恢复到ResultDisplay页面需要的所有状态\n              setActiveStep(3); // 直接跳转到ResultDisplay\n\n              if (savedState.fileData && savedState.fileData.file_id) {\n                setFileData(savedState.fileData);\n              }\n              setSelectedWorksheet(savedState.selectedWorksheet || '');\n              setProcessParams(savedState.processParams || {\n                startCol: '',\n                endCol: '',\n                perHourRate: '',\n                cbuCarHourRate: '',\n                commissionRate: ''\n              });\n              setResultData(savedState.resultData || null);\n            } catch (error) {\n              console.error('解析commissionState失败:', error);\n              // 即使commissionState解析失败，也要跳转到ResultDisplay\n              setActiveStep(3);\n            }\n          } else {\n            // 没有commissionState但有gridData，创建最小状态支持ResultDisplay\n            console.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');\n            setActiveStep(3);\n\n            // 创建最小的状态以支持ResultDisplay正常工作\n            // 使用时间戳作为fileId，确保唯一性\n            const recoveredFileId = `recovered_${Date.now()}`;\n            setFileData({\n              file_id: recoveredFileId,\n              worksheets: ['recovered']\n            });\n            setSelectedWorksheet('recovered');\n            setResultData([]); // 空的结果数据，因为实际数据在gridData中\n          }\n        }\n      } catch (error) {\n        console.error('解析savedGridData失败:', error);\n      }\n    } else if (savedStateString) {\n      // 只有commissionState，没有gridData的情况\n      try {\n        const savedState = JSON.parse(savedStateString);\n        console.log('只恢复commissionState，没有gridData');\n        setActiveStep(savedState.activeStep || 0);\n        if (savedState.fileData && savedState.fileData.file_id) {\n          setFileData(savedState.fileData);\n        }\n        setSelectedWorksheet(savedState.selectedWorksheet || '');\n        setProcessParams(savedState.processParams || {\n          startCol: '',\n          endCol: '',\n          perHourRate: '',\n          cbuCarHourRate: '',\n          commissionRate: ''\n        });\n        setResultData(savedState.resultData || null);\n      } catch (error) {\n        console.error('解析commissionState失败:', error);\n      }\n    }\n  }, []);\n  const resetPagePosition = () => {\n    window.scrollTo(0, 0);\n  };\n  const handleFileUpload = data => {\n    setFileData(data);\n    setActiveStep(1);\n  };\n  const handleWorksheetSelect = worksheet => {\n    setSelectedWorksheet(worksheet);\n    resetPagePosition();\n    setActiveStep(2);\n  };\n  const handleProcessSubmit = (params, result) => {\n    setProcessParams(params);\n    setResultData(result);\n    resetPagePosition();\n    setActiveStep(3);\n    // 当获取新的处理结果时，清除之前的gridData\n    setGridData([]);\n    localStorage.removeItem('savedGridData');\n\n    // 保存当前状态到localStorage，以便页面刷新后能恢复\n    const stateToSave = {\n      activeStep: 3,\n      fileData,\n      selectedWorksheet,\n      processParams: params,\n      resultData: result\n    };\n    try {\n      localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n      console.log('保存commissionState到localStorage');\n    } catch (error) {\n      console.error('保存commissionState失败:', error);\n    }\n  };\n  const handleReset = () => {\n    setActiveStep(0);\n    setFileData(null);\n    setSelectedWorksheet('');\n    setProcessParams({\n      startCol: '',\n      endCol: '',\n      perHourRate: '',\n      cbuCarHourRate: '',\n      commissionRate: ''\n    });\n    setResultData(null);\n    setGridData([]);\n\n    // 清除localStorage中的状态\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    console.log('重置所有状态并清除localStorage');\n  };\n\n  // 处理ResultDisplay组件中数据变化的回调\n  const handleGridDataChange = newData => {\n    // 避免不必要的更新，只有当数据真正变化时才更新状态\n    if (newData && newData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次更新时间不足500ms，则跳过此次更新\n      if (now - lastUpdateTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次的数据是否相同\n      const currentDataString = JSON.stringify(newData);\n      const lastDataString = lastGridDataRef.current;\n      if (lastDataString !== currentDataString) {\n        // console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\n        lastGridDataRef.current = currentDataString;\n        lastUpdateTimeRef.current = now;\n        setGridData(newData);\n\n        // 当gridData更新时，保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(newData));\n          console.log('App.js中保存gridData到localStorage:', newData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n\n        // 同时更新commissionState以保持同步\n        const stateToSave = {\n          activeStep: 3,\n          fileData,\n          selectedWorksheet,\n          processParams,\n          resultData\n        };\n        try {\n          localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n          // console.log('更新commissionState到localStorage');\n        } catch (error) {\n          console.error('更新commissionState失败:', error);\n        }\n      } else {\n        // console.log('接收到的gridData与上次相同，跳过更新');\n      }\n    }\n  };\n\n  // 切换主题模式\n  const toggleThemeMode = () => {\n    const newMode = themeMode === 'light' ? 'dark' : 'light';\n    setThemeMode(newMode);\n    localStorage.setItem('themeMode', newMode);\n    console.log('切换主题模式到:', newMode);\n  };\n\n  // 清除所有保存的状态\n  const clearAllSavedState = () => {\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    setError('所有保存的状态已清除，请刷新页面');\n    console.log('手动清除所有保存的状态');\n  };\n  const handleError = errorMessage => {\n    setError(errorMessage);\n  };\n  const handleCloseError = () => {\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '100vh',\n          backgroundColor: 'background.default',\n          py: {\n            xs: 2,\n            md: 4\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: false,\n          sx: {\n            maxWidth: '95vw',\n            // 使用95%的视口宽度\n            px: {\n              xs: 1,\n              sm: 2,\n              md: 3\n            } // 响应式内边距\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalculateIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                component: \"h1\",\n                sx: {\n                  color: 'text.primary',\n                  fontWeight: 600\n                },\n                children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u5DE5\\u5177\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: `切换到${themeMode === 'light' ? '深色' : '浅色'}模式`,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: toggleThemeMode,\n                  color: \"primary\",\n                  children: themeMode === 'light' ? /*#__PURE__*/_jsxDEV(DarkModeIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 46\n                  }, this) : /*#__PURE__*/_jsxDEV(LightModeIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 65\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u6E05\\u9664\\u6240\\u6709\\u4FDD\\u5B58\\u7684\\u72B6\\u6001\\uFF08\\u5982\\u679C\\u9047\\u5230\\u95EE\\u9898\\u8BF7\\u70B9\\u51FB\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: clearAllSavedState,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteSweepIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: {\n                xs: 2,\n                md: 4\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(Stepper, {\n                activeStep: activeStep,\n                children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n                  children: /*#__PURE__*/_jsxDEV(StepLabel, {\n                    children: label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this)\n                }, label, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minHeight: '400px'\n              },\n              children: [activeStep === 0 && /*#__PURE__*/_jsxDEV(FileUpload, {\n                onFileUpload: handleFileUpload,\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), activeStep === 1 && fileData && /*#__PURE__*/_jsxDEV(WorksheetSelect, {\n                worksheets: fileData.worksheets,\n                onSelect: handleWorksheetSelect,\n                onBack: () => setActiveStep(0),\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), activeStep === 2 && fileData && selectedWorksheet && /*#__PURE__*/_jsxDEV(ProcessForm, {\n                fileId: fileData.file_id,\n                worksheet: selectedWorksheet,\n                onSubmit: handleProcessSubmit,\n                onBack: () => {\n                  resetPagePosition();\n                  setActiveStep(1);\n                },\n                onError: handleError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this), activeStep === 3 && (resultData || gridData.length > 0) && /*#__PURE__*/_jsxDEV(ResultDisplay, {\n                data: resultData || [],\n                fileId: fileData === null || fileData === void 0 ? void 0 : fileData.file_id,\n                onReset: handleReset,\n                onError: handleError,\n                savedGridData: gridData,\n                onDataChange: handleGridDataChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ErrorSnackbar, {\n        open: !!error,\n        message: error,\n        onClose: handleCloseError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(App, \"kHHGba+jCo7kj6RdbPtKiFGJWko=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Container", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "CssBaseline", "ThemeProvider", "createTheme", "<PERSON><PERSON><PERSON>", "IconButton", "DeleteSweepIcon", "CalculateIcon", "LightModeIcon", "DarkModeIcon", "FileUpload", "WorksheetSelect", "ProcessForm", "ResultDisplay", "ErrorSnackbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "createAppTheme", "mode", "palette", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "join", "h4", "fontWeight", "fontSize", "h6", "shape", "borderRadius", "components", "MuiPaper", "styleOverrides", "root", "backgroundImage", "MuiB<PERSON>on", "textTransform", "padding", "MuiCard", "boxShadow", "steps", "App", "_s", "themeMode", "setThemeMode", "savedMode", "localStorage", "getItem", "theme", "activeStep", "setActiveStep", "fileData", "setFileData", "selectedWorksheet", "setSelectedWorksheet", "processParams", "setProcessParams", "startCol", "endCol", "perHourRate", "cbuCarHourRate", "commissionRate", "resultData", "setResultData", "error", "setError", "gridData", "setGridData", "lastGridDataRef", "lastUpdateTimeRef", "length", "saveTimeout", "setTimeout", "setItem", "JSON", "stringify", "console", "clearTimeout", "savedGridDataString", "savedStateString", "log", "hasSavedGridData", "hasSavedState", "savedData", "parse", "savedState", "file_id", "recoveredFileId", "Date", "now", "worksheets", "resetPagePosition", "window", "scrollTo", "handleFileUpload", "data", "handleWorksheetSelect", "worksheet", "handleProcessSubmit", "params", "result", "removeItem", "stateToSave", "handleReset", "handleGridDataChange", "newData", "current", "currentDataString", "lastDataString", "toggleThemeMode", "newMode", "clearAllSavedState", "handleError", "errorMessage", "handleCloseError", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "minHeight", "backgroundColor", "py", "xs", "md", "max<PERSON><PERSON><PERSON>", "px", "sm", "display", "justifyContent", "alignItems", "mb", "gap", "color", "variant", "component", "title", "onClick", "elevation", "p", "map", "label", "onFileUpload", "onError", "onSelect", "onBack", "fileId", "onSubmit", "onReset", "savedGridData", "onDataChange", "open", "message", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Paper,\n  CssBaseline,\n  ThemeProvider,\n  createTheme,\n  Tooltip,\n  IconButton\n} from '@mui/material';\nimport DeleteSweepIcon from '@mui/icons-material/DeleteSweep';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport LightModeIcon from '@mui/icons-material/LightMode';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport FileUpload from './components/FileUpload';\nimport WorksheetSelect from './components/WorksheetSelect';\nimport ProcessForm from './components/ProcessForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport ErrorSnackbar from './components/ErrorSnackbar';\n\n\n\n// 创建主题函数\nconst createAppTheme = (mode) => createTheme({\n  palette: {\n    mode,\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n    },\n    secondary: {\n      main: '#9c27b0',\n      light: '#ba68c8',\n      dark: '#7b1fa2',\n    },\n    background: {\n      default: mode === 'light' ? '#f5f5f5' : '#121212',\n      paper: mode === 'light' ? '#ffffff' : '#1e1e1e',\n    },\n    text: {\n      primary: mode === 'light' ? '#212121' : '#ffffff',\n      secondary: mode === 'light' ? '#757575' : '#b3b3b3',\n    },\n  },\n  typography: {\n    fontFamily: [\n      '-apple-system',\n      'BlinkMacSystemFont',\n      '\"Segoe UI\"',\n      'Roboto',\n      '\"Helvetica Neue\"',\n      'Arial',\n      'sans-serif',\n    ].join(','),\n    h4: {\n      fontWeight: 600,\n      fontSize: '2rem',\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1.25rem',\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n          borderRadius: '8px',\n          padding: '8px 16px',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: mode === 'light'\n            ? '0 2px 8px rgba(0,0,0,0.1)'\n            : '0 2px 8px rgba(0,0,0,0.3)',\n        },\n      },\n    },\n  },\n});\n\nconst steps = ['上传Excel文件', '选择工作表', '设置参数', '查看结果'];\n\nfunction App() {\n  // 主题模式状态\n  const [themeMode, setThemeMode] = useState(() => {\n    const savedMode = localStorage.getItem('themeMode');\n    return savedMode || 'light';\n  });\n\n  // 创建主题\n  const theme = createAppTheme(themeMode);\n\n  const [activeStep, setActiveStep] = useState(0);\n  const [fileData, setFileData] = useState(null);\n  const [selectedWorksheet, setSelectedWorksheet] = useState('');\n  const [processParams, setProcessParams] = useState({\n    startCol: '',\n    endCol: '',\n    perHourRate: '',\n    cbuCarHourRate: '',\n    commissionRate: ''\n  });\n  const [resultData, setResultData] = useState(null);\n  const [error, setError] = useState('');\n\n  \n  // 保存ResultDisplay组件中的表格数据\n  const [gridData, setGridData] = useState([]);\n  \n  // 使用ref来避免无限循环\n  const lastGridDataRef = useRef(null);\n  const lastUpdateTimeRef = useRef(0);\n  \n\n\n  // 使用localStorage来保存和恢复编辑状态，使用防抖技术避免频繁保存\n  useEffect(() => {\n    // 当gridData变化且不为空时，延迟保存到localStorage\n    if (gridData && gridData.length > 0) {\n      // 使用防抖技术，延迟1秒后保存\n      const saveTimeout = setTimeout(() => {\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(gridData));\n          // console.log('保存gridData到localStorage:', gridData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n      }, 1000);\n      \n      // 清除上一个定时器\n      return () => clearTimeout(saveTimeout);\n    }\n  }, [gridData]);\n\n  // 在组件加载时，尝试从localStorage恢复数据\n  useEffect(() => {\n    // 只在初始化时执行一次\n    const savedGridDataString = localStorage.getItem('savedGridData');\n    const savedStateString = localStorage.getItem('commissionState');\n\n    console.log('检查localStorage恢复:', {\n      hasSavedGridData: !!savedGridDataString,\n      hasSavedState: !!savedStateString\n    });\n\n    // 如果有savedGridData，说明用户之前在ResultDisplay页面，应该恢复到那里\n    if (savedGridDataString) {\n      try {\n        const savedData = JSON.parse(savedGridDataString);\n        if (savedData && savedData.length > 0) {\n          setGridData(savedData);\n          console.log('从localStorage恢复gridData:', savedData.length);\n\n          // 如果有savedGridData，说明应该在ResultDisplay页面\n          // 需要恢复所有必要的状态以支持ResultDisplay\n          if (savedStateString) {\n            try {\n              const savedState = JSON.parse(savedStateString);\n              console.log('从localStorage恢复commissionState以支持ResultDisplay');\n\n              // 恢复到ResultDisplay页面需要的所有状态\n              setActiveStep(3); // 直接跳转到ResultDisplay\n\n              if (savedState.fileData && savedState.fileData.file_id) {\n                setFileData(savedState.fileData);\n              }\n\n              setSelectedWorksheet(savedState.selectedWorksheet || '');\n              setProcessParams(savedState.processParams || {\n                startCol: '',\n                endCol: '',\n                perHourRate: '',\n                cbuCarHourRate: '',\n                commissionRate: ''\n              });\n\n              setResultData(savedState.resultData || null);\n            } catch (error) {\n              console.error('解析commissionState失败:', error);\n              // 即使commissionState解析失败，也要跳转到ResultDisplay\n              setActiveStep(3);\n            }\n          } else {\n            // 没有commissionState但有gridData，创建最小状态支持ResultDisplay\n            console.log('只有gridData，没有commissionState，创建最小状态支持ResultDisplay');\n            setActiveStep(3);\n\n            // 创建最小的状态以支持ResultDisplay正常工作\n            // 使用时间戳作为fileId，确保唯一性\n            const recoveredFileId = `recovered_${Date.now()}`;\n            setFileData({ file_id: recoveredFileId, worksheets: ['recovered'] });\n            setSelectedWorksheet('recovered');\n            setResultData([]); // 空的结果数据，因为实际数据在gridData中\n          }\n        }\n      } catch (error) {\n        console.error('解析savedGridData失败:', error);\n      }\n    } else if (savedStateString) {\n      // 只有commissionState，没有gridData的情况\n      try {\n        const savedState = JSON.parse(savedStateString);\n        console.log('只恢复commissionState，没有gridData');\n\n        setActiveStep(savedState.activeStep || 0);\n\n        if (savedState.fileData && savedState.fileData.file_id) {\n          setFileData(savedState.fileData);\n        }\n\n        setSelectedWorksheet(savedState.selectedWorksheet || '');\n        setProcessParams(savedState.processParams || {\n          startCol: '',\n          endCol: '',\n          perHourRate: '',\n          cbuCarHourRate: '',\n          commissionRate: ''\n        });\n\n        setResultData(savedState.resultData || null);\n      } catch (error) {\n        console.error('解析commissionState失败:', error);\n      }\n    }\n  }, []);\n\n  const resetPagePosition = () => {\n    window.scrollTo(0, 0);\n  };\n\n  const handleFileUpload = (data) => {\n    setFileData(data);\n    setActiveStep(1);\n  };\n\n  const handleWorksheetSelect = (worksheet) => {\n    setSelectedWorksheet(worksheet);\n    resetPagePosition();\n    setActiveStep(2);\n  };\n\n  const handleProcessSubmit = (params, result) => {\n    setProcessParams(params);\n    setResultData(result);\n    resetPagePosition();\n    setActiveStep(3);\n    // 当获取新的处理结果时，清除之前的gridData\n    setGridData([]);\n    localStorage.removeItem('savedGridData');\n\n    // 保存当前状态到localStorage，以便页面刷新后能恢复\n    const stateToSave = {\n      activeStep: 3,\n      fileData,\n      selectedWorksheet,\n      processParams: params,\n      resultData: result\n    };\n\n    try {\n      localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n      console.log('保存commissionState到localStorage');\n    } catch (error) {\n      console.error('保存commissionState失败:', error);\n    }\n  };\n\n  const handleReset = () => {\n    setActiveStep(0);\n    setFileData(null);\n    setSelectedWorksheet('');\n    setProcessParams({\n      startCol: '',\n      endCol: '',\n      perHourRate: '',\n      cbuCarHourRate: '',\n      commissionRate: ''\n    });\n    setResultData(null);\n    setGridData([]);\n    \n    // 清除localStorage中的状态\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    \n    console.log('重置所有状态并清除localStorage');\n  };\n\n\n  \n  // 处理ResultDisplay组件中数据变化的回调\n  const handleGridDataChange = (newData) => {\n    // 避免不必要的更新，只有当数据真正变化时才更新状态\n    if (newData && newData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次更新时间不足500ms，则跳过此次更新\n      if (now - lastUpdateTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次的数据是否相同\n      const currentDataString = JSON.stringify(newData);\n      const lastDataString = lastGridDataRef.current;\n\n      if (lastDataString !== currentDataString) {\n        // console.log('接收到新的gridData，与上次不同，更新状态:', newData.length);\n        lastGridDataRef.current = currentDataString;\n        lastUpdateTimeRef.current = now;\n        setGridData(newData);\n\n        // 当gridData更新时，保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(newData));\n          console.log('App.js中保存gridData到localStorage:', newData.length);\n        } catch (error) {\n          console.error('保存gridData到localStorage失败:', error);\n        }\n\n        // 同时更新commissionState以保持同步\n        const stateToSave = {\n          activeStep: 3,\n          fileData,\n          selectedWorksheet,\n          processParams,\n          resultData\n        };\n\n        try {\n          localStorage.setItem('commissionState', JSON.stringify(stateToSave));\n          // console.log('更新commissionState到localStorage');\n        } catch (error) {\n          console.error('更新commissionState失败:', error);\n        }\n      } else {\n        // console.log('接收到的gridData与上次相同，跳过更新');\n      }\n    }\n  };\n\n\n\n  // 切换主题模式\n  const toggleThemeMode = () => {\n    const newMode = themeMode === 'light' ? 'dark' : 'light';\n    setThemeMode(newMode);\n    localStorage.setItem('themeMode', newMode);\n    console.log('切换主题模式到:', newMode);\n  };\n\n  // 清除所有保存的状态\n  const clearAllSavedState = () => {\n    localStorage.removeItem('savedGridData');\n    localStorage.removeItem('commissionState');\n    setError('所有保存的状态已清除，请刷新页面');\n    console.log('手动清除所有保存的状态');\n  };\n\n  const handleError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleCloseError = () => {\n    setError('');\n  };\n\n  return (\n    <>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Box\n          sx={{\n            minHeight: '100vh',\n            backgroundColor: 'background.default',\n            py: { xs: 2, md: 4 },\n          }}\n        >\n          <Container\n            maxWidth={false}\n            sx={{\n              maxWidth: '95vw', // 使用95%的视口宽度\n              px: { xs: 1, sm: 2, md: 3 } // 响应式内边距\n            }}\n          >\n            {/* 头部区域 */}\n            <Box\n              sx={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 4,\n              }}\n            >\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <CalculateIcon\n                  sx={{\n                    fontSize: 40,\n                    color: 'primary.main',\n                  }}\n                />\n                <Typography\n                  variant=\"h4\"\n                  component=\"h1\"\n                  sx={{\n                    color: 'text.primary',\n                    fontWeight: 600,\n                  }}\n                >\n                  佣金计算工具\n                </Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={`切换到${themeMode === 'light' ? '深色' : '浅色'}模式`}>\n                  <IconButton\n                    onClick={toggleThemeMode}\n                    color=\"primary\"\n                  >\n                    {themeMode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}\n                  </IconButton>\n                </Tooltip>\n\n                <Tooltip title=\"清除所有保存的状态（如果遇到问题请点击）\">\n                  <IconButton\n                    onClick={clearAllSavedState}\n                    color=\"error\"\n                  >\n                    <DeleteSweepIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n            </Box>\n\n            {/* 主要内容区域 */}\n            <Paper\n              elevation={2}\n              sx={{\n                p: { xs: 2, md: 4 },\n              }}\n            >\n              {/* 步骤指示器 */}\n              <Box sx={{ mb: 4 }}>\n                <Stepper activeStep={activeStep}>\n                  {steps.map((label) => (\n                    <Step key={label}>\n                      <StepLabel>{label}</StepLabel>\n                    </Step>\n                  ))}\n                </Stepper>\n              </Box>\n\n              {/* 步骤内容 */}\n              <Box sx={{ minHeight: '400px' }}>\n                {activeStep === 0 && (\n                  <FileUpload onFileUpload={handleFileUpload} onError={handleError} />\n                )}\n\n                {activeStep === 1 && fileData && (\n                  <WorksheetSelect\n                    worksheets={fileData.worksheets}\n                    onSelect={handleWorksheetSelect}\n                    onBack={() => setActiveStep(0)}\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 2 && fileData && selectedWorksheet && (\n                  <ProcessForm\n                    fileId={fileData.file_id}\n                    worksheet={selectedWorksheet}\n                    onSubmit={handleProcessSubmit}\n                    onBack={() => {resetPagePosition(); setActiveStep(1);} }\n                    onError={handleError}\n                  />\n                )}\n\n                {activeStep === 3 && (resultData || gridData.length > 0) && (\n                  <ResultDisplay\n                    data={resultData || []}\n                    fileId={fileData?.file_id}\n                    onReset={handleReset}\n                    onError={handleError}\n                    savedGridData={gridData}\n                    onDataChange={handleGridDataChange}\n                  />\n                )}\n              </Box>\n            </Paper>\n          </Container>\n        </Box>\n\n        <ErrorSnackbar\n          open={!!error}\n          message={error}\n          onClose={handleCloseError}\n        />\n      </ThemeProvider>\n    </>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,WAAW,EACXC,OAAO,EACPC,UAAU,QACL,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,aAAa,MAAM,4BAA4B;;AAItD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,IAAI,IAAKjB,WAAW,CAAC;EAC3CkB,OAAO,EAAE;IACPD,IAAI;IACJE,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAER,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;MACjDS,KAAK,EAAET,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;IACxC,CAAC;IACDU,IAAI,EAAE;MACJR,OAAO,EAAEF,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;MACjDM,SAAS,EAAEN,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;IAC5C;EACF,CAAC;EACDW,UAAU,EAAE;IACVC,UAAU,EAAE,CACV,eAAe,EACf,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,OAAO,EACP,YAAY,CACb,CAACC,IAAI,CAAC,GAAG,CAAC;IACXC,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,EAAE,EAAE;MACFF,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDE,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,QAAQ,EAAE;MACRC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,eAAe,EAAE;QACnB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,aAAa,EAAE,MAAM;UACrBX,UAAU,EAAE,GAAG;UACfI,YAAY,EAAE,KAAK;UACnBQ,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDC,OAAO,EAAE;MACPN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJM,SAAS,EAAE7B,IAAI,KAAK,OAAO,GACvB,2BAA2B,GAC3B;QACN;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAM8B,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AAEpD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,MAAM;IAC/C,MAAMgE,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,OAAOF,SAAS,IAAI,OAAO;EAC7B,CAAC,CAAC;;EAEF;EACA,MAAMG,KAAK,GAAGvC,cAAc,CAACkC,SAAS,CAAC;EAEvC,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC;IACjD4E,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmF,KAAK,EAAEC,QAAQ,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;;EAGtC;EACA,MAAM,CAACqF,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMuF,eAAe,GAAGrF,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMsF,iBAAiB,GAAGtF,MAAM,CAAC,CAAC,CAAC;;EAInC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,IAAIoF,QAAQ,IAAIA,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACnC;MACA,MAAMC,WAAW,GAAGC,UAAU,CAAC,MAAM;QACnC,IAAI;UACF1B,YAAY,CAAC2B,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdY,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,OAAO,MAAMa,YAAY,CAACN,WAAW,CAAC;IACxC;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;;EAEd;EACApF,SAAS,CAAC,MAAM;IACd;IACA,MAAMgG,mBAAmB,GAAGhC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACjE,MAAMgC,gBAAgB,GAAGjC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAEhE6B,OAAO,CAACI,GAAG,CAAC,mBAAmB,EAAE;MAC/BC,gBAAgB,EAAE,CAAC,CAACH,mBAAmB;MACvCI,aAAa,EAAE,CAAC,CAACH;IACnB,CAAC,CAAC;;IAEF;IACA,IAAID,mBAAmB,EAAE;MACvB,IAAI;QACF,MAAMK,SAAS,GAAGT,IAAI,CAACU,KAAK,CAACN,mBAAmB,CAAC;QACjD,IAAIK,SAAS,IAAIA,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;UACrCH,WAAW,CAACgB,SAAS,CAAC;UACtBP,OAAO,CAACI,GAAG,CAAC,0BAA0B,EAAEG,SAAS,CAACb,MAAM,CAAC;;UAEzD;UACA;UACA,IAAIS,gBAAgB,EAAE;YACpB,IAAI;cACF,MAAMM,UAAU,GAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC;cAC/CH,OAAO,CAACI,GAAG,CAAC,gDAAgD,CAAC;;cAE7D;cACA9B,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;;cAElB,IAAImC,UAAU,CAAClC,QAAQ,IAAIkC,UAAU,CAAClC,QAAQ,CAACmC,OAAO,EAAE;gBACtDlC,WAAW,CAACiC,UAAU,CAAClC,QAAQ,CAAC;cAClC;cAEAG,oBAAoB,CAAC+B,UAAU,CAAChC,iBAAiB,IAAI,EAAE,CAAC;cACxDG,gBAAgB,CAAC6B,UAAU,CAAC9B,aAAa,IAAI;gBAC3CE,QAAQ,EAAE,EAAE;gBACZC,MAAM,EAAE,EAAE;gBACVC,WAAW,EAAE,EAAE;gBACfC,cAAc,EAAE,EAAE;gBAClBC,cAAc,EAAE;cAClB,CAAC,CAAC;cAEFE,aAAa,CAACsB,UAAU,CAACvB,UAAU,IAAI,IAAI,CAAC;YAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;cACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;cAC5C;cACAd,aAAa,CAAC,CAAC,CAAC;YAClB;UACF,CAAC,MAAM;YACL;YACA0B,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC;YACjE9B,aAAa,CAAC,CAAC,CAAC;;YAEhB;YACA;YACA,MAAMqC,eAAe,GAAG,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YACjDrC,WAAW,CAAC;cAAEkC,OAAO,EAAEC,eAAe;cAAEG,UAAU,EAAE,CAAC,WAAW;YAAE,CAAC,CAAC;YACpEpC,oBAAoB,CAAC,WAAW,CAAC;YACjCS,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;UACrB;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdY,OAAO,CAACZ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,MAAM,IAAIe,gBAAgB,EAAE;MAC3B;MACA,IAAI;QACF,MAAMM,UAAU,GAAGX,IAAI,CAACU,KAAK,CAACL,gBAAgB,CAAC;QAC/CH,OAAO,CAACI,GAAG,CAAC,+BAA+B,CAAC;QAE5C9B,aAAa,CAACmC,UAAU,CAACpC,UAAU,IAAI,CAAC,CAAC;QAEzC,IAAIoC,UAAU,CAAClC,QAAQ,IAAIkC,UAAU,CAAClC,QAAQ,CAACmC,OAAO,EAAE;UACtDlC,WAAW,CAACiC,UAAU,CAAClC,QAAQ,CAAC;QAClC;QAEAG,oBAAoB,CAAC+B,UAAU,CAAChC,iBAAiB,IAAI,EAAE,CAAC;QACxDG,gBAAgB,CAAC6B,UAAU,CAAC9B,aAAa,IAAI;UAC3CE,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAE,EAAE;UACVC,WAAW,EAAE,EAAE;UACfC,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE;QAClB,CAAC,CAAC;QAEFE,aAAa,CAACsB,UAAU,CAACvB,UAAU,IAAI,IAAI,CAAC;MAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC3C,WAAW,CAAC2C,IAAI,CAAC;IACjB7C,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAM8C,qBAAqB,GAAIC,SAAS,IAAK;IAC3C3C,oBAAoB,CAAC2C,SAAS,CAAC;IAC/BN,iBAAiB,CAAC,CAAC;IACnBzC,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMgD,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC9C5C,gBAAgB,CAAC2C,MAAM,CAAC;IACxBpC,aAAa,CAACqC,MAAM,CAAC;IACrBT,iBAAiB,CAAC,CAAC;IACnBzC,aAAa,CAAC,CAAC,CAAC;IAChB;IACAiB,WAAW,CAAC,EAAE,CAAC;IACfrB,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;;IAExC;IACA,MAAMC,WAAW,GAAG;MAClBrD,UAAU,EAAE,CAAC;MACbE,QAAQ;MACRE,iBAAiB;MACjBE,aAAa,EAAE4C,MAAM;MACrBrC,UAAU,EAAEsC;IACd,CAAC;IAED,IAAI;MACFtD,YAAY,CAAC2B,OAAO,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAAC2B,WAAW,CAAC,CAAC;MACpE1B,OAAO,CAACI,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxBrD,aAAa,CAAC,CAAC,CAAC;IAChBE,WAAW,CAAC,IAAI,CAAC;IACjBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,gBAAgB,CAAC;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFE,aAAa,CAAC,IAAI,CAAC;IACnBI,WAAW,CAAC,EAAE,CAAC;;IAEf;IACArB,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;IACxCvD,YAAY,CAACuD,UAAU,CAAC,iBAAiB,CAAC;IAE1CzB,OAAO,CAACI,GAAG,CAAC,uBAAuB,CAAC;EACtC,CAAC;;EAID;EACA,MAAMwB,oBAAoB,GAAIC,OAAO,IAAK;IACxC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACnC,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMmB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIA,GAAG,GAAGpB,iBAAiB,CAACqC,OAAO,GAAG,GAAG,EAAE;QACzC;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGjC,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC;MACjD,MAAMG,cAAc,GAAGxC,eAAe,CAACsC,OAAO;MAE9C,IAAIE,cAAc,KAAKD,iBAAiB,EAAE;QACxC;QACAvC,eAAe,CAACsC,OAAO,GAAGC,iBAAiB;QAC3CtC,iBAAiB,CAACqC,OAAO,GAAGjB,GAAG;QAC/BtB,WAAW,CAACsC,OAAO,CAAC;;QAEpB;QACA,IAAI;UACF3D,YAAY,CAAC2B,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC,CAAC;UAC9D7B,OAAO,CAACI,GAAG,CAAC,iCAAiC,EAAEyB,OAAO,CAACnC,MAAM,CAAC;QAChE,CAAC,CAAC,OAAON,KAAK,EAAE;UACdY,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;;QAEA;QACA,MAAMsC,WAAW,GAAG;UAClBrD,UAAU,EAAE,CAAC;UACbE,QAAQ;UACRE,iBAAiB;UACjBE,aAAa;UACbO;QACF,CAAC;QAED,IAAI;UACFhB,YAAY,CAAC2B,OAAO,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAAC2B,WAAW,CAAC,CAAC;UACpE;QACF,CAAC,CAAC,OAAOtC,KAAK,EAAE;UACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;MACF,CAAC,MAAM;QACL;MAAA;IAEJ;EACF,CAAC;;EAID;EACA,MAAM6C,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,OAAO,GAAGnE,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACxDC,YAAY,CAACkE,OAAO,CAAC;IACrBhE,YAAY,CAAC2B,OAAO,CAAC,WAAW,EAAEqC,OAAO,CAAC;IAC1ClC,OAAO,CAACI,GAAG,CAAC,UAAU,EAAE8B,OAAO,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjE,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;IACxCvD,YAAY,CAACuD,UAAU,CAAC,iBAAiB,CAAC;IAC1CpC,QAAQ,CAAC,kBAAkB,CAAC;IAC5BW,OAAO,CAACI,GAAG,CAAC,aAAa,CAAC;EAC5B,CAAC;EAED,MAAMgC,WAAW,GAAIC,YAAY,IAAK;IACpChD,QAAQ,CAACgD,YAAY,CAAC;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjD,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACE3D,OAAA,CAAAE,SAAA;IAAA2G,QAAA,eACE7G,OAAA,CAACd,aAAa;MAACwD,KAAK,EAAEA,KAAM;MAAAmE,QAAA,gBAC1B7G,OAAA,CAACf,WAAW;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfjH,OAAA,CAACrB,GAAG;QACFuI,EAAE,EAAE;UACFC,SAAS,EAAE,OAAO;UAClBC,eAAe,EAAE,oBAAoB;UACrCC,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QACrB,CAAE;QAAAV,QAAA,eAEF7G,OAAA,CAACtB,SAAS;UACR8I,QAAQ,EAAE,KAAM;UAChBN,EAAE,EAAE;YACFM,QAAQ,EAAE,MAAM;YAAE;YAClBC,EAAE,EAAE;cAAEH,EAAE,EAAE,CAAC;cAAEI,EAAE,EAAE,CAAC;cAAEH,EAAE,EAAE;YAAE,CAAC,CAAC;UAC9B,CAAE;UAAAV,QAAA,gBAGF7G,OAAA,CAACrB,GAAG;YACFuI,EAAE,EAAE;cACFS,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBC,EAAE,EAAE;YACN,CAAE;YAAAjB,QAAA,gBAEF7G,OAAA,CAACrB,GAAG;cAACuI,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAAlB,QAAA,gBACzD7G,OAAA,CAACT,aAAa;gBACZ2H,EAAE,EAAE;kBACF9F,QAAQ,EAAE,EAAE;kBACZ4G,KAAK,EAAE;gBACT;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjH,OAAA,CAACpB,UAAU;gBACTqJ,OAAO,EAAC,IAAI;gBACZC,SAAS,EAAC,IAAI;gBACdhB,EAAE,EAAE;kBACFc,KAAK,EAAE,cAAc;kBACrB7G,UAAU,EAAE;gBACd,CAAE;gBAAA0F,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENjH,OAAA,CAACrB,GAAG;cAACuI,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEI,GAAG,EAAE;cAAE,CAAE;cAAAlB,QAAA,gBACnC7G,OAAA,CAACZ,OAAO;gBAAC+I,KAAK,EAAE,MAAM9F,SAAS,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,IAAK;gBAAAwE,QAAA,eAC5D7G,OAAA,CAACX,UAAU;kBACT+I,OAAO,EAAE7B,eAAgB;kBACzByB,KAAK,EAAC,SAAS;kBAAAnB,QAAA,EAEdxE,SAAS,KAAK,OAAO,gBAAGrC,OAAA,CAACP,YAAY;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjH,OAAA,CAACR,aAAa;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEVjH,OAAA,CAACZ,OAAO;gBAAC+I,KAAK,EAAC,0HAAsB;gBAAAtB,QAAA,eACnC7G,OAAA,CAACX,UAAU;kBACT+I,OAAO,EAAE3B,kBAAmB;kBAC5BuB,KAAK,EAAC,OAAO;kBAAAnB,QAAA,eAEb7G,OAAA,CAACV,eAAe;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjH,OAAA,CAAChB,KAAK;YACJqJ,SAAS,EAAE,CAAE;YACbnB,EAAE,EAAE;cACFoB,CAAC,EAAE;gBAAEhB,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE;YACpB,CAAE;YAAAV,QAAA,gBAGF7G,OAAA,CAACrB,GAAG;cAACuI,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,eACjB7G,OAAA,CAACnB,OAAO;gBAAC8D,UAAU,EAAEA,UAAW;gBAAAkE,QAAA,EAC7B3E,KAAK,CAACqG,GAAG,CAAEC,KAAK,iBACfxI,OAAA,CAAClB,IAAI;kBAAA+H,QAAA,eACH7G,OAAA,CAACjB,SAAS;oBAAA8H,QAAA,EAAE2B;kBAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC,GADrBuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGNjH,OAAA,CAACrB,GAAG;cAACuI,EAAE,EAAE;gBAAEC,SAAS,EAAE;cAAQ,CAAE;cAAAN,QAAA,GAC7BlE,UAAU,KAAK,CAAC,iBACf3C,OAAA,CAACN,UAAU;gBAAC+I,YAAY,EAAEjD,gBAAiB;gBAACkD,OAAO,EAAEhC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACpE,EAEAtE,UAAU,KAAK,CAAC,IAAIE,QAAQ,iBAC3B7C,OAAA,CAACL,eAAe;gBACdyF,UAAU,EAAEvC,QAAQ,CAACuC,UAAW;gBAChCuD,QAAQ,EAAEjD,qBAAsB;gBAChCkD,MAAM,EAAEA,CAAA,KAAMhG,aAAa,CAAC,CAAC,CAAE;gBAC/B8F,OAAO,EAAEhC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACF,EAEAtE,UAAU,KAAK,CAAC,IAAIE,QAAQ,IAAIE,iBAAiB,iBAChD/C,OAAA,CAACJ,WAAW;gBACViJ,MAAM,EAAEhG,QAAQ,CAACmC,OAAQ;gBACzBW,SAAS,EAAE5C,iBAAkB;gBAC7B+F,QAAQ,EAAElD,mBAAoB;gBAC9BgD,MAAM,EAAEA,CAAA,KAAM;kBAACvD,iBAAiB,CAAC,CAAC;kBAAEzC,aAAa,CAAC,CAAC,CAAC;gBAAC,CAAG;gBACxD8F,OAAO,EAAEhC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACF,EAEAtE,UAAU,KAAK,CAAC,KAAKa,UAAU,IAAII,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,iBACtDhE,OAAA,CAACH,aAAa;gBACZ4F,IAAI,EAAEjC,UAAU,IAAI,EAAG;gBACvBqF,MAAM,EAAEhG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmC,OAAQ;gBAC1B+D,OAAO,EAAE9C,WAAY;gBACrByC,OAAO,EAAEhC,WAAY;gBACrBsC,aAAa,EAAEpF,QAAS;gBACxBqF,YAAY,EAAE/C;cAAqB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENjH,OAAA,CAACF,aAAa;QACZoJ,IAAI,EAAE,CAAC,CAACxF,KAAM;QACdyF,OAAO,EAAEzF,KAAM;QACf0F,OAAO,EAAExC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC,gBAChB,CAAC;AAEP;AAAC7E,EAAA,CAjaQD,GAAG;AAAAkH,EAAA,GAAHlH,GAAG;AAmaZ,eAAeA,GAAG;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}