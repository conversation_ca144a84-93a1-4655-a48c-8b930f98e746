{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 10\n  }, this);\n}\n_c = SlideDownTransition;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === newRow.id ? newRow : row);\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  }, [recalculateTotal]);\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n\n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    console.log('删除行:', id);\n\n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n\n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    console.log('恢复行:', id);\n\n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n\n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasSelectedRemark ? remarkText : '',\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: remarkText,\n              color: hasSelectedRemark ? 'primary' : 'default',\n              variant: hasSelectedRemark ? 'filled' : 'outlined',\n              size: \"small\",\n              onClick: () => handleRemarksClick(params.row.id, params.value || ''),\n              clickable: true,\n              sx: {\n                maxWidth: '100%',\n                cursor: 'pointer',\n                '& .MuiChip-label': {\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  display: 'block'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          disableSelectionOnClick: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: (newRow, oldRow) => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      TransitionComponent: SlideDownTransition,\n      sx: {\n        borderRadius: 10,\n        border: 4,\n        borderColor: 'primary.main',\n        '& .MuiAlert-root': {\n          borderRadius: 10,\n          border: 4,\n          borderColor: 'success.main'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 621,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"POasumYfcbVikh1Sh73oBD6LWOo=\");\n_c2 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "Slide", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "recalculateTotal", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "Number", "processRowUpdate", "newRow", "prev", "updatedData", "onProcessRowUpdateError", "handleCloseSnackbar", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "rowToRemove", "commissionValue", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "rowToRestore", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "sx", "max<PERSON><PERSON><PERSON>", "opacity", "overflow", "textOverflow", "whiteSpace", "remarkText", "hasSelectedRemark", "onClick", "clickable", "cursor", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "textAlign", "py", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "experimentalFeatures", "newEditingApi", "backgroundColor", "lineHeight", "padding", "overflowX", "border", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "TransitionComponent", "borderRadius", "borderColor", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      \n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === newRow.id ? newRow : row);\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  }, [recalculateTotal]);\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n        \n        // 不再直接保存到localStorage，由App组件统一处理\n        return updatedData;\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    console.log('删除行:', id);\n    \n    // 找到要删除的行，记录其COMMISSION值用于日志\n    const rowToRemove = gridData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n    \n    // 标记行为已删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      // 只对未被移除的NO重新编号\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    console.log('恢复行:', id);\n    \n    // 找到要恢复的行，记录其COMMISSION值用于日志\n    const rowToRestore = gridData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n    \n    // 标记行为未删除\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          return (\n            <Tooltip title={hasSelectedRemark ? remarkText : ''} arrow placement=\"top\">\n              <Chip\n                label={remarkText}\n                color={hasSelectedRemark ? 'primary' : 'default'}\n                variant={hasSelectedRemark ? 'filled' : 'outlined'}\n                size=\"small\"\n                onClick={() => handleRemarksClick(params.row.id, params.value || '')}\n                clickable\n                sx={{ maxWidth: '100%', cursor: 'pointer', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n              />\n            </Tooltip>\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Chip\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          return (\n            <Chip\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <DataGrid\n            rows={gridData}\n            columns={columns}\n            pageSize={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            disableSelectionOnClick\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n            }}\n            processRowUpdate={(newRow, oldRow) => {\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            }}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            experimentalFeatures={{ newEditingApi: true }}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n        TransitionComponent={SlideDownTransition} \n        sx={{\n          borderRadius: 10,\n          border: 4,\n          borderColor: 'primary.main',\n          '& .MuiAlert-root': {\n            borderRadius: 10,\n            border: 4,\n            borderColor: 'success.main',\n          },\n        }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,qBAAqB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOH,OAAA,CAACF,KAAK;IAAA,GAAKK,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;AAACC,EAAA,GAFQP,mBAAmB;AAI5B,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,MAAM;IACzD,MAAM6D,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGxB,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd6D,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAG7B,IAAI,CAAC8B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnF,QAAQ,CAAC,MAAM;IAC7CoF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7ClC,aAAa,GAAG,IAAIA,aAAa,CAACmC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAInC,aAAa,IAAIA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGpC,aAAa,CAAC0B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG3F,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM4F,iBAAiB,GAAG5F,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM6F,gBAAgB,GAAG7F,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM8F,UAAU,GAAIlD,IAAI,IAAKA,IAAI,CAAC8B,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCkB,UAAU,EAAEpB,GAAG,CAACoB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACAjG,SAAS,CAAC,MAAM;IACd,IAAImD,YAAY,IAAI8B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMa,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGrC,IAAI,CAACW,SAAS,CAACsB,UAAU,CAACf,QAAQ,CAAC,CAAC;MACpD,MAAMoB,WAAW,GAAGR,mBAAmB,CAACS,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIL,gBAAgB,CAACO,OAAO,EAAE;UAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;QACxC;QACAP,gBAAgB,CAACO,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CX,mBAAmB,CAACS,OAAO,GAAGF,OAAO;UACrCN,iBAAiB,CAACQ,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC/C,YAAY,CAAC,CAAC,GAAG8B,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACO,OAAO,EAAE;QAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACrB,QAAQ,EAAE9B,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC;IAAE4G,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhH,QAAQ,CAAC;IACjD4G,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAjH,SAAS,CAAC,MAAM;IACd,IAAIuE,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAGpF,OAAO,aAAagB,MAAM,EAAE;;MAEnD;MACA,MAAMqE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAV,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7E,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM8E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMjG,KAAK,CAACkG,MAAM,CAAC,GAAGjG,OAAO,YAAYgB,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+E,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9E,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMiF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMuC,gBAAgB,GAAGlI,WAAW,CAAE6C,IAAI,IAAK;IAC7C,MAAMsF,QAAQ,GAAGtF,IAAI,CAACuF,IAAI,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIwC,QAAQ,EAAE;MACZ;MACA,MAAME,QAAQ,GAAGxF,IAAI,CAClByF,MAAM,CAAC1D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDwD,MAAM,CAAC,CAACC,GAAG,EAAE5D,GAAG,KAAK4D,GAAG,IAAIC,MAAM,CAAC7D,GAAG,CAACoB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/Dd,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkD,QAAQ,CAAC;MACnCF,QAAQ,CAACnC,UAAU,GAAGqC,QAAQ;IAChC;IACA,OAAOxF,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6F,gBAAgB,GAAG1I,WAAW,CAAE2I,MAAM,IAAK;IAC/C1D,WAAW,CAAC2D,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAACjE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKkD,MAAM,CAAClD,EAAE,GAAGkD,MAAM,GAAG/D,GAAG,CAAC;MACtEiE,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFpC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAO+B,MAAM;EACf,CAAC,EAAE,CAACT,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,uBAAuB,GAAIjB,KAAK,IAAK;IACzCpB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASkB,KAAK,CAAClB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMmC,mBAAmB,GAAGA,CAAA,KAAM;IAChCtC,WAAW,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMsC,iBAAiB,GAAGnJ,KAAK,CAACG,WAAW,CAAC,CAAC+G,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiC,kBAAkB,GAAGjJ,WAAW,CAAC,MAAM;IAC3C8G,gBAAgB,CAAC8B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPlC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwC,kBAAkB,GAAGlJ,WAAW,CAAEmJ,MAAM,IAAK;IACjD,MAAM;MAAEpC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB7B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgE,MAAM,EAAE,MAAM,EAAEpC,KAAK,CAAC;MAChD9B,WAAW,CAACmE,QAAQ,IAAI;QACtB,IAAIP,WAAW,GAAGO,QAAQ,CAACzE,GAAG,CAACC,GAAG,IAAI;UACpC,IAAIA,GAAG,CAACa,EAAE,KAAKsB,KAAK,EAAE;YACpB;YACA,IAAIoC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAGvE,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAEsE,MAAM;gBAAErE,iBAAiB,EAAEqE;cAAO,CAAC;YAC/D;UACF;UACA,OAAOvE,GAAG;QACZ,CAAC,CAAC;;QAEF;QACAiE,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;;QAE3C;QACA,OAAOA,WAAW;MACpB,CAAC,CAAC;MACFpC,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAqC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACpC,aAAa,EAAEoC,kBAAkB,EAAEf,gBAAgB,CAAC,CAAC;;EAEzD;EACA,MAAMmB,mBAAmB,GAAGrJ,WAAW,CAAC,MAAM;IAC5CmE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmF,oBAAoB,GAAGtJ,WAAW,CAAC,MAAM;IAC7CmE,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsF,YAAY,GAAGvJ,WAAW,CAAC,MAAM;IACrC,IAAIgE,SAAS,CAACwF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC/F,cAAc,CAACgG,QAAQ,CAACzF,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE9F,iBAAiB,CAACkF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE5E,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD/C,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE0C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI7F,cAAc,CAACgG,QAAQ,CAACzF,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpD/C,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAAC5C,SAAS,EAAEP,cAAc,EAAE6F,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG1J,WAAW,CAAEmJ,MAAM,IAAK;IAC3CzF,iBAAiB,CAACkF,IAAI,IAAIA,IAAI,CAACN,MAAM,CAACqB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC,CAAC;IAC/D1C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgD,eAAe,GAAG5J,WAAW,CAAEyF,EAAE,IAAK;IAC1CP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAMoE,WAAW,GAAG7E,QAAQ,CAACoD,IAAI,CAACxD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACvD,MAAMqE,eAAe,GAAGD,WAAW,GAAGA,WAAW,CAAC7D,UAAU,GAAG,CAAC;IAChEd,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2E,eAAe,CAAC;;IAE/C;IACA7E,WAAW,CAAC2D,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAACjE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnFiE,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;MAC3C;MACA,MAAMkB,cAAc,GAAGlB,WAAW,CAACP,MAAM,CAAC1D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHoE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,EAAE,GAAGyE,CAAC,CAACzE,EAAE,CAAC;MAC1CsE,cAAc,CAACI,OAAO,CAAC,CAACvF,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOqD,WAAW;IACpB,CAAC,CAAC;IACFpC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMkC,aAAa,GAAGpK,WAAW,CAAEyF,EAAE,IAAK;IACxCP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACA,MAAM4E,YAAY,GAAGrF,QAAQ,CAACoD,IAAI,CAACxD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;IACxD,MAAMqE,eAAe,GAAGO,YAAY,GAAGA,YAAY,CAACrE,UAAU,GAAG,CAAC;IAClEd,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2E,eAAe,CAAC;;IAE/C;IACA7E,WAAW,CAAC2D,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAACjE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpFiE,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;MAC3C,MAAMkB,cAAc,GAAGlB,WAAW,CAACP,MAAM,CAAC1D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHoE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,EAAE,GAAGyE,CAAC,CAACzE,EAAE,CAAC;MAC1CsE,cAAc,CAACI,OAAO,CAAC,CAACvF,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOqD,WAAW;IACpB,CAAC,CAAC;IACFpC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMoC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjG,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMkG,YAAY,GAAGvF,QAAQ,CAC1BsD,MAAM,CAAC1D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAwF,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACtE,EAAE,KAAK,QAAQ,IAAI,OAAOuE,CAAC,CAACvE,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOsE,CAAC,CAACtE,EAAE,GAAGuE,CAAC,CAACvE,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM6E,OAAO,GAAGD,YAAY,CAAC5F,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbiF,IAAI,EAAE7F,GAAG,CAAC6F,IAAI,GAAI,OAAO7F,GAAG,CAAC6F,IAAI,KAAK,QAAQ,IAAI7F,GAAG,CAAC6F,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAG7E,GAAG,CAAC6F,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9F,GAAG,CAAC6F,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE7F,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG+F,IAAI,CAACC,KAAK,CAAChG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFiG,EAAE,EAAE,OAAOjG,GAAG,CAACiG,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAChG,GAAG,CAACiG,EAAE,CAAC,GAAGjG,GAAG,CAACiG,EAAE,IAAI,EAAE;QAClEhG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGgG,KAAK,EAAE,OAAOlG,GAAG,CAACmG,QAAQ,KAAK,QAAQ,GACpCnG,GAAG,CAACmG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGnG,GAAG,CAACmG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGpG,GAAG,CAACmG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EpG,GAAG,CAACmG,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOrG,GAAG,CAACoB,UAAU,KAAK,QAAQ,GAAGpB,GAAG,CAACoB,UAAU,CAACgF,OAAO,CAAC,CAAC,CAAC,GAAGpG,GAAG,CAACoB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMkF,WAAW,GAAGlG,QAAQ,CACzBsD,MAAM,CAAC1D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACoB,UAAU,CAAC,CACpEuC,MAAM,CAAC,CAACC,GAAG,EAAE5D,GAAG,KAAK4D,GAAG,IAAI,OAAO5D,GAAG,CAACoB,UAAU,KAAK,QAAQ,GAAGpB,GAAG,CAACoB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMmF,QAAQ,GAAG,MAAMtJ,KAAK,CAACuJ,IAAI,CAAC,GAAGtJ,OAAO,oBAAoB,EAAE;QAChEe,IAAI,EAAE2H,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnClI,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIqI,QAAQ,CAACtI,IAAI,IAAIsI,QAAQ,CAACtI,IAAI,CAACwI,KAAK,EAAE;QACxC;QACA,MAAMnE,WAAW,GAAG,GAAGpF,OAAO,CAAC4I,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAACtI,IAAI,CAACyI,MAAM,EAAE;;QAExE;QACA,MAAMnE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEL,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;QAE/B;QACAV,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACAL,UAAU,CAAC,MAAM;UACf,MAAMgF,MAAM,GAAGnE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CkE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGxE,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC6D,MAAM,CAAC;UACjChF,UAAU,CAAC,MAAM;YACfa,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC2D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRvC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMuH,kBAAkB,GAAG5L,WAAW,CAAC,CAAC+G,KAAK,EAAE8E,KAAK,KAAK;IACvD7C,iBAAiB,CAACjC,KAAK,EAAE8E,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC7C,iBAAiB,CAAC,CAAC;EAEvB,MAAM8C,OAAO,GAAG5L,OAAO,CAAC,MAAOkD,WAAW,CAACuB,GAAG,CAACoH,GAAG,IAAI;IACpD,IAAI,EAAE/G,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACgH,cAAc,CAACD,GAAG,CAAC1I,KAAK,CAAC,CAAC,IAAI0I,GAAG,CAAC1I,KAAK,KAAK,SAAS,IAAI0I,GAAG,CAAC1I,KAAK,KAAK,QAAQ,IAAI0I,GAAG,CAAC1I,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAI0I,GAAG,CAAC1I,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAE0I,GAAG,CAAC1I,KAAK;QAChBC,UAAU,EAAEyI,GAAG,CAACzI,UAAU;QAC1B2I,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV3I,QAAQ,EAAE,KAAK;QACf4I,UAAU,EAAGlE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIsC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMqH,iBAAiB,GAAGnE,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE5C,OAAA,CAACb,OAAO;cAACgL,KAAK,EAAEpE,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACwH,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvEtK,OAAA,CAACd,IAAI;gBACHqL,KAAK,EAAEL,iBAAkB;gBACzBM,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,OAAO,EAAE,GAAG;kBAAE,kBAAkB,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEzB,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAlJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UACA,IAAIyK,UAAU,GAAG,MAAM;UACvB,IAAIC,iBAAiB,GAAG,KAAK;UAC7B,IAAInF,MAAM,CAACrD,GAAG,CAACE,iBAAiB,IAAImD,MAAM,CAACrD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EqI,UAAU,GAAGlF,MAAM,CAACrD,GAAG,CAACE,iBAAiB;YACzCsI,iBAAiB,GAAG,IAAI;UAC1B;UACA,oBACElL,OAAA,CAACb,OAAO;YAACgL,KAAK,EAAEe,iBAAiB,GAAGD,UAAU,GAAG,EAAG;YAACb,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAC,QAAA,eACxEtK,OAAA,CAACd,IAAI;cACHqL,KAAK,EAAEU,UAAW;cAClBT,KAAK,EAAEU,iBAAiB,GAAG,SAAS,GAAG,SAAU;cACjDT,OAAO,EAAES,iBAAiB,GAAG,QAAQ,GAAG,UAAW;cACnDR,IAAI,EAAC,OAAO;cACZS,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAAC3D,MAAM,CAACrD,GAAG,CAACa,EAAE,EAAEwC,MAAM,CAAC4D,KAAK,IAAI,EAAE,CAAE;cACrEyB,SAAS;cACTT,EAAE,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAES,MAAM,EAAE,SAAS;gBAAE,kBAAkB,EAAE;kBAAEP,QAAQ,EAAE,QAAQ;kBAAEC,YAAY,EAAE,UAAU;kBAAEC,UAAU,EAAE,QAAQ;kBAAEzB,OAAO,EAAE;gBAAQ;cAAE;YAAE;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAEd;MACF,CAAC;IACH;IACA,IAAIqJ,GAAG,CAAC1I,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAE0I,GAAG,CAAC1I,KAAK;QAChBC,UAAU,EAAEyI,GAAG,CAACzI,UAAU;QAC1B2I,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACV3I,QAAQ,EAAE,KAAK;QACf4I,UAAU,EAAGlE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIsC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACE7C,OAAA,CAACd,IAAI;cACHqL,KAAK,EAAC,cAAI;cACVC,KAAK,EAAC,SAAS;cACfE,IAAI,EAAC,OAAO;cACZY,IAAI,eAAEtL,OAAA,CAACP,QAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnB2K,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACnC,MAAM,CAACrD,GAAG,CAACa,EAAE,CAAE;cAC5CoH,EAAE,EAAE;gBAAEU,MAAM,EAAE;cAAU;YAAE;cAAAhL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAEN;UACA,oBACER,OAAA,CAACd,IAAI;YACHqL,KAAK,EAAC,cAAI;YACVC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZY,IAAI,eAAEtL,OAAA,CAACR,UAAU;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrB2K,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC3B,MAAM,CAACrD,GAAG,CAACa,EAAE,CAAE;YAC9CoH,EAAE,EAAE;cAAEU,MAAM,EAAE;YAAU;UAAE;YAAAhL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAEN;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGqJ,GAAG;MACNxI,QAAQ,EAAE0E,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACrD,GAAG,IAAIqD,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIsC,MAAM,CAACrD,GAAG,IAAIqD,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOgH,GAAG,CAACxI,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD4I,UAAU,EAAGlE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACrD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIoG,GAAG,CAAC1I,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEnB,OAAA,CAAC9B,UAAU;YAACuM,OAAO,EAAC,OAAO;YAACc,UAAU,EAAC,MAAM;YAACf,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOvE,MAAM,CAAC4D,KAAK,KAAK,QAAQ,GAAG5D,MAAM,CAAC4D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO/C,MAAM,CAAC4D,KAAK,KAAK,QAAQ,IAAI,CAAC6B,KAAK,CAACjF,MAAM,CAACR,MAAM,CAAC4D,KAAK,CAAC,CAAC,GAAGpD,MAAM,CAACR,MAAM,CAAC4D,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG/C,MAAM,CAAC4D;UAAK;YAAAtJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIuF,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE7C,OAAA,CAAC9B,UAAU;YAACuM,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,eAAe;YAACG,EAAE,EAAE;cAAEc,cAAc,EAAE;YAAe,CAAE;YAAAnB,QAAA,EACtFvE,MAAM,CAAC4D;UAAK;YAAAtJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIqJ,GAAG,CAAC1I,KAAK,KAAK,MAAM,IAAI4E,MAAM,CAAC4D,KAAK,EAAE;UACxC,OAAO5D,MAAM,CAAC4D,KAAK,CAACnB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIqB,GAAG,CAAC1I,KAAK,KAAK,IAAI,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC1I,KAAK,KAAK,OAAO,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOlB,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC1I,KAAK,KAAK,IAAI,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOlB,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAAC4D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC1I,KAAK,KAAK,UAAU,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO5D,MAAM,CAAC4D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG5D,MAAM,CAAC4D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG/C,MAAM,CAAC4D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAAC1I,KAAK,KAAK,YAAY,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO5D,MAAM,CAAC4D,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAAC1I,KAAK,KAAK,YAAY,IAAI,OAAO4E,MAAM,CAAC4D,KAAK,KAAK,QAAQ,IAAI,CAAC6B,KAAK,CAACjF,MAAM,CAACR,MAAM,CAAC4D,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOpD,MAAM,CAACR,MAAM,CAAC4D,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAO/C,MAAM,CAAC4D,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO5D,MAAM,CAAC4D,KAAK;QACrB;QACA,OAAO5D,MAAM,CAAC4D,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACvD,MAAM,CAACsF,OAAO,CAAE,EAAE,CAACxK,WAAW,EAAE4B,QAAQ,EAAE4G,kBAAkB,EAAEhC,eAAe,EAAEQ,aAAa,CAAC,CAAC;;EAEjG;EACA,IAAI,CAACpF,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACElD,OAAA,CAAC/B,GAAG;MAAC0M,EAAE,EAAE;QAAEgB,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAtB,QAAA,gBACtCtK,OAAA,CAAC9B,UAAU;QAACuM,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAjK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAAC7B,MAAM;QACLsM,OAAO,EAAC,WAAW;QACnBU,OAAO,EAAEtK,OAAQ;QACjB8J,EAAE,EAAE;UAAEkB,EAAE,EAAE;QAAE,CAAE;QAAAvB,QAAA,EACf;MAED;QAAAjK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACER,OAAA,CAAC/B,GAAG;IAAAqM,QAAA,gBACFtK,OAAA,CAAC/B,GAAG;MAAC0M,EAAE,EAAE;QAAEpB,OAAO,EAAE,MAAM;QAAEuC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,gBACzFtK,OAAA,CAAC9B,UAAU;QAACuM,OAAO,EAAC,IAAI;QAACwB,YAAY;QAAA3B,QAAA,EAAC;MAEtC;QAAAjK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbR,OAAA,CAAC/B,GAAG;QAAAqM,QAAA,gBACFtK,OAAA,CAAC7B,MAAM;UACLsM,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf0B,SAAS,eAAElM,OAAA,CAACX,YAAY;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B2K,OAAO,EAAEpG,cAAe;UACxB4F,EAAE,EAAE;YAAEwB,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,EACf;QAED;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETR,OAAA,CAAC7B,MAAM;UACLsM,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf0B,SAAS,eAAElM,OAAA,CAACN,gBAAgB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChC2K,OAAO,EAAE/C,gBAAiB;UAC1BgE,QAAQ,EAAElK,oBAAqB;UAC/ByI,EAAE,EAAE;YAAEwB,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAEbpI,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETR,OAAA,CAAC7B,MAAM;UACLsM,OAAO,EAAC,UAAU;UAClByB,SAAS,eAAElM,OAAA,CAACV,cAAc;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9B2K,OAAO,EAAEvF,aAAc;UAAA0E,QAAA,EACxB;QAED;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAAC5B,KAAK;MAACuM,EAAE,EAAE;QAAEX,KAAK,EAAE,MAAM;QAAEc,QAAQ,EAAE;MAAS,CAAE;MAAAR,QAAA,eAC/CtK,OAAA,CAAC/B,GAAG;QAAC0M,EAAE,EAAE;UAAE0B,MAAM,EAAE,MAAM;UAAErC,KAAK,EAAE;QAAO,CAAE;QAAAM,QAAA,eACzCtK,OAAA,CAACZ,QAAQ;UACPkN,IAAI,EAAExJ,QAAS;UACf8G,OAAO,EAAEA,OAAQ;UACjB2C,QAAQ,EAAE,GAAI;UACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAG7G,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAACrD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIuC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFgK,cAAc,EAAG9G,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAACrD,GAAG,CAACc,OAAO,IAAIuC,MAAM,CAACrD,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOkD,MAAM,CAAC+G,MAAM,CAACzL,QAAQ,IAAI,OAAO0E,MAAM,CAAC+G,MAAM,CAACzL,QAAQ,KAAK,UAAU,GAC3E0E,MAAM,CAAC+G,MAAM,CAACzL,QAAQ,CAAC0E,MAAM,CAAC,GAAGA,MAAM,CAAC+G,MAAM,CAACzL,QAAQ;UAC3D,CAAE;UACFmF,gBAAgB,EAAEA,CAACC,MAAM,EAAEsG,MAAM,KAAK;YACpC,IAAItG,MAAM,CAAC3C,UAAU,KAAKV,SAAS,EAAE;cACnC,IAAI,OAAOqD,MAAM,CAAC3C,UAAU,KAAK,QAAQ,EAAE;gBACzC2C,MAAM,CAAC3C,UAAU,GAAGyC,MAAM,CAACE,MAAM,CAAC3C,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAO0C,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDoG,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9CtC,EAAE,EAAE;YACF,cAAc,EAAE;cACduC,eAAe,EAAE,0BAA0B;cAC3C3B,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChB2B,eAAe,EAAE,0BAA0B;cAC3C1C,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrBQ,UAAU,EAAE,QAAQ;cACpBmC,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCG,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrBvC,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnBwC,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BF,OAAO,EAAE,OAAO;cAChBpC,UAAU,EAAE,QAAQ;cACpBmC,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClCnC,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnBqC,UAAU,EAAE,MAAM;cAClB5B,UAAU,EAAE;YACd;UACF;QAAE;UAAAlL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRR,OAAA,CAACzB,MAAM;MACLiG,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzB+I,OAAO,EAAExG,kBAAmB;MAC5ByG,SAAS;MACT5C,QAAQ,EAAC,IAAI;MACb6C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAtD,QAAA,gBAEnBtK,OAAA,CAACxB,WAAW;QAAA8L,QAAA,eACVtK,OAAA,CAAC/B,GAAG;UAAC0M,EAAE,EAAE;YAAEpB,OAAO,EAAE,MAAM;YAAEuC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBAClFtK,OAAA,CAAC9B,UAAU;YAACuM,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAAjK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CR,OAAA,CAAC7B,MAAM;YACL+N,SAAS,eAAElM,OAAA,CAACT,OAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB2K,OAAO,EAAEhE,mBAAoB;YAC7BqD,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAjK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdR,OAAA,CAACvB,aAAa;QAACoP,QAAQ;QAAClD,EAAE,EAAE;UAAEmD,CAAC,EAAE;QAAE,CAAE;QAAAxD,QAAA,eACnCtK,OAAA,CAACH,aAAa;UACZwM,MAAM,EAAE,GAAI;UACZ0B,SAAS,EAAExM,cAAc,CAAC2B,MAAO;UACjC8K,QAAQ,EAAE,EAAG;UACbhE,KAAK,EAAC,MAAM;UAAAM,QAAA,EAEXA,CAAC;YAAEhH,KAAK;YAAEgG;UAAM,CAAC,KAAK;YACrB,MAAMrC,MAAM,GAAG1F,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACEtD,OAAA,CAACpB,QAAQ;cAEP0K,KAAK,EAAEA,KAAM;cACb2E,cAAc;cACdC,eAAe,eACblO,OAAA,CAAChB,UAAU;gBACTmP,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBhD,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACP,MAAM,CAAE;gBAAAqD,QAAA,eAEpCtK,OAAA,CAACR,UAAU;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAA8J,QAAA,eAEDtK,OAAA,CAACnB,cAAc;gBAACsM,OAAO,EAAEA,CAAA,KAAMnE,kBAAkB,CAACC,MAAM,CAAE;gBAAAqD,QAAA,eACxDtK,OAAA,CAAClB,YAAY;kBAACsP,OAAO,EAAEnH;gBAAO;kBAAA5G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZyG,MAAM;cAAA5G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBR,OAAA,CAACtB,aAAa;QAAA4L,QAAA,eACZtK,OAAA,CAAC7B,MAAM;UAACgN,OAAO,EAAEpE,kBAAmB;UAAAuD,QAAA,EAAC;QAAE;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTR,OAAA,CAACzB,MAAM;MACLiG,IAAI,EAAExC,eAAgB;MACtBuL,OAAO,EAAEnG,oBAAqB;MAC9BoG,SAAS;MACT5C,QAAQ,EAAC,IAAI;MACb6C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAtD,QAAA,gBAEnBtK,OAAA,CAACxB,WAAW;QAAA8L,QAAA,EAAC;MAAK;QAAAjK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCR,OAAA,CAACvB,aAAa;QAAA6L,QAAA,eACZtK,OAAA,CAACjB,SAAS;UACRsP,SAAS;UACTC,MAAM,EAAC,OAAO;UACd/K,EAAE,EAAC,MAAM;UACTgH,KAAK,EAAC,0BAAM;UACZgE,IAAI,EAAC,MAAM;UACXf,SAAS;UACT/C,OAAO,EAAC,UAAU;UAClBd,KAAK,EAAE7H,SAAU;UACjB0M,QAAQ,EAAGC,CAAC,IAAK1M,YAAY,CAAC0M,CAAC,CAACC,MAAM,CAAC/E,KAAK;QAAE;UAAAtJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBR,OAAA,CAACtB,aAAa;QAAA4L,QAAA,gBACZtK,OAAA,CAAC7B,MAAM;UAACgN,OAAO,EAAE/D,oBAAqB;UAAAkD,QAAA,EAAC;QAAE;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDR,OAAA,CAAC7B,MAAM;UAACgN,OAAO,EAAE9D,YAAa;UAACmD,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETR,OAAA,CAAC3B,QAAQ;MACPmG,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBmK,gBAAgB,EAAE,IAAK;MACvBpB,OAAO,EAAE1G,mBAAoB;MAC7B+H,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MACxDC,mBAAmB,EAAE7O,mBAAoB;MACzCyK,EAAE,EAAE;QACFqE,YAAY,EAAE,EAAE;QAChB1B,MAAM,EAAE,CAAC;QACT2B,WAAW,EAAE,cAAc;QAC3B,kBAAkB,EAAE;UAClBD,YAAY,EAAE,EAAE;UAChB1B,MAAM,EAAE,CAAC;UACT2B,WAAW,EAAE;QACf;MACF,CAAE;MAAA3E,QAAA,eAEFtK,OAAA,CAAC1B,KAAK;QAACiP,OAAO,EAAE1G,mBAAoB;QAACnC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAA4F,QAAA,EAC9DhG,QAAQ,CAACG;MAAO;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACS,EAAA,CA1xBIP,aAAa;AAAAwO,GAAA,GAAbxO,aAAa;AA4xBnB,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAAyO,GAAA;AAAAC,YAAA,CAAA1O,EAAA;AAAA0O,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}