{"ast": null, "code": "import{styled}from'@mui/material/styles';import axios from'axios';import{API_URL}from'../config';// 隐藏的文件输入样式\nexport const VisuallyHiddenInput=styled('input')({clip:'rect(0 0 0 0)',clipPath:'inset(50%)',height:1,overflow:'hidden',position:'absolute',bottom:0,left:0,whiteSpace:'nowrap',width:1});/**\r\n * 上传Excel文件\r\n * @param {File} file - 要上传的Excel文件\r\n * @returns {Promise} - 包含上传结果的Promise\r\n */export const uploadExcelFile=async file=>{if(!file){throw new Error('请选择文件');}if(!file.name.endsWith('.xlsx')&&!file.name.endsWith('.xls')){throw new Error('只支持Excel文件 (.xlsx, .xls)');}const formData=new FormData();formData.append('file',file);try{const response=await axios.post(\"\".concat(API_URL,\"/upload\"),formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){if(error.response&&error.response.data&&error.response.data.error){throw new Error(error.response.data.error);}else{throw new Error('上传文件失败，请重试');}}};", "map": {"version": 3, "names": ["styled", "axios", "API_URL", "VisuallyHiddenInput", "clip", "clipPath", "height", "overflow", "position", "bottom", "left", "whiteSpace", "width", "uploadExcelFile", "file", "Error", "name", "endsWith", "formData", "FormData", "append", "response", "post", "concat", "headers", "data", "error"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/utils/fileUtils.js"], "sourcesContent": ["import { styled } from '@mui/material/styles';\r\nimport axios from 'axios';\r\nimport { API_URL } from '../config';\r\n\r\n// 隐藏的文件输入样式\r\nexport const VisuallyHiddenInput = styled('input')({\r\n  clip: 'rect(0 0 0 0)',\r\n  clipPath: 'inset(50%)',\r\n  height: 1,\r\n  overflow: 'hidden',\r\n  position: 'absolute',\r\n  bottom: 0,\r\n  left: 0,\r\n  whiteSpace: 'nowrap',\r\n  width: 1,\r\n});\r\n\r\n/**\r\n * 上传Excel文件\r\n * @param {File} file - 要上传的Excel文件\r\n * @returns {Promise} - 包含上传结果的Promise\r\n */\r\nexport const uploadExcelFile = async (file) => {\r\n  if (!file) {\r\n    throw new Error('请选择文件');\r\n  }\r\n\r\n  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\r\n    throw new Error('只支持Excel文件 (.xlsx, .xls)');\r\n  }\r\n\r\n  const formData = new FormData();\r\n  formData.append('file', file);\r\n\r\n  try {\r\n    const response = await axios.post(`${API_URL}/upload`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      throw new Error(error.response.data.error);\r\n    } else {\r\n      throw new Error('上传文件失败，请重试');\r\n    }\r\n  }\r\n};\r\n\r\n\r\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,sBAAsB,CAC7C,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,WAAW,CAEnC;AACA,MAAO,MAAM,CAAAC,mBAAmB,CAAGH,MAAM,CAAC,OAAO,CAAC,CAAC,CACjDI,IAAI,CAAE,eAAe,CACrBC,QAAQ,CAAE,YAAY,CACtBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,CAAC,CACPC,UAAU,CAAE,QAAQ,CACpBC,KAAK,CAAE,CACT,CAAC,CAAC,CAEF;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,eAAe,CAAG,KAAO,CAAAC,IAAI,EAAK,CAC7C,GAAI,CAACA,IAAI,CAAE,CACT,KAAM,IAAI,CAAAC,KAAK,CAAC,OAAO,CAAC,CAC1B,CAEA,GAAI,CAACD,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAI,CAACH,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC/D,KAAM,IAAI,CAAAF,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CAEA,KAAM,CAAAG,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEN,IAAI,CAAC,CAE7B,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAApB,KAAK,CAACqB,IAAI,IAAAC,MAAA,CAAIrB,OAAO,YAAWgB,QAAQ,CAAE,CAC/DM,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAH,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,GAAIA,KAAK,CAACL,QAAQ,EAAIK,KAAK,CAACL,QAAQ,CAACI,IAAI,EAAIC,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAE,CACtE,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAC,CAC5C,CAAC,IAAM,CACL,KAAM,IAAI,CAAAX,KAAK,CAAC,YAAY,CAAC,CAC/B,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}