{"ast": null, "code": "'use client';\n\nexport { default } from './Badge';\nexport { default as badgeClasses } from './badgeClasses';\nexport * from './badgeClasses';", "map": {"version": 3, "names": ["default", "badgeClasses"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/material/Badge/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Badge';\nexport { default as badgeClasses } from './badgeClasses';\nexport * from './badgeClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}