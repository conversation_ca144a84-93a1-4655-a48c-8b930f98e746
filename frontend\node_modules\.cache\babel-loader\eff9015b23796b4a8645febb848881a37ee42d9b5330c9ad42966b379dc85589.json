{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport { GridOverlay } from './containers/GridOverlay';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridLoadingOverlay = /*#__PURE__*/React.forwardRef(function GridLoadingOverlay(props, ref) {\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref\n  }, props, {\n    children: /*#__PURE__*/_jsx(CircularProgress, {})\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridLoadingOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridLoadingOverlay };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "CircularProgress", "GridOverlay", "jsx", "_jsx", "GridLoadingOverlay", "forwardRef", "props", "ref", "children", "process", "env", "NODE_ENV", "propTypes", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/GridLoadingOverlay.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport { GridOverlay } from './containers/GridOverlay';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridLoadingOverlay = /*#__PURE__*/React.forwardRef(function GridLoadingOverlay(props, ref) {\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref\n  }, props, {\n    children: /*#__PURE__*/_jsx(CircularProgress, {})\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridLoadingOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridLoadingOverlay };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,SAASD,kBAAkBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/F,OAAO,aAAaJ,IAAI,CAACF,WAAW,EAAEJ,QAAQ,CAAC;IAC7CU,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRE,QAAQ,EAAE,aAAaL,IAAI,CAACH,gBAAgB,EAAE,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,kBAAkB,CAACQ,SAAS,GAAG;EACrE;EACA;EACA;EACA;EACAC,EAAE,EAAEd,SAAS,CAACe,SAAS,CAAC,CAACf,SAAS,CAACgB,OAAO,CAAChB,SAAS,CAACe,SAAS,CAAC,CAACf,SAAS,CAACiB,IAAI,EAAEjB,SAAS,CAACkB,MAAM,EAAElB,SAAS,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAEnB,SAAS,CAACiB,IAAI,EAAEjB,SAAS,CAACkB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,SAASb,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}