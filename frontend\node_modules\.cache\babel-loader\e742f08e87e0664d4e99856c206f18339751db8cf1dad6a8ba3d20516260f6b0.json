{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Grid, Divider } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport ErrorSnackbar from './ErrorSnackbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = 'http://localhost:5000/api';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  totalCommission\n}) => {\n  _s();\n  var _gridData$find;\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n  const [pdfDialog, setPdfDialog] = useState({\n    open: false,\n    url: null,\n    error: null\n  });\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: ''\n    };\n  });\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [pdfLoading, setPdfLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [pdfUrl, setPdfUrl] = useState('');\n  const [docUrl, setDocUrl] = useState('');\n  const [docId, setDocId] = useState('');\n  const handleDownload = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch(`/api/download/${fileId}`);\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || '下载失败');\n      }\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `processed_data.xlsx`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      a.remove();\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateDocument = async () => {\n    setPdfLoading(true);\n    setError('');\n    try {\n      const response = await fetch('/api/generate-document', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          data: data,\n          totalAmount: totalCommission ? totalCommission.toFixed(2) : '0.00',\n          fileId: fileId\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || '生成文档失败');\n      }\n      const result = await response.json();\n      setDocUrl(result.docUrl);\n      setDocId(result.docId);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setPdfLoading(false);\n    }\n  };\n  const handleConvertToPdf = async () => {\n    if (!docId) {\n      setError('没有可转换的文档');\n      return;\n    }\n    setPdfLoading(true);\n    setError('');\n    try {\n      const response = await fetch('/api/convert-to-pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          docId: docId\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || '转换PDF失败');\n      }\n      const result = await response.json();\n      setPdfUrl(result.pdfUrl);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setPdfLoading(false);\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL').reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框\n  const openRemarksDialog = (rowId, currentValue) => {\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  };\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = () => {\n    setRemarksDialog({\n      ...remarksDialog,\n      open: false\n    });\n  };\n\n  // 选择REMARKS选项\n  const selectRemarkOption = option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      const updatedData = gridData.map(row => {\n        if (row.id === rowId) {\n          return {\n            ...row,\n            REMARKS: option,\n            _selected_remarks: option\n          };\n        }\n        return row;\n      });\n      setGridData(updatedData);\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  };\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = () => {\n    setAddOptionDialog(true);\n  };\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = () => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  };\n\n  // 添加新选项\n  const addNewOption = () => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      const updatedOptions = [...remarksOptions, newOption.trim()];\n      setRemarksOptions(updatedOptions);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  };\n\n  // 删除选项\n  const deleteOption = option => {\n    const updatedOptions = remarksOptions.filter(item => item !== option);\n    setRemarksOptions(updatedOptions);\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  };\n\n  // 生成PDF\n  const generatePDF = async () => {\n    try {\n      setIsGeneratingPDF(true);\n\n      // 准备数据，排除总计行\n      const pdfData = gridData.filter(row => row.NO !== 'TOTAL').map(row => ({\n        NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks || '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成PDF\n      const response = await axios.post(`${API_URL}/generate-pdf`, {\n        data: pdfData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.pdfUrl) {\n        setPdfDialog({\n          open: true,\n          url: response.data.pdfUrl,\n          error: null\n        });\n      } else {\n        throw new Error('生成PDF失败');\n      }\n    } catch (error) {\n      console.error('生成PDF出错:', error);\n      setPdfDialog({\n        open: true,\n        url: null,\n        error: '生成PDF失败，请重试'\n      });\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  // 关闭PDF对话框\n  const closePdfDialog = () => {\n    setPdfDialog({\n      ...pdfDialog,\n      open: false\n    });\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true\n  }];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n\n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        minWidth: 180,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              cursor: 'pointer',\n              '&:hover': {\n                textDecoration: 'underline',\n                color: 'primary.main'\n              }\n            },\n            onClick: () => openRemarksDialog(params.row.id, params.value || ''),\n            children: params.row._selected_remarks || '点击选择'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 24\n          }, this) : '下载Excel'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 24\n          }, this),\n          onClick: generatePDF,\n          disabled: isGeneratingPDF,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingPDF ? '生成中...' : '生成PDF'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          getRowClassName: params => params.row.isTotal ? 'total-row' : '',\n          isCellEditable: handleCellEdit,\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this), gridData.some(row => 'COMMISSION' in row) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u9879\\u76EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u91D1\\u989D (RM)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u603B\\u4F63\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: ((_gridData$find = gridData.find(row => row.NO === 'TOTAL')) === null || _gridData$find === void 0 ? void 0 : _gridData$find.COMMISSION.toFixed(2)) || '0.00',\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: pdfDialog.open,\n      onClose: closePdfDialog,\n      fullWidth: true,\n      maxWidth: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"PDF\\u9884\\u89C8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: pdfDialog.error ? /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"error\",\n          children: pdfDialog.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this) : pdfDialog.url ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%',\n            height: '70vh'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n            src: pdfDialog.url,\n            width: \"100%\",\n            height: \"100%\",\n            style: {\n              border: 'none'\n            },\n            title: \"PDF\\u9884\\u89C8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [pdfDialog.url && !pdfDialog.error && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => window.open(pdfDialog.url, '_blank'),\n          color: \"primary\",\n          children: \"\\u4E0B\\u8F7DPDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closePdfDialog,\n          children: \"\\u5173\\u95ED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ErrorSnackbar, {\n      error: error,\n      setError: setError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 497,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"C5Tt5JxuX+w3M6GbFiXgd/pxE4c=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Grid", "Divider", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "PictureAsPdfIcon", "axios", "ErrorSnackbar", "jsxDEV", "_jsxDEV", "API_URL", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "totalCommission", "_s", "_gridData$find", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingPDF", "setIsGeneratingPDF", "pdfDialog", "setPdfDialog", "open", "url", "error", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "gridData", "setGridData", "index", "id", "isTotal", "NO", "snackbar", "setSnackbar", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "loading", "setLoading", "pdfLoading", "setPdfLoading", "setError", "pdfUrl", "setPdfUrl", "docUrl", "setDocUrl", "docId", "setDocId", "handleDownload", "response", "fetch", "ok", "errorData", "json", "Error", "blob", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "remove", "err", "handleGenerateDocument", "method", "headers", "totalAmount", "toFixed", "result", "handleConvertToPdf", "handleCleanup", "delete", "console", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "undefined", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "updatedOptions", "deleteOption", "item", "generatePDF", "pdfData", "Math", "floor", "DATE", "split", "KM", "HOURS", "MAXCHECK", "AMOUNT", "post", "closePdfDialog", "length", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "columns", "col", "hasOwnProperty", "flex", "min<PERSON><PERSON><PERSON>", "renderCell", "cursor", "textDecoration", "value", "fontWeight", "Boolean", "display", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "size", "width", "overflow", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "getRowClassName", "isCellEditable", "experimentalFeatures", "newEditingApi", "backgroundColor", "some", "component", "align", "label", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "src", "style", "border", "title", "p", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Button,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Chip,\r\n  Snackbar,\r\n  Alert,\r\n  Menu,\r\n  MenuItem,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  List,\r\n  ListItem,\r\n  ListItemButton,\r\n  ListItemText,\r\n  TextField,\r\n  IconButton,\r\n  CircularProgress,\r\n  Grid,\r\n  Divider\r\n} from '@mui/material';\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport DownloadIcon from '@mui/icons-material/Download';\r\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\r\nimport axios from 'axios';\r\nimport ErrorSnackbar from './ErrorSnackbar';\r\n\r\nconst API_URL = 'http://localhost:5000/api';\r\n\r\n// 默认的REMARKS选项\r\nconst DEFAULT_REMARKS_OPTIONS = [\r\n  \"MAXCHECK ADVANCE\",\r\n  \"MAXCHECK ADVANCE PLUS\",\r\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\r\n  \"MAXCHECK BASIC\",\r\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\r\n  \"ATF REPLACEMENT\",\r\n  \"REPLACE BRAKE PADS\"\r\n];\r\n\r\nconst ResultDisplay = ({ data, fileId, onReset, totalCommission }) => {\r\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\r\n  const [remarksOptions, setRemarksOptions] = useState(() => {\r\n    const savedOptions = localStorage.getItem('remarksOptions');\r\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\r\n  });\r\n\r\n  // 新选项输入框的状态\r\n  const [newOption, setNewOption] = useState('');\r\n  // 添加新选项对话框的状态\r\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\r\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\r\n  const [pdfDialog, setPdfDialog] = useState({\r\n    open: false,\r\n    url: null,\r\n    error: null\r\n  });\r\n\r\n  // 当remarksOptions变化时，保存到localStorage\r\n  useEffect(() => {\r\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\r\n  }, [remarksOptions]);\r\n\r\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\r\n  const processedData = data.map(row => {\r\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\r\n    return { ...row, REMARKS: '', _selected_remarks: '' };\r\n  });\r\n\r\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\r\n    ...row,\r\n    id: index,\r\n    isTotal: row.NO === 'TOTAL'\r\n  })));\r\n  \r\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\r\n  const [remarksDialog, setRemarksDialog] = useState({\r\n    open: false,\r\n    rowId: null,\r\n    currentValue: ''\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [pdfLoading, setPdfLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [pdfUrl, setPdfUrl] = useState('');\r\n  const [docUrl, setDocUrl] = useState('');\r\n  const [docId, setDocId] = useState('');\r\n\r\n  const handleDownload = async () => {\r\n    setLoading(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const response = await fetch(`/api/download/${fileId}`);\r\n      \r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || '下载失败');\r\n      }\r\n      \r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `processed_data.xlsx`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      a.remove();\r\n    } catch (err) {\r\n      setError(err.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateDocument = async () => {\r\n    setPdfLoading(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const response = await fetch('/api/generate-document', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          data: data,\r\n          totalAmount: totalCommission ? totalCommission.toFixed(2) : '0.00',\r\n          fileId: fileId\r\n        }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || '生成文档失败');\r\n      }\r\n      \r\n      const result = await response.json();\r\n      setDocUrl(result.docUrl);\r\n      setDocId(result.docId);\r\n      \r\n    } catch (err) {\r\n      setError(err.message);\r\n    } finally {\r\n      setPdfLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleConvertToPdf = async () => {\r\n    if (!docId) {\r\n      setError('没有可转换的文档');\r\n      return;\r\n    }\r\n    \r\n    setPdfLoading(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const response = await fetch('/api/convert-to-pdf', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          docId: docId\r\n        }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || '转换PDF失败');\r\n      }\r\n      \r\n      const result = await response.json();\r\n      setPdfUrl(result.pdfUrl);\r\n      \r\n    } catch (err) {\r\n      setError(err.message);\r\n    } finally {\r\n      setPdfLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCleanup = async () => {\r\n    try {\r\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\r\n    } catch (error) {\r\n      console.error('清理文件出错:', error);\r\n    }\r\n    \r\n    onReset();\r\n  };\r\n\r\n  const handleCellEdit = (params) => {\r\n    // 阻止总计行被编辑\r\n    if (params.row.NO === 'TOTAL') {\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const processRowUpdate = (newRow) => {\r\n    // 更新行数据\r\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\r\n    \r\n    // 重新计算总计\r\n    if (newRow.COMMISSION !== undefined) {\r\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\r\n      if (totalRow) {\r\n        const newTotal = updatedData\r\n          .filter(row => row.NO !== 'TOTAL')\r\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\r\n        \r\n        totalRow.COMMISSION = newTotal;\r\n      }\r\n    }\r\n    \r\n    setGridData(updatedData);\r\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\r\n    return newRow;\r\n  };\r\n\r\n  const onProcessRowUpdateError = (error) => {\r\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\r\n  };\r\n\r\n  const handleCloseSnackbar = () => {\r\n    setSnackbar(prev => ({ ...prev, open: false }));\r\n  };\r\n\r\n  // 打开REMARKS选择对话框\r\n  const openRemarksDialog = (rowId, currentValue) => {\r\n    setRemarksDialog({\r\n      open: true,\r\n      rowId,\r\n      currentValue\r\n    });\r\n  };\r\n\r\n  // 关闭REMARKS选择对话框\r\n  const closeRemarksDialog = () => {\r\n    setRemarksDialog({\r\n      ...remarksDialog,\r\n      open: false\r\n    });\r\n  };\r\n\r\n  // 选择REMARKS选项\r\n  const selectRemarkOption = (option) => {\r\n    const { rowId } = remarksDialog;\r\n    if (rowId !== null) {\r\n      const updatedData = gridData.map(row => {\r\n        if (row.id === rowId) {\r\n          return { ...row, REMARKS: option, _selected_remarks: option };\r\n        }\r\n        return row;\r\n      });\r\n      setGridData(updatedData);\r\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\r\n    }\r\n    closeRemarksDialog();\r\n  };\r\n\r\n  // 打开添加新选项对话框\r\n  const openAddOptionDialog = () => {\r\n    setAddOptionDialog(true);\r\n  };\r\n\r\n  // 关闭添加新选项对话框\r\n  const closeAddOptionDialog = () => {\r\n    setAddOptionDialog(false);\r\n    setNewOption('');\r\n  };\r\n\r\n  // 添加新选项\r\n  const addNewOption = () => {\r\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\r\n      const updatedOptions = [...remarksOptions, newOption.trim()];\r\n      setRemarksOptions(updatedOptions);\r\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\r\n      closeAddOptionDialog();\r\n    } else if (remarksOptions.includes(newOption.trim())) {\r\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\r\n    }\r\n  };\r\n\r\n  // 删除选项\r\n  const deleteOption = (option) => {\r\n    const updatedOptions = remarksOptions.filter(item => item !== option);\r\n    setRemarksOptions(updatedOptions);\r\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\r\n  };\r\n\r\n  // 生成PDF\r\n  const generatePDF = async () => {\r\n    try {\r\n      setIsGeneratingPDF(true);\r\n      \r\n      // 准备数据，排除总计行\r\n      const pdfData = gridData\r\n        .filter(row => row.NO !== 'TOTAL')\r\n        .map(row => ({\r\n          NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\r\n          DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\r\n          'VEHICLE NO': row['VEHICLE NO'] || '',\r\n          'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\r\n          KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\r\n          REMARKS: row._selected_remarks || '',\r\n          HOURS: typeof row.MAXCHECK === 'number' ? \r\n            (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \r\n            row.MAXCHECK || '',\r\n          AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\r\n        }));\r\n      \r\n      // 计算总金额\r\n      const totalAmount = gridData\r\n        .filter(row => row.NO !== 'TOTAL' && row.COMMISSION)\r\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\r\n      \r\n      // 发送请求到后端生成PDF\r\n      const response = await axios.post(`${API_URL}/generate-pdf`, {\r\n        data: pdfData,\r\n        totalAmount: totalAmount.toFixed(2),\r\n        fileId: fileId\r\n      });\r\n      \r\n      if (response.data && response.data.pdfUrl) {\r\n        setPdfDialog({\r\n          open: true,\r\n          url: response.data.pdfUrl,\r\n          error: null\r\n        });\r\n      } else {\r\n        throw new Error('生成PDF失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('生成PDF出错:', error);\r\n      setPdfDialog({\r\n        open: true,\r\n        url: null,\r\n        error: '生成PDF失败，请重试'\r\n      });\r\n    } finally {\r\n      setIsGeneratingPDF(false);\r\n    }\r\n  };\r\n  \r\n  // 关闭PDF对话框\r\n  const closePdfDialog = () => {\r\n    setPdfDialog({\r\n      ...pdfDialog,\r\n      open: false\r\n    });\r\n  };\r\n  \r\n  // 如果数据为空\r\n  if (!gridData || gridData.length === 0) {\r\n    return (\r\n      <Box sx={{ textAlign: 'center', py: 3 }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">\r\n          没有找到数据\r\n        </Typography>\r\n        <Button \r\n          variant=\"contained\" \r\n          onClick={onReset}\r\n          sx={{ mt: 2 }}\r\n        >\r\n          重新开始\r\n        </Button>\r\n      </Box>\r\n    );\r\n  }\r\n  \r\n  // 定义列的显示顺序和标题\r\n  const columnOrder = [\r\n    { field: 'NO', headerName: 'NO', editable: true },\r\n    { field: 'DATE', headerName: 'DATE', editable: true },\r\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true },\r\n    { field: 'RO NO', headerName: 'RO NO', editable: true },\r\n    { field: 'KM', headerName: 'KM', editable: true },\r\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false },  // 新添加的REMARKS列\r\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true },\r\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true }\r\n  ];\r\n  \r\n  // 过滤和排序列\r\n  const columns = columnOrder.map(col => {\r\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\r\n      // 如果COMMISSION列不存在，跳过\r\n      return null;\r\n    }\r\n    \r\n    // 特殊处理REMARKS列\r\n    if (col.field === 'REMARKS') {\r\n      return {\r\n        field: col.field,\r\n        headerName: col.headerName,\r\n        flex: 1.5,\r\n        minWidth: 180,\r\n        editable: false,\r\n        renderCell: (params) => {\r\n          // 总计行不显示REMARKS选项\r\n          if (params.row.NO === 'TOTAL') {\r\n            return '';\r\n          }\r\n          \r\n          return (\r\n            <Box \r\n              sx={{ \r\n                cursor: 'pointer',\r\n                '&:hover': {\r\n                  textDecoration: 'underline',\r\n                  color: 'primary.main'\r\n                }\r\n              }}\r\n              onClick={() => openRemarksDialog(params.row.id, params.value || '')}\r\n            >\r\n              {params.row._selected_remarks || '点击选择'}\r\n            </Box>\r\n          );\r\n        }\r\n      };\r\n    }\r\n    \r\n    return {\r\n      field: col.field,\r\n      headerName: col.headerName,\r\n      flex: 1,\r\n      minWidth: 120,\r\n      editable: col.editable,\r\n      renderCell: (params) => {\r\n        // 特殊处理总计行\r\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\r\n          return (\r\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\r\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\r\n            </Typography>\r\n          );\r\n        }\r\n        \r\n        // 处理日期格式\r\n        if (col.field === 'DATE' && params.value) {\r\n          return params.value.split('T')[0]; // 只显示日期部分\r\n        }\r\n        \r\n        // NO列不显示浮点数\r\n        if (col.field === 'NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // RO NO列不显示浮点数\r\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // KM列不显示浮点数\r\n        if (col.field === 'KM' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\r\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\r\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\r\n        }\r\n        \r\n        // COMMISSION(AMOUNT)列保留2位小数\r\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\r\n          return params.value.toFixed(2);\r\n        }\r\n        \r\n        // 其他数字格式\r\n        if (typeof params.value === 'number') {\r\n          return params.value;\r\n        }\r\n        \r\n        return params.value;\r\n      }\r\n    };\r\n  }).filter(Boolean); // 过滤掉null值\r\n  \r\n  return (\r\n    <Box>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          处理结果\r\n        </Typography>\r\n        \r\n        <Box>\r\n          <Button \r\n            variant=\"contained\" \r\n            startIcon={<DownloadIcon />}\r\n            onClick={handleDownload}\r\n            sx={{ mr: 1 }}\r\n            disabled={loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} /> : '下载Excel'}\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"contained\"\r\n            color=\"secondary\"\r\n            startIcon={<PictureAsPdfIcon />}\r\n            onClick={generatePDF}\r\n            disabled={isGeneratingPDF}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            {isGeneratingPDF ? '生成中...' : '生成PDF'}\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"outlined\" \r\n            startIcon={<RestartAltIcon />}\r\n            onClick={handleCleanup}\r\n          >\r\n            重新开始\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n      \r\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n        <Box sx={{ height: 400, width: '100%' }}>\r\n          <DataGrid\r\n            rows={gridData}\r\n            columns={columns}\r\n            pageSize={10}\r\n            rowsPerPageOptions={[10, 25, 50]}\r\n            disableSelectionOnClick\r\n            getRowClassName={(params) => params.row.isTotal ? 'total-row' : ''}\r\n            isCellEditable={handleCellEdit}\r\n            processRowUpdate={processRowUpdate}\r\n            onProcessRowUpdateError={onProcessRowUpdateError}\r\n            experimentalFeatures={{ newEditingApi: true }}\r\n            sx={{\r\n              '& .total-row': {\r\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\r\n                fontWeight: 'bold',\r\n              },\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n      \r\n      {gridData.some(row => 'COMMISSION' in row) && (\r\n        <Box sx={{ mt: 3 }}>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            佣金计算结果\r\n          </Typography>\r\n          \r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>项目</TableCell>\r\n                  <TableCell align=\"right\">金额 (RM)</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell>总佣金</TableCell>\r\n                  <TableCell align=\"right\">\r\n                    <Chip \r\n                      label={gridData.find(row => row.NO === 'TOTAL')?.COMMISSION.toFixed(2) || '0.00'} \r\n                      color=\"primary\" \r\n                      variant=\"outlined\"\r\n                    />\r\n                  </TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n        </Box>\r\n      )}\r\n      \r\n      {/* REMARKS选择对话框 */}\r\n      <Dialog \r\n        open={remarksDialog.open} \r\n        onClose={closeRemarksDialog}\r\n        fullWidth\r\n        maxWidth=\"xs\"\r\n      >\r\n        <DialogTitle>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n            <Typography variant=\"h6\">选择REMARKS</Typography>\r\n            <Button \r\n              startIcon={<AddIcon />}\r\n              onClick={openAddOptionDialog}\r\n              color=\"primary\"\r\n            >\r\n              添加选项\r\n            </Button>\r\n          </Box>\r\n        </DialogTitle>\r\n        <DialogContent dividers>\r\n          <List>\r\n            {remarksOptions.map((option) => (\r\n              <ListItem \r\n                key={option} \r\n                disablePadding\r\n                secondaryAction={\r\n                  <IconButton \r\n                    edge=\"end\" \r\n                    aria-label=\"delete\"\r\n                    onClick={() => deleteOption(option)}\r\n                  >\r\n                    <DeleteIcon />\r\n                  </IconButton>\r\n                }\r\n              >\r\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\r\n                  <ListItemText primary={option} />\r\n                </ListItemButton>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeRemarksDialog}>取消</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* 添加新选项对话框 */}\r\n      <Dialog\r\n        open={addOptionDialog}\r\n        onClose={closeAddOptionDialog}\r\n        fullWidth\r\n        maxWidth=\"xs\"\r\n      >\r\n        <DialogTitle>添加新选项</DialogTitle>\r\n        <DialogContent>\r\n          <TextField\r\n            autoFocus\r\n            margin=\"dense\"\r\n            id=\"name\"\r\n            label=\"选项名称\"\r\n            type=\"text\"\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            value={newOption}\r\n            onChange={(e) => setNewOption(e.target.value)}\r\n          />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeAddOptionDialog}>取消</Button>\r\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      \r\n      {/* PDF预览对话框 */}\r\n      <Dialog\r\n        open={pdfDialog.open}\r\n        onClose={closePdfDialog}\r\n        fullWidth\r\n        maxWidth=\"md\"\r\n      >\r\n        <DialogTitle>PDF预览</DialogTitle>\r\n        <DialogContent>\r\n          {pdfDialog.error ? (\r\n            <Typography color=\"error\">{pdfDialog.error}</Typography>\r\n          ) : pdfDialog.url ? (\r\n            <Box sx={{ width: '100%', height: '70vh' }}>\r\n              <iframe \r\n                src={pdfDialog.url} \r\n                width=\"100%\" \r\n                height=\"100%\" \r\n                style={{ border: 'none' }}\r\n                title=\"PDF预览\"\r\n              />\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\r\n              <CircularProgress />\r\n            </Box>\r\n          )}\r\n        </DialogContent>\r\n        <DialogActions>\r\n          {pdfDialog.url && !pdfDialog.error && (\r\n            <Button \r\n              onClick={() => window.open(pdfDialog.url, '_blank')}\r\n              color=\"primary\"\r\n            >\r\n              下载PDF\r\n            </Button>\r\n          )}\r\n          <Button onClick={closePdfDialog}>关闭</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      \r\n      <Snackbar\r\n        open={snackbar.open}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSnackbar}\r\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n      >\r\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\r\n          {snackbar.message}\r\n        </Alert>\r\n      </Snackbar>\r\n      \r\n      <ErrorSnackbar error={error} setError={setError} />\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,CACrB;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EACpE;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IACzD,MAAMmD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGV,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8D,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC;IACzCgE,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACAjE,SAAS,CAAC,MAAM;IACdmD,YAAY,CAACe,OAAO,CAAC,gBAAgB,EAAEb,IAAI,CAACc,SAAS,CAACnB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoB,aAAa,GAAG1B,IAAI,CAAC2B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE;IAAG,CAAC;EACvD,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAACqE,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEK,KAAK,MAAM;IAC1E,GAAGL,GAAG;IACNM,EAAE,EAAED,KAAK;IACTE,OAAO,EAAEP,GAAG,CAACQ,EAAE,KAAK;EACtB,CAAC,CAAC,CAAC,CAAC;EAEJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IAAEgE,IAAI,EAAE,KAAK;IAAEkB,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC;IACjDgE,IAAI,EAAE,KAAK;IACXsB,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0F,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkE,KAAK,EAAE0B,QAAQ,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6F,MAAM,EAAEC,SAAS,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+F,MAAM,EAAEC,SAAS,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiG,KAAK,EAAEC,QAAQ,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMmG,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCV,UAAU,CAAC,IAAI,CAAC;IAChBG,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiBzD,MAAM,EAAE,CAAC;MAEvD,IAAI,CAACwD,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACrC,KAAK,IAAI,MAAM,CAAC;MAC5C;MAEA,MAAMwC,IAAI,GAAG,MAAMN,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAMzC,GAAG,GAAG0C,MAAM,CAACC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MAC5C,MAAMI,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGhD,GAAG;MACZ6C,CAAC,CAACI,QAAQ,GAAG,qBAAqB;MAClCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACrD,GAAG,CAAC;MAC/B6C,CAAC,CAACS,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ5B,QAAQ,CAAC4B,GAAG,CAACtC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC9B,aAAa,CAAC,IAAI,CAAC;IACnBC,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,EAAE;QACrDqB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDR,IAAI,EAAE7D,IAAI,CAACc,SAAS,CAAC;UACnBzB,IAAI,EAAEA,IAAI;UACViF,WAAW,EAAE9E,eAAe,GAAGA,eAAe,CAAC+E,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM;UAClEjF,MAAM,EAAEA;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACwD,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACrC,KAAK,IAAI,QAAQ,CAAC;MAC9C;MAEA,MAAM4D,MAAM,GAAG,MAAM1B,QAAQ,CAACI,IAAI,CAAC,CAAC;MACpCR,SAAS,CAAC8B,MAAM,CAAC/B,MAAM,CAAC;MACxBG,QAAQ,CAAC4B,MAAM,CAAC7B,KAAK,CAAC;IAExB,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZ5B,QAAQ,CAAC4B,GAAG,CAACtC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRS,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAC9B,KAAK,EAAE;MACVL,QAAQ,CAAC,UAAU,CAAC;MACpB;IACF;IAEAD,aAAa,CAAC,IAAI,CAAC;IACnBC,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB,EAAE;QAClDqB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDR,IAAI,EAAE7D,IAAI,CAACc,SAAS,CAAC;UACnB6B,KAAK,EAAEA;QACT,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACrC,KAAK,IAAI,SAAS,CAAC;MAC/C;MAEA,MAAM4D,MAAM,GAAG,MAAM1B,QAAQ,CAACI,IAAI,CAAC,CAAC;MACpCV,SAAS,CAACgC,MAAM,CAACjC,MAAM,CAAC;IAE1B,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ5B,QAAQ,CAAC4B,GAAG,CAACtC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRS,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM5F,KAAK,CAAC6F,MAAM,CAAC,GAAGzF,OAAO,YAAYI,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdgE,OAAO,CAAChE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEArB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMsF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC7D,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMsD,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACA,MAAMC,WAAW,GAAG7D,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACM,EAAE,KAAKyD,MAAM,CAACzD,EAAE,GAAGyD,MAAM,GAAG/D,GAAI,CAAC;;IAE9E;IACA,IAAI+D,MAAM,CAACE,UAAU,KAAKC,SAAS,EAAE;MACnC,MAAMC,QAAQ,GAAGH,WAAW,CAACI,IAAI,CAACpE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAI2D,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGL,WAAW,CACzBM,MAAM,CAACtE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjC+D,MAAM,CAAC,CAACC,GAAG,EAAExE,GAAG,KAAKwE,GAAG,IAAIxE,GAAG,CAACiE,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDE,QAAQ,CAACF,UAAU,GAAGI,QAAQ;MAChC;IACF;IAEAjE,WAAW,CAAC4D,WAAW,CAAC;IACxBtD,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOmD,MAAM;EACf,CAAC;EAED,MAAMU,uBAAuB,GAAI9E,KAAK,IAAK;IACzCe,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,SAAShB,KAAK,CAACgB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAM8D,mBAAmB,GAAGA,CAAA,KAAM;IAChChE,WAAW,CAACiE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElF,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMmF,iBAAiB,GAAGA,CAAC7D,KAAK,EAAEC,YAAY,KAAK;IACjDF,gBAAgB,CAAC;MACfrB,IAAI,EAAE,IAAI;MACVsB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM6D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/D,gBAAgB,CAAC;MACf,GAAGD,aAAa;MAChBpB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqF,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAM;MAAEhE;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMiD,WAAW,GAAG7D,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACM,EAAE,KAAKS,KAAK,EAAE;UACpB,OAAO;YAAE,GAAGf,GAAG;YAAEC,OAAO,EAAE8E,MAAM;YAAE7E,iBAAiB,EAAE6E;UAAO,CAAC;QAC/D;QACA,OAAO/E,GAAG;MACZ,CAAC,CAAC;MACFI,WAAW,CAAC4D,WAAW,CAAC;MACxBtD,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAiE,kBAAkB,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC5F,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM6F,oBAAoB,GAAGA,CAAA,KAAM;IACjC7F,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;;EAED;EACA,MAAMgG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjG,SAAS,CAACkG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACzG,cAAc,CAAC0G,QAAQ,CAACnG,SAAS,CAACkG,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE,MAAME,cAAc,GAAG,CAAC,GAAG3G,cAAc,EAAEO,SAAS,CAACkG,IAAI,CAAC,CAAC,CAAC;MAC5DxG,iBAAiB,CAAC0G,cAAc,CAAC;MACjC3E,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnEqE,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIvG,cAAc,CAAC0G,QAAQ,CAACnG,SAAS,CAACkG,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDzE,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAM0E,YAAY,GAAIP,MAAM,IAAK;IAC/B,MAAMM,cAAc,GAAG3G,cAAc,CAAC4F,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC;IACrEpG,iBAAiB,CAAC0G,cAAc,CAAC;IACjC3E,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM4E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFlG,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,MAAMmG,OAAO,GAAGtF,QAAQ,CACrBmE,MAAM,CAACtE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjCT,GAAG,CAACC,GAAG,KAAK;QACXQ,EAAE,EAAE,OAAOR,GAAG,CAACQ,EAAE,KAAK,QAAQ,GAAGkF,IAAI,CAACC,KAAK,CAAC3F,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAACQ,EAAE;QAC5DoF,IAAI,EAAE5F,GAAG,CAAC4F,IAAI,GAAI,OAAO5F,GAAG,CAAC4F,IAAI,KAAK,QAAQ,IAAI5F,GAAG,CAAC4F,IAAI,CAACR,QAAQ,CAAC,GAAG,CAAC,GAAGpF,GAAG,CAAC4F,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG7F,GAAG,CAAC4F,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE5F,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG0F,IAAI,CAACC,KAAK,CAAC3F,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF8F,EAAE,EAAE,OAAO9F,GAAG,CAAC8F,EAAE,KAAK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAAC3F,GAAG,CAAC8F,EAAE,CAAC,GAAG9F,GAAG,CAAC8F,EAAE,IAAI,EAAE;QAClE7F,OAAO,EAAED,GAAG,CAACE,iBAAiB,IAAI,EAAE;QACpC6F,KAAK,EAAE,OAAO/F,GAAG,CAACgG,QAAQ,KAAK,QAAQ,GACpChG,GAAG,CAACgG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGhG,GAAG,CAACgG,QAAQ,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAGtD,GAAG,CAACgG,QAAQ,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAC3EtD,GAAG,CAACgG,QAAQ,IAAI,EAAE;QACpBC,MAAM,EAAE,OAAOjG,GAAG,CAACiE,UAAU,KAAK,QAAQ,GAAGjE,GAAG,CAACiE,UAAU,CAACX,OAAO,CAAC,CAAC,CAAC,GAAGtD,GAAG,CAACiE,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEL;MACA,MAAMZ,WAAW,GAAGlD,QAAQ,CACzBmE,MAAM,CAACtE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAIR,GAAG,CAACiE,UAAU,CAAC,CACnDM,MAAM,CAAC,CAACC,GAAG,EAAExE,GAAG,KAAKwE,GAAG,IAAI,OAAOxE,GAAG,CAACiE,UAAU,KAAK,QAAQ,GAAGjE,GAAG,CAACiE,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMpC,QAAQ,GAAG,MAAMhE,KAAK,CAACqI,IAAI,CAAC,GAAGjI,OAAO,eAAe,EAAE;QAC3DG,IAAI,EAAEqH,OAAO;QACbpC,WAAW,EAAEA,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC;QACnCjF,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIwD,QAAQ,CAACzD,IAAI,IAAIyD,QAAQ,CAACzD,IAAI,CAACkD,MAAM,EAAE;QACzC9B,YAAY,CAAC;UACXC,IAAI,EAAE,IAAI;UACVC,GAAG,EAAEmC,QAAQ,CAACzD,IAAI,CAACkD,MAAM;UACzB3B,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIuC,KAAK,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdgE,OAAO,CAAChE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCH,YAAY,CAAC;QACXC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE,IAAI;QACTC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,SAAS;MACRL,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM6G,cAAc,GAAGA,CAAA,KAAM;IAC3B3G,YAAY,CAAC;MACX,GAAGD,SAAS;MACZE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAI,CAACU,QAAQ,IAAIA,QAAQ,CAACiG,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEpI,OAAA,CAACrC,GAAG;MAAC0K,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtCxI,OAAA,CAACpC,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9I,OAAA,CAACnC,MAAM;QACL4K,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAEzI,OAAQ;QACjB+H,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAAG;EAC/D;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9D;;EAED;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAAClH,GAAG,CAACuH,GAAG,IAAI;IACrC,IAAI,CAACnH,QAAQ,CAAC,CAAC,CAAC,CAACoH,cAAc,CAACD,GAAG,CAACJ,KAAK,CAAC,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,IAAII,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;MACnG;MACA,OAAO,IAAI;IACb;;IAEA;IACA,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEI,GAAG,CAACJ,KAAK;QAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;QAC1BK,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,GAAG;QACbL,QAAQ,EAAE,KAAK;QACfM,UAAU,EAAG7D,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAAC7D,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;UAEA,oBACExC,OAAA,CAACrC,GAAG;YACF0K,EAAE,EAAE;cACFsB,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTC,cAAc,EAAE,WAAW;gBAC3BlB,KAAK,EAAE;cACT;YACF,CAAE;YACFK,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAACf,MAAM,CAAC7D,GAAG,CAACM,EAAE,EAAEuD,MAAM,CAACgE,KAAK,IAAI,EAAE,CAAE;YAAArB,QAAA,EAEnE3C,MAAM,CAAC7D,GAAG,CAACE,iBAAiB,IAAI;UAAM;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAEV;MACF,CAAC;IACH;IAEA,OAAO;MACLI,KAAK,EAAEI,GAAG,CAACJ,KAAK;MAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;MAC1BK,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbL,QAAQ,EAAEE,GAAG,CAACF,QAAQ;MACtBM,UAAU,EAAG7D,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAAC7D,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAI8G,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACElJ,OAAA,CAACpC,UAAU;YAAC6K,OAAO,EAAC,OAAO;YAACqB,UAAU,EAAC,MAAM;YAACpB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAO3C,MAAM,CAACgE,KAAK,KAAK,QAAQ,GAAGhE,MAAM,CAACgE,KAAK,CAACvE,OAAO,CAAC,CAAC,CAAC,GAAGO,MAAM,CAACgE;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIQ,GAAG,CAACJ,KAAK,KAAK,MAAM,IAAIrD,MAAM,CAACgE,KAAK,EAAE;UACxC,OAAOhE,MAAM,CAACgE,KAAK,CAAChC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAIyB,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAOrD,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACgE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,OAAO,IAAI,OAAOrD,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOnC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACgE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAOrD,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOnC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACgE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,UAAU,IAAI,OAAOrD,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOhE,MAAM,CAACgE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGhE,MAAM,CAACgE,KAAK,CAACvE,OAAO,CAAC,CAAC,CAAC,GAAGO,MAAM,CAACgE,KAAK,CAACvE,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAIgE,GAAG,CAACJ,KAAK,KAAK,YAAY,IAAI,OAAOrD,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOhE,MAAM,CAACgE,KAAK,CAACvE,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAOO,MAAM,CAACgE,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOhE,MAAM,CAACgE,KAAK;QACrB;QAEA,OAAOhE,MAAM,CAACgE,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACvD,MAAM,CAACyD,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACE/J,OAAA,CAACrC,GAAG;IAAA6K,QAAA,gBACFxI,OAAA,CAACrC,GAAG;MAAC0K,EAAE,EAAE;QAAE2B,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBACzFxI,OAAA,CAACpC,UAAU;QAAC6K,OAAO,EAAC,IAAI;QAAC2B,YAAY;QAAA5B,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9I,OAAA,CAACrC,GAAG;QAAA6K,QAAA,gBACFxI,OAAA,CAACnC,MAAM;UACL4K,OAAO,EAAC,WAAW;UACnB4B,SAAS,eAAErK,OAAA,CAACR,YAAY;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAEnF,cAAe;UACxByE,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UACdC,QAAQ,EAAEtH,OAAQ;UAAAuF,QAAA,EAEjBvF,OAAO,gBAAGjD,OAAA,CAACZ,gBAAgB;YAACoL,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAET9I,OAAA,CAACnC,MAAM;UACL4K,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,WAAW;UACjB2B,SAAS,eAAErK,OAAA,CAACJ,gBAAgB;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCC,OAAO,EAAEvB,WAAY;UACrB+C,QAAQ,EAAElJ,eAAgB;UAC1BgH,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EAEbnH,eAAe,GAAG,QAAQ,GAAG;QAAO;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAET9I,OAAA,CAACnC,MAAM;UACL4K,OAAO,EAAC,UAAU;UAClB4B,SAAS,eAAErK,OAAA,CAACP,cAAc;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAEtD,aAAc;UAAA+C,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9I,OAAA,CAAClC,KAAK;MAACuK,EAAE,EAAE;QAAEoC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAlC,QAAA,eAC/CxI,OAAA,CAACrC,GAAG;QAAC0K,EAAE,EAAE;UAAEsC,MAAM,EAAE,GAAG;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAjC,QAAA,eACtCxI,OAAA,CAACT,QAAQ;UACPqL,IAAI,EAAEzI,QAAS;UACfkH,OAAO,EAAEA,OAAQ;UACjBwB,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,eAAe,EAAGnF,MAAM,IAAKA,MAAM,CAAC7D,GAAG,CAACO,OAAO,GAAG,WAAW,GAAG,EAAG;UACnE0I,cAAc,EAAErF,cAAe;UAC/BE,gBAAgB,EAAEA,gBAAiB;UACnCW,uBAAuB,EAAEA,uBAAwB;UACjDyE,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9C9C,EAAE,EAAE;YACF,cAAc,EAAE;cACd+C,eAAe,EAAE,0BAA0B;cAC3CtB,UAAU,EAAE;YACd;UACF;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEP3G,QAAQ,CAACkJ,IAAI,CAACrJ,GAAG,IAAI,YAAY,IAAIA,GAAG,CAAC,iBACxChC,OAAA,CAACrC,GAAG;MAAC0K,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACjBxI,OAAA,CAACpC,UAAU;QAAC6K,OAAO,EAAC,WAAW;QAAC2B,YAAY;QAAA5B,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9I,OAAA,CAAC9B,cAAc;QAACoN,SAAS,EAAExN,KAAM;QAAA0K,QAAA,eAC/BxI,OAAA,CAACjC,KAAK;UAAAyK,QAAA,gBACJxI,OAAA,CAAC7B,SAAS;YAAAqK,QAAA,eACRxI,OAAA,CAAC5B,QAAQ;cAAAoK,QAAA,gBACPxI,OAAA,CAAC/B,SAAS;gBAAAuK,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzB9I,OAAA,CAAC/B,SAAS;gBAACsN,KAAK,EAAC,OAAO;gBAAA/C,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ9I,OAAA,CAAChC,SAAS;YAAAwK,QAAA,eACRxI,OAAA,CAAC5B,QAAQ;cAAAoK,QAAA,gBACPxI,OAAA,CAAC/B,SAAS;gBAAAuK,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1B9I,OAAA,CAAC/B,SAAS;gBAACsN,KAAK,EAAC,OAAO;gBAAA/C,QAAA,eACtBxI,OAAA,CAAC3B,IAAI;kBACHmN,KAAK,EAAE,EAAA/K,cAAA,GAAA0B,QAAQ,CAACiE,IAAI,CAACpE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,cAAA/B,cAAA,uBAAxCA,cAAA,CAA0CwF,UAAU,CAACX,OAAO,CAAC,CAAC,CAAC,KAAI,MAAO;kBACjFoD,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGD9I,OAAA,CAACtB,MAAM;MACL+C,IAAI,EAAEoB,aAAa,CAACpB,IAAK;MACzBgK,OAAO,EAAE5E,kBAAmB;MAC5B6E,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAnD,QAAA,gBAEbxI,OAAA,CAACrB,WAAW;QAAA6J,QAAA,eACVxI,OAAA,CAACrC,GAAG;UAAC0K,EAAE,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA1B,QAAA,gBAClFxI,OAAA,CAACpC,UAAU;YAAC6K,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C9I,OAAA,CAACnC,MAAM;YACLwM,SAAS,eAAErK,OAAA,CAACN,OAAO;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBC,OAAO,EAAE/B,mBAAoB;YAC7B0B,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9I,OAAA,CAACpB,aAAa;QAACgN,QAAQ;QAAApD,QAAA,eACrBxI,OAAA,CAAClB,IAAI;UAAA0J,QAAA,EACF9H,cAAc,CAACqB,GAAG,CAAEgF,MAAM,iBACzB/G,OAAA,CAACjB,QAAQ;YAEP8M,cAAc;YACdC,eAAe,eACb9L,OAAA,CAACb,UAAU;cACT4M,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnBhD,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAACP,MAAM,CAAE;cAAAyB,QAAA,eAEpCxI,OAAA,CAACL,UAAU;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAN,QAAA,eAEDxI,OAAA,CAAChB,cAAc;cAAC+J,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAACC,MAAM,CAAE;cAAAyB,QAAA,eACxDxI,OAAA,CAACf,YAAY;gBAAC+M,OAAO,EAAEjF;cAAO;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZ/B,MAAM;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB9I,OAAA,CAACnB,aAAa;QAAA2J,QAAA,eACZxI,OAAA,CAACnC,MAAM;UAACkL,OAAO,EAAElC,kBAAmB;UAAA2B,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9I,OAAA,CAACtB,MAAM;MACL+C,IAAI,EAAEN,eAAgB;MACtBsK,OAAO,EAAExE,oBAAqB;MAC9ByE,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAnD,QAAA,gBAEbxI,OAAA,CAACrB,WAAW;QAAA6J,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC9I,OAAA,CAACpB,aAAa;QAAA4J,QAAA,eACZxI,OAAA,CAACd,SAAS;UACR+M,SAAS;UACTC,MAAM,EAAC,OAAO;UACd5J,EAAE,EAAC,MAAM;UACTkJ,KAAK,EAAC,0BAAM;UACZW,IAAI,EAAC,MAAM;UACXT,SAAS;UACTjD,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAE5I,SAAU;UACjBmL,QAAQ,EAAGC,CAAC,IAAKnL,YAAY,CAACmL,CAAC,CAACC,MAAM,CAACzC,KAAK;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB9I,OAAA,CAACnB,aAAa;QAAA2J,QAAA,gBACZxI,OAAA,CAACnC,MAAM;UAACkL,OAAO,EAAE9B,oBAAqB;UAAAuB,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD9I,OAAA,CAACnC,MAAM;UAACkL,OAAO,EAAE7B,YAAa;UAACwB,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9I,OAAA,CAACtB,MAAM;MACL+C,IAAI,EAAEF,SAAS,CAACE,IAAK;MACrBgK,OAAO,EAAEtD,cAAe;MACxBuD,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAnD,QAAA,gBAEbxI,OAAA,CAACrB,WAAW;QAAA6J,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC9I,OAAA,CAACpB,aAAa;QAAA4J,QAAA,EACXjH,SAAS,CAACI,KAAK,gBACd3B,OAAA,CAACpC,UAAU;UAAC8K,KAAK,EAAC,OAAO;UAAAF,QAAA,EAAEjH,SAAS,CAACI;QAAK;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,GACtDvH,SAAS,CAACG,GAAG,gBACf1B,OAAA,CAACrC,GAAG;UAAC0K,EAAE,EAAE;YAAEoC,KAAK,EAAE,MAAM;YAAEE,MAAM,EAAE;UAAO,CAAE;UAAAnC,QAAA,eACzCxI,OAAA;YACEuM,GAAG,EAAEhL,SAAS,CAACG,GAAI;YACnB+I,KAAK,EAAC,MAAM;YACZE,MAAM,EAAC,MAAM;YACb6B,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAE;YAC1BC,KAAK,EAAC;UAAO;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN9I,OAAA,CAACrC,GAAG;UAAC0K,EAAE,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAE0C,CAAC,EAAE;UAAE,CAAE;UAAAnE,QAAA,eAC3DxI,OAAA,CAACZ,gBAAgB;YAAAuJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB9I,OAAA,CAACnB,aAAa;QAAA2J,QAAA,GACXjH,SAAS,CAACG,GAAG,IAAI,CAACH,SAAS,CAACI,KAAK,iBAChC3B,OAAA,CAACnC,MAAM;UACLkL,OAAO,EAAEA,CAAA,KAAM3E,MAAM,CAAC3C,IAAI,CAACF,SAAS,CAACG,GAAG,EAAE,QAAQ,CAAE;UACpDgH,KAAK,EAAC,SAAS;UAAAF,QAAA,EAChB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACD9I,OAAA,CAACnC,MAAM;UAACkL,OAAO,EAAEZ,cAAe;UAAAK,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET9I,OAAA,CAAC1B,QAAQ;MACPmD,IAAI,EAAEgB,QAAQ,CAAChB,IAAK;MACpBmL,gBAAgB,EAAE,IAAK;MACvBnB,OAAO,EAAE/E,mBAAoB;MAC7BmG,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAvE,QAAA,eAE1DxI,OAAA,CAACzB,KAAK;QAACkN,OAAO,EAAE/E,mBAAoB;QAAC9D,QAAQ,EAAEH,QAAQ,CAACG,QAAS;QAAA4F,QAAA,EAC9D/F,QAAQ,CAACE;MAAO;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEX9I,OAAA,CAACF,aAAa;MAAC6B,KAAK,EAAEA,KAAM;MAAC0B,QAAQ,EAAEA;IAAS;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC;AAEV,CAAC;AAACtI,EAAA,CAvpBIL,aAAa;AAAA6M,EAAA,GAAb7M,aAAa;AAypBnB,eAAeA,aAAa;AAAC,IAAA6M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}