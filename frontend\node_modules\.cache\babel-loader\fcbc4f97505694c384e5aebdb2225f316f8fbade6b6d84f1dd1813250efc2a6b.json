{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 959,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1009,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 977,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1023,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1105,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1100,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1099,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1113,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1066,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1062,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1061,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          paginationModel: paginationModel,\n          onPaginationModelChange: setPaginationModel,\n          pageSizeOptions: [25, 50, 100],\n          pagination: true,\n          paginationMode: \"client\",\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          disableVirtualization: false,\n          rowHeight: 48,\n          density: \"compact\",\n          rowBuffer: 5,\n          columnBuffer: 2,\n          disableColumnMenu: true,\n          disableColumnFilter: true,\n          disableColumnSelector: true,\n          disableDensitySelector: true,\n          hideFooterSelectedRowCount: true,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, `datagrid-${paginationModel.pageSize}-${memoGridData.length}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1279,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1288,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1274,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1302,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 975,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"cSB/R9yFg2qkUuKwTIOTCBw/Ug4=\");\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "axios", "API_URL", "FixedSizeList", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "children", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "timer", "setPaginationModel", "prev", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "setItem", "toString", "console", "log", "originalData", "setOriginalData", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "totalValue", "updatedData", "onProcessRowUpdateError", "message", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "showToast", "deleteOption", "item", "handleRemoveRow", "noCounter", "for<PERSON>ach", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "newData", "splice", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "backgroundColor", "startIcon", "fontWeight", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "memoGridData", "dataLength", "textAlign", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "rows", "onPaginationModelChange", "pageSizeOptions", "pagination", "paginationMode", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "disableVirtualization", "rowHeight", "density", "<PERSON><PERSON><PERSON><PERSON>", "columnBuffer", "disableColumnMenu", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnSelector", "disableDensitySelector", "hideFooterSelectedRowCount", "getRowClassName", "isCellEditable", "colDef", "padding", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "type", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\n\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    console.error('更新失败:', error.message);\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              key={`datagrid-${paginationModel.pageSize}-${memoGridData.length}`}\n              rows={memoGridData}\n              columns={columns}\n              paginationModel={paginationModel}\n              onPaginationModelChange={setPaginationModel}\n              pageSizeOptions={[25, 50, 100]}\n              pagination\n              paginationMode=\"client\"\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              disableVirtualization={false}\n              rowHeight={48}\n              density=\"compact\"\n              rowBuffer={5}\n              columnBuffer={2}\n              disableColumnMenu\n              disableColumnFilter\n              disableColumnSelector\n              disableDensitySelector\n              hideFooterSelectedRowCount\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;;AAG5C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAAC1B,KAAK;IAAA,GAAKsC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAG1E,KAAK,CAAC2E,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGjF,QAAQ,CAAC;IAErC6E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA7E,SAAS,CAAC,MAAM;IACdgF,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC9C,MAAM;IAELwE,OAAO,EAAEG,WAAY;IACrBE,OAAO,EAAEJ,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDO,KAAK,EAAC,SAAS;IACfC,IAAI,EAAC,OAAO;IACZC,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,sBAAsB;MAClCC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EAEDlB,OAAO,CAACH,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAAC6B,GAAA,GA5CG3B,UAAU;AA8ChB,MAAM4B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlH,QAAQ,CAAC,MAAM;IACzD,MAAMmH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGpD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAGzH,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC0H,eAAe,EAAEC,kBAAkB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC8H,UAAU,EAAEC,aAAa,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACkI,YAAY,EAAEC,eAAe,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmI,KAAK,GAAGtE,UAAU,CAAC,MAAM;MAC7BmE,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMjE,YAAY,CAACuE,KAAK,CAAC;EAClC,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACA7H,SAAS,CAAC,MAAM;IACdoI,kBAAkB,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAACP,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0I,UAAU,EAAEC,aAAa,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC4I,eAAe,EAAEP,kBAAkB,CAAC,GAAGrI,QAAQ,CAAC,MAAM;IAC3D,MAAM6I,KAAK,GAAGzB,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLkB,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACA5I,SAAS,CAAC,MAAM;IACdmH,YAAY,CAAC4B,OAAO,CAAC,kBAAkB,EAAEJ,eAAe,CAACE,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC;IAC7EC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAI9B;EACA,MAAM,CAACM,YAAY,EAAEC,eAAe,CAAC,GAAGrJ,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdmH,YAAY,CAAC4B,OAAO,CAAC,gBAAgB,EAAE1B,IAAI,CAACgC,SAAS,CAACrC,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMsC,aAAa,GAAG,CAAClD,IAAI,IAAI,EAAE,EAAEmD,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9J,QAAQ,CAAC,MAAM;IAC7CkJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C1C,aAAa,GAAG,IAAIA,aAAa,CAACsD,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAItD,aAAa,IAAIA,aAAa,CAACsD,MAAM,GAAG,CAAC,EAAE;MAC7Cb,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMa,aAAa,GAAGvD,aAAa,CAAC+C,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKK,SAAS,EAAE;UAC9BR,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKM,SAAS,EAAE;UACvCR,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMS,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAC1D,GAAGV,GAAG;QACNW,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAd,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMe,gBAAgB,GAAGX,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;MAC1D,GAAGV,GAAG;MACNW,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEZ,GAAG,CAACa,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHjB,eAAe,CAAC,CAAC,GAAGa,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGpK,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMqK,iBAAiB,GAAGrK,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMsK,gBAAgB,GAAGtK,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMuK,mBAAmB,GAAGvK,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMwK,UAAU,GAAItE,IAAI,IAAKA,IAAI,CAACmD,GAAG,CAACC,GAAG,KAAK;IAC5CW,EAAE,EAAEX,GAAG,CAACW,EAAE;IACVE,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVV,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCiB,UAAU,EAAEnB,GAAG,CAACmB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA3K,SAAS,CAAC,MAAM;IACd,IAAIyG,YAAY,IAAImD,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGzD,IAAI,CAACgC,SAAS,CAACqB,UAAU,CAACd,QAAQ,CAAC,CAAC;MACpD,MAAMmB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BpH,YAAY,CAAC4G,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGnH,UAAU,CAAC,MAAM;UAC1CyG,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCnE,YAAY,CAAC,CAAC,GAAGmD,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIY,gBAAgB,CAACQ,OAAO,EAAE;QAC5BpH,YAAY,CAAC4G,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACpB,QAAQ,EAAEnD,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnL,QAAQ,CAAC;IACjDoL,IAAI,EAAE,KAAK;IACXxG,KAAK,EAAE,IAAI;IACXyG,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApL,SAAS,CAAC,MAAM;IACd,IAAImJ,YAAY,CAACW,MAAM,KAAK,CAAC,IAAIF,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACpDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,QAAQ,CAACE,MAAM,CAAC;MACpDV,eAAe,CAAC,CAAC,GAAGQ,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAET,YAAY,CAAC,CAAC;EAI5B,MAAMkC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAIhF,MAAM,IAAIA,MAAM,CAACiF,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C/E,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAMgF,WAAW,GAAG,GAAGtI,OAAO,aAAaoD,MAAM,EAAE;MACnD,MAAMmF,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIf,IAAI,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3F,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM4F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMnJ,KAAK,CAACoJ,MAAM,CAAC,GAAGnJ,OAAO,YAAYoD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO6F,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA5F,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM+F,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC9C,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMkC,kBAAkB,GAAGtM,WAAW,CAAC,CAACmG,IAAI,EAAEoG,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGrG,IAAI,IAAIwD,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC8C,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClDkD,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAK;MACpB,IAAIgD,UAAU,IAAIhD,GAAG,CAACW,EAAE,KAAKqC,UAAU,CAACrC,EAAE,EAAE;QAC1C,OAAO2C,GAAG,IAAIC,MAAM,CAACP,UAAU,CAAC7B,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAOmC,GAAG,IAAIC,MAAM,CAACvD,GAAG,CAACmB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoD,gBAAgB,GAAG/M,WAAW,CAAEmG,IAAI,IAAK;IAC7C,MAAM6G,QAAQ,GAAG7G,IAAI,CAAC8G,IAAI,CAAC1D,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC;IACrD,IAAI4C,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG/G,IAAI,CAClBwG,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC,CAClDkD,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAIC,MAAM,CAACvD,GAAG,CAACmB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DsC,QAAQ,CAACtC,UAAU,GAAGwC,QAAQ;IAChC;IACA,OAAO/G,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgH,2BAA2B,GAAGnN,WAAW,CAC7CoD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAAC4B,OAAO,CAAC,eAAe,EAAE1B,IAAI,CAACgC,SAAS,CAACjD,IAAI,CAAC,CAAC;MAC3D6C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9C,IAAI,CAAC0D,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMmB,qBAAqB,GAAGpN,WAAW,CACvCoD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvB6C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACzC,YAAY,CACf,CAAC;;EAED;EACA,MAAM6G,gBAAgB,GAAGrN,WAAW,CAAEsN,MAAM,IAAK;IAC/C1D,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAImF,UAAU,GAAGjB,kBAAkB,CAAClE,IAAI,EAAEkF,MAAM,CAAC;MACjD,MAAME,WAAW,GAAGpF,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACW,EAAE,KAAKoD,MAAM,CAACpD,EAAE,EAAE,OAAO;UAAE,GAAGX,GAAG;UAAE,GAAG+D;QAAO,CAAC;QACtD,IAAI/D,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAEmB,UAAU,EAAE6C;QAAW,CAAC;QACjE,OAAOhE,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA4D,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMK,uBAAuB,GAAIxB,KAAK,IAAK;IACzCjD,OAAO,CAACiD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACyB,OAAO,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG9N,KAAK,CAACG,WAAW,CAAC,CAAC0E,KAAK,EAAEyG,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVxG,KAAK;MACLyG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyC,kBAAkB,GAAG5N,WAAW,CAAC,MAAM;IAC3CiL,gBAAgB,CAAC7C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP8C,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACA3C,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoF,kBAAkB,GAAG7N,WAAW,CAAE8N,MAAM,IAAK;IACjD,MAAM;MAAEpJ;IAAM,CAAC,GAAGsG,aAAa;IAC/B,IAAItG,KAAK,KAAK,IAAI,EAAE;MAClB;MACAkJ,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCpE,WAAW,CAACqE,QAAQ,IAAI;UACtB,IAAIT,WAAW,GAAGS,QAAQ,CAAC3E,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACW,EAAE,KAAKxF,KAAK,EAAE;cACpB,IAAIoJ,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGvE,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIyE,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAI7F,aAAa,EAAE;kBACjB6F,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAI5F,UAAU,EAAE;kBACd2F,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAACtE,MAAM,GAAG,CAAC,EAAE;kBACvBqE,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG9E,GAAG;kBAAEC,OAAO,EAAE0E,WAAW;kBAAEzE,iBAAiB,EAAEyE;gBAAY,CAAC;cACzE;YACF;YACA,OAAO3E,GAAG;UACZ,CAAC,CAAC;UAEFiE,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACA5J,UAAU,CAAC,MAAM;UACfoF,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC+B,aAAa,EAAE4C,kBAAkB,EAAEb,gBAAgB,EAAEzE,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAM8F,mBAAmB,GAAGtO,WAAW,CAAC,MAAM;IAC5CyH,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8G,oBAAoB,GAAGvO,WAAW,CAAC,MAAM;IAC7CyH,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiH,YAAY,GAAGxO,WAAW,CAAC,MAAM;IACrC,IAAIsH,SAAS,CAACmH,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC1H,cAAc,CAAC2H,QAAQ,CAACpH,SAAS,CAACmH,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEzH,iBAAiB,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEd,SAAS,CAACmH,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9BJ,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIxH,cAAc,CAAC2H,QAAQ,CAACpH,SAAS,CAACmH,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACrH,SAAS,EAAEP,cAAc,EAAEwH,oBAAoB,EAAEI,SAAS,CAAC,CAAC;;EAEhE;EACA,MAAMC,YAAY,GAAG5O,WAAW,CAAE8N,MAAM,IAAK;IAC3C9G,iBAAiB,CAACoB,IAAI,IAAIA,IAAI,CAACuE,MAAM,CAACkC,IAAI,IAAIA,IAAI,KAAKf,MAAM,CAAC,CAAC;IAC/Da,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMG,eAAe,GAAG9O,WAAW,CAAEkK,EAAE,IAAK;IAC1CN,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAIoF,WAAW,GAAGpF,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA,IAAIwF,SAAS,GAAG,CAAC;MACjBvB,WAAW,CAACwB,OAAO,CAACzF,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAG2E,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFvB,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFmB,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;EAChC,CAAC,EAAE,CAAC5B,gBAAgB,EAAE4B,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMM,aAAa,GAAGjP,WAAW,CAAEkK,EAAE,IAAK;IACxCN,WAAW,CAACxB,IAAI,IAAI;MAClB,IAAIoF,WAAW,GAAGpF,IAAI,CAACkB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGX,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;;MAEpF;MACA,IAAIwF,SAAS,GAAG,CAAC;MACjBvB,WAAW,CAACwB,OAAO,CAACzF,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAG2E,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFvB,WAAW,GAAGT,gBAAgB,CAACS,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF5J,UAAU,CAAC,MAAM;MACf+K,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAAC5B,gBAAgB,EAAE4B,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMO,eAAe,GAAGlP,WAAW,CAAEkK,EAAE,IAAK;IAC1CN,WAAW,CAACxB,IAAI,IAAI;MAClB;MACA,MAAM+G,YAAY,GAAG/G,IAAI,CAACuE,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAI6E,SAAS,GAAG,CAAC;MACjBI,YAAY,CAACH,OAAO,CAACzF,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAG2E,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMvB,WAAW,GAAGT,gBAAgB,CAACoC,YAAY,CAAC;;MAElD;MACAhC,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElCmB,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B,OAAOnB,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAEuB,SAAS,CAAC,CAAC;;EAErF;EACA,MAAMS,YAAY,GAAGpP,WAAW,CAAEqP,UAAU,IAAK;IAC/CzF,WAAW,CAACxB,IAAI,IAAI;MAClB;MACA,MAAMkH,WAAW,GAAGlH,IAAI,CAACmH,SAAS,CAAChG,GAAG,IAAIA,GAAG,CAACW,EAAE,KAAKmF,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAOlH,IAAI;;MAEnC;MACA,MAAMoH,KAAK,GAAG5E,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAM8E,UAAU,GAAGrH,IAAI,CAACkH,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAACrF,EAAE,KAAK,QAAQ,GAAGqF,UAAU,CAACrF,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAMkD,MAAM,GAAG;QACbpD,EAAE,EAAEsF,KAAK;QACTpF,EAAE,EAAEsF,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNpG,OAAO,EAAE,EAAE;QACXqG,QAAQ,EAAE,EAAE;QACZnF,UAAU,EAAE,CAAC;QACbjB,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,KAAK;QACfS,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAM2F,OAAO,GAAG,CAAC,GAAG1H,IAAI,CAAC;MACzB0H,OAAO,CAACC,MAAM,CAACT,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEhC,MAAM,CAAC;;MAE1C;MACA,IAAIyB,SAAS,GAAG,CAAC;MACjBe,OAAO,CAACd,OAAO,CAACzF,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACa,EAAE,KAAK,QAAQ,EAAE;UACrEb,GAAG,CAACa,EAAE,GAAG2E,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMvB,WAAW,GAAGT,gBAAgB,CAAC+C,OAAO,CAAC;;MAE7C;MACA3C,2BAA2B,CAACK,WAAW,CAAC;MACxCJ,qBAAqB,CAACI,WAAW,CAAC;MAElCmB,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;MAC7B,OAAOnB,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAEuB,SAAS,CAAC,CAAC;;EAErF;EACA,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFrI,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMsI,YAAY,GAAG,CAACtG,QAAQ,IAAI,EAAE,EACjCgD,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAuG,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC/F,EAAE,KAAK,QAAQ,IAAI,OAAOgG,CAAC,CAAChG,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO+F,CAAC,CAAC/F,EAAE,GAAGgG,CAAC,CAAChG,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMiG,OAAO,GAAGJ,YAAY,CAAC3G,GAAG,CAAC,CAACC,GAAG,EAAEU,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb0F,IAAI,EAAEpG,GAAG,CAACoG,IAAI,GAAI,OAAOpG,GAAG,CAACoG,IAAI,KAAK,QAAQ,IAAIpG,GAAG,CAACoG,IAAI,CAACjB,QAAQ,CAAC,GAAG,CAAC,GAAGnF,GAAG,CAACoG,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG/G,GAAG,CAACoG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEpG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGgH,IAAI,CAACC,KAAK,CAACjH,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFqG,EAAE,EAAE,OAAOrG,GAAG,CAACqG,EAAE,KAAK,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAACjH,GAAG,CAACqG,EAAE,CAAC,GAAGrG,GAAG,CAACqG,EAAE,IAAI,EAAE;QAClEpG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGgH,KAAK,EAAE,OAAOlH,GAAG,CAACsG,QAAQ,KAAK,QAAQ,GACpCtG,GAAG,CAACsG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGtG,GAAG,CAACsG,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAAGnH,GAAG,CAACsG,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAC3EnH,GAAG,CAACsG,QAAQ,IAAI,EAAE;QACpBc,MAAM,EAAE,OAAOpH,GAAG,CAACmB,UAAU,KAAK,QAAQ,GAAGnB,GAAG,CAACmB,UAAU,CAACgG,OAAO,CAAC,CAAC,CAAC,GAAGnH,GAAG,CAACmB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMkG,WAAW,GAAG,CAACjH,QAAQ,IAAI,EAAE,EAChCgD,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,IAAI,CAACb,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACmB,UAAU,CAAC,CACpEkC,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAI,OAAOtD,GAAG,CAACmB,UAAU,KAAK,QAAQ,GAAGnB,GAAG,CAACmB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMmG,YAAY,GAAIzK,MAAM,IAAIA,MAAM,CAACiF,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAGjF,MAAM;MAE5F,MAAM0K,QAAQ,GAAG,MAAM/N,KAAK,CAACgO,IAAI,CAAC,GAAG/N,OAAO,oBAAoB,EAAE;QAChEmD,IAAI,EAAEkK,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCtK,MAAM,EAAEyK;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAAC3K,IAAI,IAAI2K,QAAQ,CAAC3K,IAAI,CAAC6K,KAAK,EAAE;QACxC;QACA,MAAM1F,WAAW,GAAG,GAAGtI,OAAO,CAACsN,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAAC3K,IAAI,CAAC8K,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAtC,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC;;QAErC;QACA/K,UAAU,CAAC,MAAM;UACf,MAAMsN,MAAM,GAAG1F,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CyF,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAG/F,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACoF,MAAM,CAAC;UACjCtN,UAAU,CAAC,MAAM;YACf4H,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACkF,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B0C,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;IAClC,CAAC,SAAS;MACRhH,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM4J,kBAAkB,GAAGvR,WAAW,CAAC,CAAC0E,KAAK,EAAE8M,KAAK,KAAK;IACvD7D,iBAAiB,CAACjJ,KAAK,EAAE8M,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC7D,iBAAiB,CAAC,CAAC;EAEvB,MAAM8D,OAAO,GAAGvR,OAAO,CAAC,MAAOwG,WAAW,CAAC4C,GAAG,CAACoI,GAAG,IAAI;IACpD,IAAI,EAAE/H,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACgI,cAAc,CAACD,GAAG,CAAC/K,KAAK,CAAC,CAAC,IAAI+K,GAAG,CAAC/K,KAAK,KAAK,SAAS,IAAI+K,GAAG,CAAC/K,KAAK,KAAK,QAAQ,IAAI+K,GAAG,CAAC/K,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAI+K,GAAG,CAAC/K,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAE+K,GAAG,CAAC/K,KAAK;QAChBC,UAAU,EAAE8K,GAAG,CAAC9K,UAAU;QAC1BgL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVhL,QAAQ,EAAE,KAAK;QACfiL,UAAU,EAAGzF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC9C,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAIiC,MAAM,CAAC9C,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMqI,iBAAiB,GAAG1F,MAAM,CAAC9C,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEtG,OAAA,CAAC9B,OAAO;cAAC2Q,KAAK,EAAE3F,MAAM,CAAC9C,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACwI,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAlM,QAAA,eACvE7C,OAAA,CAAC/B,IAAI;gBACH+Q,KAAK,EAAEJ,iBAAkB;gBACzB5M,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAE6M,OAAO,EAAE,GAAG;kBAAEvM,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEwL,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAnN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAIiO,UAAU,GAAG,MAAM;UACvB,IAAIzN,UAAU,GAAG,KAAK;UAEtB,IAAIyH,MAAM,CAAC9C,GAAG,CAACE,iBAAiB,IAAI4C,MAAM,CAAC9C,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3E4I,UAAU,GAAGhG,MAAM,CAAC9C,GAAG,CAACE,iBAAiB;YACzC7E,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAE2H,MAAM,CAAC9C,GAAG,CAACW,EAAG;YACrBvF,IAAI,EAAE0N,UAAW;YACjBzN,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAE0M;UAAmB;YAAAtN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIsN,GAAG,CAAC/K,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAE+K,GAAG,CAAC/K,KAAK;QAChBC,UAAU,EAAE8K,GAAG,CAAC9K,UAAU;QAC1BgL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVhL,QAAQ,EAAE,KAAK;QACfiL,UAAU,EAAGzF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC9C,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEjH,OAAA,CAAChD,GAAG;YAACkF,EAAE,EAAE;cAAE+L,OAAO,EAAE,MAAM;cAAEkB,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAvM,QAAA,gBAE3D7C,OAAA,CAAC9B,OAAO;cAAC2Q,KAAK,EAAC,wDAAW;cAAAhM,QAAA,eACxB7C,OAAA,CAACjC,UAAU;gBACTkE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,SAAS;gBACfN,OAAO,EAAEA,CAAA,KAAMuK,YAAY,CAAC/C,MAAM,CAAC9C,GAAG,CAACW,EAAE,CAAE;gBAC3C7E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTmN,eAAe,EAAE,cAAc;oBAC/BrN,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACN,oBAAoB;kBAAC2C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVjB,OAAA,CAAC9B,OAAO;cAAC2Q,KAAK,EAAC,0EAAc;cAAAhM,QAAA,eAC3B7C,OAAA,CAACjC,UAAU;gBACTkE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,OAAO;gBACbN,OAAO,EAAEA,CAAA,KAAMqK,eAAe,CAAC7C,MAAM,CAAC9C,GAAG,CAACW,EAAE,CAAE;gBAC9C7E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTmN,eAAe,EAAE,YAAY;oBAC7BrN,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACL,uBAAuB;kBAAC0C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGTiI,MAAM,CAAC9C,GAAG,CAACG,QAAQ,gBAClBvG,OAAA,CAAC9C,MAAM;cAEL6E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZqN,SAAS,eAAEtP,OAAA,CAACb,QAAQ;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAMoK,aAAa,CAAC5C,MAAM,CAAC9C,GAAG,CAACW,EAAE,CAAE;cAC5C7E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,MAAM;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETjB,OAAA,CAAC9C,MAAM;cAEL6E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,OAAO;cACbC,IAAI,EAAC,OAAO;cACZqN,SAAS,eAAEtP,OAAA,CAACd,UAAU;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BS,OAAO,EAAEA,CAAA,KAAMiK,eAAe,CAACzC,MAAM,CAAC9C,GAAG,CAACW,EAAE,CAAE;cAC9C7E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,QAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGsN,GAAG;MACN7K,QAAQ,EAAEwF,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC9C,GAAG,IAAI8C,MAAM,CAAC9C,GAAG,CAACa,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAIiC,MAAM,CAAC9C,GAAG,IAAI8C,MAAM,CAAC9C,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOgI,GAAG,CAAC7K,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDiL,UAAU,EAAGzF,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC9C,GAAG,CAACa,EAAE,KAAK,OAAO,IAAIsH,GAAG,CAAC/K,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACExD,OAAA,CAAC/C,UAAU;YAAC8E,OAAO,EAAC,OAAO;YAACwN,UAAU,EAAC,MAAM;YAACvN,KAAK,EAAC,SAAS;YAAAa,QAAA,EAC1D,OAAOqG,MAAM,CAACmF,KAAK,KAAK,QAAQ,GAAGnF,MAAM,CAACmF,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOrE,MAAM,CAACmF,KAAK,KAAK,QAAQ,IAAI,CAACmB,KAAK,CAAC7F,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAAC,GAAG1E,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGrE,MAAM,CAACmF;UAAK;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIiI,MAAM,CAAC9C,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEvG,OAAA,CAAC/C,UAAU;YAAC8E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAEuN,cAAc,EAAE;YAAe,CAAE;YAAA5M,QAAA,EACtFqG,MAAM,CAACmF;UAAK;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIsN,GAAG,CAAC/K,KAAK,KAAK,MAAM,IAAI0F,MAAM,CAACmF,KAAK,EAAE;UACxC,OAAOnF,MAAM,CAACmF,KAAK,CAAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIoB,GAAG,CAAC/K,KAAK,KAAK,IAAI,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAACnE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/K,KAAK,KAAK,OAAO,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOjB,IAAI,CAACC,KAAK,CAACnE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/K,KAAK,KAAK,IAAI,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAACnE,MAAM,CAACmF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAC/K,KAAK,KAAK,UAAU,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOnF,MAAM,CAACmF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGnF,MAAM,CAACmF,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,GAAGrE,MAAM,CAACmF,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIgB,GAAG,CAAC/K,KAAK,KAAK,YAAY,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOnF,MAAM,CAACmF,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIgB,GAAG,CAAC/K,KAAK,KAAK,YAAY,IAAI,OAAO0F,MAAM,CAACmF,KAAK,KAAK,QAAQ,IAAI,CAACmB,KAAK,CAAC7F,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO1E,MAAM,CAACT,MAAM,CAACmF,KAAK,CAAC,CAACd,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOrE,MAAM,CAACmF,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOnF,MAAM,CAACmF,KAAK;QACrB;QACA,OAAOnF,MAAM,CAACmF,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC7E,MAAM,CAACkG,OAAO,CAAE,EAAE,CAACnM,WAAW,EAAE6K,kBAAkB,EAAEzC,eAAe,EAAEG,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAM4D,gBAAgB,GAAG5S,OAAO,CAAC,MAAM;IACrC,IAAI,CAAC4H,mBAAmB,CAAC2G,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAO9E,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAMoJ,WAAW,GAAGjL,mBAAmB,CAACkL,WAAW,CAAC,CAAC;IACrD,OAAO,CAACrJ,QAAQ,IAAI,EAAE,EAAEgD,MAAM,CAACpD,GAAG,IAAI;MACpC,IAAIvB,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOiL,MAAM,CAACC,MAAM,CAAC3J,GAAG,CAAC,CAAC4J,IAAI,CAAC3B,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACzI,QAAQ,CAAC,CAAC,CAACiK,WAAW,CAAC,CAAC,CAACtE,QAAQ,CAACqE,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMK,SAAS,GAAG7J,GAAG,CAACvB,YAAY,CAAC;QACnC,OAAOoL,SAAS,IAAIA,SAAS,CAACrK,QAAQ,CAAC,CAAC,CAACiK,WAAW,CAAC,CAAC,CAACtE,QAAQ,CAACqE,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpJ,QAAQ,EAAE7B,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAMqL,YAAY,GAAGnT,OAAO,CAAC,MAAM4S,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA/S,SAAS,CAAC,MAAM;IACdiJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBL,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1BiL,UAAU,EAAED,YAAY,CAACxJ;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAEgL,YAAY,CAACxJ,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE1G,OAAA,CAAChD,GAAG;MAACkF,EAAE,EAAE;QAAEkO,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxN,QAAA,gBACtC7C,OAAA,CAAC/C,UAAU;QAAC8E,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAa,QAAA,EAAC;MAEhD;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC9C,MAAM;QACL6E,OAAO,EAAC,WAAW;QACnBL,OAAO,EAAEwB,OAAQ;QACjBhB,EAAE,EAAE;UAAEoO,EAAE,EAAE;QAAE,CAAE;QAAAzN,QAAA,EACf;MAED;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAAChD,GAAG;IAAA6F,QAAA,gBAEF7C,OAAA,CAAC7B,IAAI;MAAC+D,EAAE,EAAE;QAAEqO,EAAE,EAAE;MAAE,CAAE;MAAA1N,QAAA,eAClB7C,OAAA,CAAC5B,WAAW;QAAAyE,QAAA,eACV7C,OAAA,CAAChD,GAAG;UAACkF,EAAE,EAAE;YAAE+L,OAAO,EAAE,MAAM;YAAEmB,UAAU,EAAE,QAAQ;YAAEoB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEtB,GAAG,EAAE;UAAE,CAAE;UAAAtM,QAAA,gBAC5G7C,OAAA,CAAChD,GAAG;YAACkF,EAAE,EAAE;cAAE+L,OAAO,EAAE,MAAM;cAAEmB,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAAtM,QAAA,gBACzD7C,OAAA,CAACX,cAAc;cAAC6C,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAAChD,GAAG;cAAA6F,QAAA,gBACF7C,OAAA,CAAC/C,UAAU;gBAAC8E,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAEqN,UAAU,EAAE,GAAG;kBAAEvN,KAAK,EAAE,cAAc;kBAAEuO,EAAE,EAAE;gBAAI,CAAE;gBAAA1N,QAAA,EAAC;cAElF;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAC/C,UAAU;gBAAC8E,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAa,QAAA,EAAC;cAE7D;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAAC3B,KAAK;YAACwC,SAAS,EAAC,KAAK;YAAC6P,OAAO,EAAE,CAAE;YAACxO,EAAE,EAAE;cAAEuO,QAAQ,EAAE,MAAM;cAAEtB,GAAG,EAAE;YAAE,CAAE;YAAAtM,QAAA,gBAClE7C,OAAA,CAAC/B,IAAI;cACH0S,IAAI,eAAE3Q,OAAA,CAACV,aAAa;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxB+N,KAAK,EAAE,GAAG,CAACkB,YAAY,IAAI,EAAE,EAAE1G,MAAM,CAACpD,GAAG,IAAI,CAACA,GAAG,CAACY,OAAO,IAAI,CAACZ,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,MAAO;cACzF1E,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAC/B,IAAI;cACH0S,IAAI,eAAE3Q,OAAA,CAACT,cAAc;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB+N,KAAK,EAAE,WAAW7F,kBAAkB,CAAC+G,YAAY,CAAC,CAAC3C,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEvL,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACiP,YAAY,IAAI,EAAE,EAAE1G,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,GAAG,CAAC,iBAC1D1G,OAAA,CAAC/B,IAAI;cACH0S,IAAI,eAAE3Q,OAAA,CAACd,UAAU;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrB+N,KAAK,EAAE,GAAG,CAACkB,YAAY,IAAI,EAAE,EAAE1G,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACG,MAAM,OAAQ;cACzE1E,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC7B,IAAI;MAAC+D,EAAE,EAAE;QAAEqO,EAAE,EAAE;MAAE,CAAE;MAAA1N,QAAA,eAClB7C,OAAA,CAAC5B,WAAW;QAAAyE,QAAA,gBACV7C,OAAA,CAAC/C,UAAU;UAAC8E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEqN,UAAU,EAAE,GAAG;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAA1N,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC3B,KAAK;UAACwC,SAAS,EAAE;YAAE+P,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAA7N,QAAA,gBACxD7C,OAAA,CAAC9C,MAAM;YACL6E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfsN,SAAS,eAAEtP,OAAA,CAACjB,YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAEuG,cAAe;YAAApF,QAAA,EACzB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC9C,MAAM;YACL6E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfsN,SAAS,EAAE/K,oBAAoB,gBAAGvE,OAAA,CAAChC,gBAAgB;cAACiE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACZ,gBAAgB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAEmL,gBAAiB;YAC1BiE,QAAQ,EAAEvM,oBAAqB;YAAA1B,QAAA,EAE9B0B,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC9C,MAAM;YACL6E,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACbsN,SAAS,eAAEtP,OAAA,CAAChB,cAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEqH,aAAc;YAAAlG,QAAA,EACxB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC7B,IAAI;MAAC+D,EAAE,EAAE;QAAEqO,EAAE,EAAE;MAAE,CAAE;MAAA1N,QAAA,eAClB7C,OAAA,CAAC5B,WAAW;QAAAyE,QAAA,gBACV7C,OAAA,CAAC/C,UAAU;UAAC8E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEqN,UAAU,EAAE,GAAG;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAA1N,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC3B,KAAK;UAACwC,SAAS,EAAE;YAAE+P,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACtB,UAAU,EAAC,QAAQ;UAAAvM,QAAA,gBAC5E7C,OAAA,CAACzB,WAAW;YAAC0D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAU,QAAA,gBAC9C7C,OAAA,CAACxB,UAAU;cAAAqE,QAAA,EAAC;YAAI;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACvB,MAAM;cACL4P,KAAK,EAAExJ,YAAa;cACpBmK,KAAK,EAAC,0BAAM;cACZ+B,QAAQ,EAAGjP,CAAC,IAAKgD,eAAe,CAAChD,CAAC,CAACkP,MAAM,CAAC3C,KAAK,CAAE;cAAAxL,QAAA,gBAEjD7C,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,KAAK;gBAAAxL,QAAA,EAAC;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,IAAI;gBAAAxL,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,MAAM;gBAAAxL,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,YAAY;gBAAAxL,QAAA,EAAC;cAAU;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,OAAO;gBAAAxL,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,IAAI;gBAAAxL,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,SAAS;gBAAAxL,QAAA,EAAC;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,UAAU;gBAAAxL,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACtB,QAAQ;gBAAC2P,KAAK,EAAC,YAAY;gBAAAxL,QAAA,EAAC;cAAM;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAAClC,SAAS;YACRmE,IAAI,EAAC,OAAO;YACZgP,WAAW,EAAC,yCAAW;YACvB5C,KAAK,EAAE5J,UAAW;YAClBsM,QAAQ,EAAGjP,CAAC,IAAK4C,aAAa,CAAC5C,CAAC,CAACkP,MAAM,CAAC3C,KAAK,CAAE;YAC/CnM,EAAE,EAAE;cAAEgP,QAAQ,EAAE,CAAC;cAAE/O,QAAQ,EAAE;YAAI,CAAE;YACnCgP,UAAU,EAAE;cACVC,cAAc,eACZpR,OAAA,CAACrB,cAAc;gBAAC0S,QAAQ,EAAC,OAAO;gBAAAxO,QAAA,eAC9B7C,OAAA,CAACR,UAAU;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACDqQ,YAAY,EAAE7M,UAAU,iBACtBzE,OAAA,CAACrB,cAAc;gBAAC0S,QAAQ,EAAC,KAAK;gBAAAxO,QAAA,eAC5B7C,OAAA,CAACjC,UAAU;kBACTkE,IAAI,EAAC,OAAO;kBACZP,OAAO,EAAEA,CAAA,KAAMgD,aAAa,CAAC,EAAE,CAAE;kBACjC6M,IAAI,EAAC,KAAK;kBAAA1O,QAAA,eAEV7C,OAAA,CAACP,SAAS;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDwD,UAAU,iBACTzE,OAAA,CAAC/C,UAAU;YAAC8E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAa,QAAA,GAAC,eAC9C,EAAC8M,gBAAgB,CAACnG,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC7C,KAAK;MAAC+E,EAAE,EAAE;QAAEwM,KAAK,EAAE,MAAM;QAAEnM,QAAQ,EAAE;MAAS,CAAE;MAAAM,QAAA,eAC7C7C,OAAA,CAAChD,GAAG;QAACkF,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAA7L,QAAA,eACzC7C,OAAA,CAAClB,QAAQ;UAEP0S,IAAI,EAAEtB,YAAa;UACnB5B,OAAO,EAAEA,OAAQ;UACjB/I,eAAe,EAAEA,eAAgB;UACjCkM,uBAAuB,EAAEzM,kBAAmB;UAC5C0M,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAC/BC,UAAU;UACVC,cAAc,EAAC,QAAQ;UACvBC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,qBAAqB,EAAE,KAAM;UAC7BC,SAAS,EAAE,EAAG;UACdC,OAAO,EAAC,SAAS;UACjBC,SAAS,EAAE,CAAE;UACbC,YAAY,EAAE,CAAE;UAChBC,iBAAiB;UACjBC,mBAAmB;UACnBC,qBAAqB;UACrBC,sBAAsB;UACtBC,0BAA0B;UAC1BC,eAAe,EAAGxJ,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC9C,GAAG,CAACY,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIkC,MAAM,CAAC9C,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFoM,cAAc,EAAGzJ,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC9C,GAAG,CAACY,OAAO,IAAIkC,MAAM,CAAC9C,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAO2C,MAAM,CAAC0J,MAAM,CAAClP,QAAQ,IAAI,OAAOwF,MAAM,CAAC0J,MAAM,CAAClP,QAAQ,KAAK,UAAU,GAC3EwF,MAAM,CAAC0J,MAAM,CAAClP,QAAQ,CAACwF,MAAM,CAAC,GAAGA,MAAM,CAAC0J,MAAM,CAAClP,QAAQ;UAC3D,CAAE;UACFwG,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAAC5C,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOuD,MAAM,CAAC5C,UAAU,KAAK,QAAQ,EAAE;gBACzC4C,MAAM,CAAC5C,UAAU,GAAGoC,MAAM,CAACQ,MAAM,CAAC5C,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAO2C,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDpI,EAAE,EAAE;YACF,cAAc,EAAE;cACdmN,eAAe,EAAE,0BAA0B;cAC3CE,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBF,eAAe,EAAE,0BAA0B;cAC3CrN,KAAK,EAAE,eAAe;cACtByN,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrBhN,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpBiQ,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9B1D,eAAe,EAAE,oBAAoB;cACrCyD,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7B1D,eAAe,EAAE,oBAAoB;cACrCrN,KAAK,EAAE,cAAc;cACrBgR,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClCzD,UAAU,EAAE,MAAM;cAClBvN,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChC4L,OAAO,EAAE;YACX,CAAC;YACDgF,SAAS,EAAE;UACb;QAAE,GAlFG,YAAY1N,eAAe,CAACE,QAAQ,IAAIyK,YAAY,CAACxJ,MAAM,EAAE;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAAC1C,MAAM;MACLyK,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBmL,OAAO,EAAEzI,kBAAmB;MAC5B0I,SAAS;MACT/Q,QAAQ,EAAC,IAAI;MACbgR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA1Q,QAAA,gBAEnB7C,OAAA,CAACzC,WAAW;QAAAsF,QAAA,eACV7C,OAAA,CAAChD,GAAG;UAACkF,EAAE,EAAE;YAAE+L,OAAO,EAAE,MAAM;YAAEuC,cAAc,EAAE,eAAe;YAAEpB,UAAU,EAAE;UAAS,CAAE;UAAAvM,QAAA,gBAClF7C,OAAA,CAAC/C,UAAU;YAAC8E,OAAO,EAAC,IAAI;YAAAc,QAAA,EAAC;UAAS;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC9C,MAAM;YACLoS,SAAS,eAAEtP,OAAA,CAACf,OAAO;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEyJ,mBAAoB;YAC7BnJ,KAAK,EAAC,SAAS;YAAAa,QAAA,EAChB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjB,OAAA,CAAChD,GAAG;QAACkF,EAAE,EAAE;UAAEsR,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA5Q,QAAA,gBACxB7C,OAAA,CAAC/C,UAAU;UAAC8E,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAEqO,EAAE,EAAE;UAAE,CAAE;UAAA1N,QAAA,EAAC;QAElE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC3B,KAAK;UAACwC,SAAS,EAAC,KAAK;UAAC6P,OAAO,EAAE,CAAE;UAAA7N,QAAA,gBAChC7C,OAAA,CAACnB,gBAAgB;YACf6U,OAAO,eACL1T,OAAA,CAACpB,QAAQ;cACP+U,OAAO,EAAExO,aAAc;cACvB4L,QAAQ,EAAGjP,CAAC,IAAKsD,gBAAgB,CAACtD,CAAC,CAACkP,MAAM,CAAC2C,OAAO,CAAE;cACpD1R,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACD+N,KAAK,EAAC;UAAS;YAAAlO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjB,OAAA,CAACnB,gBAAgB;YACf6U,OAAO,eACL1T,OAAA,CAACpB,QAAQ;cACP+U,OAAO,EAAEtO,UAAW;cACpB0L,QAAQ,EAAGjP,CAAC,IAAKwD,aAAa,CAACxD,CAAC,CAACkP,MAAM,CAAC2C,OAAO,CAAE;cACjD1R,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACD+N,KAAK,EAAC;UAAK;YAAAlO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjB,OAAA,CAACxC,aAAa;QAACoW,QAAQ;QAAC1R,EAAE,EAAE;UAAE2R,CAAC,EAAE;QAAE,CAAE;QAAAhR,QAAA,eACnC7C,OAAA,CAACF,aAAa;UACZ6C,MAAM,EAAE,GAAI;UACZmR,SAAS,EAAElQ,cAAc,CAAC8C,MAAO;UACjCqN,QAAQ,EAAE,EAAG;UACbrF,KAAK,EAAC,MAAM;UAAA7L,QAAA,EAEXA,CAAC;YAAEiE,KAAK;YAAEkH;UAAM,CAAC,KAAK;YACrB,MAAMrD,MAAM,GAAG/G,cAAc,CAACkD,KAAK,CAAC;YACpC,oBACE9G,OAAA,CAACrC,QAAQ;cAEPqQ,KAAK,EAAEA,KAAM;cACbgG,cAAc;cACdC,eAAe,EAAGtJ,MAAM,KAAK,MAAM,iBACjC3K,OAAA,CAACjC,UAAU;gBACTwT,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB7P,OAAO,EAAEA,CAAA,KAAM+J,YAAY,CAACd,MAAM,CAAE;gBAAA9H,QAAA,eAEpC7C,OAAA,CAACd,UAAU;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAA4B,QAAA,eAEF7C,OAAA,CAACpC,cAAc;gBAAC8D,OAAO,EAAEA,CAAA,KAAMgJ,kBAAkB,CAACC,MAAM,CAAE;gBAAA9H,QAAA,eACxD7C,OAAA,CAACnC,YAAY;kBAACqW,OAAO,EAAEvJ;gBAAO;kBAAA7J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ0J,MAAM;cAAA7J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACvC,aAAa;QAAAoF,QAAA,eACZ7C,OAAA,CAAC9C,MAAM;UAACwE,OAAO,EAAE+I,kBAAmB;UAAA5H,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAAC1C,MAAM;MACLyK,IAAI,EAAE1D,eAAgB;MACtB6O,OAAO,EAAE9H,oBAAqB;MAC9B+H,SAAS;MACT/Q,QAAQ,EAAC,IAAI;MACbgR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAA1Q,QAAA,gBAEnB7C,OAAA,CAACzC,WAAW;QAAAsF,QAAA,EAAC;MAAK;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACxC,aAAa;QAAAqF,QAAA,eACZ7C,OAAA,CAAClC,SAAS;UACRqW,SAAS;UACTC,MAAM,EAAC,OAAO;UACdrN,EAAE,EAAC,MAAM;UACTiI,KAAK,EAAC,0BAAM;UACZqF,IAAI,EAAC,MAAM;UACXlB,SAAS;UACTpR,OAAO,EAAC,UAAU;UAClBsM,KAAK,EAAElK,SAAU;UACjB4M,QAAQ,EAAGjP,CAAC,IAAKsC,YAAY,CAACtC,CAAC,CAACkP,MAAM,CAAC3C,KAAK;QAAE;UAAAvN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACvC,aAAa;QAAAoF,QAAA,gBACZ7C,OAAA,CAAC9C,MAAM;UAACwE,OAAO,EAAE0J,oBAAqB;UAAAvI,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC9C,MAAM;UAACwE,OAAO,EAAE2J,YAAa;UAACrJ,KAAK,EAAC,SAAS;UAAAa,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACqC,GAAA,CA9qCIP,aAAa;AAAAuR,GAAA,GAAbvR,aAAa;AAgrCnB,eAAeA,aAAa;AAAC,IAAA7B,EAAA,EAAAI,GAAA,EAAAwB,GAAA,EAAAwR,GAAA;AAAAC,YAAA,CAAArT,EAAA;AAAAqT,YAAA,CAAAjT,GAAA;AAAAiT,YAAA,CAAAzR,GAAA;AAAAyR,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}