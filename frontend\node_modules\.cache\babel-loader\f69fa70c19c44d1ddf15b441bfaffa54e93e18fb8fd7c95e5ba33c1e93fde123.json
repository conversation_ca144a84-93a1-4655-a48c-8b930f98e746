{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Fade, Slide, Zoom, useTheme, alpha, Stack, Divider } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Chip, {\n      label: uiState.text,\n      color: uiState.isSelected ? 'primary' : 'default',\n      variant: uiState.isSelected ? 'filled' : 'outlined',\n      size: \"small\",\n      onClick: handleClick,\n      clickable: true,\n      sx: {\n        maxWidth: '100%',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease-in-out',\n        '& .MuiChip-label': {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          display: 'block'\n        }\n      }\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n}, \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\")), \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  const theme = useTheme();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n  const onProcessRowUpdateError = error => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, \"remove\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData || [], [gridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 600,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3,\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              flexWrap: 'wrap',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 1.5,\n                  borderRadius: '50%',\n                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`\n                },\n                children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    color: 'white',\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 700,\n                    color: 'text.primary',\n                    mb: 0.5\n                  },\n                  children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'text.secondary'\n                  },\n                  children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              sx: {\n                flexWrap: 'wrap',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 25\n                }, this),\n                label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n                color: \"primary\",\n                variant: \"outlined\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 25\n                }, this),\n                label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n                color: \"success\",\n                variant: \"outlined\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 27\n                }, this),\n                label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n                color: \"warning\",\n                variant: \"outlined\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Slide, {\n      direction: \"up\",\n      in: true,\n      timeout: 800,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: {\n              xs: 'column',\n              sm: 'row'\n            },\n            spacing: 2,\n            sx: {\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 28\n              }, this),\n              onClick: handleDownload,\n              sx: {\n                borderRadius: 2,\n                px: 3,\n                py: 1.5,\n                fontWeight: 600,\n                background: `linear-gradient(45deg, ${theme.palette.success.main} 30%, ${theme.palette.success.light} 90%)`,\n                '&:hover': {\n                  background: `linear-gradient(45deg, ${theme.palette.success.dark} 30%, ${theme.palette.success.main} 90%)`,\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              children: \"\\u4E0B\\u8F7DExcel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 51\n              }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 100\n              }, this),\n              onClick: generateDocument,\n              disabled: isGeneratingDocument,\n              sx: {\n                borderRadius: 2,\n                px: 3,\n                py: 1.5,\n                fontWeight: 600,\n                background: isGeneratingDocument ? 'grey.400' : `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n                '&:hover': {\n                  background: isGeneratingDocument ? 'grey.400' : `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.secondary.dark} 90%)`,\n                  transform: isGeneratingDocument ? 'none' : 'translateY(-2px)',\n                  boxShadow: isGeneratingDocument ? 'none' : '0 8px 25px rgba(0,0,0,0.15)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              children: isGeneratingDocument ? '生成中...' : '生成文档'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                display: {\n                  xs: 'none',\n                  sm: 'block'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 28\n              }, this),\n              onClick: handleCleanup,\n              sx: {\n                borderRadius: 2,\n                px: 3,\n                py: 1.5,\n                fontWeight: 600,\n                borderColor: alpha(theme.palette.error.main, 0.5),\n                color: 'error.main',\n                '&:hover': {\n                  borderColor: 'error.main',\n                  backgroundColor: alpha(theme.palette.error.main, 0.05),\n                  transform: 'translateY(-2px)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 784,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 1000,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          overflow: 'hidden',\n          borderRadius: 3,\n          boxShadow: '0 10px 40px rgba(0,0,0,0.1)',\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: 'auto',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(DataGrid, {\n            rows: memoGridData,\n            columns: columns,\n            pageSize: 100,\n            rowsPerPageOptions: [100, 200, 500],\n            disableSelectionOnClick: true,\n            headerHeight: 64,\n            columnHeaderHeight: 64,\n            getRowClassName: params => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            },\n            isCellEditable: params => {\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n            },\n            processRowUpdate: (newRow, oldRow) => {\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            },\n            onProcessRowUpdateError: onProcessRowUpdateError,\n            sx: {\n              border: 'none',\n              '& .MuiDataGrid-cell': {\n                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`,\n                fontSize: '0.875rem',\n                padding: '12px 16px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                '&:focus': {\n                  outline: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,\n                  outlineOffset: '-2px'\n                }\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.08)} 0%, ${alpha(theme.palette.secondary.main, 0.04)} 100%)`,\n                borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n                '& .MuiDataGrid-columnHeader': {\n                  fontWeight: 700,\n                  fontSize: '0.875rem',\n                  color: theme.palette.text.primary,\n                  padding: '0 16px',\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal'\n                }\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                fontWeight: 700,\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px'\n              },\n              '& .total-row': {\n                background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.15)} 0%, ${alpha(theme.palette.success.light, 0.1)} 100%)`,\n                fontWeight: 700,\n                '& .MuiDataGrid-cell': {\n                  borderBottom: `2px solid ${theme.palette.success.main}`,\n                  color: theme.palette.success.dark,\n                  fontSize: '0.9rem'\n                }\n              },\n              '& .removed-row': {\n                background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)} 0%, ${alpha(theme.palette.error.light, 0.05)} 100%)`,\n                opacity: 0.7,\n                textDecoration: 'line-through',\n                '& .MuiDataGrid-cell': {\n                  color: theme.palette.text.disabled,\n                  borderBottom: `1px solid ${alpha(theme.palette.error.main, 0.2)}`\n                }\n              },\n              '& .MuiDataGrid-row': {\n                '&:hover': {\n                  backgroundColor: alpha(theme.palette.primary.main, 0.04),\n                  transition: 'background-color 0.2s ease'\n                }\n              },\n              '& .MuiDataGrid-footerContainer': {\n                borderTop: `2px solid ${alpha(theme.palette.divider, 0.3)}`,\n                backgroundColor: alpha(theme.palette.background.default, 0.5)\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important'\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible'\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none'\n              },\n              minHeight: 500,\n              borderRadius: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1000,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1074,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1050,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 708,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"clpmatTKzjUUVNpKgTEaSbEo0Zw=\", false, function () {\n  return [useTheme, useSnackbar];\n});\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Fade", "Slide", "Zoom", "useTheme", "alpha", "<PERSON><PERSON>", "Divider", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "axios", "API_URL", "FixedSizeList", "useSnackbar", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "clickable", "sx", "max<PERSON><PERSON><PERSON>", "cursor", "transition", "overflow", "textOverflow", "whiteSpace", "display", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "theme", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "key", "borderRadius", "border", "borderColor", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "processRowUpdate", "newRow", "prev", "totalValue", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "updatedData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "style", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "opacity", "remarkText", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "memoGridData", "textAlign", "py", "mt", "in", "timeout", "mb", "background", "palette", "primary", "main", "secondary", "alignItems", "justifyContent", "flexWrap", "gap", "p", "fontSize", "spacing", "xs", "sm", "startIcon", "px", "success", "light", "dark", "transform", "boxShadow", "disabled", "orientation", "flexItem", "backgroundColor", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "borderBottom", "divider", "padding", "lineHeight", "outline", "outlineOffset", "borderTop", "default", "overflowX", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "autoFocus", "margin", "type", "onChange", "target", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Fade,\n  Slide,\n  Zoom,\n  useTheme,\n  alpha,\n  Stack,\n  Divider\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { useSnackbar } from 'notistack';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : ''} arrow placement=\"top\">\n      <Chip\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        label={uiState.text}\n        color={uiState.isSelected ? 'primary' : 'default'}\n        variant={uiState.isSelected ? 'filled' : 'outlined'}\n        size=\"small\"\n        onClick={handleClick}\n        clickable\n        sx={{ \n          maxWidth: '100%', \n          cursor: 'pointer',\n          transition: 'all 0.2s ease-in-out',\n          '& .MuiChip-label': { \n            overflow: 'hidden', \n            textOverflow: 'ellipsis', \n            whiteSpace: 'nowrap', \n            display: 'block' \n          }\n        }}\n      />\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  const theme = useTheme();\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      return prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n    });\n    return newRow;\n  }, [getTotalCommission]);\n\n  const onProcessRowUpdateError = (error) => {\n    showSnackbar(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            return (\n              <Chip\n                key=\"undo\"\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          return (\n            <Chip\n              key=\"remove\"\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, gridData, handleRemarksClick, handleRemoveRow, handleUndoRow]);\n  \n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData || [], [gridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Fade in timeout={600}>\n        <Card\n          sx={{\n            mb: 3,\n            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,\n            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          }}\n        >\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Box\n                  sx={{\n                    p: 1.5,\n                    borderRadius: '50%',\n                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                  }}\n                >\n                  <AssessmentIcon sx={{ color: 'white', fontSize: 28 }} />\n                </Box>\n                <Box>\n                  <Typography\n                    variant=\"h6\"\n                    sx={{\n                      fontWeight: 700,\n                      color: 'text.primary',\n                      mb: 0.5,\n                    }}\n                  >\n                    处理结果\n                  </Typography>\n                  <Typography\n                    variant=\"body2\"\n                    sx={{\n                      color: 'text.secondary',\n                    }}\n                  >\n                    数据处理完成，可以编辑和导出结果\n                  </Typography>\n                </Box>\n              </Box>\n\n              {/* 统计信息 */}\n              <Stack direction=\"row\" spacing={2} sx={{ flexWrap: 'wrap', gap: 1 }}>\n                <Chip\n                  icon={<TableViewIcon />}\n                  label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                  color=\"primary\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n                <Chip\n                  icon={<TrendingUpIcon />}\n                  label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                  color=\"success\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n                {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                  <Chip\n                    icon={<DeleteIcon />}\n                    label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                    color=\"warning\"\n                    variant=\"outlined\"\n                    size=\"small\"\n                  />\n                )}\n              </Stack>\n            </Box>\n          </CardContent>\n        </Card>\n      </Fade>\n\n      {/* 操作按钮区域 */}\n      <Slide direction=\"up\" in timeout={800}>\n        <Card sx={{ mb: 3 }}>\n          <CardContent>\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n              操作选项\n            </Typography>\n            <Stack\n              direction={{ xs: 'column', sm: 'row' }}\n              spacing={2}\n              sx={{ flexWrap: 'wrap' }}\n            >\n              <Button\n                variant=\"contained\"\n                color=\"success\"\n                startIcon={<DownloadIcon />}\n                onClick={handleDownload}\n                sx={{\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1.5,\n                  fontWeight: 600,\n                  background: `linear-gradient(45deg, ${theme.palette.success.main} 30%, ${theme.palette.success.light} 90%)`,\n                  '&:hover': {\n                    background: `linear-gradient(45deg, ${theme.palette.success.dark} 30%, ${theme.palette.success.main} 90%)`,\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n              >\n                下载Excel\n              </Button>\n\n              <Button\n                variant=\"contained\"\n                color=\"primary\"\n                startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n                onClick={generateDocument}\n                disabled={isGeneratingDocument}\n                sx={{\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1.5,\n                  fontWeight: 600,\n                  background: isGeneratingDocument\n                    ? 'grey.400'\n                    : `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n                  '&:hover': {\n                    background: isGeneratingDocument\n                      ? 'grey.400'\n                      : `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.secondary.dark} 90%)`,\n                    transform: isGeneratingDocument ? 'none' : 'translateY(-2px)',\n                    boxShadow: isGeneratingDocument ? 'none' : '0 8px 25px rgba(0,0,0,0.15)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n              >\n                {isGeneratingDocument ? '生成中...' : '生成文档'}\n              </Button>\n\n              <Divider orientation=\"vertical\" flexItem sx={{ display: { xs: 'none', sm: 'block' } }} />\n\n              <Button\n                variant=\"outlined\"\n                startIcon={<RestartAltIcon />}\n                onClick={handleCleanup}\n                sx={{\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1.5,\n                  fontWeight: 600,\n                  borderColor: alpha(theme.palette.error.main, 0.5),\n                  color: 'error.main',\n                  '&:hover': {\n                    borderColor: 'error.main',\n                    backgroundColor: alpha(theme.palette.error.main, 0.05),\n                    transform: 'translateY(-2px)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n              >\n                重新开始\n              </Button>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Slide>\n      \n      {/* 数据表格 */}\n      <Fade in timeout={1000}>\n        <Paper\n          sx={{\n            width: '100%',\n            overflow: 'hidden',\n            borderRadius: 3,\n            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',\n            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          }}\n        >\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              rows={memoGridData}\n              columns={columns}\n              pageSize={100}\n              rowsPerPageOptions={[100, 200, 500]}\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow, oldRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                border: 'none',\n                '& .MuiDataGrid-cell': {\n                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`,\n                  fontSize: '0.875rem',\n                  padding: '12px 16px',\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  '&:focus': {\n                    outline: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,\n                    outlineOffset: '-2px',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.08)} 0%, ${alpha(theme.palette.secondary.main, 0.04)} 100%)`,\n                  borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n                  '& .MuiDataGrid-columnHeader': {\n                    fontWeight: 700,\n                    fontSize: '0.875rem',\n                    color: theme.palette.text.primary,\n                    padding: '0 16px',\n                    whiteSpace: 'normal',\n                    lineHeight: 'normal',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 700,\n                  whiteSpace: 'nowrap',\n                  overflow: 'visible',\n                  lineHeight: '24px',\n                },\n                '& .total-row': {\n                  background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.15)} 0%, ${alpha(theme.palette.success.light, 0.1)} 100%)`,\n                  fontWeight: 700,\n                  '& .MuiDataGrid-cell': {\n                    borderBottom: `2px solid ${theme.palette.success.main}`,\n                    color: theme.palette.success.dark,\n                    fontSize: '0.9rem',\n                  },\n                },\n                '& .removed-row': {\n                  background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)} 0%, ${alpha(theme.palette.error.light, 0.05)} 100%)`,\n                  opacity: 0.7,\n                  textDecoration: 'line-through',\n                  '& .MuiDataGrid-cell': {\n                    color: theme.palette.text.disabled,\n                    borderBottom: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,\n                  },\n                },\n                '& .MuiDataGrid-row': {\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.primary.main, 0.04),\n                    transition: 'background-color 0.2s ease',\n                  },\n                },\n                '& .MuiDataGrid-footerContainer': {\n                  borderTop: `2px solid ${alpha(theme.palette.divider, 0.3)}`,\n                  backgroundColor: alpha(theme.palette.background.default, 0.5),\n                },\n                '& .MuiDataGrid-virtualScroller': {\n                  overflowX: 'visible !important',\n                },\n                '& .MuiDataGrid-main': {\n                  overflow: 'visible',\n                },\n                '& .MuiDataGrid-root': {\n                  overflow: 'visible',\n                  border: 'none',\n                },\n                minHeight: 500,\n                borderRadius: 3,\n              }}\n            />\n          </Box>\n        </Paper>\n      </Fade>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOH,OAAA,CAACrB,KAAK;IAAA,GAAKwB,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAG5D,KAAK,CAAC6D,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC;IACrC+D,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA/D,SAAS,CAAC,MAAM;IACdkE,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEd,OAAA,CAACzB,OAAO;IAAC+C,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,EAAG;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eAC3EzB,OAAA,CAAC1B,IAAI;MAEHoD,KAAK,EAAER,OAAO,CAACH,IAAK;MACpBY,KAAK,EAAET,OAAO,CAACF,UAAU,GAAG,SAAS,GAAG,SAAU;MAClDY,OAAO,EAAEV,OAAO,CAACF,UAAU,GAAG,QAAQ,GAAG,UAAW;MACpDa,IAAI,EAAC,OAAO;MACZZ,OAAO,EAAEG,WAAY;MACrBU,SAAS;MACTC,EAAE,EAAE;QACFC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,sBAAsB;QAClC,kBAAkB,EAAE;UAClBC,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE,QAAQ;UACpBC,OAAO,EAAE;QACX;MACF;IAAE,GAjBG,UAAUxB,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAkB7C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC,kCAAC;AAAC+B,GAAA,GA5CG7B,UAAU;AA8ChB,MAAM8B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF,MAAMC,KAAK,GAAGnE,QAAQ,CAAC,CAAC;EACxB;EACA,MAAMoE,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvG,QAAQ,CAAC,MAAM;IACzD,MAAMwG,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGvD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC+G,eAAe,EAAEC,kBAAkB,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACmH,YAAY,EAAEC,eAAe,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdwG,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAG9B,IAAI,CAAC+B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9H,QAAQ,CAAC,MAAM;IAC7C+H,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7CnC,aAAa,GAAG,IAAIA,aAAa,CAACoC,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIpC,aAAa,IAAIA,aAAa,CAACoC,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGrC,aAAa,CAAC2B,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGtI,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMuI,iBAAiB,GAAGvI,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMwI,gBAAgB,GAAGxI,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMyI,mBAAmB,GAAGzI,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM0I,UAAU,GAAIpD,IAAI,IAAKA,IAAI,CAAC+B,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA7I,SAAS,CAAC,MAAM;IACd,IAAI6F,YAAY,IAAI+B,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGtC,IAAI,CAACW,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BC,YAAY,CAACT,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CZ,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtCjD,YAAY,CAAC,CAAC,GAAG+B,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5BC,YAAY,CAACT,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAE/B,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAEwD;EAAgB,CAAC,GAAGxG,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACyG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxJ,QAAQ,CAAC;IACjDyJ,IAAI,EAAE,KAAK;IACX3F,KAAK,EAAE,IAAI;IACX4F,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAzJ,SAAS,CAAC,MAAM;IACd,IAAIkH,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAGzJ,WAAW,CAAC,CAAC0J,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAIhB,mBAAmB,CAACO,OAAO,EAAE,EAAE;IAE5EG,eAAe,CAACM,OAAO,EAAE;MACvBhF,OAAO,EAAEiF,QAAQ;MACjB;MACAE,GAAG,EAAED,SAAS;MACd/E,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClBiF,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTL,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,WAAW,GAAG,GAAGxH,OAAO,aAAa8C,MAAM,EAAE;MACnD,MAAM2E,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BV,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BnF,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMoF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMrI,KAAK,CAACsI,MAAM,CAAC,GAAGrI,OAAO,YAAY8C,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOqF,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEApF,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMuF,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAGlL,WAAW,CAAC,CAACuF,IAAI,EAAE4F,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAG7F,IAAI,IAAIoC,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC0D,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD8D,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAK;MACpB,IAAI4D,UAAU,IAAI5D,GAAG,CAACa,EAAE,KAAK+C,UAAU,CAAC/C,EAAE,EAAE;QAC1C,OAAOqD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACvC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO6C,GAAG,IAAIC,MAAM,CAACnE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgE,gBAAgB,GAAG3L,WAAW,CAAEuF,IAAI,IAAK;IAC7C,MAAMqG,QAAQ,GAAGrG,IAAI,CAACsG,IAAI,CAACtE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIsD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGvG,IAAI,CAClBgG,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD8D,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAKkE,GAAG,IAAIC,MAAM,CAACnE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DgD,QAAQ,CAAChD,UAAU,GAAGkD,QAAQ;IAChC;IACA,OAAOvG,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwG,gBAAgB,GAAG/L,WAAW,CAAEgM,MAAM,IAAK;IAC/CpE,WAAW,CAACqE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGhB,kBAAkB,CAACe,IAAI,EAAED,MAAM,CAAC;MACjD,OAAOC,IAAI,CAAC3E,GAAG,CAACC,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACa,EAAE,KAAK4D,MAAM,CAAC5D,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAGyE;QAAO,CAAC;QACtD,IAAIzE,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAEsD;QAAW,CAAC;QACjE,OAAO3E,GAAG;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOyE,MAAM;EACf,CAAC,EAAE,CAACd,kBAAkB,CAAC,CAAC;EAExB,MAAMiB,uBAAuB,GAAItB,KAAK,IAAK;IACzCpB,YAAY,CAAC,SAASoB,KAAK,CAACnB,OAAO,EAAE,EAAE,OAAO,CAAC;EACjD,CAAC;;EAED;EACA,MAAM0C,iBAAiB,GAAGvM,KAAK,CAACG,WAAW,CAAC,CAAC4D,KAAK,EAAE4F,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACV3F,KAAK;MACL4F;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,kBAAkB,GAAGrM,WAAW,CAAC,MAAM;IAC3CsJ,gBAAgB,CAAC2C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP1C,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,kBAAkB,GAAGtM,WAAW,CAAEuM,MAAM,IAAK;IACjD,MAAM;MAAE3I;IAAM,CAAC,GAAGyF,aAAa;IAC/B,IAAIzF,KAAK,KAAK,IAAI,EAAE;MAClB;MACAyI,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC7E,WAAW,CAAC8E,QAAQ,IAAI;UACtB,IAAIC,WAAW,GAAGD,QAAQ,CAACpF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKxE,KAAK,EAAE;cACpB,IAAI2I,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGhF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGF,GAAG;kBAAEC,OAAO,EAAE+E,MAAM;kBAAE9E,iBAAiB,EAAE8E;gBAAO,CAAC;cAC/D;YACF;YACA,OAAOhF,GAAG;UACZ,CAAC,CAAC;UAEFoF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAxD,UAAU,CAAC,MAAM;UACfM,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEgD,kBAAkB,EAAEV,gBAAgB,EAAElC,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAMmD,mBAAmB,GAAG5M,WAAW,CAAC,MAAM;IAC5C8G,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+F,oBAAoB,GAAG7M,WAAW,CAAC,MAAM;IAC7C8G,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkG,YAAY,GAAG9M,WAAW,CAAC,MAAM;IACrC,IAAI2G,SAAS,CAACoG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC3G,cAAc,CAAC4G,QAAQ,CAACrG,SAAS,CAACoG,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE1G,iBAAiB,CAAC4F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEtF,SAAS,CAACoG,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDtD,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjCoD,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIzG,cAAc,CAAC4G,QAAQ,CAACrG,SAAS,CAACoG,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDtD,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAAC9C,SAAS,EAAEP,cAAc,EAAEyG,oBAAoB,EAAEpD,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAMwD,YAAY,GAAGjN,WAAW,CAAEuM,MAAM,IAAK;IAC3ClG,iBAAiB,CAAC4F,IAAI,IAAIA,IAAI,CAACV,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAKX,MAAM,CAAC,CAAC;IAC/D9C,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0D,eAAe,GAAGnN,WAAW,CAAEoI,EAAE,IAAK;IAC1CR,WAAW,CAACqE,IAAI,IAAI;MAClB,IAAIU,WAAW,GAAGV,IAAI,CAAC3E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;MACnFoF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;MAC3C,MAAMS,cAAc,GAAGT,WAAW,CAACpB,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH8E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClF,EAAE,GAAGmF,CAAC,CAACnF,EAAE,CAAC;MAC1CgF,cAAc,CAACI,OAAO,CAAC,CAACjG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOwE,WAAW;IACpB,CAAC,CAAC;IAEFlD,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAACkC,gBAAgB,EAAElC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMgE,aAAa,GAAGzN,WAAW,CAAEoI,EAAE,IAAK;IACxCR,WAAW,CAACqE,IAAI,IAAI;MAClB,IAAIU,WAAW,GAAGV,IAAI,CAAC3E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpFoF,WAAW,GAAGhB,gBAAgB,CAACgB,WAAW,CAAC;MAC3C,MAAMS,cAAc,GAAGT,WAAW,CAACpB,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnH8E,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClF,EAAE,GAAGmF,CAAC,CAACnF,EAAE,CAAC;MAC1CgF,cAAc,CAACI,OAAO,CAAC,CAACjG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOwE,WAAW;IACpB,CAAC,CAAC;IACFxD,UAAU,CAAC,MAAM;MACfM,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACkC,gBAAgB,EAAElC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMiE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF1G,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM2G,YAAY,GAAG,CAAChG,QAAQ,IAAI,EAAE,EACjC4D,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAiG,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAChF,EAAE,KAAK,QAAQ,IAAI,OAAOiF,CAAC,CAACjF,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOgF,CAAC,CAAChF,EAAE,GAAGiF,CAAC,CAACjF,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMsF,OAAO,GAAGD,YAAY,CAACrG,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb0F,IAAI,EAAEtG,GAAG,CAACsG,IAAI,GAAI,OAAOtG,GAAG,CAACsG,IAAI,KAAK,QAAQ,IAAItG,GAAG,CAACsG,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGzF,GAAG,CAACsG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGvG,GAAG,CAACsG,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEtG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGwG,IAAI,CAACC,KAAK,CAACzG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF0G,EAAE,EAAE,OAAO1G,GAAG,CAAC0G,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACzG,GAAG,CAAC0G,EAAE,CAAC,GAAG1G,GAAG,CAAC0G,EAAE,IAAI,EAAE;QAClEzG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGyG,KAAK,EAAE,OAAO3G,GAAG,CAAC4G,QAAQ,KAAK,QAAQ,GACpC5G,GAAG,CAAC4G,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG5G,GAAG,CAAC4G,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG7G,GAAG,CAAC4G,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3E7G,GAAG,CAAC4G,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAO9G,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAACwF,OAAO,CAAC,CAAC,CAAC,GAAG7G,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM0F,WAAW,GAAG,CAAC3G,QAAQ,IAAI,EAAE,EAChC4D,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE4C,MAAM,CAAC,CAACC,GAAG,EAAElE,GAAG,KAAKkE,GAAG,IAAI,OAAOlE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM2F,QAAQ,GAAG,MAAM9L,KAAK,CAAC+L,IAAI,CAAC,GAAG9L,OAAO,oBAAoB,EAAE;QAChE6C,IAAI,EAAEqI,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC5I,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI+I,QAAQ,CAAChJ,IAAI,IAAIgJ,QAAQ,CAAChJ,IAAI,CAACkJ,KAAK,EAAE;QACxC;QACA,MAAMvE,WAAW,GAAG,GAAGxH,OAAO,CAACoL,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAChJ,IAAI,CAACmJ,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAjF,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACAN,UAAU,CAAC,MAAM;UACf,MAAMwF,MAAM,GAAGvE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CsE,MAAM,CAACC,KAAK,CAACxJ,OAAO,GAAG,MAAM;UAC7BuJ,MAAM,CAACE,GAAG,GAAG3E,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACiE,MAAM,CAAC;UACjCxF,UAAU,CAAC,MAAM;YACfiB,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC+D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACRzC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM+H,kBAAkB,GAAG/O,WAAW,CAAC,CAAC4D,KAAK,EAAEoL,KAAK,KAAK;IACvD5C,iBAAiB,CAACxI,KAAK,EAAEoL,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC5C,iBAAiB,CAAC,CAAC;EAEvB,MAAM6C,OAAO,GAAG/O,OAAO,CAAC,MAAO6F,WAAW,CAACuB,GAAG,CAAC4H,GAAG,IAAI;IACpD,IAAI,EAAEvH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACwH,cAAc,CAACD,GAAG,CAAClJ,KAAK,CAAC,CAAC,IAAIkJ,GAAG,CAAClJ,KAAK,KAAK,SAAS,IAAIkJ,GAAG,CAAClJ,KAAK,KAAK,QAAQ,IAAIkJ,GAAG,CAAClJ,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIkJ,GAAG,CAAClJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEkJ,GAAG,CAAClJ,KAAK;QAChBC,UAAU,EAAEiJ,GAAG,CAACjJ,UAAU;QAC1BmJ,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVnJ,QAAQ,EAAE,KAAK;QACfoJ,UAAU,EAAGrE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI2C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAM6H,iBAAiB,GAAGtE,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE3E,OAAA,CAACzB,OAAO;cAAC+C,KAAK,EAAE6G,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACpD,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAC,QAAA,eACvEzB,OAAA,CAAC1B,IAAI;gBACHoD,KAAK,EAAE+K,iBAAkB;gBACzB9K,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC,OAAO;gBACZE,EAAE,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAE0K,OAAO,EAAE,GAAG;kBAAExK,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAImM,UAAU,GAAG,MAAM;UACvB,IAAI3L,UAAU,GAAG,KAAK;UAEtB,IAAImH,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,IAAIwD,MAAM,CAAC1D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EgI,UAAU,GAAGxE,MAAM,CAAC1D,GAAG,CAACE,iBAAiB;YACzC3D,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEhB,OAAA,CAACU,UAAU;YACTI,KAAK,EAAEqH,MAAM,CAAC1D,GAAG,CAACa,EAAG;YACrBvE,IAAI,EAAE4L,UAAW;YACjB3L,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEgL;UAAmB;YAAA5L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI4L,GAAG,CAAClJ,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEkJ,GAAG,CAAClJ,KAAK;QAChBC,UAAU,EAAEiJ,GAAG,CAACjJ,UAAU;QAC1BmJ,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVnJ,QAAQ,EAAE,KAAK;QACfoJ,UAAU,EAAGrE,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI2C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACE5E,OAAA,CAAC1B,IAAI;cAEHoD,KAAK,EAAC,cAAI;cACVC,KAAK,EAAC,SAAS;cACfE,IAAI,EAAC,OAAO;cACZ+K,IAAI,eAAE5M,OAAA,CAACV,QAAQ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBS,OAAO,EAAEA,CAAA,KAAM0J,aAAa,CAACxC,MAAM,CAAC1D,GAAG,CAACa,EAAE,CAAE;cAC5CvD,EAAE,EAAE;gBAAEE,MAAM,EAAE;cAAU;YAAE,GANtB,MAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOX,CAAC;UAEN;UACA,oBACER,OAAA,CAAC1B,IAAI;YAEHoD,KAAK,EAAC,cAAI;YACVC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZ+K,IAAI,eAAE5M,OAAA,CAACX,UAAU;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBS,OAAO,EAAEA,CAAA,KAAMoJ,eAAe,CAAClC,MAAM,CAAC1D,GAAG,CAACa,EAAE,CAAE;YAC9CvD,EAAE,EAAE;cAAEE,MAAM,EAAE;YAAU;UAAE,GANtB,QAAQ;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOb,CAAC;QAEN;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG4L,GAAG;MACNhJ,QAAQ,EAAE+E,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI2C,MAAM,CAAC1D,GAAG,IAAI0D,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOwH,GAAG,CAAChJ,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACDoJ,UAAU,EAAGrE,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC1D,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI4G,GAAG,CAAClJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACElD,OAAA,CAAC1C,UAAU;YAACsE,OAAO,EAAC,OAAO;YAACiL,UAAU,EAAC,MAAM;YAAClL,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAO0G,MAAM,CAAC+D,KAAK,KAAK,QAAQ,GAAG/D,MAAM,CAAC+D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOnD,MAAM,CAAC+D,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAClE,MAAM,CAACT,MAAM,CAAC+D,KAAK,CAAC,CAAC,GAAGtD,MAAM,CAACT,MAAM,CAAC+D,KAAK,CAAC,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGnD,MAAM,CAAC+D;UAAK;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI2H,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACE5E,OAAA,CAAC1C,UAAU;YAACsE,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,eAAe;YAACI,EAAE,EAAE;cAAEgL,cAAc,EAAE;YAAe,CAAE;YAAAtL,QAAA,EACtF0G,MAAM,CAAC+D;UAAK;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI4L,GAAG,CAAClJ,KAAK,KAAK,MAAM,IAAIiF,MAAM,CAAC+D,KAAK,EAAE;UACxC,OAAO/D,MAAM,CAAC+D,KAAK,CAAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIoB,GAAG,CAAClJ,KAAK,KAAK,IAAI,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAAC/C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAClJ,KAAK,KAAK,OAAO,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOjB,IAAI,CAACC,KAAK,CAAC/C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAClJ,KAAK,KAAK,IAAI,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOjB,IAAI,CAACC,KAAK,CAAC/C,MAAM,CAAC+D,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAAClJ,KAAK,KAAK,UAAU,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO/D,MAAM,CAAC+D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG/D,MAAM,CAAC+D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGnD,MAAM,CAAC+D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIc,GAAG,CAAClJ,KAAK,KAAK,YAAY,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO/D,MAAM,CAAC+D,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIc,GAAG,CAAClJ,KAAK,KAAK,YAAY,IAAI,OAAOiF,MAAM,CAAC+D,KAAK,KAAK,QAAQ,IAAI,CAACY,KAAK,CAAClE,MAAM,CAACT,MAAM,CAAC+D,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOtD,MAAM,CAACT,MAAM,CAAC+D,KAAK,CAAC,CAACZ,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOnD,MAAM,CAAC+D,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO/D,MAAM,CAAC+D,KAAK;QACrB;QACA,OAAO/D,MAAM,CAAC+D,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACzD,MAAM,CAACuE,OAAO,CAAE,EAAE,CAAC/J,WAAW,EAAE4B,QAAQ,EAAEoH,kBAAkB,EAAE5B,eAAe,EAAEM,aAAa,CAAC,CAAC;;EAEjG;EACA,MAAMsC,YAAY,GAAG7P,OAAO,CAAC,MAAMyH,QAAQ,IAAI,EAAE,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAE9D;EACA,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEjF,OAAA,CAAC3C,GAAG;MAAC0E,EAAE,EAAE;QAAEmL,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA1L,QAAA,gBACtCzB,OAAA,CAAC1C,UAAU;QAACsE,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAACzC,MAAM;QACLqE,OAAO,EAAC,WAAW;QACnBX,OAAO,EAAE0B,OAAQ;QACjBZ,EAAE,EAAE;UAAEqL,EAAE,EAAE;QAAE,CAAE;QAAA3L,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACER,OAAA,CAAC3C,GAAG;IAAAoE,QAAA,gBAEFzB,OAAA,CAACtB,IAAI;MAAC2O,EAAE;MAACC,OAAO,EAAE,GAAI;MAAA7L,QAAA,eACpBzB,OAAA,CAACxB,IAAI;QACHuD,EAAE,EAAE;UACFwL,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,2BAA2B1O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQ7O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACG,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC,QAAQ;UACvI1G,MAAM,EAAE,aAAanI,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;QAC7D,CAAE;QAAAlM,QAAA,eAEFzB,OAAA,CAACvB,WAAW;UAAAgD,QAAA,eACVzB,OAAA,CAAC3C,GAAG;YAAC0E,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEuL,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE,eAAe;cAAEC,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAvM,QAAA,gBAC5GzB,OAAA,CAAC3C,GAAG;cAAC0E,EAAE,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAEuL,UAAU,EAAE,QAAQ;gBAAEG,GAAG,EAAE;cAAE,CAAE;cAAAvM,QAAA,gBACzDzB,OAAA,CAAC3C,GAAG;gBACF0E,EAAE,EAAE;kBACFkM,CAAC,EAAE,GAAG;kBACNjH,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,0BAA0BxK,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,KAAK3K,KAAK,CAACyK,OAAO,CAACG,SAAS,CAACD,IAAI;gBACnG,CAAE;gBAAAlM,QAAA,eAEFzB,OAAA,CAACR,cAAc;kBAACuC,EAAE,EAAE;oBAAEJ,KAAK,EAAE,OAAO;oBAAEuM,QAAQ,EAAE;kBAAG;gBAAE;kBAAA7N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNR,OAAA,CAAC3C,GAAG;gBAAAoE,QAAA,gBACFzB,OAAA,CAAC1C,UAAU;kBACTsE,OAAO,EAAC,IAAI;kBACZG,EAAE,EAAE;oBACF8K,UAAU,EAAE,GAAG;oBACflL,KAAK,EAAE,cAAc;oBACrB4L,EAAE,EAAE;kBACN,CAAE;kBAAA9L,QAAA,EACH;gBAED;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbR,OAAA,CAAC1C,UAAU;kBACTsE,OAAO,EAAC,OAAO;kBACfG,EAAE,EAAE;oBACFJ,KAAK,EAAE;kBACT,CAAE;kBAAAF,QAAA,EACH;gBAED;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNR,OAAA,CAACjB,KAAK;cAACqB,SAAS,EAAC,KAAK;cAAC+N,OAAO,EAAE,CAAE;cAACpM,EAAE,EAAE;gBAAEgM,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAvM,QAAA,gBAClEzB,OAAA,CAAC1B,IAAI;gBACHsO,IAAI,eAAE5M,OAAA,CAACP,aAAa;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBkB,KAAK,EAAE,GAAG,CAACuL,YAAY,IAAI,EAAE,EAAExE,MAAM,CAAChE,GAAG,IAAI,CAACA,GAAG,CAACc,OAAO,IAAI,CAACd,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,MAAO;gBACzFtD,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACFR,OAAA,CAAC1B,IAAI;gBACHsO,IAAI,eAAE5M,OAAA,CAACN,cAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBkB,KAAK,EAAE,WAAW0G,kBAAkB,CAAC6E,YAAY,CAAC,CAAC3B,OAAO,CAAC,CAAC,CAAC,EAAG;gBAChE3J,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EACD,CAACyM,YAAY,IAAI,EAAE,EAAExE,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,iBAC1DjF,OAAA,CAAC1B,IAAI;gBACHsO,IAAI,eAAE5M,OAAA,CAACX,UAAU;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBkB,KAAK,EAAE,GAAG,CAACuL,YAAY,IAAI,EAAE,EAAExE,MAAM,CAAChE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,OAAQ;gBACzEtD,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,UAAU;gBAClBC,IAAI,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPR,OAAA,CAACrB,KAAK;MAACyB,SAAS,EAAC,IAAI;MAACiN,EAAE;MAACC,OAAO,EAAE,GAAI;MAAA7L,QAAA,eACpCzB,OAAA,CAACxB,IAAI;QAACuD,EAAE,EAAE;UAAEwL,EAAE,EAAE;QAAE,CAAE;QAAA9L,QAAA,eAClBzB,OAAA,CAACvB,WAAW;UAAAgD,QAAA,gBACVzB,OAAA,CAAC1C,UAAU;YAACsE,OAAO,EAAC,WAAW;YAACG,EAAE,EAAE;cAAE8K,UAAU,EAAE,GAAG;cAAEU,EAAE,EAAE;YAAE,CAAE;YAAA9L,QAAA,EAAC;UAEhE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbR,OAAA,CAACjB,KAAK;YACJqB,SAAS,EAAE;cAAEgO,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAM,CAAE;YACvCF,OAAO,EAAE,CAAE;YACXpM,EAAE,EAAE;cAAEgM,QAAQ,EAAE;YAAO,CAAE;YAAAtM,QAAA,gBAEzBzB,OAAA,CAACzC,MAAM;cACLqE,OAAO,EAAC,WAAW;cACnBD,KAAK,EAAC,SAAS;cACf2M,SAAS,eAAEtO,OAAA,CAACd,YAAY;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BS,OAAO,EAAEkG,cAAe;cACxBpF,EAAE,EAAE;gBACFiF,YAAY,EAAE,CAAC;gBACfuH,EAAE,EAAE,CAAC;gBACLpB,EAAE,EAAE,GAAG;gBACPN,UAAU,EAAE,GAAG;gBACfW,UAAU,EAAE,0BAA0BxK,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACb,IAAI,SAAS3K,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACC,KAAK,OAAO;gBAC3G,SAAS,EAAE;kBACTjB,UAAU,EAAE,0BAA0BxK,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACE,IAAI,SAAS1L,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACb,IAAI,OAAO;kBAC1GgB,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb,CAAC;gBACD1M,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EACH;YAED;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETR,OAAA,CAACzC,MAAM;cACLqE,OAAO,EAAC,WAAW;cACnBD,KAAK,EAAC,SAAS;cACf2M,SAAS,EAAErK,oBAAoB,gBAAGjE,OAAA,CAAC3B,gBAAgB;gBAACwD,IAAI,EAAE,EAAG;gBAACF,KAAK,EAAC;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGR,OAAA,CAACT,gBAAgB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxGS,OAAO,EAAE2J,gBAAiB;cAC1BiE,QAAQ,EAAE5K,oBAAqB;cAC/BlC,EAAE,EAAE;gBACFiF,YAAY,EAAE,CAAC;gBACfuH,EAAE,EAAE,CAAC;gBACLpB,EAAE,EAAE,GAAG;gBACPN,UAAU,EAAE,GAAG;gBACfW,UAAU,EAAEvJ,oBAAoB,GAC5B,UAAU,GACV,0BAA0BjB,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,SAAS3K,KAAK,CAACyK,OAAO,CAACG,SAAS,CAACD,IAAI,OAAO;gBACpG,SAAS,EAAE;kBACTH,UAAU,EAAEvJ,oBAAoB,GAC5B,UAAU,GACV,0BAA0BjB,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACgB,IAAI,SAAS1L,KAAK,CAACyK,OAAO,CAACG,SAAS,CAACc,IAAI,OAAO;kBACpGC,SAAS,EAAE1K,oBAAoB,GAAG,MAAM,GAAG,kBAAkB;kBAC7D2K,SAAS,EAAE3K,oBAAoB,GAAG,MAAM,GAAG;gBAC7C,CAAC;gBACD/B,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAEDwC,oBAAoB,GAAG,QAAQ,GAAG;YAAM;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAETR,OAAA,CAAChB,OAAO;cAAC8P,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAChN,EAAE,EAAE;gBAAEO,OAAO,EAAE;kBAAE8L,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ;cAAE;YAAE;cAAAhO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEzFR,OAAA,CAACzC,MAAM;cACLqE,OAAO,EAAC,UAAU;cAClB0M,SAAS,eAAEtO,OAAA,CAACb,cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9BS,OAAO,EAAE+G,aAAc;cACvBjG,EAAE,EAAE;gBACFiF,YAAY,EAAE,CAAC;gBACfuH,EAAE,EAAE,CAAC;gBACLpB,EAAE,EAAE,GAAG;gBACPN,UAAU,EAAE,GAAG;gBACf3F,WAAW,EAAEpI,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAAC1F,KAAK,CAAC4F,IAAI,EAAE,GAAG,CAAC;gBACjDhM,KAAK,EAAE,YAAY;gBACnB,SAAS,EAAE;kBACTuF,WAAW,EAAE,YAAY;kBACzB8H,eAAe,EAAElQ,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAAC1F,KAAK,CAAC4F,IAAI,EAAE,IAAI,CAAC;kBACtDgB,SAAS,EAAE;gBACb,CAAC;gBACDzM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EACH;YAED;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRR,OAAA,CAACtB,IAAI;MAAC2O,EAAE;MAACC,OAAO,EAAE,IAAK;MAAA7L,QAAA,eACrBzB,OAAA,CAACxC,KAAK;QACJuE,EAAE,EAAE;UACFwK,KAAK,EAAE,MAAM;UACbpK,QAAQ,EAAE,QAAQ;UAClB6E,YAAY,EAAE,CAAC;UACf4H,SAAS,EAAE,6BAA6B;UACxC3H,MAAM,EAAE,aAAanI,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;QAC7D,CAAE;QAAAlM,QAAA,eAEFzB,OAAA,CAAC3C,GAAG;UAAC0E,EAAE,EAAE;YAAEkN,MAAM,EAAE,MAAM;YAAE1C,KAAK,EAAE;UAAO,CAAE;UAAA9K,QAAA,eACzCzB,OAAA,CAACf,QAAQ;YACPiQ,IAAI,EAAEjC,YAAa;YACnBd,OAAO,EAAEA,OAAQ;YACjBgD,QAAQ,EAAE,GAAI;YACdC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;YACpCC,uBAAuB;YACvBC,YAAY,EAAE,EAAG;YACjBC,kBAAkB,EAAE,EAAG;YACvBC,eAAe,EAAGrH,MAAM,IAAK;cAC3B,IAAIA,MAAM,CAAC1D,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;cAC1C,IAAI4C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;cAC7C,OAAO,EAAE;YACX,CAAE;YACF6K,cAAc,EAAGtH,MAAM,IAAK;cAC1B,IAAIA,MAAM,CAAC1D,GAAG,CAACc,OAAO,IAAI4C,MAAM,CAAC1D,GAAG,CAACG,QAAQ,EAAE;gBAC7C,OAAO,KAAK;cACd;cACA,OAAOuD,MAAM,CAACuH,MAAM,CAACtM,QAAQ,IAAI,OAAO+E,MAAM,CAACuH,MAAM,CAACtM,QAAQ,KAAK,UAAU,GAC3E+E,MAAM,CAACuH,MAAM,CAACtM,QAAQ,CAAC+E,MAAM,CAAC,GAAGA,MAAM,CAACuH,MAAM,CAACtM,QAAQ;YAC3D,CAAE;YACF6F,gBAAgB,EAAEA,CAACC,MAAM,EAAEyG,MAAM,KAAK;cACpC,IAAIzG,MAAM,CAACpD,UAAU,KAAKX,SAAS,EAAE;gBACnC,IAAI,OAAO+D,MAAM,CAACpD,UAAU,KAAK,QAAQ,EAAE;kBACzCoD,MAAM,CAACpD,UAAU,GAAG8C,MAAM,CAACM,MAAM,CAACpD,UAAU,CAAC,IAAI,CAAC;gBACpD;cACF;cACA,OAAOmD,gBAAgB,CAACC,MAAM,CAAC;YACjC,CAAE;YACFG,uBAAuB,EAAEA,uBAAwB;YACjDtH,EAAE,EAAE;cACFkF,MAAM,EAAE,MAAM;cACd,qBAAqB,EAAE;gBACrB2I,YAAY,EAAE,aAAa9Q,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACoC,OAAO,EAAE,GAAG,CAAC,EAAE;gBAC9D3B,QAAQ,EAAE,UAAU;gBACpB4B,OAAO,EAAE,WAAW;gBACpBzN,UAAU,EAAE,QAAQ;gBACpB0N,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE;kBACTC,OAAO,EAAE,aAAalR,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC,EAAE;kBAC9DsC,aAAa,EAAE;gBACjB;cACF,CAAC;cACD,8BAA8B,EAAE;gBAC9BzC,UAAU,EAAE,2BAA2B1O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQ7O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACG,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACvIiC,YAAY,EAAE,aAAa9Q,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC,EAAE;gBACnE,6BAA6B,EAAE;kBAC7Bd,UAAU,EAAE,GAAG;kBACfqB,QAAQ,EAAE,UAAU;kBACpBvM,KAAK,EAAEqB,KAAK,CAACyK,OAAO,CAAC1M,IAAI,CAAC2M,OAAO;kBACjCoC,OAAO,EAAE,QAAQ;kBACjBzN,UAAU,EAAE,QAAQ;kBACpB0N,UAAU,EAAE;gBACd;cACF,CAAC;cACD,kCAAkC,EAAE;gBAClClD,UAAU,EAAE,GAAG;gBACfxK,UAAU,EAAE,QAAQ;gBACpBF,QAAQ,EAAE,SAAS;gBACnB4N,UAAU,EAAE;cACd,CAAC;cACD,cAAc,EAAE;gBACdvC,UAAU,EAAE,2BAA2B1O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACb,IAAI,EAAE,IAAI,CAAC,QAAQ7O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACC,KAAK,EAAE,GAAG,CAAC,QAAQ;gBACrI5B,UAAU,EAAE,GAAG;gBACf,qBAAqB,EAAE;kBACrB+C,YAAY,EAAE,aAAa5M,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACb,IAAI,EAAE;kBACvDhM,KAAK,EAAEqB,KAAK,CAACyK,OAAO,CAACe,OAAO,CAACE,IAAI;kBACjCR,QAAQ,EAAE;gBACZ;cACF,CAAC;cACD,gBAAgB,EAAE;gBAChBV,UAAU,EAAE,2BAA2B1O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAAC1F,KAAK,CAAC4F,IAAI,EAAE,GAAG,CAAC,QAAQ7O,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAAC1F,KAAK,CAAC0G,KAAK,EAAE,IAAI,CAAC,QAAQ;gBACjI/B,OAAO,EAAE,GAAG;gBACZK,cAAc,EAAE,cAAc;gBAC9B,qBAAqB,EAAE;kBACrBpL,KAAK,EAAEqB,KAAK,CAACyK,OAAO,CAAC1M,IAAI,CAAC8N,QAAQ;kBAClCe,YAAY,EAAE,aAAa9Q,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAAC1F,KAAK,CAAC4F,IAAI,EAAE,GAAG,CAAC;gBACjE;cACF,CAAC;cACD,oBAAoB,EAAE;gBACpB,SAAS,EAAE;kBACTqB,eAAe,EAAElQ,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC;kBACxDzL,UAAU,EAAE;gBACd;cACF,CAAC;cACD,gCAAgC,EAAE;gBAChCgO,SAAS,EAAE,aAAapR,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACoC,OAAO,EAAE,GAAG,CAAC,EAAE;gBAC3Db,eAAe,EAAElQ,KAAK,CAACkE,KAAK,CAACyK,OAAO,CAACD,UAAU,CAAC2C,OAAO,EAAE,GAAG;cAC9D,CAAC;cACD,gCAAgC,EAAE;gBAChCC,SAAS,EAAE;cACb,CAAC;cACD,qBAAqB,EAAE;gBACrBjO,QAAQ,EAAE;cACZ,CAAC;cACD,qBAAqB,EAAE;gBACrBA,QAAQ,EAAE,SAAS;gBACnB8E,MAAM,EAAE;cACV,CAAC;cACDoJ,SAAS,EAAE,GAAG;cACdrJ,YAAY,EAAE;YAChB;UAAE;YAAA3G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPR,OAAA,CAACrC,MAAM;MACL8I,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB6J,OAAO,EAAE/G,kBAAmB;MAC5BgH,SAAS;MACTvO,QAAQ,EAAC,IAAI;MACbwO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlP,QAAA,gBAEnBzB,OAAA,CAACpC,WAAW;QAAA6D,QAAA,eACVzB,OAAA,CAAC3C,GAAG;UAAC0E,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEwL,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAApM,QAAA,gBAClFzB,OAAA,CAAC1C,UAAU;YAACsE,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CR,OAAA,CAACzC,MAAM;YACL+Q,SAAS,eAAEtO,OAAA,CAACZ,OAAO;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAE6I,mBAAoB;YAC7BnI,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdR,OAAA,CAACnC,aAAa;QAAC+S,QAAQ;QAAC7O,EAAE,EAAE;UAAEkM,CAAC,EAAE;QAAE,CAAE;QAAAxM,QAAA,eACnCzB,OAAA,CAACH,aAAa;UACZoP,MAAM,EAAE,GAAI;UACZ4B,SAAS,EAAEvN,cAAc,CAAC2B,MAAO;UACjC6L,QAAQ,EAAE,EAAG;UACbvE,KAAK,EAAC,MAAM;UAAA9K,QAAA,EAEXA,CAAC;YAAE4D,KAAK;YAAEyG;UAAM,CAAC,KAAK;YACrB,MAAMrC,MAAM,GAAGnG,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACErF,OAAA,CAAChC,QAAQ;cAEP8N,KAAK,EAAEA,KAAM;cACbiF,cAAc;cACdC,eAAe,eACbhR,OAAA,CAAC5B,UAAU;gBACT6S,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBhQ,OAAO,EAAEA,CAAA,KAAMkJ,YAAY,CAACV,MAAM,CAAE;gBAAAhI,QAAA,eAEpCzB,OAAA,CAACX,UAAU;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiB,QAAA,eAEDzB,OAAA,CAAC/B,cAAc;gBAACgD,OAAO,EAAEA,CAAA,KAAMuI,kBAAkB,CAACC,MAAM,CAAE;gBAAAhI,QAAA,eACxDzB,OAAA,CAAC9B,YAAY;kBAACwP,OAAO,EAAEjE;gBAAO;kBAAApJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZiJ,MAAM;cAAApJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBR,OAAA,CAAClC,aAAa;QAAA2D,QAAA,eACZzB,OAAA,CAACzC,MAAM;UAAC0D,OAAO,EAAEsI,kBAAmB;UAAA9H,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTR,OAAA,CAACrC,MAAM;MACL8I,IAAI,EAAE1C,eAAgB;MACtBuM,OAAO,EAAEvG,oBAAqB;MAC9BwG,SAAS;MACTvO,QAAQ,EAAC,IAAI;MACbwO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlP,QAAA,gBAEnBzB,OAAA,CAACpC,WAAW;QAAA6D,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCR,OAAA,CAACnC,aAAa;QAAA4D,QAAA,eACZzB,OAAA,CAAC7B,SAAS;UACR+S,SAAS;UACTC,MAAM,EAAC,OAAO;UACd7L,EAAE,EAAC,MAAM;UACT5D,KAAK,EAAC,0BAAM;UACZ0P,IAAI,EAAC,MAAM;UACXb,SAAS;UACT3O,OAAO,EAAC,UAAU;UAClBsK,KAAK,EAAErI,SAAU;UACjBwN,QAAQ,EAAGhQ,CAAC,IAAKyC,YAAY,CAACzC,CAAC,CAACiQ,MAAM,CAACpF,KAAK;QAAE;UAAA7L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBR,OAAA,CAAClC,aAAa;QAAA2D,QAAA,gBACZzB,OAAA,CAACzC,MAAM;UAAC0D,OAAO,EAAE8I,oBAAqB;UAAAtI,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDR,OAAA,CAACzC,MAAM;UAAC0D,OAAO,EAAE+I,YAAa;UAACrI,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACuC,GAAA,CAz8BIP,aAAa;EAAA,QACH3D,QAAQ,EAiIMiB,WAAW;AAAA;AAAAyR,GAAA,GAlInC/O,aAAa;AA28BnB,eAAeA,aAAa;AAAC,IAAA/B,EAAA,EAAAI,GAAA,EAAA0B,GAAA,EAAAgP,GAAA;AAAAC,YAAA,CAAA/Q,EAAA;AAAA+Q,YAAA,CAAA3Q,GAAA;AAAA2Q,YAAA,CAAAjP,GAAA;AAAAiP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}