{"ast": null, "code": "import { styled } from '@mui/material/styles';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 隐藏的文件输入样式\nexport const VisuallyHiddenInput = styled('input')({\n  clip: 'rect(0 0 0 0)',\n  clipPath: 'inset(50%)',\n  height: 1,\n  overflow: 'hidden',\n  position: 'absolute',\n  bottom: 0,\n  left: 0,\n  whiteSpace: 'nowrap',\n  width: 1\n});\n\n/**\r\n * 上传Excel文件\r\n * @param {File} file - 要上传的Excel文件\r\n * @returns {Promise} - 包含上传结果的Promise\r\n */\nexport const uploadExcelFile = async file => {\n  if (!file) {\n    throw new Error('请选择文件');\n  }\n  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\n    throw new Error('只支持Excel文件 (.xlsx, .xls)');\n  }\n  const formData = new FormData();\n  formData.append('file', file);\n  try {\n    const response = await axios.post(`${API_URL}/upload`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.data && error.response.data.error) {\n      throw new Error(error.response.data.error);\n    } else {\n      throw new Error('上传文件失败，请重试');\n    }\n  }\n};", "map": {"version": 3, "names": ["styled", "axios", "API_URL", "VisuallyHiddenInput", "clip", "clipPath", "height", "overflow", "position", "bottom", "left", "whiteSpace", "width", "uploadExcelFile", "file", "Error", "name", "endsWith", "formData", "FormData", "append", "response", "post", "headers", "data", "error"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/utils/fileUtils.js"], "sourcesContent": ["import { styled } from '@mui/material/styles';\r\nimport axios from 'axios';\r\nimport { API_URL } from '../config';\r\n\r\n// 隐藏的文件输入样式\r\nexport const VisuallyHiddenInput = styled('input')({\r\n  clip: 'rect(0 0 0 0)',\r\n  clipPath: 'inset(50%)',\r\n  height: 1,\r\n  overflow: 'hidden',\r\n  position: 'absolute',\r\n  bottom: 0,\r\n  left: 0,\r\n  whiteSpace: 'nowrap',\r\n  width: 1,\r\n});\r\n\r\n/**\r\n * 上传Excel文件\r\n * @param {File} file - 要上传的Excel文件\r\n * @returns {Promise} - 包含上传结果的Promise\r\n */\r\nexport const uploadExcelFile = async (file) => {\r\n  if (!file) {\r\n    throw new Error('请选择文件');\r\n  }\r\n\r\n  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\r\n    throw new Error('只支持Excel文件 (.xlsx, .xls)');\r\n  }\r\n\r\n  const formData = new FormData();\r\n  formData.append('file', file);\r\n\r\n  try {\r\n    const response = await axios.post(`${API_URL}/upload`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      throw new Error(error.response.data.error);\r\n    } else {\r\n      throw new Error('上传文件失败，请重试');\r\n    }\r\n  }\r\n};\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AACA,OAAO,MAAMC,mBAAmB,GAAGH,MAAM,CAAC,OAAO,CAAC,CAAC;EACjDI,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,UAAU,EAAE,QAAQ;EACpBC,KAAK,EAAE;AACT,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAG,MAAOC,IAAI,IAAK;EAC7C,IAAI,CAACA,IAAI,EAAE;IACT,MAAM,IAAIC,KAAK,CAAC,OAAO,CAAC;EAC1B;EAEA,IAAI,CAACD,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACH,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC/D,MAAM,IAAIF,KAAK,CAAC,0BAA0B,CAAC;EAC7C;EAEA,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;EAE7B,IAAI;IACF,MAAMO,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,IAAI,CAAC,GAAGpB,OAAO,SAAS,EAAEgB,QAAQ,EAAE;MAC/DK,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACJ,QAAQ,IAAII,KAAK,CAACJ,QAAQ,CAACG,IAAI,IAAIC,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAACC,KAAK,EAAE;MACtE,MAAM,IAAIV,KAAK,CAACU,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;IAC5C,CAAC,MAAM;MACL,MAAM,IAAIV,KAAK,CAAC,YAAY,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}