{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 使用React.memo优化单元格渲染\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MemoizedRemovedChip = /*#__PURE__*/React.memo(_c = ({\n  label,\n  title\n}) => /*#__PURE__*/_jsxDEV(Tooltip, {\n  title: title || '',\n  arrow: true,\n  placement: \"top\",\n  children: /*#__PURE__*/_jsxDEV(Chip, {\n    label: label,\n    color: \"default\",\n    variant: \"outlined\",\n    size: \"small\",\n    sx: {\n      maxWidth: '100%',\n      opacity: 0.6,\n      '& .MuiChip-label': {\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        display: 'block'\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 35,\n  columnNumber: 3\n}, this));\n\n// 使用React.memo优化可点击的Chip组件\n_c2 = MemoizedRemovedChip;\nconst MemoizedClickableChip = /*#__PURE__*/React.memo(_c3 = ({\n  label,\n  hasSelected,\n  onClick\n}) => /*#__PURE__*/_jsxDEV(Tooltip, {\n  title: hasSelected ? label : '',\n  arrow: true,\n  placement: \"top\",\n  children: /*#__PURE__*/_jsxDEV(Chip, {\n    label: label,\n    color: hasSelected ? 'primary' : 'default',\n    variant: hasSelected ? 'filled' : 'outlined',\n    size: \"small\",\n    onClick: onClick,\n    clickable: true,\n    sx: {\n      maxWidth: '100%',\n      cursor: 'pointer',\n      '& .MuiChip-label': {\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        display: 'block'\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 57,\n  columnNumber: 3\n}, this));\n\n// 使用React.memo优化操作按钮\n_c4 = MemoizedClickableChip;\nconst MemoizedActionChip = /*#__PURE__*/React.memo(_c5 = ({\n  label,\n  color,\n  icon,\n  onClick\n}) => /*#__PURE__*/_jsxDEV(Chip, {\n  label: label,\n  color: color,\n  size: \"small\",\n  icon: icon,\n  onClick: onClick,\n  sx: {\n    cursor: 'pointer'\n  }\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 81,\n  columnNumber: 3\n}, this));\n\n// 默认的REMARKS选项\n_c6 = MemoizedActionChip;\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 使用React.memo包装ResultDisplay组件以避免不必要的重新渲染\nconst ResultDisplay = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c7 = _s(({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  // 使用useMemo优化，避免每次渲染都重新计算\n  const processedData = useMemo(() => data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  }), [data]);\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const gridDataRef = useRef(gridData);\n\n  // 更新ref值以便在回调中使用最新的gridData\n  useEffect(() => {\n    gridDataRef.current = gridData;\n  }, [gridData]);\n\n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n\n      // 如果距离上次通知时间不足500ms，则跳过此次通知\n      if (now - lastNotifyTimeRef.current < 500) {\n        return;\n      }\n\n      // 比较新数据和上一次通知的数据是否相同\n      const currentDataString = JSON.stringify(gridData);\n      const lastDataString = lastNotifiedDataRef.current;\n      if (lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        lastNotifiedDataRef.current = currentDataString;\n        lastNotifyTimeRef.current = now;\n\n        // 使用防抖函数延迟通知父组件\n        const timeoutId = setTimeout(() => {\n          onDataChange([...gridData]); // 确保传递深拷贝\n        }, 300);\n        return () => clearTimeout(timeoutId);\n      }\n    }\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和 - 使用useMemo优化\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n  const processRowUpdate = useCallback(newRow => {\n    console.log('行数据更新:', newRow);\n    setGridData(prevData => {\n      // 更新行数据\n      let updatedData = prevData.map(row => row.id === newRow.id ? newRow : row);\n\n      // 重新计算总计\n      return recalculateTotal(updatedData);\n    });\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  }, [recalculateTotal]);\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项 - 进一步优化\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 重新计算总计，确保TOTAL正确\n        return recalculateTotal(updatedData);\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据 - 使用useCallback优化\n  const handleRemoveRow = useCallback(id => {\n    console.log('删除行:', id);\n    setGridData(prevData => {\n      // 找到要删除的行，记录其COMMISSION值用于日志\n      const rowToRemove = prevData.find(row => row.id === id);\n      const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n      console.log('删除行的COMMISSION:', commissionValue);\n\n      // 标记行为已删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return {\n            ...row,\n            _removed: true\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  }, [recalculateTotal]);\n\n  // 恢复行数据 - 使用useCallback优化\n  const handleUndoRow = useCallback(id => {\n    console.log('恢复行:', id);\n    setGridData(prevData => {\n      // 找到要恢复的行，记录其COMMISSION值用于日志\n      const rowToRestore = prevData.find(row => row.id === id);\n      const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n      console.log('恢复行的COMMISSION:', commissionValue);\n\n      // 标记行为未删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return {\n            ...row,\n            _removed: false\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'center'\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'center'\n  } // 新添加的ACTION列\n  ];\n\n  // 使用useMemo缓存列定义，避免每次渲染都重新创建\n  const columns = useMemo(() => {\n    return columnOrder.map(col => {\n      // 特殊处理REMARKS列\n      if (col.field === 'REMARKS') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 2.5,\n          width: 250,\n          editable: false,\n          renderCell: params => {\n            // 总计行不显示REMARKS选项\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n\n            // 如果行被删除，显示灰色Chip\n            if (params.row._removed) {\n              const removedRemarkText = params.row._selected_remarks || '无备注';\n              return /*#__PURE__*/_jsxDEV(MemoizedRemovedChip, {\n                label: removedRemarkText,\n                title: params.row._selected_remarks || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this);\n            }\n\n            // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n            let remarkText = '点击选择';\n            let hasSelectedRemark = false;\n            if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n              remarkText = params.row._selected_remarks;\n              hasSelectedRemark = true;\n            }\n\n            // 使用优化过的MemoizedClickableChip组件\n            return /*#__PURE__*/_jsxDEV(MemoizedClickableChip, {\n              label: remarkText,\n              hasSelected: hasSelectedRemark,\n              onClick: () => openRemarksDialog(params.row.id, params.value || '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this);\n          }\n        };\n      }\n\n      // 特殊处理ACTION列\n      if (col.field === 'ACTION') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 0.8,\n          width: 100,\n          editable: false,\n          renderCell: params => {\n            // 总计行不显示ACTION按钮\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n\n            // 如果行已被删除，显示UNDO按钮\n            if (params.row._removed) {\n              return /*#__PURE__*/_jsxDEV(MemoizedActionChip, {\n                label: \"\\u6062\\u590D\",\n                color: \"success\",\n                icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 25\n                }, this),\n                onClick: () => handleUndoRow(params.row.id)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this);\n            }\n\n            // 否则显示REMOVE按钮\n            return /*#__PURE__*/_jsxDEV(MemoizedActionChip, {\n              label: \"\\u79FB\\u9664\",\n              color: \"error\",\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this);\n          }\n        };\n      }\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: col.field === 'NO' ? 0.5 : col.field === 'DATE' ? 0.8 : col.field === 'VEHICLE NO' ? 1 : col.field === 'RO NO' ? 0.8 : col.field === 'KM' ? 0.6 : col.field === 'REMARKS' ? 2.5 : col.field === 'MAXCHECK' ? 0.6 : col.field === 'COMMISSION' ? 0.8 : col.field === 'ACTION' ? 0.6 : 1,\n        width: col.field === 'NO' ? 80 : col.field === 'DATE' ? 100 : col.field === 'VEHICLE NO' ? 120 : col.field === 'RO NO' ? 100 : col.field === 'KM' ? 80 : col.field === 'MAXCHECK' ? 80 : col.field === 'COMMISSION' ? 100 : col.field === 'ACTION' ? 90 : col.field === 'REMARKS' ? 250 : 120,\n        editable: params => {\n          // 总计行不可编辑\n          if (params.row && params.row.NO === 'TOTAL') {\n            return false;\n          }\n          // 已删除的行不可编辑\n          if (params.row && params.row._removed) {\n            return false;\n          }\n          return col.editable !== false;\n        },\n        renderCell: params => {\n          // 特殊处理总计行\n          if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"bold\",\n              color: \"primary\",\n              children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this);\n          }\n\n          // 如果行被删除，显示灰色文本\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.disabled\",\n              sx: {\n                textDecoration: 'line-through'\n              },\n              children: params.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this);\n          }\n\n          // 处理日期格式\n          if (col.field === 'DATE' && params.value) {\n            return params.value.split('T')[0]; // 只显示日期部分\n          }\n\n          // NO列不显示浮点数\n          if (col.field === 'NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // RO NO列不显示浮点数\n          if (col.field === 'RO NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // KM列不显示浮点数\n          if (col.field === 'KM' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n          if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n            return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n          }\n\n          // COMMISSION(AMOUNT)列保留2位小数\n          if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n            return params.value.toFixed(2);\n          } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n            return Number(params.value).toFixed(2);\n          }\n\n          // 其他数字格式\n          if (typeof params.value === 'number') {\n            return params.value;\n          }\n          return params.value;\n        }\n      };\n    }).filter(Boolean); // 过滤掉null值\n  }, [gridData, openRemarksDialog, columnOrder, handleRemoveRow, handleUndoRow]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%',\n          minHeight: 400\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          autoHeight: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            // 阻止总计行和已删除行被编辑\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: (newRow, oldRow) => {\n            // 确保COMMISSION是数字类型\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          density: \"compact\",\n          pagination: true,\n          disableColumnMenu: true,\n          disableColumnSelector: true,\n          disableDensitySelector: true,\n          disableExtendRowFullWidth: true,\n          disableColumnFilter: true,\n          disableVirtualization: false,\n          components: {\n            NoRowsOverlay: () => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                height: '100%'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"\\u65E0\\u6570\\u636E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this)\n          },\n          componentsProps: {\n            cell: {\n              style: {\n                padding: '4px'\n              }\n            }\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 961,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 967,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 751,\n    columnNumber: 5\n  }, this);\n}, \"BGBzvS2mafFJQGoXQs+1XNnvK6U=\")), \"BGBzvS2mafFJQGoXQs+1XNnvK6U=\");\n_c8 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"MemoizedRemovedChip$React.memo\");\n$RefreshReg$(_c2, \"MemoizedRemovedChip\");\n$RefreshReg$(_c3, \"MemoizedClickableChip$React.memo\");\n$RefreshReg$(_c4, \"MemoizedClickableChip\");\n$RefreshReg$(_c5, \"MemoizedActionChip$React.memo\");\n$RefreshReg$(_c6, \"MemoizedActionChip\");\n$RefreshReg$(_c7, \"ResultDisplay$React.memo\");\n$RefreshReg$(_c8, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "MemoizedRemovedChip", "memo", "_c", "label", "title", "arrow", "placement", "children", "color", "variant", "size", "sx", "max<PERSON><PERSON><PERSON>", "opacity", "overflow", "textOverflow", "whiteSpace", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "MemoizedClickableChip", "_c3", "hasSelected", "onClick", "clickable", "cursor", "_c4", "MemoizedActionChip", "_c5", "icon", "_c6", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "_s", "_c7", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "gridDataRef", "current", "now", "Date", "currentDataString", "lastDataString", "timeoutId", "setTimeout", "clearTimeout", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "recalculateTotal", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "processRowUpdate", "newRow", "prevData", "updatedData", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "rowToRemove", "commissionValue", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "rowToRestore", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "style", "src", "Error", "textAlign", "py", "mt", "columnOrder", "field", "headerName", "editable", "headerAlign", "columns", "col", "flex", "width", "renderCell", "removedRemarkText", "remarkText", "hasSelectedRemark", "value", "fontWeight", "isNaN", "textDecoration", "Boolean", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "minHeight", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "autoHeight", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "experimentalFeatures", "newEditingApi", "density", "pagination", "disableColumnMenu", "disableColumnSelector", "disableDensitySelector", "disableExtendRowFullWidth", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableVirtualization", "components", "NoRowsOverlay", "componentsProps", "cell", "padding", "backgroundColor", "lineHeight", "overflowX", "border", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c8", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 使用React.memo优化单元格渲染\nconst MemoizedRemovedChip = React.memo(({ label, title }) => (\n  <Tooltip title={title || ''} arrow placement=\"top\">\n    <Chip\n      label={label}\n      color=\"default\"\n      variant=\"outlined\"\n      size=\"small\"\n      sx={{ \n        maxWidth: '100%',\n        opacity: 0.6,\n        '& .MuiChip-label': {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          display: 'block'\n        }\n      }}\n    />\n  </Tooltip>\n));\n\n// 使用React.memo优化可点击的Chip组件\nconst MemoizedClickableChip = React.memo(({ label, hasSelected, onClick }) => (\n  <Tooltip title={hasSelected ? label : ''} arrow placement=\"top\">\n    <Chip\n      label={label}\n      color={hasSelected ? 'primary' : 'default'}\n      variant={hasSelected ? 'filled' : 'outlined'}\n      size=\"small\"\n      onClick={onClick}\n      clickable\n      sx={{ \n        maxWidth: '100%',\n        cursor: 'pointer',\n        '& .MuiChip-label': {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          display: 'block'\n        }\n      }}\n    />\n  </Tooltip>\n));\n\n// 使用React.memo优化操作按钮\nconst MemoizedActionChip = React.memo(({ label, color, icon, onClick }) => (\n  <Chip\n    label={label}\n    color={color}\n    size=\"small\"\n    icon={icon}\n    onClick={onClick}\n    sx={{ cursor: 'pointer' }}\n  />\n));\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 使用React.memo包装ResultDisplay组件以避免不必要的重新渲染\nconst ResultDisplay = React.memo(({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  // 使用useMemo优化，避免每次渲染都重新计算\n  const processedData = useMemo(() => data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  }), [data]);\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const gridDataRef = useRef(gridData);\n  \n  // 更新ref值以便在回调中使用最新的gridData\n  useEffect(() => {\n    gridDataRef.current = gridData;\n  }, [gridData]);\n  \n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      \n      // 如果距离上次通知时间不足500ms，则跳过此次通知\n      if (now - lastNotifyTimeRef.current < 500) {\n        return;\n      }\n      \n      // 比较新数据和上一次通知的数据是否相同\n      const currentDataString = JSON.stringify(gridData);\n      const lastDataString = lastNotifiedDataRef.current;\n      \n      if (lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        lastNotifiedDataRef.current = currentDataString;\n        lastNotifyTimeRef.current = now;\n        \n        // 使用防抖函数延迟通知父组件\n        const timeoutId = setTimeout(() => {\n          onDataChange([...gridData]); // 确保传递深拷贝\n        }, 300);\n        \n        return () => clearTimeout(timeoutId);\n      }\n    }\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和 - 使用useMemo优化\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      \n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  const processRowUpdate = useCallback((newRow) => {\n    console.log('行数据更新:', newRow);\n    \n    setGridData(prevData => {\n      // 更新行数据\n      let updatedData = prevData.map(row => (row.id === newRow.id ? newRow : row));\n      \n      // 重新计算总计\n      return recalculateTotal(updatedData);\n    });\n    \n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  }, [recalculateTotal]);\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项 - 进一步优化\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 重新计算总计，确保TOTAL正确\n        return recalculateTotal(updatedData);\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据 - 使用useCallback优化\n  const handleRemoveRow = useCallback((id) => {\n    console.log('删除行:', id);\n    \n    setGridData(prevData => {\n      // 找到要删除的行，记录其COMMISSION值用于日志\n      const rowToRemove = prevData.find(row => row.id === id);\n      const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n      console.log('删除行的COMMISSION:', commissionValue);\n      \n      // 标记行为已删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return { ...row, _removed: true };\n        }\n        return row;\n      });\n      \n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n      \n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n      \n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      \n      return updatedData;\n    });\n    \n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  }, [recalculateTotal]);\n  \n  // 恢复行数据 - 使用useCallback优化\n  const handleUndoRow = useCallback((id) => {\n    console.log('恢复行:', id);\n    \n    setGridData(prevData => {\n      // 找到要恢复的行，记录其COMMISSION值用于日志\n      const rowToRestore = prevData.find(row => row.id === id);\n      const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n      console.log('恢复行的COMMISSION:', commissionValue);\n      \n      // 标记行为未删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return { ...row, _removed: false };\n        }\n        return row;\n      });\n      \n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n      \n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n      \n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      \n      return updatedData;\n    });\n    \n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  }, [recalculateTotal]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  \n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  // 定义列的显示顺序和标题\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'center' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'center' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'center' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'center' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'center' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'center' },  // 新添加的REMARKS列\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'center' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'center' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'center' }  // 新添加的ACTION列\n  ];\n  \n  // 使用useMemo缓存列定义，避免每次渲染都重新创建\n  const columns = useMemo(() => {\n    return columnOrder.map(col => {\n      // 特殊处理REMARKS列\n      if (col.field === 'REMARKS') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 2.5,\n          width: 250,\n          editable: false,\n          renderCell: (params) => {\n            // 总计行不显示REMARKS选项\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n            \n            // 如果行被删除，显示灰色Chip\n            if (params.row._removed) {\n              const removedRemarkText = params.row._selected_remarks || '无备注';\n              return (\n                <MemoizedRemovedChip \n                  label={removedRemarkText} \n                  title={params.row._selected_remarks || ''} \n                />\n              );\n            }\n            \n            // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n            let remarkText = '点击选择';\n            let hasSelectedRemark = false;\n            \n            if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n              remarkText = params.row._selected_remarks;\n              hasSelectedRemark = true;\n            }\n            \n            // 使用优化过的MemoizedClickableChip组件\n            return (\n              <MemoizedClickableChip \n                label={remarkText}\n                hasSelected={hasSelectedRemark}\n                onClick={() => openRemarksDialog(params.row.id, params.value || '')}\n              />\n            );\n          }\n        };\n      }\n      \n      // 特殊处理ACTION列\n      if (col.field === 'ACTION') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 0.8,\n          width: 100,\n          editable: false,\n          renderCell: (params) => {\n            // 总计行不显示ACTION按钮\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n            \n            // 如果行已被删除，显示UNDO按钮\n            if (params.row._removed) {\n              return (\n                <MemoizedActionChip\n                  label=\"恢复\"\n                  color=\"success\"\n                  icon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                />\n              );\n            }\n            \n            // 否则显示REMOVE按钮\n            return (\n              <MemoizedActionChip\n                label=\"移除\"\n                color=\"error\"\n                icon={<DeleteIcon />}\n                onClick={() => handleRemoveRow(params.row.id)}\n              />\n            );\n          }\n        };\n      }\n      \n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: col.field === 'NO' ? 0.5 : \n              col.field === 'DATE' ? 0.8 :\n              col.field === 'VEHICLE NO' ? 1 : \n              col.field === 'RO NO' ? 0.8 :\n              col.field === 'KM' ? 0.6 :\n              col.field === 'REMARKS' ? 2.5 : \n              col.field === 'MAXCHECK' ? 0.6 :\n              col.field === 'COMMISSION' ? 0.8 :\n              col.field === 'ACTION' ? 0.6 : 1,\n        width: col.field === 'NO' ? 80 : \n               col.field === 'DATE' ? 100 :\n               col.field === 'VEHICLE NO' ? 120 :\n               col.field === 'RO NO' ? 100 :\n               col.field === 'KM' ? 80 : \n               col.field === 'MAXCHECK' ? 80 : \n               col.field === 'COMMISSION' ? 100 : \n               col.field === 'ACTION' ? 90 : \n               col.field === 'REMARKS' ? 250 : 120,\n        editable: params => {\n          // 总计行不可编辑\n          if (params.row && params.row.NO === 'TOTAL') {\n            return false;\n          }\n          // 已删除的行不可编辑\n          if (params.row && params.row._removed) {\n            return false;\n          }\n          return col.editable !== false;\n        },\n        renderCell: (params) => {\n          // 特殊处理总计行\n          if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n            return (\n              <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n                {typeof params.value === 'number' ? params.value.toFixed(2) : \n                 typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : \n                 params.value}\n              </Typography>\n            );\n          }\n          \n          // 如果行被删除，显示灰色文本\n          if (params.row._removed) {\n            return (\n              <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n                {params.value}\n              </Typography>\n            );\n          }\n          \n          // 处理日期格式\n          if (col.field === 'DATE' && params.value) {\n            return params.value.split('T')[0]; // 只显示日期部分\n          }\n          \n          // NO列不显示浮点数\n          if (col.field === 'NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n          \n          // RO NO列不显示浮点数\n          if (col.field === 'RO NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n          \n          // KM列不显示浮点数\n          if (col.field === 'KM' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n          \n          // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n          if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n            return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n          }\n          \n          // COMMISSION(AMOUNT)列保留2位小数\n          if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n            return params.value.toFixed(2);\n          } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n            return Number(params.value).toFixed(2);\n          }\n          \n          // 其他数字格式\n          if (typeof params.value === 'number') {\n            return params.value;\n          }\n          \n          return params.value;\n        }\n      };\n    }).filter(Boolean); // 过滤掉null值\n  }, [gridData, openRemarksDialog, columnOrder, handleRemoveRow, handleUndoRow]);\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%', minHeight: 400 }}>\n          <DataGrid\n            rows={gridData}\n            columns={columns}\n            pageSize={10}\n            rowsPerPageOptions={[10, 25, 50]}\n            disableSelectionOnClick\n            autoHeight\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              // 阻止总计行和已删除行被编辑\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n            }}\n            processRowUpdate={(newRow, oldRow) => {\n              // 确保COMMISSION是数字类型\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n            }}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            experimentalFeatures={{ newEditingApi: true }}\n            density=\"compact\"\n            pagination\n            disableColumnMenu\n            disableColumnSelector\n            disableDensitySelector\n            disableExtendRowFullWidth\n            disableColumnFilter\n            disableVirtualization={false}\n            components={{\n              NoRowsOverlay: () => (\n                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>\n                  <Typography>无数据</Typography>\n                </Box>\n              )\n            }}\n            componentsProps={{\n              cell: {\n                style: { padding: '4px' }\n              }\n            }}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers>\n          <List>\n            {remarksOptions.map((option) => (\n              <ListItem \n                key={option} \n                disablePadding\n                secondaryAction={\n                  <IconButton \n                    edge=\"end\" \n                    aria-label=\"delete\"\n                    onClick={() => deleteOption(option)}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                }\n              >\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\n                  <ListItemText primary={option} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n});\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,gBAAGpC,KAAK,CAACqC,IAAI,CAAAC,EAAA,GAACA,CAAC;EAAEC,KAAK;EAAEC;AAAM,CAAC,kBACtDL,OAAA,CAACX,OAAO;EAACgB,KAAK,EAAEA,KAAK,IAAI,EAAG;EAACC,KAAK;EAACC,SAAS,EAAC,KAAK;EAAAC,QAAA,eAChDR,OAAA,CAACZ,IAAI;IACHgB,KAAK,EAAEA,KAAM;IACbK,KAAK,EAAC,SAAS;IACfC,OAAO,EAAC,UAAU;IAClBC,IAAI,EAAC,OAAO;IACZC,EAAE,EAAE;MACFC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,GAAG;MACZ,kBAAkB,EAAE;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE;MACX;IACF;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACV,CAAC;;AAEF;AAAAC,GAAA,GArBMtB,mBAAmB;AAsBzB,MAAMuB,qBAAqB,gBAAG3D,KAAK,CAACqC,IAAI,CAAAuB,GAAA,GAACA,CAAC;EAAErB,KAAK;EAAEsB,WAAW;EAAEC;AAAQ,CAAC,kBACvE3B,OAAA,CAACX,OAAO;EAACgB,KAAK,EAAEqB,WAAW,GAAGtB,KAAK,GAAG,EAAG;EAACE,KAAK;EAACC,SAAS,EAAC,KAAK;EAAAC,QAAA,eAC7DR,OAAA,CAACZ,IAAI;IACHgB,KAAK,EAAEA,KAAM;IACbK,KAAK,EAAEiB,WAAW,GAAG,SAAS,GAAG,SAAU;IAC3ChB,OAAO,EAAEgB,WAAW,GAAG,QAAQ,GAAG,UAAW;IAC7Cf,IAAI,EAAC,OAAO;IACZgB,OAAO,EAAEA,OAAQ;IACjBC,SAAS;IACThB,EAAE,EAAE;MACFC,QAAQ,EAAE,MAAM;MAChBgB,MAAM,EAAE,SAAS;MACjB,kBAAkB,EAAE;QAClBd,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE;MACX;IACF;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACV,CAAC;;AAEF;AAAAQ,GAAA,GAvBMN,qBAAqB;AAwB3B,MAAMO,kBAAkB,gBAAGlE,KAAK,CAACqC,IAAI,CAAA8B,GAAA,GAACA,CAAC;EAAE5B,KAAK;EAAEK,KAAK;EAAEwB,IAAI;EAAEN;AAAQ,CAAC,kBACpE3B,OAAA,CAACZ,IAAI;EACHgB,KAAK,EAAEA,KAAM;EACbK,KAAK,EAAEA,KAAM;EACbE,IAAI,EAAC,OAAO;EACZsB,IAAI,EAAEA,IAAK;EACXN,OAAO,EAAEA,OAAQ;EACjBf,EAAE,EAAE;IAAEiB,MAAM,EAAE;EAAU;AAAE;EAAAV,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3B,CACF,CAAC;;AAEF;AAAAY,GAAA,GAXMH,kBAAkB;AAYxB,MAAMI,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,MAAMC,aAAa,gBAAAC,EAAA,cAAGxE,KAAK,CAACqC,IAAI,CAAAoC,GAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAP,EAAA;EACpG;EACA,MAAM,CAACQ,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,MAAM;IACzD,MAAMiF,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGZ,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdiF,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA;EACA,MAAMiB,aAAa,GAAG5F,OAAO,CAAC,MAAMqE,IAAI,CAACwB,GAAG,CAACC,GAAG,IAAI;IAClD;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;;EAEX;EACA,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC,MAAM;IAC7CwG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C5B,aAAa,GAAG,IAAIA,aAAa,CAAC6B,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI7B,aAAa,IAAIA,aAAa,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG9B,aAAa,CAACoB,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG/G,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMgH,iBAAiB,GAAGhH,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMiH,WAAW,GAAGjH,MAAM,CAACmG,QAAQ,CAAC;;EAEpC;EACArG,SAAS,CAAC,MAAM;IACdmH,WAAW,CAACC,OAAO,GAAGf,QAAQ;EAChC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACArG,SAAS,CAAC,MAAM;IACd,IAAI6E,YAAY,IAAIwB,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIA,GAAG,GAAGH,iBAAiB,CAACE,OAAO,GAAG,GAAG,EAAE;QACzC;MACF;;MAEA;MACA,MAAMG,iBAAiB,GAAGpC,IAAI,CAACW,SAAS,CAACO,QAAQ,CAAC;MAClD,MAAMmB,cAAc,GAAGP,mBAAmB,CAACG,OAAO;MAElD,IAAII,cAAc,KAAKD,iBAAiB,EAAE;QACxChB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,QAAQ,CAACI,MAAM,CAAC;QAC9DQ,mBAAmB,CAACG,OAAO,GAAGG,iBAAiB;QAC/CL,iBAAiB,CAACE,OAAO,GAAGC,GAAG;;QAE/B;QACA,MAAMI,SAAS,GAAGC,UAAU,CAAC,MAAM;UACjC7C,YAAY,CAAC,CAAC,GAAGwB,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,EAAE,GAAG,CAAC;QAEP,OAAO,MAAMsB,YAAY,CAACF,SAAS,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACpB,QAAQ,EAAExB,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAG9H,QAAQ,CAAC;IAAE+H,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAC;IACjD+H,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApI,SAAS,CAAC,MAAM;IACd,IAAI2F,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAM0C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAGvG,OAAO,aAAa0C,MAAM,EAAE;;MAEnD;MACA,MAAM8D,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAV,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtE,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMuE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMpH,KAAK,CAACqH,MAAM,CAAC,GAAGpH,OAAO,YAAY0C,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAvE,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM0E,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACpD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAGrJ,WAAW,CAAEuE,IAAI,IAAK;IAC7C,MAAM+E,QAAQ,GAAG/E,IAAI,CAACgF,IAAI,CAACvD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIuC,QAAQ,EAAE;MACZ;MACA,MAAME,QAAQ,GAAGjF,IAAI,CAClBkF,MAAM,CAACzD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDuD,MAAM,CAAC,CAACC,GAAG,EAAE3D,GAAG,KAAK2D,GAAG,IAAIC,MAAM,CAAC5D,GAAG,CAAC6D,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/DvD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiD,QAAQ,CAAC;MACnCF,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAOjF,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuF,gBAAgB,GAAG9J,WAAW,CAAE+J,MAAM,IAAK;IAC/CzD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEwD,MAAM,CAAC;IAE7B1D,WAAW,CAAC2D,QAAQ,IAAI;MACtB;MACA,IAAIC,WAAW,GAAGD,QAAQ,CAACjE,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACa,EAAE,KAAKkD,MAAM,CAAClD,EAAE,GAAGkD,MAAM,GAAG/D,GAAI,CAAC;;MAE5E;MACA,OAAOqD,gBAAgB,CAACY,WAAW,CAAC;IACtC,CAAC,CAAC;IAEFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOgC,MAAM;EACf,CAAC,EAAE,CAACV,gBAAgB,CAAC,CAAC;EAEtB,MAAMa,uBAAuB,GAAIlB,KAAK,IAAK;IACzCpB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASkB,KAAK,CAAClB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMoC,mBAAmB,GAAGA,CAAA,KAAM;IAChCvC,WAAW,CAACwC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMwC,iBAAiB,GAAGxK,KAAK,CAACG,WAAW,CAAC,CAACkI,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,kBAAkB,GAAGtK,WAAW,CAAC,MAAM;IAC3CiI,gBAAgB,CAACmC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPvC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0C,kBAAkB,GAAGvK,WAAW,CAAEwK,MAAM,IAAK;IACjD,MAAM;MAAEtC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB5B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiE,MAAM,EAAE,MAAM,EAAEtC,KAAK,CAAC;MAChD7B,WAAW,CAAC2D,QAAQ,IAAI;QACtB,IAAIC,WAAW,GAAGD,QAAQ,CAACjE,GAAG,CAACC,GAAG,IAAI;UACpC,IAAIA,GAAG,CAACa,EAAE,KAAKqB,KAAK,EAAE;YACpB;YACA,IAAIsC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAGxE,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAEuE,MAAM;gBAAEtE,iBAAiB,EAAEsE;cAAO,CAAC;YAC/D;UACF;UACA,OAAOxE,GAAG;QACZ,CAAC,CAAC;;QAEF;QACA,OAAOqD,gBAAgB,CAACY,WAAW,CAAC;MACtC,CAAC,CAAC;MACFrC,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAuC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACtC,aAAa,EAAEsC,kBAAkB,EAAEjB,gBAAgB,CAAC,CAAC;;EAEzD;EACA,MAAMoB,mBAAmB,GAAGzK,WAAW,CAAC,MAAM;IAC5CuF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmF,oBAAoB,GAAG1K,WAAW,CAAC,MAAM;IAC7CuF,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsF,YAAY,GAAG3K,WAAW,CAAC,MAAM;IACrC,IAAIoF,SAAS,CAACwF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC/F,cAAc,CAACgG,QAAQ,CAACzF,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE9F,iBAAiB,CAACsF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhF,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE2C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI7F,cAAc,CAACgG,QAAQ,CAACzF,SAAS,CAACwF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAAC3C,SAAS,EAAEP,cAAc,EAAE6F,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG9K,WAAW,CAAEwK,MAAM,IAAK;IAC3C1F,iBAAiB,CAACsF,IAAI,IAAIA,IAAI,CAACX,MAAM,CAACsB,IAAI,IAAIA,IAAI,KAAKP,MAAM,CAAC,CAAC;IAC/D5C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,eAAe,GAAGhL,WAAW,CAAE6G,EAAE,IAAK;IAC1CP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;IAEvBR,WAAW,CAAC2D,QAAQ,IAAI;MACtB;MACA,MAAMiB,WAAW,GAAGjB,QAAQ,CAACT,IAAI,CAACvD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;MACvD,MAAMqE,eAAe,GAAGD,WAAW,GAAGA,WAAW,CAACpB,UAAU,GAAG,CAAC;MAChEvD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2E,eAAe,CAAC;;MAE/C;MACA,IAAIjB,WAAW,GAAGD,QAAQ,CAACjE,GAAG,CAACC,GAAG,IAAI;QACpC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;UACjB,OAAO;YAAE,GAAGb,GAAG;YAAEG,QAAQ,EAAE;UAAK,CAAC;QACnC;QACA,OAAOH,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAiE,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;;MAE3C;MACA,MAAMkB,cAAc,GAAGlB,WAAW,CAACR,MAAM,CAACzD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHoE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,EAAE,GAAGyE,CAAC,CAACzE,EAAE,CAAC,CAAC,CAAC;;MAE5C;MACAsE,cAAc,CAACI,OAAO,CAAC,CAACvF,GAAG,EAAEY,KAAK,KAAK;QACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MACpB,CAAC,CAAC;MAEF,OAAOqD,WAAW;IACpB,CAAC,CAAC;IAEFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMmC,aAAa,GAAGxL,WAAW,CAAE6G,EAAE,IAAK;IACxCP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;IAEvBR,WAAW,CAAC2D,QAAQ,IAAI;MACtB;MACA,MAAMyB,YAAY,GAAGzB,QAAQ,CAACT,IAAI,CAACvD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;MACxD,MAAMqE,eAAe,GAAGO,YAAY,GAAGA,YAAY,CAAC5B,UAAU,GAAG,CAAC;MAClEvD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2E,eAAe,CAAC;;MAE/C;MACA,IAAIjB,WAAW,GAAGD,QAAQ,CAACjE,GAAG,CAACC,GAAG,IAAI;QACpC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;UACjB,OAAO;YAAE,GAAGb,GAAG;YAAEG,QAAQ,EAAE;UAAM,CAAC;QACpC;QACA,OAAOH,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAiE,WAAW,GAAGZ,gBAAgB,CAACY,WAAW,CAAC;;MAE3C;MACA,MAAMkB,cAAc,GAAGlB,WAAW,CAACR,MAAM,CAACzD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHoE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,EAAE,GAAGyE,CAAC,CAACzE,EAAE,CAAC,CAAC,CAAC;;MAE5C;MACAsE,cAAc,CAACI,OAAO,CAAC,CAACvF,GAAG,EAAEY,KAAK,KAAK;QACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MACpB,CAAC,CAAC;MAEF,OAAOqD,WAAW;IACpB,CAAC,CAAC;IAEFrC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC,EAAE,CAACsB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMqC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjG,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMkG,YAAY,GAAGvF,QAAQ,CAC1BqD,MAAM,CAACzD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAwF,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACtE,EAAE,KAAK,QAAQ,IAAI,OAAOuE,CAAC,CAACvE,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOsE,CAAC,CAACtE,EAAE,GAAGuE,CAAC,CAACvE,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM6E,OAAO,GAAGD,YAAY,CAAC5F,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbiF,IAAI,EAAE7F,GAAG,CAAC6F,IAAI,GAAI,OAAO7F,GAAG,CAAC6F,IAAI,KAAK,QAAQ,IAAI7F,GAAG,CAAC6F,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAG7E,GAAG,CAAC6F,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9F,GAAG,CAAC6F,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE7F,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG+F,IAAI,CAACC,KAAK,CAAChG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFiG,EAAE,EAAE,OAAOjG,GAAG,CAACiG,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAChG,GAAG,CAACiG,EAAE,CAAC,GAAGjG,GAAG,CAACiG,EAAE,IAAI,EAAE;QAClEhG,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGgG,KAAK,EAAE,OAAOlG,GAAG,CAACmG,QAAQ,KAAK,QAAQ,GACpCnG,GAAG,CAACmG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGnG,GAAG,CAACmG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGpG,GAAG,CAACmG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EpG,GAAG,CAACmG,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOrG,GAAG,CAAC6D,UAAU,KAAK,QAAQ,GAAG7D,GAAG,CAAC6D,UAAU,CAACuC,OAAO,CAAC,CAAC,CAAC,GAAGpG,GAAG,CAAC6D,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMyC,WAAW,GAAGlG,QAAQ,CACzBqD,MAAM,CAACzD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAAC6D,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAE3D,GAAG,KAAK2D,GAAG,IAAI,OAAO3D,GAAG,CAAC6D,UAAU,KAAK,QAAQ,GAAG7D,GAAG,CAAC6D,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM0C,QAAQ,GAAG,MAAM1K,KAAK,CAAC2K,IAAI,CAAC,GAAG1K,OAAO,oBAAoB,EAAE;QAChEyC,IAAI,EAAEqH,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC5H,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI+H,QAAQ,CAAChI,IAAI,IAAIgI,QAAQ,CAAChI,IAAI,CAACkI,KAAK,EAAE;QACxC;QACA,MAAMpE,WAAW,GAAG,GAAGvG,OAAO,CAACgK,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAChI,IAAI,CAACmI,MAAM,EAAE;;QAExE;QACA,MAAMpE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEL,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;QAE/B;QACAV,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACAN,UAAU,CAAC,MAAM;UACf,MAAMkF,MAAM,GAAGpE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CmE,MAAM,CAACC,KAAK,CAAC1J,OAAO,GAAG,MAAM;UAC7ByJ,MAAM,CAACE,GAAG,GAAGxE,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC8D,MAAM,CAAC;UACjClF,UAAU,CAAC,MAAM;YACfc,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC4D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRtC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAGD;EACA,IAAI,CAACW,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACExE,OAAA,CAAC7B,GAAG;MAACyC,EAAE,EAAE;QAAEmK,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxK,QAAA,gBACtCR,OAAA,CAAC5B,UAAU;QAACsC,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAEhD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtB,OAAA,CAAC3B,MAAM;QACLqC,OAAO,EAAC,WAAW;QACnBiB,OAAO,EAAEc,OAAQ;QACjB7B,EAAE,EAAE;UAAEqK,EAAE,EAAE;QAAE,CAAE;QAAAzK,QAAA,EACf;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAM4J,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC5E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC9E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC;EAAG;EACtF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACjF;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACpF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC,CAAE;EAAA,CACpF;;EAED;EACA,MAAMC,OAAO,GAAGrN,OAAO,CAAC,MAAM;IAC5B,OAAOgN,WAAW,CAACnH,GAAG,CAACyH,GAAG,IAAI;MAC5B;MACA,IAAIA,GAAG,CAACL,KAAK,KAAK,SAAS,EAAE;QAC3B,OAAO;UACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;UAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;UAC1BK,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,GAAG;UACVL,QAAQ,EAAE,KAAK;UACfM,UAAU,EAAGvE,MAAM,IAAK;YACtB;YACA,IAAIA,MAAM,CAACpD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;cAC7B,OAAO,EAAE;YACX;;YAEA;YACA,IAAIqC,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE;cACvB,MAAMyH,iBAAiB,GAAGxE,MAAM,CAACpD,GAAG,CAACE,iBAAiB,IAAI,KAAK;cAC/D,oBACElE,OAAA,CAACC,mBAAmB;gBAClBG,KAAK,EAAEwL,iBAAkB;gBACzBvL,KAAK,EAAE+G,MAAM,CAACpD,GAAG,CAACE,iBAAiB,IAAI;cAAG;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAEN;;YAEA;YACA,IAAIuK,UAAU,GAAG,MAAM;YACvB,IAAIC,iBAAiB,GAAG,KAAK;YAE7B,IAAI1E,MAAM,CAACpD,GAAG,CAACE,iBAAiB,IAAIkD,MAAM,CAACpD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;cAC3E2H,UAAU,GAAGzE,MAAM,CAACpD,GAAG,CAACE,iBAAiB;cACzC4H,iBAAiB,GAAG,IAAI;YAC1B;;YAEA;YACA,oBACE9L,OAAA,CAACwB,qBAAqB;cACpBpB,KAAK,EAAEyL,UAAW;cAClBnK,WAAW,EAAEoK,iBAAkB;cAC/BnK,OAAO,EAAEA,CAAA,KAAM0G,iBAAiB,CAACjB,MAAM,CAACpD,GAAG,CAACa,EAAE,EAAEuC,MAAM,CAAC2E,KAAK,IAAI,EAAE;YAAE;cAAA5K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAEN;QACF,CAAC;MACH;;MAEA;MACA,IAAIkK,GAAG,CAACL,KAAK,KAAK,QAAQ,EAAE;QAC1B,OAAO;UACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;UAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;UAC1BK,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,GAAG;UACVL,QAAQ,EAAE,KAAK;UACfM,UAAU,EAAGvE,MAAM,IAAK;YACtB;YACA,IAAIA,MAAM,CAACpD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;cAC7B,OAAO,EAAE;YACX;;YAEA;YACA,IAAIqC,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE;cACvB,oBACEnE,OAAA,CAAC+B,kBAAkB;gBACjB3B,KAAK,EAAC,cAAI;gBACVK,KAAK,EAAC,SAAS;gBACfwB,IAAI,eAAEjC,OAAA,CAACL,QAAQ;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBK,OAAO,EAAEA,CAAA,KAAM6H,aAAa,CAACpC,MAAM,CAACpD,GAAG,CAACa,EAAE;cAAE;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAEN;;YAEA;YACA,oBACEtB,OAAA,CAAC+B,kBAAkB;cACjB3B,KAAK,EAAC,cAAI;cACVK,KAAK,EAAC,OAAO;cACbwB,IAAI,eAAEjC,OAAA,CAACN,UAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBK,OAAO,EAAEA,CAAA,KAAMqH,eAAe,CAAC5B,MAAM,CAACpD,GAAG,CAACa,EAAE;YAAE;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAEN;QACF,CAAC;MACH;MAEA,OAAO;QACL6J,KAAK,EAAEK,GAAG,CAACL,KAAK;QAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;QAC1BK,IAAI,EAAED,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,CAAC,GAC9BK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAC7BK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,GAAG,GAC9BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;QACtCO,KAAK,EAAEF,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,EAAE,GAC7BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,EAAE,GAC3BK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;QAC1CE,QAAQ,EAAEjE,MAAM,IAAI;UAClB;UACA,IAAIA,MAAM,CAACpD,GAAG,IAAIoD,MAAM,CAACpD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;YAC3C,OAAO,KAAK;UACd;UACA;UACA,IAAIqC,MAAM,CAACpD,GAAG,IAAIoD,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE;YACrC,OAAO,KAAK;UACd;UACA,OAAOqH,GAAG,CAACH,QAAQ,KAAK,KAAK;QAC/B,CAAC;QACDM,UAAU,EAAGvE,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAACpD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIyG,GAAG,CAACL,KAAK,KAAK,YAAY,EAAE;YAC3D,oBACEnL,OAAA,CAAC5B,UAAU;cAACsC,OAAO,EAAC,OAAO;cAACsL,UAAU,EAAC,MAAM;cAACvL,KAAK,EAAC,SAAS;cAAAD,QAAA,EAC1D,OAAO4G,MAAM,CAAC2E,KAAK,KAAK,QAAQ,GAAG3E,MAAM,CAAC2E,KAAK,CAAC3B,OAAO,CAAC,CAAC,CAAC,GAC1D,OAAOhD,MAAM,CAAC2E,KAAK,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACrE,MAAM,CAACR,MAAM,CAAC2E,KAAK,CAAC,CAAC,GAAGnE,MAAM,CAACR,MAAM,CAAC2E,KAAK,CAAC,CAAC3B,OAAO,CAAC,CAAC,CAAC,GAClGhD,MAAM,CAAC2E;YAAK;cAAA5K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAEjB;;UAEA;UACA,IAAI8F,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACEnE,OAAA,CAAC5B,UAAU;cAACsC,OAAO,EAAC,OAAO;cAACD,KAAK,EAAC,eAAe;cAACG,EAAE,EAAE;gBAAEsL,cAAc,EAAE;cAAe,CAAE;cAAA1L,QAAA,EACtF4G,MAAM,CAAC2E;YAAK;cAAA5K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAEjB;;UAEA;UACA,IAAIkK,GAAG,CAACL,KAAK,KAAK,MAAM,IAAI/D,MAAM,CAAC2E,KAAK,EAAE;YACxC,OAAO3E,MAAM,CAAC2E,KAAK,CAACjC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC;;UAEA;UACA,IAAI0B,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YAC1D,OAAOhC,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC2E,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIP,GAAG,CAACL,KAAK,KAAK,OAAO,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YAC7D,OAAOhC,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC2E,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIP,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YAC1D,OAAOhC,IAAI,CAACC,KAAK,CAAC5C,MAAM,CAAC2E,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIP,GAAG,CAACL,KAAK,KAAK,UAAU,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YAChE,OAAO3E,MAAM,CAAC2E,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG3E,MAAM,CAAC2E,KAAK,CAAC3B,OAAO,CAAC,CAAC,CAAC,GAAGhD,MAAM,CAAC2E,KAAK,CAAC3B,OAAO,CAAC,CAAC,CAAC;UACnF;;UAEA;UACA,IAAIoB,GAAG,CAACL,KAAK,KAAK,YAAY,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YAClE,OAAO3E,MAAM,CAAC2E,KAAK,CAAC3B,OAAO,CAAC,CAAC,CAAC;UAChC,CAAC,MAAM,IAAIoB,GAAG,CAACL,KAAK,KAAK,YAAY,IAAI,OAAO/D,MAAM,CAAC2E,KAAK,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACrE,MAAM,CAACR,MAAM,CAAC2E,KAAK,CAAC,CAAC,EAAE;YACzG,OAAOnE,MAAM,CAACR,MAAM,CAAC2E,KAAK,CAAC,CAAC3B,OAAO,CAAC,CAAC,CAAC;UACxC;;UAEA;UACA,IAAI,OAAOhD,MAAM,CAAC2E,KAAK,KAAK,QAAQ,EAAE;YACpC,OAAO3E,MAAM,CAAC2E,KAAK;UACrB;UAEA,OAAO3E,MAAM,CAAC2E,KAAK;QACrB;MACF,CAAC;IACH,CAAC,CAAC,CAACtE,MAAM,CAAC0E,OAAO,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC/H,QAAQ,EAAEiE,iBAAiB,EAAE6C,WAAW,EAAElC,eAAe,EAAEQ,aAAa,CAAC,CAAC;EAE9E,oBACExJ,OAAA,CAAC7B,GAAG;IAAAqC,QAAA,gBACFR,OAAA,CAAC7B,GAAG;MAACyC,EAAE,EAAE;QAAEM,OAAO,EAAE,MAAM;QAAEkL,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9L,QAAA,gBACzFR,OAAA,CAAC5B,UAAU;QAACsC,OAAO,EAAC,IAAI;QAAC6L,YAAY;QAAA/L,QAAA,EAAC;MAEtC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtB,OAAA,CAAC7B,GAAG;QAAAqC,QAAA,gBACFR,OAAA,CAAC3B,MAAM;UACLqC,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf+L,SAAS,eAAExM,OAAA,CAACT,YAAY;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BK,OAAO,EAAEyE,cAAe;UACxBxF,EAAE,EAAE;YAAE6L,EAAE,EAAE;UAAE,CAAE;UAAAjM,QAAA,EACf;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtB,OAAA,CAAC3B,MAAM;UACLqC,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf+L,SAAS,eAAExM,OAAA,CAACJ,gBAAgB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCK,OAAO,EAAE+H,gBAAiB;UAC1BgD,QAAQ,EAAElJ,oBAAqB;UAC/B5C,EAAE,EAAE;YAAE6L,EAAE,EAAE;UAAE,CAAE;UAAAjM,QAAA,EAEbgD,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETtB,OAAA,CAAC3B,MAAM;UACLqC,OAAO,EAAC,UAAU;UAClB8L,SAAS,eAAExM,OAAA,CAACR,cAAc;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BK,OAAO,EAAEsF,aAAc;UAAAzG,QAAA,EACxB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA,CAAC1B,KAAK;MAACsC,EAAE,EAAE;QAAE8K,KAAK,EAAE,MAAM;QAAE3K,QAAQ,EAAE;MAAS,CAAE;MAAAP,QAAA,eAC/CR,OAAA,CAAC7B,GAAG;QAACyC,EAAE,EAAE;UAAE+L,MAAM,EAAE,MAAM;UAAEjB,KAAK,EAAE,MAAM;UAAEkB,SAAS,EAAE;QAAI,CAAE;QAAApM,QAAA,eACzDR,OAAA,CAACV,QAAQ;UACPuN,IAAI,EAAEzI,QAAS;UACfmH,OAAO,EAAEA,OAAQ;UACjBuB,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,UAAU;UACVC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGhG,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAACpD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAIsC,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACFkJ,cAAc,EAAGjG,MAAM,IAAK;YAC1B;YACA,IAAIA,MAAM,CAACpD,GAAG,CAACc,OAAO,IAAIsC,MAAM,CAACpD,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOiD,MAAM,CAACkG,MAAM,CAACjC,QAAQ,IAAI,OAAOjE,MAAM,CAACkG,MAAM,CAACjC,QAAQ,KAAK,UAAU,GAC3EjE,MAAM,CAACkG,MAAM,CAACjC,QAAQ,CAACjE,MAAM,CAAC,GAAGA,MAAM,CAACkG,MAAM,CAACjC,QAAQ;UAC3D,CAAE;UACFvD,gBAAgB,EAAEA,CAACC,MAAM,EAAEwF,MAAM,KAAK;YACpC;YACA,IAAIxF,MAAM,CAACF,UAAU,KAAKnD,SAAS,EAAE;cACnC,IAAI,OAAOqD,MAAM,CAACF,UAAU,KAAK,QAAQ,EAAE;gBACzCE,MAAM,CAACF,UAAU,GAAGD,MAAM,CAACG,MAAM,CAACF,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOC,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFG,uBAAuB,EAAEA,uBAAwB;UACjDsF,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9CC,OAAO,EAAC,SAAS;UACjBC,UAAU;UACVC,iBAAiB;UACjBC,qBAAqB;UACrBC,sBAAsB;UACtBC,yBAAyB;UACzBC,mBAAmB;UACnBC,qBAAqB,EAAE,KAAM;UAC7BC,UAAU,EAAE;YACVC,aAAa,EAAEA,CAAA,kBACbnO,OAAA,CAAC7B,GAAG;cAACyC,EAAE,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEkL,cAAc,EAAE,QAAQ;gBAAEC,UAAU,EAAE,QAAQ;gBAAEM,MAAM,EAAE;cAAO,CAAE;cAAAnM,QAAA,eAC3FR,OAAA,CAAC5B,UAAU;gBAAAoC,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAET,CAAE;UACF8M,eAAe,EAAE;YACfC,IAAI,EAAE;cACJzD,KAAK,EAAE;gBAAE0D,OAAO,EAAE;cAAM;YAC1B;UACF,CAAE;UACF1N,EAAE,EAAE;YACF,cAAc,EAAE;cACd2N,eAAe,EAAE,0BAA0B;cAC3CvC,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBuC,eAAe,EAAE,0BAA0B;cAC3C9N,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrBQ,UAAU,EAAE,QAAQ;cACpBuN,UAAU,EAAE,QAAQ;cACpBF,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BC,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCE,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrB1N,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnB2N,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BJ,OAAO,EAAE,OAAO;cAChBrN,UAAU,EAAE,QAAQ;cACpBuN,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClCvN,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnByN,UAAU,EAAE,MAAM;cAClBxC,UAAU,EAAE;YACd;UACF;QAAE;UAAA7K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtB,OAAA,CAACvB,MAAM;MACLoH,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzB8I,OAAO,EAAErG,kBAAmB;MAC5BsG,SAAS;MACT/N,QAAQ,EAAC,IAAI;MACbgO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAxO,QAAA,gBAEnBR,OAAA,CAACtB,WAAW;QAAA8B,QAAA,eACVR,OAAA,CAAC7B,GAAG;UAACyC,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEkL,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA7L,QAAA,gBAClFR,OAAA,CAAC5B,UAAU;YAACsC,OAAO,EAAC,IAAI;YAAAF,QAAA,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CtB,OAAA,CAAC3B,MAAM;YACLmO,SAAS,eAAExM,OAAA,CAACP,OAAO;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBK,OAAO,EAAE8G,mBAAoB;YAC7BhI,KAAK,EAAC,SAAS;YAAAD,QAAA,EAChB;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdtB,OAAA,CAACrB,aAAa;QAACsQ,QAAQ;QAAAzO,QAAA,eACrBR,OAAA,CAACnB,IAAI;UAAA2B,QAAA,EACFqC,cAAc,CAACkB,GAAG,CAAEyE,MAAM,iBACzBxI,OAAA,CAAClB,QAAQ;YAEPoQ,cAAc;YACdC,eAAe,eACbnP,OAAA,CAACd,UAAU;cACTkQ,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnBzN,OAAO,EAAEA,CAAA,KAAMmH,YAAY,CAACN,MAAM,CAAE;cAAAhI,QAAA,eAEpCR,OAAA,CAACN,UAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAd,QAAA,eAEDR,OAAA,CAACjB,cAAc;cAAC4C,OAAO,EAAEA,CAAA,KAAM4G,kBAAkB,CAACC,MAAM,CAAE;cAAAhI,QAAA,eACxDR,OAAA,CAAChB,YAAY;gBAACqQ,OAAO,EAAE7G;cAAO;gBAAArH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZkH,MAAM;YAAArH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBtB,OAAA,CAACpB,aAAa;QAAA4B,QAAA,eACZR,OAAA,CAAC3B,MAAM;UAACsD,OAAO,EAAE2G,kBAAmB;UAAA9H,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtB,OAAA,CAACvB,MAAM;MACLoH,IAAI,EAAEvC,eAAgB;MACtBqL,OAAO,EAAEjG,oBAAqB;MAC9BkG,SAAS;MACT/N,QAAQ,EAAC,IAAI;MACbgO,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAxO,QAAA,gBAEnBR,OAAA,CAACtB,WAAW;QAAA8B,QAAA,EAAC;MAAK;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCtB,OAAA,CAACrB,aAAa;QAAA6B,QAAA,eACZR,OAAA,CAACf,SAAS;UACRqQ,SAAS;UACTC,MAAM,EAAC,OAAO;UACd1K,EAAE,EAAC,MAAM;UACTzE,KAAK,EAAC,0BAAM;UACZoP,IAAI,EAAC,MAAM;UACXZ,SAAS;UACTlO,OAAO,EAAC,UAAU;UAClBqL,KAAK,EAAE3I,SAAU;UACjBqM,QAAQ,EAAGC,CAAC,IAAKrM,YAAY,CAACqM,CAAC,CAACC,MAAM,CAAC5D,KAAK;QAAE;UAAA5K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBtB,OAAA,CAACpB,aAAa;QAAA4B,QAAA,gBACZR,OAAA,CAAC3B,MAAM;UAACsD,OAAO,EAAE+G,oBAAqB;UAAAlI,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDtB,OAAA,CAAC3B,MAAM;UAACsD,OAAO,EAAEgH,YAAa;UAAClI,KAAK,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETtB,OAAA,CAACzB,QAAQ;MACPsH,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB+J,gBAAgB,EAAE,IAAK;MACvBjB,OAAO,EAAExG,mBAAoB;MAC7B0H,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAvP,QAAA,eAE1DR,OAAA,CAACxB,KAAK;QAACmQ,OAAO,EAAExG,mBAAoB;QAACpC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAAvF,QAAA,EAC9DmF,QAAQ,CAACG;MAAO;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC,kCAAC;AAAC0O,GAAA,GA32BG5N,aAAa;AA62BnB,eAAeA,aAAa;AAAC,IAAAjC,EAAA,EAAAoB,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAA0N,GAAA;AAAAC,YAAA,CAAA9P,EAAA;AAAA8P,YAAA,CAAA1O,GAAA;AAAA0O,YAAA,CAAAxO,GAAA;AAAAwO,YAAA,CAAAnO,GAAA;AAAAmO,YAAA,CAAAjO,GAAA;AAAAiO,YAAA,CAAA/N,GAAA;AAAA+N,YAAA,CAAA3N,GAAA;AAAA2N,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}