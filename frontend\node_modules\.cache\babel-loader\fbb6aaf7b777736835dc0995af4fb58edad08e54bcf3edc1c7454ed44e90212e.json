{"ast": null, "code": "'use client';\n\nexport { default } from './BottomNavigationAction';\nexport { default as bottomNavigationActionClasses } from './bottomNavigationActionClasses';\nexport * from './bottomNavigationActionClasses';", "map": {"version": 3, "names": ["default", "bottomNavigationActionClasses"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/BottomNavigationAction/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './BottomNavigationAction';\nexport { default as bottomNavigationActionClasses } from './bottomNavigationActionClasses';\nexport * from './bottomNavigationActionClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,0BAA0B;AAClD,SAASA,OAAO,IAAIC,6BAA6B,QAAQ,iCAAiC;AAC1F,cAAc,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}