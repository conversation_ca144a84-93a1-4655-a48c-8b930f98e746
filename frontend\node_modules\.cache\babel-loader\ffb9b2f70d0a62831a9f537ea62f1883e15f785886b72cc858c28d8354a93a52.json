{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"label\", \"icon\", \"showInMenu\", \"onClick\"],\n  _excluded2 = [\"label\", \"icon\", \"showInMenu\", \"onClick\", \"closeMenuOnClick\", \"closeMenu\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridActionsCellItem = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  if (!props.showInMenu) {\n    var _rootProps$slotProps;\n    const {\n        label,\n        icon,\n        onClick\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const handleClick = event => {\n      onClick == null || onClick(event);\n    };\n    return /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n      ref: ref,\n      size: \"small\",\n      role: \"menuitem\",\n      \"aria-label\": label\n    }, other, {\n      onClick: handleClick\n    }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseIconButton, {\n      children: /*#__PURE__*/React.cloneElement(icon, {\n        fontSize: 'small'\n      })\n    }));\n  }\n  const {\n      label,\n      icon,\n      onClick,\n      closeMenuOnClick = true,\n      closeMenu\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const handleClick = event => {\n    onClick == null || onClick(event);\n    if (closeMenuOnClick) {\n      closeMenu == null || closeMenu();\n    }\n  };\n  return /*#__PURE__*/_jsxs(MenuItem, _extends({\n    ref: ref\n  }, other, {\n    onClick: handleClick,\n    children: [icon && /*#__PURE__*/_jsx(ListItemIcon, {\n      children: icon\n    }), label]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridActionsCellItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * from https://mui.com/material-ui/api/button-base/#ButtonBase-prop-component\n   */\n  component: PropTypes.elementType,\n  icon: PropTypes.element,\n  label: PropTypes.string.isRequired,\n  showInMenu: PropTypes.bool\n} : void 0;\nexport { GridActionsCellItem };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "PropTypes", "MenuItem", "ListItemIcon", "useGridRootProps", "jsx", "_jsx", "jsxs", "_jsxs", "GridActionsCellItem", "forwardRef", "props", "ref", "rootProps", "showInMenu", "_rootProps$slotProps", "label", "icon", "onClick", "other", "handleClick", "event", "slots", "baseIconButton", "size", "role", "slotProps", "children", "cloneElement", "fontSize", "closeMenuOnClick", "closeMenu", "process", "env", "NODE_ENV", "propTypes", "component", "elementType", "element", "string", "isRequired", "bool"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/components/cell/GridActionsCellItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"label\", \"icon\", \"showInMenu\", \"onClick\"],\n  _excluded2 = [\"label\", \"icon\", \"showInMenu\", \"onClick\", \"closeMenuOnClick\", \"closeMenu\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridActionsCellItem = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  if (!props.showInMenu) {\n    var _rootProps$slotProps;\n    const {\n        label,\n        icon,\n        onClick\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const handleClick = event => {\n      onClick == null || onClick(event);\n    };\n    return /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n      ref: ref,\n      size: \"small\",\n      role: \"menuitem\",\n      \"aria-label\": label\n    }, other, {\n      onClick: handleClick\n    }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseIconButton, {\n      children: /*#__PURE__*/React.cloneElement(icon, {\n        fontSize: 'small'\n      })\n    }));\n  }\n  const {\n      label,\n      icon,\n      onClick,\n      closeMenuOnClick = true,\n      closeMenu\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const handleClick = event => {\n    onClick == null || onClick(event);\n    if (closeMenuOnClick) {\n      closeMenu == null || closeMenu();\n    }\n  };\n  return /*#__PURE__*/_jsxs(MenuItem, _extends({\n    ref: ref\n  }, other, {\n    onClick: handleClick,\n    children: [icon && /*#__PURE__*/_jsx(ListItemIcon, {\n      children: icon\n    }), label]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridActionsCellItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * from https://mui.com/material-ui/api/button-base/#ButtonBase-prop-component\n   */\n  component: PropTypes.elementType,\n  icon: PropTypes.element,\n  label: PropTypes.string.isRequired,\n  showInMenu: PropTypes.bool\n} : void 0;\nexport { GridActionsCellItem };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC;EAC1DC,UAAU,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,CAAC;AAC1F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,mBAAmB,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACxE,MAAMC,SAAS,GAAGT,gBAAgB,CAAC,CAAC;EACpC,IAAI,CAACO,KAAK,CAACG,UAAU,EAAE;IACrB,IAAIC,oBAAoB;IACxB,MAAM;QACFC,KAAK;QACLC,IAAI;QACJC;MACF,CAAC,GAAGP,KAAK;MACTQ,KAAK,GAAGtB,6BAA6B,CAACc,KAAK,EAAEb,SAAS,CAAC;IACzD,MAAMsB,WAAW,GAAGC,KAAK,IAAI;MAC3BH,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACG,KAAK,CAAC;IACnC,CAAC;IACD,OAAO,aAAaf,IAAI,CAACO,SAAS,CAACS,KAAK,CAACC,cAAc,EAAE3B,QAAQ,CAAC;MAChEgB,GAAG,EAAEA,GAAG;MACRY,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChB,YAAY,EAAET;IAChB,CAAC,EAAEG,KAAK,EAAE;MACRD,OAAO,EAAEE;IACX,CAAC,EAAE,CAACL,oBAAoB,GAAGF,SAAS,CAACa,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,oBAAoB,CAACQ,cAAc,EAAE;MACtGI,QAAQ,EAAE,aAAa3B,KAAK,CAAC4B,YAAY,CAACX,IAAI,EAAE;QAC9CY,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;EACL;EACA,MAAM;MACFb,KAAK;MACLC,IAAI;MACJC,OAAO;MACPY,gBAAgB,GAAG,IAAI;MACvBC;IACF,CAAC,GAAGpB,KAAK;IACTQ,KAAK,GAAGtB,6BAA6B,CAACc,KAAK,EAAEZ,UAAU,CAAC;EAC1D,MAAMqB,WAAW,GAAGC,KAAK,IAAI;IAC3BH,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACG,KAAK,CAAC;IACjC,IAAIS,gBAAgB,EAAE;MACpBC,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC,CAAC;IAClC;EACF,CAAC;EACD,OAAO,aAAavB,KAAK,CAACN,QAAQ,EAAEN,QAAQ,CAAC;IAC3CgB,GAAG,EAAEA;EACP,CAAC,EAAEO,KAAK,EAAE;IACRD,OAAO,EAAEE,WAAW;IACpBO,QAAQ,EAAE,CAACV,IAAI,IAAI,aAAaX,IAAI,CAACH,YAAY,EAAE;MACjDwB,QAAQ,EAAEV;IACZ,CAAC,CAAC,EAAED,KAAK;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,mBAAmB,CAAC0B,SAAS,GAAG;EACtE;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,SAAS,EAAEnC,SAAS,CAACoC,WAAW;EAChCpB,IAAI,EAAEhB,SAAS,CAACqC,OAAO;EACvBtB,KAAK,EAAEf,SAAS,CAACsC,MAAM,CAACC,UAAU;EAClC1B,UAAU,EAAEb,SAAS,CAACwC;AACxB,CAAC,GAAG,KAAK,CAAC;AACV,SAAShC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}