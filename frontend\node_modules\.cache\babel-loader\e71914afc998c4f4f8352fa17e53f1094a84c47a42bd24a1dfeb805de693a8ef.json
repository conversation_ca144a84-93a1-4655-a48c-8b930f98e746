{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 使用useMemo优化初始化\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const gridDataRef = useRef(gridData);\n\n  // 更新ref当gridData变化时\n  useEffect(() => {\n    gridDataRef.current = gridData;\n  }, [gridData]);\n\n  // 当gridData变化时，通知父组件，使用节流优化\n  useEffect(() => {\n    if (!onDataChange || gridData.length === 0) return;\n    const now = Date.now();\n    // 如果距离上次通知时间不足500ms，则跳过此次通知\n    if (now - lastNotifyTimeRef.current < 500) {\n      return;\n    }\n\n    // 比较新数据和上一次通知的数据是否相同\n    const currentDataString = JSON.stringify(gridData);\n    const lastDataString = lastNotifiedDataRef.current;\n    if (lastDataString !== currentDataString) {\n      console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n      lastNotifiedDataRef.current = currentDataString;\n      lastNotifyTimeRef.current = now;\n      onDataChange([...gridData]); // 确保传递深拷贝\n    }\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 优化下载Excel功能\n  const handleDownloadExcel = useCallback(async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  }, [fileId, onError]);\n\n  // 优化清理功能\n  const handleCleanupCallback = useCallback(async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  }, [fileId, onReset]);\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和 - 使用useMemo优化\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 优化行更新处理，减少不必要的状态更新\n  const processRowUpdate = useCallback(newRow => {\n    console.log('行数据更新:', newRow);\n\n    // 更新行数据\n    let updatedData = gridDataRef.current.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    updatedData = recalculateTotal(updatedData);\n\n    // 不再直接保存到localStorage，由App组件统一处理\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  }, [recalculateTotal]);\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n\n  // 打开REMARKS选择对话框 - 使用useCallback优化性能\n  const openRemarksDialog = useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项 - 优化性能\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n        return updatedData;\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据 - 优化性能\n  const handleRemoveRow = useCallback(id => {\n    console.log('删除行:', id);\n\n    // 使用函数式更新减少依赖\n    setGridData(prevData => {\n      // 找到要删除的行，记录其COMMISSION值用于日志\n      const rowToRemove = prevData.find(row => row.id === id);\n      const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n      console.log('删除行的COMMISSION:', commissionValue);\n\n      // 标记行为已删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return {\n            ...row,\n            _removed: true\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  }, [recalculateTotal]);\n\n  // 恢复行数据 - 优化性能\n  const handleUndoRow = useCallback(id => {\n    console.log('恢复行:', id);\n\n    // 使用函数式更新减少依赖\n    setGridData(prevData => {\n      // 找到要恢复的行，记录其COMMISSION值用于日志\n      const rowToRestore = prevData.find(row => row.id === id);\n      const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n      console.log('恢复行的COMMISSION:', commissionValue);\n\n      // 标记行为未删除\n      let updatedData = prevData.map(row => {\n        if (row.id === id) {\n          return {\n            ...row,\n            _removed: false\n          };\n        }\n        return row;\n      });\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号NO字段（只处理数字类型的NO）\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n      // 重新编号\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  }, [recalculateTotal]);\n\n  // 优化文档生成函数，使用useCallback减少不必要的重新创建\n  const handleGenerateDocument = useCallback(async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 使用ref获取最新的gridData，避免闭包问题\n      const currentGridData = gridDataRef.current;\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = currentGridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = currentGridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  }, [fileId]);\n\n  // 优化对话框和按钮相关组件\n  const renderDialogs = useMemo(() => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true), [remarksDialog.open, closeRemarksDialog, openAddOptionDialog, remarksOptions, deleteOption, selectRemarkOption, addOptionDialog, closeAddOptionDialog, newOption, addNewOption, snackbar, handleCloseSnackbar]);\n\n  // 优化按钮组\n  const actionButtons = useMemo(() => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"success\",\n      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 20\n      }, this),\n      onClick: handleDownloadExcel,\n      sx: {\n        mr: 1\n      },\n      children: \"\\u4E0B\\u8F7DExcel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"primary\",\n      startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 20\n      }, this),\n      onClick: handleGenerateDocument,\n      disabled: isGeneratingDocument,\n      sx: {\n        mr: 1\n      },\n      children: isGeneratingDocument ? '生成中...' : '生成文档'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 20\n      }, this),\n      onClick: handleCleanupCallback,\n      children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 576,\n    columnNumber: 5\n  }, this), [handleDownloadExcel, handleGenerateDocument, isGeneratingDocument, handleCleanupCallback]);\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'center'\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'center'\n  } // 新添加的ACTION列\n  ];\n\n  // 使用useMemo优化列定义，避免每次渲染都重新创建\n  const columns = useMemo(() => {\n    return columnOrder.map(col => {\n      if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n        // 如果COMMISSION列不存在，跳过\n        return null;\n      }\n\n      // 特殊处理REMARKS列\n      if (col.field === 'REMARKS') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 2.5,\n          width: 250,\n          editable: false,\n          renderCell: params => {\n            // 总计行不显示REMARKS选项\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n\n            // 如果行被删除，显示灰色Chip\n            if (params.row._removed) {\n              const removedRemarkText = params.row._selected_remarks || '无备注';\n              return /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: params.row._selected_remarks || '',\n                arrow: true,\n                placement: \"top\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: removedRemarkText,\n                  color: \"default\",\n                  variant: \"outlined\",\n                  size: \"small\",\n                  sx: {\n                    maxWidth: '100%',\n                    opacity: 0.6,\n                    '& .MuiChip-label': {\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      whiteSpace: 'nowrap',\n                      display: 'block'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 15\n              }, this);\n            }\n\n            // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n            let remarkText = '点击选择';\n            let hasSelectedRemark = false;\n            if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n              remarkText = params.row._selected_remarks;\n              hasSelectedRemark = true;\n            }\n\n            // 使用memo包裹的点击处理函数\n            const handleClick = () => {\n              openRemarksDialog(params.row.id, params.value || '');\n            };\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: hasSelectedRemark ? remarkText : '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: remarkText,\n                color: hasSelectedRemark ? 'primary' : 'default',\n                variant: hasSelectedRemark ? 'filled' : 'outlined',\n                size: \"small\",\n                onClick: handleClick,\n                clickable: true,\n                sx: {\n                  maxWidth: '100%',\n                  cursor: 'pointer',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 13\n            }, this);\n          }\n        };\n      }\n\n      // 特殊处理ACTION列\n      if (col.field === 'ACTION') {\n        return {\n          field: col.field,\n          headerName: col.headerName,\n          flex: 0.8,\n          width: 100,\n          editable: false,\n          renderCell: params => {\n            // 总计行不显示ACTION按钮\n            if (params.row.NO === 'TOTAL') {\n              return '';\n            }\n\n            // 如果行已被删除，显示UNDO按钮\n            if (params.row._removed) {\n              return /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"\\u6062\\u590D\",\n                color: \"success\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 23\n                }, this),\n                onClick: () => handleUndoRow(params.row.id),\n                sx: {\n                  cursor: 'pointer'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 15\n              }, this);\n            }\n\n            // 否则显示REMOVE按钮\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u79FB\\u9664\",\n              color: \"error\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 13\n            }, this);\n          }\n        };\n      }\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: col.field === 'NO' ? 0.5 : col.field === 'DATE' ? 0.8 : col.field === 'VEHICLE NO' ? 1 : col.field === 'RO NO' ? 0.8 : col.field === 'KM' ? 0.6 : col.field === 'REMARKS' ? 2.5 : col.field === 'MAXCHECK' ? 0.6 : col.field === 'COMMISSION' ? 0.8 : col.field === 'ACTION' ? 0.6 : 1,\n        width: col.field === 'NO' ? 80 : col.field === 'DATE' ? 100 : col.field === 'VEHICLE NO' ? 120 : col.field === 'RO NO' ? 100 : col.field === 'KM' ? 80 : col.field === 'MAXCHECK' ? 80 : col.field === 'COMMISSION' ? 100 : col.field === 'ACTION' ? 90 : col.field === 'REMARKS' ? 250 : 120,\n        editable: params => {\n          // 总计行不可编辑\n          if (params.row && params.row.NO === 'TOTAL') {\n            return false;\n          }\n          // 已删除的行不可编辑\n          if (params.row && params.row._removed) {\n            return false;\n          }\n          return col.editable !== false;\n        },\n        renderCell: params => {\n          // 特殊处理总计行\n          if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"bold\",\n              color: \"primary\",\n              children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 13\n            }, this);\n          }\n\n          // 如果行被删除，显示灰色文本\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.disabled\",\n              sx: {\n                textDecoration: 'line-through'\n              },\n              children: params.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 13\n            }, this);\n          }\n\n          // 处理日期格式\n          if (col.field === 'DATE' && params.value) {\n            return params.value.split('T')[0]; // 只显示日期部分\n          }\n\n          // NO列不显示浮点数\n          if (col.field === 'NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // RO NO列不显示浮点数\n          if (col.field === 'RO NO' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // KM列不显示浮点数\n          if (col.field === 'KM' && typeof params.value === 'number') {\n            return Math.floor(params.value);\n          }\n\n          // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n          if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n            return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n          }\n\n          // COMMISSION(AMOUNT)列保留2位小数\n          if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n            return params.value.toFixed(2);\n          } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n            return Number(params.value).toFixed(2);\n          }\n\n          // 其他数字格式\n          if (typeof params.value === 'number') {\n            return params.value;\n          }\n          return params.value;\n        }\n      };\n    }).filter(Boolean); // 过滤掉null值\n  }, [gridData, handleRemoveRow, handleUndoRow, openRemarksDialog]);\n\n  // 使用useMemo优化DataGrid组件的配置\n  const dataGridProps = useMemo(() => ({\n    rows: gridData,\n    columns,\n    pageSize: 10,\n    rowsPerPageOptions: [10, 25, 50],\n    disableSelectionOnClick: true,\n    autoHeight: true,\n    headerHeight: 56,\n    columnHeaderHeight: 56,\n    getRowClassName: params => {\n      if (params.row.isTotal) return 'total-row';\n      if (params.row._removed) return 'removed-row';\n      return '';\n    },\n    isCellEditable: params => {\n      // 阻止总计行和已删除行被编辑\n      if (params.row.isTotal || params.row._removed) {\n        return false;\n      }\n      return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n    },\n    processRowUpdate: (newRow, oldRow) => {\n      // 确保COMMISSION是数字类型\n      if (newRow.COMMISSION !== undefined) {\n        if (typeof newRow.COMMISSION === 'string') {\n          newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n        }\n      }\n      return processRowUpdate(newRow);\n    },\n    onProcessRowUpdateError,\n    experimentalFeatures: {\n      newEditingApi: true\n    }\n  }), [gridData, columns, processRowUpdate, onProcessRowUpdateError]);\n\n  // 使用useMemo优化DataGrid样式\n  const dataGridSx = useMemo(() => ({\n    '& .total-row': {\n      backgroundColor: 'rgba(25, 118, 210, 0.08)',\n      fontWeight: 'bold'\n    },\n    '& .removed-row': {\n      backgroundColor: 'rgba(211, 211, 211, 0.3)',\n      color: 'text.disabled'\n    },\n    '& .MuiDataGrid-cell': {\n      whiteSpace: 'normal',\n      lineHeight: 'normal',\n      padding: '8px'\n    },\n    '& .MuiDataGrid-columnHeaders': {\n      backgroundColor: '#f5f5f5'\n    },\n    '& .MuiDataGrid-virtualScroller': {\n      overflowX: 'visible !important'\n    },\n    '& .MuiDataGrid-main': {\n      overflow: 'visible'\n    },\n    '& .MuiDataGrid-root': {\n      overflow: 'visible',\n      border: 'none'\n    },\n    '& .MuiDataGrid-columnHeader': {\n      padding: '0 8px',\n      whiteSpace: 'normal',\n      lineHeight: 'normal'\n    },\n    '& .MuiDataGrid-columnHeaderTitle': {\n      whiteSpace: 'nowrap',\n      overflow: 'visible',\n      lineHeight: '24px',\n      fontWeight: 'bold'\n    }\n  }), []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 9\n      }, this), actionButtons]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%',\n          minHeight: 400\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          ...dataGridProps,\n          sx: dataGridSx\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 7\n    }, this), renderDialogs]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 943,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"5AncEzOaypTd+yJaPvzw34OSX7w=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "gridDataRef", "current", "now", "Date", "currentDataString", "lastDataString", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleCloseSnackbar", "prev", "handleDownloadExcel", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanupCallback", "delete", "handleCellEdit", "params", "recalculateTotal", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "processRowUpdate", "newRow", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "rowToRemove", "commissionValue", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "rowToRestore", "handleGenerateDocument", "currentGridData", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "setTimeout", "iframe", "style", "display", "src", "Error", "renderDialogs", "children", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sx", "justifyContent", "alignItems", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "color", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "label", "type", "value", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "actionButtons", "mr", "disabled", "textAlign", "py", "mt", "columnOrder", "field", "headerName", "editable", "headerAlign", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "size", "opacity", "overflow", "textOverflow", "whiteSpace", "remarkText", "hasSelectedRemark", "handleClick", "clickable", "cursor", "icon", "fontWeight", "isNaN", "textDecoration", "Boolean", "dataGridProps", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "autoHeight", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "oldRow", "experimentalFeatures", "newEditingApi", "dataGridSx", "backgroundColor", "lineHeight", "padding", "overflowX", "border", "mb", "gutterBottom", "height", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 使用useMemo优化初始化\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const gridDataRef = useRef(gridData);\n  \n  // 更新ref当gridData变化时\n  useEffect(() => {\n    gridDataRef.current = gridData;\n  }, [gridData]);\n  \n  // 当gridData变化时，通知父组件，使用节流优化\n  useEffect(() => {\n    if (!onDataChange || gridData.length === 0) return;\n    \n      const now = Date.now();\n    // 如果距离上次通知时间不足500ms，则跳过此次通知\n    if (now - lastNotifyTimeRef.current < 500) {\n        return;\n      }\n      \n      // 比较新数据和上一次通知的数据是否相同\n      const currentDataString = JSON.stringify(gridData);\n      const lastDataString = lastNotifiedDataRef.current;\n      \n      if (lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        lastNotifiedDataRef.current = currentDataString;\n        lastNotifyTimeRef.current = now;\n        onDataChange([...gridData]); // 确保传递深拷贝\n    }\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 优化下载Excel功能\n  const handleDownloadExcel = useCallback(async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  }, [fileId, onError]);\n  \n  // 优化清理功能\n  const handleCleanupCallback = useCallback(async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  }, [fileId, onReset]);\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 重新计算TOTAL行的COMMISSION总和 - 使用useMemo优化\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      // 计算所有未被移除行的COMMISSION总和\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      \n      console.log('重新计算TOTAL:', newTotal);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 优化行更新处理，减少不必要的状态更新\n  const processRowUpdate = useCallback((newRow) => {\n    console.log('行数据更新:', newRow);\n    \n    // 更新行数据\n    let updatedData = gridDataRef.current.map(row => (row.id === newRow.id ? newRow : row));\n    \n    // 重新计算总计\n    updatedData = recalculateTotal(updatedData);\n    \n    // 不再直接保存到localStorage，由App组件统一处理\n    setGridData(updatedData);\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  }, [recalculateTotal]);\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  // 打开REMARKS选择对话框 - 使用useCallback优化性能\n  const openRemarksDialog = useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项 - 优化性能\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        let updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 重新计算总计，确保TOTAL正确\n        updatedData = recalculateTotal(updatedData);\n        return updatedData;\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据 - 优化性能\n  const handleRemoveRow = useCallback((id) => {\n    console.log('删除行:', id);\n    \n    // 使用函数式更新减少依赖\n    setGridData(prevData => {\n    // 找到要删除的行，记录其COMMISSION值用于日志\n      const rowToRemove = prevData.find(row => row.id === id);\n    const commissionValue = rowToRemove ? rowToRemove.COMMISSION : 0;\n    console.log('删除行的COMMISSION:', commissionValue);\n    \n    // 标记行为已删除\n      let updatedData = prevData.map(row => {\n      if (row.id === id) {\n        return { ...row, _removed: true };\n      }\n      return row;\n    });\n    \n    // 重新计算总计\n    updatedData = recalculateTotal(updatedData);\n    \n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n    \n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n    \n      return updatedData;\n    });\n    \n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  }, [recalculateTotal]);\n  \n  // 恢复行数据 - 优化性能\n  const handleUndoRow = useCallback((id) => {\n    console.log('恢复行:', id);\n    \n    // 使用函数式更新减少依赖\n    setGridData(prevData => {\n    // 找到要恢复的行，记录其COMMISSION值用于日志\n      const rowToRestore = prevData.find(row => row.id === id);\n    const commissionValue = rowToRestore ? rowToRestore.COMMISSION : 0;\n    console.log('恢复行的COMMISSION:', commissionValue);\n    \n    // 标记行为未删除\n      let updatedData = prevData.map(row => {\n      if (row.id === id) {\n        return { ...row, _removed: false };\n      }\n      return row;\n    });\n    \n    // 重新计算总计\n    updatedData = recalculateTotal(updatedData);\n    \n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n    \n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n    \n      return updatedData;\n    });\n    \n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  }, [recalculateTotal]);\n\n  // 优化文档生成函数，使用useCallback减少不必要的重新创建\n  const handleGenerateDocument = useCallback(async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 使用ref获取最新的gridData，避免闭包问题\n      const currentGridData = gridDataRef.current;\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = currentGridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = currentGridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  }, [fileId]);\n  \n  // 优化对话框和按钮相关组件\n  const renderDialogs = useMemo(() => (\n    <>\n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers>\n          <List>\n            {remarksOptions.map((option) => (\n              <ListItem \n                key={option} \n                disablePadding\n                secondaryAction={\n                  <IconButton \n                    edge=\"end\" \n                    aria-label=\"delete\"\n                    onClick={() => deleteOption(option)}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                }\n              >\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\n                  <ListItemText primary={option} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </>\n  ), [remarksDialog.open, closeRemarksDialog, openAddOptionDialog, remarksOptions, \n      deleteOption, selectRemarkOption, addOptionDialog, closeAddOptionDialog, \n      newOption, addNewOption, snackbar, handleCloseSnackbar]);\n  \n  // 优化按钮组\n  const actionButtons = useMemo(() => (\n    <Box>\n      <Button \n        variant=\"contained\"\n        color=\"success\"\n        startIcon={<DownloadIcon />}\n        onClick={handleDownloadExcel}\n        sx={{ mr: 1 }}\n      >\n        下载Excel\n      </Button>\n      \n      <Button \n        variant=\"contained\"\n        color=\"primary\"\n        startIcon={<PictureAsPdfIcon />}\n        onClick={handleGenerateDocument}\n        disabled={isGeneratingDocument}\n        sx={{ mr: 1 }}\n      >\n        {isGeneratingDocument ? '生成中...' : '生成文档'}\n      </Button>\n      \n      <Button \n        variant=\"outlined\" \n        startIcon={<RestartAltIcon />}\n        onClick={handleCleanupCallback}\n      >\n        重新开始\n      </Button>\n    </Box>\n  ), [handleDownloadExcel, handleGenerateDocument, isGeneratingDocument, handleCleanupCallback]);\n  \n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  // 定义列的显示顺序和标题\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'center' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'center' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'center' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'center' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'center' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'center' },  // 新添加的REMARKS列\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'center' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'center' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'center' }  // 新添加的ACTION列\n  ];\n  \n  // 使用useMemo优化列定义，避免每次渲染都重新创建\n  const columns = useMemo(() => {\n    return columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n    \n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          \n          // 如果行被删除，显示灰色Chip\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ \n                    maxWidth: '100%',\n                    opacity: 0.6,\n                    '& .MuiChip-label': {\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      whiteSpace: 'nowrap',\n                      display: 'block'\n                    }\n                  }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          \n            // 使用memo包裹的点击处理函数\n          const handleClick = () => {\n              openRemarksDialog(params.row.id, params.value || '');\n          };\n          \n          return (\n            <Tooltip title={hasSelectedRemark ? remarkText : ''} arrow placement=\"top\">\n              <Chip\n                label={remarkText}\n                color={hasSelectedRemark ? 'primary' : 'default'}\n                variant={hasSelectedRemark ? 'filled' : 'outlined'}\n                size=\"small\"\n                onClick={handleClick}\n                clickable\n                sx={{ \n                  maxWidth: '100%',\n                  cursor: 'pointer',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }}\n              />\n            </Tooltip>\n          );\n        }\n      };\n    }\n    \n    // 特殊处理ACTION列\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          // 总计行不显示ACTION按钮\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          \n          // 如果行已被删除，显示UNDO按钮\n          if (params.row._removed) {\n            return (\n              <Chip\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          \n          // 否则显示REMOVE按钮\n          return (\n            <Chip\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    \n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: col.field === 'NO' ? 0.5 : \n            col.field === 'DATE' ? 0.8 :\n            col.field === 'VEHICLE NO' ? 1 : \n            col.field === 'RO NO' ? 0.8 :\n            col.field === 'KM' ? 0.6 :\n            col.field === 'REMARKS' ? 2.5 : \n            col.field === 'MAXCHECK' ? 0.6 :\n            col.field === 'COMMISSION' ? 0.8 :\n            col.field === 'ACTION' ? 0.6 : 1,\n      width: col.field === 'NO' ? 80 : \n             col.field === 'DATE' ? 100 :\n             col.field === 'VEHICLE NO' ? 120 :\n             col.field === 'RO NO' ? 100 :\n             col.field === 'KM' ? 80 : \n             col.field === 'MAXCHECK' ? 80 : \n             col.field === 'COMMISSION' ? 100 : \n             col.field === 'ACTION' ? 90 : \n             col.field === 'REMARKS' ? 250 : 120,\n      editable: params => {\n        // 总计行不可编辑\n        if (params.row && params.row.NO === 'TOTAL') {\n          return false;\n        }\n        // 已删除的行不可编辑\n        if (params.row && params.row._removed) {\n          return false;\n        }\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : \n               typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : \n               params.value}\n            </Typography>\n          );\n        }\n        \n        // 如果行被删除，显示灰色文本\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        \n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n        \n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        \n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        \n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        \n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n  }, [gridData, handleRemoveRow, handleUndoRow, openRemarksDialog]);\n  \n  // 使用useMemo优化DataGrid组件的配置\n  const dataGridProps = useMemo(() => ({\n    rows: gridData,\n    columns,\n    pageSize: 10,\n    rowsPerPageOptions: [10, 25, 50],\n    disableSelectionOnClick: true,\n    autoHeight: true,\n    headerHeight: 56,\n    columnHeaderHeight: 56,\n    getRowClassName: (params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n    },\n    isCellEditable: (params) => {\n              // 阻止总计行和已删除行被编辑\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable && typeof params.colDef.editable === 'function' ? \n                params.colDef.editable(params) : params.colDef.editable;\n    },\n    processRowUpdate: (newRow, oldRow) => {\n              // 确保COMMISSION是数字类型\n              if (newRow.COMMISSION !== undefined) {\n                if (typeof newRow.COMMISSION === 'string') {\n                  newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                }\n              }\n              return processRowUpdate(newRow);\n    },\n    onProcessRowUpdateError,\n    experimentalFeatures: { newEditingApi: true }\n  }), [gridData, columns, processRowUpdate, onProcessRowUpdateError]);\n  \n  // 使用useMemo优化DataGrid样式\n  const dataGridSx = useMemo(() => ({\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n  }), []);\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        {actionButtons}\n          </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%', minHeight: 400 }}>\n          <DataGrid\n            {...dataGridProps}\n            sx={dataGridSx}\n          />\n        </Box>\n      </Paper>\n      \n      {renderDialogs}\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,MAAM;IACzD,MAAMgD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGX,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdgD,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAGxB,IAAI,CAACyB,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,MAAM;IAC7CuE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C7B,aAAa,GAAG,IAAIA,aAAa,CAAC8B,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI9B,aAAa,IAAIA,aAAa,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG/B,aAAa,CAACqB,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG9E,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM+E,iBAAiB,GAAG/E,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMgF,WAAW,GAAGhF,MAAM,CAACkE,QAAQ,CAAC;;EAEpC;EACApE,SAAS,CAAC,MAAM;IACdkF,WAAW,CAACC,OAAO,GAAGf,QAAQ;EAChC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACApE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2C,YAAY,IAAIyB,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IAE1C,MAAMY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACxB;IACA,IAAIA,GAAG,GAAGH,iBAAiB,CAACE,OAAO,GAAG,GAAG,EAAE;MACvC;IACF;;IAEA;IACA,MAAMG,iBAAiB,GAAGpC,IAAI,CAACW,SAAS,CAACO,QAAQ,CAAC;IAClD,MAAMmB,cAAc,GAAGP,mBAAmB,CAACG,OAAO;IAElD,IAAII,cAAc,KAAKD,iBAAiB,EAAE;MACxChB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,QAAQ,CAACI,MAAM,CAAC;MAC9DQ,mBAAmB,CAACG,OAAO,GAAGG,iBAAiB;MAC/CL,iBAAiB,CAACE,OAAO,GAAGC,GAAG;MAC/BzC,YAAY,CAAC,CAAC,GAAGyB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEzB,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IAAE2F,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC;IACjD2F,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAhG,SAAS,CAAC,MAAM;IACd,IAAI0D,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAGlG,WAAW,CAAC,YAAY;IAClD,IAAI;MACF;MACA,MAAMmG,WAAW,GAAG,GAAGrE,OAAO,aAAaQ,MAAM,EAAE;;MAEnD;MACA,MAAM8D,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIpB,IAAI,CAAC,CAAC,CAACqB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAZ,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtE,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC,EAAE,CAACF,MAAM,EAAEE,OAAO,CAAC,CAAC;;EAErB;EACA,MAAMuE,qBAAqB,GAAG/G,WAAW,CAAC,YAAY;IACpD,IAAI;MACF,MAAM6B,KAAK,CAACmF,MAAM,CAAC,GAAGlF,OAAO,YAAYQ,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAvE,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACD,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,MAAM0E,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACnD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAGnH,WAAW,CAAEqC,IAAI,IAAK;IAC7C,MAAM+E,QAAQ,GAAG/E,IAAI,CAACgF,IAAI,CAACtD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIsC,QAAQ,EAAE;MACZ;MACA,MAAME,QAAQ,GAAGjF,IAAI,CAClBkF,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDsD,MAAM,CAAC,CAACC,GAAG,EAAE1D,GAAG,KAAK0D,GAAG,IAAIC,MAAM,CAAC3D,GAAG,CAAC4D,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/DtD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgD,QAAQ,CAAC;MACnCF,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAOjF,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuF,gBAAgB,GAAG5H,WAAW,CAAE6H,MAAM,IAAK;IAC/CxD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuD,MAAM,CAAC;;IAE7B;IACA,IAAIC,WAAW,GAAG7C,WAAW,CAACC,OAAO,CAACpB,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACa,EAAE,KAAKiD,MAAM,CAACjD,EAAE,GAAGiD,MAAM,GAAG9D,GAAI,CAAC;;IAEvF;IACA+D,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;;IAE3C;IACA1D,WAAW,CAAC0D,WAAW,CAAC;IACxBtC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOkC,MAAM;EACf,CAAC,EAAE,CAACV,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,uBAAuB,GAAIjB,KAAK,IAAK;IACzCtB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASoB,KAAK,CAACpB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;;EAED;EACA,MAAMqC,iBAAiB,GAAGhI,WAAW,CAAC,CAAC8F,KAAK,EAAEC,YAAY,KAAK;IAC7D;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkC,kBAAkB,GAAGjI,WAAW,CAAC,MAAM;IAC3C6F,gBAAgB,CAACI,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPR,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyC,kBAAkB,GAAGlI,WAAW,CAAEmI,MAAM,IAAK;IACjD,MAAM;MAAErC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClBzB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6D,MAAM,EAAE,MAAM,EAAErC,KAAK,CAAC;MAChD1B,WAAW,CAACgE,QAAQ,IAAI;QACtB,IAAIN,WAAW,GAAGM,QAAQ,CAACtE,GAAG,CAACC,GAAG,IAAI;UACpC,IAAIA,GAAG,CAACa,EAAE,KAAKkB,KAAK,EAAE;YACpB;YACA,IAAIqC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAGpE,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAEmE,MAAM;gBAAElE,iBAAiB,EAAEkE;cAAO,CAAC;YAC/D;UACF;UACA,OAAOpE,GAAG;QACZ,CAAC,CAAC;;QAEF;QACA+D,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;QAC3C,OAAOA,WAAW;MACpB,CAAC,CAAC;MACFtC,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAsC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACrC,aAAa,EAAEqC,kBAAkB,EAAEd,gBAAgB,CAAC,CAAC;;EAEzD;EACA,MAAMkB,mBAAmB,GAAGrI,WAAW,CAAC,MAAM;IAC5CsD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgF,oBAAoB,GAAGtI,WAAW,CAAC,MAAM;IAC7CsD,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmF,YAAY,GAAGvI,WAAW,CAAC,MAAM;IACrC,IAAImD,SAAS,CAACqF,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC5F,cAAc,CAAC6F,QAAQ,CAACtF,SAAS,CAACqF,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE3F,iBAAiB,CAACoD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE9C,SAAS,CAACqF,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE2C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI1F,cAAc,CAAC6F,QAAQ,CAACtF,SAAS,CAACqF,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDhD,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAACxC,SAAS,EAAEP,cAAc,EAAE0F,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG1I,WAAW,CAAEmI,MAAM,IAAK;IAC3CtF,iBAAiB,CAACoD,IAAI,IAAIA,IAAI,CAACsB,MAAM,CAACoB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC,CAAC;IAC/D3C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,eAAe,GAAG5I,WAAW,CAAE4E,EAAE,IAAK;IAC1CP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACAR,WAAW,CAACgE,QAAQ,IAAI;MACxB;MACE,MAAMS,WAAW,GAAGT,QAAQ,CAACf,IAAI,CAACtD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;MACzD,MAAMkE,eAAe,GAAGD,WAAW,GAAGA,WAAW,CAAClB,UAAU,GAAG,CAAC;MAChEtD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwE,eAAe,CAAC;;MAE/C;MACE,IAAIhB,WAAW,GAAGM,QAAQ,CAACtE,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;UACjB,OAAO;YAAE,GAAGb,GAAG;YAAEG,QAAQ,EAAE;UAAK,CAAC;QACnC;QACA,OAAOH,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA+D,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;;MAE3C;MACA,MAAMiB,cAAc,GAAGjB,WAAW,CAACP,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHiE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrE,EAAE,GAAGsE,CAAC,CAACtE,EAAE,CAAC,CAAC,CAAC;;MAE5C;MACAmE,cAAc,CAACI,OAAO,CAAC,CAACpF,GAAG,EAAEY,KAAK,KAAK;QACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MACpB,CAAC,CAAC;MAEA,OAAOmD,WAAW;IACpB,CAAC,CAAC;IAEFtC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC,EAAE,CAACwB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMiC,aAAa,GAAGpJ,WAAW,CAAE4E,EAAE,IAAK;IACxCP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;;IAEvB;IACAR,WAAW,CAACgE,QAAQ,IAAI;MACxB;MACE,MAAMiB,YAAY,GAAGjB,QAAQ,CAACf,IAAI,CAACtD,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;MAC1D,MAAMkE,eAAe,GAAGO,YAAY,GAAGA,YAAY,CAAC1B,UAAU,GAAG,CAAC;MAClEtD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwE,eAAe,CAAC;;MAE/C;MACE,IAAIhB,WAAW,GAAGM,QAAQ,CAACtE,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;UACjB,OAAO;YAAE,GAAGb,GAAG;YAAEG,QAAQ,EAAE;UAAM,CAAC;QACpC;QACA,OAAOH,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA+D,WAAW,GAAGX,gBAAgB,CAACW,WAAW,CAAC;;MAE3C;MACA,MAAMiB,cAAc,GAAGjB,WAAW,CAACP,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHiE,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrE,EAAE,GAAGsE,CAAC,CAACtE,EAAE,CAAC,CAAC,CAAC;;MAE5C;MACAmE,cAAc,CAACI,OAAO,CAAC,CAACpF,GAAG,EAAEY,KAAK,KAAK;QACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MACpB,CAAC,CAAC;MAEA,OAAOmD,WAAW;IACpB,CAAC,CAAC;IAEFtC,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC,EAAE,CAACwB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMmC,sBAAsB,GAAGtJ,WAAW,CAAC,YAAY;IACrD,IAAI;MACFwD,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM+F,eAAe,GAAGtE,WAAW,CAACC,OAAO;;MAE3C;MACA,MAAMsE,YAAY,GAAGD,eAAe,CACjChC,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAsF,YAAY,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACnE,EAAE,KAAK,QAAQ,IAAI,OAAOoE,CAAC,CAACpE,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOmE,CAAC,CAACnE,EAAE,GAAGoE,CAAC,CAACpE,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM2E,OAAO,GAAGD,YAAY,CAAC1F,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb+E,IAAI,EAAE3F,GAAG,CAAC2F,IAAI,GAAI,OAAO3F,GAAG,CAAC2F,IAAI,KAAK,QAAQ,IAAI3F,GAAG,CAAC2F,IAAI,CAACjB,QAAQ,CAAC,GAAG,CAAC,GAAG1E,GAAG,CAAC2F,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG5F,GAAG,CAAC2F,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE3F,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG6F,IAAI,CAACC,KAAK,CAAC9F,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF+F,EAAE,EAAE,OAAO/F,GAAG,CAAC+F,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAC9F,GAAG,CAAC+F,EAAE,CAAC,GAAG/F,GAAG,CAAC+F,EAAE,IAAI,EAAE;QAClE9F,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG8F,KAAK,EAAE,OAAOhG,GAAG,CAACiG,QAAQ,KAAK,QAAQ,GACpCjG,GAAG,CAACiG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGjG,GAAG,CAACiG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGlG,GAAG,CAACiG,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3ElG,GAAG,CAACiG,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOnG,GAAG,CAAC4D,UAAU,KAAK,QAAQ,GAAG5D,GAAG,CAAC4D,UAAU,CAACsC,OAAO,CAAC,CAAC,CAAC,GAAGlG,GAAG,CAAC4D,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMwC,WAAW,GAAGZ,eAAe,CAChChC,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAAC4D,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAE1D,GAAG,KAAK0D,GAAG,IAAI,OAAO1D,GAAG,CAAC4D,UAAU,KAAK,QAAQ,GAAG5D,GAAG,CAAC4D,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMyC,QAAQ,GAAG,MAAMvI,KAAK,CAACwI,IAAI,CAAC,GAAGvI,OAAO,oBAAoB,EAAE;QAChEO,IAAI,EAAEoH,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC3H,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI8H,QAAQ,CAAC/H,IAAI,IAAI+H,QAAQ,CAAC/H,IAAI,CAACiI,KAAK,EAAE;QACxC;QACA,MAAMnE,WAAW,GAAG,GAAGrE,OAAO,CAAC6H,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAC/H,IAAI,CAACkI,MAAM,EAAE;;QAExE;QACA,MAAMnE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAIpB,IAAI,CAAC,CAAC,CAACqB,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEL,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;QAE/B;QACAZ,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACA6E,UAAU,CAAC,MAAM;UACf,MAAMC,MAAM,GAAGpE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CmE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGzE,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC8D,MAAM,CAAC;UACjCD,UAAU,CAAC,MAAM;YACfnE,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC4D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACRnC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMwI,aAAa,GAAG5K,OAAO,CAAC,mBAC5B8B,OAAA,CAAAE,SAAA;IAAA6I,QAAA,gBAEE/I,OAAA,CAACvB,MAAM;MACLgF,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzBuF,OAAO,EAAE/C,kBAAmB;MAC5BgD,SAAS;MACTC,QAAQ,EAAC,IAAI;MACbC,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAP,QAAA,gBAEnB/I,OAAA,CAACtB,WAAW;QAAAqK,QAAA,eACV/I,OAAA,CAAC7B,GAAG;UAACoL,EAAE,EAAE;YAAEZ,OAAO,EAAE,MAAM;YAAEa,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAV,QAAA,gBAClF/I,OAAA,CAAC5B,UAAU;YAACsL,OAAO,EAAC,IAAI;YAAAX,QAAA,EAAC;UAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C9J,OAAA,CAAC3B,MAAM;YACL0L,SAAS,eAAE/J,OAAA,CAACP,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBE,OAAO,EAAE3D,mBAAoB;YAC7B4D,KAAK,EAAC,SAAS;YAAAlB,QAAA,EAChB;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9J,OAAA,CAACrB,aAAa;QAACuL,QAAQ;QAAAnB,QAAA,eACrB/I,OAAA,CAACnB,IAAI;UAAAkK,QAAA,EACFnI,cAAc,CAACkB,GAAG,CAAEqE,MAAM,iBACzBnG,OAAA,CAAClB,QAAQ;YAEPqL,cAAc;YACdC,eAAe,eACbpK,OAAA,CAACd,UAAU;cACTmL,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnBL,OAAO,EAAEA,CAAA,KAAMtD,YAAY,CAACP,MAAM,CAAE;cAAA4C,QAAA,eAEpC/I,OAAA,CAACN,UAAU;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAf,QAAA,eAED/I,OAAA,CAACjB,cAAc;cAACiL,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAACC,MAAM,CAAE;cAAA4C,QAAA,eACxD/I,OAAA,CAAChB,YAAY;gBAACsL,OAAO,EAAEnE;cAAO;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZ3D,MAAM;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB9J,OAAA,CAACpB,aAAa;QAAAmK,QAAA,eACZ/I,OAAA,CAAC3B,MAAM;UAAC2L,OAAO,EAAE/D,kBAAmB;UAAA8C,QAAA,EAAC;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9J,OAAA,CAACvB,MAAM;MACLgF,IAAI,EAAEpC,eAAgB;MACtB2H,OAAO,EAAE1C,oBAAqB;MAC9B2C,SAAS;MACTC,QAAQ,EAAC,IAAI;MACbC,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAP,QAAA,gBAEnB/I,OAAA,CAACtB,WAAW;QAAAqK,QAAA,EAAC;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC9J,OAAA,CAACrB,aAAa;QAAAoK,QAAA,eACZ/I,OAAA,CAACf,SAAS;UACRsL,SAAS;UACTC,MAAM,EAAC,OAAO;UACd5H,EAAE,EAAC,MAAM;UACT6H,KAAK,EAAC,0BAAM;UACZC,IAAI,EAAC,MAAM;UACXzB,SAAS;UACTS,OAAO,EAAC,UAAU;UAClBiB,KAAK,EAAExJ,SAAU;UACjByJ,QAAQ,EAAGC,CAAC,IAAKzJ,YAAY,CAACyJ,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB9J,OAAA,CAACpB,aAAa;QAAAmK,QAAA,gBACZ/I,OAAA,CAAC3B,MAAM;UAAC2L,OAAO,EAAE1D,oBAAqB;UAAAyC,QAAA,EAAC;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD9J,OAAA,CAAC3B,MAAM;UAAC2L,OAAO,EAAEzD,YAAa;UAAC0D,KAAK,EAAC,SAAS;UAAAlB,QAAA,EAAC;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET9J,OAAA,CAACzB,QAAQ;MACPkF,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBsH,gBAAgB,EAAE,IAAK;MACvB/B,OAAO,EAAEhF,mBAAoB;MAC7BgH,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAnC,QAAA,eAE1D/I,OAAA,CAACxB,KAAK;QAACwK,OAAO,EAAEhF,mBAAoB;QAACL,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAAoF,QAAA,EAC9DxF,QAAQ,CAACG;MAAO;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACX,CACH,EAAE,CAAClG,aAAa,CAACH,IAAI,EAAEwC,kBAAkB,EAAEI,mBAAmB,EAAEzF,cAAc,EAC3E8F,YAAY,EAAER,kBAAkB,EAAE7E,eAAe,EAAEiF,oBAAoB,EACvEnF,SAAS,EAAEoF,YAAY,EAAEhD,QAAQ,EAAES,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAMmH,aAAa,GAAGjN,OAAO,CAAC,mBAC5B8B,OAAA,CAAC7B,GAAG;IAAA4K,QAAA,gBACF/I,OAAA,CAAC3B,MAAM;MACLqL,OAAO,EAAC,WAAW;MACnBO,KAAK,EAAC,SAAS;MACfF,SAAS,eAAE/J,OAAA,CAACT,YAAY;QAAAoK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC5BE,OAAO,EAAE9F,mBAAoB;MAC7BqF,EAAE,EAAE;QAAE6B,EAAE,EAAE;MAAE,CAAE;MAAArC,QAAA,EACf;IAED;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET9J,OAAA,CAAC3B,MAAM;MACLqL,OAAO,EAAC,WAAW;MACnBO,KAAK,EAAC,SAAS;MACfF,SAAS,eAAE/J,OAAA,CAACJ,gBAAgB;QAAA+J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAChCE,OAAO,EAAE1C,sBAAuB;MAChC+D,QAAQ,EAAE9J,oBAAqB;MAC/BgI,EAAE,EAAE;QAAE6B,EAAE,EAAE;MAAE,CAAE;MAAArC,QAAA,EAEbxH,oBAAoB,GAAG,QAAQ,GAAG;IAAM;MAAAoI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAET9J,OAAA,CAAC3B,MAAM;MACLqL,OAAO,EAAC,UAAU;MAClBK,SAAS,eAAE/J,OAAA,CAACR,cAAc;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC9BE,OAAO,EAAEjF,qBAAsB;MAAAgE,QAAA,EAChC;IAED;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN,EAAE,CAAC5F,mBAAmB,EAAEoD,sBAAsB,EAAE/F,oBAAoB,EAAEwD,qBAAqB,CAAC,CAAC;;EAE9F;EACA,IAAI,CAAC5C,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEvC,OAAA,CAAC7B,GAAG;MAACoL,EAAE,EAAE;QAAE+B,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxC,QAAA,gBACtC/I,OAAA,CAAC5B,UAAU;QAACsL,OAAO,EAAC,IAAI;QAACO,KAAK,EAAC,gBAAgB;QAAAlB,QAAA,EAAC;MAEhD;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9J,OAAA,CAAC3B,MAAM;QACLqL,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAEzJ,OAAQ;QACjBgJ,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,EACf;MAED;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAM2B,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC5E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC9E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC;EAAG;EACtF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACjF;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACpF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC,CAAE;EAAA,CACpF;;EAED;EACA,MAAMC,OAAO,GAAG5N,OAAO,CAAC,MAAM;IAC5B,OAAOuN,WAAW,CAAC3J,GAAG,CAACiK,GAAG,IAAI;MAC9B,IAAI,CAAC5J,QAAQ,CAAC,CAAC,CAAC,CAAC6J,cAAc,CAACD,GAAG,CAACL,KAAK,CAAC,IAAIK,GAAG,CAACL,KAAK,KAAK,SAAS,IAAIK,GAAG,CAACL,KAAK,KAAK,QAAQ,IAAIK,GAAG,CAACL,KAAK,KAAK,YAAY,EAAE;QAC7H;QACA,OAAO,IAAI;MACb;;MAEA;MACA,IAAIK,GAAG,CAACL,KAAK,KAAK,SAAS,EAAE;QAC3B,OAAO;UACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;UAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;UAC1BM,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,GAAG;UACVN,QAAQ,EAAE,KAAK;UACfO,UAAU,EAAGjH,MAAM,IAAK;YACtB;YACA,IAAIA,MAAM,CAACnD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;cAC7B,OAAO,EAAE;YACX;;YAEA;YACA,IAAIoC,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE;cACvB,MAAMkK,iBAAiB,GAAGlH,MAAM,CAACnD,GAAG,CAACE,iBAAiB,IAAI,KAAK;cAC/D,oBACEjC,OAAA,CAACX,OAAO;gBAACgN,KAAK,EAAEnH,MAAM,CAACnD,GAAG,CAACE,iBAAiB,IAAI,EAAG;gBAACqK,KAAK;gBAACC,SAAS,EAAC,KAAK;gBAAAxD,QAAA,eACvE/I,OAAA,CAACZ,IAAI;kBACHqL,KAAK,EAAE2B,iBAAkB;kBACzBnC,KAAK,EAAC,SAAS;kBACfP,OAAO,EAAC,UAAU;kBAClB8C,IAAI,EAAC,OAAO;kBACZjD,EAAE,EAAE;oBACFL,QAAQ,EAAE,MAAM;oBAChBuD,OAAO,EAAE,GAAG;oBACZ,kBAAkB,EAAE;sBAClBC,QAAQ,EAAE,QAAQ;sBAClBC,YAAY,EAAE,UAAU;sBACxBC,UAAU,EAAE,QAAQ;sBACpBjE,OAAO,EAAE;oBACX;kBACF;gBAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAEd;;YAEA;YACA,IAAI+C,UAAU,GAAG,MAAM;YACvB,IAAIC,iBAAiB,GAAG,KAAK;YAE7B,IAAI5H,MAAM,CAACnD,GAAG,CAACE,iBAAiB,IAAIiD,MAAM,CAACnD,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;cAC3E4K,UAAU,GAAG3H,MAAM,CAACnD,GAAG,CAACE,iBAAiB;cACzC6K,iBAAiB,GAAG,IAAI;YAC1B;;YAEE;YACF,MAAMC,WAAW,GAAGA,CAAA,KAAM;cACtB/G,iBAAiB,CAACd,MAAM,CAACnD,GAAG,CAACa,EAAE,EAAEsC,MAAM,CAACyF,KAAK,IAAI,EAAE,CAAC;YACxD,CAAC;YAED,oBACE3K,OAAA,CAACX,OAAO;cAACgN,KAAK,EAAES,iBAAiB,GAAGD,UAAU,GAAG,EAAG;cAACP,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAxD,QAAA,eACxE/I,OAAA,CAACZ,IAAI;gBACHqL,KAAK,EAAEoC,UAAW;gBAClB5C,KAAK,EAAE6C,iBAAiB,GAAG,SAAS,GAAG,SAAU;gBACjDpD,OAAO,EAAEoD,iBAAiB,GAAG,QAAQ,GAAG,UAAW;gBACnDN,IAAI,EAAC,OAAO;gBACZxC,OAAO,EAAE+C,WAAY;gBACrBC,SAAS;gBACTzD,EAAE,EAAE;kBACFL,QAAQ,EAAE,MAAM;kBAChB+D,MAAM,EAAE,SAAS;kBACjB,kBAAkB,EAAE;oBAClBP,QAAQ,EAAE,QAAQ;oBAClBC,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE,QAAQ;oBACpBjE,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;QACF,CAAC;MACH;;MAEA;MACA,IAAIiC,GAAG,CAACL,KAAK,KAAK,QAAQ,EAAE;QAC1B,OAAO;UACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;UAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;UAC1BM,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,GAAG;UACVN,QAAQ,EAAE,KAAK;UACfO,UAAU,EAAGjH,MAAM,IAAK;YACtB;YACA,IAAIA,MAAM,CAACnD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;cAC7B,OAAO,EAAE;YACX;;YAEA;YACA,IAAIoC,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE;cACvB,oBACElC,OAAA,CAACZ,IAAI;gBACHqL,KAAK,EAAC,cAAI;gBACVR,KAAK,EAAC,SAAS;gBACfuC,IAAI,EAAC,OAAO;gBACZU,IAAI,eAAElN,OAAA,CAACL,QAAQ;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBE,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAAClC,MAAM,CAACnD,GAAG,CAACa,EAAE,CAAE;gBAC5C2G,EAAE,EAAE;kBAAE0D,MAAM,EAAE;gBAAU;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAEN;;YAEA;YACA,oBACE9J,OAAA,CAACZ,IAAI;cACHqL,KAAK,EAAC,cAAI;cACVR,KAAK,EAAC,OAAO;cACbuC,IAAI,EAAC,OAAO;cACZU,IAAI,eAAElN,OAAA,CAACN,UAAU;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBE,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAAC1B,MAAM,CAACnD,GAAG,CAACa,EAAE,CAAE;cAC9C2G,EAAE,EAAE;gBAAE0D,MAAM,EAAE;cAAU;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAEN;QACF,CAAC;MACH;MAEA,OAAO;QACL4B,KAAK,EAAEK,GAAG,CAACL,KAAK;QAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;QAC1BM,IAAI,EAAEF,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,CAAC,GAC9BK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAC7BK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,GAAG,GAC9BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;QACtCQ,KAAK,EAAEH,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,EAAE,GAC7BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,EAAE,GAC3BK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;QAC1CE,QAAQ,EAAE1G,MAAM,IAAI;UAClB;UACA,IAAIA,MAAM,CAACnD,GAAG,IAAImD,MAAM,CAACnD,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;YAC3C,OAAO,KAAK;UACd;UACA;UACA,IAAIoC,MAAM,CAACnD,GAAG,IAAImD,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE;YACrC,OAAO,KAAK;UACd;UACA,OAAO6J,GAAG,CAACH,QAAQ,KAAK,KAAK;QAC/B,CAAC;QACDO,UAAU,EAAGjH,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAACnD,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIiJ,GAAG,CAACL,KAAK,KAAK,YAAY,EAAE;YAC3D,oBACE1L,OAAA,CAAC5B,UAAU;cAACsL,OAAO,EAAC,OAAO;cAACyD,UAAU,EAAC,MAAM;cAAClD,KAAK,EAAC,SAAS;cAAAlB,QAAA,EAC1D,OAAO7D,MAAM,CAACyF,KAAK,KAAK,QAAQ,GAAGzF,MAAM,CAACyF,KAAK,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAC1D,OAAO/C,MAAM,CAACyF,KAAK,KAAK,QAAQ,IAAI,CAACyC,KAAK,CAAC1H,MAAM,CAACR,MAAM,CAACyF,KAAK,CAAC,CAAC,GAAGjF,MAAM,CAACR,MAAM,CAACyF,KAAK,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAClG/C,MAAM,CAACyF;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAEjB;;UAEA;UACA,IAAI5E,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACElC,OAAA,CAAC5B,UAAU;cAACsL,OAAO,EAAC,OAAO;cAACO,KAAK,EAAC,eAAe;cAACV,EAAE,EAAE;gBAAE8D,cAAc,EAAE;cAAe,CAAE;cAAAtE,QAAA,EACtF7D,MAAM,CAACyF;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAEjB;;UAEA;UACA,IAAIiC,GAAG,CAACL,KAAK,KAAK,MAAM,IAAIxG,MAAM,CAACyF,KAAK,EAAE;YACxC,OAAOzF,MAAM,CAACyF,KAAK,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC;;UAEA;UACA,IAAIoE,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YAC1D,OAAO/C,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACyF,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIoB,GAAG,CAACL,KAAK,KAAK,OAAO,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YAC7D,OAAO/C,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACyF,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIoB,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YAC1D,OAAO/C,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACyF,KAAK,CAAC;UACjC;;UAEA;UACA,IAAIoB,GAAG,CAACL,KAAK,KAAK,UAAU,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YAChE,OAAOzF,MAAM,CAACyF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGzF,MAAM,CAACyF,KAAK,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG/C,MAAM,CAACyF,KAAK,CAAC1C,OAAO,CAAC,CAAC,CAAC;UACnF;;UAEA;UACA,IAAI8D,GAAG,CAACL,KAAK,KAAK,YAAY,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YAClE,OAAOzF,MAAM,CAACyF,KAAK,CAAC1C,OAAO,CAAC,CAAC,CAAC;UAChC,CAAC,MAAM,IAAI8D,GAAG,CAACL,KAAK,KAAK,YAAY,IAAI,OAAOxG,MAAM,CAACyF,KAAK,KAAK,QAAQ,IAAI,CAACyC,KAAK,CAAC1H,MAAM,CAACR,MAAM,CAACyF,KAAK,CAAC,CAAC,EAAE;YACzG,OAAOjF,MAAM,CAACR,MAAM,CAACyF,KAAK,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC;UACxC;;UAEA;UACA,IAAI,OAAO/C,MAAM,CAACyF,KAAK,KAAK,QAAQ,EAAE;YACpC,OAAOzF,MAAM,CAACyF,KAAK;UACrB;UAEA,OAAOzF,MAAM,CAACyF,KAAK;QACrB;MACF,CAAC;IACH,CAAC,CAAC,CAACpF,MAAM,CAAC+H,OAAO,CAAC,CAAC,CAAC;EACpB,CAAC,EAAE,CAACnL,QAAQ,EAAEyE,eAAe,EAAEQ,aAAa,EAAEpB,iBAAiB,CAAC,CAAC;;EAEjE;EACA,MAAMuH,aAAa,GAAGrP,OAAO,CAAC,OAAO;IACnCsP,IAAI,EAAErL,QAAQ;IACd2J,OAAO;IACP2B,QAAQ,EAAE,EAAE;IACZC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAChCC,uBAAuB,EAAE,IAAI;IAC7BC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAG7I,MAAM,IAAK;MACnB,IAAIA,MAAM,CAACnD,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;MAC1C,IAAIqC,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;MAC7C,OAAO,EAAE;IACnB,CAAC;IACD8L,cAAc,EAAG9I,MAAM,IAAK;MAClB;MACA,IAAIA,MAAM,CAACnD,GAAG,CAACc,OAAO,IAAIqC,MAAM,CAACnD,GAAG,CAACG,QAAQ,EAAE;QAC7C,OAAO,KAAK;MACd;MACA,OAAOgD,MAAM,CAAC+I,MAAM,CAACrC,QAAQ,IAAI,OAAO1G,MAAM,CAAC+I,MAAM,CAACrC,QAAQ,KAAK,UAAU,GAC3E1G,MAAM,CAAC+I,MAAM,CAACrC,QAAQ,CAAC1G,MAAM,CAAC,GAAGA,MAAM,CAAC+I,MAAM,CAACrC,QAAQ;IACnE,CAAC;IACDhG,gBAAgB,EAAEA,CAACC,MAAM,EAAEqI,MAAM,KAAK;MAC5B;MACA,IAAIrI,MAAM,CAACF,UAAU,KAAKlD,SAAS,EAAE;QACnC,IAAI,OAAOoD,MAAM,CAACF,UAAU,KAAK,QAAQ,EAAE;UACzCE,MAAM,CAACF,UAAU,GAAGD,MAAM,CAACG,MAAM,CAACF,UAAU,CAAC,IAAI,CAAC;QACpD;MACF;MACA,OAAOC,gBAAgB,CAACC,MAAM,CAAC;IACzC,CAAC;IACDE,uBAAuB;IACvBoI,oBAAoB,EAAE;MAAEC,aAAa,EAAE;IAAK;EAC9C,CAAC,CAAC,EAAE,CAACjM,QAAQ,EAAE2J,OAAO,EAAElG,gBAAgB,EAAEG,uBAAuB,CAAC,CAAC;;EAEnE;EACA,MAAMsI,UAAU,GAAGnQ,OAAO,CAAC,OAAO;IACtB,cAAc,EAAE;MACdoQ,eAAe,EAAE,0BAA0B;MAC3CnB,UAAU,EAAE;IACd,CAAC;IACD,gBAAgB,EAAE;MAChBmB,eAAe,EAAE,0BAA0B;MAC3CrE,KAAK,EAAE;IACT,CAAC;IACD,qBAAqB,EAAE;MACrB2C,UAAU,EAAE,QAAQ;MACpB2B,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE;IACX,CAAC;IACD,8BAA8B,EAAE;MAC9BF,eAAe,EAAE;IACnB,CAAC;IACD,gCAAgC,EAAE;MAChCG,SAAS,EAAE;IACb,CAAC;IACD,qBAAqB,EAAE;MACrB/B,QAAQ,EAAE;IACZ,CAAC;IACD,qBAAqB,EAAE;MACrBA,QAAQ,EAAE,SAAS;MACnBgC,MAAM,EAAE;IACV,CAAC;IACD,6BAA6B,EAAE;MAC7BF,OAAO,EAAE,OAAO;MAChB5B,UAAU,EAAE,QAAQ;MACpB2B,UAAU,EAAE;IACd,CAAC;IACD,kCAAkC,EAAE;MAClC3B,UAAU,EAAE,QAAQ;MACpBF,QAAQ,EAAE,SAAS;MACnB6B,UAAU,EAAE,MAAM;MAClBpB,UAAU,EAAE;IACd;EACZ,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,oBACEnN,OAAA,CAAC7B,GAAG;IAAA4K,QAAA,gBACF/I,OAAA,CAAC7B,GAAG;MAACoL,EAAE,EAAE;QAAEZ,OAAO,EAAE,MAAM;QAAEa,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEkF,EAAE,EAAE;MAAE,CAAE;MAAA5F,QAAA,gBACzF/I,OAAA,CAAC5B,UAAU;QAACsL,OAAO,EAAC,IAAI;QAACkF,YAAY;QAAA7F,QAAA,EAAC;MAEtC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZqB,aAAa;IAAA;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEV9J,OAAA,CAAC1B,KAAK;MAACiL,EAAE,EAAE;QAAE2C,KAAK,EAAE,MAAM;QAAEQ,QAAQ,EAAE;MAAS,CAAE;MAAA3D,QAAA,eAC/C/I,OAAA,CAAC7B,GAAG;QAACoL,EAAE,EAAE;UAAEsF,MAAM,EAAE,MAAM;UAAE3C,KAAK,EAAE,MAAM;UAAE4C,SAAS,EAAE;QAAI,CAAE;QAAA/F,QAAA,eACzD/I,OAAA,CAACV,QAAQ;UAAA,GACHiO,aAAa;UACjBhE,EAAE,EAAE8E;QAAW;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEPhB,aAAa;EAAA;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAACnJ,EAAA,CAv5BIP,aAAa;AAAA2O,EAAA,GAAb3O,aAAa;AAy5BnB,eAAeA,aAAa;AAAC,IAAA2O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}