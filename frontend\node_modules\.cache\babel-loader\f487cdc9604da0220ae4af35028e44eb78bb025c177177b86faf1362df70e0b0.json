{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF } from './gridStringColDef';\nimport { renderBooleanCell } from '../components/cell/GridBooleanCell';\nimport { renderEditBooleanCell } from '../components/cell/GridEditBooleanCell';\nimport { gridNumberComparator } from '../hooks/features/sorting/gridSortingUtils';\nimport { getGridBooleanOperators } from './gridBooleanOperators';\nfunction gridBooleanFormatter(_ref) {\n  let {\n    value,\n    api\n  } = _ref;\n  return value ? api.getLocaleText('booleanCellTrueLabel') : api.getLocaleText('booleanCellFalseLabel');\n}\nconst stringToBoolean = value => {\n  switch (value.toLowerCase().trim()) {\n    case 'true':\n    case 'yes':\n    case '1':\n      return true;\n    case 'false':\n    case 'no':\n    case '0':\n    case 'null':\n    case 'undefined':\n      return false;\n    default:\n      return undefined;\n  }\n};\nexport const GRID_BOOLEAN_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'boolean',\n  align: 'center',\n  headerAlign: 'center',\n  renderCell: renderBooleanCell,\n  renderEditCell: renderEditBooleanCell,\n  sortComparator: gridNumberComparator,\n  valueFormatter: gridBooleanFormatter,\n  filterOperators: getGridBooleanOperators(),\n  getApplyQuickFilterFn: undefined,\n  getApplyQuickFilterFnV7: undefined,\n  // @ts-ignore\n  aggregable: false,\n  // @ts-ignore\n  pastedValueParser: value => stringToBoolean(value)\n});", "map": {"version": 3, "names": ["_extends", "GRID_STRING_COL_DEF", "renderBooleanCell", "renderEditBooleanCell", "gridNumberComparator", "getGridBooleanOperators", "gridBooleanFormatter", "_ref", "value", "api", "getLocaleText", "stringToBoolean", "toLowerCase", "trim", "undefined", "GRID_BOOLEAN_COL_DEF", "type", "align", "headerAlign", "renderCell", "renderEditCell", "sortComparator", "valueFormatter", "filterOperators", "getApplyQuickFilterFn", "getApplyQuickFilterFnV7", "aggregable", "pastedValueParser"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/colDef/gridBooleanColDef.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF } from './gridStringColDef';\nimport { renderBooleanCell } from '../components/cell/GridBooleanCell';\nimport { renderEditBooleanCell } from '../components/cell/GridEditBooleanCell';\nimport { gridNumberComparator } from '../hooks/features/sorting/gridSortingUtils';\nimport { getGridBooleanOperators } from './gridBooleanOperators';\nfunction gridBooleanFormatter({\n  value,\n  api\n}) {\n  return value ? api.getLocaleText('booleanCellTrueLabel') : api.getLocaleText('booleanCellFalseLabel');\n}\nconst stringToBoolean = value => {\n  switch (value.toLowerCase().trim()) {\n    case 'true':\n    case 'yes':\n    case '1':\n      return true;\n    case 'false':\n    case 'no':\n    case '0':\n    case 'null':\n    case 'undefined':\n      return false;\n    default:\n      return undefined;\n  }\n};\nexport const GRID_BOOLEAN_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'boolean',\n  align: 'center',\n  headerAlign: 'center',\n  renderCell: renderBooleanCell,\n  renderEditCell: renderEditBooleanCell,\n  sortComparator: gridNumberComparator,\n  valueFormatter: gridBooleanFormatter,\n  filterOperators: getGridBooleanOperators(),\n  getApplyQuickFilterFn: undefined,\n  getApplyQuickFilterFnV7: undefined,\n  // @ts-ignore\n  aggregable: false,\n  // @ts-ignore\n  pastedValueParser: value => stringToBoolean(value)\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,oBAAoB,QAAQ,4CAA4C;AACjF,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,oBAAoBA,CAAAC,IAAA,EAG1B;EAAA,IAH2B;IAC5BC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EACC,OAAOC,KAAK,GAAGC,GAAG,CAACC,aAAa,CAAC,sBAAsB,CAAC,GAAGD,GAAG,CAACC,aAAa,CAAC,uBAAuB,CAAC;AACvG;AACA,MAAMC,eAAe,GAAGH,KAAK,IAAI;EAC/B,QAAQA,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAChC,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,GAAG;MACN,OAAO,IAAI;IACb,KAAK,OAAO;IACZ,KAAK,IAAI;IACT,KAAK,GAAG;IACR,KAAK,MAAM;IACX,KAAK,WAAW;MACd,OAAO,KAAK;IACd;MACE,OAAOC,SAAS;EACpB;AACF,CAAC;AACD,OAAO,MAAMC,oBAAoB,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEC,mBAAmB,EAAE;EACpEe,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,QAAQ;EACrBC,UAAU,EAAEjB,iBAAiB;EAC7BkB,cAAc,EAAEjB,qBAAqB;EACrCkB,cAAc,EAAEjB,oBAAoB;EACpCkB,cAAc,EAAEhB,oBAAoB;EACpCiB,eAAe,EAAElB,uBAAuB,CAAC,CAAC;EAC1CmB,qBAAqB,EAAEV,SAAS;EAChCW,uBAAuB,EAAEX,SAAS;EAClC;EACAY,UAAU,EAAE,KAAK;EACjB;EACAC,iBAAiB,EAAEnB,KAAK,IAAIG,eAAe,CAACH,KAAK;AACnD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}