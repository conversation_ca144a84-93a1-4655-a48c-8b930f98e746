{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\FileUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Box, Typography, Paper, Button, CircularProgress } from '@mui/material';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = 'http://localhost:5000/api';\nconst FileUpload = ({\n  onFileUploaded,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = React.useState(false);\n  const onDrop = useCallback(async acceptedFiles => {\n    // 只处理第一个文件\n    const file = acceptedFiles[0];\n    if (!file) return;\n\n    // 检查文件类型\n    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\n      onError('只支持Excel文件 (.xlsx, .xls)');\n      return;\n    }\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await axios.post(`${API_URL}/upload`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data) {\n        onFileUpload(response.data);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('上传文件出错:', error);\n      onError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || '上传文件失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  }, [onFileUpload, onError]);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\n      'application/vnd.ms-excel': ['.xls']\n    },\n    multiple: false\n  });\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"\\u8BF7\\u4E0A\\u4F20Excel\\u6587\\u4EF6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      ...getRootProps(),\n      sx: {\n        p: 5,\n        mt: 2,\n        mb: 3,\n        border: '2px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.400',\n        backgroundColor: isDragActive ? 'rgba(25, 118, 210, 0.04)' : 'background.paper',\n        cursor: 'pointer',\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          borderColor: 'primary.main',\n          backgroundColor: 'rgba(25, 118, 210, 0.04)'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 60,\n            color: 'primary.main',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: isDragActive ? '释放文件以上传' : '拖拽文件到此处，或点击选择文件'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"\\u652F\\u6301 .xlsx \\u548C .xls \\u683C\\u5F0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      component: \"label\",\n      disabled: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 20\n      }, this),\n      children: [\"\\u9009\\u62E9\\u6587\\u4EF6\", /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".xlsx,.xls\",\n        hidden: true,\n        onChange: e => {\n          if (e.target.files && e.target.files[0]) {\n            onDrop([e.target.files[0]]);\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"WVy50pYmNLCdrZXot1tNb61M0rc=\", false, function () {\n  return [useDropzone];\n});\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useCallback", "useDropzone", "Box", "Typography", "Paper", "<PERSON><PERSON>", "CircularProgress", "CloudUploadIcon", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "FileUpload", "onFileUploaded", "onError", "_s", "loading", "setLoading", "useState", "onDrop", "acceptedFiles", "file", "name", "endsWith", "formData", "FormData", "append", "response", "post", "headers", "data", "onFileUpload", "error", "_error$response", "_error$response$data", "console", "getRootProps", "getInputProps", "isDragActive", "accept", "multiple", "sx", "textAlign", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "mt", "mb", "border", "borderColor", "backgroundColor", "cursor", "transition", "fontSize", "color", "component", "disabled", "startIcon", "type", "hidden", "onChange", "e", "target", "files", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/FileUpload.js"], "sourcesContent": ["import React, { useCallback } from 'react';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Paper, \r\n  Button,\r\n  CircularProgress\r\n} from '@mui/material';\r\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\r\nimport axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:5000/api';\r\n\r\nconst FileUpload = ({ onFileUploaded, onError }) => {\r\n  const [loading, setLoading] = React.useState(false);\r\n\r\n  const onDrop = useCallback(async (acceptedFiles) => {\r\n    // 只处理第一个文件\r\n    const file = acceptedFiles[0];\r\n    \r\n    if (!file) return;\r\n    \r\n    // 检查文件类型\r\n    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\r\n      onError('只支持Excel文件 (.xlsx, .xls)');\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      \r\n      const response = await axios.post(`${API_URL}/upload`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      \r\n      if (response.data) {\r\n        onFileUpload(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('上传文件出错:', error);\r\n      onError(error.response?.data?.error || '上传文件失败，请重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [onFileUpload, onError]);\r\n  \r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\r\n      'application/vnd.ms-excel': ['.xls']\r\n    },\r\n    multiple: false\r\n  });\r\n  \r\n  return (\r\n    <Box sx={{ textAlign: 'center' }}>\r\n      <Typography variant=\"h6\" gutterBottom>\r\n        请上传Excel文件\r\n      </Typography>\r\n      \r\n      <Paper \r\n        {...getRootProps()} \r\n        sx={{\r\n          p: 5,\r\n          mt: 2,\r\n          mb: 3,\r\n          border: '2px dashed',\r\n          borderColor: isDragActive ? 'primary.main' : 'grey.400',\r\n          backgroundColor: isDragActive ? 'rgba(25, 118, 210, 0.04)' : 'background.paper',\r\n          cursor: 'pointer',\r\n          transition: 'all 0.3s ease',\r\n          '&:hover': {\r\n            borderColor: 'primary.main',\r\n            backgroundColor: 'rgba(25, 118, 210, 0.04)'\r\n          }\r\n        }}\r\n      >\r\n        <input {...getInputProps()} />\r\n        \r\n        {loading ? (\r\n          <CircularProgress />\r\n        ) : (\r\n          <>\r\n            <CloudUploadIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              {isDragActive ? '释放文件以上传' : '拖拽文件到此处，或点击选择文件'}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">\r\n              支持 .xlsx 和 .xls 格式\r\n            </Typography>\r\n          </>\r\n        )}\r\n      </Paper>\r\n      \r\n      <Button \r\n        variant=\"contained\" \r\n        component=\"label\"\r\n        disabled={loading}\r\n        startIcon={<CloudUploadIcon />}\r\n      >\r\n        选择文件\r\n        <input\r\n          type=\"file\"\r\n          accept=\".xlsx,.xls\"\r\n          hidden\r\n          onChange={(e) => {\r\n            if (e.target.files && e.target.files[0]) {\r\n              onDrop([e.target.files[0]]);\r\n            }\r\n          }}\r\n        />\r\n      </Button>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FileUpload; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,WAAW,QAAQ,OAAO;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,OAAO,GAAG,2BAA2B;AAE3C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMC,MAAM,GAAGrB,WAAW,CAAC,MAAOsB,aAAa,IAAK;IAClD;IACA,MAAMC,IAAI,GAAGD,aAAa,CAAC,CAAC,CAAC;IAE7B,IAAI,CAACC,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACF,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC/DT,OAAO,CAAC,0BAA0B,CAAC;MACnC;IACF;IAEAG,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,MAAMM,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,IAAI,CAAC,GAAGjB,OAAO,SAAS,EAAEa,QAAQ,EAAE;QAC/DK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACjBC,YAAY,CAACJ,QAAQ,CAACG,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlB,OAAO,CAAC,EAAAmB,eAAA,GAAAD,KAAK,CAACL,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,YAAY,CAAC;IACtD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACc,YAAY,EAAEjB,OAAO,CAAC,CAAC;EAE3B,MAAM;IAAEsB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGvC,WAAW,CAAC;IAChEoB,MAAM;IACNoB,MAAM,EAAE;MACN,mEAAmE,EAAE,CAAC,OAAO,CAAC;MAC9E,0BAA0B,EAAE,CAAC,MAAM;IACrC,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,oBACEhC,OAAA,CAACR,GAAG;IAACyC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAC/BnC,OAAA,CAACP,UAAU;MAAC2C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbzC,OAAA,CAACN,KAAK;MAAA,GACAkC,YAAY,CAAC,CAAC;MAClBK,EAAE,EAAE;QACFS,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAEhB,YAAY,GAAG,cAAc,GAAG,UAAU;QACvDiB,eAAe,EAAEjB,YAAY,GAAG,0BAA0B,GAAG,kBAAkB;QAC/EkB,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,eAAe;QAC3B,SAAS,EAAE;UACTH,WAAW,EAAE,cAAc;UAC3BC,eAAe,EAAE;QACnB;MACF,CAAE;MAAAZ,QAAA,gBAEFnC,OAAA;QAAA,GAAW6B,aAAa,CAAC;MAAC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAE7BjC,OAAO,gBACNR,OAAA,CAACJ,gBAAgB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEpBzC,OAAA,CAAAE,SAAA;QAAAiC,QAAA,gBACEnC,OAAA,CAACH,eAAe;UAACoC,EAAE,EAAE;YAAEiB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEP,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEzC,OAAA,CAACP,UAAU;UAAC2C,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAClCL,YAAY,GAAG,SAAS,GAAG;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACbzC,OAAA,CAACP,UAAU;UAAC2C,OAAO,EAAC,OAAO;UAACe,KAAK,EAAC,eAAe;UAAAhB,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA,eACb,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERzC,OAAA,CAACL,MAAM;MACLyC,OAAO,EAAC,WAAW;MACnBgB,SAAS,EAAC,OAAO;MACjBC,QAAQ,EAAE7C,OAAQ;MAClB8C,SAAS,eAAEtD,OAAA,CAACH,eAAe;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAN,QAAA,GAChC,0BAEC,eAAAnC,OAAA;QACEuD,IAAI,EAAC,MAAM;QACXxB,MAAM,EAAC,YAAY;QACnByB,MAAM;QACNC,QAAQ,EAAGC,CAAC,IAAK;UACf,IAAIA,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;YACvCjD,MAAM,CAAC,CAAC+C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B;QACF;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClC,EAAA,CA3GIH,UAAU;EAAA,QAsCwCb,WAAW;AAAA;AAAAsE,EAAA,GAtC7DzD,UAAU;AA6GhB,eAAeA,UAAU;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}