{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: uiState.isSelected ? uiState.text : '',\n    arrow: true,\n    placement: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Chip, {\n      label: uiState.text,\n      color: uiState.isSelected ? 'primary' : 'default',\n      variant: uiState.isSelected ? 'filled' : 'outlined',\n      size: \"small\",\n      onClick: handleClick,\n      clickable: true,\n      sx: {\n        maxWidth: '100%',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease-in-out',\n        '& .MuiChip-label': {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          display: 'block'\n        }\n      }\n    }, `remark-${rowId}-${uiState.isSelected}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n}, \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\")), \"qYAf8k9SAflwGeh0jskx0Mgm4aM=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    enqueueSnackbar(message, {\n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: {\n        '& .MuiPaper-root': {\n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: severity === 'success' ? 'success.main' : severity === 'error' ? 'error.main' : severity === 'warning' ? 'warning.main' : severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onRowEditComplete = e => {\n    let {\n      newData,\n      index\n    } = e;\n\n    // 阻止总计行被编辑\n    if (newData.NO === 'TOTAL') {\n      return;\n    }\n\n    // 处理数值字段\n    if (newData.COMMISSION !== undefined) {\n      if (typeof newData.COMMISSION === 'string') {\n        newData.COMMISSION = Number(newData.COMMISSION) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = [...prev];\n      updatedData[index] = newData;\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n      return updatedData;\n    });\n  };\n  const allowEdit = rowData => {\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"text\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this);\n  };\n  const numberEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputText, {\n      type: \"number\",\n      value: options.value,\n      onChange: e => options.editorCallback(e.target.value),\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                return {\n                  ...row,\n                  REMARKS: option,\n                  _selected_remarks: option\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      // 首先标记行为已删除\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return {\n            ...row,\n            NO: newNumber++\n          };\n        }\n        return row;\n      });\n      return updatedData;\n    });\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => {\n        row.NO = index + 1;\n      });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          value: memoGridData,\n          editMode: \"row\",\n          dataKey: \"id\",\n          onRowEditComplete: onRowEditComplete,\n          rowClassName: rowData => {\n            if (rowData.NO === 'TOTAL') return 'total-row';\n            if (rowData._removed) return 'removed-row';\n            return '';\n          },\n          className: \"custom-datatable\",\n          paginator: true,\n          rows: 100,\n          rowsPerPageOptions: [100, 200, 500],\n          paginatorTemplate: \"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\",\n          currentPageReportTemplate: \"\\u663E\\u793A {first} \\u5230 {last} \\u6761\\uFF0C\\u5171 {totalRecords} \\u6761\\u8BB0\\u5F55\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"NO\",\n            header: \"NO\",\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            body: rowData => {\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: Math.floor(rowData.NO)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 26\n                }, this);\n              }\n              return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n            },\n            style: {\n              width: '80px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"DATE\",\n            header: \"DATE\",\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            body: rowData => {\n              const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: dateValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 26\n                }, this);\n              }\n              return dateValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"VEHICLE NO\",\n            header: \"VEHICLE NO\",\n            editor: options => allowEdit(options.rowData) ? textEditor(options) : null,\n            body: rowData => {\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: rowData['VEHICLE NO']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 26\n                }, this);\n              }\n              return rowData['VEHICLE NO'];\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"RO NO\",\n            header: \"RO NO\",\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            body: rowData => {\n              const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: roValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 26\n                }, this);\n              }\n              return roValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"KM\",\n            header: \"KM\",\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            body: rowData => {\n              const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: kmValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 26\n                }, this);\n              }\n              return kmValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"REMARKS\",\n            header: \"REMARKS\",\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                const removedRemarkText = rowData._selected_remarks || '无备注';\n                return /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: rowData._selected_remarks || '',\n                  arrow: true,\n                  placement: \"top\",\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: removedRemarkText,\n                    color: \"default\",\n                    variant: \"outlined\",\n                    size: \"small\",\n                    sx: {\n                      maxWidth: '100%',\n                      opacity: 0.6,\n                      transition: 'all 0.2s ease-in-out',\n                      '& .MuiChip-label': {\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        whiteSpace: 'nowrap',\n                        display: 'block'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 21\n                }, this);\n              }\n              let remarkText = '点击选择';\n              let isSelected = false;\n              if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                remarkText = rowData._selected_remarks;\n                isSelected = true;\n              }\n              return /*#__PURE__*/_jsxDEV(RemarkChip, {\n                rowId: rowData.id,\n                text: remarkText,\n                isSelected: isSelected,\n                onClick: handleRemarksClick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '200px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"MAXCHECK\",\n            header: \"HOURS\",\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            body: rowData => {\n              const hoursValue = typeof rowData.MAXCHECK === 'number' ? rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: hoursValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 26\n                }, this);\n              }\n              return hoursValue;\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"COMMISSION\",\n            header: \"AMOUNT\",\n            editor: options => allowEdit(options.rowData) ? numberEditor(options) : null,\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') {\n                const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                return /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  children: totalValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 21\n                }, this);\n              }\n              const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) : typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    textDecoration: 'line-through',\n                    color: '#999'\n                  },\n                  children: amountValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 26\n                }, this);\n              }\n              return amountValue;\n            },\n            style: {\n              width: '120px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            header: \"ACTION\",\n            body: rowData => {\n              if (rowData.NO === 'TOTAL') return '';\n              if (rowData._removed) {\n                return /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"\\u6062\\u590D\",\n                  color: \"success\",\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 29\n                  }, this),\n                  onClick: () => handleUndoRow(rowData.id),\n                  sx: {\n                    cursor: 'pointer'\n                  }\n                }, \"undo\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 21\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"\\u79FB\\u9664\",\n                color: \"error\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 27\n                }, this),\n                onClick: () => handleRemoveRow(rowData.id),\n                sx: {\n                  cursor: 'pointer'\n                }\n              }, \"remove\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this);\n            },\n            style: {\n              width: '100px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 901,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 913,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 946,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 961,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 599,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"e10B347t1Axo4vyoflMK1D8dcao=\", false, function () {\n  return [useSnackbar];\n});\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataTable", "Column", "InputText", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "FixedSizeList", "Slide", "useSnackbar", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "title", "arrow", "placement", "children", "label", "color", "variant", "size", "clickable", "sx", "max<PERSON><PERSON><PERSON>", "cursor", "transition", "overflow", "textOverflow", "whiteSpace", "display", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "keyData", "lastKeyData", "current", "clearTimeout", "setTimeout", "Date", "now", "enqueueSnackbar", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showSnackbar", "message", "severity", "<PERSON><PERSON><PERSON>", "key", "borderRadius", "border", "borderColor", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "onRowEditComplete", "newData", "Number", "prev", "updatedData", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "allowEdit", "rowData", "textEditor", "options", "type", "value", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "target", "style", "width", "numberEditor", "recalculateTotal", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "newNumber", "handleUndoRow", "for<PERSON>ach", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "iframe", "src", "Error", "handleRemarksClick", "memoGridData", "textAlign", "py", "mt", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "editMode", "dataKey", "rowClassName", "className", "paginator", "rows", "rowsPerPageOptions", "paginatorTemplate", "currentPageReportTemplate", "field", "header", "editor", "textDecoration", "dateValue", "roValue", "kmValue", "removedRemarkText", "opacity", "remarkText", "hoursValue", "totalValue", "isNaN", "fontWeight", "amountValue", "icon", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport Slide from '@mui/material/Slide';\nimport { useSnackbar } from 'notistack';\n\n// Import PrimeReact CSS\nimport 'primereact/resources/themes/lara-light-indigo/theme.css';\nimport 'primereact/resources/primereact.min.css';\nimport 'primeicons/primeicons.css';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Tooltip title={uiState.isSelected ? uiState.text : ''} arrow placement=\"top\">\n      <Chip\n        key={`remark-${rowId}-${uiState.isSelected}`}\n        label={uiState.text}\n        color={uiState.isSelected ? 'primary' : 'default'}\n        variant={uiState.isSelected ? 'filled' : 'outlined'}\n        size=\"small\"\n        onClick={handleClick}\n        clickable\n        sx={{ \n          maxWidth: '100%', \n          cursor: 'pointer',\n          transition: 'all 0.2s ease-in-out',\n          '& .MuiChip-label': { \n            overflow: 'hidden', \n            textOverflow: 'ellipsis', \n            whiteSpace: 'nowrap', \n            display: 'block' \n          }\n        }}\n      />\n    </Tooltip>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  \n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  \n  // 用notistack替代原有的showSnackbar\n  const showSnackbar = useCallback((message, severity = 'success') => {\n    // 递增计数器，确保每个通知都有唯一的key\n    const uniqueKey = `notification_${message}_${notificationCounter.current++}`;\n    \n    enqueueSnackbar(message, { \n      variant: severity,\n      // 使用递增计数器生成的key\n      key: uniqueKey,\n      sx: { \n        '& .MuiPaper-root': { \n          borderRadius: '10px',\n          border: '1px solid',\n          borderColor: \n            severity === 'success' ? 'success.main' : \n            severity === 'error' ? 'error.main' :\n            severity === 'warning' ? 'warning.main' : \n            severity === 'info' ? 'info.main' : 'grey.500'\n        }\n      }\n    });\n  }, [enqueueSnackbar]);\n\n  const handleDownload = async () => {\n    try {\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showSnackbar('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  // PrimeReact DataTable cell editing handlers\n  const onRowEditComplete = (e) => {\n    let { newData, index } = e;\n\n    // 阻止总计行被编辑\n    if (newData.NO === 'TOTAL') {\n      return;\n    }\n\n    // 处理数值字段\n    if (newData.COMMISSION !== undefined) {\n      if (typeof newData.COMMISSION === 'string') {\n        newData.COMMISSION = Number(newData.COMMISSION) || 0;\n      }\n    }\n\n    // 更新数据\n    setGridData(prev => {\n      const updatedData = [...prev];\n      updatedData[index] = newData;\n\n      // 重新计算总计\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData\n          .filter(row => row.NO !== 'TOTAL' && !row._removed)\n          .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n\n      return updatedData;\n    });\n  };\n\n  const allowEdit = (rowData) => {\n    return rowData.NO !== 'TOTAL' && !rowData._removed;\n  };\n\n  // Cell editor templates for PrimeReact\n  const textEditor = (options) => {\n    return (\n      <InputText\n        type=\"text\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n  const numberEditor = (options) => {\n    return (\n      <InputText\n        type=\"number\"\n        value={options.value}\n        onChange={(e) => options.editorCallback(e.target.value)}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                return { ...row, REMARKS: option, _selected_remarks: option };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showSnackbar('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showSnackbar]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showSnackbar('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showSnackbar('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showSnackbar]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showSnackbar('选项已删除', 'success');\n  }, [showSnackbar]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      // 首先标记行为已删除\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新计算总计\n      updatedData = recalculateTotal(updatedData);\n\n      // 重新编号未删除的行\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n\n      // 更新NO字段\n      let newNumber = 1;\n      updatedData = updatedData.map(row => {\n        if (row.NO !== 'TOTAL' && !row._removed) {\n          return { ...row, NO: newNumber++ };\n        }\n        return row;\n      });\n\n      return updatedData;\n    });\n\n    showSnackbar('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showSnackbar]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n      updatedData = recalculateTotal(updatedData);\n      const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n      nonRemovedRows.sort((a, b) => a.id - b.id);\n      nonRemovedRows.forEach((row, index) => { row.NO = index + 1; });\n      return updatedData;\n    });\n    setTimeout(() => {\n      showSnackbar('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showSnackbar]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showSnackbar('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showSnackbar('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n\n  \n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => gridData, [gridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%' }}>\n          <style>\n            {`\n              .custom-datatable .p-datatable-thead > tr > th {\n                background-color: #f5f5f5 !important;\n                font-weight: bold !important;\n                padding: 12px 8px !important;\n                border-bottom: 2px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.total-row {\n                background-color: rgba(25, 118, 210, 0.08) !important;\n                font-weight: bold !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr.removed-row {\n                background-color: rgba(211, 211, 211, 0.3) !important;\n                color: #999 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr > td {\n                padding: 8px !important;\n                border-bottom: 1px solid #e0e0e0 !important;\n              }\n\n              .custom-datatable .p-datatable-tbody > tr:hover {\n                background-color: #f8f9fa !important;\n              }\n\n              .custom-datatable .p-row-editor-init,\n              .custom-datatable .p-row-editor-save,\n              .custom-datatable .p-row-editor-cancel {\n                margin-left: 4px !important;\n              }\n\n              .custom-datatable .p-inputtext {\n                width: 100% !important;\n                padding: 4px 8px !important;\n                font-size: 14px !important;\n              }\n            `}\n          </style>\n          <DataTable\n            value={memoGridData}\n            editMode=\"row\"\n            dataKey=\"id\"\n            onRowEditComplete={onRowEditComplete}\n            rowClassName={(rowData) => {\n              if (rowData.NO === 'TOTAL') return 'total-row';\n              if (rowData._removed) return 'removed-row';\n              return '';\n            }}\n            className=\"custom-datatable\"\n            paginator\n            rows={100}\n            rowsPerPageOptions={[100, 200, 500]}\n            paginatorTemplate=\"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown\"\n            currentPageReportTemplate=\"显示 {first} 到 {last} 条，共 {totalRecords} 条记录\"\n          >\n            {/* NO Column */}\n            <Column\n              field=\"NO\"\n              header=\"NO\"\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              body={(rowData) => {\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{Math.floor(rowData.NO)}</span>;\n                }\n                return typeof rowData.NO === 'number' ? Math.floor(rowData.NO) : rowData.NO;\n              }}\n              style={{ width: '80px' }}\n            />\n\n            {/* DATE Column */}\n            <Column\n              field=\"DATE\"\n              header=\"DATE\"\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              body={(rowData) => {\n                const dateValue = rowData.DATE && rowData.DATE.includes('T') ? rowData.DATE.split('T')[0] : rowData.DATE;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{dateValue}</span>;\n                }\n                return dateValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* VEHICLE NO Column */}\n            <Column\n              field=\"VEHICLE NO\"\n              header=\"VEHICLE NO\"\n              editor={(options) => allowEdit(options.rowData) ? textEditor(options) : null}\n              body={(rowData) => {\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{rowData['VEHICLE NO']}</span>;\n                }\n                return rowData['VEHICLE NO'];\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* RO NO Column */}\n            <Column\n              field=\"RO NO\"\n              header=\"RO NO\"\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              body={(rowData) => {\n                const roValue = typeof rowData['RO NO'] === 'number' ? Math.floor(rowData['RO NO']) : rowData['RO NO'];\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{roValue}</span>;\n                }\n                return roValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* KM Column */}\n            <Column\n              field=\"KM\"\n              header=\"KM\"\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              body={(rowData) => {\n                const kmValue = typeof rowData.KM === 'number' ? Math.floor(rowData.KM) : rowData.KM;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{kmValue}</span>;\n                }\n                return kmValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* REMARKS Column */}\n            <Column\n              field=\"REMARKS\"\n              header=\"REMARKS\"\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  const removedRemarkText = rowData._selected_remarks || '无备注';\n                  return (\n                    <Tooltip title={rowData._selected_remarks || ''} arrow placement=\"top\">\n                      <Chip\n                        label={removedRemarkText}\n                        color=\"default\"\n                        variant=\"outlined\"\n                        size=\"small\"\n                        sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                      />\n                    </Tooltip>\n                  );\n                }\n\n                let remarkText = '点击选择';\n                let isSelected = false;\n\n                if (rowData._selected_remarks && rowData._selected_remarks !== 'None') {\n                  remarkText = rowData._selected_remarks;\n                  isSelected = true;\n                }\n\n                return (\n                  <RemarkChip\n                    rowId={rowData.id}\n                    text={remarkText}\n                    isSelected={isSelected}\n                    onClick={handleRemarksClick}\n                  />\n                );\n              }}\n              style={{ width: '200px' }}\n            />\n\n            {/* MAXCHECK (HOURS) Column */}\n            <Column\n              field=\"MAXCHECK\"\n              header=\"HOURS\"\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              body={(rowData) => {\n                const hoursValue = typeof rowData.MAXCHECK === 'number' ?\n                  (rowData.MAXCHECK % 1 === 0 ? rowData.MAXCHECK.toFixed(1) : rowData.MAXCHECK.toFixed(1)) :\n                  rowData.MAXCHECK;\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{hoursValue}</span>;\n                }\n                return hoursValue;\n              }}\n              style={{ width: '100px' }}\n            />\n\n            {/* COMMISSION (AMOUNT) Column */}\n            <Column\n              field=\"COMMISSION\"\n              header=\"AMOUNT\"\n              editor={(options) => allowEdit(options.rowData) ? numberEditor(options) : null}\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') {\n                  const totalValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                    typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n                  return (\n                    <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n                      {totalValue}\n                    </Typography>\n                  );\n                }\n\n                const amountValue = typeof rowData.COMMISSION === 'number' ? rowData.COMMISSION.toFixed(2) :\n                  typeof rowData.COMMISSION === 'string' && !isNaN(Number(rowData.COMMISSION)) ? Number(rowData.COMMISSION).toFixed(2) : rowData.COMMISSION;\n\n                if (rowData._removed) {\n                  return <span style={{ textDecoration: 'line-through', color: '#999' }}>{amountValue}</span>;\n                }\n                return amountValue;\n              }}\n              style={{ width: '120px' }}\n            />\n\n            {/* ACTION Column */}\n            <Column\n              header=\"ACTION\"\n              body={(rowData) => {\n                if (rowData.NO === 'TOTAL') return '';\n                if (rowData._removed) {\n                  return (\n                    <Chip\n                      key=\"undo\"\n                      label=\"恢复\"\n                      color=\"success\"\n                      size=\"small\"\n                      icon={<UndoIcon />}\n                      onClick={() => handleUndoRow(rowData.id)}\n                      sx={{ cursor: 'pointer' }}\n                    />\n                  );\n                }\n                return (\n                  <Chip\n                    key=\"remove\"\n                    label=\"移除\"\n                    color=\"error\"\n                    size=\"small\"\n                    icon={<DeleteIcon />}\n                    onClick={() => handleRemoveRow(rowData.id)}\n                    sx={{ cursor: 'pointer' }}\n                  />\n                );\n              }}\n              style={{ width: '100px' }}\n            />\n          </DataTable>\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AACA,OAAO,yDAAyD;AAChE,OAAO,yCAAyC;AAChD,OAAO,2BAA2B;;AAElC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOH,OAAA,CAACH,KAAK;IAAA,GAAKM,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAGnD,KAAK,CAACoD,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC;IACrCsD,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACAtD,SAAS,CAAC,MAAM;IACdyD,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEd,OAAA,CAAChB,OAAO;IAACsC,KAAK,EAAEJ,OAAO,CAACF,UAAU,GAAGE,OAAO,CAACH,IAAI,GAAG,EAAG;IAACQ,KAAK;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,eAC3EzB,OAAA,CAACjB,IAAI;MAEH2C,KAAK,EAAER,OAAO,CAACH,IAAK;MACpBY,KAAK,EAAET,OAAO,CAACF,UAAU,GAAG,SAAS,GAAG,SAAU;MAClDY,OAAO,EAAEV,OAAO,CAACF,UAAU,GAAG,QAAQ,GAAG,UAAW;MACpDa,IAAI,EAAC,OAAO;MACZZ,OAAO,EAAEG,WAAY;MACrBU,SAAS;MACTC,EAAE,EAAE;QACFC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,sBAAsB;QAClC,kBAAkB,EAAE;UAClBC,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE,QAAQ;UACpBC,OAAO,EAAE;QACX;MACF;IAAE,GAjBG,UAAUxB,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAkB7C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC,kCAAC;AAAC+B,GAAA,GA5CG7B,UAAU;AA8ChB,MAAM8B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EAEzF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,MAAM;IACzD,MAAMyF,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGjD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;;EAIpD;EACAC,SAAS,CAAC,MAAM;IACdyF,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAGxB,IAAI,CAACyB,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/G,QAAQ,CAAC,MAAM;IAC7CgH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C7B,aAAa,GAAG,IAAIA,aAAa,CAAC8B,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI9B,aAAa,IAAIA,aAAa,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG/B,aAAa,CAACqB,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGvH,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwH,iBAAiB,GAAGxH,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMyH,gBAAgB,GAAGzH,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM0H,mBAAmB,GAAG1H,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM2H,UAAU,GAAI9C,IAAI,IAAKA,IAAI,CAACyB,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA9H,SAAS,CAAC,MAAM;IACd,IAAIoF,YAAY,IAAIyB,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,OAAO,GAAGpC,IAAI,CAACW,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMmB,WAAW,GAAGP,mBAAmB,CAACQ,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIJ,gBAAgB,CAACM,OAAO,EAAE;UAC5BC,YAAY,CAACP,gBAAgB,CAACM,OAAO,CAAC;QACxC;QACAN,gBAAgB,CAACM,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC1CV,mBAAmB,CAACQ,OAAO,GAAGF,OAAO;UACrCL,iBAAiB,CAACO,OAAO,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC;UACtCjD,YAAY,CAAC,CAAC,GAAGyB,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACM,OAAO,EAAE;QAC5BC,YAAY,CAACP,gBAAgB,CAACM,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACpB,QAAQ,EAAEzB,YAAY,CAAC,CAAC;EAE5B,MAAM;IAAEkD;EAAgB,CAAC,GAAGlG,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzI,QAAQ,CAAC;IACjD0I,IAAI,EAAE,KAAK;IACXrF,KAAK,EAAE,IAAI;IACXsF,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA1I,SAAS,CAAC,MAAM;IACd,IAAImG,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAG1I,WAAW,CAAC,CAAC2I,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAClE;IACA,MAAMC,SAAS,GAAG,gBAAgBF,OAAO,IAAIhB,mBAAmB,CAACK,OAAO,EAAE,EAAE;IAE5EK,eAAe,CAACM,OAAO,EAAE;MACvB1E,OAAO,EAAE2E,QAAQ;MACjB;MACAE,GAAG,EAAED,SAAS;MACdzE,EAAE,EAAE;QACF,kBAAkB,EAAE;UAClB2E,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,WAAW;UACnBC,WAAW,EACTL,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,OAAO,GAAG,YAAY,GACnCA,QAAQ,KAAK,SAAS,GAAG,cAAc,GACvCA,QAAQ,KAAK,MAAM,GAAG,WAAW,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,WAAW,GAAG,GAAGnH,OAAO,aAAa+C,MAAM,EAAE;MACnD,MAAMqE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIrB,IAAI,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BV,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7E,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM8E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMhI,KAAK,CAACiI,MAAM,CAAC,GAAGhI,OAAO,YAAY+C,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+E,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9E,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMiF,iBAAiB,GAAIvG,CAAC,IAAK;IAC/B,IAAI;MAAEwG,OAAO;MAAE9C;IAAM,CAAC,GAAG1D,CAAC;;IAE1B;IACA,IAAIwG,OAAO,CAAC3C,EAAE,KAAK,OAAO,EAAE;MAC1B;IACF;;IAEA;IACA,IAAI2C,OAAO,CAACrC,UAAU,KAAKX,SAAS,EAAE;MACpC,IAAI,OAAOgD,OAAO,CAACrC,UAAU,KAAK,QAAQ,EAAE;QAC1CqC,OAAO,CAACrC,UAAU,GAAGsC,MAAM,CAACD,OAAO,CAACrC,UAAU,CAAC,IAAI,CAAC;MACtD;IACF;;IAEA;IACAhB,WAAW,CAACuD,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG,CAAC,GAAGD,IAAI,CAAC;MAC7BC,WAAW,CAACjD,KAAK,CAAC,GAAG8C,OAAO;;MAE5B;MACA,MAAMI,QAAQ,GAAGD,WAAW,CAACE,IAAI,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAI+C,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGH,WAAW,CACzBI,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD+D,MAAM,CAAC,CAACC,GAAG,EAAEnE,GAAG,KAAKmE,GAAG,IAAIR,MAAM,CAAC3D,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/DyC,QAAQ,CAACzC,UAAU,GAAG2C,QAAQ;MAChC;MAEA,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,SAAS,GAAIC,OAAO,IAAK;IAC7B,OAAOA,OAAO,CAACtD,EAAE,KAAK,OAAO,IAAI,CAACsD,OAAO,CAAClE,QAAQ;EACpD,CAAC;;EAED;EACA,MAAMmE,UAAU,GAAIC,OAAO,IAAK;IAC9B,oBACE1I,OAAA,CAACb,SAAS;MACRwJ,IAAI,EAAC,MAAM;MACXC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGxH,CAAC,IAAKqH,OAAO,CAACI,cAAc,CAACzH,CAAC,CAAC0H,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAA5I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAED,MAAM0I,YAAY,GAAIR,OAAO,IAAK;IAChC,oBACE1I,OAAA,CAACb,SAAS;MACRwJ,IAAI,EAAC,QAAQ;MACbC,KAAK,EAAEF,OAAO,CAACE,KAAM;MACrBC,QAAQ,EAAGxH,CAAC,IAAKqH,OAAO,CAACI,cAAc,CAACzH,CAAC,CAAC0H,MAAM,CAACH,KAAK,CAAE;MACxDI,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAA5I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;;EAID;EACA,MAAM2I,gBAAgB,GAAGxL,WAAW,CAAE8E,IAAI,IAAK;IAC7C,MAAMwF,QAAQ,GAAGxF,IAAI,CAACyF,IAAI,CAAC/D,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAI+C,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG1F,IAAI,CAClB2F,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClD+D,MAAM,CAAC,CAACC,GAAG,EAAEnE,GAAG,KAAKmE,GAAG,IAAIR,MAAM,CAAC3D,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DyC,QAAQ,CAACzC,UAAU,GAAG2C,QAAQ;IAChC;IACA,OAAO1F,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAM2G,iBAAiB,GAAG5L,KAAK,CAACG,WAAW,CAAC,CAACmD,KAAK,EAAEsF,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVrF,KAAK;MACLsF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,kBAAkB,GAAG1L,WAAW,CAAC,MAAM;IAC3CuI,gBAAgB,CAAC6B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP5B,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmD,kBAAkB,GAAG3L,WAAW,CAAE4L,MAAM,IAAK;IACjD,MAAM;MAAEzI;IAAM,CAAC,GAAGmF,aAAa;IAC/B,IAAInF,KAAK,KAAK,IAAI,EAAE;MAClB;MACAuI,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCjF,WAAW,CAACkF,QAAQ,IAAI;UACtB,IAAI1B,WAAW,GAAG0B,QAAQ,CAACxF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKlE,KAAK,EAAE;cACpB,IAAIyI,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGpF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL,OAAO;kBAAE,GAAGF,GAAG;kBAAEC,OAAO,EAAEmF,MAAM;kBAAElF,iBAAiB,EAAEkF;gBAAO,CAAC;cAC/D;YACF;YACA,OAAOpF,GAAG;UACZ,CAAC,CAAC;UAEF6D,WAAW,GAAGmB,gBAAgB,CAACnB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAnC,UAAU,CAAC,MAAM;UACfQ,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC;QACvC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEoD,kBAAkB,EAAEF,gBAAgB,EAAE9C,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAMsD,mBAAmB,GAAGhM,WAAW,CAAC,MAAM;IAC5C+F,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkG,oBAAoB,GAAGjM,WAAW,CAAC,MAAM;IAC7C+F,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqG,YAAY,GAAGlM,WAAW,CAAC,MAAM;IACrC,IAAI4F,SAAS,CAACuG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC9G,cAAc,CAAC+G,QAAQ,CAACxG,SAAS,CAACuG,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE7G,iBAAiB,CAAC8E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExE,SAAS,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDzD,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACjCuD,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI5G,cAAc,CAAC+G,QAAQ,CAACxG,SAAS,CAACuG,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDzD,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,CAAC9C,SAAS,EAAEP,cAAc,EAAE4G,oBAAoB,EAAEvD,YAAY,CAAC,CAAC;;EAEnE;EACA,MAAM2D,YAAY,GAAGrM,WAAW,CAAE4L,MAAM,IAAK;IAC3CtG,iBAAiB,CAAC8E,IAAI,IAAIA,IAAI,CAACK,MAAM,CAAC6B,IAAI,IAAIA,IAAI,KAAKV,MAAM,CAAC,CAAC;IAC/DlD,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;EAClC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM6D,eAAe,GAAGvM,WAAW,CAAEqH,EAAE,IAAK;IAC1CR,WAAW,CAACuD,IAAI,IAAI;MAClB;MACA,IAAIC,WAAW,GAAGD,IAAI,CAAC7D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA6D,WAAW,GAAGmB,gBAAgB,CAACnB,WAAW,CAAC;;MAE3C;MACA,MAAMmC,cAAc,GAAGnC,WAAW,CAACI,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;MACrF6F,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrF,EAAE,GAAGsF,CAAC,CAACtF,EAAE,CAAC;;MAE1C;MACA,IAAIuF,SAAS,GAAG,CAAC;MACjBvC,WAAW,GAAGA,WAAW,CAAC9D,GAAG,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,EAAE;UACvC,OAAO;YAAE,GAAGH,GAAG;YAAEe,EAAE,EAAEqF,SAAS;UAAG,CAAC;QACpC;QACA,OAAOpG,GAAG;MACZ,CAAC,CAAC;MAEF,OAAO6D,WAAW;IACpB,CAAC,CAAC;IAEF3B,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;EACnC,CAAC,EAAE,CAAC8C,gBAAgB,EAAE9C,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMmE,aAAa,GAAG7M,WAAW,CAAEqH,EAAE,IAAK;IACxCR,WAAW,CAACuD,IAAI,IAAI;MAClB,IAAIC,WAAW,GAAGD,IAAI,CAAC7D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;MACpF6D,WAAW,GAAGmB,gBAAgB,CAACnB,WAAW,CAAC;MAC3C,MAAMmC,cAAc,GAAGnC,WAAW,CAACI,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;MACnHiF,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrF,EAAE,GAAGsF,CAAC,CAACtF,EAAE,CAAC;MAC1CmF,cAAc,CAACM,OAAO,CAAC,CAACtG,GAAG,EAAEY,KAAK,KAAK;QAAEZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;MAC/D,OAAOiD,WAAW;IACpB,CAAC,CAAC;IACFnC,UAAU,CAAC,MAAM;MACfQ,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAAC8C,gBAAgB,EAAE9C,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMqE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF9G,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAM+G,YAAY,GAAGpG,QAAQ,CAC1B6D,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAqG,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACnF,EAAE,KAAK,QAAQ,IAAI,OAAOoF,CAAC,CAACpF,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOmF,CAAC,CAACnF,EAAE,GAAGoF,CAAC,CAACpF,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM0F,OAAO,GAAGD,YAAY,CAACzG,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACb8F,IAAI,EAAE1G,GAAG,CAAC0G,IAAI,GAAI,OAAO1G,GAAG,CAAC0G,IAAI,KAAK,QAAQ,IAAI1G,GAAG,CAAC0G,IAAI,CAACd,QAAQ,CAAC,GAAG,CAAC,GAAG5F,GAAG,CAAC0G,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG3G,GAAG,CAAC0G,IAAI,GAAI,EAAE;QAClH,YAAY,EAAE1G,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG4G,IAAI,CAACC,KAAK,CAAC7G,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzF8G,EAAE,EAAE,OAAO9G,GAAG,CAAC8G,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAAC7G,GAAG,CAAC8G,EAAE,CAAC,GAAG9G,GAAG,CAAC8G,EAAE,IAAI,EAAE;QAClE7G,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG6G,KAAK,EAAE,OAAO/G,GAAG,CAACgH,QAAQ,KAAK,QAAQ,GACpChH,GAAG,CAACgH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGhH,GAAG,CAACgH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGjH,GAAG,CAACgH,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EjH,GAAG,CAACgH,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOlH,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAAC4F,OAAO,CAAC,CAAC,CAAC,GAAGjH,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM8F,WAAW,GAAG/G,QAAQ,CACzB6D,MAAM,CAACjE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE6C,MAAM,CAAC,CAACC,GAAG,EAAEnE,GAAG,KAAKmE,GAAG,IAAI,OAAOnE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM+F,QAAQ,GAAG,MAAM7L,KAAK,CAAC8L,IAAI,CAAC,GAAG7L,OAAO,oBAAoB,EAAE;QAChE8C,IAAI,EAAEmI,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnC1I,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAI6I,QAAQ,CAAC9I,IAAI,IAAI8I,QAAQ,CAAC9I,IAAI,CAACgJ,KAAK,EAAE;QACxC;QACA,MAAM3E,WAAW,GAAG,GAAGnH,OAAO,CAACmL,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAAC9I,IAAI,CAACiJ,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACArF,YAAY,CAAC,eAAe,EAAE,SAAS,CAAC;;QAExC;QACAR,UAAU,CAAC,MAAM;UACf,MAAM8F,MAAM,GAAG3E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C0E,MAAM,CAAC3C,KAAK,CAAC1G,OAAO,GAAG,MAAM;UAC7BqJ,MAAM,CAACC,GAAG,GAAG9E,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACqE,MAAM,CAAC;UACjC9F,UAAU,CAAC,MAAM;YACfmB,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACmE,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IACrC,CAAC,SAAS;MACRzC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMkI,kBAAkB,GAAGnO,WAAW,CAAC,CAACmD,KAAK,EAAE8H,KAAK,KAAK;IACvDQ,iBAAiB,CAACtI,KAAK,EAAE8H,KAAK,CAAC;EACjC,CAAC,EAAE,CAACQ,iBAAiB,CAAC,CAAC;;EAIvB;EACA,MAAM2C,YAAY,GAAGlO,OAAO,CAAC,MAAM0G,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAExD;EACA,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE3E,OAAA,CAAClC,GAAG;MAACiE,EAAE,EAAE;QAAEiK,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxK,QAAA,gBACtCzB,OAAA,CAACjC,UAAU;QAAC6D,OAAO,EAAC,IAAI;QAACD,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAAChC,MAAM;QACL4D,OAAO,EAAC,WAAW;QACnBX,OAAO,EAAE0B,OAAQ;QACjBZ,EAAE,EAAE;UAAEmK,EAAE,EAAE;QAAE,CAAE;QAAAzK,QAAA,EACf;MAED;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACER,OAAA,CAAClC,GAAG;IAAA2D,QAAA,gBACFzB,OAAA,CAAClC,GAAG;MAACiE,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE6J,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA5K,QAAA,gBACzFzB,OAAA,CAACjC,UAAU;QAAC6D,OAAO,EAAC,IAAI;QAAC0K,YAAY;QAAA7K,QAAA,EAAC;MAEtC;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbR,OAAA,CAAClC,GAAG;QAAA2D,QAAA,gBACFzB,OAAA,CAAChC,MAAM;UACL4D,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf4K,SAAS,eAAEvM,OAAA,CAACZ,YAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BS,OAAO,EAAE4F,cAAe;UACxB9E,EAAE,EAAE;YAAEyK,EAAE,EAAE;UAAE,CAAE;UAAA/K,QAAA,EACf;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETR,OAAA,CAAChC,MAAM;UACL4D,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACf4K,SAAS,eAAEvM,OAAA,CAACP,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCS,OAAO,EAAEyJ,gBAAiB;UAC1B+B,QAAQ,EAAE9I,oBAAqB;UAC/B5B,EAAE,EAAE;YAAEyK,EAAE,EAAE;UAAE,CAAE;UAAA/K,QAAA,EAEbkC,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETR,OAAA,CAAChC,MAAM;UACL4D,OAAO,EAAC,UAAU;UAClB2K,SAAS,eAAEvM,OAAA,CAACX,cAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BS,OAAO,EAAEyG,aAAc;UAAAjG,QAAA,EACxB;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAAC/B,KAAK;MAAC8D,EAAE,EAAE;QAAEkH,KAAK,EAAE,MAAM;QAAE9G,QAAQ,EAAE;MAAS,CAAE;MAAAV,QAAA,eAC/CzB,OAAA,CAAClC,GAAG;QAACiE,EAAE,EAAE;UAAE2K,MAAM,EAAE,MAAM;UAAEzD,KAAK,EAAE;QAAO,CAAE;QAAAxH,QAAA,gBACzCzB,OAAA;UAAAyB,QAAA,EACG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QAAa;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACRR,OAAA,CAACf,SAAS;UACR2J,KAAK,EAAEmD,YAAa;UACpBY,QAAQ,EAAC,KAAK;UACdC,OAAO,EAAC,IAAI;UACZhF,iBAAiB,EAAEA,iBAAkB;UACrCiF,YAAY,EAAGrE,OAAO,IAAK;YACzB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,WAAW;YAC9C,IAAIsD,OAAO,CAAClE,QAAQ,EAAE,OAAO,aAAa;YAC1C,OAAO,EAAE;UACX,CAAE;UACFwI,SAAS,EAAC,kBAAkB;UAC5BC,SAAS;UACTC,IAAI,EAAE,GAAI;UACVC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;UACpCC,iBAAiB,EAAC,sGAAsG;UACxHC,yBAAyB,EAAC,yFAA4C;UAAA1L,QAAA,gBAGtEzB,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGC,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEsJ,IAAI,CAACC,KAAK,CAACxC,OAAO,CAACtD,EAAE;gBAAC;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACxG;cACA,OAAO,OAAOgI,OAAO,CAACtD,EAAE,KAAK,QAAQ,GAAG6F,IAAI,CAACC,KAAK,CAACxC,OAAO,CAACtD,EAAE,CAAC,GAAGsD,OAAO,CAACtD,EAAE;YAC7E,CAAE;YACF8D,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,MAAM;YACZC,MAAM,EAAC,MAAM;YACbC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGC,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,MAAMgF,SAAS,GAAGhF,OAAO,CAACqC,IAAI,IAAIrC,OAAO,CAACqC,IAAI,CAACd,QAAQ,CAAC,GAAG,CAAC,GAAGvB,OAAO,CAACqC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGtC,OAAO,CAACqC,IAAI;cACxG,IAAIrC,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAE+L;gBAAS;kBAAAnN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC3F;cACA,OAAOgN,SAAS;YAClB,CAAE;YACFxE,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,YAAY;YAClBC,MAAM,EAAC,YAAY;YACnBC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGC,UAAU,CAACC,OAAO,CAAC,GAAG,IAAK;YAC7ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,IAAIA,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAE+G,OAAO,CAAC,YAAY;gBAAC;kBAAAnI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACvG;cACA,OAAOgI,OAAO,CAAC,YAAY,CAAC;YAC9B,CAAE;YACFQ,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,OAAO;YACbC,MAAM,EAAC,OAAO;YACdC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGU,YAAY,CAACR,OAAO,CAAC,GAAG,IAAK;YAC/ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,MAAMiF,OAAO,GAAG,OAAOjF,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGuC,IAAI,CAACC,KAAK,CAACxC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAGA,OAAO,CAAC,OAAO,CAAC;cACtG,IAAIA,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEgM;gBAAO;kBAAApN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOiN,OAAO;YAChB,CAAE;YACFzE,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGU,YAAY,CAACR,OAAO,CAAC,GAAG,IAAK;YAC/ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,MAAMkF,OAAO,GAAG,OAAOlF,OAAO,CAACyC,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACxC,OAAO,CAACyC,EAAE,CAAC,GAAGzC,OAAO,CAACyC,EAAE;cACpF,IAAIzC,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEiM;gBAAO;kBAAArN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cACzF;cACA,OAAOkN,OAAO;YAChB,CAAE;YACF1E,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,SAAS;YACfC,MAAM,EAAC,SAAS;YAChBhG,IAAI,EAAGmB,OAAO,IAAK;cACjB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAIsD,OAAO,CAAClE,QAAQ,EAAE;gBACpB,MAAMqJ,iBAAiB,GAAGnF,OAAO,CAACnE,iBAAiB,IAAI,KAAK;gBAC5D,oBACErE,OAAA,CAAChB,OAAO;kBAACsC,KAAK,EAAEkH,OAAO,CAACnE,iBAAiB,IAAI,EAAG;kBAAC9C,KAAK;kBAACC,SAAS,EAAC,KAAK;kBAAAC,QAAA,eACpEzB,OAAA,CAACjB,IAAI;oBACH2C,KAAK,EAAEiM,iBAAkB;oBACzBhM,KAAK,EAAC,SAAS;oBACfC,OAAO,EAAC,UAAU;oBAClBC,IAAI,EAAC,OAAO;oBACZE,EAAE,EAAE;sBAAEC,QAAQ,EAAE,MAAM;sBAAE4L,OAAO,EAAE,GAAG;sBAAE1L,UAAU,EAAE,sBAAsB;sBAAE,kBAAkB,EAAE;wBAAEC,QAAQ,EAAE,QAAQ;wBAAEC,YAAY,EAAE,UAAU;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,OAAO,EAAE;sBAAQ;oBAAE;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAEd;cAEA,IAAIqN,UAAU,GAAG,MAAM;cACvB,IAAI7M,UAAU,GAAG,KAAK;cAEtB,IAAIwH,OAAO,CAACnE,iBAAiB,IAAImE,OAAO,CAACnE,iBAAiB,KAAK,MAAM,EAAE;gBACrEwJ,UAAU,GAAGrF,OAAO,CAACnE,iBAAiB;gBACtCrD,UAAU,GAAG,IAAI;cACnB;cAEA,oBACEhB,OAAA,CAACU,UAAU;gBACTI,KAAK,EAAE0H,OAAO,CAACxD,EAAG;gBAClBjE,IAAI,EAAE8M,UAAW;gBACjB7M,UAAU,EAAEA,UAAW;gBACvBC,OAAO,EAAE6K;cAAmB;gBAAAzL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAEN,CAAE;YACFwI,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,UAAU;YAChBC,MAAM,EAAC,OAAO;YACdC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGU,YAAY,CAACR,OAAO,CAAC,GAAG,IAAK;YAC/ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,MAAMsF,UAAU,GAAG,OAAOtF,OAAO,CAAC2C,QAAQ,KAAK,QAAQ,GACpD3C,OAAO,CAAC2C,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG3C,OAAO,CAAC2C,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG5C,OAAO,CAAC2C,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GACvF5C,OAAO,CAAC2C,QAAQ;cAClB,IAAI3C,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEqM;gBAAU;kBAAAzN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC5F;cACA,OAAOsN,UAAU;YACnB,CAAE;YACF9E,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLkO,KAAK,EAAC,YAAY;YAClBC,MAAM,EAAC,QAAQ;YACfC,MAAM,EAAG5E,OAAO,IAAKH,SAAS,CAACG,OAAO,CAACF,OAAO,CAAC,GAAGU,YAAY,CAACR,OAAO,CAAC,GAAG,IAAK;YAC/ErB,IAAI,EAAGmB,OAAO,IAAK;cACjB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE;gBAC1B,MAAM6I,UAAU,GAAG,OAAOvF,OAAO,CAAChD,UAAU,KAAK,QAAQ,GAAGgD,OAAO,CAAChD,UAAU,CAAC4F,OAAO,CAAC,CAAC,CAAC,GACvF,OAAO5C,OAAO,CAAChD,UAAU,KAAK,QAAQ,IAAI,CAACwI,KAAK,CAAClG,MAAM,CAACU,OAAO,CAAChD,UAAU,CAAC,CAAC,GAAGsC,MAAM,CAACU,OAAO,CAAChD,UAAU,CAAC,CAAC4F,OAAO,CAAC,CAAC,CAAC,GAAG5C,OAAO,CAAChD,UAAU;gBAC3I,oBACExF,OAAA,CAACjC,UAAU;kBAAC6D,OAAO,EAAC,OAAO;kBAACqM,UAAU,EAAC,MAAM;kBAACtM,KAAK,EAAC,SAAS;kBAAAF,QAAA,EAC1DsM;gBAAU;kBAAA1N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAEjB;cAEA,MAAM0N,WAAW,GAAG,OAAO1F,OAAO,CAAChD,UAAU,KAAK,QAAQ,GAAGgD,OAAO,CAAChD,UAAU,CAAC4F,OAAO,CAAC,CAAC,CAAC,GACxF,OAAO5C,OAAO,CAAChD,UAAU,KAAK,QAAQ,IAAI,CAACwI,KAAK,CAAClG,MAAM,CAACU,OAAO,CAAChD,UAAU,CAAC,CAAC,GAAGsC,MAAM,CAACU,OAAO,CAAChD,UAAU,CAAC,CAAC4F,OAAO,CAAC,CAAC,CAAC,GAAG5C,OAAO,CAAChD,UAAU;cAE3I,IAAIgD,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBAAOtE,OAAA;kBAAMgJ,KAAK,EAAE;oBAAEuE,cAAc,EAAE,cAAc;oBAAE5L,KAAK,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEyM;gBAAW;kBAAA7N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAC7F;cACA,OAAO0N,WAAW;YACpB,CAAE;YACFlF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFR,OAAA,CAACd,MAAM;YACLmO,MAAM,EAAC,QAAQ;YACfhG,IAAI,EAAGmB,OAAO,IAAK;cACjB,IAAIA,OAAO,CAACtD,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;cACrC,IAAIsD,OAAO,CAAClE,QAAQ,EAAE;gBACpB,oBACEtE,OAAA,CAACjB,IAAI;kBAEH2C,KAAK,EAAC,cAAI;kBACVC,KAAK,EAAC,SAAS;kBACfE,IAAI,EAAC,OAAO;kBACZsM,IAAI,eAAEnO,OAAA,CAACR,QAAQ;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBS,OAAO,EAAEA,CAAA,KAAMuJ,aAAa,CAAChC,OAAO,CAACxD,EAAE,CAAE;kBACzCjD,EAAE,EAAE;oBAAEE,MAAM,EAAE;kBAAU;gBAAE,GANtB,MAAM;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOX,CAAC;cAEN;cACA,oBACER,OAAA,CAACjB,IAAI;gBAEH2C,KAAK,EAAC,cAAI;gBACVC,KAAK,EAAC,OAAO;gBACbE,IAAI,EAAC,OAAO;gBACZsM,IAAI,eAAEnO,OAAA,CAACT,UAAU;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBS,OAAO,EAAEA,CAAA,KAAMiJ,eAAe,CAAC1B,OAAO,CAACxD,EAAE,CAAE;gBAC3CjD,EAAE,EAAE;kBAAEE,MAAM,EAAE;gBAAU;cAAE,GANtB,QAAQ;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOb,CAAC;YAEN,CAAE;YACFwI,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRR,OAAA,CAAC5B,MAAM;MACL+H,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBiI,OAAO,EAAE/E,kBAAmB;MAC5BgF,SAAS;MACTrM,QAAQ,EAAC,IAAI;MACbsM,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAhN,QAAA,gBAEnBzB,OAAA,CAAC3B,WAAW;QAAAoD,QAAA,eACVzB,OAAA,CAAClC,GAAG;UAACiE,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE6J,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA3K,QAAA,gBAClFzB,OAAA,CAACjC,UAAU;YAAC6D,OAAO,EAAC,IAAI;YAAAH,QAAA,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CR,OAAA,CAAChC,MAAM;YACLuO,SAAS,eAAEvM,OAAA,CAACV,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAE0I,mBAAoB;YAC7BhI,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdR,OAAA,CAAC1B,aAAa;QAACoQ,QAAQ;QAAC3M,EAAE,EAAE;UAAE4M,CAAC,EAAE;QAAE,CAAE;QAAAlN,QAAA,eACnCzB,OAAA,CAACJ,aAAa;UACZ8M,MAAM,EAAE,GAAI;UACZkC,SAAS,EAAE5L,cAAc,CAAC2B,MAAO;UACjCkK,QAAQ,EAAE,EAAG;UACb5F,KAAK,EAAC,MAAM;UAAAxH,QAAA,EAEXA,CAAC;YAAEsD,KAAK;YAAEiE;UAAM,CAAC,KAAK;YACrB,MAAMO,MAAM,GAAGvG,cAAc,CAAC+B,KAAK,CAAC;YACpC,oBACE/E,OAAA,CAACvB,QAAQ;cAEPuK,KAAK,EAAEA,KAAM;cACb8F,cAAc;cACdC,eAAe,eACb/O,OAAA,CAACnB,UAAU;gBACTmQ,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnB/N,OAAO,EAAEA,CAAA,KAAM+I,YAAY,CAACT,MAAM,CAAE;gBAAA9H,QAAA,eAEpCzB,OAAA,CAACT,UAAU;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;cAAAiB,QAAA,eAEDzB,OAAA,CAACtB,cAAc;gBAACuC,OAAO,EAAEA,CAAA,KAAMqI,kBAAkB,CAACC,MAAM,CAAE;gBAAA9H,QAAA,eACxDzB,OAAA,CAACrB,YAAY;kBAACsQ,OAAO,EAAE1F;gBAAO;kBAAAlJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ+I,MAAM;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBR,OAAA,CAACzB,aAAa;QAAAkD,QAAA,eACZzB,OAAA,CAAChC,MAAM;UAACiD,OAAO,EAAEoI,kBAAmB;UAAA5H,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTR,OAAA,CAAC5B,MAAM;MACL+H,IAAI,EAAE1C,eAAgB;MACtB2K,OAAO,EAAExE,oBAAqB;MAC9ByE,SAAS;MACTrM,QAAQ,EAAC,IAAI;MACbsM,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAhN,QAAA,gBAEnBzB,OAAA,CAAC3B,WAAW;QAAAoD,QAAA,EAAC;MAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCR,OAAA,CAAC1B,aAAa;QAAAmD,QAAA,eACZzB,OAAA,CAACpB,SAAS;UACRsQ,SAAS;UACTC,MAAM,EAAC,OAAO;UACdnK,EAAE,EAAC,MAAM;UACTtD,KAAK,EAAC,0BAAM;UACZiH,IAAI,EAAC,MAAM;UACX0F,SAAS;UACTzM,OAAO,EAAC,UAAU;UAClBgH,KAAK,EAAErF,SAAU;UACjBsF,QAAQ,EAAGxH,CAAC,IAAKmC,YAAY,CAACnC,CAAC,CAAC0H,MAAM,CAACH,KAAK;QAAE;UAAAvI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBR,OAAA,CAACzB,aAAa;QAAAkD,QAAA,gBACZzB,OAAA,CAAChC,MAAM;UAACiD,OAAO,EAAE2I,oBAAqB;UAAAnI,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDR,OAAA,CAAChC,MAAM;UAACiD,OAAO,EAAE4I,YAAa;UAAClI,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACuC,GAAA,CA12BIP,aAAa;EAAA,QAsHW1C,WAAW;AAAA;AAAAsP,GAAA,GAtHnC5M,aAAa;AA42BnB,eAAeA,aAAa;AAAC,IAAA/B,EAAA,EAAAI,GAAA,EAAA0B,GAAA,EAAA6M,GAAA;AAAAC,YAAA,CAAA5O,EAAA;AAAA4O,YAAA,CAAAxO,GAAA;AAAAwO,YAAA,CAAA9M,GAAA;AAAA8M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}