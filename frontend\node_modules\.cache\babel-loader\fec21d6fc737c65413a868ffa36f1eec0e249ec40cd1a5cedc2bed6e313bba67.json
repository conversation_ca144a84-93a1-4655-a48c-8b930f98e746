{"ast": null, "code": "// Keys are translated from enUS\nexport * from './arSD';\nexport * from './beBY';\nexport * from './bgBG';\nexport * from './csCZ';\nexport * from './daDK';\nexport * from './deDE';\nexport * from './elGR';\nexport * from './enUS';\nexport * from './esES';\nexport * from './faIR';\nexport * from './fiFI';\nexport * from './frFR';\nexport * from './heIL';\nexport * from './huHU';\nexport * from './itIT';\nexport * from './jaJP';\nexport * from './koKR';\nexport * from './nbNO';\nexport * from './nlNL';\nexport * from './plPL';\nexport * from './ptBR';\nexport * from './roRO';\nexport * from './ruRU';\nexport * from './skSK';\nexport * from './svSE';\nexport * from './trTR';\nexport * from './ukUA';\nexport * from './urPK';\nexport * from './viVN';\nexport * from './zhCN';\nexport * from './zhTW';\nexport * from './hrHR';\nexport * from './ptPT';\nexport * from './zhHK';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/locales/index.js"], "sourcesContent": ["// Keys are translated from enUS\nexport * from './arSD';\nexport * from './beBY';\nexport * from './bgBG';\nexport * from './csCZ';\nexport * from './daDK';\nexport * from './deDE';\nexport * from './elGR';\nexport * from './enUS';\nexport * from './esES';\nexport * from './faIR';\nexport * from './fiFI';\nexport * from './frFR';\nexport * from './heIL';\nexport * from './huHU';\nexport * from './itIT';\nexport * from './jaJP';\nexport * from './koKR';\nexport * from './nbNO';\nexport * from './nlNL';\nexport * from './plPL';\nexport * from './ptBR';\nexport * from './roRO';\nexport * from './ruRU';\nexport * from './skSK';\nexport * from './svSE';\nexport * from './trTR';\nexport * from './ukUA';\nexport * from './urPK';\nexport * from './viVN';\nexport * from './zhCN';\nexport * from './zhTW';\nexport * from './hrHR';\nexport * from './ptPT';\nexport * from './zhHK';"], "mappings": "AAAA;AACA,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}