{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF } from '../../../colDef';\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from './gridColumnsSelector';\nimport { clamp } from '../../../utils/utils';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../columnGrouping/gridColumnGroupsSelector';\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth(_ref) {\n  let {\n    initialFreeSpace,\n    totalFlexUnits,\n    flexColumns\n  } = _ref;\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(_ref2 => {\n        let {\n          field\n        } = _ref2;\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, viewportInnerWidth) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute there minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    const newColumn = _extends({}, rawState.lookup[columnField]);\n    if (rawState.columnVisibilityModel[columnField] === false) {\n      newColumn.computedWidth = 0;\n    } else {\n      let computedWidth;\n      if (newColumn.flex && newColumn.flex > 0) {\n        totalFlexUnits += newColumn.flex;\n        computedWidth = 0;\n        flexColumns.push(newColumn);\n      } else {\n        computedWidth = clamp(newColumn.width || GRID_STRING_COL_DEF.width, newColumn.minWidth || GRID_STRING_COL_DEF.minWidth, newColumn.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n      newColumn.computedWidth = computedWidth;\n    }\n    columnsLookup[columnField] = newColumn;\n  });\n  const initialFreeSpace = Math.max(viewportInnerWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && viewportInnerWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(_ref3 => {\n      let [key, value] = _ref3;\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(columnTypes, type) {\n  let colDef = columnTypes[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && columnTypes[type]) {\n    colDef = columnTypes[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = _ref4 => {\n  let {\n    apiRef,\n    columnsToUpsert,\n    initialState,\n    columnTypes,\n    columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n    keepOnlyColumnsToUpsert = false\n  } = _ref4;\n  var _apiRef$current$getRo, _apiRef$current$getRo2, _apiRef$current;\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = _extends({}, existingState, newColumn, {\n      hasBeenResized\n    });\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, (_apiRef$current$getRo = (_apiRef$current$getRo2 = (_apiRef$current = apiRef.current).getRootDimensions) == null || (_apiRef$current$getRo2 = _apiRef$current$getRo2.call(_apiRef$current)) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0);\n};\nexport const mergeColumnsState = columnsState => state => _extends({}, state, {\n  columns: columnsState\n});\nexport function getFirstNonSpannedColumnToRender(_ref5) {\n  let {\n    firstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  } = _ref5;\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getFirstColumnIndexToRender(_ref6) {\n  let {\n    firstColumnIndex,\n    minColumnIndex,\n    columnBuffer,\n    firstRowToRender,\n    lastRowToRender,\n    apiRef,\n    visibleRows\n  } = _ref6;\n  const initialFirstColumnToRender = Math.max(firstColumnIndex - columnBuffer, minColumnIndex);\n  const firstColumnToRender = getFirstNonSpannedColumnToRender({\n    firstColumnToRender: initialFirstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  });\n  return firstColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, headerHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  return Math.floor(headerHeight * densityFactor) * ((maxDepth != null ? maxDepth : 0) + 1);\n}", "map": {"version": 3, "names": ["_extends", "DEFAULT_GRID_COL_TYPE_KEY", "GRID_STRING_COL_DEF", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "clamp", "gridDensityFactorSelector", "gridColumnGroupsHeaderMaxDepthSelector", "COLUMNS_DIMENSION_PROPERTIES", "computeFlexColumnsWidth", "_ref", "initialFreeSpace", "totalFlexUnits", "flexColumns", "uniqueFlexColumns", "Set", "map", "col", "field", "flexColumnsLookup", "all", "frozenFields", "freeze", "value", "frozen", "push", "loopOverFlexItems", "length", "size", "violationsLookup", "min", "max", "remainingFreeSpace", "flexUnits", "totalViolation", "for<PERSON>ach", "computedWidth", "flex", "i", "column", "widthPerFlexUnit", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Object", "keys", "_ref2", "hydrateColumnsWidth", "rawState", "viewportInnerWidth", "columnsLookup", "widthAllocatedBeforeFlex", "orderedFields", "columnField", "newColumn", "lookup", "columnVisibilityModel", "width", "Math", "computedColumnWidths", "applyInitialState", "columnsState", "initialState", "dimensions", "columnsWithUpdatedDimensions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanOrderedFields", "new<PERSON><PERSON><PERSON><PERSON><PERSON>s", "filter", "newColumnLookup", "newColDef", "hasBeenResized", "entries", "_ref3", "key", "Infinity", "newColumnsState", "getDefaultColTypeDef", "columnTypes", "type", "colDef", "createColumnsState", "_ref4", "apiRef", "columnsToUpsert", "keepOnlyColumnsToUpsert", "_apiRef$current$getRo", "_apiRef$current$getRo2", "_apiRef$current", "isInsideStateInitializer", "current", "state", "columns", "currentState", "columnsToKeep", "reduce", "acc", "columnsToUpsertLookup", "existingState", "undefined", "columnsStateWithPreProcessing", "unstable_applyPipeProcessors", "columnsStateWithPortableColumns", "getRootDimensions", "call", "viewportInnerSize", "mergeColumnsState", "getFirstNonSpannedColumnToRender", "_ref5", "firstColumnToRender", "firstRowToRender", "lastRowToRender", "visibleRows", "firstNonSpannedColumnToRender", "row", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "getFirstColumnIndexToRender", "_ref6", "firstColumnIndex", "minColumnIndex", "columnBuffer", "initialFirstColumnToRender", "getTotalHeaderHeight", "headerHeight", "densityFactor", "max<PERSON><PERSON><PERSON>", "floor"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columns/gridColumnsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF } from '../../../colDef';\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from './gridColumnsSelector';\nimport { clamp } from '../../../utils/utils';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nimport { gridColumnGroupsHeaderMaxDepthSelector } from '../columnGrouping/gridColumnGroupsSelector';\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, viewportInnerWidth) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute there minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    const newColumn = _extends({}, rawState.lookup[columnField]);\n    if (rawState.columnVisibilityModel[columnField] === false) {\n      newColumn.computedWidth = 0;\n    } else {\n      let computedWidth;\n      if (newColumn.flex && newColumn.flex > 0) {\n        totalFlexUnits += newColumn.flex;\n        computedWidth = 0;\n        flexColumns.push(newColumn);\n      } else {\n        computedWidth = clamp(newColumn.width || GRID_STRING_COL_DEF.width, newColumn.minWidth || GRID_STRING_COL_DEF.minWidth, newColumn.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n      newColumn.computedWidth = computedWidth;\n    }\n    columnsLookup[columnField] = newColumn;\n  });\n  const initialFreeSpace = Math.max(viewportInnerWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && viewportInnerWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(columnTypes, type) {\n  let colDef = columnTypes[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && columnTypes[type]) {\n    colDef = columnTypes[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnTypes,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false\n}) => {\n  var _apiRef$current$getRo, _apiRef$current$getRo2, _apiRef$current;\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(columnTypes, newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = _extends({}, existingState, newColumn, {\n      hasBeenResized\n    });\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, (_apiRef$current$getRo = (_apiRef$current$getRo2 = (_apiRef$current = apiRef.current).getRootDimensions) == null || (_apiRef$current$getRo2 = _apiRef$current$getRo2.call(_apiRef$current)) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0);\n};\nexport const mergeColumnsState = columnsState => state => _extends({}, state, {\n  columns: columnsState\n});\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getFirstColumnIndexToRender({\n  firstColumnIndex,\n  minColumnIndex,\n  columnBuffer,\n  firstRowToRender,\n  lastRowToRender,\n  apiRef,\n  visibleRows\n}) {\n  const initialFirstColumnToRender = Math.max(firstColumnIndex - columnBuffer, minColumnIndex);\n  const firstColumnToRender = getFirstNonSpannedColumnToRender({\n    firstColumnToRender: initialFirstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  });\n  return firstColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, headerHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  return Math.floor(headerHeight * densityFactor) * ((maxDepth != null ? maxDepth : 0) + 1);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,yBAAyB,EAAEC,mBAAmB,QAAQ,iBAAiB;AAChF,SAASC,wBAAwB,EAAEC,iCAAiC,QAAQ,uBAAuB;AACnG,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,sCAAsC,QAAQ,4CAA4C;AACnG,OAAO,MAAMC,4BAA4B,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAAAC,IAAA,EAIpC;EAAA,IAJqC;IACtCC,gBAAgB;IAChBC,cAAc;IACdC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,iBAAiB,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMC,iBAAiB,GAAG;IACxBC,GAAG,EAAE,CAAC,CAAC;IACPC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAEJ,KAAK,IAAI;MACf,MAAMK,KAAK,GAAGJ,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC;MAC1C,IAAIK,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,EAAE;QAClCL,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACM,MAAM,GAAG,IAAI;QAC1CL,iBAAiB,CAACE,YAAY,CAACI,IAAI,CAACP,KAAK,CAAC;MAC5C;IACF;EACF,CAAC;;EAED;EACA,SAASQ,iBAAiBA,CAAA,EAAG;IAC3B;IACA,IAAIP,iBAAiB,CAACE,YAAY,CAACM,MAAM,KAAKb,iBAAiB,CAACc,IAAI,EAAE;MACpE;IACF;IACA,MAAMC,gBAAgB,GAAG;MACvBC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC;IACR,CAAC;IACD,IAAIC,kBAAkB,GAAGrB,gBAAgB;IACzC,IAAIsB,SAAS,GAAGrB,cAAc;IAC9B,IAAIsB,cAAc,GAAG,CAAC;;IAEtB;IACAf,iBAAiB,CAACE,YAAY,CAACc,OAAO,CAACjB,KAAK,IAAI;MAC9Cc,kBAAkB,IAAIb,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACkB,aAAa;MAChEH,SAAS,IAAId,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACmB,IAAI;IAChD,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,WAAW,CAACc,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;MAC9C,MAAMC,MAAM,GAAG1B,WAAW,CAACyB,CAAC,CAAC;MAC7B,IAAInB,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,IAAIC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,CAACM,MAAM,KAAK,IAAI,EAAE;QAC9F;MACF;;MAEA;MACA,MAAMgB,gBAAgB,GAAGR,kBAAkB,GAAGC,SAAS;MACvD,IAAIG,aAAa,GAAGI,gBAAgB,GAAGD,MAAM,CAACF,IAAI;;MAElD;MACA,IAAID,aAAa,GAAGG,MAAM,CAACE,QAAQ,EAAE;QACnCP,cAAc,IAAIK,MAAM,CAACE,QAAQ,GAAGL,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACE,QAAQ;QAC/BZ,gBAAgB,CAACC,GAAG,CAACS,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C,CAAC,MAAM,IAAIkB,aAAa,GAAGG,MAAM,CAACG,QAAQ,EAAE;QAC1CR,cAAc,IAAIK,MAAM,CAACG,QAAQ,GAAGN,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACG,QAAQ;QAC/Bb,gBAAgB,CAACE,GAAG,CAACQ,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C;MACAC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,GAAG;QACpCM,MAAM,EAAE,KAAK;QACbY,aAAa;QACbC,IAAI,EAAEE,MAAM,CAACF;MACf,CAAC;IACH;;IAEA;IACA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACE,GAAG,CAAC,CAACI,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgB,cAAc,GAAG,CAAC,EAAE;MAC7B;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACC,GAAG,CAAC,CAACK,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAACsB,OAAO,CAACU,KAAA,IAEd;QAAA,IAFe;UACnB3B;QACF,CAAC,GAAA2B,KAAA;QACC1B,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;;IAEA;IACAQ,iBAAiB,CAAC,CAAC;EACrB;EACAA,iBAAiB,CAAC,CAAC;EACnB,OAAOP,iBAAiB,CAACC,GAAG;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,kBAAkB,KAAK;EACnE,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIrC,cAAc,GAAG,CAAC;EACtB,IAAIsC,wBAAwB,GAAG,CAAC;EAChC,MAAMrC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACAkC,QAAQ,CAACI,aAAa,CAAChB,OAAO,CAACiB,WAAW,IAAI;IAC5C,MAAMC,SAAS,GAAGrD,QAAQ,CAAC,CAAC,CAAC,EAAE+C,QAAQ,CAACO,MAAM,CAACF,WAAW,CAAC,CAAC;IAC5D,IAAIL,QAAQ,CAACQ,qBAAqB,CAACH,WAAW,CAAC,KAAK,KAAK,EAAE;MACzDC,SAAS,CAACjB,aAAa,GAAG,CAAC;IAC7B,CAAC,MAAM;MACL,IAAIA,aAAa;MACjB,IAAIiB,SAAS,CAAChB,IAAI,IAAIgB,SAAS,CAAChB,IAAI,GAAG,CAAC,EAAE;QACxCzB,cAAc,IAAIyC,SAAS,CAAChB,IAAI;QAChCD,aAAa,GAAG,CAAC;QACjBvB,WAAW,CAACY,IAAI,CAAC4B,SAAS,CAAC;MAC7B,CAAC,MAAM;QACLjB,aAAa,GAAG/B,KAAK,CAACgD,SAAS,CAACG,KAAK,IAAItD,mBAAmB,CAACsD,KAAK,EAAEH,SAAS,CAACZ,QAAQ,IAAIvC,mBAAmB,CAACuC,QAAQ,EAAEY,SAAS,CAACX,QAAQ,IAAIxC,mBAAmB,CAACwC,QAAQ,CAAC;MAC7K;MACAQ,wBAAwB,IAAId,aAAa;MACzCiB,SAAS,CAACjB,aAAa,GAAGA,aAAa;IACzC;IACAa,aAAa,CAACG,WAAW,CAAC,GAAGC,SAAS;EACxC,CAAC,CAAC;EACF,MAAM1C,gBAAgB,GAAG8C,IAAI,CAAC1B,GAAG,CAACiB,kBAAkB,GAAGE,wBAAwB,EAAE,CAAC,CAAC;;EAEnF;EACA,IAAItC,cAAc,GAAG,CAAC,IAAIoC,kBAAkB,GAAG,CAAC,EAAE;IAChD,MAAMU,oBAAoB,GAAGjD,uBAAuB,CAAC;MACnDE,gBAAgB;MAChBC,cAAc;MACdC;IACF,CAAC,CAAC;IACF8B,MAAM,CAACC,IAAI,CAACc,oBAAoB,CAAC,CAACvB,OAAO,CAACjB,KAAK,IAAI;MACjD+B,aAAa,CAAC/B,KAAK,CAAC,CAACkB,aAAa,GAAGsB,oBAAoB,CAACxC,KAAK,CAAC,CAACkB,aAAa;IAChF,CAAC,CAAC;EACJ;EACA,OAAOpC,QAAQ,CAAC,CAAC,CAAC,EAAE+C,QAAQ,EAAE;IAC5BO,MAAM,EAAEL;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMU,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;EAC/D,IAAI,CAACA,YAAY,EAAE;IACjB,OAAOD,YAAY;EACrB;EACA,MAAM;IACJT,aAAa,GAAG,EAAE;IAClBW,UAAU,GAAG,CAAC;EAChB,CAAC,GAAGD,YAAY;EAChB,MAAME,4BAA4B,GAAGpB,MAAM,CAACC,IAAI,CAACkB,UAAU,CAAC;EAC5D,IAAIC,4BAA4B,CAACpC,MAAM,KAAK,CAAC,IAAIwB,aAAa,CAACxB,MAAM,KAAK,CAAC,EAAE;IAC3E,OAAOiC,YAAY;EACrB;EACA,MAAMI,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,aAAa,CAACxB,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMpB,KAAK,GAAGiC,aAAa,CAACb,CAAC,CAAC;;IAE9B;IACA,IAAIsB,YAAY,CAACN,MAAM,CAACpC,KAAK,CAAC,EAAE;MAC9B8C,mBAAmB,CAAC9C,KAAK,CAAC,GAAG,IAAI;MACjC+C,kBAAkB,CAACxC,IAAI,CAACP,KAAK,CAAC;IAChC;EACF;EACA,MAAMgD,gBAAgB,GAAGD,kBAAkB,CAACtC,MAAM,KAAK,CAAC,GAAGiC,YAAY,CAACT,aAAa,GAAG,CAAC,GAAGc,kBAAkB,EAAE,GAAGL,YAAY,CAACT,aAAa,CAACgB,MAAM,CAACjD,KAAK,IAAI,CAAC8C,mBAAmB,CAAC9C,KAAK,CAAC,CAAC,CAAC;EAC3L,MAAMkD,eAAe,GAAGpE,QAAQ,CAAC,CAAC,CAAC,EAAE4D,YAAY,CAACN,MAAM,CAAC;EACzD,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,4BAA4B,CAACpC,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAC/D,MAAMpB,KAAK,GAAG6C,4BAA4B,CAACzB,CAAC,CAAC;IAC7C,MAAM+B,SAAS,GAAGrE,QAAQ,CAAC,CAAC,CAAC,EAAEoE,eAAe,CAAClD,KAAK,CAAC,EAAE;MACrDoD,cAAc,EAAE;IAClB,CAAC,CAAC;IACF3B,MAAM,CAAC4B,OAAO,CAACT,UAAU,CAAC5C,KAAK,CAAC,CAAC,CAACiB,OAAO,CAACqC,KAAA,IAAkB;MAAA,IAAjB,CAACC,GAAG,EAAElD,KAAK,CAAC,GAAAiD,KAAA;MACrDH,SAAS,CAACI,GAAG,CAAC,GAAGlD,KAAK,KAAK,CAAC,CAAC,GAAGmD,QAAQ,GAAGnD,KAAK;IAClD,CAAC,CAAC;IACF6C,eAAe,CAAClD,KAAK,CAAC,GAAGmD,SAAS;EACpC;EACA,MAAMM,eAAe,GAAG3E,QAAQ,CAAC,CAAC,CAAC,EAAE4D,YAAY,EAAE;IACjDT,aAAa,EAAEe,gBAAgB;IAC/BZ,MAAM,EAAEc;EACV,CAAC,CAAC;EACF,OAAOO,eAAe;AACxB,CAAC;AACD,SAASC,oBAAoBA,CAACC,WAAW,EAAEC,IAAI,EAAE;EAC/C,IAAIC,MAAM,GAAGF,WAAW,CAAC5E,yBAAyB,CAAC;EACnD,IAAI6E,IAAI,IAAID,WAAW,CAACC,IAAI,CAAC,EAAE;IAC7BC,MAAM,GAAGF,WAAW,CAACC,IAAI,CAAC;EAC5B;EACA,OAAOC,MAAM;AACf;AACA,OAAO,MAAMC,kBAAkB,GAAGC,KAAA,IAO5B;EAAA,IAP6B;IACjCC,MAAM;IACNC,eAAe;IACftB,YAAY;IACZgB,WAAW;IACXtB,qBAAqB,GAAGnD,iCAAiC,CAAC8E,MAAM,CAAC;IACjEE,uBAAuB,GAAG;EAC5B,CAAC,GAAAH,KAAA;EACC,IAAII,qBAAqB,EAAEC,sBAAsB,EAAEC,eAAe;EAClE,MAAMC,wBAAwB,GAAG,CAACN,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,OAAO;EAC9D,IAAI/B,YAAY;EAChB,IAAI4B,wBAAwB,EAAE;IAC5B5B,YAAY,GAAG;MACbT,aAAa,EAAE,EAAE;MACjBG,MAAM,EAAE,CAAC,CAAC;MACVC;IACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMqC,YAAY,GAAGzF,wBAAwB,CAAC+E,MAAM,CAACO,OAAO,CAACC,KAAK,CAAC;IACnE9B,YAAY,GAAG;MACbT,aAAa,EAAEiC,uBAAuB,GAAG,EAAE,GAAG,CAAC,GAAGQ,YAAY,CAACzC,aAAa,CAAC;MAC7EG,MAAM,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAE4F,YAAY,CAACtC,MAAM,CAAC;MACzC;MACAC;IACF,CAAC;EACH;EACA,IAAIsC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIT,uBAAuB,IAAI,CAACI,wBAAwB,EAAE;IACxDK,aAAa,GAAGlD,MAAM,CAACC,IAAI,CAACgB,YAAY,CAACN,MAAM,CAAC,CAACwC,MAAM,CAAC,CAACC,GAAG,EAAEtB,GAAG,KAAKzE,QAAQ,CAAC,CAAC,CAAC,EAAE+F,GAAG,EAAE;MACtF,CAACtB,GAAG,GAAG;IACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT;EACA,MAAMuB,qBAAqB,GAAG,CAAC,CAAC;EAChCb,eAAe,CAAChD,OAAO,CAACkB,SAAS,IAAI;IACnC,MAAM;MACJnC;IACF,CAAC,GAAGmC,SAAS;IACb2C,qBAAqB,CAAC9E,KAAK,CAAC,GAAG,IAAI;IACnC2E,aAAa,CAAC3E,KAAK,CAAC,GAAG,IAAI;IAC3B,IAAI+E,aAAa,GAAGrC,YAAY,CAACN,MAAM,CAACpC,KAAK,CAAC;IAC9C,IAAI+E,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGjG,QAAQ,CAAC,CAAC,CAAC,EAAE4E,oBAAoB,CAACC,WAAW,EAAExB,SAAS,CAACyB,IAAI,CAAC,EAAE;QAC9E5D,KAAK;QACLoD,cAAc,EAAE;MAClB,CAAC,CAAC;MACFV,YAAY,CAACT,aAAa,CAAC1B,IAAI,CAACP,KAAK,CAAC;IACxC,CAAC,MAAM,IAAIkE,uBAAuB,EAAE;MAClCxB,YAAY,CAACT,aAAa,CAAC1B,IAAI,CAACP,KAAK,CAAC;IACxC;;IAEA;IACA,IAAI+E,aAAa,IAAIA,aAAa,CAACnB,IAAI,KAAKzB,SAAS,CAACyB,IAAI,EAAE;MAC1DmB,aAAa,GAAGjG,QAAQ,CAAC,CAAC,CAAC,EAAE4E,oBAAoB,CAACC,WAAW,EAAExB,SAAS,CAACyB,IAAI,CAAC,EAAE;QAC9E5D;MACF,CAAC,CAAC;IACJ;IACA,IAAIoD,cAAc,GAAG2B,aAAa,CAAC3B,cAAc;IACjD9D,4BAA4B,CAAC2B,OAAO,CAACsC,GAAG,IAAI;MAC1C,IAAIpB,SAAS,CAACoB,GAAG,CAAC,KAAKyB,SAAS,EAAE;QAChC5B,cAAc,GAAG,IAAI;QACrB,IAAIjB,SAAS,CAACoB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzBpB,SAAS,CAACoB,GAAG,CAAC,GAAGC,QAAQ;QAC3B;MACF;IACF,CAAC,CAAC;IACFd,YAAY,CAACN,MAAM,CAACpC,KAAK,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAEiG,aAAa,EAAE5C,SAAS,EAAE;MAClEiB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIc,uBAAuB,IAAI,CAACI,wBAAwB,EAAE;IACxD7C,MAAM,CAACC,IAAI,CAACgB,YAAY,CAACN,MAAM,CAAC,CAACnB,OAAO,CAACjB,KAAK,IAAI;MAChD,IAAI,CAAC2E,aAAa,CAAC3E,KAAK,CAAC,EAAE;QACzB,OAAO0C,YAAY,CAACN,MAAM,CAACpC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;EACA,MAAMiF,6BAA6B,GAAGjB,MAAM,CAACO,OAAO,CAACW,4BAA4B,CAAC,gBAAgB,EAAExC,YAAY,CAAC;EACjH,MAAMyC,+BAA+B,GAAG1C,iBAAiB,CAACwC,6BAA6B,EAAEtC,YAAY,CAAC;EACtG,OAAOf,mBAAmB,CAACuD,+BAA+B,EAAE,CAAChB,qBAAqB,GAAG,CAACC,sBAAsB,GAAG,CAACC,eAAe,GAAGL,MAAM,CAACO,OAAO,EAAEa,iBAAiB,KAAK,IAAI,IAAI,CAAChB,sBAAsB,GAAGA,sBAAsB,CAACiB,IAAI,CAAChB,eAAe,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAACkB,iBAAiB,CAAChD,KAAK,KAAK,IAAI,GAAG6B,qBAAqB,GAAG,CAAC,CAAC;AACjW,CAAC;AACD,OAAO,MAAMoB,iBAAiB,GAAG7C,YAAY,IAAI8B,KAAK,IAAI1F,QAAQ,CAAC,CAAC,CAAC,EAAE0F,KAAK,EAAE;EAC5EC,OAAO,EAAE/B;AACX,CAAC,CAAC;AACF,OAAO,SAAS8C,gCAAgCA,CAAAC,KAAA,EAM7C;EAAA,IAN8C;IAC/CC,mBAAmB;IACnB1B,MAAM;IACN2B,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,GAAAJ,KAAA;EACC,IAAIK,6BAA6B,GAAGJ,mBAAmB;EACvD,KAAK,IAAItE,CAAC,GAAGuE,gBAAgB,EAAEvE,CAAC,GAAGwE,eAAe,EAAExE,CAAC,IAAI,CAAC,EAAE;IAC1D,MAAM2E,GAAG,GAAGF,WAAW,CAACzE,CAAC,CAAC;IAC1B,IAAI2E,GAAG,EAAE;MACP,MAAMC,KAAK,GAAGH,WAAW,CAACzE,CAAC,CAAC,CAAC6E,EAAE;MAC/B,MAAMC,eAAe,GAAGlC,MAAM,CAACO,OAAO,CAAC4B,2BAA2B,CAACH,KAAK,EAAEN,mBAAmB,CAAC;MAC9F,IAAIQ,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;QACvDN,6BAA6B,GAAGI,eAAe,CAACG,oBAAoB;MACtE;IACF;EACF;EACA,OAAOP,6BAA6B;AACtC;AACA,OAAO,SAASQ,2BAA2BA,CAAAC,KAAA,EAQxC;EAAA,IARyC;IAC1CC,gBAAgB;IAChBC,cAAc;IACdC,YAAY;IACZf,gBAAgB;IAChBC,eAAe;IACf5B,MAAM;IACN6B;EACF,CAAC,GAAAU,KAAA;EACC,MAAMI,0BAA0B,GAAGpE,IAAI,CAAC1B,GAAG,CAAC2F,gBAAgB,GAAGE,YAAY,EAAED,cAAc,CAAC;EAC5F,MAAMf,mBAAmB,GAAGF,gCAAgC,CAAC;IAC3DE,mBAAmB,EAAEiB,0BAA0B;IAC/C3C,MAAM;IACN2B,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,CAAC;EACF,OAAOH,mBAAmB;AAC5B;AACA,OAAO,SAASkB,oBAAoBA,CAAC5C,MAAM,EAAE6C,YAAY,EAAE;EACzD,MAAMC,aAAa,GAAG1H,yBAAyB,CAAC4E,MAAM,CAAC;EACvD,MAAM+C,QAAQ,GAAG1H,sCAAsC,CAAC2E,MAAM,CAAC;EAC/D,OAAOzB,IAAI,CAACyE,KAAK,CAACH,YAAY,GAAGC,aAAa,CAAC,IAAI,CAACC,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}