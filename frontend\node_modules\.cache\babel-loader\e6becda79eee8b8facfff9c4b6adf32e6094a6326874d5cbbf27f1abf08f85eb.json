{"ast": null, "code": "function _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n  return [true, null];\n}\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n    accept = _ref.accept,\n    minSize = _ref.minSize,\n    maxSize = _ref.maxSize,\n    multiple = _ref.multiple,\n    maxFiles = _ref.maxFiles,\n    validator = _ref.validator;\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n      _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n      accepted = _fileAccepted2[0];\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n      _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n      sizeMatch = _fileMatchSize2[0];\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n        mimeType = _ref3[0],\n        ext = _ref3[1];\n      var ok = true;\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n        mimeType = _ref5[0],\n        ext = _ref5[1];\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n        mimeType = _ref7[0],\n        ext = _ref7[1];\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */", "map": {"version": 3, "names": ["_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "iter", "Symbol", "iterator", "Array", "from", "isArray", "_arrayLikeToArray", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "_typeof", "constructor", "prototype", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_nonIterableRest", "o", "minLen", "n", "toString", "call", "slice", "name", "test", "len", "arr2", "_i", "_arr", "_n", "_d", "_s", "_e", "next", "done", "err", "_accepts", "accepts", "default", "FILE_INVALID_TYPE", "FILE_TOO_LARGE", "FILE_TOO_SMALL", "TOO_MANY_FILES", "ErrorCode", "FileInvalidType", "FileTooLarge", "FileTooSmall", "TooManyFiles", "getInvalidTypeRejectionErr", "accept", "undefined", "acceptArr", "split", "msg", "concat", "join", "code", "message", "getTooLargeRejectionErr", "maxSize", "getTooSmallRejectionErr", "minSize", "TOO_MANY_FILES_REJECTION", "fileAccepted", "file", "isAcceptable", "type", "fileMatchSize", "isDefined", "size", "allFilesAccepted", "_ref", "files", "multiple", "maxFiles", "validator", "every", "_fileAccepted", "_fileAccepted2", "accepted", "_fileMatchSize", "_fileMatchSize2", "sizeMatch", "customErrors", "isPropagationStopped", "event", "cancelBubble", "isEvtWithFiles", "dataTransfer", "some", "types", "isKindFile", "item", "kind", "onDocumentDragOver", "preventDefault", "isIe", "userAgent", "indexOf", "isEdge", "isIeOrEdge", "window", "navigator", "composeEventHandlers", "_len", "fns", "_key", "_len2", "args", "_key2", "fn", "canUseFileSystemAccessAPI", "pickerOptionsFromAccept", "acceptForPicker", "entries", "_ref2", "_ref3", "mimeType", "ext", "ok", "isMIMEType", "console", "warn", "isExt", "reduce", "agg", "_ref4", "_ref5", "description", "acceptPropAsAcceptAttr", "a", "_ref6", "_ref7", "v", "isAbort", "DOMException", "ABORT_ERR", "isSecurityError", "SECURITY_ERR"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/react-dropzone/dist/es/utils/index.js"], "sourcesContent": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AAExJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAE7L,SAASH,gBAAgBA,CAACI,IAAI,EAAE;EAAE,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIF,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOG,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC;AAAE;AAE7J,SAASL,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIS,KAAK,CAACE,OAAO,CAACX,GAAG,CAAC,EAAE,OAAOY,iBAAiB,CAACZ,GAAG,CAAC;AAAE;AAE1F,SAASa,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAEpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AAEzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,OAAOA,CAACJ,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOI,OAAO,GAAG,UAAU,IAAI,OAAOnC,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAU8B,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAO/B,MAAM,IAAI+B,GAAG,CAACK,WAAW,KAAKpC,MAAM,IAAI+B,GAAG,KAAK/B,MAAM,CAACqC,SAAS,GAAG,QAAQ,GAAG,OAAON,GAAG;EAAE,CAAC,EAAEI,OAAO,CAACJ,GAAG,CAAC;AAAE;AAE/U,SAASO,cAAcA,CAAC7C,GAAG,EAAE4B,CAAC,EAAE;EAAE,OAAOkB,eAAe,CAAC9C,GAAG,CAAC,IAAI+C,qBAAqB,CAAC/C,GAAG,EAAE4B,CAAC,CAAC,IAAIzB,2BAA2B,CAACH,GAAG,EAAE4B,CAAC,CAAC,IAAIoB,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAI3C,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAAC8C,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOrC,iBAAiB,CAACqC,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIC,CAAC,GAAGlC,MAAM,CAAC2B,SAAS,CAACQ,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIH,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACN,WAAW,EAAEQ,CAAC,GAAGF,CAAC,CAACN,WAAW,CAACY,IAAI;EAAE,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAO1C,KAAK,CAACC,IAAI,CAACuC,CAAC,CAAC;EAAE,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACK,IAAI,CAACL,CAAC,CAAC,EAAE,OAAOvC,iBAAiB,CAACqC,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAAStC,iBAAiBA,CAACZ,GAAG,EAAEyD,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGzD,GAAG,CAAC8B,MAAM,EAAE2B,GAAG,GAAGzD,GAAG,CAAC8B,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE8B,IAAI,GAAG,IAAIjD,KAAK,CAACgD,GAAG,CAAC,EAAE7B,CAAC,GAAG6B,GAAG,EAAE7B,CAAC,EAAE,EAAE;IAAE8B,IAAI,CAAC9B,CAAC,CAAC,GAAG5B,GAAG,CAAC4B,CAAC,CAAC;EAAE;EAAE,OAAO8B,IAAI;AAAE;AAEtL,SAASX,qBAAqBA,CAAC/C,GAAG,EAAE4B,CAAC,EAAE;EAAE,IAAI+B,EAAE,GAAG3D,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOO,MAAM,KAAK,WAAW,IAAIP,GAAG,CAACO,MAAM,CAACC,QAAQ,CAAC,IAAIR,GAAG,CAAC,YAAY,CAAC;EAAE,IAAI2D,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIC,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKL,EAAE,GAAGA,EAAE,CAACN,IAAI,CAACrD,GAAG,CAAC,EAAE,EAAE6D,EAAE,GAAG,CAACE,EAAE,GAAGJ,EAAE,CAACM,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACpC,IAAI,CAACuC,EAAE,CAACxB,KAAK,CAAC;MAAE,IAAIX,CAAC,IAAIgC,IAAI,CAAC9B,MAAM,KAAKF,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOuC,GAAG,EAAE;IAAEL,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGG,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACN,EAAE,IAAIF,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIG,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASd,eAAeA,CAAC9C,GAAG,EAAE;EAAE,IAAIS,KAAK,CAACE,OAAO,CAACX,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAOoE,QAAQ,MAAM,aAAa;AAClC,IAAIC,OAAO,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAGA,QAAQ,CAACE,OAAO,CAAC,CAAC;;AAE5E,OAAO,IAAIC,iBAAiB,GAAG,mBAAmB;AAClD,OAAO,IAAIC,cAAc,GAAG,gBAAgB;AAC5C,OAAO,IAAIC,cAAc,GAAG,gBAAgB;AAC5C,OAAO,IAAIC,cAAc,GAAG,gBAAgB;AAC5C,OAAO,IAAIC,SAAS,GAAG;EACrBC,eAAe,EAAEL,iBAAiB;EAClCM,YAAY,EAAEL,cAAc;EAC5BM,YAAY,EAAEL,cAAc;EAC5BM,YAAY,EAAEL;AAChB,CAAC;AACD;AACA;AACA;AACA;;AAEA,OAAO,IAAIM,0BAA0B,GAAG,SAASA,0BAA0BA,CAAA,EAAG;EAC5E,IAAIC,MAAM,GAAGpD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqD,SAAS,GAAGrD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,IAAIsD,SAAS,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EACjC,IAAIC,GAAG,GAAGF,SAAS,CAACrD,MAAM,GAAG,CAAC,GAAG,SAAS,CAACwD,MAAM,CAACH,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGJ,SAAS,CAAC,CAAC,CAAC;EACtF,OAAO;IACLK,IAAI,EAAEjB,iBAAiB;IACvBkB,OAAO,EAAE,oBAAoB,CAACH,MAAM,CAACD,GAAG;EAC1C,CAAC;AACH,CAAC;AACD,OAAO,IAAIK,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,OAAO,EAAE;EAC7E,OAAO;IACLH,IAAI,EAAEhB,cAAc;IACpBiB,OAAO,EAAE,sBAAsB,CAACH,MAAM,CAACK,OAAO,EAAE,GAAG,CAAC,CAACL,MAAM,CAACK,OAAO,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;EAC9F,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,OAAO,EAAE;EAC7E,OAAO;IACLL,IAAI,EAAEf,cAAc;IACpBgB,OAAO,EAAE,uBAAuB,CAACH,MAAM,CAACO,OAAO,EAAE,GAAG,CAAC,CAACP,MAAM,CAACO,OAAO,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;EAC/F,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,wBAAwB,GAAG;EACpCN,IAAI,EAAEd,cAAc;EACpBe,OAAO,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASM,YAAYA,CAACC,IAAI,EAAEf,MAAM,EAAE;EACzC,IAAIgB,YAAY,GAAGD,IAAI,CAACE,IAAI,KAAK,wBAAwB,IAAI7B,OAAO,CAAC2B,IAAI,EAAEf,MAAM,CAAC;EAClF,OAAO,CAACgB,YAAY,EAAEA,YAAY,GAAG,IAAI,GAAGjB,0BAA0B,CAACC,MAAM,CAAC,CAAC;AACjF;AACA,OAAO,SAASkB,aAAaA,CAACH,IAAI,EAAEH,OAAO,EAAEF,OAAO,EAAE;EACpD,IAAIS,SAAS,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;IACxB,IAAID,SAAS,CAACP,OAAO,CAAC,IAAIO,SAAS,CAACT,OAAO,CAAC,EAAE;MAC5C,IAAIK,IAAI,CAACK,IAAI,GAAGV,OAAO,EAAE,OAAO,CAAC,KAAK,EAAED,uBAAuB,CAACC,OAAO,CAAC,CAAC;MACzE,IAAIK,IAAI,CAACK,IAAI,GAAGR,OAAO,EAAE,OAAO,CAAC,KAAK,EAAED,uBAAuB,CAACC,OAAO,CAAC,CAAC;IAC3E,CAAC,MAAM,IAAIO,SAAS,CAACP,OAAO,CAAC,IAAIG,IAAI,CAACK,IAAI,GAAGR,OAAO,EAAE,OAAO,CAAC,KAAK,EAAED,uBAAuB,CAACC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAIO,SAAS,CAACT,OAAO,CAAC,IAAIK,IAAI,CAACK,IAAI,GAAGV,OAAO,EAAE,OAAO,CAAC,KAAK,EAAED,uBAAuB,CAACC,OAAO,CAAC,CAAC;EAC7M;EAEA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;AACrB;AAEA,SAASS,SAASA,CAAC7D,KAAK,EAAE;EACxB,OAAOA,KAAK,KAAK2C,SAAS,IAAI3C,KAAK,KAAK,IAAI;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAAS+D,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBvB,MAAM,GAAGsB,IAAI,CAACtB,MAAM;IACpBY,OAAO,GAAGU,IAAI,CAACV,OAAO;IACtBF,OAAO,GAAGY,IAAI,CAACZ,OAAO;IACtBc,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;EAE9B,IAAI,CAACF,QAAQ,IAAID,KAAK,CAAC1E,MAAM,GAAG,CAAC,IAAI2E,QAAQ,IAAIC,QAAQ,IAAI,CAAC,IAAIF,KAAK,CAAC1E,MAAM,GAAG4E,QAAQ,EAAE;IACzF,OAAO,KAAK;EACd;EAEA,OAAOF,KAAK,CAACI,KAAK,CAAC,UAAUZ,IAAI,EAAE;IACjC,IAAIa,aAAa,GAAGd,YAAY,CAACC,IAAI,EAAEf,MAAM,CAAC;MAC1C6B,cAAc,GAAGjE,cAAc,CAACgE,aAAa,EAAE,CAAC,CAAC;MACjDE,QAAQ,GAAGD,cAAc,CAAC,CAAC,CAAC;IAEhC,IAAIE,cAAc,GAAGb,aAAa,CAACH,IAAI,EAAEH,OAAO,EAAEF,OAAO,CAAC;MACtDsB,eAAe,GAAGpE,cAAc,CAACmE,cAAc,EAAE,CAAC,CAAC;MACnDE,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;IAElC,IAAIE,YAAY,GAAGR,SAAS,GAAGA,SAAS,CAACX,IAAI,CAAC,GAAG,IAAI;IACrD,OAAOe,QAAQ,IAAIG,SAAS,IAAI,CAACC,YAAY;EAC/C,CAAC,CAAC;AACJ,CAAC,CAAC;AACF;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,IAAI,OAAOA,KAAK,CAACD,oBAAoB,KAAK,UAAU,EAAE;IACpD,OAAOC,KAAK,CAACD,oBAAoB,CAAC,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOC,KAAK,CAACC,YAAY,KAAK,WAAW,EAAE;IACpD,OAAOD,KAAK,CAACC,YAAY;EAC3B;EAEA,OAAO,KAAK;AACd;AACA,OAAO,SAASC,cAAcA,CAACF,KAAK,EAAE;EACpC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE;IACvB,OAAO,CAAC,CAACH,KAAK,CAAC1F,MAAM,IAAI,CAAC,CAAC0F,KAAK,CAAC1F,MAAM,CAAC6E,KAAK;EAC/C,CAAC,CAAC;EACF;;EAGA,OAAO/F,KAAK,CAACmC,SAAS,CAAC6E,IAAI,CAACpE,IAAI,CAACgE,KAAK,CAACG,YAAY,CAACE,KAAK,EAAE,UAAUxB,IAAI,EAAE;IACzE,OAAOA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,wBAAwB;EAC9D,CAAC,CAAC;AACJ;AACA,OAAO,SAASyB,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOlF,OAAO,CAACkF,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM;AAC5E,CAAC,CAAC;;AAEF,OAAO,SAASC,kBAAkBA,CAACT,KAAK,EAAE;EACxCA,KAAK,CAACU,cAAc,CAAC,CAAC;AACxB;AAEA,SAASC,IAAIA,CAACC,SAAS,EAAE;EACvB,OAAOA,SAAS,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAID,SAAS,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACjF;AAEA,SAASC,MAAMA,CAACF,SAAS,EAAE;EACzB,OAAOA,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1C;AAEA,OAAO,SAASE,UAAUA,CAAA,EAAG;EAC3B,IAAIH,SAAS,GAAGpG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqD,SAAS,GAAGrD,SAAS,CAAC,CAAC,CAAC,GAAGwG,MAAM,CAACC,SAAS,CAACL,SAAS;EAC9G,OAAOD,IAAI,CAACC,SAAS,CAAC,IAAIE,MAAM,CAACF,SAAS,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASM,oBAAoBA,CAAA,EAAG;EACrC,KAAK,IAAIC,IAAI,GAAG3G,SAAS,CAACC,MAAM,EAAE2G,GAAG,GAAG,IAAIhI,KAAK,CAAC+H,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IACtFD,GAAG,CAACC,IAAI,CAAC,GAAG7G,SAAS,CAAC6G,IAAI,CAAC;EAC7B;EAEA,OAAO,UAAUrB,KAAK,EAAE;IACtB,KAAK,IAAIsB,KAAK,GAAG9G,SAAS,CAACC,MAAM,EAAE8G,IAAI,GAAG,IAAInI,KAAK,CAACkI,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;MACjHD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,GAAGhH,SAAS,CAACgH,KAAK,CAAC;IACpC;IAEA,OAAOJ,GAAG,CAAChB,IAAI,CAAC,UAAUqB,EAAE,EAAE;MAC5B,IAAI,CAAC1B,oBAAoB,CAACC,KAAK,CAAC,IAAIyB,EAAE,EAAE;QACtCA,EAAE,CAACrH,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC4F,KAAK,CAAC,CAAC/B,MAAM,CAACsD,IAAI,CAAC,CAAC;MACxC;MAEA,OAAOxB,oBAAoB,CAACC,KAAK,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS0B,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,oBAAoB,IAAIV,MAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASW,uBAAuBA,CAAC/D,MAAM,EAAE;EAC9C,IAAImB,SAAS,CAACnB,MAAM,CAAC,EAAE;IACrB,IAAIgE,eAAe,GAAGhI,MAAM,CAACiI,OAAO,CAACjE,MAAM,CAAC,CAAC7D,MAAM,CAAC,UAAU+H,KAAK,EAAE;MACnE,IAAIC,KAAK,GAAGvG,cAAc,CAACsG,KAAK,EAAE,CAAC,CAAC;QAChCE,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC;QACnBE,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC;MAElB,IAAIG,EAAE,GAAG,IAAI;MAEb,IAAI,CAACC,UAAU,CAACH,QAAQ,CAAC,EAAE;QACzBI,OAAO,CAACC,IAAI,CAAC,YAAY,CAACpE,MAAM,CAAC+D,QAAQ,EAAE,wKAAwK,CAAC,CAAC;QACrNE,EAAE,GAAG,KAAK;MACZ;MAEA,IAAI,CAAC9I,KAAK,CAACE,OAAO,CAAC2I,GAAG,CAAC,IAAI,CAACA,GAAG,CAAC1C,KAAK,CAAC+C,KAAK,CAAC,EAAE;QAC5CF,OAAO,CAACC,IAAI,CAAC,YAAY,CAACpE,MAAM,CAAC+D,QAAQ,EAAE,oDAAoD,CAAC,CAAC;QACjGE,EAAE,GAAG,KAAK;MACZ;MAEA,OAAOA,EAAE;IACX,CAAC,CAAC,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;MAC9B,IAAIC,KAAK,GAAGlH,cAAc,CAACiH,KAAK,EAAE,CAAC,CAAC;QAChCT,QAAQ,GAAGU,KAAK,CAAC,CAAC,CAAC;QACnBT,GAAG,GAAGS,KAAK,CAAC,CAAC,CAAC;MAElB,OAAOrI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE3H,eAAe,CAAC,CAAC,CAAC,EAAEmH,QAAQ,EAAEC,GAAG,CAAC,CAAC;IACtF,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,OAAO,CAAC;MACN;MACAU,WAAW,EAAE,OAAO;MACpB/E,MAAM,EAAEgE;IACV,CAAC,CAAC;EACJ;EAEA,OAAOhE,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASgF,sBAAsBA,CAAChF,MAAM,EAAE;EAC7C,IAAImB,SAAS,CAACnB,MAAM,CAAC,EAAE;IACrB,OAAOhE,MAAM,CAACiI,OAAO,CAACjE,MAAM,CAAC,CAAC2E,MAAM,CAAC,UAAUM,CAAC,EAAEC,KAAK,EAAE;MACvD,IAAIC,KAAK,GAAGvH,cAAc,CAACsH,KAAK,EAAE,CAAC,CAAC;QAChCd,QAAQ,GAAGe,KAAK,CAAC,CAAC,CAAC;QACnBd,GAAG,GAAGc,KAAK,CAAC,CAAC,CAAC;MAElB,OAAO,EAAE,CAAC9E,MAAM,CAACvF,kBAAkB,CAACmK,CAAC,CAAC,EAAE,CAACb,QAAQ,CAAC,EAAEtJ,kBAAkB,CAACuJ,GAAG,CAAC,CAAC;IAC9E,CAAC,EAAE,EAAE,CAAC,CAAC;IAAA,CACNlI,MAAM,CAAC,UAAUiJ,CAAC,EAAE;MACnB,OAAOb,UAAU,CAACa,CAAC,CAAC,IAAIV,KAAK,CAACU,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC9E,IAAI,CAAC,GAAG,CAAC;EACd;EAEA,OAAOL,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASoF,OAAOA,CAACD,CAAC,EAAE;EACzB,OAAOA,CAAC,YAAYE,YAAY,KAAKF,CAAC,CAAC9G,IAAI,KAAK,YAAY,IAAI8G,CAAC,CAAC7E,IAAI,KAAK6E,CAAC,CAACG,SAAS,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACJ,CAAC,EAAE;EACjC,OAAOA,CAAC,YAAYE,YAAY,KAAKF,CAAC,CAAC9G,IAAI,KAAK,eAAe,IAAI8G,CAAC,CAAC7E,IAAI,KAAK6E,CAAC,CAACK,YAAY,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASlB,UAAUA,CAACa,CAAC,EAAE;EAC5B,OAAOA,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,eAAe,IAAI,gBAAgB,CAAC7G,IAAI,CAAC6G,CAAC,CAAC;AACrI;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASV,KAAKA,CAACU,CAAC,EAAE;EACvB,OAAO,aAAa,CAAC7G,IAAI,CAAC6G,CAAC,CAAC;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}