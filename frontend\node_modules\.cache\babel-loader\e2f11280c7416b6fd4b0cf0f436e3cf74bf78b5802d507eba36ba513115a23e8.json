{"ast": null, "code": "import * as React from 'react';\nimport { unstable_debounce as debounce, unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnsTotalWidthSelector } from '../columns';\nimport { gridDensityFactorSelector } from '../density';\nimport { useGridSelector } from '../../utils';\nimport { getVisibleRows } from '../../utils/useGridVisibleRows';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { calculatePinnedRowsHeight } from '../rows/gridRowsUtils';\nimport { getTotalHeaderHeight } from '../columns/gridColumnsUtils';\nconst isTestEnvironment = process.env.NODE_ENV === 'test';\nconst hasScroll = ({\n  content,\n  container,\n  scrollBarSize\n}) => {\n  const hasScrollXIfNoYScrollBar = content.width > container.width;\n  const hasScrollYIfNoXScrollBar = content.height > container.height;\n  let hasScrollX = false;\n  let hasScrollY = false;\n  if (hasScrollXIfNoYScrollBar || hasScrollYIfNoXScrollBar) {\n    hasScrollX = hasScrollXIfNoYScrollBar;\n    hasScrollY = content.height + (hasScrollX ? scrollBarSize : 0) > container.height;\n\n    // We recalculate the scroll x to consider the size of the y scrollbar.\n    if (hasScrollY) {\n      hasScrollX = content.width + scrollBarSize > container.width;\n    }\n  }\n  return {\n    hasScrollX,\n    hasScrollY\n  };\n};\nexport function useGridDimensions(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useResizeContainer');\n  const errorShown = React.useRef(false);\n  const rootDimensionsRef = React.useRef(null);\n  const fullDimensionsRef = React.useRef(null);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  const totalHeaderHeight = getTotalHeaderHeight(apiRef, props.columnHeaderHeight);\n  const updateGridDimensionsRef = React.useCallback(() => {\n    var _apiRef$current$rootE;\n    const rootElement = (_apiRef$current$rootE = apiRef.current.rootElementRef) == null ? void 0 : _apiRef$current$rootE.current;\n    const columnsTotalWidth = gridColumnsTotalWidthSelector(apiRef);\n    const pinnedRowsHeight = calculatePinnedRowsHeight(apiRef);\n    if (!rootDimensionsRef.current) {\n      return;\n    }\n    let scrollBarSize;\n    if (props.scrollbarSize != null) {\n      scrollBarSize = props.scrollbarSize;\n    } else if (!columnsTotalWidth || !rootElement) {\n      scrollBarSize = 0;\n    } else {\n      const doc = ownerDocument(rootElement);\n      const scrollDiv = doc.createElement('div');\n      scrollDiv.style.width = '99px';\n      scrollDiv.style.height = '99px';\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.overflow = 'scroll';\n      scrollDiv.className = 'scrollDiv';\n      rootElement.appendChild(scrollDiv);\n      scrollBarSize = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      rootElement.removeChild(scrollDiv);\n    }\n    let viewportOuterSize;\n    let hasScrollX;\n    let hasScrollY;\n    if (props.autoHeight) {\n      hasScrollY = false;\n      hasScrollX = Math.round(columnsTotalWidth) > Math.round(rootDimensionsRef.current.width);\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: rowsMeta.currentPageTotalHeight + (hasScrollX ? scrollBarSize : 0)\n      };\n    } else {\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: Math.max(rootDimensionsRef.current.height - totalHeaderHeight, 0)\n      };\n      const scrollInformation = hasScroll({\n        content: {\n          width: Math.round(columnsTotalWidth),\n          height: rowsMeta.currentPageTotalHeight\n        },\n        container: {\n          width: Math.round(viewportOuterSize.width),\n          height: viewportOuterSize.height - pinnedRowsHeight.top - pinnedRowsHeight.bottom\n        },\n        scrollBarSize\n      });\n      hasScrollY = scrollInformation.hasScrollY;\n      hasScrollX = scrollInformation.hasScrollX;\n    }\n    const viewportInnerSize = {\n      width: viewportOuterSize.width - (hasScrollY ? scrollBarSize : 0),\n      height: viewportOuterSize.height - (hasScrollX ? scrollBarSize : 0)\n    };\n    const newFullDimensions = {\n      viewportOuterSize,\n      viewportInnerSize,\n      hasScrollX,\n      hasScrollY,\n      scrollBarSize\n    };\n    const prevDimensions = fullDimensionsRef.current;\n    fullDimensionsRef.current = newFullDimensions;\n    if (newFullDimensions.viewportInnerSize.width !== (prevDimensions == null ? void 0 : prevDimensions.viewportInnerSize.width) || newFullDimensions.viewportInnerSize.height !== (prevDimensions == null ? void 0 : prevDimensions.viewportInnerSize.height)) {\n      apiRef.current.publishEvent('viewportInnerSizeChange', newFullDimensions.viewportInnerSize);\n    }\n  }, [apiRef, props.scrollbarSize, props.autoHeight, rowsMeta.currentPageTotalHeight, totalHeaderHeight]);\n  const [savedSize, setSavedSize] = React.useState();\n  const debouncedSetSavedSize = React.useMemo(() => debounce(setSavedSize, 60), []);\n  const previousSize = React.useRef();\n  useEnhancedEffect(() => {\n    if (savedSize) {\n      updateGridDimensionsRef();\n      apiRef.current.publishEvent('debouncedResize', rootDimensionsRef.current);\n    }\n  }, [apiRef, savedSize, updateGridDimensionsRef]);\n\n  // This is the function called by apiRef.current.resize()\n  const resize = React.useCallback(() => {\n    apiRef.current.computeSizeAndPublishResizeEvent();\n  }, [apiRef]);\n  const getRootDimensions = React.useCallback(() => fullDimensionsRef.current, []);\n  const getViewportPageSize = React.useCallback(() => {\n    const dimensions = apiRef.current.getRootDimensions();\n    if (!dimensions) {\n      return 0;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n\n    // TODO: Use a combination of scrollTop, dimensions.viewportInnerSize.height and rowsMeta.possitions\n    // to find out the maximum number of rows that can fit in the visible part of the grid\n    if (props.getRowHeight) {\n      const renderContext = apiRef.current.getRenderContext();\n      const viewportPageSize = renderContext.lastRowIndex - renderContext.firstRowIndex;\n      return Math.min(viewportPageSize - 1, currentPage.rows.length);\n    }\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    return Math.min(maximumPageSizeWithoutScrollBar, currentPage.rows.length);\n  }, [apiRef, props.pagination, props.paginationMode, props.getRowHeight, rowHeight]);\n  const computeSizeAndPublishResizeEvent = React.useCallback(() => {\n    var _apiRef$current$mainE, _previousSize$current, _previousSize$current2;\n    const mainEl = (_apiRef$current$mainE = apiRef.current.mainElementRef) == null ? void 0 : _apiRef$current$mainE.current;\n    if (!mainEl) {\n      return;\n    }\n    const win = ownerWindow(mainEl);\n    const computedStyle = win.getComputedStyle(mainEl);\n    const height = parseFloat(computedStyle.height) || 0;\n    const width = parseFloat(computedStyle.width) || 0;\n    const hasHeightChanged = height !== ((_previousSize$current = previousSize.current) == null ? void 0 : _previousSize$current.height);\n    const hasWidthChanged = width !== ((_previousSize$current2 = previousSize.current) == null ? void 0 : _previousSize$current2.width);\n    if (!previousSize.current || hasHeightChanged || hasWidthChanged) {\n      const size = {\n        width,\n        height\n      };\n      apiRef.current.publishEvent('resize', size);\n      previousSize.current = size;\n    }\n  }, [apiRef]);\n  const dimensionsApi = {\n    resize,\n    getRootDimensions\n  };\n  const dimensionsPrivateApi = {\n    getViewportPageSize,\n    updateGridDimensionsRef,\n    computeSizeAndPublishResizeEvent\n  };\n  useGridApiMethod(apiRef, dimensionsApi, 'public');\n  useGridApiMethod(apiRef, dimensionsPrivateApi, 'private');\n  const isFirstSizing = React.useRef(true);\n  const handleResize = React.useCallback(size => {\n    rootDimensionsRef.current = size;\n\n    // jsdom has no layout capabilities\n    const isJSDOM = /jsdom/.test(window.navigator.userAgent);\n    if (size.height === 0 && !errorShown.current && !props.autoHeight && !isJSDOM) {\n      logger.error(['The parent DOM element of the data grid has an empty height.', 'Please make sure that this element has an intrinsic height.', 'The grid displays with a height of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (size.width === 0 && !errorShown.current && !isJSDOM) {\n      logger.error(['The parent DOM element of the data grid has an empty width.', 'Please make sure that this element has an intrinsic width.', 'The grid displays with a width of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (isTestEnvironment) {\n      // We don't need to debounce the resize for tests.\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    if (isFirstSizing.current) {\n      // We want to initialize the grid dimensions as soon as possible to avoid flickering\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    debouncedSetSavedSize(size);\n  }, [props.autoHeight, debouncedSetSavedSize, logger]);\n  useEnhancedEffect(() => updateGridDimensionsRef(), [updateGridDimensionsRef]);\n  useGridApiOptionHandler(apiRef, 'sortedRowsSet', updateGridDimensionsRef);\n  useGridApiOptionHandler(apiRef, 'paginationModelChange', updateGridDimensionsRef);\n  useGridApiOptionHandler(apiRef, 'columnsChange', updateGridDimensionsRef);\n  useGridApiEventHandler(apiRef, 'resize', handleResize);\n  useGridApiOptionHandler(apiRef, 'debouncedResize', props.onResize);\n}", "map": {"version": 3, "names": ["React", "unstable_debounce", "debounce", "unstable_ownerDocument", "ownerDocument", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_ownerW<PERSON>ow", "ownerWindow", "useGridApiEventHandler", "useGridApiOptionHandler", "useGridApiMethod", "useGridLogger", "gridColumnsTotalWidthSelector", "gridDensityFactorSelector", "useGridSelector", "getVisibleRows", "gridRowsMetaSelector", "calculatePinnedRowsHeight", "getTotalHeaderHeight", "isTestEnvironment", "process", "env", "NODE_ENV", "hasScroll", "content", "container", "scrollBarSize", "hasScrollXIfNoYScrollBar", "width", "hasScrollYIfNoXScrollBar", "height", "hasScrollX", "hasScrollY", "useGridDimensions", "apiRef", "props", "logger", "errorShown", "useRef", "rootDimensionsRef", "fullDimensionsRef", "rowsMeta", "densityFactor", "rowHeight", "Math", "floor", "totalHeaderHeight", "columnHeaderHeight", "updateGridDimensionsRef", "useCallback", "_apiRef$current$rootE", "rootElement", "current", "rootElementRef", "columnsTotalWidth", "pinnedRowsHeight", "scrollbarSize", "doc", "scrollDiv", "createElement", "style", "position", "overflow", "className", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "viewportOuterSize", "autoHeight", "round", "currentPageTotalHeight", "max", "scrollInformation", "top", "bottom", "viewportInnerSize", "newFullDimensions", "prevDimensions", "publishEvent", "savedSize", "setSavedSize", "useState", "debouncedSetSavedSize", "useMemo", "previousSize", "resize", "computeSizeAndPublishResizeEvent", "getRootDimensions", "getViewportPageSize", "dimensions", "currentPage", "pagination", "paginationMode", "getRowHeight", "renderContext", "getRenderContext", "viewportPageSize", "lastRowIndex", "firstRowIndex", "min", "rows", "length", "maximumPageSizeWithoutScrollBar", "_apiRef$current$mainE", "_previousSize$current", "_previousSize$current2", "mainEl", "mainElementRef", "win", "computedStyle", "getComputedStyle", "parseFloat", "hasHeightChanged", "hasWidthChanged", "size", "dimensionsApi", "dimensionsPrivateApi", "isFirstSizing", "handleResize", "isJSDOM", "test", "window", "navigator", "userAgent", "error", "join", "onResize"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/features/dimensions/useGridDimensions.js"], "sourcesContent": ["import * as React from 'react';\nimport { unstable_debounce as debounce, unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnsTotalWidthSelector } from '../columns';\nimport { gridDensityFactorSelector } from '../density';\nimport { useGridSelector } from '../../utils';\nimport { getVisibleRows } from '../../utils/useGridVisibleRows';\nimport { gridRowsMetaSelector } from '../rows/gridRowsMetaSelector';\nimport { calculatePinnedRowsHeight } from '../rows/gridRowsUtils';\nimport { getTotalHeaderHeight } from '../columns/gridColumnsUtils';\nconst isTestEnvironment = process.env.NODE_ENV === 'test';\nconst hasScroll = ({\n  content,\n  container,\n  scrollBarSize\n}) => {\n  const hasScrollXIfNoYScrollBar = content.width > container.width;\n  const hasScrollYIfNoXScrollBar = content.height > container.height;\n  let hasScrollX = false;\n  let hasScrollY = false;\n  if (hasScrollXIfNoYScrollBar || hasScrollYIfNoXScrollBar) {\n    hasScrollX = hasScrollXIfNoYScrollBar;\n    hasScrollY = content.height + (hasScrollX ? scrollBarSize : 0) > container.height;\n\n    // We recalculate the scroll x to consider the size of the y scrollbar.\n    if (hasScrollY) {\n      hasScrollX = content.width + scrollBarSize > container.width;\n    }\n  }\n  return {\n    hasScrollX,\n    hasScrollY\n  };\n};\nexport function useGridDimensions(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useResizeContainer');\n  const errorShown = React.useRef(false);\n  const rootDimensionsRef = React.useRef(null);\n  const fullDimensionsRef = React.useRef(null);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  const totalHeaderHeight = getTotalHeaderHeight(apiRef, props.columnHeaderHeight);\n  const updateGridDimensionsRef = React.useCallback(() => {\n    var _apiRef$current$rootE;\n    const rootElement = (_apiRef$current$rootE = apiRef.current.rootElementRef) == null ? void 0 : _apiRef$current$rootE.current;\n    const columnsTotalWidth = gridColumnsTotalWidthSelector(apiRef);\n    const pinnedRowsHeight = calculatePinnedRowsHeight(apiRef);\n    if (!rootDimensionsRef.current) {\n      return;\n    }\n    let scrollBarSize;\n    if (props.scrollbarSize != null) {\n      scrollBarSize = props.scrollbarSize;\n    } else if (!columnsTotalWidth || !rootElement) {\n      scrollBarSize = 0;\n    } else {\n      const doc = ownerDocument(rootElement);\n      const scrollDiv = doc.createElement('div');\n      scrollDiv.style.width = '99px';\n      scrollDiv.style.height = '99px';\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.overflow = 'scroll';\n      scrollDiv.className = 'scrollDiv';\n      rootElement.appendChild(scrollDiv);\n      scrollBarSize = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      rootElement.removeChild(scrollDiv);\n    }\n    let viewportOuterSize;\n    let hasScrollX;\n    let hasScrollY;\n    if (props.autoHeight) {\n      hasScrollY = false;\n      hasScrollX = Math.round(columnsTotalWidth) > Math.round(rootDimensionsRef.current.width);\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: rowsMeta.currentPageTotalHeight + (hasScrollX ? scrollBarSize : 0)\n      };\n    } else {\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: Math.max(rootDimensionsRef.current.height - totalHeaderHeight, 0)\n      };\n      const scrollInformation = hasScroll({\n        content: {\n          width: Math.round(columnsTotalWidth),\n          height: rowsMeta.currentPageTotalHeight\n        },\n        container: {\n          width: Math.round(viewportOuterSize.width),\n          height: viewportOuterSize.height - pinnedRowsHeight.top - pinnedRowsHeight.bottom\n        },\n        scrollBarSize\n      });\n      hasScrollY = scrollInformation.hasScrollY;\n      hasScrollX = scrollInformation.hasScrollX;\n    }\n    const viewportInnerSize = {\n      width: viewportOuterSize.width - (hasScrollY ? scrollBarSize : 0),\n      height: viewportOuterSize.height - (hasScrollX ? scrollBarSize : 0)\n    };\n    const newFullDimensions = {\n      viewportOuterSize,\n      viewportInnerSize,\n      hasScrollX,\n      hasScrollY,\n      scrollBarSize\n    };\n    const prevDimensions = fullDimensionsRef.current;\n    fullDimensionsRef.current = newFullDimensions;\n    if (newFullDimensions.viewportInnerSize.width !== (prevDimensions == null ? void 0 : prevDimensions.viewportInnerSize.width) || newFullDimensions.viewportInnerSize.height !== (prevDimensions == null ? void 0 : prevDimensions.viewportInnerSize.height)) {\n      apiRef.current.publishEvent('viewportInnerSizeChange', newFullDimensions.viewportInnerSize);\n    }\n  }, [apiRef, props.scrollbarSize, props.autoHeight, rowsMeta.currentPageTotalHeight, totalHeaderHeight]);\n  const [savedSize, setSavedSize] = React.useState();\n  const debouncedSetSavedSize = React.useMemo(() => debounce(setSavedSize, 60), []);\n  const previousSize = React.useRef();\n  useEnhancedEffect(() => {\n    if (savedSize) {\n      updateGridDimensionsRef();\n      apiRef.current.publishEvent('debouncedResize', rootDimensionsRef.current);\n    }\n  }, [apiRef, savedSize, updateGridDimensionsRef]);\n\n  // This is the function called by apiRef.current.resize()\n  const resize = React.useCallback(() => {\n    apiRef.current.computeSizeAndPublishResizeEvent();\n  }, [apiRef]);\n  const getRootDimensions = React.useCallback(() => fullDimensionsRef.current, []);\n  const getViewportPageSize = React.useCallback(() => {\n    const dimensions = apiRef.current.getRootDimensions();\n    if (!dimensions) {\n      return 0;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n\n    // TODO: Use a combination of scrollTop, dimensions.viewportInnerSize.height and rowsMeta.possitions\n    // to find out the maximum number of rows that can fit in the visible part of the grid\n    if (props.getRowHeight) {\n      const renderContext = apiRef.current.getRenderContext();\n      const viewportPageSize = renderContext.lastRowIndex - renderContext.firstRowIndex;\n      return Math.min(viewportPageSize - 1, currentPage.rows.length);\n    }\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    return Math.min(maximumPageSizeWithoutScrollBar, currentPage.rows.length);\n  }, [apiRef, props.pagination, props.paginationMode, props.getRowHeight, rowHeight]);\n  const computeSizeAndPublishResizeEvent = React.useCallback(() => {\n    var _apiRef$current$mainE, _previousSize$current, _previousSize$current2;\n    const mainEl = (_apiRef$current$mainE = apiRef.current.mainElementRef) == null ? void 0 : _apiRef$current$mainE.current;\n    if (!mainEl) {\n      return;\n    }\n    const win = ownerWindow(mainEl);\n    const computedStyle = win.getComputedStyle(mainEl);\n    const height = parseFloat(computedStyle.height) || 0;\n    const width = parseFloat(computedStyle.width) || 0;\n    const hasHeightChanged = height !== ((_previousSize$current = previousSize.current) == null ? void 0 : _previousSize$current.height);\n    const hasWidthChanged = width !== ((_previousSize$current2 = previousSize.current) == null ? void 0 : _previousSize$current2.width);\n    if (!previousSize.current || hasHeightChanged || hasWidthChanged) {\n      const size = {\n        width,\n        height\n      };\n      apiRef.current.publishEvent('resize', size);\n      previousSize.current = size;\n    }\n  }, [apiRef]);\n  const dimensionsApi = {\n    resize,\n    getRootDimensions\n  };\n  const dimensionsPrivateApi = {\n    getViewportPageSize,\n    updateGridDimensionsRef,\n    computeSizeAndPublishResizeEvent\n  };\n  useGridApiMethod(apiRef, dimensionsApi, 'public');\n  useGridApiMethod(apiRef, dimensionsPrivateApi, 'private');\n  const isFirstSizing = React.useRef(true);\n  const handleResize = React.useCallback(size => {\n    rootDimensionsRef.current = size;\n\n    // jsdom has no layout capabilities\n    const isJSDOM = /jsdom/.test(window.navigator.userAgent);\n    if (size.height === 0 && !errorShown.current && !props.autoHeight && !isJSDOM) {\n      logger.error(['The parent DOM element of the data grid has an empty height.', 'Please make sure that this element has an intrinsic height.', 'The grid displays with a height of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (size.width === 0 && !errorShown.current && !isJSDOM) {\n      logger.error(['The parent DOM element of the data grid has an empty width.', 'Please make sure that this element has an intrinsic width.', 'The grid displays with a width of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (isTestEnvironment) {\n      // We don't need to debounce the resize for tests.\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    if (isFirstSizing.current) {\n      // We want to initialize the grid dimensions as soon as possible to avoid flickering\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    debouncedSetSavedSize(size);\n  }, [props.autoHeight, debouncedSetSavedSize, logger]);\n  useEnhancedEffect(() => updateGridDimensionsRef(), [updateGridDimensionsRef]);\n  useGridApiOptionHandler(apiRef, 'sortedRowsSet', updateGridDimensionsRef);\n  useGridApiOptionHandler(apiRef, 'paginationModelChange', updateGridDimensionsRef);\n  useGridApiOptionHandler(apiRef, 'columnsChange', updateGridDimensionsRef);\n  useGridApiEventHandler(apiRef, 'resize', handleResize);\n  useGridApiOptionHandler(apiRef, 'debouncedResize', props.onResize);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,IAAIC,QAAQ,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,oBAAoB,IAAIC,WAAW,QAAQ,YAAY;AACzL,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,oCAAoC;AACpG,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,6BAA6B,QAAQ,YAAY;AAC1D,SAASC,yBAAyB,QAAQ,YAAY;AACtD,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,yBAAyB,QAAQ,uBAAuB;AACjE,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;AACzD,MAAMC,SAAS,GAAGA,CAAC;EACjBC,OAAO;EACPC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,MAAMC,wBAAwB,GAAGH,OAAO,CAACI,KAAK,GAAGH,SAAS,CAACG,KAAK;EAChE,MAAMC,wBAAwB,GAAGL,OAAO,CAACM,MAAM,GAAGL,SAAS,CAACK,MAAM;EAClE,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIL,wBAAwB,IAAIE,wBAAwB,EAAE;IACxDE,UAAU,GAAGJ,wBAAwB;IACrCK,UAAU,GAAGR,OAAO,CAACM,MAAM,IAAIC,UAAU,GAAGL,aAAa,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACK,MAAM;;IAEjF;IACA,IAAIE,UAAU,EAAE;MACdD,UAAU,GAAGP,OAAO,CAACI,KAAK,GAAGF,aAAa,GAAGD,SAAS,CAACG,KAAK;IAC9D;EACF;EACA,OAAO;IACLG,UAAU;IACVC;EACF,CAAC;AACH,CAAC;AACD,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/C,MAAMC,MAAM,GAAGzB,aAAa,CAACuB,MAAM,EAAE,oBAAoB,CAAC;EAC1D,MAAMG,UAAU,GAAGtC,KAAK,CAACuC,MAAM,CAAC,KAAK,CAAC;EACtC,MAAMC,iBAAiB,GAAGxC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAME,iBAAiB,GAAGzC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMG,QAAQ,GAAG3B,eAAe,CAACoB,MAAM,EAAElB,oBAAoB,CAAC;EAC9D,MAAM0B,aAAa,GAAG5B,eAAe,CAACoB,MAAM,EAAErB,yBAAyB,CAAC;EACxE,MAAM8B,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACV,KAAK,CAACQ,SAAS,GAAGD,aAAa,CAAC;EAC7D,MAAMI,iBAAiB,GAAG5B,oBAAoB,CAACgB,MAAM,EAAEC,KAAK,CAACY,kBAAkB,CAAC;EAChF,MAAMC,uBAAuB,GAAGjD,KAAK,CAACkD,WAAW,CAAC,MAAM;IACtD,IAAIC,qBAAqB;IACzB,MAAMC,WAAW,GAAG,CAACD,qBAAqB,GAAGhB,MAAM,CAACkB,OAAO,CAACC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACE,OAAO;IAC5H,MAAME,iBAAiB,GAAG1C,6BAA6B,CAACsB,MAAM,CAAC;IAC/D,MAAMqB,gBAAgB,GAAGtC,yBAAyB,CAACiB,MAAM,CAAC;IAC1D,IAAI,CAACK,iBAAiB,CAACa,OAAO,EAAE;MAC9B;IACF;IACA,IAAI1B,aAAa;IACjB,IAAIS,KAAK,CAACqB,aAAa,IAAI,IAAI,EAAE;MAC/B9B,aAAa,GAAGS,KAAK,CAACqB,aAAa;IACrC,CAAC,MAAM,IAAI,CAACF,iBAAiB,IAAI,CAACH,WAAW,EAAE;MAC7CzB,aAAa,GAAG,CAAC;IACnB,CAAC,MAAM;MACL,MAAM+B,GAAG,GAAGtD,aAAa,CAACgD,WAAW,CAAC;MACtC,MAAMO,SAAS,GAAGD,GAAG,CAACE,aAAa,CAAC,KAAK,CAAC;MAC1CD,SAAS,CAACE,KAAK,CAAChC,KAAK,GAAG,MAAM;MAC9B8B,SAAS,CAACE,KAAK,CAAC9B,MAAM,GAAG,MAAM;MAC/B4B,SAAS,CAACE,KAAK,CAACC,QAAQ,GAAG,UAAU;MACrCH,SAAS,CAACE,KAAK,CAACE,QAAQ,GAAG,QAAQ;MACnCJ,SAAS,CAACK,SAAS,GAAG,WAAW;MACjCZ,WAAW,CAACa,WAAW,CAACN,SAAS,CAAC;MAClChC,aAAa,GAAGgC,SAAS,CAACO,WAAW,GAAGP,SAAS,CAACQ,WAAW;MAC7Df,WAAW,CAACgB,WAAW,CAACT,SAAS,CAAC;IACpC;IACA,IAAIU,iBAAiB;IACrB,IAAIrC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIG,KAAK,CAACkC,UAAU,EAAE;MACpBrC,UAAU,GAAG,KAAK;MAClBD,UAAU,GAAGa,IAAI,CAAC0B,KAAK,CAAChB,iBAAiB,CAAC,GAAGV,IAAI,CAAC0B,KAAK,CAAC/B,iBAAiB,CAACa,OAAO,CAACxB,KAAK,CAAC;MACxFwC,iBAAiB,GAAG;QAClBxC,KAAK,EAAEW,iBAAiB,CAACa,OAAO,CAACxB,KAAK;QACtCE,MAAM,EAAEW,QAAQ,CAAC8B,sBAAsB,IAAIxC,UAAU,GAAGL,aAAa,GAAG,CAAC;MAC3E,CAAC;IACH,CAAC,MAAM;MACL0C,iBAAiB,GAAG;QAClBxC,KAAK,EAAEW,iBAAiB,CAACa,OAAO,CAACxB,KAAK;QACtCE,MAAM,EAAEc,IAAI,CAAC4B,GAAG,CAACjC,iBAAiB,CAACa,OAAO,CAACtB,MAAM,GAAGgB,iBAAiB,EAAE,CAAC;MAC1E,CAAC;MACD,MAAM2B,iBAAiB,GAAGlD,SAAS,CAAC;QAClCC,OAAO,EAAE;UACPI,KAAK,EAAEgB,IAAI,CAAC0B,KAAK,CAAChB,iBAAiB,CAAC;UACpCxB,MAAM,EAAEW,QAAQ,CAAC8B;QACnB,CAAC;QACD9C,SAAS,EAAE;UACTG,KAAK,EAAEgB,IAAI,CAAC0B,KAAK,CAACF,iBAAiB,CAACxC,KAAK,CAAC;UAC1CE,MAAM,EAAEsC,iBAAiB,CAACtC,MAAM,GAAGyB,gBAAgB,CAACmB,GAAG,GAAGnB,gBAAgB,CAACoB;QAC7E,CAAC;QACDjD;MACF,CAAC,CAAC;MACFM,UAAU,GAAGyC,iBAAiB,CAACzC,UAAU;MACzCD,UAAU,GAAG0C,iBAAiB,CAAC1C,UAAU;IAC3C;IACA,MAAM6C,iBAAiB,GAAG;MACxBhD,KAAK,EAAEwC,iBAAiB,CAACxC,KAAK,IAAII,UAAU,GAAGN,aAAa,GAAG,CAAC,CAAC;MACjEI,MAAM,EAAEsC,iBAAiB,CAACtC,MAAM,IAAIC,UAAU,GAAGL,aAAa,GAAG,CAAC;IACpE,CAAC;IACD,MAAMmD,iBAAiB,GAAG;MACxBT,iBAAiB;MACjBQ,iBAAiB;MACjB7C,UAAU;MACVC,UAAU;MACVN;IACF,CAAC;IACD,MAAMoD,cAAc,GAAGtC,iBAAiB,CAACY,OAAO;IAChDZ,iBAAiB,CAACY,OAAO,GAAGyB,iBAAiB;IAC7C,IAAIA,iBAAiB,CAACD,iBAAiB,CAAChD,KAAK,MAAMkD,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACF,iBAAiB,CAAChD,KAAK,CAAC,IAAIiD,iBAAiB,CAACD,iBAAiB,CAAC9C,MAAM,MAAMgD,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACF,iBAAiB,CAAC9C,MAAM,CAAC,EAAE;MAC1PI,MAAM,CAACkB,OAAO,CAAC2B,YAAY,CAAC,yBAAyB,EAAEF,iBAAiB,CAACD,iBAAiB,CAAC;IAC7F;EACF,CAAC,EAAE,CAAC1C,MAAM,EAAEC,KAAK,CAACqB,aAAa,EAAErB,KAAK,CAACkC,UAAU,EAAE5B,QAAQ,CAAC8B,sBAAsB,EAAEzB,iBAAiB,CAAC,CAAC;EACvG,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGlF,KAAK,CAACmF,QAAQ,CAAC,CAAC;EAClD,MAAMC,qBAAqB,GAAGpF,KAAK,CAACqF,OAAO,CAAC,MAAMnF,QAAQ,CAACgF,YAAY,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;EACjF,MAAMI,YAAY,GAAGtF,KAAK,CAACuC,MAAM,CAAC,CAAC;EACnCjC,iBAAiB,CAAC,MAAM;IACtB,IAAI2E,SAAS,EAAE;MACbhC,uBAAuB,CAAC,CAAC;MACzBd,MAAM,CAACkB,OAAO,CAAC2B,YAAY,CAAC,iBAAiB,EAAExC,iBAAiB,CAACa,OAAO,CAAC;IAC3E;EACF,CAAC,EAAE,CAAClB,MAAM,EAAE8C,SAAS,EAAEhC,uBAAuB,CAAC,CAAC;;EAEhD;EACA,MAAMsC,MAAM,GAAGvF,KAAK,CAACkD,WAAW,CAAC,MAAM;IACrCf,MAAM,CAACkB,OAAO,CAACmC,gCAAgC,CAAC,CAAC;EACnD,CAAC,EAAE,CAACrD,MAAM,CAAC,CAAC;EACZ,MAAMsD,iBAAiB,GAAGzF,KAAK,CAACkD,WAAW,CAAC,MAAMT,iBAAiB,CAACY,OAAO,EAAE,EAAE,CAAC;EAChF,MAAMqC,mBAAmB,GAAG1F,KAAK,CAACkD,WAAW,CAAC,MAAM;IAClD,MAAMyC,UAAU,GAAGxD,MAAM,CAACkB,OAAO,CAACoC,iBAAiB,CAAC,CAAC;IACrD,IAAI,CAACE,UAAU,EAAE;MACf,OAAO,CAAC;IACV;IACA,MAAMC,WAAW,GAAG5E,cAAc,CAACmB,MAAM,EAAE;MACzC0D,UAAU,EAAEzD,KAAK,CAACyD,UAAU;MAC5BC,cAAc,EAAE1D,KAAK,CAAC0D;IACxB,CAAC,CAAC;;IAEF;IACA;IACA,IAAI1D,KAAK,CAAC2D,YAAY,EAAE;MACtB,MAAMC,aAAa,GAAG7D,MAAM,CAACkB,OAAO,CAAC4C,gBAAgB,CAAC,CAAC;MACvD,MAAMC,gBAAgB,GAAGF,aAAa,CAACG,YAAY,GAAGH,aAAa,CAACI,aAAa;MACjF,OAAOvD,IAAI,CAACwD,GAAG,CAACH,gBAAgB,GAAG,CAAC,EAAEN,WAAW,CAACU,IAAI,CAACC,MAAM,CAAC;IAChE;IACA,MAAMC,+BAA+B,GAAG3D,IAAI,CAACC,KAAK,CAAC6C,UAAU,CAACd,iBAAiB,CAAC9C,MAAM,GAAGa,SAAS,CAAC;IACnG,OAAOC,IAAI,CAACwD,GAAG,CAACG,+BAA+B,EAAEZ,WAAW,CAACU,IAAI,CAACC,MAAM,CAAC;EAC3E,CAAC,EAAE,CAACpE,MAAM,EAAEC,KAAK,CAACyD,UAAU,EAAEzD,KAAK,CAAC0D,cAAc,EAAE1D,KAAK,CAAC2D,YAAY,EAAEnD,SAAS,CAAC,CAAC;EACnF,MAAM4C,gCAAgC,GAAGxF,KAAK,CAACkD,WAAW,CAAC,MAAM;IAC/D,IAAIuD,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;IACxE,MAAMC,MAAM,GAAG,CAACH,qBAAqB,GAAGtE,MAAM,CAACkB,OAAO,CAACwD,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,qBAAqB,CAACpD,OAAO;IACvH,IAAI,CAACuD,MAAM,EAAE;MACX;IACF;IACA,MAAME,GAAG,GAAGtG,WAAW,CAACoG,MAAM,CAAC;IAC/B,MAAMG,aAAa,GAAGD,GAAG,CAACE,gBAAgB,CAACJ,MAAM,CAAC;IAClD,MAAM7E,MAAM,GAAGkF,UAAU,CAACF,aAAa,CAAChF,MAAM,CAAC,IAAI,CAAC;IACpD,MAAMF,KAAK,GAAGoF,UAAU,CAACF,aAAa,CAAClF,KAAK,CAAC,IAAI,CAAC;IAClD,MAAMqF,gBAAgB,GAAGnF,MAAM,MAAM,CAAC2E,qBAAqB,GAAGpB,YAAY,CAACjC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqD,qBAAqB,CAAC3E,MAAM,CAAC;IACpI,MAAMoF,eAAe,GAAGtF,KAAK,MAAM,CAAC8E,sBAAsB,GAAGrB,YAAY,CAACjC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsD,sBAAsB,CAAC9E,KAAK,CAAC;IACnI,IAAI,CAACyD,YAAY,CAACjC,OAAO,IAAI6D,gBAAgB,IAAIC,eAAe,EAAE;MAChE,MAAMC,IAAI,GAAG;QACXvF,KAAK;QACLE;MACF,CAAC;MACDI,MAAM,CAACkB,OAAO,CAAC2B,YAAY,CAAC,QAAQ,EAAEoC,IAAI,CAAC;MAC3C9B,YAAY,CAACjC,OAAO,GAAG+D,IAAI;IAC7B;EACF,CAAC,EAAE,CAACjF,MAAM,CAAC,CAAC;EACZ,MAAMkF,aAAa,GAAG;IACpB9B,MAAM;IACNE;EACF,CAAC;EACD,MAAM6B,oBAAoB,GAAG;IAC3B5B,mBAAmB;IACnBzC,uBAAuB;IACvBuC;EACF,CAAC;EACD7E,gBAAgB,CAACwB,MAAM,EAAEkF,aAAa,EAAE,QAAQ,CAAC;EACjD1G,gBAAgB,CAACwB,MAAM,EAAEmF,oBAAoB,EAAE,SAAS,CAAC;EACzD,MAAMC,aAAa,GAAGvH,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMiF,YAAY,GAAGxH,KAAK,CAACkD,WAAW,CAACkE,IAAI,IAAI;IAC7C5E,iBAAiB,CAACa,OAAO,GAAG+D,IAAI;;IAEhC;IACA,MAAMK,OAAO,GAAG,OAAO,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,SAAS,CAAC;IACxD,IAAIT,IAAI,CAACrF,MAAM,KAAK,CAAC,IAAI,CAACO,UAAU,CAACe,OAAO,IAAI,CAACjB,KAAK,CAACkC,UAAU,IAAI,CAACmD,OAAO,EAAE;MAC7EpF,MAAM,CAACyF,KAAK,CAAC,CAAC,8DAA8D,EAAE,6DAA6D,EAAE,yCAAyC,EAAE,EAAE,EAAE,4DAA4D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrQzF,UAAU,CAACe,OAAO,GAAG,IAAI;IAC3B;IACA,IAAI+D,IAAI,CAACvF,KAAK,KAAK,CAAC,IAAI,CAACS,UAAU,CAACe,OAAO,IAAI,CAACoE,OAAO,EAAE;MACvDpF,MAAM,CAACyF,KAAK,CAAC,CAAC,6DAA6D,EAAE,4DAA4D,EAAE,wCAAwC,EAAE,EAAE,EAAE,4DAA4D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAClQzF,UAAU,CAACe,OAAO,GAAG,IAAI;IAC3B;IACA,IAAIjC,iBAAiB,EAAE;MACrB;MACA8D,YAAY,CAACkC,IAAI,CAAC;MAClBG,aAAa,CAAClE,OAAO,GAAG,KAAK;MAC7B;IACF;IACA,IAAIkE,aAAa,CAAClE,OAAO,EAAE;MACzB;MACA6B,YAAY,CAACkC,IAAI,CAAC;MAClBG,aAAa,CAAClE,OAAO,GAAG,KAAK;MAC7B;IACF;IACA+B,qBAAqB,CAACgC,IAAI,CAAC;EAC7B,CAAC,EAAE,CAAChF,KAAK,CAACkC,UAAU,EAAEc,qBAAqB,EAAE/C,MAAM,CAAC,CAAC;EACrD/B,iBAAiB,CAAC,MAAM2C,uBAAuB,CAAC,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;EAC7EvC,uBAAuB,CAACyB,MAAM,EAAE,eAAe,EAAEc,uBAAuB,CAAC;EACzEvC,uBAAuB,CAACyB,MAAM,EAAE,uBAAuB,EAAEc,uBAAuB,CAAC;EACjFvC,uBAAuB,CAACyB,MAAM,EAAE,eAAe,EAAEc,uBAAuB,CAAC;EACzExC,sBAAsB,CAAC0B,MAAM,EAAE,QAAQ,EAAEqF,YAAY,CAAC;EACtD9G,uBAAuB,CAACyB,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAAC4F,QAAQ,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}