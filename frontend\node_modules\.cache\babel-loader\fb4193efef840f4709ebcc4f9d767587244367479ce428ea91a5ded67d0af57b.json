{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemAvatar', slot);\n}\nconst listItemAvatarClasses = generateUtilityClasses('MuiListItemAvatar', ['root', 'alignItemsFlexStart']);\nexport default listItemAvatarClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getListItemAvatarUtilityClass", "slot", "listItemAvatarClasses"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/ListItemAvatar/listItemAvatarClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemAvatar', slot);\n}\nconst listItemAvatarClasses = generateUtilityClasses('MuiListItemAvatar', ['root', 'alignItemsFlexStart']);\nexport default listItemAvatarClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;AAC1G,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}