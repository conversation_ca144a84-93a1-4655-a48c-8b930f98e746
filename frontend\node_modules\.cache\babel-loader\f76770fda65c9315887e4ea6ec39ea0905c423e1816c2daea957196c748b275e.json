{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from './gridColumnsSelector';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nimport { hydrateColumnsWidth, createColumnsState, mergeColumnsState, COLUMNS_DIMENSION_PROPERTIES } from './gridColumnsUtils';\nimport { GridPreferencePanelsValue } from '../preferencesPanel';\nimport { getGridDefaultColumnTypes } from '../../../colDef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultColumnTypes = getGridDefaultColumnTypes();\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  var _props$initialState, _ref, _props$columnVisibili, _props$initialState2;\n  const columnsState = createColumnsState({\n    apiRef,\n    columnTypes: defaultColumnTypes,\n    columnsToUpsert: props.columns,\n    initialState: (_props$initialState = props.initialState) == null ? void 0 : _props$initialState.columns,\n    columnVisibilityModel: (_ref = (_props$columnVisibili = props.columnVisibilityModel) != null ? _props$columnVisibili : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.columns) == null ? void 0 : _props$initialState2.columnVisibilityModel) != null ? _ref : {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  var _props$initialState4, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const columnTypes = defaultColumnTypes;\n  const previousColumnsProp = React.useRef(props.columns);\n  const previousColumnTypesProp = React.useRef(columnTypes);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnTypes,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, columnTypes]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState, columnTypes]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    var _columnVisibilityMode;\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = (_columnVisibilityMode = columnVisibilityModel[field]) != null ? _columnVisibilityMode : true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    var _apiRef$current$getRo, _apiRef$current$getRo2;\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), (_apiRef$current$getRo = (_apiRef$current$getRo2 = apiRef.current.getRootDimensions()) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState$c, _props$initialState3;\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys((_props$initialState$c = (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.columns) == null ? void 0 : _props$initialState3.columnVisibilityModel) != null ? _props$initialState$c : {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, (_props$initialState4 = props.initialState) == null ? void 0 : _props$initialState4.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const columnVisibilityModelToImport = (_context$stateToResto = context.stateToRestore.columns) == null ? void 0 : _context$stateToResto.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef, columnTypes]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      var _props$slotProps;\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), viewportInnerSize.width));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState, columnTypes]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /**\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns && previousColumnTypesProp.current === columnTypes) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    previousColumnTypesProp.current = columnTypes;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, columnTypes]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "gridColumnFieldsSelector", "gridColumnDefinitionsSelector", "gridColumnLookupSelector", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnPositionsSelector", "GridSignature", "useGridApiEventHandler", "useGridRegisterPipeProcessor", "useGridRegisterPipeApplier", "hydrateColumnsWidth", "createColumnsState", "mergeColumnsState", "COLUMNS_DIMENSION_PROPERTIES", "GridPreferencePanelsValue", "getGridDefaultColumnTypes", "jsx", "_jsx", "defaultColumnTypes", "columnsStateInitializer", "state", "props", "apiRef", "_props$initialState", "_ref", "_props$columnVisibili", "_props$initialState2", "columnsState", "columnTypes", "columnsToUpsert", "columns", "initialState", "columnVisibilityModel", "keepOnlyColumnsToUpsert", "useGridColumns", "_props$initialState4", "_props$slotProps2", "logger", "previousColumnsProp", "useRef", "previousColumnTypesProp", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onColumnVisibilityModelChange", "stateSelector", "changeEvent", "setGridColumnsState", "useCallback", "debug", "setState", "forceUpdate", "publishEvent", "orderedFields", "getColumn", "field", "getAllColumns", "getVisibleColumns", "getColumnIndex", "useVisibleColumns", "findIndex", "col", "getColumnPosition", "index", "setColumnVisibilityModel", "model", "currentModel", "undefined", "updateColumns", "setColumnVisibility", "isVisible", "_columnVisibilityMode", "isCurrentlyVisible", "newModel", "getColumnIndexRelativeToVisibleColumns", "allColumns", "setColumnIndex", "targetIndexPosition", "oldIndexPosition", "updatedColumns", "fieldRemoved", "splice", "params", "column", "targetIndex", "oldIndex", "setColumn<PERSON><PERSON><PERSON>", "width", "_apiRef$current$getRo", "_apiRef$current$getRo2", "lookup", "newColumn", "hasBeenResized", "getRootDimensions", "viewportInnerSize", "element", "getColumnHeaderElement", "colDef", "columnApi", "columnReorderApi", "signature", "DataGrid", "stateExportPreProcessing", "prevState", "context", "_props$initialState$c", "_props$initialState3", "columnsStateToExport", "columnVisibilityModelToExport", "shouldExportColumnVisibilityModel", "exportOnlyDirtyModels", "Object", "keys", "length", "dimensions", "for<PERSON>ach", "colDefDimensions", "propertyName", "propertyValue", "Infinity", "stateRestorePreProcessing", "_context$stateToResto", "columnVisibilityModelToImport", "stateToRestore", "preferencePanelPreProcessing", "initialValue", "value", "_props$slotProps", "ColumnsPanel", "slots", "columnsPanel", "slotProps", "addColumnMenuItems", "columnMenuItems", "disableColumnSelector", "prevInnerWidth", "handleGridSizeChange", "hydrateColumns", "info", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columns/useGridColumns.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from './gridColumnsSelector';\nimport { GridSignature, useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from '../../core/pipeProcessing';\nimport { hydrateColumnsWidth, createColumnsState, mergeColumnsState, COLUMNS_DIMENSION_PROPERTIES } from './gridColumnsUtils';\nimport { GridPreferencePanelsValue } from '../preferencesPanel';\nimport { getGridDefaultColumnTypes } from '../../../colDef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultColumnTypes = getGridDefaultColumnTypes();\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  var _props$initialState, _ref, _props$columnVisibili, _props$initialState2;\n  const columnsState = createColumnsState({\n    apiRef,\n    columnTypes: defaultColumnTypes,\n    columnsToUpsert: props.columns,\n    initialState: (_props$initialState = props.initialState) == null ? void 0 : _props$initialState.columns,\n    columnVisibilityModel: (_ref = (_props$columnVisibili = props.columnVisibilityModel) != null ? _props$columnVisibili : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.columns) == null ? void 0 : _props$initialState2.columnVisibilityModel) != null ? _ref : {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  var _props$initialState4, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const columnTypes = defaultColumnTypes;\n  const previousColumnsProp = React.useRef(props.columns);\n  const previousColumnTypesProp = React.useRef(columnTypes);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.forceUpdate();\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnTypes,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, columnTypes]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState, columnTypes]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    var _columnVisibilityMode;\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = (_columnVisibilityMode = columnVisibilityModel[field]) != null ? _columnVisibilityMode : true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    var _apiRef$current$getRo, _apiRef$current$getRo2;\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), (_apiRef$current$getRo = (_apiRef$current$getRo2 = apiRef.current.getRootDimensions()) == null ? void 0 : _apiRef$current$getRo2.viewportInnerSize.width) != null ? _apiRef$current$getRo : 0));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState$c, _props$initialState3;\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys((_props$initialState$c = (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.columns) == null ? void 0 : _props$initialState3.columnVisibilityModel) != null ? _props$initialState$c : {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, (_props$initialState4 = props.initialState) == null ? void 0 : _props$initialState4.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const columnVisibilityModelToImport = (_context$stateToResto = context.stateToRestore.columns) == null ? void 0 : _context$stateToResto.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef, columnTypes]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      var _props$slotProps;\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), viewportInnerSize.width));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState, columnTypes]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /**\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns && previousColumnTypesProp.current === columnTypes) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnTypes,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    previousColumnTypesProp.current = columnTypes;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, columnTypes]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,wBAAwB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEC,iCAAiC,EAAEC,oCAAoC,EAAEC,2BAA2B,QAAQ,uBAAuB;AACzP,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,oCAAoC;AAC1F,SAASC,4BAA4B,EAAEC,0BAA0B,QAAQ,2BAA2B;AACpG,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,4BAA4B,QAAQ,oBAAoB;AAC7H,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,yBAAyB,QAAQ,iBAAiB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGH,yBAAyB,CAAC,CAAC;AACtD,OAAO,MAAMI,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC/D,IAAIC,mBAAmB,EAAEC,IAAI,EAAEC,qBAAqB,EAAEC,oBAAoB;EAC1E,MAAMC,YAAY,GAAGhB,kBAAkB,CAAC;IACtCW,MAAM;IACNM,WAAW,EAAEV,kBAAkB;IAC/BW,eAAe,EAAER,KAAK,CAACS,OAAO;IAC9BC,YAAY,EAAE,CAACR,mBAAmB,GAAGF,KAAK,CAACU,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,mBAAmB,CAACO,OAAO;IACvGE,qBAAqB,EAAE,CAACR,IAAI,GAAG,CAACC,qBAAqB,GAAGJ,KAAK,CAACW,qBAAqB,KAAK,IAAI,GAAGP,qBAAqB,GAAG,CAACC,oBAAoB,GAAGL,KAAK,CAACU,YAAY,KAAK,IAAI,IAAI,CAACL,oBAAoB,GAAGA,oBAAoB,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,oBAAoB,CAACM,qBAAqB,KAAK,IAAI,GAAGR,IAAI,GAAG,CAAC,CAAC;IACtTS,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAOtC,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACzBU,OAAO,EAAEH;EACX,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,cAAcA,CAACZ,MAAM,EAAED,KAAK,EAAE;EAC5C,IAAIc,oBAAoB,EAAEC,iBAAiB;EAC3C,MAAMC,MAAM,GAAGvC,aAAa,CAACwB,MAAM,EAAE,gBAAgB,CAAC;EACtD,MAAMM,WAAW,GAAGV,kBAAkB;EACtC,MAAMoB,mBAAmB,GAAG1C,KAAK,CAAC2C,MAAM,CAAClB,KAAK,CAACS,OAAO,CAAC;EACvD,MAAMU,uBAAuB,GAAG5C,KAAK,CAAC2C,MAAM,CAACX,WAAW,CAAC;EACzDN,MAAM,CAACmB,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAEvB,KAAK,CAACW,qBAAqB;IACtCa,YAAY,EAAExB,KAAK,CAACyB,6BAA6B;IACjDC,aAAa,EAAE5C,iCAAiC;IAChD6C,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAGrD,KAAK,CAACsD,WAAW,CAACvB,YAAY,IAAI;IAC5DU,MAAM,CAACc,KAAK,CAAC,yBAAyB,CAAC;IACvC7B,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAACxC,iBAAiB,CAACe,YAAY,CAAC,CAAC;IACxDL,MAAM,CAACmB,OAAO,CAACY,WAAW,CAAC,CAAC;IAC5B/B,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,eAAe,EAAE3B,YAAY,CAAC4B,aAAa,CAAC;EAC1E,CAAC,EAAE,CAAClB,MAAM,EAAEf,MAAM,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAMkC,SAAS,GAAG5D,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAIxD,wBAAwB,CAACqB,MAAM,CAAC,CAACmC,KAAK,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EAC/F,MAAMoC,aAAa,GAAG9D,KAAK,CAACsD,WAAW,CAAC,MAAMlD,6BAA6B,CAACsB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC9F,MAAMqC,iBAAiB,GAAG/D,KAAK,CAACsD,WAAW,CAAC,MAAM9C,oCAAoC,CAACkB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACzG,MAAMsC,cAAc,GAAGhE,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEI,iBAAiB,GAAG,IAAI,KAAK;IAC5E,MAAM/B,OAAO,GAAG+B,iBAAiB,GAAGzD,oCAAoC,CAACkB,MAAM,CAAC,GAAGtB,6BAA6B,CAACsB,MAAM,CAAC;IACxH,OAAOQ,OAAO,CAACgC,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACN,KAAK,KAAKA,KAAK,CAAC;EACtD,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZ,MAAM0C,iBAAiB,GAAGpE,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAI;IACnD,MAAMQ,KAAK,GAAGL,cAAc,CAACH,KAAK,CAAC;IACnC,OAAOpD,2BAA2B,CAACiB,MAAM,CAAC,CAAC2C,KAAK,CAAC;EACnD,CAAC,EAAE,CAAC3C,MAAM,EAAEsC,cAAc,CAAC,CAAC;EAC5B,MAAMM,wBAAwB,GAAGtE,KAAK,CAACsD,WAAW,CAACiB,KAAK,IAAI;IAC1D,MAAMC,YAAY,GAAGjE,iCAAiC,CAACmB,MAAM,CAAC;IAC9D,IAAI8C,YAAY,KAAKD,KAAK,EAAE;MAC1B7C,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAAChC,KAAK,IAAIzB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;QACnDU,OAAO,EAAEnB,kBAAkB,CAAC;UAC1BW,MAAM;UACNM,WAAW;UACXC,eAAe,EAAE,EAAE;UACnBE,YAAY,EAAEsC,SAAS;UACvBrC,qBAAqB,EAAEmC,KAAK;UAC5BlC,uBAAuB,EAAE;QAC3B,CAAC;MACH,CAAC,CAAC,CAAC;MACHX,MAAM,CAACmB,OAAO,CAACY,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC/B,MAAM,EAAEM,WAAW,CAAC,CAAC;EACzB,MAAM0C,aAAa,GAAG1E,KAAK,CAACsD,WAAW,CAACpB,OAAO,IAAI;IACjD,MAAMH,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAEC,OAAO;MACxBC,YAAY,EAAEsC,SAAS;MACvBpC,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFgB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAE2B,mBAAmB,EAAErB,WAAW,CAAC,CAAC;EAC9C,MAAM2C,mBAAmB,GAAG3E,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEe,SAAS,KAAK;IAClE,IAAIC,qBAAqB;IACzB,MAAMzC,qBAAqB,GAAG7B,iCAAiC,CAACmB,MAAM,CAAC;IACvE,MAAMoD,kBAAkB,GAAG,CAACD,qBAAqB,GAAGzC,qBAAqB,CAACyB,KAAK,CAAC,KAAK,IAAI,GAAGgB,qBAAqB,GAAG,IAAI;IACxH,IAAID,SAAS,KAAKE,kBAAkB,EAAE;MACpC,MAAMC,QAAQ,GAAGhF,QAAQ,CAAC,CAAC,CAAC,EAAEqC,qBAAqB,EAAE;QACnD,CAACyB,KAAK,GAAGe;MACX,CAAC,CAAC;MACFlD,MAAM,CAACmB,OAAO,CAACyB,wBAAwB,CAACS,QAAQ,CAAC;IACnD;EACF,CAAC,EAAE,CAACrD,MAAM,CAAC,CAAC;EACZ,MAAMsD,sCAAsC,GAAGhF,KAAK,CAACsD,WAAW,CAACO,KAAK,IAAI;IACxE,MAAMoB,UAAU,GAAG9E,wBAAwB,CAACuB,MAAM,CAAC;IACnD,OAAOuD,UAAU,CAACf,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKN,KAAK,CAAC;EACnD,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZ,MAAMwD,cAAc,GAAGlF,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEsB,mBAAmB,KAAK;IACvE,MAAMF,UAAU,GAAG9E,wBAAwB,CAACuB,MAAM,CAAC;IACnD,MAAM0D,gBAAgB,GAAGJ,sCAAsC,CAACnB,KAAK,CAAC;IACtE,IAAIuB,gBAAgB,KAAKD,mBAAmB,EAAE;MAC5C;IACF;IACA1C,MAAM,CAACc,KAAK,CAAC,iBAAiBM,KAAK,aAAasB,mBAAmB,EAAE,CAAC;IACtE,MAAME,cAAc,GAAG,CAAC,GAAGJ,UAAU,CAAC;IACtC,MAAMK,YAAY,GAAGD,cAAc,CAACE,MAAM,CAACH,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClEC,cAAc,CAACE,MAAM,CAACJ,mBAAmB,EAAE,CAAC,EAAEG,YAAY,CAAC;IAC3DjC,mBAAmB,CAACtD,QAAQ,CAAC,CAAC,CAAC,EAAEO,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC,EAAE;MAC/EmC,aAAa,EAAE0B;IACjB,CAAC,CAAC,CAAC;IACH,MAAMG,MAAM,GAAG;MACbC,MAAM,EAAE/D,MAAM,CAACmB,OAAO,CAACe,SAAS,CAACC,KAAK,CAAC;MACvC6B,WAAW,EAAEhE,MAAM,CAACmB,OAAO,CAACmC,sCAAsC,CAACnB,KAAK,CAAC;MACzE8B,QAAQ,EAAEP;IACZ,CAAC;IACD1D,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAE8B,MAAM,CAAC;EAC1D,CAAC,EAAE,CAAC9D,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,EAAE2B,sCAAsC,CAAC,CAAC;EACjF,MAAMY,cAAc,GAAG5F,KAAK,CAACsD,WAAW,CAAC,CAACO,KAAK,EAAEgC,KAAK,KAAK;IACzD,IAAIC,qBAAqB,EAAEC,sBAAsB;IACjDtD,MAAM,CAACc,KAAK,CAAC,mBAAmBM,KAAK,aAAagC,KAAK,EAAE,CAAC;IAC1D,MAAM9D,YAAY,GAAGzB,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC;IACnE,MAAMiE,MAAM,GAAG1D,YAAY,CAACiE,MAAM,CAACnC,KAAK,CAAC;IACzC,MAAMoC,SAAS,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAE0F,MAAM,EAAE;MACrCI,KAAK;MACLK,cAAc,EAAE;IAClB,CAAC,CAAC;IACF7C,mBAAmB,CAACvC,mBAAmB,CAACf,QAAQ,CAAC,CAAC,CAAC,EAAEgC,YAAY,EAAE;MACjEiE,MAAM,EAAEjG,QAAQ,CAAC,CAAC,CAAC,EAAEgC,YAAY,CAACiE,MAAM,EAAE;QACxC,CAACnC,KAAK,GAAGoC;MACX,CAAC;IACH,CAAC,CAAC,EAAE,CAACH,qBAAqB,GAAG,CAACC,sBAAsB,GAAGrE,MAAM,CAACmB,OAAO,CAACsD,iBAAiB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,sBAAsB,CAACK,iBAAiB,CAACP,KAAK,KAAK,IAAI,GAAGC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACnMpE,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAE;MAC/C2C,OAAO,EAAE3E,MAAM,CAACmB,OAAO,CAACyD,sBAAsB,CAACzC,KAAK,CAAC;MACrD0C,MAAM,EAAEN,SAAS;MACjBJ;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnE,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,CAAC,CAAC;EACzC,MAAMmD,SAAS,GAAG;IAChB5C,SAAS;IACTE,aAAa;IACbE,cAAc;IACdI,iBAAiB;IACjBL,iBAAiB;IACjBiB,sCAAsC;IACtCN,aAAa;IACbJ,wBAAwB;IACxBK,mBAAmB;IACnBiB;EACF,CAAC;EACD,MAAMa,gBAAgB,GAAG;IACvBvB;EACF,CAAC;EACDjF,gBAAgB,CAACyB,MAAM,EAAE8E,SAAS,EAAE,QAAQ,CAAC;EAC7CvG,gBAAgB,CAACyB,MAAM,EAAE+E,gBAAgB,EAAEhF,KAAK,CAACiF,SAAS,KAAKhG,aAAa,CAACiG,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAE7G;AACF;AACA;EACE,MAAMC,wBAAwB,GAAG5G,KAAK,CAACsD,WAAW,CAAC,CAACuD,SAAS,EAAEC,OAAO,KAAK;IACzE,IAAIC,qBAAqB,EAAEC,oBAAoB;IAC/C,MAAMC,oBAAoB,GAAG,CAAC,CAAC;IAC/B,MAAMC,6BAA6B,GAAG3G,iCAAiC,CAACmB,MAAM,CAAC;IAC/E,MAAMyF,iCAAiC;IACvC;IACA,CAACL,OAAO,CAACM,qBAAqB;IAC9B;IACA3F,KAAK,CAACW,qBAAqB,IAAI,IAAI;IACnC;IACA;IACAiF,MAAM,CAACC,IAAI,CAAC,CAACP,qBAAqB,GAAG,CAACC,oBAAoB,GAAGvF,KAAK,CAACU,YAAY,KAAK,IAAI,IAAI,CAAC6E,oBAAoB,GAAGA,oBAAoB,CAAC9E,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8E,oBAAoB,CAAC5E,qBAAqB,KAAK,IAAI,GAAG2E,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAACQ,MAAM,GAAG,CAAC;IACjQ;IACAF,MAAM,CAACC,IAAI,CAACJ,6BAA6B,CAAC,CAACK,MAAM,GAAG,CAAC;IACrD,IAAIJ,iCAAiC,EAAE;MACrCF,oBAAoB,CAAC7E,qBAAqB,GAAG8E,6BAA6B;IAC5E;IACAD,oBAAoB,CAACtD,aAAa,GAAGxD,wBAAwB,CAACuB,MAAM,CAAC;IACrE,MAAMQ,OAAO,GAAG9B,6BAA6B,CAACsB,MAAM,CAAC;IACrD,MAAM8F,UAAU,GAAG,CAAC,CAAC;IACrBtF,OAAO,CAACuF,OAAO,CAAClB,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACL,cAAc,EAAE;QACzB,MAAMwB,gBAAgB,GAAG,CAAC,CAAC;QAC3BzG,4BAA4B,CAACwG,OAAO,CAACE,YAAY,IAAI;UACnD,IAAIC,aAAa,GAAGrB,MAAM,CAACoB,YAAY,CAAC;UACxC,IAAIC,aAAa,KAAKC,QAAQ,EAAE;YAC9BD,aAAa,GAAG,CAAC,CAAC;UACpB;UACAF,gBAAgB,CAACC,YAAY,CAAC,GAAGC,aAAa;QAChD,CAAC,CAAC;QACFJ,UAAU,CAACjB,MAAM,CAAC1C,KAAK,CAAC,GAAG6D,gBAAgB;MAC7C;IACF,CAAC,CAAC;IACF,IAAIL,MAAM,CAACC,IAAI,CAACE,UAAU,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MACtCN,oBAAoB,CAACO,UAAU,GAAGA,UAAU;IAC9C;IACA,OAAOzH,QAAQ,CAAC,CAAC,CAAC,EAAE8G,SAAS,EAAE;MAC7B3E,OAAO,EAAE+E;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvF,MAAM,EAAED,KAAK,CAACW,qBAAqB,EAAE,CAACG,oBAAoB,GAAGd,KAAK,CAACU,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,oBAAoB,CAACL,OAAO,CAAC,CAAC;EACtI,MAAM4F,yBAAyB,GAAG9H,KAAK,CAACsD,WAAW,CAAC,CAACkC,MAAM,EAAEsB,OAAO,KAAK;IACvE,IAAIiB,qBAAqB;IACzB,MAAMC,6BAA6B,GAAG,CAACD,qBAAqB,GAAGjB,OAAO,CAACmB,cAAc,CAAC/F,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6F,qBAAqB,CAAC3F,qBAAqB;IAC7J,MAAMD,YAAY,GAAG2E,OAAO,CAACmB,cAAc,CAAC/F,OAAO;IACnD,IAAI8F,6BAA6B,IAAI,IAAI,IAAI7F,YAAY,IAAI,IAAI,EAAE;MACjE,OAAOqD,MAAM;IACf;IACA,MAAMzD,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAE,EAAE;MACnBE,YAAY;MACZC,qBAAqB,EAAE4F,6BAA6B;MACpD3F,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFX,MAAM,CAACmB,OAAO,CAACW,QAAQ,CAACxC,iBAAiB,CAACe,YAAY,CAAC,CAAC;IACxD,IAAII,YAAY,IAAI,IAAI,EAAE;MACxBT,MAAM,CAACmB,OAAO,CAACa,YAAY,CAAC,eAAe,EAAE3B,YAAY,CAAC4B,aAAa,CAAC;IAC1E;IACA,OAAO6B,MAAM;EACf,CAAC,EAAE,CAAC9D,MAAM,EAAEM,WAAW,CAAC,CAAC;EACzB,MAAMkG,4BAA4B,GAAGlI,KAAK,CAACsD,WAAW,CAAC,CAAC6E,YAAY,EAAEC,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKlH,yBAAyB,CAACgB,OAAO,EAAE;MAC/C,IAAImG,gBAAgB;MACpB,MAAMC,YAAY,GAAG7G,KAAK,CAAC8G,KAAK,CAACC,YAAY;MAC7C,OAAO,aAAanH,IAAI,CAACiH,YAAY,EAAEvI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACsI,gBAAgB,GAAG5G,KAAK,CAACgH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,gBAAgB,CAACG,YAAY,CAAC,CAAC;IAC7I;IACA,OAAOL,YAAY;EACrB,CAAC,EAAE,CAAC1G,KAAK,CAAC8G,KAAK,CAACC,YAAY,EAAE,CAAChG,iBAAiB,GAAGf,KAAK,CAACgH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjG,iBAAiB,CAACgG,YAAY,CAAC,CAAC;EACvH,MAAME,kBAAkB,GAAG1I,KAAK,CAACsD,WAAW,CAACqF,eAAe,IAAI;IAC9D,IAAIlH,KAAK,CAACmH,qBAAqB,EAAE;MAC/B,OAAOD,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,uBAAuB,CAAC;EACtD,CAAC,EAAE,CAAClH,KAAK,CAACmH,qBAAqB,CAAC,CAAC;EACjChI,4BAA4B,CAACc,MAAM,EAAE,YAAY,EAAEgH,kBAAkB,CAAC;EACtE9H,4BAA4B,CAACc,MAAM,EAAE,aAAa,EAAEkF,wBAAwB,CAAC;EAC7EhG,4BAA4B,CAACc,MAAM,EAAE,cAAc,EAAEoG,yBAAyB,CAAC;EAC/ElH,4BAA4B,CAACc,MAAM,EAAE,iBAAiB,EAAEwG,4BAA4B,CAAC;;EAErF;AACF;AACA;EACE,MAAMW,cAAc,GAAG7I,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMmG,oBAAoB,GAAG1C,iBAAiB,IAAI;IAChD,IAAIyC,cAAc,CAAChG,OAAO,KAAKuD,iBAAiB,CAACP,KAAK,EAAE;MACtDgD,cAAc,CAAChG,OAAO,GAAGuD,iBAAiB,CAACP,KAAK;MAChDxC,mBAAmB,CAACvC,mBAAmB,CAACR,wBAAwB,CAACoB,MAAM,CAACmB,OAAO,CAACrB,KAAK,CAAC,EAAE4E,iBAAiB,CAACP,KAAK,CAAC,CAAC;IACnH;EACF,CAAC;EACDlF,sBAAsB,CAACe,MAAM,EAAE,yBAAyB,EAAEoH,oBAAoB,CAAC;;EAE/E;AACF;AACA;EACE,MAAMC,cAAc,GAAG/I,KAAK,CAACsD,WAAW,CAAC,MAAM;IAC7Cb,MAAM,CAACuG,IAAI,CAAC,gEAAgE,CAAC;IAC7E,MAAMjH,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXC,eAAe,EAAE,EAAE;MACnBE,YAAY,EAAEsC,SAAS;MACvBpC,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFgB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAEe,MAAM,EAAEY,mBAAmB,EAAErB,WAAW,CAAC,CAAC;EACtDnB,0BAA0B,CAACa,MAAM,EAAE,gBAAgB,EAAEqH,cAAc,CAAC;;EAEpE;AACF;AACA;EACE;EACA;EACA,MAAME,aAAa,GAAGjJ,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACxC3C,KAAK,CAACkJ,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAACpG,OAAO,EAAE;MACzBoG,aAAa,CAACpG,OAAO,GAAG,KAAK;MAC7B;IACF;IACAJ,MAAM,CAACuG,IAAI,CAAC,wCAAwCvH,KAAK,CAACS,OAAO,CAACqF,MAAM,EAAE,CAAC;IAC3E,IAAI7E,mBAAmB,CAACG,OAAO,KAAKpB,KAAK,CAACS,OAAO,IAAIU,uBAAuB,CAACC,OAAO,KAAKb,WAAW,EAAE;MACpG;IACF;IACA,MAAMD,YAAY,GAAGhB,kBAAkB,CAAC;MACtCW,MAAM;MACNM,WAAW;MACXG,YAAY,EAAEsC,SAAS;MACvB;MACAxC,eAAe,EAAER,KAAK,CAACS,OAAO;MAC9BG,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFK,mBAAmB,CAACG,OAAO,GAAGpB,KAAK,CAACS,OAAO;IAC3CU,uBAAuB,CAACC,OAAO,GAAGb,WAAW;IAC7CqB,mBAAmB,CAACtB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACU,MAAM,EAAEf,MAAM,EAAE2B,mBAAmB,EAAE5B,KAAK,CAACS,OAAO,EAAEF,WAAW,CAAC,CAAC;EACrEhC,KAAK,CAACkJ,SAAS,CAAC,MAAM;IACpB,IAAIzH,KAAK,CAACW,qBAAqB,KAAKqC,SAAS,EAAE;MAC7C/C,MAAM,CAACmB,OAAO,CAACyB,wBAAwB,CAAC7C,KAAK,CAACW,qBAAqB,CAAC;IACtE;EACF,CAAC,EAAE,CAACV,MAAM,EAAEe,MAAM,EAAEhB,KAAK,CAACW,qBAAqB,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}