{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridPinnedRowsSelector } from './gridRowsSelector';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI: The data grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  const dataRowIdToIdLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    dataRowIdToIdLookup[id] = id;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToIdLookup,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp\n  });\n};\nexport const isAutoGeneratedRow = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutoGeneratedRow(tree[child])) {\n      validDescendants.push(child);\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates\n}) => {\n  var _previousCache$update, _previousCache$update2, _previousCache$update3;\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI: Unable to prepare a partial update if a full update is not applied yet');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...((_previousCache$update = previousCache.updates.actions.insert) != null ? _previousCache$update : [])],\n      modify: [...((_previousCache$update2 = previousCache.updates.actions.modify) != null ? _previousCache$update2 : [])],\n      remove: [...((_previousCache$update3 = previousCache.updates.actions.remove) != null ? _previousCache$update3 : [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup)\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const dataRowIdToIdLookup = _extends({}, previousCache.dataRowIdToIdLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      delete dataRowIdToIdLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n    dataRowIdToIdLookup[id] = id;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    dataRowIdToIdLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport function calculatePinnedRowsHeight(apiRef) {\n  var _pinnedRows$top, _pinnedRows$bottom;\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsHeight = (pinnedRows == null || (_pinnedRows$top = pinnedRows.top) == null ? void 0 : _pinnedRows$top.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0)) || 0;\n  const bottomPinnedRowsHeight = (pinnedRows == null || (_pinnedRows$bottom = pinnedRows.bottom) == null ? void 0 : _pinnedRows$bottom.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0)) || 0;\n  return {\n    top: topPinnedRowsHeight,\n    bottom: bottomPinnedRowsHeight\n  };\n}\nexport function getMinimalContentHeight(apiRef, rowHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  return `var(--DataGrid-overlayHeight, ${2 * Math.floor(rowHeight * densityFactor)}px)`;\n}", "map": {"version": 3, "names": ["_extends", "gridPinnedRowsSelector", "gridDensityFactorSelector", "GRID_ROOT_GROUP_ID", "GRID_ID_AUTOGENERATED", "Symbol", "buildRootGroup", "type", "id", "depth", "groupingField", "grouping<PERSON>ey", "isAutoGenerated", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenExpanded", "parent", "checkGridRowIdIsValid", "row", "detailErrorMessage", "Error", "JSON", "stringify", "join", "getRowIdFromRowModel", "rowModel", "getRowId", "createRowsInternalCache", "rows", "loading", "rowCount", "updates", "dataRowIdToModelLookup", "dataRowIdToIdLookup", "i", "length", "model", "push", "rowsBeforePartialUpdates", "loadingPropBeforePartialUpdates", "rowCountPropBeforePartialUpdates", "getTopLevelRowCount", "tree", "rowCountProp", "rootGroupNode", "Math", "max", "footerId", "getRowsStateFromCache", "apiRef", "loadingProp", "previousTree", "previousTreeDepths", "cache", "current", "caches", "unProcessedTree", "treeDepths", "unProcessedTreeDepths", "dataRowIds", "unProcessedDataRowIds", "groupingName", "applyStrategyProcessor", "groupingParamsWithHydrateRows", "unstable_applyPipeProcessors", "actions", "insert", "modify", "remove", "idToActionLookup", "totalRowCount", "totalTopLevelRowCount", "isAutoGeneratedRow", "rowNode", "getTreeNodeDescendants", "parentId", "skipAutoGeneratedRows", "node", "validDescendants", "child", "childDescendants", "j", "updateCacheWithNewRows", "previousCache", "_previousCache$update", "_previousCache$update2", "_previousCache$update3", "uniqueUpdates", "Map", "for<PERSON>ach", "update", "has", "set", "get", "partialUpdates", "alreadyAppliedActionsToRemove", "partialRow", "actionAlreadyAppliedToRow", "_action", "oldRow", "actionTypeWithActionsToRemove", "Object", "keys", "actionType", "idsToRemove", "filter", "calculatePinnedRowsHeight", "_pinnedRows$top", "_pinnedRows$bottom", "pinnedRows", "topPinnedRowsHeight", "top", "reduce", "acc", "value", "unstable_getRowHeight", "bottomPinnedRowsHeight", "bottom", "getMinimalContentHeight", "rowHeight", "densityFactor", "floor"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/rows/gridRowsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridPinnedRowsSelector } from './gridRowsSelector';\nimport { gridDensityFactorSelector } from '../density/densitySelector';\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI: The data grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  const dataRowIdToIdLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    dataRowIdToIdLookup[id] = id;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToIdLookup,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp\n  });\n};\nexport const isAutoGeneratedRow = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutoGeneratedRow(tree[child])) {\n      validDescendants.push(child);\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates\n}) => {\n  var _previousCache$update, _previousCache$update2, _previousCache$update3;\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI: Unable to prepare a partial update if a full update is not applied yet');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...((_previousCache$update = previousCache.updates.actions.insert) != null ? _previousCache$update : [])],\n      modify: [...((_previousCache$update2 = previousCache.updates.actions.modify) != null ? _previousCache$update2 : [])],\n      remove: [...((_previousCache$update3 = previousCache.updates.actions.remove) != null ? _previousCache$update3 : [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup)\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const dataRowIdToIdLookup = _extends({}, previousCache.dataRowIdToIdLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      delete dataRowIdToIdLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n    dataRowIdToIdLookup[id] = id;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    dataRowIdToIdLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport function calculatePinnedRowsHeight(apiRef) {\n  var _pinnedRows$top, _pinnedRows$bottom;\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsHeight = (pinnedRows == null || (_pinnedRows$top = pinnedRows.top) == null ? void 0 : _pinnedRows$top.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0)) || 0;\n  const bottomPinnedRowsHeight = (pinnedRows == null || (_pinnedRows$bottom = pinnedRows.bottom) == null ? void 0 : _pinnedRows$bottom.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0)) || 0;\n  return {\n    top: topPinnedRowsHeight,\n    bottom: bottomPinnedRowsHeight\n  };\n}\nexport function getMinimalContentHeight(apiRef, rowHeight) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  return `var(--DataGrid-overlayHeight, ${2 * Math.floor(rowHeight * densityFactor)}px)`;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,OAAO,MAAMC,kBAAkB,GAAG,gCAAgC;AAClE,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,CAAC,sBAAsB,CAAC;AACnE,OAAO,MAAMC,cAAc,GAAGA,CAAA,MAAO;EACnCC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEL,kBAAkB;EACtBM,KAAK,EAAE,CAAC,CAAC;EACTC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,EAAE;EACZC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,MAAM,EAAE;AACV,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACT,EAAE,EAAEU,GAAG,EAAEC,kBAAkB,GAAG,iDAAiD,EAAE;EACrH,IAAIX,EAAE,IAAI,IAAI,EAAE;IACd,MAAM,IAAIY,KAAK,CAAC,CAAC,gFAAgF,EAAE,qFAAqF,EAAED,kBAAkB,EAAEE,IAAI,CAACC,SAAS,CAACJ,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;EAChP;AACF;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEP,kBAAkB,KAAK;EAC9E,MAAMX,EAAE,GAAGkB,QAAQ,GAAGA,QAAQ,CAACD,QAAQ,CAAC,GAAGA,QAAQ,CAACjB,EAAE;EACtDS,qBAAqB,CAACT,EAAE,EAAEiB,QAAQ,EAAEN,kBAAkB,CAAC;EACvD,OAAOX,EAAE;AACX,CAAC;AACD,OAAO,MAAMmB,uBAAuB,GAAGA,CAAC;EACtCC,IAAI;EACJF,QAAQ;EACRG,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG;IACdxB,IAAI,EAAE,MAAM;IACZqB,IAAI,EAAE;EACR,CAAC;EACD,MAAMI,sBAAsB,GAAG,CAAC,CAAC;EACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,MAAME,KAAK,GAAGR,IAAI,CAACM,CAAC,CAAC;IACrB,MAAM1B,EAAE,GAAGgB,oBAAoB,CAACY,KAAK,EAAEV,QAAQ,CAAC;IAChDM,sBAAsB,CAACxB,EAAE,CAAC,GAAG4B,KAAK;IAClCH,mBAAmB,CAACzB,EAAE,CAAC,GAAGA,EAAE;IAC5BuB,OAAO,CAACH,IAAI,CAACS,IAAI,CAAC7B,EAAE,CAAC;EACvB;EACA,OAAO;IACL8B,wBAAwB,EAAEV,IAAI;IAC9BW,+BAA+B,EAAEV,OAAO;IACxCW,gCAAgC,EAAEV,QAAQ;IAC1CC,OAAO;IACPE,mBAAmB;IACnBD;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMS,mBAAmB,GAAGA,CAAC;EAClCC,IAAI;EACJC,YAAY,GAAG;AACjB,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAGF,IAAI,CAACvC,kBAAkB,CAAC;EAC9C,OAAO0C,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEC,aAAa,CAAC/B,QAAQ,CAACsB,MAAM,IAAIS,aAAa,CAACG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzG,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAAC;EACpCC,MAAM;EACNN,YAAY,GAAG,CAAC;EAChBO,WAAW;EACXC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGJ,MAAM,CAACK,OAAO,CAACC,MAAM,CAAC3B,IAAI;;EAExC;EACA,MAAM;IACJc,IAAI,EAAEc,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjCC,UAAU,EAAEC,qBAAqB;IACjCC;EACF,CAAC,GAAGZ,MAAM,CAACK,OAAO,CAACQ,sBAAsB,CAAC,iBAAiB,EAAE;IAC3DX,YAAY;IACZC,kBAAkB;IAClBrB,OAAO,EAAEsB,KAAK,CAACtB,OAAO;IACtBE,mBAAmB,EAAEoB,KAAK,CAACpB,mBAAmB;IAC9CD,sBAAsB,EAAEqB,KAAK,CAACrB;EAChC,CAAC,CAAC;;EAEF;EACA,MAAM+B,6BAA6B,GAAGd,MAAM,CAACK,OAAO,CAACU,4BAA4B,CAAC,aAAa,EAAE;IAC/FtB,IAAI,EAAEc,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjCzB,mBAAmB,EAAEoB,KAAK,CAACpB,mBAAmB;IAC9C0B,UAAU,EAAEC,qBAAqB;IACjC5B,sBAAsB,EAAEqB,KAAK,CAACrB;EAChC,CAAC,CAAC;;EAEF;EACAiB,MAAM,CAACK,OAAO,CAACC,MAAM,CAAC3B,IAAI,CAACG,OAAO,GAAG;IACnCxB,IAAI,EAAE,SAAS;IACf0D,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE,CAAC;EACrB,CAAC;EACD,OAAOrE,QAAQ,CAAC,CAAC,CAAC,EAAE+D,6BAA6B,EAAE;IACjDO,aAAa,EAAEzB,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEoB,6BAA6B,CAACJ,UAAU,CAACxB,MAAM,CAAC;IACtFoC,qBAAqB,EAAE9B,mBAAmB,CAAC;MACzCC,IAAI,EAAEqB,6BAA6B,CAACrB,IAAI;MACxCC;IACF,CAAC,CAAC;IACFkB,YAAY;IACZhC,OAAO,EAAEqB;EACX,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMsB,kBAAkB,GAAGC,OAAO,IAAIA,OAAO,CAAClE,IAAI,KAAK,aAAa,IAAIkE,OAAO,CAAClE,IAAI,KAAK,QAAQ,IAAIkE,OAAO,CAAClE,IAAI,KAAK,OAAO,IAAIkE,OAAO,CAAC7D,eAAe,IAAI6D,OAAO,CAAClE,IAAI,KAAK,WAAW,IAAIkE,OAAO,CAAC7D,eAAe;AAC1N,OAAO,MAAM8D,sBAAsB,GAAGA,CAAChC,IAAI,EAAEiC,QAAQ,EAAEC,qBAAqB,KAAK;EAC/E,MAAMC,IAAI,GAAGnC,IAAI,CAACiC,QAAQ,CAAC;EAC3B,IAAIE,IAAI,CAACtE,IAAI,KAAK,OAAO,EAAE;IACzB,OAAO,EAAE;EACX;EACA,MAAMuE,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,IAAI,CAAChE,QAAQ,CAACsB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChD,MAAM6C,KAAK,GAAGF,IAAI,CAAChE,QAAQ,CAACqB,CAAC,CAAC;IAC9B,IAAI,CAAC0C,qBAAqB,IAAI,CAACJ,kBAAkB,CAAC9B,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAE;MAC9DD,gBAAgB,CAACzC,IAAI,CAAC0C,KAAK,CAAC;IAC9B;IACA,MAAMC,gBAAgB,GAAGN,sBAAsB,CAAChC,IAAI,EAAEqC,KAAK,EAAEH,qBAAqB,CAAC;IACnF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAAC7C,MAAM,EAAE8C,CAAC,IAAI,CAAC,EAAE;MACnDH,gBAAgB,CAACzC,IAAI,CAAC2C,gBAAgB,CAACC,CAAC,CAAC,CAAC;IAC5C;EACF;EACA,IAAI,CAACL,qBAAqB,IAAIC,IAAI,CAAC9B,QAAQ,IAAI,IAAI,EAAE;IACnD+B,gBAAgB,CAACzC,IAAI,CAACwC,IAAI,CAAC9B,QAAQ,CAAC;EACtC;EACA,OAAO+B,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMI,sBAAsB,GAAGA,CAAC;EACrCC,aAAa;EACbzD,QAAQ;EACRK;AACF,CAAC,KAAK;EACJ,IAAIqD,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;EACzE,IAAIH,aAAa,CAACpD,OAAO,CAACxB,IAAI,KAAK,MAAM,EAAE;IACzC,MAAM,IAAIa,KAAK,CAAC,6EAA6E,CAAC;EAChG;;EAEA;EACA;EACA,MAAMmE,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/BzD,OAAO,CAAC0D,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMlF,EAAE,GAAGgB,oBAAoB,CAACkE,MAAM,EAAEhE,QAAQ,EAAE,0DAA0D,CAAC;IAC7G,IAAI6D,aAAa,CAACI,GAAG,CAACnF,EAAE,CAAC,EAAE;MACzB+E,aAAa,CAACK,GAAG,CAACpF,EAAE,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEuF,aAAa,CAACM,GAAG,CAACrF,EAAE,CAAC,EAAEkF,MAAM,CAAC,CAAC;IACpE,CAAC,MAAM;MACLH,aAAa,CAACK,GAAG,CAACpF,EAAE,EAAEkF,MAAM,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,MAAMI,cAAc,GAAG;IACrBvF,IAAI,EAAE,SAAS;IACf0D,OAAO,EAAE;MACPC,MAAM,EAAE,CAAC,IAAI,CAACkB,qBAAqB,GAAGD,aAAa,CAACpD,OAAO,CAACkC,OAAO,CAACC,MAAM,KAAK,IAAI,GAAGkB,qBAAqB,GAAG,EAAE,CAAC,CAAC;MAClHjB,MAAM,EAAE,CAAC,IAAI,CAACkB,sBAAsB,GAAGF,aAAa,CAACpD,OAAO,CAACkC,OAAO,CAACE,MAAM,KAAK,IAAI,GAAGkB,sBAAsB,GAAG,EAAE,CAAC,CAAC;MACpHjB,MAAM,EAAE,CAAC,IAAI,CAACkB,sBAAsB,GAAGH,aAAa,CAACpD,OAAO,CAACkC,OAAO,CAACG,MAAM,KAAK,IAAI,GAAGkB,sBAAsB,GAAG,EAAE,CAAC;IACrH,CAAC;IACDjB,gBAAgB,EAAErE,QAAQ,CAAC,CAAC,CAAC,EAAEmF,aAAa,CAACpD,OAAO,CAACsC,gBAAgB;EACvE,CAAC;EACD,MAAMrC,sBAAsB,GAAGhC,QAAQ,CAAC,CAAC,CAAC,EAAEmF,aAAa,CAACnD,sBAAsB,CAAC;EACjF,MAAMC,mBAAmB,GAAGjC,QAAQ,CAAC,CAAC,CAAC,EAAEmF,aAAa,CAAClD,mBAAmB,CAAC;EAC3E,MAAM8D,6BAA6B,GAAG;IACpC7B,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC;EACX,CAAC;;EAED;EACA;EACA;EACA;EACA;EACAmB,aAAa,CAACE,OAAO,CAAC,CAACO,UAAU,EAAExF,EAAE,KAAK;IACxC,MAAMyF,yBAAyB,GAAGH,cAAc,CAACzB,gBAAgB,CAAC7D,EAAE,CAAC;;IAErE;IACA;IACA,IAAIwF,UAAU,CAACE,OAAO,KAAK,QAAQ,EAAE;MACnC;MACA;MACA,IAAID,yBAAyB,KAAK,QAAQ,IAAI,CAACjE,sBAAsB,CAACxB,EAAE,CAAC,EAAE;QACzE;MACF;;MAEA;MACA;MACA,IAAIyF,yBAAyB,IAAI,IAAI,EAAE;QACrCF,6BAA6B,CAACE,yBAAyB,CAAC,CAACzF,EAAE,CAAC,GAAG,IAAI;MACrE;;MAEA;MACAsF,cAAc,CAAC7B,OAAO,CAACG,MAAM,CAAC/B,IAAI,CAAC7B,EAAE,CAAC;MACtC,OAAOwB,sBAAsB,CAACxB,EAAE,CAAC;MACjC,OAAOyB,mBAAmB,CAACzB,EAAE,CAAC;MAC9B;IACF;IACA,MAAM2F,MAAM,GAAGnE,sBAAsB,CAACxB,EAAE,CAAC;;IAEzC;IACA,IAAI2F,MAAM,EAAE;MACV;MACA;MACA,IAAIF,yBAAyB,KAAK,QAAQ,EAAE;QAC1CF,6BAA6B,CAAC3B,MAAM,CAAC5D,EAAE,CAAC,GAAG,IAAI;QAC/CsF,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAC9B,IAAI,CAAC7B,EAAE,CAAC;MACxC;MACA;MACA;MAAA,KACK,IAAIyF,yBAAyB,IAAI,IAAI,EAAE;QAC1CH,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAC9B,IAAI,CAAC7B,EAAE,CAAC;MACxC;;MAEA;MACAwB,sBAAsB,CAACxB,EAAE,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEmG,MAAM,EAAEH,UAAU,CAAC;MAC7D;IACF;;IAEA;IACA;IACA;IACA,IAAIC,yBAAyB,KAAK,QAAQ,EAAE;MAC1CF,6BAA6B,CAAC3B,MAAM,CAAC5D,EAAE,CAAC,GAAG,IAAI;MAC/CsF,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAAC7B,IAAI,CAAC7B,EAAE,CAAC;IACxC;IACA;IACA;IACA;IAAA,KACK,IAAIyF,yBAAyB,IAAI,IAAI,EAAE;MAC1CH,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAAC7B,IAAI,CAAC7B,EAAE,CAAC;IACxC;;IAEA;IACAwB,sBAAsB,CAACxB,EAAE,CAAC,GAAGwF,UAAU;IACvC/D,mBAAmB,CAACzB,EAAE,CAAC,GAAGA,EAAE;EAC9B,CAAC,CAAC;EACF,MAAM4F,6BAA6B,GAAGC,MAAM,CAACC,IAAI,CAACP,6BAA6B,CAAC;EAChF,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,6BAA6B,CAACjE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChE,MAAMqE,UAAU,GAAGH,6BAA6B,CAAClE,CAAC,CAAC;IACnD,MAAMsE,WAAW,GAAGT,6BAA6B,CAACQ,UAAU,CAAC;IAC7D,IAAIF,MAAM,CAACC,IAAI,CAACE,WAAW,CAAC,CAACrE,MAAM,GAAG,CAAC,EAAE;MACvC2D,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,GAAGT,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,CAACE,MAAM,CAACjG,EAAE,IAAI,CAACgG,WAAW,CAAChG,EAAE,CAAC,CAAC;IACxG;EACF;EACA,OAAO;IACLwB,sBAAsB;IACtBC,mBAAmB;IACnBF,OAAO,EAAE+D,cAAc;IACvBxD,wBAAwB,EAAE6C,aAAa,CAAC7C,wBAAwB;IAChEC,+BAA+B,EAAE4C,aAAa,CAAC5C,+BAA+B;IAC9EC,gCAAgC,EAAE2C,aAAa,CAAC3C;EAClD,CAAC;AACH,CAAC;AACD,OAAO,SAASkE,yBAAyBA,CAACzD,MAAM,EAAE;EAChD,IAAI0D,eAAe,EAAEC,kBAAkB;EACvC,MAAMC,UAAU,GAAG5G,sBAAsB,CAACgD,MAAM,CAAC;EACjD,MAAM6D,mBAAmB,GAAG,CAACD,UAAU,IAAI,IAAI,IAAI,CAACF,eAAe,GAAGE,UAAU,CAACE,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,eAAe,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC9ID,GAAG,IAAIhE,MAAM,CAACK,OAAO,CAAC6D,qBAAqB,CAACD,KAAK,CAAC1G,EAAE,CAAC;IACrD,OAAOyG,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;EACX,MAAMG,sBAAsB,GAAG,CAACP,UAAU,IAAI,IAAI,IAAI,CAACD,kBAAkB,GAAGC,UAAU,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC1JD,GAAG,IAAIhE,MAAM,CAACK,OAAO,CAAC6D,qBAAqB,CAACD,KAAK,CAAC1G,EAAE,CAAC;IACrD,OAAOyG,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;EACX,OAAO;IACLF,GAAG,EAAED,mBAAmB;IACxBO,MAAM,EAAED;EACV,CAAC;AACH;AACA,OAAO,SAASE,uBAAuBA,CAACrE,MAAM,EAAEsE,SAAS,EAAE;EACzD,MAAMC,aAAa,GAAGtH,yBAAyB,CAAC+C,MAAM,CAAC;EACvD,OAAO,iCAAiC,CAAC,GAAGJ,IAAI,CAAC4E,KAAK,CAACF,SAAS,GAAGC,aAAa,CAAC,KAAK;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}