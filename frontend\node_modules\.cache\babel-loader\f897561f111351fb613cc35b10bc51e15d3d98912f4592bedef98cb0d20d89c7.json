{"ast": null, "code": "import { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../../colDef';\nimport { buildWarning } from '../../../../utils/warning';\nfunction sanitizeCellValue(value, csvOptions) {\n  if (typeof value === 'string') {\n    if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n      const escapedValue = value.replace(/\"/g, '\"\"');\n      // Make sure value containing delimiter or line break won't be split into multiple cells\n      if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => value.includes(delimiter))) {\n        return \"\\\"\".concat(escapedValue, \"\\\"\");\n      }\n      if (csvOptions.escapeFormulas) {\n        // See https://owasp.org/www-community/attacks/CSV_Injection\n        if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n          return \"'\".concat(escapedValue);\n        }\n      }\n      return escapedValue;\n    }\n    return value;\n  }\n  return value;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    var _cellParams$value2;\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      var _cellParams$value;\n      value = (_cellParams$value = cellParams.value) == null ? void 0 : _cellParams$value.toISOString();\n    } else if (typeof ((_cellParams$value2 = cellParams.value) == null ? void 0 : _cellParams$value2.toString) === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nconst objectFormattedValueWarning = buildWarning(['MUI: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\nclass CSVRow {\n  constructor(options) {\n    this.options = void 0;\n    this.rowString = '';\n    this.isEmpty = true;\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (value === null || value === undefined) {\n      this.rowString += '';\n    } else if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = _ref => {\n  let {\n    id,\n    columns,\n    getCellParams,\n    csvOptions,\n    ignoreValueFormatter\n  } = _ref;\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        objectFormattedValueWarning();\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => \"\".concat(acc).concat(serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  }), \"\\r\\n\"), '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.unstable_getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.unstable_getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = \"\".concat(headerRows.map(row => row.getRowString()).join('\\r\\n'), \"\\r\\n\");\n  return \"\".concat(CSVHead).concat(CSVBody).trim();\n}", "map": {"version": 3, "names": ["GRID_CHECKBOX_SELECTION_COL_DEF", "buildWarning", "sanitizeCellValue", "value", "csvOptions", "shouldAppendQuotes", "escapeFormulas", "escapedValue", "replace", "delimiter", "some", "includes", "concat", "serializeCellValue", "cellParams", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_cellParams$value2", "columnType", "colDef", "type", "String", "_cellParams$value", "toISOString", "toString", "formattedValue", "objectFormattedValueWarning", "CSVRow", "constructor", "rowString", "isEmpty", "addValue", "undefined", "getRowString", "serializeRow", "_ref", "id", "columns", "getCellParams", "row", "for<PERSON>ach", "column", "field", "process", "env", "NODE_ENV", "buildCSV", "rowIds", "apiRef", "CSVBody", "reduce", "acc", "current", "trim", "includeHeaders", "filteredColumns", "filter", "headerRows", "includeColumnGroupsHeaders", "columnGroupLookup", "unstable_getAllGroupDetails", "maxColumnGroupsDepth", "columnGroupPathsLookup", "columnGroupPath", "unstable_getColumnGroupPath", "Math", "max", "length", "i", "headerGroupRow", "push", "columnGroupId", "columnGroup", "headerName", "groupId", "mainHeaderRow", "CSVHead", "map", "join"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/export/serializers/csvSerializer.js"], "sourcesContent": ["import { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../../colDef';\nimport { buildWarning } from '../../../../utils/warning';\nfunction sanitizeCellValue(value, csvOptions) {\n  if (typeof value === 'string') {\n    if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n      const escapedValue = value.replace(/\"/g, '\"\"');\n      // Make sure value containing delimiter or line break won't be split into multiple cells\n      if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => value.includes(delimiter))) {\n        return `\"${escapedValue}\"`;\n      }\n      if (csvOptions.escapeFormulas) {\n        // See https://owasp.org/www-community/attacks/CSV_Injection\n        if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n          return `'${escapedValue}`;\n        }\n      }\n      return escapedValue;\n    }\n    return value;\n  }\n  return value;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    var _cellParams$value2;\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      var _cellParams$value;\n      value = (_cellParams$value = cellParams.value) == null ? void 0 : _cellParams$value.toISOString();\n    } else if (typeof ((_cellParams$value2 = cellParams.value) == null ? void 0 : _cellParams$value2.toString) === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nconst objectFormattedValueWarning = buildWarning(['MUI: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\nclass CSVRow {\n  constructor(options) {\n    this.options = void 0;\n    this.rowString = '';\n    this.isEmpty = true;\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (value === null || value === undefined) {\n      this.rowString += '';\n    } else if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        objectFormattedValueWarning();\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.unstable_getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.unstable_getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,oBAAoB;AACpE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC5C,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIC,UAAU,CAACC,kBAAkB,IAAID,UAAU,CAACE,cAAc,EAAE;MAC9D,MAAMC,YAAY,GAAGJ,KAAK,CAACK,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MAC9C;MACA,IAAI,CAACJ,UAAU,CAACK,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAACC,IAAI,CAACD,SAAS,IAAIN,KAAK,CAACQ,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAE;QACxF,YAAAG,MAAA,CAAWL,YAAY;MACzB;MACA,IAAIH,UAAU,CAACE,cAAc,EAAE;QAC7B;QACA,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAACK,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,WAAAK,MAAA,CAAWL,YAAY;QACzB;MACF;MACA,OAAOA,YAAY;IACrB;IACA,OAAOJ,KAAK;EACd;EACA,OAAOA,KAAK;AACd;AACA,OAAO,MAAMU,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,OAAO,KAAK;EACzD,MAAM;IACJX,UAAU;IACVY;EACF,CAAC,GAAGD,OAAO;EACX,IAAIZ,KAAK;EACT,IAAIa,oBAAoB,EAAE;IACxB,IAAIC,kBAAkB;IACtB,MAAMC,UAAU,GAAGJ,UAAU,CAACK,MAAM,CAACC,IAAI;IACzC,IAAIF,UAAU,KAAK,QAAQ,EAAE;MAC3Bf,KAAK,GAAGkB,MAAM,CAACP,UAAU,CAACX,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIe,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MAC7D,IAAII,iBAAiB;MACrBnB,KAAK,GAAG,CAACmB,iBAAiB,GAAGR,UAAU,CAACX,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,iBAAiB,CAACC,WAAW,CAAC,CAAC;IACnG,CAAC,MAAM,IAAI,QAAQ,CAACN,kBAAkB,GAAGH,UAAU,CAACX,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGc,kBAAkB,CAACO,QAAQ,CAAC,KAAK,UAAU,EAAE;MACzHrB,KAAK,GAAGW,UAAU,CAACX,KAAK,CAACqB,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM;MACLrB,KAAK,GAAGW,UAAU,CAACX,KAAK;IAC1B;EACF,CAAC,MAAM;IACLA,KAAK,GAAGW,UAAU,CAACW,cAAc;EACnC;EACA,OAAOvB,iBAAiB,CAACC,KAAK,EAAEC,UAAU,CAAC;AAC7C,CAAC;AACD,MAAMsB,2BAA2B,GAAGzB,YAAY,CAAC,CAAC,kIAAkI,EAAE,6EAA6E,CAAC,CAAC;AACrQ,MAAM0B,MAAM,CAAC;EACXC,WAAWA,CAACb,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAG,KAAK,CAAC;IACrB,IAAI,CAACc,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACf,OAAO,GAAGA,OAAO;EACxB;EACAgB,QAAQA,CAAC5B,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAAC2B,OAAO,EAAE;MACjB,IAAI,CAACD,SAAS,IAAI,IAAI,CAACd,OAAO,CAACX,UAAU,CAACK,SAAS;IACrD;IACA,IAAIN,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK6B,SAAS,EAAE;MACzC,IAAI,CAACH,SAAS,IAAI,EAAE;IACtB,CAAC,MAAM,IAAI,OAAO,IAAI,CAACd,OAAO,CAACb,iBAAiB,KAAK,UAAU,EAAE;MAC/D,IAAI,CAAC2B,SAAS,IAAI,IAAI,CAACd,OAAO,CAACb,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAACY,OAAO,CAACX,UAAU,CAAC;IAClF,CAAC,MAAM;MACL,IAAI,CAACyB,SAAS,IAAI1B,KAAK;IACzB;IACA,IAAI,CAAC2B,OAAO,GAAG,KAAK;EACtB;EACAG,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACvB;AACF;AACA,MAAMK,YAAY,GAAGC,IAAA,IAMf;EAAA,IANgB;IACpBC,EAAE;IACFC,OAAO;IACPC,aAAa;IACblC,UAAU;IACVY;EACF,CAAC,GAAAmB,IAAA;EACC,MAAMI,GAAG,GAAG,IAAIZ,MAAM,CAAC;IACrBvB;EACF,CAAC,CAAC;EACFiC,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;IACxB,MAAM3B,UAAU,GAAGwB,aAAa,CAACF,EAAE,EAAEK,MAAM,CAACC,KAAK,CAAC;IAClD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIxB,MAAM,CAACP,UAAU,CAACW,cAAc,CAAC,KAAK,iBAAiB,EAAE;QAC3DC,2BAA2B,CAAC,CAAC;MAC/B;IACF;IACAa,GAAG,CAACR,QAAQ,CAAClB,kBAAkB,CAACC,UAAU,EAAE;MAC1CE,oBAAoB;MACpBZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOmC,GAAG,CAACN,YAAY,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,SAASa,QAAQA,CAAC/B,OAAO,EAAE;EAChC,MAAM;IACJsB,OAAO;IACPU,MAAM;IACN3C,UAAU;IACVY,oBAAoB;IACpBgC;EACF,CAAC,GAAGjC,OAAO;EACX,MAAMkC,OAAO,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEf,EAAE,QAAAxB,MAAA,CAAQuC,GAAG,EAAAvC,MAAA,CAAGsB,YAAY,CAAC;IAC/DE,EAAE;IACFC,OAAO;IACPC,aAAa,EAAEU,MAAM,CAACI,OAAO,CAACd,aAAa;IAC3CtB,oBAAoB;IACpBZ;EACF,CAAC,CAAC,SAAM,EAAE,EAAE,CAAC,CAACiD,IAAI,CAAC,CAAC;EACpB,IAAI,CAACjD,UAAU,CAACkD,cAAc,EAAE;IAC9B,OAAOL,OAAO;EAChB;EACA,MAAMM,eAAe,GAAGlB,OAAO,CAACmB,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK1C,+BAA+B,CAAC0C,KAAK,CAAC;EACxG,MAAMe,UAAU,GAAG,EAAE;EACrB,IAAIrD,UAAU,CAACsD,0BAA0B,EAAE;IACzC,MAAMC,iBAAiB,GAAGX,MAAM,CAACI,OAAO,CAACQ,2BAA2B,CAAC,CAAC;IACtE,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,MAAMC,sBAAsB,GAAGP,eAAe,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEV,MAAM,KAAK;MACrE,MAAMsB,eAAe,GAAGf,MAAM,CAACI,OAAO,CAACY,2BAA2B,CAACvB,MAAM,CAACC,KAAK,CAAC;MAChFS,GAAG,CAACV,MAAM,CAACC,KAAK,CAAC,GAAGqB,eAAe;MACnCF,oBAAoB,GAAGI,IAAI,CAACC,GAAG,CAACL,oBAAoB,EAAEE,eAAe,CAACI,MAAM,CAAC;MAC7E,OAAOhB,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,oBAAoB,EAAEO,CAAC,IAAI,CAAC,EAAE;MAChD,MAAMC,cAAc,GAAG,IAAI1C,MAAM,CAAC;QAChCvB,UAAU;QACVF;MACF,CAAC,CAAC;MACFuD,UAAU,CAACa,IAAI,CAACD,cAAc,CAAC;MAC/Bd,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;QAChC,MAAM8B,aAAa,GAAG,CAACT,sBAAsB,CAACrB,MAAM,CAACC,KAAK,CAAC,IAAI,EAAE,EAAE0B,CAAC,CAAC;QACrE,MAAMI,WAAW,GAAGb,iBAAiB,CAACY,aAAa,CAAC;QACpDF,cAAc,CAACtC,QAAQ,CAACyC,WAAW,GAAGA,WAAW,CAACC,UAAU,IAAID,WAAW,CAACE,OAAO,GAAG,EAAE,CAAC;MAC3F,CAAC,CAAC;IACJ;EACF;EACA,MAAMC,aAAa,GAAG,IAAIhD,MAAM,CAAC;IAC/BvB,UAAU;IACVF;EACF,CAAC,CAAC;EACFqD,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;IAChCkC,aAAa,CAAC5C,QAAQ,CAACU,MAAM,CAACgC,UAAU,IAAIhC,MAAM,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACFe,UAAU,CAACa,IAAI,CAACK,aAAa,CAAC;EAC9B,MAAMC,OAAO,MAAAhE,MAAA,CAAM6C,UAAU,CAACoB,GAAG,CAACtC,GAAG,IAAIA,GAAG,CAACN,YAAY,CAAC,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM,CAAC,SAAM;EAC/E,OAAO,GAAAlE,MAAA,CAAGgE,OAAO,EAAAhE,MAAA,CAAGqC,OAAO,EAAGI,IAAI,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}