{"ast": null, "code": "import React from'react';import{Box,Typography,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Button,Divider}from'@mui/material';import TableChartIcon from'@mui/icons-material/TableChart';import ArrowBackIcon from'@mui/icons-material/ArrowBack';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WorksheetSelect=_ref=>{let{worksheets,onSelect,onBack}=_ref;return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"\\u8BF7\\u9009\\u62E9\\u5DE5\\u4F5C\\u8868\"}),/*#__PURE__*/_jsx(List,{sx:{width:'100%',bgcolor:'background.paper',mt:2},children:worksheets.map((worksheet,index)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[index>0&&/*#__PURE__*/_jsx(Divider,{component:\"li\"}),/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>onSelect(worksheet),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(TableChartIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:worksheet})]})})]},worksheet))}),/*#__PURE__*/_jsx(Box,{sx:{mt:3,display:'flex',justifyContent:'flex-start'},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:onBack,children:\"\\u8FD4\\u56DE\"})})]});};export default WorksheetSelect;", "map": {"version": 3, "names": ["React", "Box", "Typography", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "Divider", "TableChartIcon", "ArrowBackIcon", "jsx", "_jsx", "jsxs", "_jsxs", "WorksheetSelect", "_ref", "worksheets", "onSelect", "onBack", "children", "variant", "gutterBottom", "sx", "width", "bgcolor", "mt", "map", "worksheet", "index", "Fragment", "component", "disablePadding", "onClick", "color", "primary", "display", "justifyContent", "startIcon"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/WorksheetSelect.js"], "sourcesContent": ["import React from 'react';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  List, \r\n  ListItem, \r\n  ListItemButton,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Button,\r\n  Divider\r\n} from '@mui/material';\r\nimport TableChartIcon from '@mui/icons-material/TableChart';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\n\r\nconst WorksheetSelect = ({ worksheets, onSelect, onBack }) => {\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h6\" gutterBottom>\r\n        请选择工作表\r\n      </Typography>\r\n      \r\n      <List sx={{ width: '100%', bgcolor: 'background.paper', mt: 2 }}>\r\n        {worksheets.map((worksheet, index) => (\r\n          <React.Fragment key={worksheet}>\r\n            {index > 0 && <Divider component=\"li\" />}\r\n            <ListItem disablePadding>\r\n              <ListItemButton onClick={() => onSelect(worksheet)}>\r\n                <ListItemIcon>\r\n                  <TableChartIcon color=\"primary\" />\r\n                </ListItemIcon>\r\n                <ListItemText primary={worksheet} />\r\n              </ListItemButton>\r\n            </ListItem>\r\n          </React.Fragment>\r\n        ))}\r\n      </List>\r\n      \r\n      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-start' }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          startIcon={<ArrowBackIcon />}\r\n          onClick={onBack}\r\n        >\r\n          返回\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default WorksheetSelect; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,QAAQ,CACRC,cAAc,CACdC,YAAY,CACZC,YAAY,CACZC,MAAM,CACNC,OAAO,KACF,eAAe,CACtB,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1D,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAsC,IAArC,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,MAAO,CAAC,CAAAH,IAAA,CACvD,mBACEF,KAAA,CAACd,GAAG,EAAAoB,QAAA,eACFR,IAAA,CAACX,UAAU,EAACoB,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,sCAEtC,CAAY,CAAC,cAEbR,IAAA,CAACV,IAAI,EAACqB,EAAE,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,OAAO,CAAE,kBAAkB,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CAC7DH,UAAU,CAACU,GAAG,CAAC,CAACC,SAAS,CAAEC,KAAK,gBAC/Bf,KAAA,CAACf,KAAK,CAAC+B,QAAQ,EAAAV,QAAA,EACZS,KAAK,CAAG,CAAC,eAAIjB,IAAA,CAACJ,OAAO,EAACuB,SAAS,CAAC,IAAI,CAAE,CAAC,cACxCnB,IAAA,CAACT,QAAQ,EAAC6B,cAAc,MAAAZ,QAAA,cACtBN,KAAA,CAACV,cAAc,EAAC6B,OAAO,CAAEA,CAAA,GAAMf,QAAQ,CAACU,SAAS,CAAE,CAAAR,QAAA,eACjDR,IAAA,CAACP,YAAY,EAAAe,QAAA,cACXR,IAAA,CAACH,cAAc,EAACyB,KAAK,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cACftB,IAAA,CAACN,YAAY,EAAC6B,OAAO,CAAEP,SAAU,CAAE,CAAC,EACtB,CAAC,CACT,CAAC,GATQA,SAUL,CACjB,CAAC,CACE,CAAC,cAEPhB,IAAA,CAACZ,GAAG,EAACuB,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAC,CAAEU,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,YAAa,CAAE,CAAAjB,QAAA,cAChER,IAAA,CAACL,MAAM,EACLc,OAAO,CAAC,UAAU,CAClBiB,SAAS,cAAE1B,IAAA,CAACF,aAAa,GAAE,CAAE,CAC7BuB,OAAO,CAAEd,MAAO,CAAAC,QAAA,CACjB,cAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}