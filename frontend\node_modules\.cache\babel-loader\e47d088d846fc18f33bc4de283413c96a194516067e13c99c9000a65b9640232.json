{"ast": null, "code": "import createChainedFunction from '@mui/utils/createChainedFunction';\nexport default createChainedFunction;", "map": {"version": 3, "names": ["createChainedFunction"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/utils/createChainedFunction.js"], "sourcesContent": ["import createChainedFunction from '@mui/utils/createChainedFunction';\nexport default createChainedFunction;"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,kCAAkC;AACpE,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}