{"ast": null, "code": "import invariant from 'tiny-invariant';\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n    right = _ref.right,\n    bottom = _ref.bottom,\n    left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n    _ref2$margin = _ref2.margin,\n    margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n    _ref2$border = _ref2.border,\n    border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n    _ref2$padding = _ref2.padding,\n    padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n  if (suffix !== 'px') {\n    return 0;\n  }\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n    border = original.border,\n    margin = original.margin,\n    padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };", "map": {"version": 3, "names": ["invariant", "getRect", "_ref", "top", "right", "bottom", "left", "width", "height", "rect", "x", "y", "center", "expand", "target", "expandBy", "shrink", "shrinkBy", "shift", "shiftBy", "noSpacing", "createBox", "_ref2", "borderBox", "_ref2$margin", "margin", "_ref2$border", "border", "_ref2$padding", "padding", "marginBox", "paddingBox", "contentBox", "parse", "raw", "value", "slice", "suffix", "result", "Number", "isNaN", "process", "env", "NODE_ENV", "getWindowScroll", "window", "pageXOffset", "pageYOffset", "offset", "original", "change", "shifted", "withScroll", "scroll", "calculateBox", "styles", "marginTop", "marginRight", "marginBottom", "marginLeft", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "getBox", "el", "getBoundingClientRect", "getComputedStyle"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/css-box-model/dist/css-box-model.esm.js"], "sourcesContent": ["import invariant from 'tiny-invariant';\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,IAAI,GAAGJ,IAAI,CAACI,IAAI;EACpB,IAAIC,KAAK,GAAGH,KAAK,GAAGE,IAAI;EACxB,IAAIE,MAAM,GAAGH,MAAM,GAAGF,GAAG;EACzB,IAAIM,IAAI,GAAG;IACTN,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdE,CAAC,EAAEJ,IAAI;IACPK,CAAC,EAAER,GAAG;IACNS,MAAM,EAAE;MACNF,CAAC,EAAE,CAACN,KAAK,GAAGE,IAAI,IAAI,CAAC;MACrBK,CAAC,EAAE,CAACN,MAAM,GAAGF,GAAG,IAAI;IACtB;EACF,CAAC;EACD,OAAOM,IAAI;AACb,CAAC;AACD,IAAII,MAAM,GAAG,SAASA,MAAMA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC7C,OAAO;IACLZ,GAAG,EAAEW,MAAM,CAACX,GAAG,GAAGY,QAAQ,CAACZ,GAAG;IAC9BG,IAAI,EAAEQ,MAAM,CAACR,IAAI,GAAGS,QAAQ,CAACT,IAAI;IACjCD,MAAM,EAAES,MAAM,CAACT,MAAM,GAAGU,QAAQ,CAACV,MAAM;IACvCD,KAAK,EAAEU,MAAM,CAACV,KAAK,GAAGW,QAAQ,CAACX;EACjC,CAAC;AACH,CAAC;AACD,IAAIY,MAAM,GAAG,SAASA,MAAMA,CAACF,MAAM,EAAEG,QAAQ,EAAE;EAC7C,OAAO;IACLd,GAAG,EAAEW,MAAM,CAACX,GAAG,GAAGc,QAAQ,CAACd,GAAG;IAC9BG,IAAI,EAAEQ,MAAM,CAACR,IAAI,GAAGW,QAAQ,CAACX,IAAI;IACjCD,MAAM,EAAES,MAAM,CAACT,MAAM,GAAGY,QAAQ,CAACZ,MAAM;IACvCD,KAAK,EAAEU,MAAM,CAACV,KAAK,GAAGa,QAAQ,CAACb;EACjC,CAAC;AACH,CAAC;AAED,IAAIc,KAAK,GAAG,SAASA,KAAKA,CAACJ,MAAM,EAAEK,OAAO,EAAE;EAC1C,OAAO;IACLhB,GAAG,EAAEW,MAAM,CAACX,GAAG,GAAGgB,OAAO,CAACR,CAAC;IAC3BL,IAAI,EAAEQ,MAAM,CAACR,IAAI,GAAGa,OAAO,CAACT,CAAC;IAC7BL,MAAM,EAAES,MAAM,CAACT,MAAM,GAAGc,OAAO,CAACR,CAAC;IACjCP,KAAK,EAAEU,MAAM,CAACV,KAAK,GAAGe,OAAO,CAACT;EAChC,CAAC;AACH,CAAC;AAED,IAAIU,SAAS,GAAG;EACdjB,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;AACD,IAAIe,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,YAAY,GAAGF,KAAK,CAACG,MAAM;IAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGJ,SAAS,GAAGI,YAAY;IAC3DE,YAAY,GAAGJ,KAAK,CAACK,MAAM;IAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGN,SAAS,GAAGM,YAAY;IAC3DE,aAAa,GAAGN,KAAK,CAACO,OAAO;IAC7BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGR,SAAS,GAAGQ,aAAa;EAClE,IAAIE,SAAS,GAAG7B,OAAO,CAACY,MAAM,CAACU,SAAS,EAAEE,MAAM,CAAC,CAAC;EAClD,IAAIM,UAAU,GAAG9B,OAAO,CAACe,MAAM,CAACO,SAAS,EAAEI,MAAM,CAAC,CAAC;EACnD,IAAIK,UAAU,GAAG/B,OAAO,CAACe,MAAM,CAACe,UAAU,EAAEF,OAAO,CAAC,CAAC;EACrD,OAAO;IACLC,SAAS,EAAEA,SAAS;IACpBP,SAAS,EAAEtB,OAAO,CAACsB,SAAS,CAAC;IAC7BQ,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA,UAAU;IACtBP,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA;EACX,CAAC;AACH,CAAC;AAED,IAAII,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;EAC9B,IAAIC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5B,IAAIC,MAAM,GAAGH,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;EAE1B,IAAIC,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,CAAC;EACV;EAEA,IAAIC,MAAM,GAAGC,MAAM,CAACJ,KAAK,CAAC;EAC1B,CAAC,CAACK,KAAK,CAACF,MAAM,CAAC,GAAGG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3C,SAAS,CAAC,KAAK,EAAE,8BAA8B,GAAGkC,GAAG,GAAG,oBAAoB,GAAGC,KAAK,GAAG,GAAG,CAAC,GAAGnC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EACjL,OAAOsC,MAAM;AACf,CAAC;AAED,IAAIM,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EAC/C,OAAO;IACLlC,CAAC,EAAEmC,MAAM,CAACC,WAAW;IACrBnC,CAAC,EAAEkC,MAAM,CAACE;EACZ,CAAC;AACH,CAAC;AAED,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC7C,IAAI3B,SAAS,GAAG0B,QAAQ,CAAC1B,SAAS;IAC9BI,MAAM,GAAGsB,QAAQ,CAACtB,MAAM;IACxBF,MAAM,GAAGwB,QAAQ,CAACxB,MAAM;IACxBI,OAAO,GAAGoB,QAAQ,CAACpB,OAAO;EAC9B,IAAIsB,OAAO,GAAGjC,KAAK,CAACK,SAAS,EAAE2B,MAAM,CAAC;EACtC,OAAO7B,SAAS,CAAC;IACfE,SAAS,EAAE4B,OAAO;IAClBxB,MAAM,EAAEA,MAAM;IACdF,MAAM,EAAEA,MAAM;IACdI,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ,CAAC;AACD,IAAIuB,UAAU,GAAG,SAASA,UAAUA,CAACH,QAAQ,EAAEI,MAAM,EAAE;EACrD,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAGT,eAAe,CAAC,CAAC;EAC5B;EAEA,OAAOI,MAAM,CAACC,QAAQ,EAAEI,MAAM,CAAC;AACjC,CAAC;AACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAC/B,SAAS,EAAEgC,MAAM,EAAE;EAC1D,IAAI9B,MAAM,GAAG;IACXtB,GAAG,EAAE8B,KAAK,CAACsB,MAAM,CAACC,SAAS,CAAC;IAC5BpD,KAAK,EAAE6B,KAAK,CAACsB,MAAM,CAACE,WAAW,CAAC;IAChCpD,MAAM,EAAE4B,KAAK,CAACsB,MAAM,CAACG,YAAY,CAAC;IAClCpD,IAAI,EAAE2B,KAAK,CAACsB,MAAM,CAACI,UAAU;EAC/B,CAAC;EACD,IAAI9B,OAAO,GAAG;IACZ1B,GAAG,EAAE8B,KAAK,CAACsB,MAAM,CAACK,UAAU,CAAC;IAC7BxD,KAAK,EAAE6B,KAAK,CAACsB,MAAM,CAACM,YAAY,CAAC;IACjCxD,MAAM,EAAE4B,KAAK,CAACsB,MAAM,CAACO,aAAa,CAAC;IACnCxD,IAAI,EAAE2B,KAAK,CAACsB,MAAM,CAACQ,WAAW;EAChC,CAAC;EACD,IAAIpC,MAAM,GAAG;IACXxB,GAAG,EAAE8B,KAAK,CAACsB,MAAM,CAACS,cAAc,CAAC;IACjC5D,KAAK,EAAE6B,KAAK,CAACsB,MAAM,CAACU,gBAAgB,CAAC;IACrC5D,MAAM,EAAE4B,KAAK,CAACsB,MAAM,CAACW,iBAAiB,CAAC;IACvC5D,IAAI,EAAE2B,KAAK,CAACsB,MAAM,CAACY,eAAe;EACpC,CAAC;EACD,OAAO9C,SAAS,CAAC;IACfE,SAAS,EAAEA,SAAS;IACpBE,MAAM,EAAEA,MAAM;IACdI,OAAO,EAAEA,OAAO;IAChBF,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAIyC,MAAM,GAAG,SAASA,MAAMA,CAACC,EAAE,EAAE;EAC/B,IAAI9C,SAAS,GAAG8C,EAAE,CAACC,qBAAqB,CAAC,CAAC;EAC1C,IAAIf,MAAM,GAAGV,MAAM,CAAC0B,gBAAgB,CAACF,EAAE,CAAC;EACxC,OAAOf,YAAY,CAAC/B,SAAS,EAAEgC,MAAM,CAAC;AACxC,CAAC;AAED,SAASD,YAAY,EAAEjC,SAAS,EAAER,MAAM,EAAEuD,MAAM,EAAEnE,OAAO,EAAE+C,MAAM,EAAEhC,MAAM,EAAEoC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}