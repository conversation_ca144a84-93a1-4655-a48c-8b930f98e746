{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Dialog, DialogTitle, DialogContent, DialogActions, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination } from '@mui/material';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  row,\n  index,\n  columns,\n  onCellEdit\n}) => {\n  _s();\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n  const handleCellSave = columnField => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      handleCellSave(columnField);\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Draggable, {\n    draggableId: row.id.toString(),\n    index: index,\n    children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableRow, {\n      ref: provided.innerRef,\n      ...provided.draggableProps,\n      sx: {\n        backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n        transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n        boxShadow: snapshot.isDragging ? 3 : 0,\n        transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n        '&.removed-row': {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        },\n        ...(snapshot.isDragging && {\n          zIndex: 1000\n        }),\n        ...(row._removed && {\n          backgroundColor: 'rgba(211, 211, 211, 0.3)',\n          color: 'text.disabled',\n          textDecoration: 'line-through'\n        })\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        ...provided.dragHandleProps,\n        sx: {\n          width: 40,\n          cursor: 'grab',\n          '&:active': {\n            cursor: 'grabbing'\n          },\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n          sx: {\n            color: 'text.secondary'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), columns.map(column => {\n        const value = row[column.field];\n        const isEditing = editingCell === column.field;\n        return /*#__PURE__*/_jsxDEV(TableCell, {\n          sx: {\n            padding: '8px',\n            cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default'\n          },\n          onClick: () => handleCellClick(column.field, value),\n          children: isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            value: editValue,\n            onChange: e => setEditValue(e.target.value),\n            onBlur: () => handleCellSave(column.field),\n            onKeyDown: e => handleKeyPress(e, column.field),\n            autoFocus: true,\n            fullWidth: true,\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 19\n          }, this) : column.renderCell ? column.renderCell({\n            row,\n            value\n          }) : value\n        }, column.field, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 15\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n}, \"vUzEbioNkVLGJwl96b8AAiFyV68=\")), \"vUzEbioNkVLGJwl96b8AAiFyV68=\");\n\n// 默认的REMARKS选项\n_c2 = DraggableTableRow;\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c3 = _s2(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c4 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s3();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01',\n            'FEB': '02',\n            'MAR': '03',\n            'APR': '04',\n            'MAY': '05',\n            'JUNE': '06',\n            'JULY': '07',\n            'AUG': '08',\n            'SEP': '09',\n            'OCT': '10',\n            'NOV': '11',\n            'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback(result => {\n    if (!result.destination) {\n      return;\n    }\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = {\n            ...row,\n            [field]: newValue\n          };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n          return updatedRow;\n        }\n        return row;\n      });\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1183,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1189,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 17\n              }, this), \"\\u6570\\u636E\\u641C\\u7D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: searchColumn,\n                  label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n                  onChange: e => setSearchColumn(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u5168\\u90E8\\u5217\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1229,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"NO\",\n                    children: \"NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1230,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DATE\",\n                    children: \"DATE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"VEHICLE NO\",\n                    children: \"VEHICLE NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"RO NO\",\n                    children: \"RO NO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"KM\",\n                    children: \"KM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"REMARKS\",\n                    children: \"REMARKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"MAXCHECK\",\n                    children: \"HOURS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMMISSION\",\n                    children: \"AMOUNT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1237,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n                value: searchText,\n                onChange: e => setSearchText(e.target.value),\n                sx: {\n                  flexGrow: 1,\n                  minWidth: 200\n                },\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1250,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1249,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setSearchText(''),\n                      edge: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1260,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1255,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1254,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 17\n              }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                mb: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n                sx: {\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 17\n              }, this), \"\\u64CD\\u4F5C\\u9009\\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: {\n                xs: 'column',\n                sm: 'row'\n              },\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"success\",\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1286,\n                  columnNumber: 30\n                }, this),\n                onClick: handleDownload,\n                children: \"\\u4E0B\\u8F7DExcel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 53\n                }, this) : /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 102\n                }, this),\n                onClick: generateDocument,\n                disabled: isGeneratingDocument,\n                children: isGeneratingDocument ? '生成中...' : '生成Excel文档'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1305,\n                  columnNumber: 30\n                }, this),\n                onClick: handleCleanup,\n                children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragDropContext, {\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease'\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    width: 40,\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u62D6\\u62FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 19\n                }, this), columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'bold',\n                    backgroundColor: 'background.default',\n                    borderRight: '2px solid',\n                    borderColor: 'divider',\n                    '&:last-child': {\n                      borderRight: 'none'\n                    }\n                  },\n                  children: column.headerName\n                }, column.field, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1344,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Droppable, {\n              droppableId: \"table-body\",\n              children: (provided, snapshot) => /*#__PURE__*/_jsxDEV(TableBody, {\n                ref: provided.innerRef,\n                ...provided.droppableProps,\n                sx: {\n                  backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                  transition: 'background-color 0.2s ease',\n                  borderTop: '2px solid',\n                  borderColor: 'divider'\n                },\n                children: [memoGridData.filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                .slice(paginationModel.page * paginationModel.pageSize, (paginationModel.page + 1) * paginationModel.pageSize).map((row, index) => /*#__PURE__*/_jsxDEV(DraggableTableRow, {\n                  row: row,\n                  index: index,\n                  columns: columns,\n                  onCellEdit: handleCellEdit\n                }, row.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 25\n                }, this)), memoGridData.find(row => row.NO === 'TOTAL') && /*#__PURE__*/_jsxDEV(TableRow, {\n                  className: \"total-row\",\n                  sx: {\n                    backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                    fontWeight: 'bold',\n                    '& .MuiTableCell-root': {\n                      fontWeight: 'bold',\n                      borderTop: '2px solid',\n                      borderColor: 'primary.main'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    sx: {\n                      width: 40\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 25\n                  }, this), columns.map(column => {\n                    const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                    const value = totalRow ? totalRow[column.field] : '';\n                    return /*#__PURE__*/_jsxDEV(TableCell, {\n                      sx: {\n                        padding: '8px'\n                      },\n                      children: column.renderCell ? column.renderCell({\n                        row: totalRow,\n                        value\n                      }) : value\n                    }, column.field, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 29\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 23\n                }, this), provided.placeholder]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1364,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          component: \"div\",\n          count: memoGridData.filter(row => row.NO !== 'TOTAL').length,\n          page: paginationModel.page,\n          onPageChange: (event, newPage) => {\n            setPaginationModel(prev => ({\n              ...prev,\n              page: newPage\n            }));\n          },\n          rowsPerPage: paginationModel.pageSize,\n          onRowsPerPageChange: event => {\n            const newPageSize = parseInt(event.target.value, 10);\n            setPaginationModel({\n              page: 0,\n              pageSize: newPageSize\n            });\n            localStorage.setItem('dataGridPageSize', newPageSize.toString());\n          },\n          rowsPerPageOptions: [25, 50, 100],\n          labelRowsPerPage: \"\\u6BCF\\u9875\\u884C\\u6570:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} 共 ${count} 条`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1318,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1459,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1458,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1456,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1476,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1486,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1484,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1469,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1512,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1507,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1498,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1530,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1529,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1446,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1546,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1545,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1560,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1558,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1535,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1163,\n    columnNumber: 5\n  }, this);\n};\n_s3(ResultDisplay, \"+rDQw1c7ShjSx0IV4JJ0ismjYGA=\");\n_c5 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DraggableTableRow$React.memo\");\n$RefreshReg$(_c2, \"DraggableTableRow\");\n$RefreshReg$(_c3, \"RemarkChip$React.memo\");\n$RefreshReg$(_c4, \"RemarkChip\");\n$RefreshReg$(_c5, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "SettingsIcon", "axios", "API_URL", "FixedSizeList", "DragDropContext", "Droppable", "Draggable", "DragIndicatorIcon", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DraggableTableRow", "_s", "memo", "_c", "row", "index", "columns", "onCellEdit", "editingCell", "setEditingCell", "editValue", "setEditValue", "handleCellClick", "columnField", "currentValue", "column", "find", "col", "field", "editable", "isTotal", "_removed", "handleCellSave", "id", "handleKeyPress", "e", "key", "draggableId", "toString", "children", "provided", "snapshot", "ref", "innerRef", "draggableProps", "sx", "backgroundColor", "isDragging", "transform", "boxShadow", "transition", "color", "textDecoration", "zIndex", "dragHandleProps", "width", "cursor", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "value", "isEditing", "padding", "onClick", "size", "onChange", "target", "onBlur", "onKeyDown", "autoFocus", "fullWidth", "variant", "renderCell", "_c2", "DEFAULT_REMARKS_OPTIONS", "RemarkChip", "_s2", "_c3", "rowId", "text", "isSelected", "uiState", "setUiState", "handleClick", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "height", "lineHeight", "_c4", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s3", "columnOrder", "headerName", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "getSelectedMonth", "commissionState", "selectedWorksheet", "match", "monthName", "year", "monthMap", "monthNumber", "toUpperCase", "error", "console", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "debouncedSaveToLocalStorage", "setItem", "stringify", "log", "length", "debouncedNotifyParent", "recalculateTotal", "totalRow", "NO", "newTotal", "filter", "reduce", "sum", "Number", "COMMISSION", "handleDragEnd", "result", "destination", "sourceIndex", "source", "destinationIndex", "setGridData", "prev", "newData", "nonTotalRows", "removed", "splice", "for<PERSON>ach", "reorderedData", "handleCellEdit", "newValue", "updatedData", "updatedRow", "numericFields", "includes", "undefined", "numValue", "isNaN", "timer", "setPaginationModel", "page", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "paginationModel", "saved", "pageSize", "parseInt", "originalData", "setOriginalData", "processedData", "REMARKS", "_selected_remarks", "gridData", "validatedData", "processedWithIds", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "getKeyData", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "deleteOption", "item", "handleRemoveRow", "noCounter", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "newRow", "DATE", "KM", "MAXCHECK", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "style", "display", "src", "Error", "handleRemarksClick", "hasOwnProperty", "flex", "params", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "startIcon", "fontWeight", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "memoGridData", "dataLength", "py", "mt", "mb", "justifyContent", "flexWrap", "direction", "spacing", "icon", "container", "xs", "md", "sm", "placeholder", "flexGrow", "InputProps", "startAdornment", "position", "endAdornment", "edge", "disabled", "onDragEnd", "borderBottom", "borderRight", "borderColor", "<PERSON><PERSON><PERSON><PERSON>", "droppableId", "droppableProps", "isDraggingOver", "borderTop", "slice", "className", "component", "count", "onPageChange", "event", "newPage", "rowsPerPage", "onRowsPerPageChange", "newPageSize", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "onClose", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "margin", "type", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel,\n  Grid,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination\n} from '@mui/material';\n\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\n\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';\nimport DragIndicatorIcon from '@mui/icons-material/DragIndicator';\n\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 拖拽表格行组件\nconst DraggableTableRow = React.memo(({ row, index, columns, onCellEdit }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [editValue, setEditValue] = useState('');\n\n  const handleCellClick = (columnField, currentValue) => {\n    // 检查是否可编辑\n    const column = columns.find(col => col.field === columnField);\n    if (!column || !column.editable || row.isTotal || row._removed || columnField === 'NO') {\n      return;\n    }\n\n    setEditingCell(columnField);\n    setEditValue(currentValue || '');\n  };\n\n  const handleCellSave = (columnField) => {\n    if (onCellEdit) {\n      onCellEdit(row.id, columnField, editValue);\n    }\n    setEditingCell(null);\n    setEditValue('');\n  };\n\n  const handleKeyPress = (e, columnField) => {\n    if (e.key === 'Enter') {\n      handleCellSave(columnField);\n    } else if (e.key === 'Escape') {\n      setEditingCell(null);\n      setEditValue('');\n    }\n  };\n\n  return (\n    <Draggable draggableId={row.id.toString()} index={index}>\n      {(provided, snapshot) => (\n        <TableRow\n          ref={provided.innerRef}\n          {...provided.draggableProps}\n          sx={{\n            backgroundColor: snapshot.isDragging ? 'action.hover' : 'inherit',\n            transform: snapshot.isDragging ? 'rotate(2deg)' : 'none',\n            boxShadow: snapshot.isDragging ? 3 : 0,\n            transition: snapshot.isDragging ? 'none' : 'all 0.2s ease',\n            '&.removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            },\n            ...(snapshot.isDragging && {\n              zIndex: 1000,\n            }),\n            ...(row._removed && {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through',\n            }),\n          }}\n        >\n          {/* 拖拽手柄 */}\n          <TableCell\n            {...provided.dragHandleProps}\n            sx={{\n              width: 40,\n              cursor: 'grab',\n              '&:active': { cursor: 'grabbing' },\n              textAlign: 'center',\n            }}\n          >\n            <DragIndicatorIcon sx={{ color: 'text.secondary' }} />\n          </TableCell>\n\n          {/* 数据列 */}\n          {columns.map((column) => {\n            const value = row[column.field];\n            const isEditing = editingCell === column.field;\n\n            return (\n              <TableCell\n                key={column.field}\n                sx={{\n                  padding: '8px',\n                  cursor: column.editable && !row.isTotal && !row._removed && column.field !== 'NO' ? 'pointer' : 'default',\n                }}\n                onClick={() => handleCellClick(column.field, value)}\n              >\n                {isEditing ? (\n                  <TextField\n                    size=\"small\"\n                    value={editValue}\n                    onChange={(e) => setEditValue(e.target.value)}\n                    onBlur={() => handleCellSave(column.field)}\n                    onKeyDown={(e) => handleKeyPress(e, column.field)}\n                    autoFocus\n                    fullWidth\n                    variant=\"outlined\"\n                  />\n                ) : (\n                  column.renderCell ? column.renderCell({ row, value }) : value\n                )}\n              </TableCell>\n            );\n          })}\n        </TableRow>\n      )}\n    </Draggable>\n  );\n});\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: false, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 从commissionState中提取月份信息\n  const getSelectedMonth = () => {\n    try {\n      const commissionState = JSON.parse(localStorage.getItem('commissionState') || '{}');\n      const selectedWorksheet = commissionState.selectedWorksheet;\n\n      if (selectedWorksheet) {\n        // 解析工作表名称，例如 \"JUNE'2025\" -> \"2025-06\"\n        const match = selectedWorksheet.match(/(\\w+)'(\\d{4})/);\n        if (match) {\n          const [, monthName, year] = match;\n          const monthMap = {\n            'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',\n            'MAY': '05', 'JUNE': '06', 'JULY': '07', 'AUG': '08',\n            'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'\n          };\n          const monthNumber = monthMap[monthName.toUpperCase()];\n          if (monthNumber) {\n            return `${year}-${monthNumber}`;\n          }\n        }\n      }\n    } catch (error) {\n      console.error('解析commissionState失败:', error);\n    }\n\n    // 如果解析失败，返回当前月份\n    const now = new Date();\n    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n  };\n\n  const selectedMonth = getSelectedMonth();\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // 重新计算总计函数\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 拖拽处理函数\n  const handleDragEnd = useCallback((result) => {\n    if (!result.destination) {\n      return;\n    }\n\n    const sourceIndex = result.source.index;\n    const destinationIndex = result.destination.index;\n\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    setGridData(prev => {\n      const newData = [...prev];\n\n      // 找到非TOTAL行的索引映射\n      const nonTotalRows = newData.filter(row => row.NO !== 'TOTAL');\n      const totalRow = newData.find(row => row.NO === 'TOTAL');\n\n      // 重新排序非TOTAL行\n      const [removed] = nonTotalRows.splice(sourceIndex, 1);\n      nonTotalRows.splice(destinationIndex, 0, removed);\n\n      // 重新编号\n      nonTotalRows.forEach((row, index) => {\n        if (typeof row.NO === 'number') {\n          row.NO = index + 1;\n        }\n      });\n\n      // 重新组合数据（非TOTAL行 + TOTAL行）\n      const reorderedData = totalRow ? [...nonTotalRows, totalRow] : nonTotalRows;\n\n      console.log('行拖拽重排序完成');\n      return recalculateTotal(reorderedData);\n    });\n  }, [recalculateTotal]);\n\n  // 处理单元格编辑\n  const handleCellEdit = useCallback((rowId, field, newValue) => {\n    setGridData(prev => {\n      const updatedData = prev.map(row => {\n        if (row.id === rowId) {\n          const updatedRow = { ...row, [field]: newValue };\n\n          // 保持数字字段的正确类型\n          const numericFields = ['NO', 'RO NO', 'KM', 'MAXCHECK', 'COMMISSION', 'HOUR_RATE', 'COMMISSION_RATE'];\n          if (numericFields.includes(field) && newValue !== undefined && newValue !== null && newValue !== '') {\n            if (typeof newValue === 'string') {\n              const numValue = Number(newValue);\n              if (!isNaN(numValue)) {\n                updatedRow[field] = numValue;\n              }\n            }\n          }\n\n          return updatedRow;\n        }\n        return row;\n      });\n\n      const result = recalculateTotal(updatedData);\n\n      // 保存到localStorage和通知父组件\n      debouncedSaveToLocalStorage(result);\n      debouncedNotifyParent(result);\n\n      return result;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPaginationModel(prev => ({ ...prev, page: 0 }));\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态 - 使用新的paginationModel格式\n  const [paginationModel, setPaginationModel] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return {\n      page: 0,\n      pageSize: saved ? parseInt(saved, 10) : 25\n    };\n  });\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', paginationModel.pageSize.toString());\n    console.log('分页大小已保存:', paginationModel.pageSize);\n  }, [paginationModel.pageSize]);\n\n\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：1000ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 1000);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 记录更新日志\n        setTimeout(() => {\n          console.log('REMARKS已更新');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      console.log('新选项已添加');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      console.log('该选项已存在');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    console.log('选项已删除');\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    console.log('行已移除并重新编号');\n  }, [recalculateTotal]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      console.log('行已恢复并重新编号');\n    }, 0);\n  }, [recalculateTotal]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('行已永久删除');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      console.log('新行已添加');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId,\n        selectedMonth: selectedMonth\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 记录成功消息\n        console.log('文档已生成，正在下载...');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      console.error('生成文档失败，请重试');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // 调试日志 - 在memoGridData定义之后\n  useEffect(() => {\n    console.log('当前分页状态:', {\n      pageSize: paginationModel.pageSize,\n      page: paginationModel.page,\n      dataLength: memoGridData.length\n    });\n  }, [paginationModel.pageSize, paginationModel.page, memoGridData.length]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 处理结果概览 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 数据搜索和操作选项 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Grid container spacing={3}>\n            {/* 数据搜索 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SearchIcon sx={{ color: 'primary.main' }} />\n                数据搜索\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n                <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n                  <InputLabel>搜索范围</InputLabel>\n                  <Select\n                    value={searchColumn}\n                    label=\"搜索范围\"\n                    onChange={(e) => setSearchColumn(e.target.value)}\n                  >\n                    <MenuItem value=\"all\">全部列</MenuItem>\n                    <MenuItem value=\"NO\">NO</MenuItem>\n                    <MenuItem value=\"DATE\">DATE</MenuItem>\n                    <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                    <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                    <MenuItem value=\"KM\">KM</MenuItem>\n                    <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                    <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                    <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n                  </Select>\n                </FormControl>\n\n                <TextField\n                  size=\"small\"\n                  placeholder=\"输入搜索内容...\"\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  sx={{ flexGrow: 1, minWidth: 200 }}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    ),\n                    endAdornment: searchText && (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => setSearchText('')}\n                          edge=\"end\"\n                        >\n                          <ClearIcon />\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {searchText && (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n                  </Typography>\n                )}\n              </Stack>\n            </Grid>\n\n            {/* 操作选项 */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <SettingsIcon sx={{ color: 'primary.main' }} />\n                操作选项\n              </Typography>\n\n              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>\n                <Button\n                  variant=\"contained\"\n                  color=\"success\"\n                  startIcon={<DownloadIcon />}\n                  onClick={handleDownload}\n                >\n                  下载Excel\n                </Button>\n\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <TableViewIcon />}\n                  onClick={generateDocument}\n                  disabled={isGeneratingDocument}\n                >\n                  {isGeneratingDocument ? '生成中...' : '生成Excel文档'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<RestartAltIcon />}\n                  onClick={handleCleanup}\n                >\n                  重新开始\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n      \n      {/* 拖拽数据表格 */}\n      <DragDropContext onDragEnd={handleDragEnd}>\n        <Paper sx={{\n          width: '100%',\n          overflow: 'hidden',\n          '& .dragging-row': {\n            transform: 'rotate(2deg)',\n            boxShadow: 3,\n            zIndex: 1000,\n          },\n          '& .MuiTableRow-root': {\n            transition: 'all 0.2s ease',\n          },\n          '& .MuiTableCell-root': {\n            borderBottom: '2px solid',\n            borderRight: '2px solid',\n            borderColor: 'divider',\n          },\n        }}>\n          <TableContainer>\n            <Table stickyHeader>\n              <TableHead>\n                <TableRow>\n                  {/* 拖拽手柄列 */}\n                  <TableCell sx={{ width: 40, fontWeight: 'bold' }}>\n                    拖拽\n                  </TableCell>\n                  {columns.map((column) => (\n                    <TableCell\n                      key={column.field}\n                      sx={{\n                        fontWeight: 'bold',\n                        backgroundColor: 'background.default',\n                        borderRight: '2px solid',\n                        borderColor: 'divider',\n                        '&:last-child': {\n                          borderRight: 'none',\n                        },\n                      }}\n                    >\n                      {column.headerName}\n                    </TableCell>\n                  ))}\n                </TableRow>\n              </TableHead>\n\n              <Droppable droppableId=\"table-body\">\n                {(provided, snapshot) => (\n                  <TableBody\n                    ref={provided.innerRef}\n                    {...provided.droppableProps}\n                    sx={{\n                      backgroundColor: snapshot.isDraggingOver ? 'action.hover' : 'inherit',\n                      transition: 'background-color 0.2s ease',\n                      borderTop: '2px solid',\n                      borderColor: 'divider',\n                    }}\n                  >\n                    {/* 只显示当前页的数据 */}\n                    {memoGridData\n                      .filter(row => row.NO !== 'TOTAL') // 过滤掉TOTAL行，单独处理\n                      .slice(\n                        paginationModel.page * paginationModel.pageSize,\n                        (paginationModel.page + 1) * paginationModel.pageSize\n                      )\n                      .map((row, index) => (\n                        <DraggableTableRow\n                          key={row.id}\n                          row={row}\n                          index={index}\n                          columns={columns}\n                          onCellEdit={handleCellEdit}\n                        />\n                      ))}\n\n                    {/* TOTAL行单独显示在最后 */}\n                    {memoGridData.find(row => row.NO === 'TOTAL') && (\n                      <TableRow className=\"total-row\" sx={{\n                        backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                        fontWeight: 'bold',\n                        '& .MuiTableCell-root': {\n                          fontWeight: 'bold',\n                          borderTop: '2px solid',\n                          borderColor: 'primary.main',\n                        },\n                      }}>\n                        <TableCell sx={{ width: 40 }}>\n                          {/* TOTAL行不显示拖拽手柄 */}\n                        </TableCell>\n                        {columns.map((column) => {\n                          const totalRow = memoGridData.find(row => row.NO === 'TOTAL');\n                          const value = totalRow ? totalRow[column.field] : '';\n                          return (\n                            <TableCell key={column.field} sx={{ padding: '8px' }}>\n                              {column.renderCell ? column.renderCell({ row: totalRow, value }) : value}\n                            </TableCell>\n                          );\n                        })}\n                      </TableRow>\n                    )}\n\n                    {provided.placeholder}\n                  </TableBody>\n                )}\n              </Droppable>\n            </Table>\n          </TableContainer>\n\n          {/* 分页控件 */}\n          <TablePagination\n            component=\"div\"\n            count={memoGridData.filter(row => row.NO !== 'TOTAL').length}\n            page={paginationModel.page}\n            onPageChange={(event, newPage) => {\n              setPaginationModel(prev => ({ ...prev, page: newPage }));\n            }}\n            rowsPerPage={paginationModel.pageSize}\n            onRowsPerPageChange={(event) => {\n              const newPageSize = parseInt(event.target.value, 10);\n              setPaginationModel({ page: 0, pageSize: newPageSize });\n              localStorage.setItem('dataGridPageSize', newPageSize.toString());\n            }}\n            rowsPerPageOptions={[25, 50, 100]}\n            labelRowsPerPage=\"每页行数:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}\n          />\n        </Paper>\n      </DragDropContext>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,QACV,eAAe;AAEtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,eAAe,EAAEC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACzE,OAAOC,iBAAiB,MAAM,mCAAmC;;AAGjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,iBAAiB,gBAAAC,EAAA,cAAGxE,KAAK,CAACyE,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,GAAG;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAN,EAAA;EAC5E,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMkF,eAAe,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACrD;IACA,MAAMC,MAAM,GAAGT,OAAO,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,WAAW,CAAC;IAC7D,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACI,QAAQ,IAAIf,GAAG,CAACgB,OAAO,IAAIhB,GAAG,CAACiB,QAAQ,IAAIR,WAAW,KAAK,IAAI,EAAE;MACtF;IACF;IAEAJ,cAAc,CAACI,WAAW,CAAC;IAC3BF,YAAY,CAACG,YAAY,IAAI,EAAE,CAAC;EAClC,CAAC;EAED,MAAMQ,cAAc,GAAIT,WAAW,IAAK;IACtC,IAAIN,UAAU,EAAE;MACdA,UAAU,CAACH,GAAG,CAACmB,EAAE,EAAEV,WAAW,EAAEH,SAAS,CAAC;IAC5C;IACAD,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACC,CAAC,EAAEZ,WAAW,KAAK;IACzC,IAAIY,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBJ,cAAc,CAACT,WAAW,CAAC;IAC7B,CAAC,MAAM,IAAIY,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BjB,cAAc,CAAC,IAAI,CAAC;MACpBE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EAED,oBACErB,OAAA,CAACH,SAAS;IAACwC,WAAW,EAAEvB,GAAG,CAACmB,EAAE,CAACK,QAAQ,CAAC,CAAE;IAACvB,KAAK,EAAEA,KAAM;IAAAwB,QAAA,EACrDA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBzC,OAAA,CAACvB,QAAQ;MACPiE,GAAG,EAAEF,QAAQ,CAACG,QAAS;MAAA,GACnBH,QAAQ,CAACI,cAAc;MAC3BC,EAAE,EAAE;QACFC,eAAe,EAAEL,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,SAAS;QACjEC,SAAS,EAAEP,QAAQ,CAACM,UAAU,GAAG,cAAc,GAAG,MAAM;QACxDE,SAAS,EAAER,QAAQ,CAACM,UAAU,GAAG,CAAC,GAAG,CAAC;QACtCG,UAAU,EAAET,QAAQ,CAACM,UAAU,GAAG,MAAM,GAAG,eAAe;QAC1D,eAAe,EAAE;UACfD,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;QACD,IAAIX,QAAQ,CAACM,UAAU,IAAI;UACzBM,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAIvC,GAAG,CAACiB,QAAQ,IAAI;UAClBe,eAAe,EAAE,0BAA0B;UAC3CK,KAAK,EAAE,eAAe;UACtBC,cAAc,EAAE;QAClB,CAAC;MACH,CAAE;MAAAb,QAAA,gBAGFvC,OAAA,CAAC1B,SAAS;QAAA,GACJkE,QAAQ,CAACc,eAAe;QAC5BT,EAAE,EAAE;UACFU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YAAEA,MAAM,EAAE;UAAW,CAAC;UAClCC,SAAS,EAAE;QACb,CAAE;QAAAlB,QAAA,eAEFvC,OAAA,CAACF,iBAAiB;UAAC+C,EAAE,EAAE;YAAEM,KAAK,EAAE;UAAiB;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAGX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,IAAK;QACvB,MAAMsC,KAAK,GAAGjD,GAAG,CAACW,MAAM,CAACG,KAAK,CAAC;QAC/B,MAAMoC,SAAS,GAAG9C,WAAW,KAAKO,MAAM,CAACG,KAAK;QAE9C,oBACE5B,OAAA,CAAC1B,SAAS;UAERuE,EAAE,EAAE;YACFoB,OAAO,EAAE,KAAK;YACdT,MAAM,EAAE/B,MAAM,CAACI,QAAQ,IAAI,CAACf,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,IAAIN,MAAM,CAACG,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG;UAClG,CAAE;UACFsC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAACG,MAAM,CAACG,KAAK,EAAEmC,KAAK,CAAE;UAAAxB,QAAA,EAEnDyB,SAAS,gBACRhE,OAAA,CAAC5C,SAAS;YACR+G,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAE3C,SAAU;YACjBgD,QAAQ,EAAGjC,CAAC,IAAKd,YAAY,CAACc,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;YAC9CO,MAAM,EAAEA,CAAA,KAAMtC,cAAc,CAACP,MAAM,CAACG,KAAK,CAAE;YAC3C2C,SAAS,EAAGpC,CAAC,IAAKD,cAAc,CAACC,CAAC,EAAEV,MAAM,CAACG,KAAK,CAAE;YAClD4C,SAAS;YACTC,SAAS;YACTC,OAAO,EAAC;UAAU;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,GAEFpC,MAAM,CAACkD,UAAU,GAAGlD,MAAM,CAACkD,UAAU,CAAC;YAAE7D,GAAG;YAAEiD;UAAM,CAAC,CAAC,GAAGA;QACzD,GApBItC,MAAM,CAACG,KAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBR,CAAC;MAEhB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC,kCAAC;;AAEF;AAAAe,GAAA,GA5GMlE,iBAAiB;AA6GvB,MAAMmE,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAID;AACA,MAAMC,UAAU,gBAAAC,GAAA,cAAG5I,KAAK,CAACyE,IAAI,CAAAoE,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEjB;AAAQ,CAAC,KAAK;EAAAa,GAAA;EACtE;EACA,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGjJ,QAAQ,CAAC;IAErC8I,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA9I,SAAS,CAAC,MAAM;IACdgJ,UAAU,CAAC;MACTH,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMG,WAAW,GAAInD,CAAC,IAAK;IACzB+B,OAAO,CAACe,KAAK,CAAC;EAChB,CAAC;EAED,oBACEjF,OAAA,CAACrD,MAAM;IAELuH,OAAO,EAAEoB,WAAY;IACrBZ,OAAO,EAAEU,OAAO,CAACD,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDhC,KAAK,EAAC,SAAS;IACfgB,IAAI,EAAC,OAAO;IACZtB,EAAE,EAAE;MACF0C,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpB3C,UAAU,EAAE,sBAAsB;MAClC4C,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAxD,QAAA,EAED6C,OAAO,CAACF,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAIG,OAAO,CAACD,UAAU,EAAE;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAACmC,GAAA,GA5CGlB,UAAU;AA8ChB,MAAMmB,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAE7E,KAAK,EAAE,IAAI;IAAE8E,UAAU,EAAE,IAAI;IAAE7E,QAAQ,EAAE,KAAK;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACvE;IAAE/E,KAAK,EAAE,MAAM;IAAE8E,UAAU,EAAE,MAAM;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAE/E,KAAK,EAAE,YAAY;IAAE8E,UAAU,EAAE,YAAY;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACtF;IAAE/E,KAAK,EAAE,OAAO;IAAE8E,UAAU,EAAE,OAAO;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAE/E,KAAK,EAAE,IAAI;IAAE8E,UAAU,EAAE,IAAI;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACtE;IAAE/E,KAAK,EAAE,SAAS;IAAE8E,UAAU,EAAE,SAAS;IAAE7E,QAAQ,EAAE,KAAK;IAAE8E,WAAW,EAAE;EAAO,CAAC,EACjF;IAAE/E,KAAK,EAAE,UAAU;IAAE8E,UAAU,EAAE,OAAO;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAE/E,KAAK,EAAE,YAAY;IAAE8E,UAAU,EAAE,QAAQ;IAAE7E,QAAQ,EAAE,IAAI;IAAE8E,WAAW,EAAE;EAAO,CAAC,EAClF;IAAE/E,KAAK,EAAE,QAAQ;IAAE8E,UAAU,EAAE,QAAQ;IAAE7E,QAAQ,EAAE,KAAK;IAAE8E,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzK,QAAQ,CAAC,MAAM;IACzD,MAAM0K,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGjC,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACiL,eAAe,EAAEC,kBAAkB,CAAC,GAAGlL,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpL,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAMqL,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACF,MAAMC,eAAe,GAAGT,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACnF,MAAMW,iBAAiB,GAAGD,eAAe,CAACC,iBAAiB;MAE3D,IAAIA,iBAAiB,EAAE;QACrB;QACA,MAAMC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAAC,eAAe,CAAC;QACtD,IAAIA,KAAK,EAAE;UACT,MAAM,GAAGC,SAAS,EAAEC,IAAI,CAAC,GAAGF,KAAK;UACjC,MAAMG,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAClD,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YACpD,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE;UAChD,CAAC;UACD,MAAMC,WAAW,GAAGD,QAAQ,CAACF,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC;UACrD,IAAID,WAAW,EAAE;YACf,OAAO,GAAGF,IAAI,IAAIE,WAAW,EAAE;UACjC;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;;IAEA;IACA,MAAME,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,OAAO,GAAGD,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9E,CAAC;EAED,MAAMC,aAAa,GAAGjB,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGxM,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1M,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC2M,YAAY,EAAEC,eAAe,CAAC,GAAG5M,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM6M,2BAA2B,GAAG3M,WAAW,CAC7C2D,QAAQ,CAAEiG,IAAI,IAAK;IACjB,IAAI;MACFa,YAAY,CAACmC,OAAO,CAAC,eAAe,EAAEjC,IAAI,CAACkC,SAAS,CAACjD,IAAI,CAAC,CAAC;MAC3DiC,OAAO,CAACiB,GAAG,CAAC,sBAAsB,EAAElD,IAAI,CAACmD,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMoB,qBAAqB,GAAGhN,WAAW,CACvC2D,QAAQ,CAAEiG,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvBiC,OAAO,CAACiB,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAAC7C,YAAY,CACf,CAAC;;EAED;EACA,MAAMgD,gBAAgB,GAAGjN,WAAW,CAAE4J,IAAI,IAAK;IAC7C,MAAMsD,QAAQ,GAAGtD,IAAI,CAACxE,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC;IACrD,IAAID,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGxD,IAAI,CAClByD,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,CAAC,CAClD6H,MAAM,CAAC,CAACC,GAAG,EAAE/I,GAAG,KAAK+I,GAAG,IAAIC,MAAM,CAAChJ,GAAG,CAACiJ,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DP,QAAQ,CAACO,UAAU,GAAGL,QAAQ;IAChC;IACA,OAAOxD,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8D,aAAa,GAAG1N,WAAW,CAAE2N,MAAM,IAAK;IAC5C,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;MACvB;IACF;IAEA,MAAMC,WAAW,GAAGF,MAAM,CAACG,MAAM,CAACrJ,KAAK;IACvC,MAAMsJ,gBAAgB,GAAGJ,MAAM,CAACC,WAAW,CAACnJ,KAAK;IAEjD,IAAIoJ,WAAW,KAAKE,gBAAgB,EAAE;MACpC;IACF;IAEAC,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;;MAEzB;MACA,MAAME,YAAY,GAAGD,OAAO,CAACb,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC;MAC9D,MAAMD,QAAQ,GAAGgB,OAAO,CAAC9I,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC;;MAExD;MACA,MAAM,CAACiB,OAAO,CAAC,GAAGD,YAAY,CAACE,MAAM,CAACR,WAAW,EAAE,CAAC,CAAC;MACrDM,YAAY,CAACE,MAAM,CAACN,gBAAgB,EAAE,CAAC,EAAEK,OAAO,CAAC;;MAEjD;MACAD,YAAY,CAACG,OAAO,CAAC,CAAC9J,GAAG,EAAEC,KAAK,KAAK;QACnC,IAAI,OAAOD,GAAG,CAAC2I,EAAE,KAAK,QAAQ,EAAE;UAC9B3I,GAAG,CAAC2I,EAAE,GAAG1I,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM8J,aAAa,GAAGrB,QAAQ,GAAG,CAAC,GAAGiB,YAAY,EAAEjB,QAAQ,CAAC,GAAGiB,YAAY;MAE3EtC,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;MACvB,OAAOG,gBAAgB,CAACsB,aAAa,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMuB,cAAc,GAAGxO,WAAW,CAAC,CAAC2I,KAAK,EAAErD,KAAK,EAAEmJ,QAAQ,KAAK;IAC7DT,WAAW,CAACC,IAAI,IAAI;MAClB,MAAMS,WAAW,GAAGT,IAAI,CAACzG,GAAG,CAAChD,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACmB,EAAE,KAAKgD,KAAK,EAAE;UACpB,MAAMgG,UAAU,GAAG;YAAE,GAAGnK,GAAG;YAAE,CAACc,KAAK,GAAGmJ;UAAS,CAAC;;UAEhD;UACA,MAAMG,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;UACrG,IAAIA,aAAa,CAACC,QAAQ,CAACvJ,KAAK,CAAC,IAAImJ,QAAQ,KAAKK,SAAS,IAAIL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;YACnG,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAChC,MAAMM,QAAQ,GAAGvB,MAAM,CAACiB,QAAQ,CAAC;cACjC,IAAI,CAACO,KAAK,CAACD,QAAQ,CAAC,EAAE;gBACpBJ,UAAU,CAACrJ,KAAK,CAAC,GAAGyJ,QAAQ;cAC9B;YACF;UACF;UAEA,OAAOJ,UAAU;QACnB;QACA,OAAOnK,GAAG;MACZ,CAAC,CAAC;MAEF,MAAMmJ,MAAM,GAAGV,gBAAgB,CAACyB,WAAW,CAAC;;MAE5C;MACA/B,2BAA2B,CAACgB,MAAM,CAAC;MACnCX,qBAAqB,CAACW,MAAM,CAAC;MAE7B,OAAOA,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACAjN,SAAS,CAAC,MAAM;IACd,MAAMkP,KAAK,GAAG9K,UAAU,CAAC,MAAM;MAC7BqI,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMnI,YAAY,CAAC+K,KAAK,CAAC;EAClC,CAAC,EAAE,CAAC5C,UAAU,CAAC,CAAC;;EAEhB;EACAtM,SAAS,CAAC,MAAM;IACdmP,kBAAkB,CAACjB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEkB,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAAC5C,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAGvP,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwP,UAAU,EAAEC,aAAa,CAAC,GAAGzP,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC0P,eAAe,EAAEN,kBAAkB,CAAC,GAAGpP,QAAQ,CAAC,MAAM;IAC3D,MAAM2P,KAAK,GAAGhF,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO;MACLyE,IAAI,EAAE,CAAC;MACPO,QAAQ,EAAED,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,GAAG;IAC1C,CAAC;EACH,CAAC,CAAC;;EAEF;EACA1P,SAAS,CAAC,MAAM;IACd0K,YAAY,CAACmC,OAAO,CAAC,kBAAkB,EAAE4C,eAAe,CAACE,QAAQ,CAAC1J,QAAQ,CAAC,CAAC,CAAC;IAC7E6F,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAE0C,eAAe,CAACE,QAAQ,CAAC;EACnD,CAAC,EAAE,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAC;;EAI9B;EACA,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG/P,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd0K,YAAY,CAACmC,OAAO,CAAC,gBAAgB,EAAEjC,IAAI,CAACkC,SAAS,CAACvC,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMwF,aAAa,GAAG,CAAClG,IAAI,IAAI,EAAE,EAAEpC,GAAG,CAAChD,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEuL,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEvK,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACwK,QAAQ,EAAEjC,WAAW,CAAC,GAAGlO,QAAQ,CAAC,MAAM;IAC7C+L,OAAO,CAACiB,GAAG,CAAC,mCAAmC,EAC7C9C,aAAa,GAAG,IAAIA,aAAa,CAAC+C,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI/C,aAAa,IAAIA,aAAa,CAAC+C,MAAM,GAAG,CAAC,EAAE;MAC7ClB,OAAO,CAACiB,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMoD,aAAa,GAAGlG,aAAa,CAACxC,GAAG,CAAChD,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACiB,QAAQ,KAAKqJ,SAAS,EAAE;UAC9BtK,GAAG,CAACiB,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIjB,GAAG,CAACwL,iBAAiB,KAAKlB,SAAS,EAAE;UACvCtK,GAAG,CAACwL,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOxL,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM2L,gBAAgB,GAAGL,aAAa,CAACtI,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;QAC1D,GAAGD,GAAG;QACNmB,EAAE,EAAElB,KAAK;QACTe,OAAO,EAAEhB,GAAG,CAAC2I,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACH0C,eAAe,CAAC,CAAC,GAAGM,gBAAgB,CAAC,CAAC;MAEtC,OAAOD,aAAa;IACtB;;IAEA;IACArE,OAAO,CAACiB,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMqD,gBAAgB,GAAGL,aAAa,CAACtI,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;MAC1D,GAAGD,GAAG;MACNmB,EAAE,EAAElB,KAAK;MACTe,OAAO,EAAEhB,GAAG,CAAC2I,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACH0C,eAAe,CAAC,CAAC,GAAGM,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGnQ,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMoQ,iBAAiB,GAAGpQ,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMqQ,gBAAgB,GAAGrQ,MAAM,CAAC,IAAI,CAAC;;EAIrC;EACA,MAAMsQ,UAAU,GAAI3G,IAAI,IAAKA,IAAI,CAACpC,GAAG,CAAChD,GAAG,KAAK;IAC5CmB,EAAE,EAAEnB,GAAG,CAACmB,EAAE;IACVwH,EAAE,EAAE3I,GAAG,CAAC2I,EAAE;IACV1H,QAAQ,EAAEjB,GAAG,CAACiB,QAAQ;IACtBsK,OAAO,EAAEvL,GAAG,CAACuL,OAAO;IACpBC,iBAAiB,EAAExL,GAAG,CAACwL,iBAAiB;IACxCvC,UAAU,EAAEjJ,GAAG,CAACiJ;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA1N,SAAS,CAAC,MAAM;IACd,IAAIkK,YAAY,IAAIgG,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMyD,OAAO,GAAG7F,IAAI,CAACkC,SAAS,CAAC0D,UAAU,CAACN,QAAQ,CAAC,CAAC;MACpD,MAAMQ,WAAW,GAAGL,mBAAmB,CAACM,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIF,gBAAgB,CAACI,OAAO,EAAE;UAC5BxM,YAAY,CAACoM,gBAAgB,CAACI,OAAO,CAAC;QACxC;QACAJ,gBAAgB,CAACI,OAAO,GAAGvM,UAAU,CAAC,MAAM;UAC1CiM,mBAAmB,CAACM,OAAO,GAAGF,OAAO;UACrCH,iBAAiB,CAACK,OAAO,GAAG3E,IAAI,CAACD,GAAG,CAAC,CAAC;UACtC7B,YAAY,CAAC,CAAC,GAAGgG,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IACA,OAAO,MAAM;MACX,IAAIK,gBAAgB,CAACI,OAAO,EAAE;QAC5BxM,YAAY,CAACoM,gBAAgB,CAACI,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACT,QAAQ,EAAEhG,YAAY,CAAC,CAAC;EAE5B,MAAM,CAAC0G,aAAa,EAAEC,gBAAgB,CAAC,GAAG9Q,QAAQ,CAAC;IACjD+Q,IAAI,EAAE,KAAK;IACXlI,KAAK,EAAE,IAAI;IACXzD,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAnF,SAAS,CAAC,MAAM;IACd,IAAI6P,YAAY,CAAC7C,MAAM,KAAK,CAAC,IAAIkD,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAE;MACpDlB,OAAO,CAACiB,GAAG,CAAC,sBAAsB,EAAEmD,QAAQ,CAAClD,MAAM,CAAC;MACpD8C,eAAe,CAAC,CAAC,GAAGI,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEL,YAAY,CAAC,CAAC;EAI5B,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAIjH,MAAM,IAAIA,MAAM,CAACkH,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7ChH,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAMiH,WAAW,GAAG,GAAG7N,OAAO,aAAa0G,MAAM,EAAE;MACnD,MAAMoH,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAItF,IAAI,CAAC,CAAC,CAACuF,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAEjC,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7B,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM4H,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMzO,KAAK,CAAC0O,MAAM,CAAC,GAAGzO,OAAO,YAAY0G,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA9B,OAAO,CAAC,CAAC;EACX,CAAC;;EAID;EACA,MAAM+H,kBAAkB,GAAG7R,WAAW,CAAC,CAAC4J,IAAI,EAAEkI,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGnI,IAAI,IAAIqG,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC+B,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACb1E,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,CAAC,CAClD6H,MAAM,CAAC,CAACC,GAAG,EAAE/I,GAAG,KAAK;MACpB,IAAIsN,UAAU,IAAItN,GAAG,CAACmB,EAAE,KAAKmM,UAAU,CAACnM,EAAE,EAAE;QAC1C,OAAO4H,GAAG,IAAIC,MAAM,CAACsE,UAAU,CAACrE,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAOF,GAAG,IAAIC,MAAM,CAAChJ,GAAG,CAACiJ,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACwC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiC,iBAAiB,GAAGrS,KAAK,CAACG,WAAW,CAAC,CAAC2I,KAAK,EAAEzD,YAAY,KAAK;IACnE;IACA0L,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVlI,KAAK;MACLzD;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiN,kBAAkB,GAAGnS,WAAW,CAAC,MAAM;IAC3C4Q,gBAAgB,CAAC3C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP4C,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACAxB,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,kBAAkB,GAAGpS,WAAW,CAAEqS,MAAM,IAAK;IACjD,MAAM;MAAE1J;IAAM,CAAC,GAAGgI,aAAa;IAC/B,IAAIhI,KAAK,KAAK,IAAI,EAAE;MAClB;MACAwJ,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjCvE,WAAW,CAACwE,QAAQ,IAAI;UACtB,IAAI9D,WAAW,GAAG8D,QAAQ,CAAChL,GAAG,CAAChD,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACmB,EAAE,KAAKgD,KAAK,EAAE;cACpB,IAAI0J,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAG7N,GAAG;kBAAEuL,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIyC,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAItD,aAAa,EAAE;kBACjBsD,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAIrD,UAAU,EAAE;kBACdoD,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAC3F,MAAM,GAAG,CAAC,EAAE;kBACvB0F,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAGpO,GAAG;kBAAEuL,OAAO,EAAE0C,WAAW;kBAAEzC,iBAAiB,EAAEyC;gBAAY,CAAC;cACzE;YACF;YACA,OAAOjO,GAAG;UACZ,CAAC,CAAC;UAEFkK,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAvK,UAAU,CAAC,MAAM;UACf0H,OAAO,CAACiB,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC6D,aAAa,EAAEwB,kBAAkB,EAAElF,gBAAgB,EAAEmC,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAEpF;EACA,MAAMuD,mBAAmB,GAAG7S,WAAW,CAAC,MAAM;IAC5CgL,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8H,oBAAoB,GAAG9S,WAAW,CAAC,MAAM;IAC7CgL,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiI,YAAY,GAAG/S,WAAW,CAAC,MAAM;IACrC,IAAI6K,SAAS,CAACmI,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC1I,cAAc,CAACuE,QAAQ,CAAChE,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEzI,iBAAiB,CAAC0D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpD,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDnH,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC;MACrBgG,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIxI,cAAc,CAACuE,QAAQ,CAAChE,SAAS,CAACmI,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDnH,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACjC,SAAS,EAAEP,cAAc,EAAEwI,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMG,YAAY,GAAGjT,WAAW,CAAEqS,MAAM,IAAK;IAC3C9H,iBAAiB,CAAC0D,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAAC6F,IAAI,IAAIA,IAAI,KAAKb,MAAM,CAAC,CAAC;IAC/DxG,OAAO,CAACiB,GAAG,CAAC,OAAO,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqG,eAAe,GAAGnT,WAAW,CAAE2F,EAAE,IAAK;IAC1CqI,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAACzG,GAAG,CAAChD,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAK,CAAC,GAAGjB,GAAG,CAAC;;MAEnF;MACA,IAAI4O,SAAS,GAAG,CAAC;MACjB1E,WAAW,CAACJ,OAAO,CAAC9J,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC2I,EAAE,KAAK,QAAQ,EAAE;UACrE3I,GAAG,CAAC2I,EAAE,GAAGiG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF1E,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEF7C,OAAO,CAACiB,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMoG,aAAa,GAAGrT,WAAW,CAAE2F,EAAE,IAAK;IACxCqI,WAAW,CAACC,IAAI,IAAI;MAClB,IAAIS,WAAW,GAAGT,IAAI,CAACzG,GAAG,CAAChD,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGnB,GAAG;QAAEiB,QAAQ,EAAE;MAAM,CAAC,GAAGjB,GAAG,CAAC;;MAEpF;MACA,IAAI4O,SAAS,GAAG,CAAC;MACjB1E,WAAW,CAACJ,OAAO,CAAC9J,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC2I,EAAE,KAAK,QAAQ,EAAE;UACrE3I,GAAG,CAAC2I,EAAE,GAAGiG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEF1E,WAAW,GAAGzB,gBAAgB,CAACyB,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFvK,UAAU,CAAC,MAAM;MACf0H,OAAO,CAACiB,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACG,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMqG,eAAe,GAAGtT,WAAW,CAAE2F,EAAE,IAAK;IAC1CqI,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAMsF,YAAY,GAAGtF,IAAI,CAACZ,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAIyN,SAAS,GAAG,CAAC;MACjBG,YAAY,CAACjF,OAAO,CAAC9J,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC2I,EAAE,KAAK,QAAQ,EAAE;UACrE3I,GAAG,CAAC2I,EAAE,GAAGiG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM1E,WAAW,GAAGzB,gBAAgB,CAACsG,YAAY,CAAC;;MAElD;MACA5G,2BAA2B,CAAC+B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElC7C,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC;MACrB,OAAO4B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMwG,YAAY,GAAGxT,WAAW,CAAEyT,UAAU,IAAK;IAC/CzF,WAAW,CAACC,IAAI,IAAI;MAClB;MACA,MAAMyF,WAAW,GAAGzF,IAAI,CAAC0F,SAAS,CAACnP,GAAG,IAAIA,GAAG,CAACmB,EAAE,KAAK8N,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAOzF,IAAI;;MAEnC;MACA,MAAM2F,KAAK,GAAG7H,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAM+H,UAAU,GAAG5F,IAAI,CAACyF,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAAC1G,EAAE,KAAK,QAAQ,GAAG0G,UAAU,CAAC1G,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM4G,MAAM,GAAG;QACbpO,EAAE,EAAEiO,KAAK;QACTzG,EAAE,EAAE2G,QAAQ;QACZE,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNlE,OAAO,EAAE,EAAE;QACXmE,QAAQ,EAAE,EAAE;QACZzG,UAAU,EAAE,CAAC;QACbuC,iBAAiB,EAAE,EAAE;QACrBvK,QAAQ,EAAE,KAAK;QACfD,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAM0I,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;MACzBC,OAAO,CAACG,MAAM,CAACqF,WAAW,GAAG,CAAC,EAAE,CAAC,EAAEK,MAAM,CAAC;;MAE1C;MACA,IAAIX,SAAS,GAAG,CAAC;MACjBlF,OAAO,CAACI,OAAO,CAAC9J,GAAG,IAAI;QACrB,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,IAAI,OAAOjB,GAAG,CAAC2I,EAAE,KAAK,QAAQ,EAAE;UACrE3I,GAAG,CAAC2I,EAAE,GAAGiG,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAM1E,WAAW,GAAGzB,gBAAgB,CAACiB,OAAO,CAAC;;MAE7C;MACAvB,2BAA2B,CAAC+B,WAAW,CAAC;MACxC1B,qBAAqB,CAAC0B,WAAW,CAAC;MAElC7C,OAAO,CAACiB,GAAG,CAAC,OAAO,CAAC;MACpB,OAAO4B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEN,2BAA2B,EAAEK,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMmH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjJ,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMkJ,YAAY,GAAG,CAACnE,QAAQ,IAAI,EAAE,EACjC5C,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,CAAC;;MAErD;MACA2O,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAACnH,EAAE,KAAK,QAAQ,IAAI,OAAOoH,CAAC,CAACpH,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAOmH,CAAC,CAACnH,EAAE,GAAGoH,CAAC,CAACpH,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMqH,OAAO,GAAGJ,YAAY,CAAC5M,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,MAAM;QAChD;QACA0I,EAAE,EAAE1I,KAAK,GAAG,CAAC;QACbuP,IAAI,EAAExP,GAAG,CAACwP,IAAI,GAAI,OAAOxP,GAAG,CAACwP,IAAI,KAAK,QAAQ,IAAIxP,GAAG,CAACwP,IAAI,CAACnF,QAAQ,CAAC,GAAG,CAAC,GAAGrK,GAAG,CAACwP,IAAI,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGjQ,GAAG,CAACwP,IAAI,GAAI,EAAE;QAClH,YAAY,EAAExP,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGkQ,IAAI,CAACC,KAAK,CAACnQ,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFyP,EAAE,EAAE,OAAOzP,GAAG,CAACyP,EAAE,KAAK,QAAQ,GAAGS,IAAI,CAACC,KAAK,CAACnQ,GAAG,CAACyP,EAAE,CAAC,GAAGzP,GAAG,CAACyP,EAAE,IAAI,EAAE;QAClElE,OAAO,EAAGvL,GAAG,CAACwL,iBAAiB,IAAIxL,GAAG,CAACwL,iBAAiB,KAAK,MAAM,GAAIxL,GAAG,CAACwL,iBAAiB,GAAG,EAAE;QACjG4E,KAAK,EAAE,OAAOpQ,GAAG,CAAC0P,QAAQ,KAAK,QAAQ,GACpC1P,GAAG,CAAC0P,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG1P,GAAG,CAAC0P,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,GAAGrQ,GAAG,CAAC0P,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,GAC3ErQ,GAAG,CAAC0P,QAAQ,IAAI,EAAE;QACpBY,MAAM,EAAE,OAAOtQ,GAAG,CAACiJ,UAAU,KAAK,QAAQ,GAAGjJ,GAAG,CAACiJ,UAAU,CAACoH,OAAO,CAAC,CAAC,CAAC,GAAGrQ,GAAG,CAACiJ,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMsH,WAAW,GAAG,CAAC9E,QAAQ,IAAI,EAAE,EAChC5C,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI,CAAC3I,GAAG,CAACiB,QAAQ,IAAIjB,GAAG,CAACiJ,UAAU,CAAC,CACpEH,MAAM,CAAC,CAACC,GAAG,EAAE/I,GAAG,KAAK+I,GAAG,IAAI,OAAO/I,GAAG,CAACiJ,UAAU,KAAK,QAAQ,GAAGjJ,GAAG,CAACiJ,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAMuH,YAAY,GAAInL,MAAM,IAAIA,MAAM,CAACkH,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAGlH,MAAM;MAE5F,MAAMoL,QAAQ,GAAG,MAAM/R,KAAK,CAACgS,IAAI,CAAC,GAAG/R,OAAO,oBAAoB,EAAE;QAChEyG,IAAI,EAAE4K,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnChL,MAAM,EAAEmL,YAAY;QACpB5I,aAAa,EAAEA;MACjB,CAAC,CAAC;MAEF,IAAI6I,QAAQ,CAACrL,IAAI,IAAIqL,QAAQ,CAACrL,IAAI,CAACuL,KAAK,EAAE;QACxC;QACA,MAAMnE,WAAW,GAAG,GAAG7N,OAAO,CAACsR,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACrL,IAAI,CAACwL,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAvJ,OAAO,CAACiB,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACA3I,UAAU,CAAC,MAAM;UACf,MAAMkR,MAAM,GAAGnE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CkE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGxE,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC6D,MAAM,CAAC;UACjClR,UAAU,CAAC,MAAM;YACf+M,QAAQ,CAACK,IAAI,CAACG,WAAW,CAAC2D,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO7J,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRV,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMwK,kBAAkB,GAAG1V,WAAW,CAAC,CAAC2I,KAAK,EAAElB,KAAK,KAAK;IACvDyK,iBAAiB,CAACvJ,KAAK,EAAElB,KAAK,CAAC;EACjC,CAAC,EAAE,CAACyK,iBAAiB,CAAC,CAAC;EAEvB,MAAMxN,OAAO,GAAGxE,OAAO,CAAC,MAAOiK,WAAW,CAAC3C,GAAG,CAACnC,GAAG,IAAI;IACpD,IAAI,EAAE4K,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC0F,cAAc,CAACtQ,GAAG,CAACC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,IAAID,GAAG,CAACC,KAAK,KAAK,QAAQ,IAAID,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAID,GAAG,CAACC,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8E,UAAU,EAAE/E,GAAG,CAAC+E,UAAU;QAC1BwL,IAAI,EAAE,CAAC;QACP3O,KAAK,EAAE,GAAG;QACV1B,QAAQ,EAAE,KAAK;QACf8C,UAAU,EAAGwN,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrR,GAAG,CAAC2I,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI0I,MAAM,CAACrR,GAAG,CAACiB,QAAQ,EAAE;YACvB,MAAMqQ,iBAAiB,GAAGD,MAAM,CAACrR,GAAG,CAACwL,iBAAiB,IAAI,KAAK;YAC/D,oBACEtM,OAAA,CAACxC,OAAO;cAAC6U,KAAK,EAAEF,MAAM,CAACrR,GAAG,CAACwL,iBAAiB,IAAI,EAAG;cAACgG,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAhQ,QAAA,eACvEvC,OAAA,CAACzC,IAAI;gBACHiV,KAAK,EAAEJ,iBAAkB;gBACzBjP,KAAK,EAAC,SAAS;gBACfuB,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACZtB,EAAE,EAAE;kBAAE2C,QAAQ,EAAE,MAAM;kBAAEiN,OAAO,EAAE,GAAG;kBAAEvP,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEgM,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAAnO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAI6O,UAAU,GAAG,MAAM;UACvB,IAAIvN,UAAU,GAAG,KAAK;UAEtB,IAAIgN,MAAM,CAACrR,GAAG,CAACwL,iBAAiB,IAAI6F,MAAM,CAACrR,GAAG,CAACwL,iBAAiB,KAAK,MAAM,EAAE;YAC3EoG,UAAU,GAAGP,MAAM,CAACrR,GAAG,CAACwL,iBAAiB;YACzCnH,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEnF,OAAA,CAAC8E,UAAU;YACTG,KAAK,EAAEkN,MAAM,CAACrR,GAAG,CAACmB,EAAG;YACrBiD,IAAI,EAAEwN,UAAW;YACjBvN,UAAU,EAAEA,UAAW;YACvBjB,OAAO,EAAE8N;UAAmB;YAAAtO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAIlC,GAAG,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAED,GAAG,CAACC,KAAK;QAChB8E,UAAU,EAAE/E,GAAG,CAAC+E,UAAU;QAC1BwL,IAAI,EAAE,GAAG;QACT3O,KAAK,EAAE,GAAG;QACV1B,QAAQ,EAAE,KAAK;QACf8C,UAAU,EAAGwN,MAAM,IAAK;UACtB,IAAIA,MAAM,CAACrR,GAAG,CAAC2I,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEzJ,OAAA,CAACvD,GAAG;YAACoG,EAAE,EAAE;cAAEgP,OAAO,EAAE,MAAM;cAAEc,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAArQ,QAAA,gBAE3DvC,OAAA,CAACxC,OAAO;cAAC6U,KAAK,EAAC,wDAAW;cAAA9P,QAAA,eACxBvC,OAAA,CAAC3C,UAAU;gBACT8G,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,SAAS;gBACfe,OAAO,EAAEA,CAAA,KAAM4L,YAAY,CAACqC,MAAM,CAACrR,GAAG,CAACmB,EAAE,CAAE;gBAC3CY,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,cAAc;oBAC/BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEFvC,OAAA,CAACX,oBAAoB;kBAACoG,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGV7D,OAAA,CAACxC,OAAO;cAAC6U,KAAK,EAAC,0EAAc;cAAA9P,QAAA,eAC3BvC,OAAA,CAAC3C,UAAU;gBACT8G,IAAI,EAAC,OAAO;gBACZhB,KAAK,EAAC,OAAO;gBACbe,OAAO,EAAEA,CAAA,KAAM0L,eAAe,CAACuC,MAAM,CAACrR,GAAG,CAACmB,EAAE,CAAE;gBAC9CY,EAAE,EAAE;kBACF,SAAS,EAAE;oBACTC,eAAe,EAAE,YAAY;oBAC7BK,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAZ,QAAA,eAEFvC,OAAA,CAACV,uBAAuB;kBAACmG,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGTsO,MAAM,CAACrR,GAAG,CAACiB,QAAQ,gBAClB/B,OAAA,CAACrD,MAAM;cAEL+H,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,SAAS;cACfgB,IAAI,EAAC,OAAO;cACZ0O,SAAS,eAAE7S,OAAA,CAACjB,QAAQ;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBK,OAAO,EAAEA,CAAA,KAAMyL,aAAa,CAACwC,MAAM,CAACrR,GAAG,CAACmB,EAAE,CAAE;cAC5CY,EAAE,EAAE;gBACF4C,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACH;YAED,GAbM,MAAM;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAET7D,OAAA,CAACrD,MAAM;cAEL+H,OAAO,EAAC,WAAW;cACnBvB,KAAK,EAAC,OAAO;cACbgB,IAAI,EAAC,OAAO;cACZ0O,SAAS,eAAE7S,OAAA,CAAClB,UAAU;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BK,OAAO,EAAEA,CAAA,KAAMuL,eAAe,CAAC0C,MAAM,CAACrR,GAAG,CAACmB,EAAE,CAAE;cAC9CY,EAAE,EAAE;gBACF4C,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACH;YAED,GAbM,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAGlC,GAAG;MACNE,QAAQ,EAAEsQ,MAAM,IAAI;QAClB,IAAIA,MAAM,CAACrR,GAAG,IAAIqR,MAAM,CAACrR,GAAG,CAAC2I,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI0I,MAAM,CAACrR,GAAG,IAAIqR,MAAM,CAACrR,GAAG,CAACiB,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAOJ,GAAG,CAACE,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD8C,UAAU,EAAGwN,MAAM,IAAK;QACtB,IAAIA,MAAM,CAACrR,GAAG,CAAC2I,EAAE,KAAK,OAAO,IAAI9H,GAAG,CAACC,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE5B,OAAA,CAACtD,UAAU;YAACgI,OAAO,EAAC,OAAO;YAACoO,UAAU,EAAC,MAAM;YAAC3P,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAC1D,OAAO4P,MAAM,CAACpO,KAAK,KAAK,QAAQ,GAAGoO,MAAM,CAACpO,KAAK,CAACoN,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOgB,MAAM,CAACpO,KAAK,KAAK,QAAQ,IAAI,CAACuH,KAAK,CAACxB,MAAM,CAACqI,MAAM,CAACpO,KAAK,CAAC,CAAC,GAAG+F,MAAM,CAACqI,MAAM,CAACpO,KAAK,CAAC,CAACoN,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAACpO;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAIsO,MAAM,CAACrR,GAAG,CAACiB,QAAQ,EAAE;UACvB,oBACE/B,OAAA,CAACtD,UAAU;YAACgI,OAAO,EAAC,OAAO;YAACvB,KAAK,EAAC,eAAe;YAACN,EAAE,EAAE;cAAEO,cAAc,EAAE;YAAe,CAAE;YAAAb,QAAA,EACtF4P,MAAM,CAACpO;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAIlC,GAAG,CAACC,KAAK,KAAK,MAAM,IAAIuQ,MAAM,CAACpO,KAAK,EAAE;UACxC,OAAOoO,MAAM,CAACpO,KAAK,CAACgN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAIpP,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOiN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAACpO,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,OAAO,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOiN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAACpO,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,IAAI,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOiN,IAAI,CAACC,KAAK,CAACkB,MAAM,CAACpO,KAAK,CAAC;QACjC;QACA,IAAIpC,GAAG,CAACC,KAAK,KAAK,UAAU,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOoO,MAAM,CAACpO,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGoO,MAAM,CAACpO,KAAK,CAACoN,OAAO,CAAC,CAAC,CAAC,GAAGgB,MAAM,CAACpO,KAAK,CAACoN,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIxP,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOoO,MAAM,CAACpO,KAAK,CAACoN,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIxP,GAAG,CAACC,KAAK,KAAK,YAAY,IAAI,OAAOuQ,MAAM,CAACpO,KAAK,KAAK,QAAQ,IAAI,CAACuH,KAAK,CAACxB,MAAM,CAACqI,MAAM,CAACpO,KAAK,CAAC,CAAC,EAAE;UACzG,OAAO+F,MAAM,CAACqI,MAAM,CAACpO,KAAK,CAAC,CAACoN,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOgB,MAAM,CAACpO,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOoO,MAAM,CAACpO,KAAK;QACrB;QACA,OAAOoO,MAAM,CAACpO,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC4F,MAAM,CAACoJ,OAAO,CAAE,EAAE,CAACtM,WAAW,EAAEuL,kBAAkB,EAAEvC,eAAe,EAAEE,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAMoD,gBAAgB,GAAGxW,OAAO,CAAC,MAAM;IACrC,IAAI,CAACqM,mBAAmB,CAACyG,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAO/C,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAM0G,WAAW,GAAGpK,mBAAmB,CAACqK,WAAW,CAAC,CAAC;IACrD,OAAO,CAAC3G,QAAQ,IAAI,EAAE,EAAE5C,MAAM,CAAC7I,GAAG,IAAI;MACpC,IAAIiI,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOoK,MAAM,CAACC,MAAM,CAACtS,GAAG,CAAC,CAACuS,IAAI,CAACtP,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACzB,QAAQ,CAAC,CAAC,CAAC4Q,WAAW,CAAC,CAAC,CAAC/H,QAAQ,CAAC8H,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMK,SAAS,GAAGxS,GAAG,CAACiI,YAAY,CAAC;QACnC,OAAOuK,SAAS,IAAIA,SAAS,CAAChR,QAAQ,CAAC,CAAC,CAAC4Q,WAAW,CAAC,CAAC,CAAC/H,QAAQ,CAAC8H,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1G,QAAQ,EAAE1D,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAMwK,YAAY,GAAG/W,OAAO,CAAC,MAAMwW,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA3W,SAAS,CAAC,MAAM;IACd8L,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAE;MACrB4C,QAAQ,EAAEF,eAAe,CAACE,QAAQ;MAClCP,IAAI,EAAEK,eAAe,CAACL,IAAI;MAC1B+H,UAAU,EAAED,YAAY,CAAClK;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACyC,eAAe,CAACE,QAAQ,EAAEF,eAAe,CAACL,IAAI,EAAE8H,YAAY,CAAClK,MAAM,CAAC,CAAC;;EAEzE;EACA,IAAI,CAACkD,QAAQ,IAAIA,QAAQ,CAAClD,MAAM,KAAK,CAAC,EAAE;IACtC,oBACErJ,OAAA,CAACvD,GAAG;MAACoG,EAAE,EAAE;QAAEY,SAAS,EAAE,QAAQ;QAAEgQ,EAAE,EAAE;MAAE,CAAE;MAAAlR,QAAA,gBACtCvC,OAAA,CAACtD,UAAU;QAACgI,OAAO,EAAC,IAAI;QAACvB,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAAC;MAEhD;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACrD,MAAM;QACL+H,OAAO,EAAC,WAAW;QACnBR,OAAO,EAAEkC,OAAQ;QACjBvD,EAAE,EAAE;UAAE6Q,EAAE,EAAE;QAAE,CAAE;QAAAnR,QAAA,EACf;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE7D,OAAA,CAACvD,GAAG;IAAA8F,QAAA,gBAEFvC,OAAA,CAACvC,IAAI;MAACoF,EAAE,EAAE;QAAE8Q,EAAE,EAAE;MAAE,CAAE;MAAApR,QAAA,eAClBvC,OAAA,CAACtC,WAAW;QAAA6E,QAAA,eACVvC,OAAA,CAACvD,GAAG;UAACoG,EAAE,EAAE;YAAEgP,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE,QAAQ;YAAEgB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAElB,GAAG,EAAE;UAAE,CAAE;UAAApQ,QAAA,gBAC5GvC,OAAA,CAACvD,GAAG;YAACoG,EAAE,EAAE;cAAEgP,OAAO,EAAE,MAAM;cAAEe,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAApQ,QAAA,gBACzDvC,OAAA,CAAChB,cAAc;cAAC6D,EAAE,EAAE;gBAAEM,KAAK,EAAE,cAAc;gBAAEsC,QAAQ,EAAE;cAAG;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D7D,OAAA,CAACvD,GAAG;cAAA8F,QAAA,gBACFvC,OAAA,CAACtD,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAAC7B,EAAE,EAAE;kBAAEiQ,UAAU,EAAE,GAAG;kBAAE3P,KAAK,EAAE,cAAc;kBAAEwQ,EAAE,EAAE;gBAAI,CAAE;gBAAApR,QAAA,EAAC;cAElF;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7D,OAAA,CAACtD,UAAU;gBAACgI,OAAO,EAAC,OAAO;gBAAC7B,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAiB,CAAE;gBAAAZ,QAAA,EAAC;cAE7D;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA,CAACrC,KAAK;YAACmW,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAAClR,EAAE,EAAE;cAAEgR,QAAQ,EAAE,MAAM;cAAElB,GAAG,EAAE;YAAE,CAAE;YAAApQ,QAAA,gBAClEvC,OAAA,CAACzC,IAAI;cACHyW,IAAI,eAAEhU,OAAA,CAACf,aAAa;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxB2O,KAAK,EAAE,GAAG,CAACe,YAAY,IAAI,EAAE,EAAE5J,MAAM,CAAC7I,GAAG,IAAI,CAACA,GAAG,CAACgB,OAAO,IAAI,CAAChB,GAAG,CAACiB,QAAQ,CAAC,CAACsH,MAAM,MAAO;cACzFlG,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACF7D,OAAA,CAACzC,IAAI;cACHyW,IAAI,eAAEhU,OAAA,CAACd,cAAc;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB2O,KAAK,EAAE,WAAWrE,kBAAkB,CAACoF,YAAY,CAAC,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEhO,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAAC0P,YAAY,IAAI,EAAE,EAAE5J,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAACsH,MAAM,GAAG,CAAC,iBAC1DrJ,OAAA,CAACzC,IAAI;cACHyW,IAAI,eAAEhU,OAAA,CAAClB,UAAU;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrB2O,KAAK,EAAE,GAAG,CAACe,YAAY,IAAI,EAAE,EAAE5J,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAACiB,QAAQ,CAAC,CAACsH,MAAM,OAAQ;cACzElG,KAAK,EAAC,SAAS;cACfuB,OAAO,EAAC,UAAU;cAClBP,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACvC,IAAI;MAACoF,EAAE,EAAE;QAAE8Q,EAAE,EAAE;MAAE,CAAE;MAAApR,QAAA,eAClBvC,OAAA,CAACtC,WAAW;QAAA6E,QAAA,eACVvC,OAAA,CAAC7B,IAAI;UAAC8V,SAAS;UAACF,OAAO,EAAE,CAAE;UAAAxR,QAAA,gBAEzBvC,OAAA,CAAC7B,IAAI;YAACqR,IAAI;YAAC0E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5R,QAAA,gBACvBvC,OAAA,CAACtD,UAAU;cAACgI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEiQ,UAAU,EAAE,GAAG;gBAAEa,EAAE,EAAE,CAAC;gBAAE9B,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAApQ,QAAA,gBAC5GvC,OAAA,CAACb,UAAU;gBAAC0D,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7D,OAAA,CAACrC,KAAK;cAACmW,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAACnB,UAAU,EAAC,QAAQ;cAAArQ,QAAA,gBAC5EvC,OAAA,CAACpC,WAAW;gBAACuG,IAAI,EAAC,OAAO;gBAACtB,EAAE,EAAE;kBAAE0C,QAAQ,EAAE;gBAAI,CAAE;gBAAAhD,QAAA,gBAC9CvC,OAAA,CAACnC,UAAU;kBAAA0E,QAAA,EAAC;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B7D,OAAA,CAAClC,MAAM;kBACLiG,KAAK,EAAEgF,YAAa;kBACpByJ,KAAK,EAAC,0BAAM;kBACZpO,QAAQ,EAAGjC,CAAC,IAAK6G,eAAe,CAAC7G,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;kBAAAxB,QAAA,gBAEjDvC,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,KAAK;oBAAAxB,QAAA,EAAC;kBAAG;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpC7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClC7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,MAAM;oBAAAxB,QAAA,EAAC;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACtC7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAU;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClD7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,OAAO;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxC7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,IAAI;oBAAAxB,QAAA,EAAC;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClC7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5C7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,UAAU;oBAAAxB,QAAA,EAAC;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC3C7D,OAAA,CAACjC,QAAQ;oBAACgG,KAAK,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAAM;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEd7D,OAAA,CAAC5C,SAAS;gBACR+G,IAAI,EAAC,OAAO;gBACZkQ,WAAW,EAAC,yCAAW;gBACvBtQ,KAAK,EAAE4E,UAAW;gBAClBvE,QAAQ,EAAGjC,CAAC,IAAKyG,aAAa,CAACzG,CAAC,CAACkC,MAAM,CAACN,KAAK,CAAE;gBAC/ClB,EAAE,EAAE;kBAAEyR,QAAQ,EAAE,CAAC;kBAAE/O,QAAQ,EAAE;gBAAI,CAAE;gBACnCgP,UAAU,EAAE;kBACVC,cAAc,eACZxU,OAAA,CAAChC,cAAc;oBAACyW,QAAQ,EAAC,OAAO;oBAAAlS,QAAA,eAC9BvC,OAAA,CAACb,UAAU;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBACD6Q,YAAY,EAAE/L,UAAU,iBACtB3I,OAAA,CAAChC,cAAc;oBAACyW,QAAQ,EAAC,KAAK;oBAAAlS,QAAA,eAC5BvC,OAAA,CAAC3C,UAAU;sBACT8G,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM0E,aAAa,CAAC,EAAE,CAAE;sBACjC+L,IAAI,EAAC,KAAK;sBAAApS,QAAA,eAEVvC,OAAA,CAACZ,SAAS;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAED8E,UAAU,iBACT3I,OAAA,CAACtD,UAAU;gBAACgI,OAAO,EAAC,OAAO;gBAACvB,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,GAAC,eAC9C,EAACyQ,gBAAgB,CAACrJ,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAM,EAAC,qBAChE;cAAA;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGP7D,OAAA,CAAC7B,IAAI;YAACqR,IAAI;YAAC0E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5R,QAAA,gBACvBvC,OAAA,CAACtD,UAAU;cAACgI,OAAO,EAAC,WAAW;cAAC7B,EAAE,EAAE;gBAAEiQ,UAAU,EAAE,GAAG;gBAAEa,EAAE,EAAE,CAAC;gBAAE9B,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,GAAG,EAAE;cAAE,CAAE;cAAApQ,QAAA,gBAC5GvC,OAAA,CAACT,YAAY;gBAACsD,EAAE,EAAE;kBAAEM,KAAK,EAAE;gBAAe;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7D,OAAA,CAACrC,KAAK;cAACmW,SAAS,EAAE;gBAAEI,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAACL,OAAO,EAAE,CAAE;cAAAxR,QAAA,gBACxDvC,OAAA,CAACrD,MAAM;gBACL+H,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACf0P,SAAS,eAAE7S,OAAA,CAACrB,YAAY;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BK,OAAO,EAAEkJ,cAAe;gBAAA7K,QAAA,EACzB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET7D,OAAA,CAACrD,MAAM;gBACL+H,OAAO,EAAC,WAAW;gBACnBvB,KAAK,EAAC,SAAS;gBACf0P,SAAS,EAAEtL,oBAAoB,gBAAGvH,OAAA,CAAC1C,gBAAgB;kBAAC6G,IAAI,EAAE,EAAG;kBAAChB,KAAK,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACf,aAAa;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrGK,OAAO,EAAEuM,gBAAiB;gBAC1BmE,QAAQ,EAAErN,oBAAqB;gBAAAhF,QAAA,EAE9BgF,oBAAoB,GAAG,QAAQ,GAAG;cAAW;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAET7D,OAAA,CAACrD,MAAM;gBACL+H,OAAO,EAAC,UAAU;gBAClBvB,KAAK,EAAC,OAAO;gBACb0P,SAAS,eAAE7S,OAAA,CAACpB,cAAc;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BK,OAAO,EAAE+J,aAAc;gBAAA1L,QAAA,EACxB;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACL,eAAe;MAACkV,SAAS,EAAE7K,aAAc;MAAAzH,QAAA,eACxCvC,OAAA,CAACpD,KAAK;QAACiG,EAAE,EAAE;UACTU,KAAK,EAAE,MAAM;UACboC,QAAQ,EAAE,QAAQ;UAClB,iBAAiB,EAAE;YACjB3C,SAAS,EAAE,cAAc;YACzBC,SAAS,EAAE,CAAC;YACZI,MAAM,EAAE;UACV,CAAC;UACD,qBAAqB,EAAE;YACrBH,UAAU,EAAE;UACd,CAAC;UACD,sBAAsB,EAAE;YACtB4R,YAAY,EAAE,WAAW;YACzBC,WAAW,EAAE,WAAW;YACxBC,WAAW,EAAE;UACf;QACF,CAAE;QAAAzS,QAAA,gBACAvC,OAAA,CAACzB,cAAc;UAAAgE,QAAA,eACbvC,OAAA,CAAC5B,KAAK;YAAC6W,YAAY;YAAA1S,QAAA,gBACjBvC,OAAA,CAACxB,SAAS;cAAA+D,QAAA,eACRvC,OAAA,CAACvB,QAAQ;gBAAA8D,QAAA,gBAEPvC,OAAA,CAAC1B,SAAS;kBAACuE,EAAE,EAAE;oBAAEU,KAAK,EAAE,EAAE;oBAAEuP,UAAU,EAAE;kBAAO,CAAE;kBAAAvQ,QAAA,EAAC;gBAElD;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EACX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,iBAClBzB,OAAA,CAAC1B,SAAS;kBAERuE,EAAE,EAAE;oBACFiQ,UAAU,EAAE,MAAM;oBAClBhQ,eAAe,EAAE,oBAAoB;oBACrCiS,WAAW,EAAE,WAAW;oBACxBC,WAAW,EAAE,SAAS;oBACtB,cAAc,EAAE;sBACdD,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAxS,QAAA,EAEDd,MAAM,CAACiF;gBAAU,GAXbjF,MAAM,CAACG,KAAK;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYR,CACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEZ7D,OAAA,CAACJ,SAAS;cAACsV,WAAW,EAAC,YAAY;cAAA3S,QAAA,EAChCA,CAACC,QAAQ,EAAEC,QAAQ,kBAClBzC,OAAA,CAAC3B,SAAS;gBACRqE,GAAG,EAAEF,QAAQ,CAACG,QAAS;gBAAA,GACnBH,QAAQ,CAAC2S,cAAc;gBAC3BtS,EAAE,EAAE;kBACFC,eAAe,EAAEL,QAAQ,CAAC2S,cAAc,GAAG,cAAc,GAAG,SAAS;kBACrElS,UAAU,EAAE,4BAA4B;kBACxCmS,SAAS,EAAE,WAAW;kBACtBL,WAAW,EAAE;gBACf,CAAE;gBAAAzS,QAAA,GAGDgR,YAAY,CACV5J,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC,CAAC;gBAAA,CAClC6L,KAAK,CACJxJ,eAAe,CAACL,IAAI,GAAGK,eAAe,CAACE,QAAQ,EAC/C,CAACF,eAAe,CAACL,IAAI,GAAG,CAAC,IAAIK,eAAe,CAACE,QAC/C,CAAC,CACAlI,GAAG,CAAC,CAAChD,GAAG,EAAEC,KAAK,kBACdf,OAAA,CAACU,iBAAiB;kBAEhBI,GAAG,EAAEA,GAAI;kBACTC,KAAK,EAAEA,KAAM;kBACbC,OAAO,EAAEA,OAAQ;kBACjBC,UAAU,EAAE6J;gBAAe,GAJtBhK,GAAG,CAACmB,EAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKZ,CACF,CAAC,EAGH0P,YAAY,CAAC7R,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC,iBAC3CzJ,OAAA,CAACvB,QAAQ;kBAAC8W,SAAS,EAAC,WAAW;kBAAC1S,EAAE,EAAE;oBAClCC,eAAe,EAAE,0BAA0B;oBAC3CgQ,UAAU,EAAE,MAAM;oBAClB,sBAAsB,EAAE;sBACtBA,UAAU,EAAE,MAAM;sBAClBuC,SAAS,EAAE,WAAW;sBACtBL,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAzS,QAAA,gBACAvC,OAAA,CAAC1B,SAAS;oBAACuE,EAAE,EAAE;sBAAEU,KAAK,EAAE;oBAAG;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElB,CAAC,EACX7C,OAAO,CAAC8C,GAAG,CAAErC,MAAM,IAAK;oBACvB,MAAM+H,QAAQ,GAAG+J,YAAY,CAAC7R,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC;oBAC7D,MAAM1F,KAAK,GAAGyF,QAAQ,GAAGA,QAAQ,CAAC/H,MAAM,CAACG,KAAK,CAAC,GAAG,EAAE;oBACpD,oBACE5B,OAAA,CAAC1B,SAAS;sBAAoBuE,EAAE,EAAE;wBAAEoB,OAAO,EAAE;sBAAM,CAAE;sBAAA1B,QAAA,EAClDd,MAAM,CAACkD,UAAU,GAAGlD,MAAM,CAACkD,UAAU,CAAC;wBAAE7D,GAAG,EAAE0I,QAAQ;wBAAEzF;sBAAM,CAAC,CAAC,GAAGA;oBAAK,GAD1DtC,MAAM,CAACG,KAAK;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEjB,CAAC;kBAEhB,CAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CACX,EAEArB,QAAQ,CAAC6R,WAAW;cAAA;gBAAA3Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACZ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGjB7D,OAAA,CAACtB,eAAe;UACd8W,SAAS,EAAC,KAAK;UACfC,KAAK,EAAElC,YAAY,CAAC5J,MAAM,CAAC7I,GAAG,IAAIA,GAAG,CAAC2I,EAAE,KAAK,OAAO,CAAC,CAACJ,MAAO;UAC7DoC,IAAI,EAAEK,eAAe,CAACL,IAAK;UAC3BiK,YAAY,EAAEA,CAACC,KAAK,EAAEC,OAAO,KAAK;YAChCpK,kBAAkB,CAACjB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEkB,IAAI,EAAEmK;YAAQ,CAAC,CAAC,CAAC;UAC1D,CAAE;UACFC,WAAW,EAAE/J,eAAe,CAACE,QAAS;UACtC8J,mBAAmB,EAAGH,KAAK,IAAK;YAC9B,MAAMI,WAAW,GAAG9J,QAAQ,CAAC0J,KAAK,CAACtR,MAAM,CAACN,KAAK,EAAE,EAAE,CAAC;YACpDyH,kBAAkB,CAAC;cAAEC,IAAI,EAAE,CAAC;cAAEO,QAAQ,EAAE+J;YAAY,CAAC,CAAC;YACtDhP,YAAY,CAACmC,OAAO,CAAC,kBAAkB,EAAE6M,WAAW,CAACzT,QAAQ,CAAC,CAAC,CAAC;UAClE,CAAE;UACF0T,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAClCC,gBAAgB,EAAC,2BAAO;UACxBC,kBAAkB,EAAEA,CAAC;YAAEC,IAAI;YAAEC,EAAE;YAAEX;UAAM,CAAC,KAAK,GAAGU,IAAI,IAAIC,EAAE,MAAMX,KAAK;QAAK;UAAA/R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGlB7D,OAAA,CAACnD,MAAM;MACLsQ,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzBkJ,OAAO,EAAE5H,kBAAmB;MAC5BhK,SAAS;MACTe,QAAQ,EAAC,IAAI;MACb8Q,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlU,QAAA,gBAEnBvC,OAAA,CAAClD,WAAW;QAAAyF,QAAA,eACVvC,OAAA,CAACvD,GAAG;UAACoG,EAAE,EAAE;YAAEgP,OAAO,EAAE,MAAM;YAAE+B,cAAc,EAAE,eAAe;YAAEhB,UAAU,EAAE;UAAS,CAAE;UAAArQ,QAAA,gBAClFvC,OAAA,CAACtD,UAAU;YAACgI,OAAO,EAAC,IAAI;YAAAnC,QAAA,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C7D,OAAA,CAACrD,MAAM;YACLkW,SAAS,eAAE7S,OAAA,CAACnB,OAAO;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBK,OAAO,EAAEiL,mBAAoB;YAC7BhM,KAAK,EAAC,SAAS;YAAAZ,QAAA,EAChB;UAED;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGd7D,OAAA,CAACvD,GAAG;QAACoG,EAAE,EAAE;UAAE6T,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApU,QAAA,gBACxBvC,OAAA,CAACtD,UAAU;UAACgI,OAAO,EAAC,OAAO;UAACvB,KAAK,EAAC,gBAAgB;UAACN,EAAE,EAAE;YAAE8Q,EAAE,EAAE;UAAE,CAAE;UAAApR,QAAA,EAAC;QAElE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7D,OAAA,CAACrC,KAAK;UAACmW,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAAxR,QAAA,gBAChCvC,OAAA,CAAC9B,gBAAgB;YACf0Y,OAAO,eACL5W,OAAA,CAAC/B,QAAQ;cACP4Y,OAAO,EAAEnL,aAAc;cACvBtH,QAAQ,EAAGjC,CAAC,IAAKwJ,gBAAgB,CAACxJ,CAAC,CAACkC,MAAM,CAACwS,OAAO,CAAE;cACpD1S,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACD2O,KAAK,EAAC;UAAS;YAAA9O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF7D,OAAA,CAAC9B,gBAAgB;YACf0Y,OAAO,eACL5W,OAAA,CAAC/B,QAAQ;cACP4Y,OAAO,EAAEjL,UAAW;cACpBxH,QAAQ,EAAGjC,CAAC,IAAK0J,aAAa,CAAC1J,CAAC,CAACkC,MAAM,CAACwS,OAAO,CAAE;cACjD1S,IAAI,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACD2O,KAAK,EAAC;UAAK;YAAA9O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA,CAACjD,aAAa;QAAC+Z,QAAQ;QAACjU,EAAE,EAAE;UAAEkU,CAAC,EAAE;QAAE,CAAE;QAAAxU,QAAA,eACnCvC,OAAA,CAACN,aAAa;UACZoG,MAAM,EAAE,GAAI;UACZkR,SAAS,EAAEpQ,cAAc,CAACyC,MAAO;UACjC4N,QAAQ,EAAE,EAAG;UACb1T,KAAK,EAAC,MAAM;UAAAhB,QAAA,EAEXA,CAAC;YAAExB,KAAK;YAAE6Q;UAAM,CAAC,KAAK;YACrB,MAAMjD,MAAM,GAAG/H,cAAc,CAAC7F,KAAK,CAAC;YACpC,oBACEf,OAAA,CAAC/C,QAAQ;cAEP2U,KAAK,EAAEA,KAAM;cACbsF,cAAc;cACdC,eAAe,EAAGxI,MAAM,KAAK,MAAM,iBACjC3O,OAAA,CAAC3C,UAAU;gBACTsX,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBzQ,OAAO,EAAEA,CAAA,KAAMqL,YAAY,CAACZ,MAAM,CAAE;gBAAApM,QAAA,eAEpCvC,OAAA,CAAClB,UAAU;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAAtB,QAAA,eAEFvC,OAAA,CAAC9C,cAAc;gBAACgH,OAAO,EAAEA,CAAA,KAAMwK,kBAAkB,CAACC,MAAM,CAAE;gBAAApM,QAAA,eACxDvC,OAAA,CAAC7C,YAAY;kBAACia,OAAO,EAAEzI;gBAAO;kBAAAjL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZ8K,MAAM;cAAAjL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChB7D,OAAA,CAAChD,aAAa;QAAAuF,QAAA,eACZvC,OAAA,CAACrD,MAAM;UAACuH,OAAO,EAAEuK,kBAAmB;UAAAlM,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7D,OAAA,CAACnD,MAAM;MACLsQ,IAAI,EAAE9F,eAAgB;MACtBgP,OAAO,EAAEjH,oBAAqB;MAC9B3K,SAAS;MACTe,QAAQ,EAAC,IAAI;MACb8Q,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAlU,QAAA,gBAEnBvC,OAAA,CAAClD,WAAW;QAAAyF,QAAA,EAAC;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC7D,OAAA,CAACjD,aAAa;QAAAwF,QAAA,eACZvC,OAAA,CAAC5C,SAAS;UACRoH,SAAS;UACT6S,MAAM,EAAC,OAAO;UACdpV,EAAE,EAAC,MAAM;UACTuQ,KAAK,EAAC,0BAAM;UACZ8E,IAAI,EAAC,MAAM;UACX7S,SAAS;UACTC,OAAO,EAAC,UAAU;UAClBX,KAAK,EAAEoD,SAAU;UACjB/C,QAAQ,EAAGjC,CAAC,IAAKiF,YAAY,CAACjF,CAAC,CAACkC,MAAM,CAACN,KAAK;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB7D,OAAA,CAAChD,aAAa;QAAAuF,QAAA,gBACZvC,OAAA,CAACrD,MAAM;UAACuH,OAAO,EAAEkL,oBAAqB;UAAA7M,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD7D,OAAA,CAACrD,MAAM;UAACuH,OAAO,EAAEmL,YAAa;UAAClM,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC2C,GAAA,CAtyCIP,aAAa;AAAAsR,GAAA,GAAbtR,aAAa;AAwyCnB,eAAeA,aAAa;AAAC,IAAApF,EAAA,EAAA+D,GAAA,EAAAI,GAAA,EAAAgB,GAAA,EAAAuR,GAAA;AAAAC,YAAA,CAAA3W,EAAA;AAAA2W,YAAA,CAAA5S,GAAA;AAAA4S,YAAA,CAAAxS,GAAA;AAAAwS,YAAA,CAAAxR,GAAA;AAAAwR,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}