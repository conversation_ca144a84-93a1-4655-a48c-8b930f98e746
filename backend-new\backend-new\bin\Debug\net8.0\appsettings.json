{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "CommissionCalculation": "Debug"}}, "AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "CommissionCalculation": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/commission-app-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}", "retainedFileCountLimit": 7}}]}, "FileSettings": {"UploadPath": "Uploads", "MaxFileSize": 52428800, "AllowedExtensions": [".xlsx", ".xls"], "CleanupIntervalMinutes": 60, "FileRetentionHours": 24}, "CorsSettings": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"]}}