{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { defaultMemoize } from 'reselect';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useLazyRef } from '../../utils/useLazyRef';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridFilterableColumnLookupSelector } from '../columns/gridColumnsSelector';\nimport { GridPreferencePanelsValue } from '../preferencesPanel/gridPreferencePanelsValue';\nimport { getDefaultGridFilterModel } from './gridFilterState';\nimport { gridFilterModelSelector } from './gridFilterSelector';\nimport { useFirstRender } from '../../utils/useFirstRender';\nimport { gridRowsLookupSelector } from '../rows';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from '../../core/strategyProcessing';\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic } from './gridFilterUtils';\nimport { isDeepEqual } from '../../../utils/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  var _ref, _props$filterModel, _props$initialState;\n  const filterModel = (_ref = (_props$filterModel = props.filterModel) != null ? _props$filterModel : (_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.filter) == null ? void 0 : _props$initialState.filterModel) != null ? _ref : getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: {\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef),\n      filteredRowsLookup: {},\n      filteredDescendantCountLookup: {}\n    },\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return defaultMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  var _props$initialState3, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(state, apiRef.current.instanceId);\n      const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n      const filteringResult = apiRef.current.applyStrategyProcessor('filtering', {\n        isRowMatchingFilters,\n        filterModel: filterModel != null ? filterModel : getDefaultGridFilterModel()\n      });\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filteringResult)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef, props.filterMode, props.disableEval]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const applyFilters = React.useCallback(() => {\n    updateFilteredRows();\n    apiRef.current.forceUpdate();\n  }, [apiRef, updateFilteredRows]);\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        var _column$filterOperato;\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = (_column$filterOperato = column.filterOperators) == null ? void 0 : _column$filterOperato.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof (filterOperator == null ? void 0 : filterOperator.requiresFilterValue) === 'undefined' ? true : filterOperator == null ? void 0 : filterOperator.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: applyFilters,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState2;\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    ((_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.filter) == null ? void 0 : _props$initialState2.filterModel) != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.filter) == null ? void 0 : _props$initialState3.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const filterModel = (_context$stateToResto = context.stateToRestore.filter) == null ? void 0 : _context$stateToResto.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      var _props$slotProps;\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters) {\n      return {\n        filteredRowsLookup: {},\n        filteredDescendantCountLookup: {}\n      };\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      filteredRowsLookup[id] = isRowPassing;\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const filterableColumnsLookup = gridFilterableColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && filterableColumnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n\n  // Do not call `apiRef.current.forceUpdate` to avoid re-render before updating the sorted rows.\n  // Otherwise, the state is not consistent during the render\n  useGridApiEventHandler(apiRef, 'rowsSet', updateFilteredRows);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridApiEventHandler(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues && filterModel.quickFilterExcludeHiddenColumns) {\n      // re-apply filters because the quick filter results may have changed\n      apiRef.current.unstable_applyFilters();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.unstable_applyFilters();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};", "map": {"version": 3, "names": ["_extends", "React", "defaultMemoize", "unstable_useEnhancedEffect", "useEnhancedEffect", "useLazyRef", "useGridApiEventHandler", "useGridApiMethod", "useGridLogger", "gridFilterableColumnLookupSelector", "GridPreferencePanelsValue", "getDefaultGridFilterModel", "gridFilterModelSelector", "useFirstRender", "gridRowsLookupSelector", "useGridRegisterPipeProcessor", "GRID_DEFAULT_STRATEGY", "useGridRegisterStrategyProcessor", "buildAggregatedFilterApplier", "sanitizeFilterModel", "mergeStateWithFilterModel", "cleanFilterItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isDeepEqual", "jsx", "_jsx", "filterStateInitializer", "state", "props", "apiRef", "_ref", "_props$filterModel", "_props$initialState", "filterModel", "initialState", "filter", "disableMultipleColumnsFiltering", "filteredRowsLookup", "filteredDescendantCountLookup", "visibleRowsLookup", "getVisibleRowsLookup", "params", "getVisibleRowsLookupState", "current", "applyStrategyProcessor", "tree", "rows", "createMemoizedValues", "Object", "values", "useGridFilter", "_props$initialState3", "_props$slotProps2", "logger", "registerControlState", "stateId", "propModel", "propOnChange", "onFilterModelChange", "stateSelector", "changeEvent", "updateFilteredRows", "useCallback", "setState", "instanceId", "isRowMatchingFilters", "filterMode", "disableEval", "filteringResult", "newState", "visibleRowsLookupState", "publishEvent", "addColumnMenuItem", "columnMenuItems", "colDef", "filterable", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applyFilters", "forceUpdate", "upsertFilterItem", "item", "items", "itemIndex", "findIndex", "filterItem", "id", "push", "setFilterModel", "upsertFilterItems", "existingItems", "for<PERSON>ach", "deleteFilterItem", "itemToDelete", "length", "showFilterPanel", "targetColumnField", "panelId", "labelId", "debug", "filterItemsWithValue", "_column$filterOperato", "value", "undefined", "Array", "isArray", "column", "getColumn", "field", "filterOperator", "filterOperators", "find", "operator", "requiresFilterValue", "newFilterItems", "filterItemOnTarget", "targetColumn", "showPreferences", "filters", "hideFilterPanel", "hidePreferences", "setFilterLogicOperator", "logicOperator", "setQuickFilter<PERSON><PERSON><PERSON>", "quickFilterV<PERSON>ues", "model", "reason", "currentModel", "updateControlState", "unstable_applyFilters", "filterApi", "ignoreDiacritics", "stateExportPreProcessing", "prevState", "context", "_props$initialState2", "filterModelToExport", "shouldExportFilterModel", "exportOnlyDirtyModels", "stateRestorePreProcessing", "_context$stateToResto", "stateToRestore", "callbacks", "preferencePanelPreProcessing", "initialValue", "_props$slotProps", "FilterPanel", "slots", "filterPanel", "slotProps", "getRowId", "getRowsRef", "flatFilteringMethod", "dataRowIdToModelLookup", "filterCache", "result", "passingFilterItems", "passingQuickF<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "row", "isRowPassing", "footerId", "footer", "handleColumnsChange", "filterableColumnsLookup", "handleStrategyProcessorChange", "methodName", "updateVisibleRowsLookupState", "quickFilterExcludeHiddenColumns"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/filter/useGridFilter.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { defaultMemoize } from 'reselect';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useLazyRef } from '../../utils/useLazyRef';\nimport { useGridApiEventHandler } from '../../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { useGridLogger } from '../../utils/useGridLogger';\nimport { gridFilterableColumnLookupSelector } from '../columns/gridColumnsSelector';\nimport { GridPreferencePanelsValue } from '../preferencesPanel/gridPreferencePanelsValue';\nimport { getDefaultGridFilterModel } from './gridFilterState';\nimport { gridFilterModelSelector } from './gridFilterSelector';\nimport { useFirstRender } from '../../utils/useFirstRender';\nimport { gridRowsLookupSelector } from '../rows';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from '../../core/strategyProcessing';\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic } from './gridFilterUtils';\nimport { isDeepEqual } from '../../../utils/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  var _ref, _props$filterModel, _props$initialState;\n  const filterModel = (_ref = (_props$filterModel = props.filterModel) != null ? _props$filterModel : (_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.filter) == null ? void 0 : _props$initialState.filterModel) != null ? _ref : getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: {\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef),\n      filteredRowsLookup: {},\n      filteredDescendantCountLookup: {}\n    },\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return defaultMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  var _props$initialState3, _props$slotProps2;\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(state, apiRef.current.instanceId);\n      const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n      const filteringResult = apiRef.current.applyStrategyProcessor('filtering', {\n        isRowMatchingFilters,\n        filterModel: filterModel != null ? filterModel : getDefaultGridFilterModel()\n      });\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filteringResult)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef, props.filterMode, props.disableEval]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const applyFilters = React.useCallback(() => {\n    updateFilteredRows();\n    apiRef.current.forceUpdate();\n  }, [apiRef, updateFilteredRows]);\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        var _column$filterOperato;\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = (_column$filterOperato = column.filterOperators) == null ? void 0 : _column$filterOperato.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof (filterOperator == null ? void 0 : filterOperator.requiresFilterValue) === 'undefined' ? true : filterOperator == null ? void 0 : filterOperator.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: applyFilters,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState2;\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    ((_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.filter) == null ? void 0 : _props$initialState2.filterModel) != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, (_props$initialState3 = props.initialState) == null || (_props$initialState3 = _props$initialState3.filter) == null ? void 0 : _props$initialState3.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const filterModel = (_context$stateToResto = context.stateToRestore.filter) == null ? void 0 : _context$stateToResto.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      var _props$slotProps;\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, (_props$slotProps = props.slotProps) == null ? void 0 : _props$slotProps.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, (_props$slotProps2 = props.slotProps) == null ? void 0 : _props$slotProps2.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters) {\n      return {\n        filteredRowsLookup: {},\n        filteredDescendantCountLookup: {}\n      };\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      filteredRowsLookup[id] = isRowPassing;\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const filterableColumnsLookup = gridFilterableColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && filterableColumnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n\n  // Do not call `apiRef.current.forceUpdate` to avoid re-render before updating the sorted rows.\n  // Otherwise, the state is not consistent during the render\n  useGridApiEventHandler(apiRef, 'rowsSet', updateFilteredRows);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridApiEventHandler(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues && filterModel.quickFilterExcludeHiddenColumns) {\n      // re-apply filters because the quick filter results may have changed\n      apiRef.current.unstable_applyFilters();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.unstable_applyFilters();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kCAAkC,QAAQ,gCAAgC;AACnF,SAASC,yBAAyB,QAAQ,+CAA+C;AACzF,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,sBAAsB,QAAQ,SAAS;AAChD,SAASC,4BAA4B,QAAQ,2BAA2B;AACxE,SAASC,qBAAqB,EAAEC,gCAAgC,QAAQ,+BAA+B;AACvG,SAASC,4BAA4B,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,mBAAmB;AAClJ,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC9D,IAAIC,IAAI,EAAEC,kBAAkB,EAAEC,mBAAmB;EACjD,MAAMC,WAAW,GAAG,CAACH,IAAI,GAAG,CAACC,kBAAkB,GAAGH,KAAK,CAACK,WAAW,KAAK,IAAI,GAAGF,kBAAkB,GAAG,CAACC,mBAAmB,GAAGJ,KAAK,CAACM,YAAY,KAAK,IAAI,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB,CAACG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,mBAAmB,CAACC,WAAW,KAAK,IAAI,GAAGH,IAAI,GAAGnB,yBAAyB,CAAC,CAAC;EAC7S,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACzBQ,MAAM,EAAE;MACNF,WAAW,EAAEd,mBAAmB,CAACc,WAAW,EAAEL,KAAK,CAACQ,+BAA+B,EAAEP,MAAM,CAAC;MAC5FQ,kBAAkB,EAAE,CAAC,CAAC;MACtBC,6BAA6B,EAAE,CAAC;IAClC,CAAC;IACDC,iBAAiB,EAAE,CAAC;EACtB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,oBAAoB,GAAGC,MAAM,IAAI;EACrC;EACA,OAAOA,MAAM,CAACJ,kBAAkB;AAClC,CAAC;AACD,SAASK,yBAAyBA,CAACb,MAAM,EAAEF,KAAK,EAAE;EAChD,OAAOE,MAAM,CAACc,OAAO,CAACC,sBAAsB,CAAC,2BAA2B,EAAE;IACxEC,IAAI,EAAElB,KAAK,CAACmB,IAAI,CAACD,IAAI;IACrBR,kBAAkB,EAAEV,KAAK,CAACQ,MAAM,CAACE;EACnC,CAAC,CAAC;AACJ;AACA,SAASU,oBAAoBA,CAAA,EAAG;EAC9B,OAAO7C,cAAc,CAAC8C,MAAM,CAACC,MAAM,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACrB,MAAM,EAAED,KAAK,KAAK;EAC9C,IAAIuB,oBAAoB,EAAEC,iBAAiB;EAC3C,MAAMC,MAAM,GAAG7C,aAAa,CAACqB,MAAM,EAAE,eAAe,CAAC;EACrDA,MAAM,CAACc,OAAO,CAACW,oBAAoB,CAAC;IAClCC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE5B,KAAK,CAACK,WAAW;IAC5BwB,YAAY,EAAE7B,KAAK,CAAC8B,mBAAmB;IACvCC,aAAa,EAAE/C,uBAAuB;IACtCgD,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAG5D,KAAK,CAAC6D,WAAW,CAAC,MAAM;IACjDjC,MAAM,CAACc,OAAO,CAACoB,QAAQ,CAACpC,KAAK,IAAI;MAC/B,MAAMM,WAAW,GAAGrB,uBAAuB,CAACe,KAAK,EAAEE,MAAM,CAACc,OAAO,CAACqB,UAAU,CAAC;MAC7E,MAAMC,oBAAoB,GAAGrC,KAAK,CAACsC,UAAU,KAAK,QAAQ,GAAGhD,4BAA4B,CAACe,WAAW,EAAEJ,MAAM,EAAED,KAAK,CAACuC,WAAW,CAAC,GAAG,IAAI;MACxI,MAAMC,eAAe,GAAGvC,MAAM,CAACc,OAAO,CAACC,sBAAsB,CAAC,WAAW,EAAE;QACzEqB,oBAAoB;QACpBhC,WAAW,EAAEA,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGtB,yBAAyB,CAAC;MAC7E,CAAC,CAAC;MACF,MAAM0D,QAAQ,GAAGrE,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;QACnCQ,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAACQ,MAAM,EAAEiC,eAAe;MACpD,CAAC,CAAC;MACF,MAAME,sBAAsB,GAAG5B,yBAAyB,CAACb,MAAM,EAAEwC,QAAQ,CAAC;MAC1E,OAAOrE,QAAQ,CAAC,CAAC,CAAC,EAAEqE,QAAQ,EAAE;QAC5B9B,iBAAiB,EAAE+B;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFzC,MAAM,CAACc,OAAO,CAAC4B,YAAY,CAAC,iBAAiB,CAAC;EAChD,CAAC,EAAE,CAAC1C,MAAM,EAAED,KAAK,CAACsC,UAAU,EAAEtC,KAAK,CAACuC,WAAW,CAAC,CAAC;EACjD,MAAMK,iBAAiB,GAAGvE,KAAK,CAAC6D,WAAW,CAAC,CAACW,eAAe,EAAEC,MAAM,KAAK;IACvE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,UAAU,KAAK,KAAK,IAAI/C,KAAK,CAACgD,mBAAmB,EAAE;MAC9E,OAAOH,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,sBAAsB,CAAC;EACrD,CAAC,EAAE,CAAC7C,KAAK,CAACgD,mBAAmB,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAMC,YAAY,GAAG5E,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAC3CD,kBAAkB,CAAC,CAAC;IACpBhC,MAAM,CAACc,OAAO,CAACmC,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACjD,MAAM,EAAEgC,kBAAkB,CAAC,CAAC;EAChC,MAAMkB,gBAAgB,GAAG9E,KAAK,CAAC6D,WAAW,CAACkB,IAAI,IAAI;IACjD,MAAM/C,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMoD,KAAK,GAAG,CAAC,GAAGhD,WAAW,CAACgD,KAAK,CAAC;IACpC,MAAMC,SAAS,GAAGD,KAAK,CAACE,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;IAC1E,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBD,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;IAClB,CAAC,MAAM;MACLC,KAAK,CAACC,SAAS,CAAC,GAAGF,IAAI;IACzB;IACAnD,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;MACtDgD;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAACpD,MAAM,CAAC,CAAC;EACZ,MAAM2D,iBAAiB,GAAGvF,KAAK,CAAC6D,WAAW,CAACmB,KAAK,IAAI;IACnD,MAAMhD,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAM4D,aAAa,GAAG,CAAC,GAAGxD,WAAW,CAACgD,KAAK,CAAC;IAC5CA,KAAK,CAACS,OAAO,CAACV,IAAI,IAAI;MACpB,MAAME,SAAS,GAAGD,KAAK,CAACE,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;MAC1E,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBO,aAAa,CAACH,IAAI,CAACN,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLS,aAAa,CAACP,SAAS,CAAC,GAAGF,IAAI;MACjC;IACF,CAAC,CAAC;IACFnD,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;MACtDgD;IACF,CAAC,CAAC,EAAE,mBAAmB,CAAC;EAC1B,CAAC,EAAE,CAACpD,MAAM,CAAC,CAAC;EACZ,MAAM8D,gBAAgB,GAAG1F,KAAK,CAAC6D,WAAW,CAAC8B,YAAY,IAAI;IACzD,MAAM3D,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMoD,KAAK,GAAGhD,WAAW,CAACgD,KAAK,CAAC9C,MAAM,CAAC6C,IAAI,IAAIA,IAAI,CAACK,EAAE,KAAKO,YAAY,CAACP,EAAE,CAAC;IAC3E,IAAIJ,KAAK,CAACY,MAAM,KAAK5D,WAAW,CAACgD,KAAK,CAACY,MAAM,EAAE;MAC7C;IACF;IACAhE,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;MACtDgD;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAACpD,MAAM,CAAC,CAAC;EACZ,MAAMiE,eAAe,GAAG7F,KAAK,CAAC6D,WAAW,CAAC,CAACiC,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,KAAK;IACjF5C,MAAM,CAAC6C,KAAK,CAAC,yBAAyB,CAAC;IACvC,IAAIH,iBAAiB,EAAE;MACrB,MAAM9D,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;MACnD,MAAMsE,oBAAoB,GAAGlE,WAAW,CAACgD,KAAK,CAAC9C,MAAM,CAAC6C,IAAI,IAAI;QAC5D,IAAIoB,qBAAqB;QACzB,IAAIpB,IAAI,CAACqB,KAAK,KAAKC,SAAS,EAAE;UAC5B;UACA;UACA,IAAIC,KAAK,CAACC,OAAO,CAACxB,IAAI,CAACqB,KAAK,CAAC,IAAIrB,IAAI,CAACqB,KAAK,CAACR,MAAM,KAAK,CAAC,EAAE;YACxD,OAAO,KAAK;UACd;UACA,OAAO,IAAI;QACb;QACA,MAAMY,MAAM,GAAG5E,MAAM,CAACc,OAAO,CAAC+D,SAAS,CAAC1B,IAAI,CAAC2B,KAAK,CAAC;QACnD,MAAMC,cAAc,GAAG,CAACR,qBAAqB,GAAGK,MAAM,CAACI,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,qBAAqB,CAACU,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACV,KAAK,KAAKrB,IAAI,CAAC+B,QAAQ,CAAC;QACnK,MAAMC,mBAAmB,GAAG,QAAQJ,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,mBAAmB,CAAC,KAAK,WAAW,GAAG,IAAI,GAAGJ,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,mBAAmB;;QAEvM;QACA;QACA;QACA,IAAIA,mBAAmB,EAAE;UACvB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIC,cAAc;MAClB,MAAMC,kBAAkB,GAAGf,oBAAoB,CAACW,IAAI,CAAC9B,IAAI,IAAIA,IAAI,CAAC2B,KAAK,KAAKZ,iBAAiB,CAAC;MAC9F,MAAMoB,YAAY,GAAGtF,MAAM,CAACc,OAAO,CAAC+D,SAAS,CAACX,iBAAiB,CAAC;MAChE,IAAImB,kBAAkB,EAAE;QACtBD,cAAc,GAAGd,oBAAoB;MACvC,CAAC,MAAM,IAAIvE,KAAK,CAACQ,+BAA+B,EAAE;QAChD6E,cAAc,GAAG,CAAC5F,eAAe,CAAC;UAChCsF,KAAK,EAAEZ,iBAAiB;UACxBgB,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAExE,MAAM,CAAC,CAAC;MACb,CAAC,MAAM;QACLoF,cAAc,GAAG,CAAC,GAAGd,oBAAoB,EAAE9E,eAAe,CAAC;UACzDsF,KAAK,EAAEZ,iBAAiB;UACxBgB,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAExE,MAAM,CAAC,CAAC;MACb;MACAA,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;QACtDgD,KAAK,EAAEgC;MACT,CAAC,CAAC,CAAC;IACL;IACApF,MAAM,CAACc,OAAO,CAACyE,eAAe,CAAC1G,yBAAyB,CAAC2G,OAAO,EAAErB,OAAO,EAAEC,OAAO,CAAC;EACrF,CAAC,EAAE,CAACpE,MAAM,EAAEwB,MAAM,EAAEzB,KAAK,CAACQ,+BAA+B,CAAC,CAAC;EAC3D,MAAMkF,eAAe,GAAGrH,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAC9CT,MAAM,CAAC6C,KAAK,CAAC,qBAAqB,CAAC;IACnCrE,MAAM,CAACc,OAAO,CAAC4E,eAAe,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC1F,MAAM,EAAEwB,MAAM,CAAC,CAAC;EACpB,MAAMmE,sBAAsB,GAAGvH,KAAK,CAAC6D,WAAW,CAAC2D,aAAa,IAAI;IAChE,MAAMxF,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAII,WAAW,CAACwF,aAAa,KAAKA,aAAa,EAAE;MAC/C;IACF;IACA5F,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;MACtDwF;IACF,CAAC,CAAC,EAAE,qBAAqB,CAAC;EAC5B,CAAC,EAAE,CAAC5F,MAAM,CAAC,CAAC;EACZ,MAAM6F,oBAAoB,GAAGzH,KAAK,CAAC6D,WAAW,CAACb,MAAM,IAAI;IACvD,MAAMhB,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAIN,WAAW,CAACU,WAAW,CAAC0F,iBAAiB,EAAE1E,MAAM,CAAC,EAAE;MACtD;IACF;IACApB,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;MACtD0F,iBAAiB,EAAE,CAAC,GAAG1E,MAAM;IAC/B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM0D,cAAc,GAAGtF,KAAK,CAAC6D,WAAW,CAAC,CAAC8D,KAAK,EAAEC,MAAM,KAAK;IAC1D,MAAMC,YAAY,GAAGlH,uBAAuB,CAACiB,MAAM,CAAC;IACpD,IAAIiG,YAAY,KAAKF,KAAK,EAAE;MAC1BvE,MAAM,CAAC6C,KAAK,CAAC,sBAAsB,CAAC;MACpCrE,MAAM,CAACc,OAAO,CAACoF,kBAAkB,CAAC,QAAQ,EAAE3G,yBAAyB,CAACwG,KAAK,EAAEhG,KAAK,CAACQ,+BAA+B,EAAEP,MAAM,CAAC,EAAEgG,MAAM,CAAC;MACpIhG,MAAM,CAACc,OAAO,CAACqF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAACnG,MAAM,EAAEwB,MAAM,EAAEzB,KAAK,CAACQ,+BAA+B,CAAC,CAAC;EAC3D,MAAM6F,SAAS,GAAG;IAChBT,sBAAsB;IACtBQ,qBAAqB,EAAEnD,YAAY;IACnCc,gBAAgB;IAChBZ,gBAAgB;IAChBS,iBAAiB;IACjBD,cAAc;IACdO,eAAe;IACfwB,eAAe;IACfI,oBAAoB;IACpBQ,gBAAgB,EAAEtG,KAAK,CAACsG;EAC1B,CAAC;EACD3H,gBAAgB,CAACsB,MAAM,EAAEoG,SAAS,EAAE,QAAQ,CAAC;;EAE7C;AACF;AACA;EACE,MAAME,wBAAwB,GAAGlI,KAAK,CAAC6D,WAAW,CAAC,CAACsE,SAAS,EAAEC,OAAO,KAAK;IACzE,IAAIC,oBAAoB;IACxB,MAAMC,mBAAmB,GAAG3H,uBAAuB,CAACiB,MAAM,CAAC;IAC3D,MAAM2G,uBAAuB;IAC7B;IACA,CAACH,OAAO,CAACI,qBAAqB;IAC9B;IACA7G,KAAK,CAACK,WAAW,IAAI,IAAI;IACzB;IACA,CAAC,CAACqG,oBAAoB,GAAG1G,KAAK,CAACM,YAAY,KAAK,IAAI,IAAI,CAACoG,oBAAoB,GAAGA,oBAAoB,CAACnG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmG,oBAAoB,CAACrG,WAAW,KAAK,IAAI;IACzK;IACA,CAACV,WAAW,CAACgH,mBAAmB,EAAE5H,yBAAyB,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC6H,uBAAuB,EAAE;MAC5B,OAAOJ,SAAS;IAClB;IACA,OAAOpI,QAAQ,CAAC,CAAC,CAAC,EAAEoI,SAAS,EAAE;MAC7BjG,MAAM,EAAE;QACNF,WAAW,EAAEsG;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1G,MAAM,EAAED,KAAK,CAACK,WAAW,EAAE,CAACkB,oBAAoB,GAAGvB,KAAK,CAACM,YAAY,KAAK,IAAI,IAAI,CAACiB,oBAAoB,GAAGA,oBAAoB,CAAChB,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,oBAAoB,CAAClB,WAAW,CAAC,CAAC;EAChM,MAAMyG,yBAAyB,GAAGzI,KAAK,CAAC6D,WAAW,CAAC,CAACrB,MAAM,EAAE4F,OAAO,KAAK;IACvE,IAAIM,qBAAqB;IACzB,MAAM1G,WAAW,GAAG,CAAC0G,qBAAqB,GAAGN,OAAO,CAACO,cAAc,CAACzG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwG,qBAAqB,CAAC1G,WAAW;IAChI,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB,OAAOQ,MAAM;IACf;IACAZ,MAAM,CAACc,OAAO,CAACoF,kBAAkB,CAAC,QAAQ,EAAE3G,yBAAyB,CAACa,WAAW,EAAEL,KAAK,CAACQ,+BAA+B,EAAEP,MAAM,CAAC,EAAE,cAAc,CAAC;IAClJ,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAEyC,MAAM,EAAE;MAC1BoG,SAAS,EAAE,CAAC,GAAGpG,MAAM,CAACoG,SAAS,EAAEhH,MAAM,CAACc,OAAO,CAACqF,qBAAqB;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnG,MAAM,EAAED,KAAK,CAACQ,+BAA+B,CAAC,CAAC;EACnD,MAAM0G,4BAA4B,GAAG7I,KAAK,CAAC6D,WAAW,CAAC,CAACiF,YAAY,EAAE1C,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAK3F,yBAAyB,CAAC2G,OAAO,EAAE;MAC/C,IAAI2B,gBAAgB;MACpB,MAAMC,WAAW,GAAGrH,KAAK,CAACsH,KAAK,CAACC,WAAW;MAC3C,OAAO,aAAa1H,IAAI,CAACwH,WAAW,EAAEjJ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACgJ,gBAAgB,GAAGpH,KAAK,CAACwH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,gBAAgB,CAACG,WAAW,CAAC,CAAC;IAC3I;IACA,OAAOJ,YAAY;EACrB,CAAC,EAAE,CAACnH,KAAK,CAACsH,KAAK,CAACC,WAAW,EAAE,CAAC/F,iBAAiB,GAAGxB,KAAK,CAACwH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhG,iBAAiB,CAAC+F,WAAW,CAAC,CAAC;EACrH,MAAM;IACJE;EACF,CAAC,GAAGzH,KAAK;EACT,MAAM0H,UAAU,GAAGjJ,UAAU,CAAC0C,oBAAoB,CAAC;EACnD,MAAMwG,mBAAmB,GAAGtJ,KAAK,CAAC6D,WAAW,CAACrB,MAAM,IAAI;IACtD,IAAIb,KAAK,CAACsC,UAAU,KAAK,QAAQ,IAAI,CAACzB,MAAM,CAACwB,oBAAoB,EAAE;MACjE,OAAO;QACL5B,kBAAkB,EAAE,CAAC,CAAC;QACtBC,6BAA6B,EAAE,CAAC;MAClC,CAAC;IACH;IACA,MAAMkH,sBAAsB,GAAG1I,sBAAsB,CAACe,MAAM,CAAC;IAC7D,MAAMQ,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAM;MACJ4B;IACF,CAAC,GAAGxB,MAAM;IACV,MAAMgH,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMC,MAAM,GAAG;MACbC,kBAAkB,EAAE,IAAI;MACxBC,wBAAwB,EAAE;IAC5B,CAAC;IACD,MAAM9G,IAAI,GAAGwG,UAAU,CAAC3G,OAAO,CAACd,MAAM,CAACc,OAAO,CAAChB,KAAK,CAACmB,IAAI,CAAC0G,sBAAsB,CAAC;IACjF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/G,IAAI,CAAC+C,MAAM,EAAEgE,CAAC,IAAI,CAAC,EAAE;MACvC,MAAMC,GAAG,GAAGhH,IAAI,CAAC+G,CAAC,CAAC;MACnB,MAAMxE,EAAE,GAAGgE,QAAQ,GAAGA,QAAQ,CAACS,GAAG,CAAC,GAAGA,GAAG,CAACzE,EAAE;MAC5CpB,oBAAoB,CAAC6F,GAAG,EAAExD,SAAS,EAAEoD,MAAM,CAAC;MAC5C,MAAMK,YAAY,GAAGzI,eAAe,CAAC,CAACoI,MAAM,CAACC,kBAAkB,CAAC,EAAE,CAACD,MAAM,CAACE,wBAAwB,CAAC,EAAEnH,MAAM,CAACR,WAAW,EAAEJ,MAAM,EAAE4H,WAAW,CAAC;MAC7IpH,kBAAkB,CAACgD,EAAE,CAAC,GAAG0E,YAAY;IACvC;IACA,MAAMC,QAAQ,GAAG,kCAAkC;IACnD,MAAMC,MAAM,GAAGT,sBAAsB,CAACQ,QAAQ,CAAC;IAC/C,IAAIC,MAAM,EAAE;MACV5H,kBAAkB,CAAC2H,QAAQ,CAAC,GAAG,IAAI;IACrC;IACA,OAAO;MACL3H,kBAAkB;MAClBC,6BAA6B,EAAE,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACT,MAAM,EAAED,KAAK,CAACsC,UAAU,EAAEmF,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACpDvI,4BAA4B,CAACc,MAAM,EAAE,YAAY,EAAE2C,iBAAiB,CAAC;EACrEzD,4BAA4B,CAACc,MAAM,EAAE,aAAa,EAAEsG,wBAAwB,CAAC;EAC7EpH,4BAA4B,CAACc,MAAM,EAAE,cAAc,EAAE6G,yBAAyB,CAAC;EAC/E3H,4BAA4B,CAACc,MAAM,EAAE,iBAAiB,EAAEiH,4BAA4B,CAAC;EACrF7H,gCAAgC,CAACY,MAAM,EAAEb,qBAAqB,EAAE,WAAW,EAAEuI,mBAAmB,CAAC;EACjGtI,gCAAgC,CAACY,MAAM,EAAEb,qBAAqB,EAAE,2BAA2B,EAAEwB,oBAAoB,CAAC;;EAElH;AACF;AACA;EACE,MAAM0H,mBAAmB,GAAGjK,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAClDT,MAAM,CAAC6C,KAAK,CAAC,sDAAsD,CAAC;IACpE,MAAMjE,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMsI,uBAAuB,GAAG1J,kCAAkC,CAACoB,MAAM,CAAC;IAC1E,MAAMoF,cAAc,GAAGhF,WAAW,CAACgD,KAAK,CAAC9C,MAAM,CAAC6C,IAAI,IAAIA,IAAI,CAAC2B,KAAK,IAAIwD,uBAAuB,CAACnF,IAAI,CAAC2B,KAAK,CAAC,CAAC;IAC1G,IAAIM,cAAc,CAACpB,MAAM,GAAG5D,WAAW,CAACgD,KAAK,CAACY,MAAM,EAAE;MACpDhE,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAACvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;QACtDgD,KAAK,EAAEgC;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACpF,MAAM,EAAEwB,MAAM,CAAC,CAAC;EACpB,MAAM+G,6BAA6B,GAAGnK,KAAK,CAAC6D,WAAW,CAACuG,UAAU,IAAI;IACpE,IAAIA,UAAU,KAAK,WAAW,EAAE;MAC9BxI,MAAM,CAACc,OAAO,CAACqF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAACnG,MAAM,CAAC,CAAC;EACZ,MAAMyI,4BAA4B,GAAGrK,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAC3DjC,MAAM,CAACc,OAAO,CAACoB,QAAQ,CAACpC,KAAK,IAAI;MAC/B,OAAO3B,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;QACzBY,iBAAiB,EAAEG,yBAAyB,CAACb,MAAM,EAAEF,KAAK;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC;IACFE,MAAM,CAACc,OAAO,CAACmC,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;;EAEZ;EACA;EACAvB,sBAAsB,CAACuB,MAAM,EAAE,SAAS,EAAEgC,kBAAkB,CAAC;EAC7DvD,sBAAsB,CAACuB,MAAM,EAAE,eAAe,EAAEqI,mBAAmB,CAAC;EACpE5J,sBAAsB,CAACuB,MAAM,EAAE,+BAA+B,EAAEuI,6BAA6B,CAAC;EAC9F9J,sBAAsB,CAACuB,MAAM,EAAE,oBAAoB,EAAEyI,4BAA4B,CAAC;EAClFhK,sBAAsB,CAACuB,MAAM,EAAE,6BAA6B,EAAE,MAAM;IAClE,MAAMI,WAAW,GAAGrB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAII,WAAW,CAAC0F,iBAAiB,IAAI1F,WAAW,CAACsI,+BAA+B,EAAE;MAChF;MACA1I,MAAM,CAACc,OAAO,CAACqF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,CAAC;;EAEF;AACF;AACA;EACEnH,cAAc,CAAC,MAAM;IACnBgB,MAAM,CAACc,OAAO,CAACqF,qBAAqB,CAAC,CAAC;EACxC,CAAC,CAAC;;EAEF;AACF;AACA;EACE5H,iBAAiB,CAAC,MAAM;IACtB,IAAIwB,KAAK,CAACK,WAAW,KAAKqE,SAAS,EAAE;MACnCzE,MAAM,CAACc,OAAO,CAAC4C,cAAc,CAAC3D,KAAK,CAACK,WAAW,CAAC;IAClD;EACF,CAAC,EAAE,CAACJ,MAAM,EAAEwB,MAAM,EAAEzB,KAAK,CAACK,WAAW,CAAC,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}