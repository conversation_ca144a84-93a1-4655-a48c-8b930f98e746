{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root', 'avatar']);\nexport default avatarGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getAvatarGroupUtilityClass", "slot", "avatarGroupClasses"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/material/AvatarGroup/avatarGroupClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root', 'avatar']);\nexport default avatarGroupClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvF,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}