[{"C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ErrorSnackbar.js": "3", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\FileUpload.js": "4", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ProcessForm.js": "5", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\WorksheetSelect.js": "6", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ResultDisplay.js": "7", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\config.js": "8", "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\utils\\fileUtils.js": "9"}, {"size": 241, "mtime": 1751059944898, "results": "10", "hashOfConfig": "11"}, {"size": 15805, "mtime": 1754230200426, "results": "12", "hashOfConfig": "11"}, {"size": 544, "mtime": 1751059944899, "results": "13", "hashOfConfig": "11"}, {"size": 3151, "mtime": 1753204624341, "results": "14", "hashOfConfig": "11"}, {"size": 15382, "mtime": 1753204624462, "results": "15", "hashOfConfig": "11"}, {"size": 4538, "mtime": 1754230200437, "results": "16", "hashOfConfig": "11"}, {"size": 68486, "mtime": 1754230222410, "results": "17", "hashOfConfig": "11"}, {"size": 96, "mtime": 1754229567278, "results": "18", "hashOfConfig": "11"}, {"size": 1282, "mtime": 1752365205996, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ae7dql", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ErrorSnackbar.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ProcessForm.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\WorksheetSelect.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\components\\ResultDisplay.js", ["47", "48", "49"], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\config.js", [], [], "C:\\Users\\<USER>\\Code\\My Project\\commision-calculation\\frontend\\src\\utils\\fileUtils.js", [], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 56, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 56, "endColumn": 23}, {"ruleId": "54", "severity": 1, "message": "55", "line": 426, "column": 39, "nodeType": "52", "endLine": 426, "endColumn": 50}, {"ruleId": "54", "severity": 1, "message": "55", "line": 439, "column": 33, "nodeType": "52", "endLine": 439, "endColumn": 44}, "no-unused-vars", "'FixedSizeList' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead."]