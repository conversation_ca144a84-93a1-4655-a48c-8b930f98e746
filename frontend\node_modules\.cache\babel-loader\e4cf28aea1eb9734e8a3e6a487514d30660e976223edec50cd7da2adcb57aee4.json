{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip, Card, CardContent, Stack, Slide, FormControl, InputLabel, Select, MenuItem, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK COMPULSORY 2ND SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"SPARK PLUG\", \"REPLACE BRAKE PADS\", \"REPLACE BATTERY\", \"REPLACE WIPER RUBBER\", \"None\"];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 10\n  }, this);\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\n_c = SlideDownTransition;\nconst RemarkChip = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c2 = _s(({\n  rowId,\n  text,\n  isSelected,\n  onClick\n}) => {\n  _s();\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n    text: text,\n    isSelected: isSelected\n  });\n\n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n\n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = e => {\n    onClick(rowId);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleClick,\n    variant: uiState.isSelected ? 'contained' : 'outlined',\n    color: \"primary\",\n    size: \"small\",\n    sx: {\n      minWidth: '150px',\n      maxWidth: '300px',\n      fontSize: '0.75rem',\n      textTransform: 'none',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      transition: 'all 0.2s ease-in-out',\n      height: 'auto',\n      lineHeight: 1.2\n    },\n    children: uiState.text || '点击选择'\n  }, `remark-${rowId}-${uiState.isSelected}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n}, \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\")), \"VEYxYAbOMN1KMgaZZEjv3fGxeic=\");\n_c3 = RemarkChip;\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s2();\n  // 先声明columnOrder\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'left'\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'left'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'left'\n  }];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPage(0);\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态\n  const [pageSize, setPageSize] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return saved ? parseInt(saved, 10) : 25;\n  });\n  const [page, setPage] = useState(0);\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', pageSize.toString());\n  }, [pageSize]);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = data => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n          }\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => {\n      if (changedRow && row.id === changedRow.id) {\n        return sum + (Number(changedRow.COMMISSION) || 0);\n      }\n      return sum + (Number(row.COMMISSION) || 0);\n    }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback(data => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(debounce(data => {\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(data));\n      console.log('防抖保存数据到localStorage:', data.length);\n    } catch (error) {\n      console.error('保存编辑数据到localStorage失败:', error);\n    }\n  }, 2000),\n  // 2秒防抖，减少保存频率\n  []);\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(debounce(data => {\n    if (onDataChange) {\n      onDataChange([...data]);\n      console.log('防抖通知父组件数据变化');\n    }\n  }, 1500),\n  // 1.5秒防抖，减少通知频率\n  [onDataChange]);\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback(newRow => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return {\n          ...row,\n          ...newRow\n        };\n        if (row.NO === 'TOTAL') return {\n          ...row,\n          COMMISSION: totalValue\n        };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n  const onProcessRowUpdateError = error => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n\n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return {\n                  ...row,\n                  REMARKS: '',\n                  _selected_remarks: ''\n                };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n                return {\n                  ...row,\n                  REMARKS: finalOption,\n                  _selected_remarks: finalOption\n                };\n              }\n            }\n            return row;\n          });\n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n\n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n\n  // 删除行数据\n  const handleRemoveRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: true\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n\n  // 恢复行数据\n  const handleUndoRow = useCallback(id => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? {\n        ...row,\n        _removed: false\n      } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback(id => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback(afterRowId => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || []).filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = fileId && fileId.startsWith('recovered_') ? 'recovered_data' : fileId;\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n\n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n  const columns = useMemo(() => columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  transition: 'all 0.2s ease-in-out',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this);\n          }\n          let remarkText = '点击选择';\n          let isSelected = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          return /*#__PURE__*/_jsxDEV(RemarkChip, {\n            rowId: params.row.id,\n            text: remarkText,\n            isSelected: isSelected,\n            onClick: handleRemarksClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: params => {\n          if (params.row.NO === 'TOTAL') return '';\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 0.5,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u5728\\u6B64\\u884C\\u4E0B\\u65B9\\u6DFB\\u52A0\\u65B0\\u884C\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                onClick: () => handleAddRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u6C38\\u4E45\\u5220\\u9664\\u6B64\\u884C\\uFF08\\u65E0\\u6CD5\\u6062\\u590D\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => handleDeleteRow(params.row.id),\n                sx: {\n                  '&:hover': {\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), params.row._removed ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u6062\\u590D\"\n            }, \"undo\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 30\n              }, this),\n              onClick: () => handleRemoveRow(params.row.id),\n              sx: {\n                fontSize: '0.75rem',\n                textTransform: 'none',\n                minWidth: '70px'\n              },\n              children: \"\\u79FB\\u9664\"\n            }, \"remove\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: params => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this);\n        }\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this);\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n\n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value => value && value.toString().toLowerCase().includes(searchLower));\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n\n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1011,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary',\n                  mb: 0.5\n                },\n                children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'text.secondary'\n                },\n                children: \"\\u6570\\u636E\\u5904\\u7406\\u5B8C\\u6210\\uFF0C\\u53EF\\u4EE5\\u7F16\\u8F91\\u548C\\u5BFC\\u51FA\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            sx: {\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TableViewIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 23\n              }, this),\n              label: `${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`,\n              color: \"primary\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 23\n              }, this),\n              label: `总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`,\n              color: \"success\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 15\n            }, this), (memoGridData || []).filter(row => row._removed).length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 25\n              }, this),\n              label: `${(memoGridData || []).filter(row => row._removed).length} 条已删除`,\n              color: \"warning\",\n              variant: \"outlined\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1031,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1029,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u64CD\\u4F5C\\u9009\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"success\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 26\n            }, this),\n            onClick: handleDownload,\n            children: \"\\u4E0B\\u8F7DExcel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: isGeneratingDocument ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1093,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1093,\n              columnNumber: 98\n            }, this),\n            onClick: generateDocument,\n            disabled: isGeneratingDocument,\n            children: isGeneratingDocument ? '生成中...' : '生成文档'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1090,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 26\n            }, this),\n            onClick: handleCleanup,\n            children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1075,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: \"\\u6570\\u636E\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"\\u641C\\u7D22\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: searchColumn,\n              label: \"\\u641C\\u7D22\\u8303\\u56F4\",\n              onChange: e => setSearchColumn(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\\u5217\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NO\",\n                children: \"NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"DATE\",\n                children: \"DATE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"VEHICLE NO\",\n                children: \"VEHICLE NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"RO NO\",\n                children: \"RO NO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"KM\",\n                children: \"KM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"REMARKS\",\n                children: \"REMARKS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"MAXCHECK\",\n                children: \"HOURS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COMMISSION\",\n                children: \"AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: \"\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            sx: {\n              flexGrow: 1,\n              minWidth: 200\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 19\n              }, this),\n              endAdornment: searchText && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => setSearchText(''),\n                  edge: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1157,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1152,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 13\n          }, this), searchText && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"\\u627E\\u5230 \", filteredGridData.filter(row => row.NO !== 'TOTAL').length, \" \\u6761\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: memoGridData,\n          columns: columns,\n          pageSize: pageSize,\n          onPageSizeChange: newPageSize => setPageSize(newPageSize),\n          page: page,\n          onPageChange: newPage => setPage(newPage),\n          rowsPerPageOptions: [25, 50, 150],\n          pagination: true,\n          disableSelectionOnClick: true,\n          headerHeight: 64,\n          columnHeaderHeight: 64,\n          disableVirtualization: false,\n          rowHeight: 48,\n          density: \"compact\",\n          rowBuffer: 5,\n          columnBuffer: 2,\n          disableColumnMenu: true,\n          disableColumnFilter: true,\n          disableColumnSelector: true,\n          disableDensitySelector: true,\n          hideFooterSelectedRowCount: true,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable && typeof params.colDef.editable === 'function' ? params.colDef.editable(params) : params.colDef.editable;\n          },\n          processRowUpdate: newRow => {\n            if (newRow.COMMISSION !== undefined) {\n              if (typeof newRow.COMMISSION === 'string') {\n                newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n              }\n            }\n            return processRowUpdate(newRow);\n          },\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled',\n              textDecoration: 'line-through'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px',\n              borderBottom: '1px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: 'background.default',\n              borderBottom: '2px solid',\n              borderColor: 'divider'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              backgroundColor: 'background.default',\n              color: 'text.primary',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              '&:last-child': {\n                borderRight: 'none'\n              }\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              fontWeight: 'bold',\n              color: 'text.primary',\n              fontSize: '0.875rem'\n            },\n            '& .MuiDataGrid-columnSeparator': {\n              display: 'none'\n            },\n            minHeight: 500\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1176,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1175,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1278,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 3,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 1\n          },\n          children: \"\\u989D\\u5916\\u9009\\u9879\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: cbuCarChecked,\n              onChange: e => setCbuCarChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1295,\n              columnNumber: 17\n            }, this),\n            label: \"CBU CAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: wtyChecked,\n              onChange: e => setWtyChecked(e.target.checked),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1305,\n              columnNumber: 17\n            }, this),\n            label: \"WTY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FixedSizeList, {\n          height: 300,\n          itemCount: remarksOptions.length,\n          itemSize: 48,\n          width: \"100%\",\n          children: ({\n            index,\n            style\n          }) => {\n            const option = remarksOptions[index];\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              style: style,\n              disablePadding: true,\n              secondaryAction: option !== 'None' && /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                \"aria-label\": \"delete\",\n                onClick: () => deleteOption(option),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => selectRemarkOption(option),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 19\n              }, this)\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 17\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1354,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1027,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"ik8tPzNKxx98mhJyq6lc1zJv05g=\");\n_c4 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SlideDownTransition\");\n$RefreshReg$(_c2, \"RemarkChip$React.memo\");\n$RefreshReg$(_c3, \"RemarkChip\");\n$RefreshReg$(_c4, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useMemo", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Slide", "FormControl", "InputLabel", "Select", "MenuItem", "InputAdornment", "Checkbox", "FormControlLabel", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "AssessmentIcon", "TableViewIcon", "TrendingUpIcon", "SearchIcon", "ClearIcon", "AddCircleOutlineIcon", "RemoveCircleOutlineIcon", "axios", "API_URL", "FixedSizeList", "toast", "jsxDEV", "_jsxDEV", "debounce", "func", "wait", "timeout", "executedFunction", "args", "later", "clearTimeout", "setTimeout", "DEFAULT_REMARKS_OPTIONS", "SlideDownTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RemarkChip", "_s", "memo", "_c2", "rowId", "text", "isSelected", "onClick", "uiState", "setUiState", "handleClick", "e", "variant", "color", "size", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fontSize", "textTransform", "overflow", "textOverflow", "whiteSpace", "transition", "height", "lineHeight", "children", "_c3", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s2", "columnOrder", "field", "headerName", "editable", "headerAlign", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "searchText", "setSearchText", "debouncedSearchText", "setDebouncedSearchText", "searchColumn", "setSearchColumn", "timer", "setPage", "cbuCarChecked", "setCbuCarChecked", "wtyChecked", "setWtyChecked", "pageSize", "setPageSize", "saved", "parseInt", "page", "setItem", "toString", "originalData", "setOriginalData", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "lastNotifiedDataRef", "lastNotifyTimeRef", "notifyTimeoutRef", "notificationCounter", "getKeyData", "COMMISSION", "now", "Date", "keyData", "lastKeyData", "current", "remarksDialog", "setRemarksDialog", "open", "currentValue", "showToast", "message", "type", "success", "duration", "position", "style", "background", "borderRadius", "fontWeight", "padding", "boxShadow", "error", "handleDownload", "startsWith", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleCleanup", "delete", "handleCellEdit", "params", "getTotalCommission", "changedRow", "dataToUse", "Array", "isArray", "filter", "reduce", "sum", "Number", "recalculateTotal", "totalRow", "find", "newTotal", "debouncedSaveToLocalStorage", "debouncedNotifyParent", "processRowUpdate", "newRow", "prev", "totalValue", "updatedData", "onProcessRowUpdateError", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "window", "requestAnimationFrame", "prevData", "finalOption", "suffixes", "push", "join", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "noCounter", "for<PERSON>ach", "handleUndoRow", "handleDeleteRow", "filteredData", "handleAddRow", "afterRowId", "insertIndex", "findIndex", "newId", "currentRow", "newRowNo", "DATE", "KM", "MAXCHECK", "newData", "splice", "generateDocument", "filteredRows", "sort", "a", "b", "docData", "split", "Math", "floor", "HOURS", "toFixed", "AMOUNT", "totalAmount", "actualFileId", "response", "post", "docId", "docUrl", "iframe", "display", "src", "Error", "handleRemarksClick", "value", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "label", "opacity", "remarkText", "gap", "alignItems", "backgroundColor", "startIcon", "isNaN", "textDecoration", "Boolean", "filteredGridData", "searchLower", "toLowerCase", "Object", "values", "some", "cellValue", "memoGridData", "textAlign", "py", "mt", "mb", "justifyContent", "flexWrap", "spacing", "icon", "xs", "sm", "disabled", "onChange", "target", "placeholder", "flexGrow", "InputProps", "startAdornment", "endAdornment", "edge", "rows", "onPageSizeChange", "newPageSize", "onPageChange", "newPage", "rowsPerPageOptions", "pagination", "disableSelectionOnClick", "headerHeight", "columnHeaderHeight", "disableVirtualization", "rowHeight", "density", "<PERSON><PERSON><PERSON><PERSON>", "columnBuffer", "disableColumnMenu", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnSelector", "disableDensitySelector", "hideFooterSelectedRowCount", "getRowClassName", "isCellEditable", "colDef", "borderBottom", "borderColor", "borderRight", "minHeight", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "px", "pb", "control", "checked", "dividers", "p", "itemCount", "itemSize", "disablePadding", "secondaryAction", "primary", "autoFocus", "margin", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip,\n  Card,\n  CardContent,\n  Stack,\n  Slide,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  InputAdornment,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport AssessmentIcon from '@mui/icons-material/Assessment';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport SearchIcon from '@mui/icons-material/Search';\nimport ClearIcon from '@mui/icons-material/Clear';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';\nimport axios from 'axios';\nimport { API_URL } from '../config';\nimport { FixedSizeList } from 'react-window';\nimport toast from 'react-hot-toast';\n\n// 简单的防抖函数\nfunction debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK COMPULSORY 2ND SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"SPARK PLUG\",\n  \"REPLACE BRAKE PADS\",\n  \"REPLACE BATTERY\",\n  \"REPLACE WIPER RUBBER\",\n  \"None\"\n];\n\n// 组件内部定义滑入动画\nfunction SlideDownTransition(props) {\n  return <Slide {...props} direction=\"down\" />;\n}\n\n// 更高效的REMARKS选择组件，带CSS过渡\nconst RemarkChip = React.memo(({ rowId, text, isSelected, onClick }) => {\n  // 添加内部状态，用于立即更新UI效果\n  const [uiState, setUiState] = useState({\n \n    text: text,\n    isSelected: isSelected\n  });\n  \n  // 当props变化时更新内部状态\n  useEffect(() => {\n    setUiState({\n      text: text,\n      isSelected: isSelected\n    });\n  }, [text, isSelected]);\n  \n  // 点击时先更新UI状态，再调用onClick\n  const handleClick = (e) => {\n    onClick(rowId);\n  };\n  \n  return (\n    <Button\n      key={`remark-${rowId}-${uiState.isSelected}`}\n      onClick={handleClick}\n      variant={uiState.isSelected ? 'contained' : 'outlined'}\n      color=\"primary\"\n      size=\"small\"\n      sx={{\n        minWidth: '150px',\n        maxWidth: '300px',\n        fontSize: '0.75rem',\n        textTransform: 'none',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n        transition: 'all 0.2s ease-in-out',\n        height: 'auto',\n        lineHeight: 1.2\n      }}\n    >\n      {uiState.text || '点击选择'}\n    </Button>\n  );\n});\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 先声明columnOrder\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'left' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'left' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'left' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'left' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'left' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'left' },\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'left' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'left' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'left' }\n  ];\n\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 搜索功能状态\n  const [searchText, setSearchText] = useState('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState('');\n  const [searchColumn, setSearchColumn] = useState('all'); // 'all' 或具体列名\n\n  // 搜索防抖\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300); // 300ms防抖\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  // 当搜索结果变化时重置到第一页\n  useEffect(() => {\n    setPage(0);\n  }, [debouncedSearchText, searchColumn]);\n\n  // REMARKS对话框的额外选项状态\n  const [cbuCarChecked, setCbuCarChecked] = useState(false);\n  const [wtyChecked, setWtyChecked] = useState(false);\n\n  // DataGrid分页状态\n  const [pageSize, setPageSize] = useState(() => {\n    const saved = localStorage.getItem('dataGridPageSize');\n    return saved ? parseInt(saved, 10) : 25;\n  });\n  const [page, setPage] = useState(0);\n\n  // 保存分页大小到localStorage\n  useEffect(() => {\n    localStorage.setItem('dataGridPageSize', pageSize.toString());\n  }, [pageSize]);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = (data || []).map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 使用ref来避免无限循环\n  const lastNotifiedDataRef = useRef(null);\n  const lastNotifyTimeRef = useRef(0);\n  const notifyTimeoutRef = useRef(null);\n\n  // 添加一个递增计数器，用于生成唯一的通知key\n  const notificationCounter = useRef(0);\n\n  // 只比较关键字段\n  const getKeyData = (data) => data.map(row => ({\n    id: row.id,\n    NO: row.NO,\n    _removed: row._removed,\n    REMARKS: row.REMARKS,\n    _selected_remarks: row._selected_remarks,\n    COMMISSION: row.COMMISSION\n  }));\n\n  // 当gridData变化时，通知父组件（节流+diff）\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      const now = Date.now();\n      const keyData = JSON.stringify(getKeyData(gridData));\n      const lastKeyData = lastNotifiedDataRef.current;\n      if (lastKeyData !== keyData) {\n        // 节流：300ms内只通知一次\n        if (notifyTimeoutRef.current) {\n          clearTimeout(notifyTimeoutRef.current);\n        }\n        notifyTimeoutRef.current = setTimeout(() => {\n          lastNotifiedDataRef.current = keyData;\n          lastNotifyTimeRef.current = Date.now();\n          onDataChange([...gridData]);\n        }, 300);\n      }\n    }\n    return () => {\n      if (notifyTimeoutRef.current) {\n        clearTimeout(notifyTimeoutRef.current);\n      }\n    };\n  }, [gridData, onDataChange]);\n  \n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  // 使用react-hot-toast替代snackbar\n  const showToast = useCallback((message, type = 'success') => {\n    switch (type) {\n      case 'success':\n        toast.success(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#4caf50',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'error':\n        toast.error(message, {\n          duration: 4000,\n          position: 'bottom-right',\n          style: {\n            background: '#f44336',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'info':\n        toast(message, {\n          duration: 3000,\n          position: 'bottom-right',\n          style: {\n            background: '#2196f3',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      case 'warning':\n        toast(message, {\n          duration: 3500,\n          position: 'bottom-right',\n          style: {\n            background: '#ff9800',\n            color: 'white',\n            borderRadius: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            padding: '12px 16px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n          },\n        });\n        break;\n      default:\n        toast(message);\n    }\n  }, []);\n\n  const handleDownload = async () => {\n    try {\n      // 检查是否是恢复的状态\n      if (fileId && fileId.startsWith('recovered_')) {\n        onError('无法下载原始Excel文件，因为这是从保存状态恢复的数据。请重新上传文件进行处理。');\n        return;\n      }\n\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      showToast('正在下载Excel文件...', 'success');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  // 新增：只返回总和\n  const getTotalCommission = useCallback((data, changedRow) => {\n    const dataToUse = data || gridData || [];\n    if (!Array.isArray(dataToUse)) {\n      return 0;\n    }\n    return dataToUse\n      .filter(row => row.NO !== 'TOTAL' && !row._removed)\n      .reduce((sum, row) => {\n        if (changedRow && row.id === changedRow.id) {\n          return sum + (Number(changedRow.COMMISSION) || 0);\n        }\n        return sum + (Number(row.COMMISSION) || 0);\n      }, 0);\n  }, [gridData]);\n\n  // 原 recalculateTotal 保持返回新数组\n  const recalculateTotal = useCallback((data) => {\n    const totalRow = data.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = data\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (Number(row.COMMISSION) || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n    return data;\n  }, []);\n\n  // 防抖保存到localStorage\n  const debouncedSaveToLocalStorage = useCallback(\n    debounce((data) => {\n      try {\n        localStorage.setItem('savedGridData', JSON.stringify(data));\n        console.log('防抖保存数据到localStorage:', data.length);\n      } catch (error) {\n        console.error('保存编辑数据到localStorage失败:', error);\n      }\n    }, 2000), // 2秒防抖，减少保存频率\n    []\n  );\n\n  // 防抖通知父组件\n  const debouncedNotifyParent = useCallback(\n    debounce((data) => {\n      if (onDataChange) {\n        onDataChange([...data]);\n        console.log('防抖通知父组件数据变化');\n      }\n    }, 1500), // 1.5秒防抖，减少通知频率\n    [onDataChange]\n  );\n\n  // processRowUpdate 只用 getTotalCommission\n  const processRowUpdate = useCallback((newRow) => {\n    setGridData(prev => {\n      let totalValue = getTotalCommission(prev, newRow);\n      const updatedData = prev.map(row => {\n        if (row.id === newRow.id) return { ...row, ...newRow };\n        if (row.NO === 'TOTAL') return { ...row, COMMISSION: totalValue };\n        return row;\n      });\n\n      // 使用防抖保存和通知\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      return updatedData;\n    });\n    return newRow;\n  }, [getTotalCommission, debouncedSaveToLocalStorage, debouncedNotifyParent]);\n\n  const onProcessRowUpdateError = (error) => {\n    showToast(`更新失败: ${error.message}`, 'error');\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n    // 重置勾选状态\n    setCbuCarChecked(false);\n    setWtyChecked(false);\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      // 1. 立即关闭对话框\n      closeRemarksDialog();\n      \n      // 2. 立即更新状态 - 使用批处理以确保同步更新\n      window.requestAnimationFrame(() => {\n        setGridData(prevData => {\n          let updatedData = prevData.map(row => {\n            if (row.id === rowId) {\n              if (option === \"None\") {\n                return { ...row, REMARKS: '', _selected_remarks: '' };\n              } else {\n                // 根据勾选状态添加后缀\n                let finalOption = option;\n                const suffixes = [];\n\n                if (cbuCarChecked) {\n                  suffixes.push('CBU CAR');\n                }\n                if (wtyChecked) {\n                  suffixes.push('WTY');\n                }\n\n                if (suffixes.length > 0) {\n                  finalOption = `${option} (${suffixes.join(', ')})`;\n                }\n\n                return { ...row, REMARKS: finalOption, _selected_remarks: finalOption };\n              }\n            }\n            return row;\n          });\n          \n          updatedData = recalculateTotal(updatedData);\n          return updatedData;\n        });\n        \n        // 3. 通知显示延后，避免抢占渲染线程\n        setTimeout(() => {\n          showToast('REMARKS已更新', 'success');\n        }, 50);\n      });\n    }\n  }, [remarksDialog, closeRemarksDialog, recalculateTotal, showToast, cbuCarChecked, wtyChecked]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      showToast('新选项已添加', 'success');\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      showToast('该选项已存在', 'error');\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog, showToast]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    showToast('选项已删除', 'success');\n  }, [showToast]);\n  \n  // 删除行数据\n  const handleRemoveRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: true } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n\n    showToast('行已移除并重新编号', 'info');\n  }, [recalculateTotal, showToast]);\n  \n  // 恢复行数据\n  const handleUndoRow = useCallback((id) => {\n    setGridData(prev => {\n      let updatedData = prev.map(row => row.id === id ? { ...row, _removed: false } : row);\n\n      // 重新编号：按照数组中的实际位置编号，而不是按ID排序\n      let noCounter = 1;\n      updatedData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      updatedData = recalculateTotal(updatedData);\n      return updatedData;\n    });\n    setTimeout(() => {\n      showToast('行已恢复并重新编号', 'success');\n    }, 0);\n  }, [recalculateTotal, showToast]);\n\n  // 永久删除行功能\n  const handleDeleteRow = useCallback((id) => {\n    setGridData(prev => {\n      // 过滤掉要删除的行\n      const filteredData = prev.filter(row => row.id !== id);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      filteredData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(filteredData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('行已永久删除', 'warning');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 添加新行功能\n  const handleAddRow = useCallback((afterRowId) => {\n    setGridData(prev => {\n      // 找到要插入位置的索引\n      const insertIndex = prev.findIndex(row => row.id === afterRowId);\n      if (insertIndex === -1) return prev;\n\n      // 生成新的ID（使用时间戳确保唯一性）\n      const newId = Date.now();\n\n      // 计算新行的NO值（插入位置的下一个编号）\n      const currentRow = prev[insertIndex];\n      const newRowNo = typeof currentRow.NO === 'number' ? currentRow.NO + 1 : 1;\n\n      // 创建新行数据\n      const newRow = {\n        id: newId,\n        NO: newRowNo,\n        DATE: '',\n        'VEHICLE NO': '',\n        'RO NO': '',\n        KM: '',\n        REMARKS: '',\n        MAXCHECK: '',\n        COMMISSION: 0,\n        _selected_remarks: '',\n        _removed: false,\n        isTotal: false\n      };\n\n      // 创建新的数组，在指定位置插入新行\n      const newData = [...prev];\n      newData.splice(insertIndex + 1, 0, newRow);\n\n      // 重新编号：按照数组中的实际位置编号\n      let noCounter = 1;\n      newData.forEach(row => {\n        if (row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number') {\n          row.NO = noCounter++;\n        }\n      });\n\n      // 重新计算总计\n      const updatedData = recalculateTotal(newData);\n\n      // 保存到localStorage\n      debouncedSaveToLocalStorage(updatedData);\n      debouncedNotifyParent(updatedData);\n\n      showToast('新行已添加', 'success');\n      return updatedData;\n    });\n  }, [recalculateTotal, debouncedSaveToLocalStorage, debouncedNotifyParent, showToast]);\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = (gridData || [])\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      // 检查是否是恢复的状态，决定使用哪个fileId\n      const actualFileId = (fileId && fileId.startsWith('recovered_')) ? 'recovered_data' : fileId;\n\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: actualFileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // // 方法1：创建隐藏的a标签并触发点击\n        // const link = document.createElement('a');\n        // link.href = downloadUrl;\n        // link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        // link.setAttribute('target', '_blank');\n        // document.body.appendChild(link);\n        // link.click();\n        // document.body.removeChild(link);\n        \n        // 显示成功消息\n        showToast('文档已生成，正在下载...', 'success');\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 1000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      showToast('生成文档失败，请重试', 'error');\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  // columns和相关事件处理函数优化\n  const handleRemarksClick = useCallback((rowId, value) => {\n    openRemarksDialog(rowId, value);\n  }, [openRemarksDialog]);\n\n  const columns = useMemo(() => (columnOrder.map(col => {\n    if (!(gridData && gridData[0] && gridData[0].hasOwnProperty(col.field)) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      return null;\n    }\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ maxWidth: '100%', opacity: 0.6, transition: 'all 0.2s ease-in-out', '& .MuiChip-label': { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block' } }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          let remarkText = '点击选择';\n          let isSelected = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            isSelected = true;\n          }\n          \n          return (\n            <RemarkChip\n              rowId={params.row.id}\n              text={remarkText}\n              isSelected={isSelected}\n              onClick={handleRemarksClick}\n            />\n          );\n        }\n      };\n    }\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        width: 200,\n        editable: false,\n        renderCell: (params) => {\n          if (params.row.NO === 'TOTAL') return '';\n\n          return (\n            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>\n              {/* 添加按钮 */}\n              <Tooltip title=\"在此行下方添加新行\">\n                <IconButton\n                  size=\"small\"\n                  color=\"primary\"\n                  onClick={() => handleAddRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <AddCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 永久删除按钮 */}\n              <Tooltip title=\"永久删除此行（无法恢复）\">\n                <IconButton\n                  size=\"small\"\n                  color=\"error\"\n                  onClick={() => handleDeleteRow(params.row.id)}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'error.main',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  <RemoveCircleOutlineIcon fontSize=\"small\" />\n                </IconButton>\n              </Tooltip>\n\n              {/* 移除/恢复按钮 */}\n              {params.row._removed ? (\n                <Button\n                  key=\"undo\"\n                  variant=\"contained\"\n                  color=\"success\"\n                  size=\"small\"\n                  startIcon={<UndoIcon />}\n                  onClick={() => handleUndoRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  恢复\n                </Button>\n              ) : (\n                <Button\n                  key=\"remove\"\n                  variant=\"contained\"\n                  color=\"error\"\n                  size=\"small\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => handleRemoveRow(params.row.id)}\n                  sx={{\n                    fontSize: '0.75rem',\n                    textTransform: 'none',\n                    minWidth: '70px'\n                  }}\n                >\n                  移除\n                </Button>\n              )}\n            </Box>\n          );\n        }\n      };\n    }\n    return {\n      ...col,\n      editable: params => {\n        if (params.row && params.row.NO === 'TOTAL') return false;\n        if (params.row && params.row._removed) return false;\n        return col.editable !== false;\n      },\n      renderCell: (params) => {\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : typeof params.value === 'string' && !isNaN(Number(params.value)) ? Number(params.value).toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0];\n        }\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        } else if (col.field === 'COMMISSION' && typeof params.value === 'string' && !isNaN(Number(params.value))) {\n          return Number(params.value).toFixed(2);\n        }\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean)), [columnOrder, handleRemarksClick, handleRemoveRow, handleUndoRow, handleAddRow, handleDeleteRow]);\n  \n  // 搜索过滤逻辑\n  const filteredGridData = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return gridData || [];\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase();\n    return (gridData || []).filter(row => {\n      if (searchColumn === 'all') {\n        // 搜索所有列\n        return Object.values(row).some(value =>\n          value && value.toString().toLowerCase().includes(searchLower)\n        );\n      } else {\n        // 搜索特定列\n        const cellValue = row[searchColumn];\n        return cellValue && cellValue.toString().toLowerCase().includes(searchLower);\n      }\n    });\n  }, [gridData, debouncedSearchText, searchColumn]);\n\n  // gridData 用 useMemo 缓存，提升编辑流畅度\n  const memoGridData = useMemo(() => filteredGridData || [], [filteredGridData]);\n  \n  // ！！！提前return，避免后面再声明Hook！！！\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  return (\n    <Box>\n      {/* 标题和统计信息 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <AssessmentIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>\n                  处理结果\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                  数据处理完成，可以编辑和导出结果\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* 统计信息 */}\n            <Stack direction=\"row\" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>\n              <Chip\n                icon={<TableViewIcon />}\n                label={`${(memoGridData || []).filter(row => !row.isTotal && !row._removed).length} 条记录`}\n                color=\"primary\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              <Chip\n                icon={<TrendingUpIcon />}\n                label={`总佣金: RM ${getTotalCommission(memoGridData).toFixed(2)}`}\n                color=\"success\"\n                variant=\"outlined\"\n                size=\"small\"\n              />\n              {(memoGridData || []).filter(row => row._removed).length > 0 && (\n                <Chip\n                  icon={<DeleteIcon />}\n                  label={`${(memoGridData || []).filter(row => row._removed).length} 条已删除`}\n                  color=\"warning\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n            </Stack>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            操作选项\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<DownloadIcon />}\n              onClick={handleDownload}\n            >\n              下载Excel\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={isGeneratingDocument ? <CircularProgress size={20} color=\"inherit\" /> : <PictureAsPdfIcon />}\n              onClick={generateDocument}\n              disabled={isGeneratingDocument}\n            >\n              {isGeneratingDocument ? '生成中...' : '生成文档'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<RestartAltIcon />}\n              onClick={handleCleanup}\n            >\n              重新开始\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* 搜索区域 */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n            数据搜索\n          </Typography>\n          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems=\"center\">\n            <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n              <InputLabel>搜索范围</InputLabel>\n              <Select\n                value={searchColumn}\n                label=\"搜索范围\"\n                onChange={(e) => setSearchColumn(e.target.value)}\n              >\n                <MenuItem value=\"all\">全部列</MenuItem>\n                <MenuItem value=\"NO\">NO</MenuItem>\n                <MenuItem value=\"DATE\">DATE</MenuItem>\n                <MenuItem value=\"VEHICLE NO\">VEHICLE NO</MenuItem>\n                <MenuItem value=\"RO NO\">RO NO</MenuItem>\n                <MenuItem value=\"KM\">KM</MenuItem>\n                <MenuItem value=\"REMARKS\">REMARKS</MenuItem>\n                <MenuItem value=\"MAXCHECK\">HOURS</MenuItem>\n                <MenuItem value=\"COMMISSION\">AMOUNT</MenuItem>\n              </Select>\n            </FormControl>\n\n            <TextField\n              size=\"small\"\n              placeholder=\"输入搜索内容...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              sx={{ flexGrow: 1, minWidth: 200 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon />\n                  </InputAdornment>\n                ),\n                endAdornment: searchText && (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => setSearchText('')}\n                      edge=\"end\"\n                    >\n                      <ClearIcon />\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            {searchText && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                找到 {filteredGridData.filter(row => row.NO !== 'TOTAL').length} 条记录\n              </Typography>\n            )}\n          </Stack>\n        </CardContent>\n      </Card>\n      \n      {/* 数据表格 */}\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n          <Box sx={{ height: 'auto', width: '100%' }}>\n            <DataGrid\n              rows={memoGridData}\n              columns={columns}\n              pageSize={pageSize}\n              onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}\n              page={page}\n              onPageChange={(newPage) => setPage(newPage)}\n              rowsPerPageOptions={[25, 50, 150]}\n              pagination\n              disableSelectionOnClick\n              headerHeight={64}\n              columnHeaderHeight={64}\n              disableVirtualization={false}\n              rowHeight={48}\n              density=\"compact\"\n              rowBuffer={5}\n              columnBuffer={2}\n              disableColumnMenu\n              disableColumnFilter\n              disableColumnSelector\n              disableDensitySelector\n              hideFooterSelectedRowCount\n              getRowClassName={(params) => {\n                if (params.row.isTotal) return 'total-row';\n                if (params.row._removed) return 'removed-row';\n                return '';\n              }}\n              isCellEditable={(params) => {\n                if (params.row.isTotal || params.row._removed) {\n                  return false;\n                }\n                return params.colDef.editable && typeof params.colDef.editable === 'function' ?\n                  params.colDef.editable(params) : params.colDef.editable;\n              }}\n              processRowUpdate={(newRow) => {\n                if (newRow.COMMISSION !== undefined) {\n                  if (typeof newRow.COMMISSION === 'string') {\n                    newRow.COMMISSION = Number(newRow.COMMISSION) || 0;\n                  }\n                }\n                return processRowUpdate(newRow);\n              }}\n              onProcessRowUpdateError={onProcessRowUpdateError}\n              sx={{\n                '& .total-row': {\n                  backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                  fontWeight: 'bold',\n                },\n                '& .removed-row': {\n                  backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                  color: 'text.disabled',\n                  textDecoration: 'line-through',\n                },\n                '& .MuiDataGrid-cell': {\n                  whiteSpace: 'normal',\n                  lineHeight: 'normal',\n                  padding: '8px',\n                  borderBottom: '1px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeaders': {\n                  backgroundColor: 'background.default',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                },\n                '& .MuiDataGrid-columnHeader': {\n                  backgroundColor: 'background.default',\n                  color: 'text.primary',\n                  borderRight: '1px solid',\n                  borderColor: 'divider',\n                  '&:last-child': {\n                    borderRight: 'none',\n                  },\n                },\n                '& .MuiDataGrid-columnHeaderTitle': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  fontSize: '0.875rem',\n                },\n                '& .MuiDataGrid-columnSeparator': {\n                  display: 'none',\n                },\n                minHeight: 500,\n              }}\n            />\n          </Box>\n        </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n\n        {/* 勾选选项区域 */}\n        <Box sx={{ px: 3, pb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n            额外选项：\n          </Typography>\n          <Stack direction=\"row\" spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={cbuCarChecked}\n                  onChange={(e) => setCbuCarChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"CBU CAR\"\n            />\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={wtyChecked}\n                  onChange={(e) => setWtyChecked(e.target.checked)}\n                  size=\"small\"\n                />\n              }\n              label=\"WTY\"\n            />\n          </Stack>\n        </Box>\n\n        <DialogContent dividers sx={{ p: 0 }}>\n          <FixedSizeList\n            height={300}\n            itemCount={remarksOptions.length}\n            itemSize={48}\n            width=\"100%\"\n          >\n            {({ index, style }) => {\n              const option = remarksOptions[index];\n              return (\n                <ListItem \n                  key={option} \n                  style={style}\n                  disablePadding\n                  secondaryAction={(option !== 'None') && (\n                    <IconButton \n                      edge=\"end\" \n                      aria-label=\"delete\"\n                      onClick={() => deleteOption(option)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  )}\n                >\n                  <ListItemButton onClick={() => selectRemarkOption(option)}>\n                    <ListItemText primary={option} />\n                  </ListItemButton>\n                </ListItem>\n              );\n            }}\n          </FixedSizeList>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAChF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,IAAIC,OAAO;EACX,OAAO,SAASC,gBAAgBA,CAAC,GAAGC,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClBC,YAAY,CAACJ,OAAO,CAAC;MACrBF,IAAI,CAAC,GAAGI,IAAI,CAAC;IACf,CAAC;IACDE,YAAY,CAACJ,OAAO,CAAC;IACrBA,OAAO,GAAGK,UAAU,CAACF,KAAK,EAAEJ,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA,MAAMO,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,EACtB,MAAM,CACP;;AAED;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,oBAAOZ,OAAA,CAAC3B,KAAK;IAAA,GAAKuC,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9C;;AAEA;AAAAC,EAAA,GAJSP,mBAAmB;AAK5B,MAAMQ,UAAU,gBAAAC,EAAA,cAAG3E,KAAK,CAAC4E,IAAI,CAAAC,GAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAN,EAAA;EACtE;EACA,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC;IAErC8E,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA9E,SAAS,CAAC,MAAM;IACdiF,UAAU,CAAC;MACTJ,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMI,WAAW,GAAIC,CAAC,IAAK;IACzBJ,OAAO,CAACH,KAAK,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA,CAAC/C,MAAM;IAELyE,OAAO,EAAEG,WAAY;IACrBE,OAAO,EAAEJ,OAAO,CAACF,UAAU,GAAG,WAAW,GAAG,UAAW;IACvDO,KAAK,EAAC,SAAS;IACfC,IAAI,EAAC,OAAO;IACZC,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,sBAAsB;MAClCC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EAEDlB,OAAO,CAACH,IAAI,IAAI;EAAM,GAlBlB,UAAUD,KAAK,IAAII,OAAO,CAACF,UAAU,EAAE;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBtC,CAAC;AAEb,CAAC,kCAAC;AAAC6B,GAAA,GA5CG3B,UAAU;AA8ChB,MAAM4B,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACzF;EACA,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC1E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EACtE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,EACjF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAC/E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,EAClF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAO,CAAC,CAChF;;EAED;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnH,QAAQ,CAAC,MAAM;IACzD,MAAMoH,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGpD,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAAC2H,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC+H,UAAU,EAAEC,aAAa,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACmI,YAAY,EAAEC,eAAe,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoI,KAAK,GAAGtE,UAAU,CAAC,MAAM;MAC7BmE,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMjE,YAAY,CAACuE,KAAK,CAAC;EAClC,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACA9H,SAAS,CAAC,MAAM;IACdqI,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAACL,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyI,UAAU,EAAEC,aAAa,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC2I,QAAQ,EAAEC,WAAW,CAAC,GAAG5I,QAAQ,CAAC,MAAM;IAC7C,MAAM6I,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAOuB,KAAK,GAAGC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE;EACzC,CAAC,CAAC;EACF,MAAM,CAACE,IAAI,EAAET,OAAO,CAAC,GAAGtI,QAAQ,CAAC,CAAC,CAAC;;EAEnC;EACAC,SAAS,CAAC,MAAM;IACdoH,YAAY,CAAC2B,OAAO,CAAC,kBAAkB,EAAEL,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdoH,YAAY,CAAC2B,OAAO,CAAC,gBAAgB,EAAEzB,IAAI,CAAC6B,SAAS,CAAClC,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmC,aAAa,GAAG,CAAC/C,IAAI,IAAI,EAAE,EAAEgD,GAAG,CAACC,GAAG,IAAI;IAC5C;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5J,QAAQ,CAAC,MAAM;IAC7C6J,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7CpD,aAAa,GAAG,IAAIA,aAAa,CAACqD,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAIrD,aAAa,IAAIA,aAAa,CAACqD,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAGtD,aAAa,CAAC4C,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHnB,eAAe,CAAC,CAAC,GAAGe,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHnB,eAAe,CAAC,CAAC,GAAGe,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAGpK,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMqK,iBAAiB,GAAGrK,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMsK,gBAAgB,GAAGtK,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMuK,mBAAmB,GAAGvK,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMwK,UAAU,GAAIrE,IAAI,IAAKA,IAAI,CAACgD,GAAG,CAACC,GAAG,KAAK;IAC5Ca,EAAE,EAAEb,GAAG,CAACa,EAAE;IACVE,EAAE,EAAEf,GAAG,CAACe,EAAE;IACVZ,QAAQ,EAAEH,GAAG,CAACG,QAAQ;IACtBF,OAAO,EAAED,GAAG,CAACC,OAAO;IACpBC,iBAAiB,EAAEF,GAAG,CAACE,iBAAiB;IACxCmB,UAAU,EAAErB,GAAG,CAACqB;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA3K,SAAS,CAAC,MAAM;IACd,IAAI0G,YAAY,IAAIgD,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAGxD,IAAI,CAAC6B,SAAS,CAACuB,UAAU,CAAChB,QAAQ,CAAC,CAAC;MACpD,MAAMqB,WAAW,GAAGT,mBAAmB,CAACU,OAAO;MAC/C,IAAID,WAAW,KAAKD,OAAO,EAAE;QAC3B;QACA,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;UAC5BnH,YAAY,CAAC2G,gBAAgB,CAACQ,OAAO,CAAC;QACxC;QACAR,gBAAgB,CAACQ,OAAO,GAAGlH,UAAU,CAAC,MAAM;UAC1CwG,mBAAmB,CAACU,OAAO,GAAGF,OAAO;UACrCP,iBAAiB,CAACS,OAAO,GAAGH,IAAI,CAACD,GAAG,CAAC,CAAC;UACtClE,YAAY,CAAC,CAAC,GAAGgD,QAAQ,CAAC,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IACA,OAAO,MAAM;MACX,IAAIc,gBAAgB,CAACQ,OAAO,EAAE;QAC5BnH,YAAY,CAAC2G,gBAAgB,CAACQ,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,QAAQ,EAAEhD,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnL,QAAQ,CAAC;IACjDoL,IAAI,EAAE,KAAK;IACXvG,KAAK,EAAE,IAAI;IACXwG,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApL,SAAS,CAAC,MAAM;IACd,IAAIiJ,YAAY,CAACa,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDZ,eAAe,CAAC,CAAC,GAAGQ,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAET,YAAY,CAAC,CAAC;;EAE5B;EACA,MAAMoC,SAAS,GAAGpL,WAAW,CAAC,CAACqL,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC3D,QAAQA,IAAI;MACV,KAAK,SAAS;QACZpI,KAAK,CAACqI,OAAO,CAACF,OAAO,EAAE;UACrBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBvG,KAAK,EAAE,OAAO;YACdwG,YAAY,EAAE,MAAM;YACpBnG,QAAQ,EAAE,MAAM;YAChBoG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,OAAO;QACV7I,KAAK,CAAC8I,KAAK,CAACX,OAAO,EAAE;UACnBG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBvG,KAAK,EAAE,OAAO;YACdwG,YAAY,EAAE,MAAM;YACpBnG,QAAQ,EAAE,MAAM;YAChBoG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,MAAM;QACT7I,KAAK,CAACmI,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBvG,KAAK,EAAE,OAAO;YACdwG,YAAY,EAAE,MAAM;YACpBnG,QAAQ,EAAE,MAAM;YAChBoG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZ7I,KAAK,CAACmI,OAAO,EAAE;UACbG,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBvG,KAAK,EAAE,OAAO;YACdwG,YAAY,EAAE,MAAM;YACpBnG,QAAQ,EAAE,MAAM;YAChBoG,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QACF;MACF;QACE7I,KAAK,CAACmI,OAAO,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,IAAI5F,MAAM,IAAIA,MAAM,CAAC6F,UAAU,CAAC,YAAY,CAAC,EAAE;QAC7C3F,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEA,MAAM4F,WAAW,GAAG,GAAGnJ,OAAO,aAAaqD,MAAM,EAAE;MACnD,MAAM+F,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAI5B,IAAI,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhB,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC;IACxC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BzF,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMuG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM/J,KAAK,CAACgK,MAAM,CAAC,GAAG/J,OAAO,YAAYqD,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAO2F,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEA1F,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM0G,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8C,kBAAkB,GAAGlN,WAAW,CAAC,CAACoG,IAAI,EAAE+G,UAAU,KAAK;IAC3D,MAAMC,SAAS,GAAGhH,IAAI,IAAIqD,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC4D,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,CAAC;IACV;IACA,OAAOA,SAAS,CACbG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAK;MACpB,IAAI8D,UAAU,IAAI9D,GAAG,CAACa,EAAE,KAAKiD,UAAU,CAACjD,EAAE,EAAE;QAC1C,OAAOuD,GAAG,IAAIC,MAAM,CAACP,UAAU,CAACzC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnD;MACA,OAAO+C,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkE,gBAAgB,GAAG3N,WAAW,CAAEoG,IAAI,IAAK;IAC7C,MAAMwH,QAAQ,GAAGxH,IAAI,CAACyH,IAAI,CAACxE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IACrD,IAAIwD,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAG1H,IAAI,CAClBmH,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDgE,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAIC,MAAM,CAACrE,GAAG,CAACqB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/DkD,QAAQ,CAAClD,UAAU,GAAGoD,QAAQ;IAChC;IACA,OAAO1H,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2H,2BAA2B,GAAG/N,WAAW,CAC7CqD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAI;MACFe,YAAY,CAAC2B,OAAO,CAAC,eAAe,EAAEzB,IAAI,CAAC6B,SAAS,CAAC9C,IAAI,CAAC,CAAC;MAC3DuD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExD,IAAI,CAACyD,MAAM,CAAC;IAClD,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,EACF,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGhO,WAAW,CACvCqD,QAAQ,CAAE+C,IAAI,IAAK;IACjB,IAAIK,YAAY,EAAE;MAChBA,YAAY,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC;MACvBuD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACnD,YAAY,CACf,CAAC;;EAED;EACA,MAAMwH,gBAAgB,GAAGjO,WAAW,CAAEkO,MAAM,IAAK;IAC/CxE,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIC,UAAU,GAAGlB,kBAAkB,CAACiB,IAAI,EAAED,MAAM,CAAC;MACjD,MAAMG,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAI;QAClC,IAAIA,GAAG,CAACa,EAAE,KAAKgE,MAAM,CAAChE,EAAE,EAAE,OAAO;UAAE,GAAGb,GAAG;UAAE,GAAG6E;QAAO,CAAC;QACtD,IAAI7E,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO;UAAE,GAAGf,GAAG;UAAEqB,UAAU,EAAE0D;QAAW,CAAC;QACjE,OAAO/E,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA0E,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElC,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF,OAAOH,MAAM;EACf,CAAC,EAAE,CAAChB,kBAAkB,EAAEa,2BAA2B,EAAEC,qBAAqB,CAAC,CAAC;EAE5E,MAAMM,uBAAuB,GAAItC,KAAK,IAAK;IACzCZ,SAAS,CAAC,SAASY,KAAK,CAACX,OAAO,EAAE,EAAE,OAAO,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAG1O,KAAK,CAACG,WAAW,CAAC,CAAC2E,KAAK,EAAEwG,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfC,IAAI,EAAE,IAAI;MACVvG,KAAK;MACLwG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqD,kBAAkB,GAAGxO,WAAW,CAAC,MAAM;IAC3CiL,gBAAgB,CAACkD,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPjD,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH;IACA5C,gBAAgB,CAAC,KAAK,CAAC;IACvBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiG,kBAAkB,GAAGzO,WAAW,CAAE0O,MAAM,IAAK;IACjD,MAAM;MAAE/J;IAAM,CAAC,GAAGqG,aAAa;IAC/B,IAAIrG,KAAK,KAAK,IAAI,EAAE;MAClB;MACA6J,kBAAkB,CAAC,CAAC;;MAEpB;MACAG,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjClF,WAAW,CAACmF,QAAQ,IAAI;UACtB,IAAIR,WAAW,GAAGQ,QAAQ,CAACzF,GAAG,CAACC,GAAG,IAAI;YACpC,IAAIA,GAAG,CAACa,EAAE,KAAKvF,KAAK,EAAE;cACpB,IAAI+J,MAAM,KAAK,MAAM,EAAE;gBACrB,OAAO;kBAAE,GAAGrF,GAAG;kBAAEC,OAAO,EAAE,EAAE;kBAAEC,iBAAiB,EAAE;gBAAG,CAAC;cACvD,CAAC,MAAM;gBACL;gBACA,IAAIuF,WAAW,GAAGJ,MAAM;gBACxB,MAAMK,QAAQ,GAAG,EAAE;gBAEnB,IAAI1G,aAAa,EAAE;kBACjB0G,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;gBAC1B;gBACA,IAAIzG,UAAU,EAAE;kBACdwG,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;gBACtB;gBAEA,IAAID,QAAQ,CAAClF,MAAM,GAAG,CAAC,EAAE;kBACvBiF,WAAW,GAAG,GAAGJ,MAAM,KAAKK,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpD;gBAEA,OAAO;kBAAE,GAAG5F,GAAG;kBAAEC,OAAO,EAAEwF,WAAW;kBAAEvF,iBAAiB,EAAEuF;gBAAY,CAAC;cACzE;YACF;YACA,OAAOzF,GAAG;UACZ,CAAC,CAAC;UAEFgF,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;UAC3C,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAxK,UAAU,CAAC,MAAM;UACfuH,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC;QACpC,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACJ,aAAa,EAAEwD,kBAAkB,EAAEb,gBAAgB,EAAEvC,SAAS,EAAE/C,aAAa,EAAEE,UAAU,CAAC,CAAC;;EAE/F;EACA,MAAM2G,mBAAmB,GAAGlP,WAAW,CAAC,MAAM;IAC5C0H,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyH,oBAAoB,GAAGnP,WAAW,CAAC,MAAM;IAC7C0H,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4H,YAAY,GAAGpP,WAAW,CAAC,MAAM;IACrC,IAAIuH,SAAS,CAAC8H,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACrI,cAAc,CAACsI,QAAQ,CAAC/H,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEpI,iBAAiB,CAACkH,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE5G,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,CAAC;MACtDjE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B+D,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAInI,cAAc,CAACsI,QAAQ,CAAC/H,SAAS,CAAC8H,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDjE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC7D,SAAS,EAAEP,cAAc,EAAEmI,oBAAoB,EAAE/D,SAAS,CAAC,CAAC;;EAEhE;EACA,MAAMmE,YAAY,GAAGvP,WAAW,CAAE0O,MAAM,IAAK;IAC3CzH,iBAAiB,CAACkH,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAACiC,IAAI,IAAIA,IAAI,KAAKd,MAAM,CAAC,CAAC;IAC/DtD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMqE,eAAe,GAAGzP,WAAW,CAAEkK,EAAE,IAAK;IAC1CR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,GAAG,CAAC;;MAEnF;MACA,IAAIqG,SAAS,GAAG,CAAC;MACjBrB,WAAW,CAACsB,OAAO,CAACtG,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UACrEf,GAAG,CAACe,EAAE,GAAGsF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFrB,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IAEFjD,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;EAChC,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMwE,aAAa,GAAG5P,WAAW,CAAEkK,EAAE,IAAK;IACxCR,WAAW,CAACyE,IAAI,IAAI;MAClB,IAAIE,WAAW,GAAGF,IAAI,CAAC/E,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGb,GAAG;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAAGH,GAAG,CAAC;;MAEpF;MACA,IAAIqG,SAAS,GAAG,CAAC;MACjBrB,WAAW,CAACsB,OAAO,CAACtG,GAAG,IAAI;QACzB,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UACrEf,GAAG,CAACe,EAAE,GAAGsF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;MAEFrB,WAAW,GAAGV,gBAAgB,CAACU,WAAW,CAAC;MAC3C,OAAOA,WAAW;IACpB,CAAC,CAAC;IACFxK,UAAU,CAAC,MAAM;MACfuH,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACuC,gBAAgB,EAAEvC,SAAS,CAAC,CAAC;;EAEjC;EACA,MAAMyE,eAAe,GAAG7P,WAAW,CAAEkK,EAAE,IAAK;IAC1CR,WAAW,CAACyE,IAAI,IAAI;MAClB;MACA,MAAM2B,YAAY,GAAG3B,IAAI,CAACZ,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,CAAC;;MAEtD;MACA,IAAIwF,SAAS,GAAG,CAAC;MACjBI,YAAY,CAACH,OAAO,CAACtG,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UACrEf,GAAG,CAACe,EAAE,GAAGsF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMrB,WAAW,GAAGV,gBAAgB,CAACmC,YAAY,CAAC;;MAElD;MACA/B,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElCjD,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC;MAC9B,OAAOiD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAM2E,YAAY,GAAG/P,WAAW,CAAEgQ,UAAU,IAAK;IAC/CtG,WAAW,CAACyE,IAAI,IAAI;MAClB;MACA,MAAM8B,WAAW,GAAG9B,IAAI,CAAC+B,SAAS,CAAC7G,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK8F,UAAU,CAAC;MAChE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE,OAAO9B,IAAI;;MAEnC;MACA,MAAMgC,KAAK,GAAGvF,IAAI,CAACD,GAAG,CAAC,CAAC;;MAExB;MACA,MAAMyF,UAAU,GAAGjC,IAAI,CAAC8B,WAAW,CAAC;MACpC,MAAMI,QAAQ,GAAG,OAAOD,UAAU,CAAChG,EAAE,KAAK,QAAQ,GAAGgG,UAAU,CAAChG,EAAE,GAAG,CAAC,GAAG,CAAC;;MAE1E;MACA,MAAM8D,MAAM,GAAG;QACbhE,EAAE,EAAEiG,KAAK;QACT/F,EAAE,EAAEiG,QAAQ;QACZC,IAAI,EAAE,EAAE;QACR,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNjH,OAAO,EAAE,EAAE;QACXkH,QAAQ,EAAE,EAAE;QACZ9F,UAAU,EAAE,CAAC;QACbnB,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,KAAK;QACfW,OAAO,EAAE;MACX,CAAC;;MAED;MACA,MAAMsG,OAAO,GAAG,CAAC,GAAGtC,IAAI,CAAC;MACzBsC,OAAO,CAACC,MAAM,CAACT,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE/B,MAAM,CAAC;;MAE1C;MACA,IAAIwB,SAAS,GAAG,CAAC;MACjBe,OAAO,CAACd,OAAO,CAACtG,GAAG,IAAI;QACrB,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,EAAE;UACrEf,GAAG,CAACe,EAAE,GAAGsF,SAAS,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMrB,WAAW,GAAGV,gBAAgB,CAAC8C,OAAO,CAAC;;MAE7C;MACA1C,2BAA2B,CAACM,WAAW,CAAC;MACxCL,qBAAqB,CAACK,WAAW,CAAC;MAElCjD,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC;MAC7B,OAAOiD,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,gBAAgB,EAAEI,2BAA2B,EAAEC,qBAAqB,EAAE5C,SAAS,CAAC,CAAC;;EAErF;EACA,MAAMuF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF/I,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMgJ,YAAY,GAAG,CAACnH,QAAQ,IAAI,EAAE,EACjC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACAoH,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC1G,EAAE,KAAK,QAAQ,IAAI,OAAO2G,CAAC,CAAC3G,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO0G,CAAC,CAAC1G,EAAE,GAAG2G,CAAC,CAAC3G,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM4G,OAAO,GAAGJ,YAAY,CAACxH,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACbqG,IAAI,EAAEjH,GAAG,CAACiH,IAAI,GAAI,OAAOjH,GAAG,CAACiH,IAAI,KAAK,QAAQ,IAAIjH,GAAG,CAACiH,IAAI,CAAChB,QAAQ,CAAC,GAAG,CAAC,GAAGjG,GAAG,CAACiH,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG5H,GAAG,CAACiH,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEjH,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG6H,IAAI,CAACC,KAAK,CAAC9H,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFkH,EAAE,EAAE,OAAOlH,GAAG,CAACkH,EAAE,KAAK,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAAC9H,GAAG,CAACkH,EAAE,CAAC,GAAGlH,GAAG,CAACkH,EAAE,IAAI,EAAE;QAClEjH,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjG6H,KAAK,EAAE,OAAO/H,GAAG,CAACmH,QAAQ,KAAK,QAAQ,GACpCnH,GAAG,CAACmH,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGnH,GAAG,CAACmH,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAAGhI,GAAG,CAACmH,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC,GAC3EhI,GAAG,CAACmH,QAAQ,IAAI,EAAE;QACpBc,MAAM,EAAE,OAAOjI,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,CAAC2G,OAAO,CAAC,CAAC,CAAC,GAAGhI,GAAG,CAACqB,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM6G,WAAW,GAAG,CAAC9H,QAAQ,IAAI,EAAE,EAChC8D,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACqB,UAAU,CAAC,CACpE8C,MAAM,CAAC,CAACC,GAAG,EAAEpE,GAAG,KAAKoE,GAAG,IAAI,OAAOpE,GAAG,CAACqB,UAAU,KAAK,QAAQ,GAAGrB,GAAG,CAACqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA;MACA,MAAM8G,YAAY,GAAInL,MAAM,IAAIA,MAAM,CAAC6F,UAAU,CAAC,YAAY,CAAC,GAAI,gBAAgB,GAAG7F,MAAM;MAE5F,MAAMoL,QAAQ,GAAG,MAAM1O,KAAK,CAAC2O,IAAI,CAAC,GAAG1O,OAAO,oBAAoB,EAAE;QAChEoD,IAAI,EAAE4K,OAAO;QACbO,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnChL,MAAM,EAAEmL;MACV,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACrL,IAAI,IAAIqL,QAAQ,CAACrL,IAAI,CAACuL,KAAK,EAAE;QACxC;QACA,MAAMxF,WAAW,GAAG,GAAGnJ,OAAO,CAACiO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACrL,IAAI,CAACwL,MAAM,EAAE;;QAExE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAxG,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC;;QAErC;QACAvH,UAAU,CAAC,MAAM;UACf,MAAMgO,MAAM,GAAGxF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CuF,MAAM,CAACnG,KAAK,CAACoG,OAAO,GAAG,MAAM;UAC7BD,MAAM,CAACE,GAAG,GAAG5F,WAAW;UACxBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACkF,MAAM,CAAC;UACjChO,UAAU,CAAC,MAAM;YACfwI,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACgF,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOhG,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BZ,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;IAClC,CAAC,SAAS;MACRxD,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMqK,kBAAkB,GAAGjS,WAAW,CAAC,CAAC2E,KAAK,EAAEuN,KAAK,KAAK;IACvD3D,iBAAiB,CAAC5J,KAAK,EAAEuN,KAAK,CAAC;EACjC,CAAC,EAAE,CAAC3D,iBAAiB,CAAC,CAAC;EAEvB,MAAM4D,OAAO,GAAGjS,OAAO,CAAC,MAAOyG,WAAW,CAACyC,GAAG,CAACgJ,GAAG,IAAI;IACpD,IAAI,EAAE3I,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC4I,cAAc,CAACD,GAAG,CAACxL,KAAK,CAAC,CAAC,IAAIwL,GAAG,CAACxL,KAAK,KAAK,SAAS,IAAIwL,GAAG,CAACxL,KAAK,KAAK,QAAQ,IAAIwL,GAAG,CAACxL,KAAK,KAAK,YAAY,EAAE;MAC1J,OAAO,IAAI;IACb;IACA,IAAIwL,GAAG,CAACxL,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEwL,GAAG,CAACxL,KAAK;QAChBC,UAAU,EAAEuL,GAAG,CAACvL,UAAU;QAC1ByL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVzL,QAAQ,EAAE,KAAK;QACf0L,UAAU,EAAGvF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UACxC,IAAI6C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMiJ,iBAAiB,GAAGxF,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACEnG,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAEzF,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAACoJ,KAAK;cAACC,SAAS,EAAC,KAAK;cAAA3M,QAAA,eACvE7C,OAAA,CAAChC,IAAI;gBACHyR,KAAK,EAAEJ,iBAAkB;gBACzBrN,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBE,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAE;kBAAEE,QAAQ,EAAE,MAAM;kBAAEsN,OAAO,EAAE,GAAG;kBAAEhN,UAAU,EAAE,sBAAsB;kBAAE,kBAAkB,EAAE;oBAAEH,QAAQ,EAAE,QAAQ;oBAAEC,YAAY,EAAE,UAAU;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiM,OAAO,EAAE;kBAAQ;gBAAE;cAAE;gBAAA5N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;UAEA,IAAI0O,UAAU,GAAG,MAAM;UACvB,IAAIlO,UAAU,GAAG,KAAK;UAEtB,IAAIoI,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,IAAI0D,MAAM,CAAC5D,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EwJ,UAAU,GAAG9F,MAAM,CAAC5D,GAAG,CAACE,iBAAiB;YACzC1E,UAAU,GAAG,IAAI;UACnB;UAEA,oBACEzB,OAAA,CAACmB,UAAU;YACTI,KAAK,EAAEsI,MAAM,CAAC5D,GAAG,CAACa,EAAG;YACrBtF,IAAI,EAAEmO,UAAW;YACjBlO,UAAU,EAAEA,UAAW;YACvBC,OAAO,EAAEmN;UAAmB;YAAA/N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAEN;MACF,CAAC;IACH;IACA,IAAI+N,GAAG,CAACxL,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEwL,GAAG,CAACxL,KAAK;QAChBC,UAAU,EAAEuL,GAAG,CAACvL,UAAU;QAC1ByL,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVzL,QAAQ,EAAE,KAAK;QACf0L,UAAU,EAAGvF,MAAM,IAAK;UACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;UAExC,oBACEhH,OAAA,CAACjD,GAAG;YAACmF,EAAE,EAAE;cAAEwM,OAAO,EAAE,MAAM;cAAEkB,GAAG,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAhN,QAAA,gBAE3D7C,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAC,wDAAW;cAAAzM,QAAA,eACxB7C,OAAA,CAAClC,UAAU;gBACTmE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,SAAS;gBACfN,OAAO,EAAEA,CAAA,KAAMiL,YAAY,CAAC9C,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;gBAC3C5E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACT4N,eAAe,EAAE,cAAc;oBAC/B9N,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACP,oBAAoB;kBAAC4C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGVjB,OAAA,CAAC/B,OAAO;cAACqR,KAAK,EAAC,0EAAc;cAAAzM,QAAA,eAC3B7C,OAAA,CAAClC,UAAU;gBACTmE,IAAI,EAAC,OAAO;gBACZD,KAAK,EAAC,OAAO;gBACbN,OAAO,EAAEA,CAAA,KAAM+K,eAAe,CAAC5C,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;gBAC9C5E,EAAE,EAAE;kBACF,SAAS,EAAE;oBACT4N,eAAe,EAAE,YAAY;oBAC7B9N,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAa,QAAA,eAEF7C,OAAA,CAACN,uBAAuB;kBAAC2C,QAAQ,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGT4I,MAAM,CAAC5D,GAAG,CAACG,QAAQ,gBAClBpG,OAAA,CAAC/C,MAAM;cAEL8E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZ8N,SAAS,eAAE/P,OAAA,CAACd,QAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBS,OAAO,EAAEA,CAAA,KAAM8K,aAAa,CAAC3C,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;cAC5C5E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,MAAM;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaJ,CAAC,gBAETjB,OAAA,CAAC/C,MAAM;cAEL8E,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,OAAO;cACbC,IAAI,EAAC,OAAO;cACZ8N,SAAS,eAAE/P,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BS,OAAO,EAAEA,CAAA,KAAM2K,eAAe,CAACxC,MAAM,CAAC5D,GAAG,CAACa,EAAE,CAAE;cAC9C5E,EAAE,EAAE;gBACFG,QAAQ,EAAE,SAAS;gBACnBC,aAAa,EAAE,MAAM;gBACrBH,QAAQ,EAAE;cACZ,CAAE;cAAAU,QAAA,EACH;YAED,GAbM,QAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV;MACF,CAAC;IACH;IACA,OAAO;MACL,GAAG+N,GAAG;MACNtL,QAAQ,EAAEmG,MAAM,IAAI;QAClB,IAAIA,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE,OAAO,KAAK;QACzD,IAAI6C,MAAM,CAAC5D,GAAG,IAAI4D,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,KAAK;QACnD,OAAO4I,GAAG,CAACtL,QAAQ,KAAK,KAAK;MAC/B,CAAC;MACD0L,UAAU,EAAGvF,MAAM,IAAK;QACtB,IAAIA,MAAM,CAAC5D,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIgI,GAAG,CAACxL,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACExD,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAAC0G,UAAU,EAAC,MAAM;YAACzG,KAAK,EAAC,SAAS;YAAAa,QAAA,EAC1D,OAAOgH,MAAM,CAACiF,KAAK,KAAK,QAAQ,GAAGjF,MAAM,CAACiF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAOpE,MAAM,CAACiF,KAAK,KAAK,QAAQ,IAAI,CAACkB,KAAK,CAAC1F,MAAM,CAACT,MAAM,CAACiF,KAAK,CAAC,CAAC,GAAGxE,MAAM,CAACT,MAAM,CAACiF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGpE,MAAM,CAACiF;UAAK;YAAAhO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrK,CAAC;QAEjB;QACA,IAAI4I,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEpG,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACE,EAAE,EAAE;cAAE+N,cAAc,EAAE;YAAe,CAAE;YAAApN,QAAA,EACtFgH,MAAM,CAACiF;UAAK;YAAAhO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;QACA,IAAI+N,GAAG,CAACxL,KAAK,KAAK,MAAM,IAAIqG,MAAM,CAACiF,KAAK,EAAE;UACxC,OAAOjF,MAAM,CAACiF,KAAK,CAACjB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,IAAImB,GAAG,CAACxL,KAAK,KAAK,IAAI,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAAClE,MAAM,CAACiF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,OAAO,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOhB,IAAI,CAACC,KAAK,CAAClE,MAAM,CAACiF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,IAAI,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOhB,IAAI,CAACC,KAAK,CAAClE,MAAM,CAACiF,KAAK,CAAC;QACjC;QACA,IAAIE,GAAG,CAACxL,KAAK,KAAK,UAAU,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOjF,MAAM,CAACiF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGjF,MAAM,CAACiF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC,GAAGpE,MAAM,CAACiF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QACnF;QACA,IAAIe,GAAG,CAACxL,KAAK,KAAK,YAAY,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOjF,MAAM,CAACiF,KAAK,CAACb,OAAO,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIe,GAAG,CAACxL,KAAK,KAAK,YAAY,IAAI,OAAOqG,MAAM,CAACiF,KAAK,KAAK,QAAQ,IAAI,CAACkB,KAAK,CAAC1F,MAAM,CAACT,MAAM,CAACiF,KAAK,CAAC,CAAC,EAAE;UACzG,OAAOxE,MAAM,CAACT,MAAM,CAACiF,KAAK,CAAC,CAACb,OAAO,CAAC,CAAC,CAAC;QACxC;QACA,IAAI,OAAOpE,MAAM,CAACiF,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOjF,MAAM,CAACiF,KAAK;QACrB;QACA,OAAOjF,MAAM,CAACiF,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC3E,MAAM,CAAC+F,OAAO,CAAE,EAAE,CAAC3M,WAAW,EAAEsL,kBAAkB,EAAExC,eAAe,EAAEG,aAAa,EAAEG,YAAY,EAAEF,eAAe,CAAC,CAAC;;EAEtH;EACA,MAAM0D,gBAAgB,GAAGrT,OAAO,CAAC,MAAM;IACrC,IAAI,CAAC6H,mBAAmB,CAACsH,IAAI,CAAC,CAAC,EAAE;MAC/B,OAAO5F,QAAQ,IAAI,EAAE;IACvB;IAEA,MAAM+J,WAAW,GAAGzL,mBAAmB,CAAC0L,WAAW,CAAC,CAAC;IACrD,OAAO,CAAChK,QAAQ,IAAI,EAAE,EAAE8D,MAAM,CAAClE,GAAG,IAAI;MACpC,IAAIpB,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,OAAOyL,MAAM,CAACC,MAAM,CAACtK,GAAG,CAAC,CAACuK,IAAI,CAAC1B,KAAK,IAClCA,KAAK,IAAIA,KAAK,CAACnJ,QAAQ,CAAC,CAAC,CAAC0K,WAAW,CAAC,CAAC,CAACnE,QAAQ,CAACkE,WAAW,CAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMK,SAAS,GAAGxK,GAAG,CAACpB,YAAY,CAAC;QACnC,OAAO4L,SAAS,IAAIA,SAAS,CAAC9K,QAAQ,CAAC,CAAC,CAAC0K,WAAW,CAAC,CAAC,CAACnE,QAAQ,CAACkE,WAAW,CAAC;MAC9E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/J,QAAQ,EAAE1B,mBAAmB,EAAEE,YAAY,CAAC,CAAC;;EAEjD;EACA,MAAM6L,YAAY,GAAG5T,OAAO,CAAC,MAAMqT,gBAAgB,IAAI,EAAE,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE9E;EACA,IAAI,CAAC9J,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEzG,OAAA,CAACjD,GAAG;MAACmF,EAAE,EAAE;QAAEyO,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA/N,QAAA,gBACtC7C,OAAA,CAAChD,UAAU;QAAC+E,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAa,QAAA,EAAC;MAEhD;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjB,OAAA,CAAC/C,MAAM;QACL8E,OAAO,EAAC,WAAW;QACnBL,OAAO,EAAEwB,OAAQ;QACjBhB,EAAE,EAAE;UAAE2O,EAAE,EAAE;QAAE,CAAE;QAAAhO,QAAA,EACf;MAED;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjB,OAAA,CAACjD,GAAG;IAAA8F,QAAA,gBAEF7C,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE4O,EAAE,EAAE;MAAE,CAAE;MAAAjO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,eACV7C,OAAA,CAACjD,GAAG;UAACmF,EAAE,EAAE;YAAEwM,OAAO,EAAE,MAAM;YAAEmB,UAAU,EAAE,QAAQ;YAAEkB,cAAc,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEpB,GAAG,EAAE;UAAE,CAAE;UAAA/M,QAAA,gBAC5G7C,OAAA,CAACjD,GAAG;YAACmF,EAAE,EAAE;cAAEwM,OAAO,EAAE,MAAM;cAAEmB,UAAU,EAAE,QAAQ;cAAED,GAAG,EAAE;YAAE,CAAE;YAAA/M,QAAA,gBACzD7C,OAAA,CAACZ,cAAc;cAAC8C,EAAE,EAAE;gBAAEF,KAAK,EAAE,cAAc;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACjD,GAAG;cAAA8F,QAAA,gBACF7C,OAAA,CAAChD,UAAU;gBAAC+E,OAAO,EAAC,IAAI;gBAACG,EAAE,EAAE;kBAAEuG,UAAU,EAAE,GAAG;kBAAEzG,KAAK,EAAE,cAAc;kBAAE8O,EAAE,EAAE;gBAAI,CAAE;gBAAAjO,QAAA,EAAC;cAElF;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA,CAAChD,UAAU;gBAAC+E,OAAO,EAAC,OAAO;gBAACG,EAAE,EAAE;kBAAEF,KAAK,EAAE;gBAAiB,CAAE;gBAAAa,QAAA,EAAC;cAE7D;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA,CAAC5B,KAAK;YAACyC,SAAS,EAAC,KAAK;YAACoQ,OAAO,EAAE,CAAE;YAAC/O,EAAE,EAAE;cAAE8O,QAAQ,EAAE,MAAM;cAAEpB,GAAG,EAAE;YAAE,CAAE;YAAA/M,QAAA,gBAClE7C,OAAA,CAAChC,IAAI;cACHkT,IAAI,eAAElR,OAAA,CAACX,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBwO,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAI,CAACA,GAAG,CAACc,OAAO,IAAI,CAACd,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,MAAO;cACzFzE,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFjB,OAAA,CAAChC,IAAI;cACHkT,IAAI,eAAElR,OAAA,CAACV,cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBwO,KAAK,EAAE,WAAW3F,kBAAkB,CAAC4G,YAAY,CAAC,CAACzC,OAAO,CAAC,CAAC,CAAC,EAAG;cAChEjM,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD,CAACyP,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,iBAC1DzG,OAAA,CAAChC,IAAI;cACHkT,IAAI,eAAElR,OAAA,CAACf,UAAU;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBwO,KAAK,EAAE,GAAG,CAACiB,YAAY,IAAI,EAAE,EAAEvG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACG,QAAQ,CAAC,CAACK,MAAM,OAAQ;cACzEzE,KAAK,EAAC,SAAS;cACfD,OAAO,EAAC,UAAU;cAClBE,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE4O,EAAE,EAAE;MAAE,CAAE;MAAAjO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,gBACV7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEuG,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAAjO,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAEsQ,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAAApO,QAAA,gBACxD7C,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACf+N,SAAS,eAAE/P,OAAA,CAAClB,YAAY;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BS,OAAO,EAAEmH,cAAe;YAAAhG,QAAA,EACzB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACf+N,SAAS,EAAExL,oBAAoB,gBAAGvE,OAAA,CAACjC,gBAAgB;cAACkE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAS;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACb,gBAAgB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxGS,OAAO,EAAE6L,gBAAiB;YAC1B8D,QAAQ,EAAE9M,oBAAqB;YAAA1B,QAAA,EAE9B0B,oBAAoB,GAAG,QAAQ,GAAG;UAAM;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAETjB,OAAA,CAAC/C,MAAM;YACL8E,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAC,OAAO;YACb+N,SAAS,eAAE/P,OAAA,CAACjB,cAAc;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BS,OAAO,EAAEgI,aAAc;YAAA7G,QAAA,EACxB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9B,IAAI;MAACgE,EAAE,EAAE;QAAE4O,EAAE,EAAE;MAAE,CAAE;MAAAjO,QAAA,eAClB7C,OAAA,CAAC7B,WAAW;QAAA0E,QAAA,gBACV7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,WAAW;UAACG,EAAE,EAAE;YAAEuG,UAAU,EAAE,GAAG;YAAEqI,EAAE,EAAE;UAAE,CAAE;UAAAjO,QAAA,EAAC;QAEhE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAE;YAAEsQ,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UAACH,OAAO,EAAE,CAAE;UAACpB,UAAU,EAAC,QAAQ;UAAAhN,QAAA,gBAC5E7C,OAAA,CAAC1B,WAAW;YAAC2D,IAAI,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAU,QAAA,gBAC9C7C,OAAA,CAACzB,UAAU;cAAAsE,QAAA,EAAC;YAAI;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BjB,OAAA,CAACxB,MAAM;cACLsQ,KAAK,EAAEjK,YAAa;cACpB4K,KAAK,EAAC,0BAAM;cACZ6B,QAAQ,EAAGxP,CAAC,IAAKgD,eAAe,CAAChD,CAAC,CAACyP,MAAM,CAACzC,KAAK,CAAE;cAAAjM,QAAA,gBAEjD7C,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,KAAK;gBAAAjM,QAAA,EAAC;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,IAAI;gBAAAjM,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,MAAM;gBAAAjM,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,YAAY;gBAAAjM,QAAA,EAAC;cAAU;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,OAAO;gBAAAjM,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,IAAI;gBAAAjM,QAAA,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,SAAS;gBAAAjM,QAAA,EAAC;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,UAAU;gBAAAjM,QAAA,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3CjB,OAAA,CAACvB,QAAQ;gBAACqQ,KAAK,EAAC,YAAY;gBAAAjM,QAAA,EAAC;cAAM;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdjB,OAAA,CAACnC,SAAS;YACRoE,IAAI,EAAC,OAAO;YACZuP,WAAW,EAAC,yCAAW;YACvB1C,KAAK,EAAErK,UAAW;YAClB6M,QAAQ,EAAGxP,CAAC,IAAK4C,aAAa,CAAC5C,CAAC,CAACyP,MAAM,CAACzC,KAAK,CAAE;YAC/C5M,EAAE,EAAE;cAAEuP,QAAQ,EAAE,CAAC;cAAEtP,QAAQ,EAAE;YAAI,CAAE;YACnCuP,UAAU,EAAE;cACVC,cAAc,eACZ3R,OAAA,CAACtB,cAAc;gBAAC2J,QAAQ,EAAC,OAAO;gBAAAxF,QAAA,eAC9B7C,OAAA,CAACT,UAAU;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACjB;cACD2Q,YAAY,EAAEnN,UAAU,iBACtBzE,OAAA,CAACtB,cAAc;gBAAC2J,QAAQ,EAAC,KAAK;gBAAAxF,QAAA,eAC5B7C,OAAA,CAAClC,UAAU;kBACTmE,IAAI,EAAC,OAAO;kBACZP,OAAO,EAAEA,CAAA,KAAMgD,aAAa,CAAC,EAAE,CAAE;kBACjCmN,IAAI,EAAC,KAAK;kBAAAhP,QAAA,eAEV7C,OAAA,CAACR,SAAS;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDwD,UAAU,iBACTzE,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAa,QAAA,GAAC,eAC9C,EAACsN,gBAAgB,CAAChG,MAAM,CAAClE,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM,EAAC,qBAChE;UAAA;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjB,OAAA,CAAC9C,KAAK;MAACgF,EAAE,EAAE;QAAEiN,KAAK,EAAE,MAAM;QAAE5M,QAAQ,EAAE;MAAS,CAAE;MAAAM,QAAA,eAC7C7C,OAAA,CAACjD,GAAG;QAACmF,EAAE,EAAE;UAAES,MAAM,EAAE,MAAM;UAAEwM,KAAK,EAAE;QAAO,CAAE;QAAAtM,QAAA,eACzC7C,OAAA,CAACnB,QAAQ;UACPiT,IAAI,EAAEpB,YAAa;UACnB3B,OAAO,EAAEA,OAAQ;UACjB1J,QAAQ,EAAEA,QAAS;UACnB0M,gBAAgB,EAAGC,WAAW,IAAK1M,WAAW,CAAC0M,WAAW,CAAE;UAC5DvM,IAAI,EAAEA,IAAK;UACXwM,YAAY,EAAGC,OAAO,IAAKlN,OAAO,CAACkN,OAAO,CAAE;UAC5CC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAClCC,UAAU;UACVC,uBAAuB;UACvBC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,qBAAqB,EAAE,KAAM;UAC7BC,SAAS,EAAE,EAAG;UACdC,OAAO,EAAC,SAAS;UACjBC,SAAS,EAAE,CAAE;UACbC,YAAY,EAAE,CAAE;UAChBC,iBAAiB;UACjBC,mBAAmB;UACnBC,qBAAqB;UACrBC,sBAAsB;UACtBC,0BAA0B;UAC1BC,eAAe,EAAGrJ,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACF+M,cAAc,EAAGtJ,MAAM,IAAK;YAC1B,IAAIA,MAAM,CAAC5D,GAAG,CAACc,OAAO,IAAI8C,MAAM,CAAC5D,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOyD,MAAM,CAACuJ,MAAM,CAAC1P,QAAQ,IAAI,OAAOmG,MAAM,CAACuJ,MAAM,CAAC1P,QAAQ,KAAK,UAAU,GAC3EmG,MAAM,CAACuJ,MAAM,CAAC1P,QAAQ,CAACmG,MAAM,CAAC,GAAGA,MAAM,CAACuJ,MAAM,CAAC1P,QAAQ;UAC3D,CAAE;UACFmH,gBAAgB,EAAGC,MAAM,IAAK;YAC5B,IAAIA,MAAM,CAACxD,UAAU,KAAKX,SAAS,EAAE;cACnC,IAAI,OAAOmE,MAAM,CAACxD,UAAU,KAAK,QAAQ,EAAE;gBACzCwD,MAAM,CAACxD,UAAU,GAAGgD,MAAM,CAACQ,MAAM,CAACxD,UAAU,CAAC,IAAI,CAAC;cACpD;YACF;YACA,OAAOuD,gBAAgB,CAACC,MAAM,CAAC;UACjC,CAAE;UACFI,uBAAuB,EAAEA,uBAAwB;UACjDhJ,EAAE,EAAE;YACF,cAAc,EAAE;cACd4N,eAAe,EAAE,0BAA0B;cAC3CrH,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBqH,eAAe,EAAE,0BAA0B;cAC3C9N,KAAK,EAAE,eAAe;cACtBiO,cAAc,EAAE;YAClB,CAAC;YACD,qBAAqB,EAAE;cACrBxN,UAAU,EAAE,QAAQ;cACpBG,UAAU,EAAE,QAAQ;cACpB8F,OAAO,EAAE,KAAK;cACd2K,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,8BAA8B,EAAE;cAC9BxD,eAAe,EAAE,oBAAoB;cACrCuD,YAAY,EAAE,WAAW;cACzBC,WAAW,EAAE;YACf,CAAC;YACD,6BAA6B,EAAE;cAC7BxD,eAAe,EAAE,oBAAoB;cACrC9N,KAAK,EAAE,cAAc;cACrBuR,WAAW,EAAE,WAAW;cACxBD,WAAW,EAAE,SAAS;cACtB,cAAc,EAAE;gBACdC,WAAW,EAAE;cACf;YACF,CAAC;YACD,kCAAkC,EAAE;cAClC9K,UAAU,EAAE,MAAM;cAClBzG,KAAK,EAAE,cAAc;cACrBK,QAAQ,EAAE;YACZ,CAAC;YACD,gCAAgC,EAAE;cAChCqM,OAAO,EAAE;YACX,CAAC;YACD8E,SAAS,EAAE;UACb;QAAE;UAAA1S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGVjB,OAAA,CAAC3C,MAAM;MACLyK,IAAI,EAAEF,aAAa,CAACE,IAAK;MACzB2L,OAAO,EAAErI,kBAAmB;MAC5BsI,SAAS;MACTtR,QAAQ,EAAC,IAAI;MACbuR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjR,QAAA,gBAEnB7C,OAAA,CAAC1C,WAAW;QAAAuF,QAAA,eACV7C,OAAA,CAACjD,GAAG;UAACmF,EAAE,EAAE;YAAEwM,OAAO,EAAE,MAAM;YAAEqC,cAAc,EAAE,eAAe;YAAElB,UAAU,EAAE;UAAS,CAAE;UAAAhN,QAAA,gBAClF7C,OAAA,CAAChD,UAAU;YAAC+E,OAAO,EAAC,IAAI;YAAAc,QAAA,EAAC;UAAS;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CjB,OAAA,CAAC/C,MAAM;YACL8S,SAAS,eAAE/P,OAAA,CAAChB,OAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEoK,mBAAoB;YAC7B9J,KAAK,EAAC,SAAS;YAAAa,QAAA,EAChB;UAED;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdjB,OAAA,CAACjD,GAAG;QAACmF,EAAE,EAAE;UAAE6R,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnR,QAAA,gBACxB7C,OAAA,CAAChD,UAAU;UAAC+E,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAE4O,EAAE,EAAE;UAAE,CAAE;UAAAjO,QAAA,EAAC;QAElE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA,CAAC5B,KAAK;UAACyC,SAAS,EAAC,KAAK;UAACoQ,OAAO,EAAE,CAAE;UAAApO,QAAA,gBAChC7C,OAAA,CAACpB,gBAAgB;YACfqV,OAAO,eACLjU,OAAA,CAACrB,QAAQ;cACPuV,OAAO,EAAEjP,aAAc;cACvBqM,QAAQ,EAAGxP,CAAC,IAAKoD,gBAAgB,CAACpD,CAAC,CAACyP,MAAM,CAAC2C,OAAO,CAAE;cACpDjS,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDwO,KAAK,EAAC;UAAS;YAAA3O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFjB,OAAA,CAACpB,gBAAgB;YACfqV,OAAO,eACLjU,OAAA,CAACrB,QAAQ;cACPuV,OAAO,EAAE/O,UAAW;cACpBmM,QAAQ,EAAGxP,CAAC,IAAKsD,aAAa,CAACtD,CAAC,CAACyP,MAAM,CAAC2C,OAAO,CAAE;cACjDjS,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDwO,KAAK,EAAC;UAAK;YAAA3O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENjB,OAAA,CAACzC,aAAa;QAAC4W,QAAQ;QAACjS,EAAE,EAAE;UAAEkS,CAAC,EAAE;QAAE,CAAE;QAAAvR,QAAA,eACnC7C,OAAA,CAACH,aAAa;UACZ8C,MAAM,EAAE,GAAI;UACZ0R,SAAS,EAAEzQ,cAAc,CAAC6C,MAAO;UACjC6N,QAAQ,EAAE,EAAG;UACbnF,KAAK,EAAC,MAAM;UAAAtM,QAAA,EAEXA,CAAC;YAAEgE,KAAK;YAAEyB;UAAM,CAAC,KAAK;YACrB,MAAMgD,MAAM,GAAG1H,cAAc,CAACiD,KAAK,CAAC;YACpC,oBACE7G,OAAA,CAACtC,QAAQ;cAEP4K,KAAK,EAAEA,KAAM;cACbiM,cAAc;cACdC,eAAe,EAAGlJ,MAAM,KAAK,MAAM,iBACjCtL,OAAA,CAAClC,UAAU;gBACT+T,IAAI,EAAC,KAAK;gBACV,cAAW,QAAQ;gBACnBnQ,OAAO,EAAEA,CAAA,KAAMyK,YAAY,CAACb,MAAM,CAAE;gBAAAzI,QAAA,eAEpC7C,OAAA,CAACf,UAAU;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ;cAAA4B,QAAA,eAEF7C,OAAA,CAACrC,cAAc;gBAAC+D,OAAO,EAAEA,CAAA,KAAM2J,kBAAkB,CAACC,MAAM,CAAE;gBAAAzI,QAAA,eACxD7C,OAAA,CAACpC,YAAY;kBAAC6W,OAAO,EAAEnJ;gBAAO;kBAAAxK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GAfZqK,MAAM;cAAAxK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBH,CAAC;UAEf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAAqF,QAAA,eACZ7C,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAE0J,kBAAmB;UAAAvI,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjB,OAAA,CAAC3C,MAAM;MACLyK,IAAI,EAAEzD,eAAgB;MACtBoP,OAAO,EAAE1H,oBAAqB;MAC9B2H,SAAS;MACTtR,QAAQ,EAAC,IAAI;MACbuR,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAjR,QAAA,gBAEnB7C,OAAA,CAAC1C,WAAW;QAAAuF,QAAA,EAAC;MAAK;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChCjB,OAAA,CAACzC,aAAa;QAAAsF,QAAA,eACZ7C,OAAA,CAACnC,SAAS;UACR6W,SAAS;UACTC,MAAM,EAAC,OAAO;UACd7N,EAAE,EAAC,MAAM;UACT2I,KAAK,EAAC,0BAAM;UACZvH,IAAI,EAAC,MAAM;UACXwL,SAAS;UACT3R,OAAO,EAAC,UAAU;UAClB+M,KAAK,EAAE3K,SAAU;UACjBmN,QAAQ,EAAGxP,CAAC,IAAKsC,YAAY,CAACtC,CAAC,CAACyP,MAAM,CAACzC,KAAK;QAAE;UAAAhO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBjB,OAAA,CAACxC,aAAa;QAAAqF,QAAA,gBACZ7C,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAEqK,oBAAqB;UAAAlJ,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjB,OAAA,CAAC/C,MAAM;UAACyE,OAAO,EAAEsK,YAAa;UAAChK,KAAK,EAAC,SAAS;UAAAa,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACqC,GAAA,CAluCIP,aAAa;AAAA6R,GAAA,GAAb7R,aAAa;AAouCnB,eAAeA,aAAa;AAAC,IAAA7B,EAAA,EAAAI,GAAA,EAAAwB,GAAA,EAAA8R,GAAA;AAAAC,YAAA,CAAA3T,EAAA;AAAA2T,YAAA,CAAAvT,GAAA;AAAAuT,YAAA,CAAA/R,GAAA;AAAA+R,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}