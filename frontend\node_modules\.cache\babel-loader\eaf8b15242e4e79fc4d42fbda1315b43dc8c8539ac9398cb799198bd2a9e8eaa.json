{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = 'http://localhost:5000/api';\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError\n}) => {\n  _s();\n  var _gridData$find;\n  const [gridData, setGridData] = useState(data.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const handleDownload = async () => {\n    try {\n      // 使用浏览器的下载功能\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL').reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true\n  }, {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true\n  }];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          getRowClassName: params => params.row.isTotal ? 'total-row' : '',\n          isCellEditable: handleCellEdit,\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), gridData.some(row => 'COMMISSION' in row) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u9879\\u76EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u91D1\\u989D (RM)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u603B\\u4F63\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: ((_gridData$find = gridData.find(row => row.NO === 'TOTAL')) === null || _gridData$find === void 0 ? void 0 : _gridData$find.COMMISSION.toFixed(2)) || '0.00',\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"aXOWaYeDjdd+HfgQUD/SX2es1Hw=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "axios", "jsxDEV", "_jsxDEV", "API_URL", "ResultDisplay", "data", "fileId", "onReset", "onError", "_s", "_gridData$find", "gridData", "setGridData", "map", "row", "index", "id", "isTotal", "NO", "snackbar", "setSnackbar", "open", "message", "severity", "handleDownload", "window", "error", "console", "handleCleanup", "delete", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "undefined", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "length", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "columns", "col", "hasOwnProperty", "flex", "min<PERSON><PERSON><PERSON>", "renderCell", "fontWeight", "value", "toFixed", "split", "Math", "floor", "Boolean", "display", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "width", "overflow", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "getRowClassName", "isCellEditable", "experimentalFeatures", "newEditingApi", "backgroundColor", "some", "component", "align", "label", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Button,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Chip,\r\n  Snackbar,\r\n  Alert\r\n} from '@mui/material';\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport DownloadIcon from '@mui/icons-material/Download';\r\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\r\nimport axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:5000/api';\r\n\r\nconst ResultDisplay = ({ data, fileId, onReset, onError }) => {\r\n  const [gridData, setGridData] = useState(data.map((row, index) => ({\r\n    ...row,\r\n    id: index,\r\n    isTotal: row.NO === 'TOTAL'\r\n  })));\r\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\r\n\r\n  const handleDownload = async () => {\r\n    try {\r\n      // 使用浏览器的下载功能\r\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\r\n    } catch (error) {\r\n      console.error('下载文件出错:', error);\r\n      onError('下载文件失败，请重试');\r\n    }\r\n  };\r\n  \r\n  const handleCleanup = async () => {\r\n    try {\r\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\r\n    } catch (error) {\r\n      console.error('清理文件出错:', error);\r\n    }\r\n    \r\n    onReset();\r\n  };\r\n\r\n  const handleCellEdit = (params) => {\r\n    // 阻止总计行被编辑\r\n    if (params.row.NO === 'TOTAL') {\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const processRowUpdate = (newRow) => {\r\n    // 更新行数据\r\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\r\n    \r\n    // 重新计算总计\r\n    if (newRow.COMMISSION !== undefined) {\r\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\r\n      if (totalRow) {\r\n        const newTotal = updatedData\r\n          .filter(row => row.NO !== 'TOTAL')\r\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\r\n        \r\n        totalRow.COMMISSION = newTotal;\r\n      }\r\n    }\r\n    \r\n    setGridData(updatedData);\r\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\r\n    return newRow;\r\n  };\r\n\r\n  const onProcessRowUpdateError = (error) => {\r\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\r\n  };\r\n\r\n  const handleCloseSnackbar = () => {\r\n    setSnackbar(prev => ({ ...prev, open: false }));\r\n  };\r\n  \r\n  // 如果数据为空\r\n  if (!gridData || gridData.length === 0) {\r\n    return (\r\n      <Box sx={{ textAlign: 'center', py: 3 }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">\r\n          没有找到数据\r\n        </Typography>\r\n        <Button \r\n          variant=\"contained\" \r\n          onClick={onReset}\r\n          sx={{ mt: 2 }}\r\n        >\r\n          重新开始\r\n        </Button>\r\n      </Box>\r\n    );\r\n  }\r\n  \r\n  // 定义列的显示顺序和标题\r\n  const columnOrder = [\r\n    { field: 'NO', headerName: 'NO', editable: true },\r\n    { field: 'DATE', headerName: 'DATE', editable: true },\r\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true },\r\n    { field: 'RO NO', headerName: 'RO NO', editable: true },\r\n    { field: 'KM', headerName: 'KM', editable: true },\r\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true },\r\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true }\r\n  ];\r\n  \r\n  // 过滤和排序列\r\n  const columns = columnOrder.map(col => {\r\n    if (!gridData[0].hasOwnProperty(col.field) && col.field === 'COMMISSION') {\r\n      // 如果COMMISSION列不存在，跳过\r\n      return null;\r\n    }\r\n    \r\n    return {\r\n      field: col.field,\r\n      headerName: col.headerName,\r\n      flex: 1,\r\n      minWidth: 120,\r\n      editable: col.editable,\r\n      renderCell: (params) => {\r\n        // 特殊处理总计行\r\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\r\n          return (\r\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\r\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\r\n            </Typography>\r\n          );\r\n        }\r\n        \r\n        // 处理日期格式\r\n        if (col.field === 'DATE' && params.value) {\r\n          return params.value.split('T')[0]; // 只显示日期部分\r\n        }\r\n        \r\n        // NO列不显示浮点数\r\n        if (col.field === 'NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // RO NO列不显示浮点数\r\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // KM列不显示浮点数\r\n        if (col.field === 'KM' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\r\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\r\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\r\n        }\r\n        \r\n        // COMMISSION(AMOUNT)列保留2位小数\r\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\r\n          return params.value.toFixed(2);\r\n        }\r\n        \r\n        // 其他数字格式\r\n        if (typeof params.value === 'number') {\r\n          return params.value;\r\n        }\r\n        \r\n        return params.value;\r\n      }\r\n    };\r\n  }).filter(Boolean); // 过滤掉null值\r\n  \r\n  return (\r\n    <Box>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          处理结果\r\n        </Typography>\r\n        \r\n        <Box>\r\n          <Button \r\n            variant=\"contained\" \r\n            startIcon={<DownloadIcon />}\r\n            onClick={handleDownload}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            下载Excel\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"outlined\" \r\n            startIcon={<RestartAltIcon />}\r\n            onClick={handleCleanup}\r\n          >\r\n            重新开始\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n      \r\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n        <Box sx={{ height: 400, width: '100%' }}>\r\n          <DataGrid\r\n            rows={gridData}\r\n            columns={columns}\r\n            pageSize={10}\r\n            rowsPerPageOptions={[10, 25, 50]}\r\n            disableSelectionOnClick\r\n            getRowClassName={(params) => params.row.isTotal ? 'total-row' : ''}\r\n            isCellEditable={handleCellEdit}\r\n            processRowUpdate={processRowUpdate}\r\n            onProcessRowUpdateError={onProcessRowUpdateError}\r\n            experimentalFeatures={{ newEditingApi: true }}\r\n            sx={{\r\n              '& .total-row': {\r\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\r\n                fontWeight: 'bold',\r\n              },\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n      \r\n      {gridData.some(row => 'COMMISSION' in row) && (\r\n        <Box sx={{ mt: 3 }}>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            佣金计算结果\r\n          </Typography>\r\n          \r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>项目</TableCell>\r\n                  <TableCell align=\"right\">金额 (RM)</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell>总佣金</TableCell>\r\n                  <TableCell align=\"right\">\r\n                    <Chip \r\n                      label={gridData.find(row => row.NO === 'TOTAL')?.COMMISSION.toFixed(2) || '0.00'} \r\n                      color=\"primary\" \r\n                      variant=\"outlined\"\r\n                    />\r\n                  </TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n        </Box>\r\n      )}\r\n      \r\n      <Snackbar\r\n        open={snackbar.open}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSnackbar}\r\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n      >\r\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\r\n          {snackbar.message}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAG,2BAA2B;AAE3C,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC5D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAACsB,IAAI,CAACQ,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;IACjE,GAAGD,GAAG;IACNE,EAAE,EAAED,KAAK;IACTE,OAAO,EAAEH,GAAG,CAACI,EAAE,KAAK;EACtB,CAAC,CAAC,CAAC,CAAC;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC;IAAEsC,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAE3F,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACAC,MAAM,CAACJ,IAAI,CAAC,GAAGlB,OAAO,aAAaG,MAAM,EAAE,EAAE,QAAQ,CAAC;IACxD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlB,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM5B,KAAK,CAAC6B,MAAM,CAAC,GAAG1B,OAAO,YAAYG,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAnB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMuB,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACjB,GAAG,CAACI,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMc,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACA,MAAMC,WAAW,GAAGvB,QAAQ,CAACE,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACE,EAAE,KAAKiB,MAAM,CAACjB,EAAE,GAAGiB,MAAM,GAAGnB,GAAI,CAAC;;IAE9E;IACA,IAAImB,MAAM,CAACE,UAAU,KAAKC,SAAS,EAAE;MACnC,MAAMC,QAAQ,GAAGH,WAAW,CAACI,IAAI,CAACxB,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAImB,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGL,WAAW,CACzBM,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAK,OAAO,CAAC,CACjCuB,MAAM,CAAC,CAACC,GAAG,EAAE5B,GAAG,KAAK4B,GAAG,IAAI5B,GAAG,CAACqB,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDE,QAAQ,CAACF,UAAU,GAAGI,QAAQ;MAChC;IACF;IAEA3B,WAAW,CAACsB,WAAW,CAAC;IACxBd,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOU,MAAM;EACf,CAAC;EAED,MAAMU,uBAAuB,GAAIjB,KAAK,IAAK;IACzCN,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASI,KAAK,CAACJ,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMqB,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,WAAW,CAACyB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExB,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,IAAI,CAACV,QAAQ,IAAIA,QAAQ,CAACmC,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE5C,OAAA,CAAClB,GAAG;MAAC+D,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtChD,OAAA,CAACjB,UAAU;QAACkE,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtD,OAAA,CAAChB,MAAM;QACLiE,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAElD,OAAQ;QACjBwC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9D;;EAED;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAAC9C,GAAG,CAACmD,GAAG,IAAI;IACrC,IAAI,CAACrD,QAAQ,CAAC,CAAC,CAAC,CAACsD,cAAc,CAACD,GAAG,CAACJ,KAAK,CAAC,IAAII,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;MACxE;MACA,OAAO,IAAI;IACb;IAEA,OAAO;MACLA,KAAK,EAAEI,GAAG,CAACJ,KAAK;MAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;MAC1BK,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbL,QAAQ,EAAEE,GAAG,CAACF,QAAQ;MACtBM,UAAU,EAAGrC,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAACjB,GAAG,CAACI,EAAE,KAAK,OAAO,IAAI8C,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE1D,OAAA,CAACjB,UAAU;YAACkE,OAAO,EAAC,OAAO;YAACkB,UAAU,EAAC,MAAM;YAACjB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOnB,MAAM,CAACuC,KAAK,KAAK,QAAQ,GAAGvC,MAAM,CAACuC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGxC,MAAM,CAACuC;UAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIQ,GAAG,CAACJ,KAAK,KAAK,MAAM,IAAI7B,MAAM,CAACuC,KAAK,EAAE;UACxC,OAAOvC,MAAM,CAACuC,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAIR,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO7B,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOG,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACuC,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIN,GAAG,CAACJ,KAAK,KAAK,OAAO,IAAI,OAAO7B,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOG,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACuC,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIN,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO7B,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOG,IAAI,CAACC,KAAK,CAAC3C,MAAM,CAACuC,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIN,GAAG,CAACJ,KAAK,KAAK,UAAU,IAAI,OAAO7B,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOvC,MAAM,CAACuC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGvC,MAAM,CAACuC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGxC,MAAM,CAACuC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,YAAY,IAAI,OAAO7B,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOvC,MAAM,CAACuC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAOxC,MAAM,CAACuC,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOvC,MAAM,CAACuC,KAAK;QACrB;QAEA,OAAOvC,MAAM,CAACuC,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC9B,MAAM,CAACmC,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACEzE,OAAA,CAAClB,GAAG;IAAAkE,QAAA,gBACFhD,OAAA,CAAClB,GAAG;MAAC+D,EAAE,EAAE;QAAE6B,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA7B,QAAA,gBACzFhD,OAAA,CAACjB,UAAU;QAACkE,OAAO,EAAC,IAAI;QAAC6B,YAAY;QAAA9B,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtD,OAAA,CAAClB,GAAG;QAAAkE,QAAA,gBACFhD,OAAA,CAAChB,MAAM;UACLiE,OAAO,EAAC,WAAW;UACnB8B,SAAS,eAAE/E,OAAA,CAACJ,YAAY;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAEjC,cAAe;UACxBuB,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtD,OAAA,CAAChB,MAAM;UACLiE,OAAO,EAAC,UAAU;UAClB8B,SAAS,eAAE/E,OAAA,CAACH,cAAc;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAE7B,aAAc;UAAAsB,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA,CAACf,KAAK;MAAC4D,EAAE,EAAE;QAAEoC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAlC,QAAA,eAC/ChD,OAAA,CAAClB,GAAG;QAAC+D,EAAE,EAAE;UAAEsC,MAAM,EAAE,GAAG;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAjC,QAAA,eACtChD,OAAA,CAACL,QAAQ;UACPyF,IAAI,EAAE3E,QAAS;UACfoD,OAAO,EAAEA,OAAQ;UACjBwB,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,eAAe,EAAG3D,MAAM,IAAKA,MAAM,CAACjB,GAAG,CAACG,OAAO,GAAG,WAAW,GAAG,EAAG;UACnE0E,cAAc,EAAE7D,cAAe;UAC/BE,gBAAgB,EAAEA,gBAAiB;UACnCW,uBAAuB,EAAEA,uBAAwB;UACjDiD,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9C9C,EAAE,EAAE;YACF,cAAc,EAAE;cACd+C,eAAe,EAAE,0BAA0B;cAC3CzB,UAAU,EAAE;YACd;UACF;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEP7C,QAAQ,CAACoF,IAAI,CAACjF,GAAG,IAAI,YAAY,IAAIA,GAAG,CAAC,iBACxCZ,OAAA,CAAClB,GAAG;MAAC+D,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACjBhD,OAAA,CAACjB,UAAU;QAACkE,OAAO,EAAC,WAAW;QAAC6B,YAAY;QAAA9B,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtD,OAAA,CAACX,cAAc;QAACyG,SAAS,EAAE7G,KAAM;QAAA+D,QAAA,eAC/BhD,OAAA,CAACd,KAAK;UAAA8D,QAAA,gBACJhD,OAAA,CAACV,SAAS;YAAA0D,QAAA,eACRhD,OAAA,CAACT,QAAQ;cAAAyD,QAAA,gBACPhD,OAAA,CAACZ,SAAS;gBAAA4D,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzBtD,OAAA,CAACZ,SAAS;gBAAC2G,KAAK,EAAC,OAAO;gBAAA/C,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZtD,OAAA,CAACb,SAAS;YAAA6D,QAAA,eACRhD,OAAA,CAACT,QAAQ;cAAAyD,QAAA,gBACPhD,OAAA,CAACZ,SAAS;gBAAA4D,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1BtD,OAAA,CAACZ,SAAS;gBAAC2G,KAAK,EAAC,OAAO;gBAAA/C,QAAA,eACtBhD,OAAA,CAACR,IAAI;kBACHwG,KAAK,EAAE,EAAAxF,cAAA,GAAAC,QAAQ,CAAC2B,IAAI,CAACxB,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAK,OAAO,CAAC,cAAAR,cAAA,uBAAxCA,cAAA,CAA0CyB,UAAU,CAACoC,OAAO,CAAC,CAAC,CAAC,KAAI,MAAO;kBACjFnB,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAEDtD,OAAA,CAACP,QAAQ;MACP0B,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB8E,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAExD,mBAAoB;MAC7ByD,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAArD,QAAA,eAE1DhD,OAAA,CAACN,KAAK;QAACwG,OAAO,EAAExD,mBAAoB;QAACrB,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAA2B,QAAA,EAC9D/B,QAAQ,CAACG;MAAO;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA1PIL,aAAa;AAAAoG,EAAA,GAAbpG,aAAa;AA4PnB,eAAeA,aAAa;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}