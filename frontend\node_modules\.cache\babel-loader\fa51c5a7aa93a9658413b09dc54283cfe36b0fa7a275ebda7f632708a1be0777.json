{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel } from './gridPaginationUtils';\nimport { useGridPaginationModel } from './useGridPaginationModel';\nimport { useGridRowCount } from './useGridRowCount';\nexport const paginationStateInitializer = (state, props) => {\n  var _props$paginationMode, _props$initialState, _ref, _props$rowCount, _props$initialState2;\n  const paginationModel = _extends({}, getDefaultGridPaginationModel(props.autoPageSize), (_props$paginationMode = props.paginationModel) != null ? _props$paginationMode : (_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.pagination) == null ? void 0 : _props$initialState.paginationModel);\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, props.signature);\n  const rowCount = (_ref = (_props$rowCount = props.rowCount) != null ? _props$rowCount : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.pagination) == null ? void 0 : _props$initialState2.rowCount) != null ? _ref : 0;\n  return _extends({}, state, {\n    pagination: {\n      paginationModel,\n      rowCount\n    }\n  });\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPagination = (apiRef, props) => {\n  useGridPaginationModel(apiRef, props);\n  useGridRowCount(apiRef, props);\n};", "map": {"version": 3, "names": ["_extends", "throwIfPageSizeExceedsTheLimit", "getDefaultGridPaginationModel", "useGridPaginationModel", "useGridRowCount", "paginationStateInitializer", "state", "props", "_props$paginationMode", "_props$initialState", "_ref", "_props$rowCount", "_props$initialState2", "paginationModel", "autoPageSize", "initialState", "pagination", "pageSize", "signature", "rowCount", "useGridPagination", "apiRef"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/node_modules/@mui/x-data-grid/hooks/features/pagination/useGridPagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel } from './gridPaginationUtils';\nimport { useGridPaginationModel } from './useGridPaginationModel';\nimport { useGridRowCount } from './useGridRowCount';\nexport const paginationStateInitializer = (state, props) => {\n  var _props$paginationMode, _props$initialState, _ref, _props$rowCount, _props$initialState2;\n  const paginationModel = _extends({}, getDefaultGridPaginationModel(props.autoPageSize), (_props$paginationMode = props.paginationModel) != null ? _props$paginationMode : (_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.pagination) == null ? void 0 : _props$initialState.paginationModel);\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, props.signature);\n  const rowCount = (_ref = (_props$rowCount = props.rowCount) != null ? _props$rowCount : (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.pagination) == null ? void 0 : _props$initialState2.rowCount) != null ? _ref : 0;\n  return _extends({}, state, {\n    pagination: {\n      paginationModel,\n      rowCount\n    }\n  });\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPagination = (apiRef, props) => {\n  useGridPaginationModel(apiRef, props);\n  useGridRowCount(apiRef, props);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,8BAA8B,EAAEC,6BAA6B,QAAQ,uBAAuB;AACrG,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC1D,IAAIC,qBAAqB,EAAEC,mBAAmB,EAAEC,IAAI,EAAEC,eAAe,EAAEC,oBAAoB;EAC3F,MAAMC,eAAe,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEE,6BAA6B,CAACK,KAAK,CAACO,YAAY,CAAC,EAAE,CAACN,qBAAqB,GAAGD,KAAK,CAACM,eAAe,KAAK,IAAI,GAAGL,qBAAqB,GAAG,CAACC,mBAAmB,GAAGF,KAAK,CAACQ,YAAY,KAAK,IAAI,IAAI,CAACN,mBAAmB,GAAGA,mBAAmB,CAACO,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,mBAAmB,CAACI,eAAe,CAAC;EAC9UZ,8BAA8B,CAACY,eAAe,CAACI,QAAQ,EAAEV,KAAK,CAACW,SAAS,CAAC;EACzE,MAAMC,QAAQ,GAAG,CAACT,IAAI,GAAG,CAACC,eAAe,GAAGJ,KAAK,CAACY,QAAQ,KAAK,IAAI,GAAGR,eAAe,GAAG,CAACC,oBAAoB,GAAGL,KAAK,CAACQ,YAAY,KAAK,IAAI,IAAI,CAACH,oBAAoB,GAAGA,oBAAoB,CAACI,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,oBAAoB,CAACO,QAAQ,KAAK,IAAI,GAAGT,IAAI,GAAG,CAAC;EAC5Q,OAAOV,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IACzBU,UAAU,EAAE;MACVH,eAAe;MACfM;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEd,KAAK,KAAK;EAClDJ,sBAAsB,CAACkB,MAAM,EAAEd,KAAK,CAAC;EACrCH,eAAe,CAACiB,MAAM,EAAEd,KAAK,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}