{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getGridLocalization = (gridTranslations, coreTranslations) => {\n  var _coreTranslations$com;\n  return {\n    components: {\n      MuiDataGrid: {\n        defaultProps: {\n          localeText: _extends({}, gridTranslations, {\n            MuiTablePagination: (coreTranslations == null || (_coreTranslations$com = coreTranslations.components) == null || (_coreTranslations$com = _coreTranslations$com.MuiTablePagination) == null ? void 0 : _coreTranslations$com.defaultProps) || {}\n          })\n        }\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["_extends", "getGridLocalization", "gridTranslations", "coreTranslations", "_coreTranslations$com", "components", "MuiDataGrid", "defaultProps", "localeText", "MuiTablePagination"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/utils/getGridLocalization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getGridLocalization = (gridTranslations, coreTranslations) => {\n  var _coreTranslations$com;\n  return {\n    components: {\n      MuiDataGrid: {\n        defaultProps: {\n          localeText: _extends({}, gridTranslations, {\n            MuiTablePagination: (coreTranslations == null || (_coreTranslations$com = coreTranslations.components) == null || (_coreTranslations$com = _coreTranslations$com.MuiTablePagination) == null ? void 0 : _coreTranslations$com.defaultProps) || {}\n          })\n        }\n      }\n    }\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,gBAAgB,EAAEC,gBAAgB,KAAK;EACzE,IAAIC,qBAAqB;EACzB,OAAO;IACLC,UAAU,EAAE;MACVC,WAAW,EAAE;QACXC,YAAY,EAAE;UACZC,UAAU,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEE,gBAAgB,EAAE;YACzCO,kBAAkB,EAAE,CAACN,gBAAgB,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,gBAAgB,CAACE,UAAU,KAAK,IAAI,IAAI,CAACD,qBAAqB,GAAGA,qBAAqB,CAACK,kBAAkB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,qBAAqB,CAACG,YAAY,KAAK,CAAC;UAClP,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}