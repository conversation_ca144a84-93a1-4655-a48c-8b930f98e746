{"ast": null, "code": "import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\n/**\n * Get the columns state\n * @category Columns\n */\nexport const gridColumnsStateSelector = state => state.columns;\n\n/**\n * Get an array of column fields in the order rendered on screen.\n * @category Columns\n */\nexport const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);\n\n/**\n * Get the columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);\n\n/**\n * Get an array of column definitions in the order rendered on screen..\n * @category Columns\n */\nexport const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));\n\n/**\n * Get the column visibility model, containing the visibility status of each column.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);\n\n/**\n * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Visible Columns\n */\nexport const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, (columns, columnVisibilityModel) => columns.filter(column => columnVisibilityModel[column.field] !== false));\n\n/**\n * Get the field of each visible column.\n * @category Visible Columns\n */\nexport const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));\n\n/**\n * Get the left position in pixel of each visible columns relative to the left of the first column.\n * @category Visible Columns\n */\nexport const gridColumnPositionsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {\n  const positions = [];\n  let currentPosition = 0;\n  for (let i = 0; i < visibleColumns.length; i += 1) {\n    positions.push(currentPosition);\n    currentPosition += visibleColumns[i].computedWidth;\n  }\n  return positions;\n});\n\n/**\n * Get the summed width of all the visible columns.\n * @category Visible Columns\n */\nexport const gridColumnsTotalWidthSelector = createSelector(gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector, (visibleColumns, positions) => {\n  const colCount = visibleColumns.length;\n  if (colCount === 0) {\n    return 0;\n  }\n  return positions[colCount - 1] + visibleColumns[colCount - 1].computedWidth;\n});\n\n/**\n * Get the filterable columns as an array.\n * @category Columns\n */\nexport const gridFilterableColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));\n\n/**\n * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridFilterableColumnLookupSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {\n  if (col.filterable) {\n    acc[col.field] = col;\n  }\n  return acc;\n}, {}));", "map": {"version": 3, "names": ["createSelector", "createSelectorMemoized", "gridColumnsStateSelector", "state", "columns", "gridColumnFieldsSelector", "columnsState", "orderedFields", "gridColumnLookupSelector", "lookup", "gridColumnDefinitionsSelector", "allFields", "map", "field", "gridColumnVisibilityModelSelector", "columnVisibilityModel", "gridVisibleColumnDefinitionsSelector", "filter", "column", "gridVisibleColumnFieldsSelector", "visibleColumns", "gridColumnPositionsSelector", "positions", "currentPosition", "i", "length", "push", "computedWidth", "gridColumnsTotalWidthSelector", "col<PERSON>ount", "gridFilterableColumnDefinitionsSelector", "col", "filterable", "gridFilterableColumnLookupSelector", "reduce", "acc"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/x-data-grid/hooks/features/columns/gridColumnsSelector.js"], "sourcesContent": ["import { createSelector, createSelectorMemoized } from '../../../utils/createSelector';\n/**\n * Get the columns state\n * @category Columns\n */\nexport const gridColumnsStateSelector = state => state.columns;\n\n/**\n * Get an array of column fields in the order rendered on screen.\n * @category Columns\n */\nexport const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);\n\n/**\n * Get the columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);\n\n/**\n * Get an array of column definitions in the order rendered on screen..\n * @category Columns\n */\nexport const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));\n\n/**\n * Get the column visibility model, containing the visibility status of each column.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);\n\n/**\n * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Visible Columns\n */\nexport const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, (columns, columnVisibilityModel) => columns.filter(column => columnVisibilityModel[column.field] !== false));\n\n/**\n * Get the field of each visible column.\n * @category Visible Columns\n */\nexport const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));\n\n/**\n * Get the left position in pixel of each visible columns relative to the left of the first column.\n * @category Visible Columns\n */\nexport const gridColumnPositionsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {\n  const positions = [];\n  let currentPosition = 0;\n  for (let i = 0; i < visibleColumns.length; i += 1) {\n    positions.push(currentPosition);\n    currentPosition += visibleColumns[i].computedWidth;\n  }\n  return positions;\n});\n\n/**\n * Get the summed width of all the visible columns.\n * @category Visible Columns\n */\nexport const gridColumnsTotalWidthSelector = createSelector(gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector, (visibleColumns, positions) => {\n  const colCount = visibleColumns.length;\n  if (colCount === 0) {\n    return 0;\n  }\n  return positions[colCount - 1] + visibleColumns[colCount - 1].computedWidth;\n});\n\n/**\n * Get the filterable columns as an array.\n * @category Columns\n */\nexport const gridFilterableColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));\n\n/**\n * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridFilterableColumnLookupSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {\n  if (col.filterable) {\n    acc[col.field] = col;\n  }\n  return acc;\n}, {}));"], "mappings": "AAAA,SAASA,cAAc,EAAEC,sBAAsB,QAAQ,+BAA+B;AACtF;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGC,KAAK,IAAIA,KAAK,CAACC,OAAO;;AAE9D;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGL,cAAc,CAACE,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACC,aAAa,CAAC;;AAE5H;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGR,cAAc,CAACE,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACG,MAAM,CAAC;;AAErH;AACA;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAGT,sBAAsB,CAACI,wBAAwB,EAAEG,wBAAwB,EAAE,CAACG,SAAS,EAAEF,MAAM,KAAKE,SAAS,CAACC,GAAG,CAACC,KAAK,IAAIJ,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;;AAErL;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iCAAiC,GAAGd,cAAc,CAACE,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACS,qBAAqB,CAAC;;AAE7I;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGf,sBAAsB,CAACS,6BAA6B,EAAEI,iCAAiC,EAAE,CAACV,OAAO,EAAEW,qBAAqB,KAAKX,OAAO,CAACa,MAAM,CAACC,MAAM,IAAIH,qBAAqB,CAACG,MAAM,CAACL,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;;AAEzP;AACA;AACA;AACA;AACA,OAAO,MAAMM,+BAA+B,GAAGlB,sBAAsB,CAACe,oCAAoC,EAAEI,cAAc,IAAIA,cAAc,CAACR,GAAG,CAACM,MAAM,IAAIA,MAAM,CAACL,KAAK,CAAC,CAAC;;AAEzK;AACA;AACA;AACA;AACA,OAAO,MAAMQ,2BAA2B,GAAGpB,sBAAsB,CAACe,oCAAoC,EAAEI,cAAc,IAAI;EACxH,MAAME,SAAS,GAAG,EAAE;EACpB,IAAIC,eAAe,GAAG,CAAC;EACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACK,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACjDF,SAAS,CAACI,IAAI,CAACH,eAAe,CAAC;IAC/BA,eAAe,IAAIH,cAAc,CAACI,CAAC,CAAC,CAACG,aAAa;EACpD;EACA,OAAOL,SAAS;AAClB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMM,6BAA6B,GAAG5B,cAAc,CAACgB,oCAAoC,EAAEK,2BAA2B,EAAE,CAACD,cAAc,EAAEE,SAAS,KAAK;EAC5J,MAAMO,QAAQ,GAAGT,cAAc,CAACK,MAAM;EACtC,IAAII,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAO,CAAC;EACV;EACA,OAAOP,SAAS,CAACO,QAAQ,GAAG,CAAC,CAAC,GAAGT,cAAc,CAACS,QAAQ,GAAG,CAAC,CAAC,CAACF,aAAa;AAC7E,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMG,uCAAuC,GAAG7B,sBAAsB,CAACS,6BAA6B,EAAEN,OAAO,IAAIA,OAAO,CAACa,MAAM,CAACc,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,CAAC;;AAE9J;AACA;AACA;AACA;AACA,OAAO,MAAMC,kCAAkC,GAAGhC,sBAAsB,CAACS,6BAA6B,EAAEN,OAAO,IAAIA,OAAO,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAK;EAC9I,IAAIA,GAAG,CAACC,UAAU,EAAE;IAClBG,GAAG,CAACJ,GAAG,CAAClB,KAAK,CAAC,GAAGkB,GAAG;EACtB;EACA,OAAOI,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}