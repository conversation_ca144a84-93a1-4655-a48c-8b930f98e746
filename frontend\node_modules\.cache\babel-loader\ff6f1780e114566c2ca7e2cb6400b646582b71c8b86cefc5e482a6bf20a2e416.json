{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend-old\\\\src\\\\components\\\\ProcessForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Grid, InputAdornment, CircularProgress, Card, CardContent, Fade, Slide, Chip, Alert, useTheme, alpha, Tooltip } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SendIcon from '@mui/icons-material/Send';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport InfoIcon from '@mui/icons-material/Info';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// localStorage的键名\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STORAGE_KEY = 'processFormParams';\nconst ProcessForm = ({\n  fileId,\n  worksheet,\n  onSubmit,\n  onBack,\n  onError\n}) => {\n  _s();\n  const theme = useTheme();\n\n  // 从localStorage获取上次保存的参数\n  const [startCol, setStartCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).startCol || '' : '';\n  });\n  const [endCol, setEndCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).endCol || '' : '';\n  });\n  const [perHourRate, setPerHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).perHourRate || '' : '';\n  });\n  const [cbuCarHourRate, setCbuCarHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).cbuCarHourRate || '' : '';\n  });\n  const [commissionRate, setCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).commissionRate || '' : '';\n  });\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // 验证函数\n  const validateColumn = value => {\n    const colRegex = /^[A-Za-z]+$/;\n    if (!value) return '请输入列名';\n    if (!colRegex.test(value)) return '列名格式无效，请使用字母 (例如: A, AB, AT)';\n    return '';\n  };\n  const validateRate = (value, fieldName) => {\n    if (value && (isNaN(value) || parseFloat(value) < 0)) {\n      return `${fieldName}必须是正数`;\n    }\n    return '';\n  };\n\n  // 当参数变化时保存到localStorage\n  useEffect(() => {\n    const paramsToSave = {\n      startCol,\n      endCol,\n      perHourRate,\n      cbuCarHourRate,\n      commissionRate\n    };\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(paramsToSave));\n  }, [startCol, endCol, perHourRate, cbuCarHourRate, commissionRate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // 验证输入\n    const newErrors = {};\n    const startColError = validateColumn(startCol);\n    const endColError = validateColumn(endCol);\n    const perHourRateError = validateRate(perHourRate, '小时费率');\n    const cbuCarHourRateError = validateRate(cbuCarHourRate, 'CBU CAR小时费率');\n    const commissionRateError = validateRate(commissionRate, '佣金率');\n    if (startColError) newErrors.startCol = startColError;\n    if (endColError) newErrors.endCol = endColError;\n    if (perHourRateError) newErrors.perHourRate = perHourRateError;\n    if (cbuCarHourRateError) newErrors.cbuCarHourRate = cbuCarHourRateError;\n    if (commissionRateError) newErrors.commissionRate = commissionRateError;\n    if (commissionRate && parseFloat(commissionRate) > 1) {\n      newErrors.commissionRate = '佣金率应在0-1之间';\n    }\n    setErrors(newErrors);\n    if (Object.keys(newErrors).length > 0) {\n      onError('请检查输入的参数');\n      return;\n    }\n    setLoading(true);\n    try {\n      const requestData = {\n        fileId,\n        worksheet,\n        startCol,\n        endCol,\n        perHourRate: perHourRate || undefined,\n        cbuCarHourRate: cbuCarHourRate || undefined,\n        commissionRate: commissionRate || undefined\n      };\n      const response = await axios.post(`${API_URL}/process`, requestData);\n      if (response.data && response.data.success) {\n        onSubmit({\n          startCol,\n          endCol,\n          perHourRate,\n          cbuCarHourRate,\n          commissionRate\n        }, response.data.data);\n      } else {\n        onError('处理数据失败，请重试');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('处理数据出错:', error);\n      onError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || '处理数据失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mb: 2,\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            fontSize: 32,\n            color: 'primary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: 'text.primary'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: 1,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary'\n          },\n          children: \"\\u5F53\\u524D\\u5DE5\\u4F5C\\u8868:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: worksheet,\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 38\n        }, this),\n        sx: {\n          textAlign: 'left'\n        },\n        children: \"\\u8BF7\\u8BBE\\u7F6E\\u6570\\u636E\\u5904\\u7406\\u7684\\u5217\\u8303\\u56F4\\u548C\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\\u3002\\u5217\\u8303\\u56F4\\u4E3A\\u5FC5\\u586B\\u9879\\uFF0C\\u4F63\\u91D1\\u53C2\\u6570\\u4E3A\\u53EF\\u9009\\u9879\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Slide, {\n      direction: \"up\",\n      in: true,\n      timeout: 800,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3,\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableViewIcon, {\n              sx: {\n                color: 'primary.main',\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600\n              },\n              children: \"\\u6570\\u636E\\u5217\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u5FC5\\u586B\",\n              size: \"small\",\n              color: \"error\",\n              sx: {\n                ml: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                label: \"\\u8D77\\u59CB\\u5217\",\n                value: startCol,\n                onChange: e => {\n                  const value = e.target.value.toUpperCase();\n                  setStartCol(value);\n                  if (errors.startCol) {\n                    setErrors(prev => ({\n                      ...prev,\n                      startCol: ''\n                    }));\n                  }\n                },\n                placeholder: \"\\u4F8B\\u5982: AT\",\n                error: !!errors.startCol,\n                helperText: errors.startCol || \"请输入起始列的字母\",\n                inputProps: {\n                  maxLength: 3\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 2\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                label: \"\\u7ED3\\u675F\\u5217\",\n                value: endCol,\n                onChange: e => {\n                  const value = e.target.value.toUpperCase();\n                  setEndCol(value);\n                  if (errors.endCol) {\n                    setErrors(prev => ({\n                      ...prev,\n                      endCol: ''\n                    }));\n                  }\n                },\n                placeholder: \"\\u4F8B\\u5982: BP\",\n                error: !!errors.endCol,\n                helperText: errors.endCol || \"请输入结束列的字母\",\n                inputProps: {\n                  maxLength: 3\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 2\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Slide, {\n      direction: \"up\",\n      in: true,\n      timeout: 1000,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 4,\n          background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,\n          border: `1px solid ${alpha(theme.palette.secondary.main, 0.1)}`\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(MonetizationOnIcon, {\n              sx: {\n                color: 'secondary.main',\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600\n              },\n              children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u53C2\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u53EF\\u9009\",\n              size: \"small\",\n              color: \"success\",\n              sx: {\n                ml: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"\\u5C0F\\u65F6\\u8D39\\u7387\",\n                type: \"number\",\n                value: perHourRate,\n                onChange: e => {\n                  setPerHourRate(e.target.value);\n                  if (errors.perHourRate) {\n                    setErrors(prev => ({\n                      ...prev,\n                      perHourRate: ''\n                    }));\n                  }\n                },\n                placeholder: \"\\u4F8B\\u5982: 65\",\n                error: !!errors.perHourRate,\n                helperText: errors.perHourRate || \"每小时费率 (RM)\",\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: \"RM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 37\n                  }, this)\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 2\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"CBU CAR\\u5C0F\\u65F6\\u8D39\\u7387\",\n                type: \"number\",\n                value: cbuCarHourRate,\n                onChange: e => {\n                  setCbuCarHourRate(e.target.value);\n                  if (errors.cbuCarHourRate) {\n                    setErrors(prev => ({\n                      ...prev,\n                      cbuCarHourRate: ''\n                    }));\n                  }\n                },\n                placeholder: \"\\u4F8B\\u5982: 80\",\n                error: !!errors.cbuCarHourRate,\n                helperText: errors.cbuCarHourRate || \"CBU CAR每小时费率 (RM)\",\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: \"RM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 37\n                  }, this)\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 2\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u4F63\\u91D1\\u6BD4\\u4F8B\\uFF0C\\u8303\\u56F4\\u57280\\u52301\\u4E4B\\u95F4\\uFF0C\\u4F8B\\u59820.6\\u8868\\u793A60%\",\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"\\u4F63\\u91D1\\u7387\",\n                  type: \"number\",\n                  value: commissionRate,\n                  onChange: e => {\n                    setCommissionRate(e.target.value);\n                    if (errors.commissionRate) {\n                      setErrors(prev => ({\n                        ...prev,\n                        commissionRate: ''\n                      }));\n                    }\n                  },\n                  placeholder: \"\\u4F8B\\u5982: 0.6\",\n                  error: !!errors.commissionRate,\n                  helperText: errors.commissionRate || \"佣金比例 (0-1 之间)\",\n                  inputProps: {\n                    step: \"0.01\",\n                    min: \"0\",\n                    max: \"1\"\n                  },\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 1200,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 24\n          }, this),\n          onClick: onBack,\n          disabled: loading,\n          sx: {\n            borderRadius: 3,\n            px: 3,\n            py: 1.5,\n            fontWeight: 600,\n            borderColor: alpha(theme.palette.primary.main, 0.3),\n            color: 'primary.main',\n            '&:hover': {\n              borderColor: 'primary.main',\n              backgroundColor: alpha(theme.palette.primary.main, 0.05),\n              transform: 'translateX(-4px)'\n            },\n            transition: 'all 0.3s ease'\n          },\n          children: \"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u6B65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          endIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20,\n            color: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 32\n          }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 81\n          }, this),\n          disabled: loading,\n          sx: {\n            borderRadius: 3,\n            px: 4,\n            py: 1.5,\n            fontWeight: 600,\n            background: loading ? 'grey.400' : `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n            '&:hover': {\n              background: loading ? 'grey.400' : `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.secondary.dark} 90%)`,\n              transform: loading ? 'none' : 'translateY(-2px)',\n              boxShadow: loading ? 'none' : '0 8px 25px rgba(0,0,0,0.15)'\n            },\n            transition: 'all 0.3s ease'\n          },\n          children: loading ? '处理中...' : '开始处理'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessForm, \"kQ//XL/uz13OC2yz/0XVZRAHq/4=\", false, function () {\n  return [useTheme];\n});\n_c = ProcessForm;\nexport default ProcessForm;\nvar _c;\n$RefreshReg$(_c, \"ProcessForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "InputAdornment", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Fade", "Slide", "Chip", "<PERSON><PERSON>", "useTheme", "alpha", "<PERSON><PERSON><PERSON>", "ArrowBackIcon", "SendIcon", "SettingsIcon", "InfoIcon", "MonetizationOnIcon", "TableViewIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "STORAGE_KEY", "ProcessForm", "fileId", "worksheet", "onSubmit", "onBack", "onError", "_s", "theme", "startCol", "setStartCol", "savedParams", "localStorage", "getItem", "JSON", "parse", "endCol", "setEndCol", "perHourRate", "setPerHourRate", "cbuCarHourRate", "setCbuCarHourRate", "commissionRate", "setCommissionRate", "loading", "setLoading", "errors", "setErrors", "validateColumn", "value", "colRegex", "test", "validateRate", "fieldName", "isNaN", "parseFloat", "paramsToSave", "setItem", "stringify", "handleSubmit", "e", "preventDefault", "newErrors", "startColError", "endColError", "perHourRateError", "cbuCarHourRateError", "commissionRateError", "Object", "keys", "length", "requestData", "undefined", "response", "post", "data", "success", "error", "_error$response", "_error$response$data", "console", "component", "noValidate", "children", "sx", "textAlign", "mb", "display", "alignItems", "justifyContent", "gap", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "severity", "icon", "direction", "in", "timeout", "background", "palette", "primary", "main", "secondary", "border", "mr", "ml", "container", "spacing", "item", "xs", "sm", "required", "fullWidth", "onChange", "target", "toUpperCase", "prev", "placeholder", "helperText", "inputProps", "max<PERSON><PERSON><PERSON>", "borderRadius", "md", "type", "InputProps", "startAdornment", "position", "title", "step", "min", "max", "startIcon", "onClick", "disabled", "px", "py", "borderColor", "backgroundColor", "transform", "transition", "endIcon", "dark", "boxShadow", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend-old/src/components/ProcessForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  TextField,\n  Button,\n  Grid,\n  InputAdornment,\n  CircularProgress,\n  Card,\n  CardContent,\n  Fade,\n  Slide,\n  Chip,\n  Alert,\n  useTheme,\n  alpha,\n  Tooltip\n} from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SendIcon from '@mui/icons-material/Send';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport InfoIcon from '@mui/icons-material/Info';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport TableViewIcon from '@mui/icons-material/TableView';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// localStorage的键名\nconst STORAGE_KEY = 'processFormParams';\n\nconst ProcessForm = ({ fileId, worksheet, onSubmit, onBack, onError }) => {\n  const theme = useTheme();\n\n  // 从localStorage获取上次保存的参数\n  const [startCol, setStartCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).startCol || '' : '';\n  });\n\n  const [endCol, setEndCol] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).endCol || '' : '';\n  });\n\n  const [perHourRate, setPerHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).perHourRate || '' : '';\n  });\n\n  const [cbuCarHourRate, setCbuCarHourRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).cbuCarHourRate || '' : '';\n  });\n\n  const [commissionRate, setCommissionRate] = useState(() => {\n    const savedParams = localStorage.getItem(STORAGE_KEY);\n    return savedParams ? JSON.parse(savedParams).commissionRate || '' : '';\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // 验证函数\n  const validateColumn = (value) => {\n    const colRegex = /^[A-Za-z]+$/;\n    if (!value) return '请输入列名';\n    if (!colRegex.test(value)) return '列名格式无效，请使用字母 (例如: A, AB, AT)';\n    return '';\n  };\n\n  const validateRate = (value, fieldName) => {\n    if (value && (isNaN(value) || parseFloat(value) < 0)) {\n      return `${fieldName}必须是正数`;\n    }\n    return '';\n  };\n  \n  // 当参数变化时保存到localStorage\n  useEffect(() => {\n    const paramsToSave = {\n      startCol,\n      endCol,\n      perHourRate,\n      cbuCarHourRate,\n      commissionRate\n    };\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(paramsToSave));\n  }, [startCol, endCol, perHourRate, cbuCarHourRate, commissionRate]);\n  \n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // 验证输入\n    const newErrors = {};\n\n    const startColError = validateColumn(startCol);\n    const endColError = validateColumn(endCol);\n    const perHourRateError = validateRate(perHourRate, '小时费率');\n    const cbuCarHourRateError = validateRate(cbuCarHourRate, 'CBU CAR小时费率');\n    const commissionRateError = validateRate(commissionRate, '佣金率');\n\n    if (startColError) newErrors.startCol = startColError;\n    if (endColError) newErrors.endCol = endColError;\n    if (perHourRateError) newErrors.perHourRate = perHourRateError;\n    if (cbuCarHourRateError) newErrors.cbuCarHourRate = cbuCarHourRateError;\n    if (commissionRateError) newErrors.commissionRate = commissionRateError;\n\n    if (commissionRate && (parseFloat(commissionRate) > 1)) {\n      newErrors.commissionRate = '佣金率应在0-1之间';\n    }\n\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      onError('请检查输入的参数');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const requestData = {\n        fileId,\n        worksheet,\n        startCol,\n        endCol,\n        perHourRate: perHourRate || undefined,\n        cbuCarHourRate: cbuCarHourRate || undefined,\n        commissionRate: commissionRate || undefined\n      };\n\n      const response = await axios.post(`${API_URL}/process`, requestData);\n\n      if (response.data && response.data.success) {\n        onSubmit(\n          { startCol, endCol, perHourRate, cbuCarHourRate, commissionRate },\n          response.data.data\n        );\n      } else {\n        onError('处理数据失败，请重试');\n      }\n    } catch (error) {\n      console.error('处理数据出错:', error);\n      onError(error.response?.data?.error || '处理数据失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {/* 标题区域 */}\n      <Box sx={{ textAlign: 'center', mb: 4 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2, gap: 2 }}>\n          <SettingsIcon sx={{ fontSize: 32, color: 'primary.main' }} />\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'text.primary' }}>\n            参数设置\n          </Typography>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>\n          <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n            当前工作表:\n          </Typography>\n          <Chip label={worksheet} size=\"small\" color=\"primary\" variant=\"outlined\" />\n        </Box>\n\n        <Alert severity=\"info\" icon={<InfoIcon />} sx={{ textAlign: 'left' }}>\n          请设置数据处理的列范围和佣金计算参数。列范围为必填项，佣金参数为可选项。\n        </Alert>\n      </Box>\n\n      {/* 列范围设置 */}\n      <Slide direction=\"up\" in timeout={800}>\n        <Card\n          sx={{\n            mb: 3,\n            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,\n            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          }}\n        >\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <TableViewIcon sx={{ color: 'primary.main', mr: 1 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                数据列范围\n              </Typography>\n              <Chip label=\"必填\" size=\"small\" color=\"error\" sx={{ ml: 2 }} />\n            </Box>\n\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"起始列\"\n                  value={startCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setStartCol(value);\n                    if (errors.startCol) {\n                      setErrors(prev => ({ ...prev, startCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: AT\"\n                  error={!!errors.startCol}\n                  helperText={errors.startCol || \"请输入起始列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label=\"结束列\"\n                  value={endCol}\n                  onChange={(e) => {\n                    const value = e.target.value.toUpperCase();\n                    setEndCol(value);\n                    if (errors.endCol) {\n                      setErrors(prev => ({ ...prev, endCol: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: BP\"\n                  error={!!errors.endCol}\n                  helperText={errors.endCol || \"请输入结束列的字母\"}\n                  inputProps={{ maxLength: 3 }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      </Slide>\n\n      {/* 佣金计算参数 */}\n      <Slide direction=\"up\" in timeout={1000}>\n        <Card\n          sx={{\n            mb: 4,\n            background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,\n            border: `1px solid ${alpha(theme.palette.secondary.main, 0.1)}`,\n          }}\n        >\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <MonetizationOnIcon sx={{ color: 'secondary.main', mr: 1 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                佣金计算参数\n              </Typography>\n              <Chip label=\"可选\" size=\"small\" color=\"success\" sx={{ ml: 2 }} />\n            </Box>\n\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  fullWidth\n                  label=\"小时费率\"\n                  type=\"number\"\n                  value={perHourRate}\n                  onChange={(e) => {\n                    setPerHourRate(e.target.value);\n                    if (errors.perHourRate) {\n                      setErrors(prev => ({ ...prev, perHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 65\"\n                  error={!!errors.perHourRate}\n                  helperText={errors.perHourRate || \"每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  fullWidth\n                  label=\"CBU CAR小时费率\"\n                  type=\"number\"\n                  value={cbuCarHourRate}\n                  onChange={(e) => {\n                    setCbuCarHourRate(e.target.value);\n                    if (errors.cbuCarHourRate) {\n                      setErrors(prev => ({ ...prev, cbuCarHourRate: '' }));\n                    }\n                  }}\n                  placeholder=\"例如: 80\"\n                  error={!!errors.cbuCarHourRate}\n                  helperText={errors.cbuCarHourRate || \"CBU CAR每小时费率 (RM)\"}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">RM</InputAdornment>,\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                    },\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6} md={4}>\n                <Tooltip title=\"佣金比例，范围在0到1之间，例如0.6表示60%\">\n                  <TextField\n                    fullWidth\n                    label=\"佣金率\"\n                    type=\"number\"\n                    value={commissionRate}\n                    onChange={(e) => {\n                      setCommissionRate(e.target.value);\n                      if (errors.commissionRate) {\n                        setErrors(prev => ({ ...prev, commissionRate: '' }));\n                      }\n                    }}\n                    placeholder=\"例如: 0.6\"\n                    error={!!errors.commissionRate}\n                    helperText={errors.commissionRate || \"佣金比例 (0-1 之间)\"}\n                    inputProps={{ step: \"0.01\", min: \"0\", max: \"1\" }}\n                    sx={{\n                      '& .MuiOutlinedInput-root': {\n                        borderRadius: 2,\n                      },\n                    }}\n                  />\n                </Tooltip>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      </Slide>\n\n      {/* 操作按钮 */}\n      <Fade in timeout={1200}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ArrowBackIcon />}\n            onClick={onBack}\n            disabled={loading}\n            sx={{\n              borderRadius: 3,\n              px: 3,\n              py: 1.5,\n              fontWeight: 600,\n              borderColor: alpha(theme.palette.primary.main, 0.3),\n              color: 'primary.main',\n              '&:hover': {\n                borderColor: 'primary.main',\n                backgroundColor: alpha(theme.palette.primary.main, 0.05),\n                transform: 'translateX(-4px)',\n              },\n              transition: 'all 0.3s ease',\n            }}\n          >\n            返回上一步\n          </Button>\n\n          <Button\n            type=\"submit\"\n            variant=\"contained\"\n            endIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SendIcon />}\n            disabled={loading}\n            sx={{\n              borderRadius: 3,\n              px: 4,\n              py: 1.5,\n              fontWeight: 600,\n              background: loading\n                ? 'grey.400'\n                : `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n              '&:hover': {\n                background: loading\n                  ? 'grey.400'\n                  : `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.secondary.dark} 90%)`,\n                transform: loading ? 'none' : 'translateY(-2px)',\n                boxShadow: loading ? 'none' : '0 8px 25px rgba(0,0,0,0.15)',\n              },\n              transition: 'all 0.3s ease',\n            }}\n          >\n            {loading ? '处理中...' : '开始处理'}\n          </Button>\n        </Box>\n      </Fade>\n    </Box>\n  );\n};\n\nexport default ProcessForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,mBAAmB;AAEvC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,QAAQ;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,KAAK,GAAGrB,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,MAAM;IAC7C,MAAMuC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACb,WAAW,CAAC;IACrD,OAAOW,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACF,QAAQ,IAAI,EAAE,GAAG,EAAE;EAClE,CAAC,CAAC;EAEF,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,MAAM;IACzC,MAAMuC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACb,WAAW,CAAC;IACrD,OAAOW,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACK,MAAM,IAAI,EAAE,GAAG,EAAE;EAChE,CAAC,CAAC;EAEF,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,MAAM;IACnD,MAAMuC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACb,WAAW,CAAC;IACrD,OAAOW,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACO,WAAW,IAAI,EAAE,GAAG,EAAE;EACrE,CAAC,CAAC;EAEF,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,MAAM;IACzD,MAAMuC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACb,WAAW,CAAC;IACrD,OAAOW,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACS,cAAc,IAAI,EAAE,GAAG,EAAE;EACxE,CAAC,CAAC;EAEF,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,MAAM;IACzD,MAAMuC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACb,WAAW,CAAC;IACrD,OAAOW,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAACW,cAAc,IAAI,EAAE,GAAG,EAAE;EACxE,CAAC,CAAC;EAEF,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMwD,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMC,QAAQ,GAAG,aAAa;IAC9B,IAAI,CAACD,KAAK,EAAE,OAAO,OAAO;IAC1B,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE,OAAO,8BAA8B;IAChE,OAAO,EAAE;EACX,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACH,KAAK,EAAEI,SAAS,KAAK;IACzC,IAAIJ,KAAK,KAAKK,KAAK,CAACL,KAAK,CAAC,IAAIM,UAAU,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;MACpD,OAAO,GAAGI,SAAS,OAAO;IAC5B;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA5D,SAAS,CAAC,MAAM;IACd,MAAM+D,YAAY,GAAG;MACnB3B,QAAQ;MACRO,MAAM;MACNE,WAAW;MACXE,cAAc;MACdE;IACF,CAAC;IACDV,YAAY,CAACyB,OAAO,CAACrC,WAAW,EAAEc,IAAI,CAACwB,SAAS,CAACF,YAAY,CAAC,CAAC;EACjE,CAAC,EAAE,CAAC3B,QAAQ,EAAEO,MAAM,EAAEE,WAAW,EAAEE,cAAc,EAAEE,cAAc,CAAC,CAAC;EAEnE,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,MAAMC,aAAa,GAAGf,cAAc,CAACnB,QAAQ,CAAC;IAC9C,MAAMmC,WAAW,GAAGhB,cAAc,CAACZ,MAAM,CAAC;IAC1C,MAAM6B,gBAAgB,GAAGb,YAAY,CAACd,WAAW,EAAE,MAAM,CAAC;IAC1D,MAAM4B,mBAAmB,GAAGd,YAAY,CAACZ,cAAc,EAAE,aAAa,CAAC;IACvE,MAAM2B,mBAAmB,GAAGf,YAAY,CAACV,cAAc,EAAE,KAAK,CAAC;IAE/D,IAAIqB,aAAa,EAAED,SAAS,CAACjC,QAAQ,GAAGkC,aAAa;IACrD,IAAIC,WAAW,EAAEF,SAAS,CAAC1B,MAAM,GAAG4B,WAAW;IAC/C,IAAIC,gBAAgB,EAAEH,SAAS,CAACxB,WAAW,GAAG2B,gBAAgB;IAC9D,IAAIC,mBAAmB,EAAEJ,SAAS,CAACtB,cAAc,GAAG0B,mBAAmB;IACvE,IAAIC,mBAAmB,EAAEL,SAAS,CAACpB,cAAc,GAAGyB,mBAAmB;IAEvE,IAAIzB,cAAc,IAAKa,UAAU,CAACb,cAAc,CAAC,GAAG,CAAE,EAAE;MACtDoB,SAAS,CAACpB,cAAc,GAAG,YAAY;IACzC;IAEAK,SAAS,CAACe,SAAS,CAAC;IAEpB,IAAIM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACQ,MAAM,GAAG,CAAC,EAAE;MACrC5C,OAAO,CAAC,UAAU,CAAC;MACnB;IACF;IAEAmB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM0B,WAAW,GAAG;QAClBjD,MAAM;QACNC,SAAS;QACTM,QAAQ;QACRO,MAAM;QACNE,WAAW,EAAEA,WAAW,IAAIkC,SAAS;QACrChC,cAAc,EAAEA,cAAc,IAAIgC,SAAS;QAC3C9B,cAAc,EAAEA,cAAc,IAAI8B;MACpC,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,IAAI,CAAC,GAAGzD,OAAO,UAAU,EAAEsD,WAAW,CAAC;MAEpE,IAAIE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CpD,QAAQ,CACN;UAAEK,QAAQ;UAAEO,MAAM;UAAEE,WAAW;UAAEE,cAAc;UAAEE;QAAe,CAAC,EACjE+B,QAAQ,CAACE,IAAI,CAACA,IAChB,CAAC;MACH,CAAC,MAAM;QACLjD,OAAO,CAAC,YAAY,CAAC;MACvB;IACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BnD,OAAO,CAAC,EAAAoD,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,YAAY,CAAC;IACtD,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE1B,OAAA,CAACzB,GAAG;IAACuF,SAAS,EAAC,MAAM;IAACzD,QAAQ,EAAEmC,YAAa;IAACuB,UAAU;IAAAC,QAAA,gBAEtDhE,OAAA,CAACzB,GAAG;MAAC0F,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACtChE,OAAA,CAACzB,GAAG;QAAC0F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,QAAQ;UAAEH,EAAE,EAAE,CAAC;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBAC1FhE,OAAA,CAACP,YAAY;UAACwE,EAAE,EAAE;YAAEO,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D7E,OAAA,CAACxB,UAAU;UAACsG,OAAO,EAAC,IAAI;UAACb,EAAE,EAAE;YAAEc,UAAU,EAAE,GAAG;YAAEN,KAAK,EAAE;UAAe,CAAE;UAAAT,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN7E,OAAA,CAACzB,GAAG;QAAC0F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,QAAQ;UAAEC,GAAG,EAAE,CAAC;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAC1FhE,OAAA,CAACxB,UAAU;UAACsG,OAAO,EAAC,OAAO;UAACb,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAiB,CAAE;UAAAT,QAAA,EAAC;QAE7D;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7E,OAAA,CAACd,IAAI;UAAC8F,KAAK,EAAE5E,SAAU;UAAC6E,IAAI,EAAC,OAAO;UAACR,KAAK,EAAC,SAAS;UAACK,OAAO,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAEN7E,OAAA,CAACb,KAAK;QAAC+F,QAAQ,EAAC,MAAM;QAACC,IAAI,eAAEnF,OAAA,CAACN,QAAQ;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACZ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEtE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN7E,OAAA,CAACf,KAAK;MAACmG,SAAS,EAAC,IAAI;MAACC,EAAE;MAACC,OAAO,EAAE,GAAI;MAAAtB,QAAA,eACpChE,OAAA,CAAClB,IAAI;QACHmF,EAAE,EAAE;UACFE,EAAE,EAAE,CAAC;UACLoB,UAAU,EAAE,2BAA2BlG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQrG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACG,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC,QAAQ;UACvIE,MAAM,EAAE,aAAavG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;QAC7D,CAAE;QAAA1B,QAAA,eAEFhE,OAAA,CAACjB,WAAW;UAAAiF,QAAA,gBACVhE,OAAA,CAACzB,GAAG;YAAC0F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEF,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACxDhE,OAAA,CAACJ,aAAa;cAACqE,EAAE,EAAE;gBAAEQ,KAAK,EAAE,cAAc;gBAAEoB,EAAE,EAAE;cAAE;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD7E,OAAA,CAACxB,UAAU;cAACsG,OAAO,EAAC,IAAI;cAACb,EAAE,EAAE;gBAAEc,UAAU,EAAE;cAAI,CAAE;cAAAf,QAAA,EAAC;YAElD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAACd,IAAI;cAAC8F,KAAK,EAAC,cAAI;cAACC,IAAI,EAAC,OAAO;cAACR,KAAK,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAE6B,EAAE,EAAE;cAAE;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAEN7E,OAAA,CAACrB,IAAI;YAACoH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAhC,QAAA,gBACzBhE,OAAA,CAACrB,IAAI;cAACsH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBhE,OAAA,CAACvB,SAAS;gBACR2H,QAAQ;gBACRC,SAAS;gBACTrB,KAAK,EAAC,oBAAK;gBACXlD,KAAK,EAAEpB,QAAS;gBAChB4F,QAAQ,EAAG7D,CAAC,IAAK;kBACf,MAAMX,KAAK,GAAGW,CAAC,CAAC8D,MAAM,CAACzE,KAAK,CAAC0E,WAAW,CAAC,CAAC;kBAC1C7F,WAAW,CAACmB,KAAK,CAAC;kBAClB,IAAIH,MAAM,CAACjB,QAAQ,EAAE;oBACnBkB,SAAS,CAAC6E,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE/F,QAAQ,EAAE;oBAAG,CAAC,CAAC,CAAC;kBAChD;gBACF,CAAE;gBACFgG,WAAW,EAAC,kBAAQ;gBACpBhD,KAAK,EAAE,CAAC,CAAC/B,MAAM,CAACjB,QAAS;gBACzBiG,UAAU,EAAEhF,MAAM,CAACjB,QAAQ,IAAI,WAAY;gBAC3CkG,UAAU,EAAE;kBAAEC,SAAS,EAAE;gBAAE,CAAE;gBAC7B5C,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1B6C,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP7E,OAAA,CAACrB,IAAI;cAACsH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBhE,OAAA,CAACvB,SAAS;gBACR2H,QAAQ;gBACRC,SAAS;gBACTrB,KAAK,EAAC,oBAAK;gBACXlD,KAAK,EAAEb,MAAO;gBACdqF,QAAQ,EAAG7D,CAAC,IAAK;kBACf,MAAMX,KAAK,GAAGW,CAAC,CAAC8D,MAAM,CAACzE,KAAK,CAAC0E,WAAW,CAAC,CAAC;kBAC1CtF,SAAS,CAACY,KAAK,CAAC;kBAChB,IAAIH,MAAM,CAACV,MAAM,EAAE;oBACjBW,SAAS,CAAC6E,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAExF,MAAM,EAAE;oBAAG,CAAC,CAAC,CAAC;kBAC9C;gBACF,CAAE;gBACFyF,WAAW,EAAC,kBAAQ;gBACpBhD,KAAK,EAAE,CAAC,CAAC/B,MAAM,CAACV,MAAO;gBACvB0F,UAAU,EAAEhF,MAAM,CAACV,MAAM,IAAI,WAAY;gBACzC2F,UAAU,EAAE;kBAAEC,SAAS,EAAE;gBAAE,CAAE;gBAC7B5C,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1B6C,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR7E,OAAA,CAACf,KAAK;MAACmG,SAAS,EAAC,IAAI;MAACC,EAAE;MAACC,OAAO,EAAE,IAAK;MAAAtB,QAAA,eACrChE,OAAA,CAAClB,IAAI;QACHmF,EAAE,EAAE;UACFE,EAAE,EAAE,CAAC;UACLoB,UAAU,EAAE,2BAA2BlG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACG,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC,QAAQrG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQ;UACvIE,MAAM,EAAE,aAAavG,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACG,SAAS,CAACD,IAAI,EAAE,GAAG,CAAC;QAC/D,CAAE;QAAA1B,QAAA,eAEFhE,OAAA,CAACjB,WAAW;UAAAiF,QAAA,gBACVhE,OAAA,CAACzB,GAAG;YAAC0F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEF,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACxDhE,OAAA,CAACL,kBAAkB;cAACsE,EAAE,EAAE;gBAAEQ,KAAK,EAAE,gBAAgB;gBAAEoB,EAAE,EAAE;cAAE;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9D7E,OAAA,CAACxB,UAAU;cAACsG,OAAO,EAAC,IAAI;cAACb,EAAE,EAAE;gBAAEc,UAAU,EAAE;cAAI,CAAE;cAAAf,QAAA,EAAC;YAElD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAACd,IAAI;cAAC8F,KAAK,EAAC,cAAI;cAACC,IAAI,EAAC,OAAO;cAACR,KAAK,EAAC,SAAS;cAACR,EAAE,EAAE;gBAAE6B,EAAE,EAAE;cAAE;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAEN7E,OAAA,CAACrB,IAAI;YAACoH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAhC,QAAA,gBACzBhE,OAAA,CAACrB,IAAI;cAACsH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACY,EAAE,EAAE,CAAE;cAAA/C,QAAA,eAC9BhE,OAAA,CAACvB,SAAS;gBACR4H,SAAS;gBACTrB,KAAK,EAAC,0BAAM;gBACZgC,IAAI,EAAC,QAAQ;gBACblF,KAAK,EAAEX,WAAY;gBACnBmF,QAAQ,EAAG7D,CAAC,IAAK;kBACfrB,cAAc,CAACqB,CAAC,CAAC8D,MAAM,CAACzE,KAAK,CAAC;kBAC9B,IAAIH,MAAM,CAACR,WAAW,EAAE;oBACtBS,SAAS,CAAC6E,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEtF,WAAW,EAAE;oBAAG,CAAC,CAAC,CAAC;kBACnD;gBACF,CAAE;gBACFuF,WAAW,EAAC,kBAAQ;gBACpBhD,KAAK,EAAE,CAAC,CAAC/B,MAAM,CAACR,WAAY;gBAC5BwF,UAAU,EAAEhF,MAAM,CAACR,WAAW,IAAI,YAAa;gBAC/C8F,UAAU,EAAE;kBACVC,cAAc,eAAElH,OAAA,CAACpB,cAAc;oBAACuI,QAAQ,EAAC,OAAO;oBAAAnD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBACrE,CAAE;gBACFZ,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1B6C,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP7E,OAAA,CAACrB,IAAI;cAACsH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACY,EAAE,EAAE,CAAE;cAAA/C,QAAA,eAC9BhE,OAAA,CAACvB,SAAS;gBACR4H,SAAS;gBACTrB,KAAK,EAAC,iCAAa;gBACnBgC,IAAI,EAAC,QAAQ;gBACblF,KAAK,EAAET,cAAe;gBACtBiF,QAAQ,EAAG7D,CAAC,IAAK;kBACfnB,iBAAiB,CAACmB,CAAC,CAAC8D,MAAM,CAACzE,KAAK,CAAC;kBACjC,IAAIH,MAAM,CAACN,cAAc,EAAE;oBACzBO,SAAS,CAAC6E,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEpF,cAAc,EAAE;oBAAG,CAAC,CAAC,CAAC;kBACtD;gBACF,CAAE;gBACFqF,WAAW,EAAC,kBAAQ;gBACpBhD,KAAK,EAAE,CAAC,CAAC/B,MAAM,CAACN,cAAe;gBAC/BsF,UAAU,EAAEhF,MAAM,CAACN,cAAc,IAAI,mBAAoB;gBACzD4F,UAAU,EAAE;kBACVC,cAAc,eAAElH,OAAA,CAACpB,cAAc;oBAACuI,QAAQ,EAAC,OAAO;oBAAAnD,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBACrE,CAAE;gBACFZ,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1B6C,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP7E,OAAA,CAACrB,IAAI;cAACsH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACY,EAAE,EAAE,CAAE;cAAA/C,QAAA,eAC9BhE,OAAA,CAACV,OAAO;gBAAC8H,KAAK,EAAC,0GAA0B;gBAAApD,QAAA,eACvChE,OAAA,CAACvB,SAAS;kBACR4H,SAAS;kBACTrB,KAAK,EAAC,oBAAK;kBACXgC,IAAI,EAAC,QAAQ;kBACblF,KAAK,EAAEP,cAAe;kBACtB+E,QAAQ,EAAG7D,CAAC,IAAK;oBACfjB,iBAAiB,CAACiB,CAAC,CAAC8D,MAAM,CAACzE,KAAK,CAAC;oBACjC,IAAIH,MAAM,CAACJ,cAAc,EAAE;sBACzBK,SAAS,CAAC6E,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAElF,cAAc,EAAE;sBAAG,CAAC,CAAC,CAAC;oBACtD;kBACF,CAAE;kBACFmF,WAAW,EAAC,mBAAS;kBACrBhD,KAAK,EAAE,CAAC,CAAC/B,MAAM,CAACJ,cAAe;kBAC/BoF,UAAU,EAAEhF,MAAM,CAACJ,cAAc,IAAI,eAAgB;kBACrDqF,UAAU,EAAE;oBAAES,IAAI,EAAE,MAAM;oBAAEC,GAAG,EAAE,GAAG;oBAAEC,GAAG,EAAE;kBAAI,CAAE;kBACjDtD,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1B6C,YAAY,EAAE;oBAChB;kBACF;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR7E,OAAA,CAAChB,IAAI;MAACqG,EAAE;MAACC,OAAO,EAAE,IAAK;MAAAtB,QAAA,eACrBhE,OAAA,CAACzB,GAAG;QAAC0F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACpEhE,OAAA,CAACtB,MAAM;UACLoG,OAAO,EAAC,UAAU;UAClB0C,SAAS,eAAExH,OAAA,CAACT,aAAa;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7B4C,OAAO,EAAEnH,MAAO;UAChBoH,QAAQ,EAAEjG,OAAQ;UAClBwC,EAAE,EAAE;YACF6C,YAAY,EAAE,CAAC;YACfa,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,GAAG;YACP7C,UAAU,EAAE,GAAG;YACf8C,WAAW,EAAExI,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;YACnDjB,KAAK,EAAE,cAAc;YACrB,SAAS,EAAE;cACToD,WAAW,EAAE,cAAc;cAC3BC,eAAe,EAAEzI,KAAK,CAACoB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC;cACxDqC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE;UACd,CAAE;UAAAhE,QAAA,EACH;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7E,OAAA,CAACtB,MAAM;UACLsI,IAAI,EAAC,QAAQ;UACblC,OAAO,EAAC,WAAW;UACnBmD,OAAO,EAAExG,OAAO,gBAAGzB,OAAA,CAACnB,gBAAgB;YAACoG,IAAI,EAAE,EAAG;YAACR,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,QAAQ;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjF6C,QAAQ,EAAEjG,OAAQ;UAClBwC,EAAE,EAAE;YACF6C,YAAY,EAAE,CAAC;YACfa,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,GAAG;YACP7C,UAAU,EAAE,GAAG;YACfQ,UAAU,EAAE9D,OAAO,GACf,UAAU,GACV,0BAA0BhB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI,SAASjF,KAAK,CAAC+E,OAAO,CAACG,SAAS,CAACD,IAAI,OAAO;YACpG,SAAS,EAAE;cACTH,UAAU,EAAE9D,OAAO,GACf,UAAU,GACV,0BAA0BhB,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACyC,IAAI,SAASzH,KAAK,CAAC+E,OAAO,CAACG,SAAS,CAACuC,IAAI,OAAO;cACpGH,SAAS,EAAEtG,OAAO,GAAG,MAAM,GAAG,kBAAkB;cAChD0G,SAAS,EAAE1G,OAAO,GAAG,MAAM,GAAG;YAChC,CAAC;YACDuG,UAAU,EAAE;UACd,CAAE;UAAAhE,QAAA,EAEDvC,OAAO,GAAG,QAAQ,GAAG;QAAM;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrE,EAAA,CAnXIN,WAAW;EAAA,QACDd,QAAQ;AAAA;AAAAgJ,EAAA,GADlBlI,WAAW;AAqXjB,eAAeA,WAAW;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}