{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"classes\", \"className\", \"closeAfterTransition\", \"children\", \"container\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"onBackdropClick\", \"onClose\", \"onTransitionEnter\", \"onTransitionExited\", \"open\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport FocusTrap from '../Unstable_TrapFocus';\nimport Portal from '../Portal';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Backdrop from '../Backdrop';\nimport useModal from './useModal';\nimport { getModalUtilityClass } from './modalClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0\n}, !ownerState.open && ownerState.exited && {\n  visibility: 'hidden'\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop',\n  overridesResolver: (props, styles) => {\n    return styles.backdrop;\n  }\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$backdrop, _slotProps$root, _slotProps$backdrop;\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      className,\n      closeAfterTransition = false,\n      children,\n      container,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      onBackdropClick,\n      open,\n      slotProps,\n      slots\n      // eslint-disable-next-line react/prop-types\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const propsWithDefaults = _extends({}, props, {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  });\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal(_extends({}, propsWithDefaults, {\n    rootRef: ref\n  }));\n  const ownerState = _extends({}, propsWithDefaults, {\n    exited\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : ModalRoot;\n  const BackdropSlot = (_ref2 = (_slots$backdrop = slots == null ? void 0 : slots.backdrop) != null ? _slots$backdrop : components.Backdrop) != null ? _ref2 : BackdropComponent;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const backdropSlotProps = (_slotProps$backdrop = slotProps == null ? void 0 : slotProps.backdrop) != null ? _slotProps$backdrop : componentsProps.backdrop;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(className, rootSlotProps == null ? void 0 : rootSlotProps.className, classes == null ? void 0 : classes.root, !ownerState.open && ownerState.exited && (classes == null ? void 0 : classes.hidden))\n  });\n  const backdropProps = useSlotProps({\n    elementType: BackdropSlot,\n    externalSlotProps: backdropSlotProps,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps(_extends({}, otherHandlers, {\n        onClick: e => {\n          if (onBackdropClick) {\n            onBackdropClick(e);\n          }\n          if (otherHandlers != null && otherHandlers.onClick) {\n            otherHandlers.onClick(e);\n          }\n        }\n      }));\n    },\n    className: clsx(backdropSlotProps == null ? void 0 : backdropSlotProps.className, BackdropProps == null ? void 0 : BackdropProps.className, classes == null ? void 0 : classes.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, _extends({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "HTMLElementType", "elementAcceptingRef", "composeClasses", "useSlotProps", "FocusTrap", "Portal", "styled", "useDefaultProps", "Backdrop", "useModal", "getModalUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "open", "exited", "classes", "slots", "root", "backdrop", "ModalRoot", "name", "slot", "overridesResolver", "props", "styles", "hidden", "theme", "position", "zIndex", "vars", "modal", "right", "bottom", "top", "left", "visibility", "ModalBackdrop", "Modal", "forwardRef", "inProps", "ref", "_ref", "_slots$root", "_ref2", "_slots$backdrop", "_slotProps$root", "_slotProps$backdrop", "BackdropComponent", "BackdropProps", "className", "closeAfterTransition", "children", "container", "component", "components", "componentsProps", "disableAutoFocus", "disableEnforceFocus", "disableEscapeKeyDown", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "keepMounted", "onBackdropClick", "slotProps", "other", "propsWithDefaults", "getRootProps", "getBackdropProps", "getTransitionProps", "portalRef", "isTopModal", "hasTransition", "rootRef", "childProps", "tabIndex", "undefined", "onEnter", "onExited", "RootSlot", "Root", "BackdropSlot", "rootSlotProps", "backdropSlotProps", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "getSlotProps", "additionalProps", "as", "backdropProps", "otherHandlers", "onClick", "e", "isEnabled", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "string", "bool", "shape", "oneOfType", "func", "onClose", "onTransitionEnter", "onTransitionExited", "sx", "arrayOf"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/node_modules/@mui/material/Modal/Modal.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"classes\", \"className\", \"closeAfterTransition\", \"children\", \"container\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"onBackdropClick\", \"onClose\", \"onTransitionEnter\", \"onTransitionExited\", \"open\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport FocusTrap from '../Unstable_TrapFocus';\nimport Portal from '../Portal';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Backdrop from '../Backdrop';\nimport useModal from './useModal';\nimport { getModalUtilityClass } from './modalClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0\n}, !ownerState.open && ownerState.exited && {\n  visibility: 'hidden'\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop',\n  overridesResolver: (props, styles) => {\n    return styles.backdrop;\n  }\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$backdrop, _slotProps$root, _slotProps$backdrop;\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      className,\n      closeAfterTransition = false,\n      children,\n      container,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      onBackdropClick,\n      open,\n      slotProps,\n      slots\n      // eslint-disable-next-line react/prop-types\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const propsWithDefaults = _extends({}, props, {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  });\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal(_extends({}, propsWithDefaults, {\n    rootRef: ref\n  }));\n  const ownerState = _extends({}, propsWithDefaults, {\n    exited\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : ModalRoot;\n  const BackdropSlot = (_ref2 = (_slots$backdrop = slots == null ? void 0 : slots.backdrop) != null ? _slots$backdrop : components.Backdrop) != null ? _ref2 : BackdropComponent;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const backdropSlotProps = (_slotProps$backdrop = slotProps == null ? void 0 : slotProps.backdrop) != null ? _slotProps$backdrop : componentsProps.backdrop;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(className, rootSlotProps == null ? void 0 : rootSlotProps.className, classes == null ? void 0 : classes.root, !ownerState.open && ownerState.exited && (classes == null ? void 0 : classes.hidden))\n  });\n  const backdropProps = useSlotProps({\n    elementType: BackdropSlot,\n    externalSlotProps: backdropSlotProps,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps(_extends({}, otherHandlers, {\n        onClick: e => {\n          if (onBackdropClick) {\n            onBackdropClick(e);\n          }\n          if (otherHandlers != null && otherHandlers.onClick) {\n            otherHandlers.onClick(e);\n          }\n        }\n      }));\n    },\n    className: clsx(backdropSlotProps == null ? void 0 : backdropSlotProps.className, BackdropProps == null ? void 0 : BackdropProps.className, classes == null ? void 0 : classes.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, _extends({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,mBAAmB,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,sBAAsB,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,eAAe,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,SAAS,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;AAC9b,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC,MAAM;IACNC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACJ,IAAI,IAAIC,MAAM,IAAI,QAAQ,CAAC;IAC3CI,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOpB,cAAc,CAACkB,KAAK,EAAEV,oBAAoB,EAAES,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGjB,MAAM,CAAC,KAAK,EAAE;EAC9BkB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,IAAIU,MAAM,CAACC,MAAM,CAAC;EAC9E;AACF,CAAC,CAAC,CAAC,CAAC;EACFC,KAAK;EACLd;AACF,CAAC,KAAKrB,QAAQ,CAAC;EACboC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEE,MAAM,CAACE,KAAK;EAC1CC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE;AACR,CAAC,EAAE,CAACtB,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,IAAI;EAC1CqB,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAGlC,MAAM,CAACE,QAAQ,EAAE;EACrCgB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,OAAOA,MAAM,CAACN,QAAQ;EACxB;AACF,CAAC,CAAC,CAAC;EACDU,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,KAAK,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,IAAIC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAEC,mBAAmB;EACnF,MAAMvB,KAAK,GAAGpB,eAAe,CAAC;IAC5BiB,IAAI,EAAE,UAAU;IAChBG,KAAK,EAAEgB;EACT,CAAC,CAAC;EACF,MAAM;MACFQ,iBAAiB,GAAGX,aAAa;MACjCY,aAAa;MACbC,SAAS;MACTC,oBAAoB,GAAG,KAAK;MAC5BC,QAAQ;MACRC,SAAS;MACTC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,gBAAgB,GAAG,KAAK;MACxBC,mBAAmB,GAAG,KAAK;MAC3BC,oBAAoB,GAAG,KAAK;MAC5BC,aAAa,GAAG,KAAK;MACrBC,mBAAmB,GAAG,KAAK;MAC3BC,iBAAiB,GAAG,KAAK;MACzBC,YAAY,GAAG,KAAK;MACpBC,WAAW,GAAG,KAAK;MACnBC,eAAe;MACfnD,IAAI;MACJoD,SAAS;MACTjD;MACA;IACF,CAAC,GAAGO,KAAK;IACT2C,KAAK,GAAG5E,6BAA6B,CAACiC,KAAK,EAAE/B,SAAS,CAAC;EACzD,MAAM2E,iBAAiB,GAAG5E,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IAC5C2B,oBAAoB;IACpBM,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,aAAa;IACbC,mBAAmB;IACnBC,iBAAiB;IACjBC,YAAY;IACZC;EACF,CAAC,CAAC;EACF,MAAM;IACJK,YAAY;IACZC,gBAAgB;IAChBC,kBAAkB;IAClBC,SAAS;IACTC,UAAU;IACV1D,MAAM;IACN2D;EACF,CAAC,GAAGpE,QAAQ,CAACd,QAAQ,CAAC,CAAC,CAAC,EAAE4E,iBAAiB,EAAE;IAC3CO,OAAO,EAAElC;EACX,CAAC,CAAC,CAAC;EACH,MAAM5B,UAAU,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAE4E,iBAAiB,EAAE;IACjDrD;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM+D,UAAU,GAAG,CAAC,CAAC;EACrB,IAAIxB,QAAQ,CAAC5B,KAAK,CAACqD,QAAQ,KAAKC,SAAS,EAAE;IACzCF,UAAU,CAACC,QAAQ,GAAG,IAAI;EAC5B;;EAEA;EACA,IAAIH,aAAa,EAAE;IACjB,MAAM;MACJK,OAAO;MACPC;IACF,CAAC,GAAGT,kBAAkB,CAAC,CAAC;IACxBK,UAAU,CAACG,OAAO,GAAGA,OAAO;IAC5BH,UAAU,CAACI,QAAQ,GAAGA,QAAQ;EAChC;EACA,MAAMC,QAAQ,GAAG,CAACvC,IAAI,GAAG,CAACC,WAAW,GAAG1B,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGyB,WAAW,GAAGY,UAAU,CAAC2B,IAAI,KAAK,IAAI,GAAGxC,IAAI,GAAGtB,SAAS;EAChJ,MAAM+D,YAAY,GAAG,CAACvC,KAAK,GAAG,CAACC,eAAe,GAAG5B,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,QAAQ,KAAK,IAAI,GAAG0B,eAAe,GAAGU,UAAU,CAAClD,QAAQ,KAAK,IAAI,GAAGuC,KAAK,GAAGI,iBAAiB;EAC9K,MAAMoC,aAAa,GAAG,CAACtC,eAAe,GAAGoB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAChD,IAAI,KAAK,IAAI,GAAG4B,eAAe,GAAGU,eAAe,CAACtC,IAAI;EACtI,MAAMmE,iBAAiB,GAAG,CAACtC,mBAAmB,GAAGmB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/C,QAAQ,KAAK,IAAI,GAAG4B,mBAAmB,GAAGS,eAAe,CAACrC,QAAQ;EAC1J,MAAMmE,SAAS,GAAGtF,YAAY,CAAC;IAC7BuF,WAAW,EAAEN,QAAQ;IACrBO,iBAAiB,EAAEJ,aAAa;IAChCK,sBAAsB,EAAEtB,KAAK;IAC7BuB,YAAY,EAAErB,YAAY;IAC1BsB,eAAe,EAAE;MACflD,GAAG;MACHmD,EAAE,EAAEtC;IACN,CAAC;IACDzC,UAAU;IACVqC,SAAS,EAAEtD,IAAI,CAACsD,SAAS,EAAEkC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAClC,SAAS,EAAElC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,KAAKC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACU,MAAM,CAAC;EACpN,CAAC,CAAC;EACF,MAAMmE,aAAa,GAAG7F,YAAY,CAAC;IACjCuF,WAAW,EAAEJ,YAAY;IACzBK,iBAAiB,EAAEH,iBAAiB;IACpCM,eAAe,EAAE1C,aAAa;IAC9ByC,YAAY,EAAEI,aAAa,IAAI;MAC7B,OAAOxB,gBAAgB,CAAC9E,QAAQ,CAAC,CAAC,CAAC,EAAEsG,aAAa,EAAE;QAClDC,OAAO,EAAEC,CAAC,IAAI;UACZ,IAAI/B,eAAe,EAAE;YACnBA,eAAe,CAAC+B,CAAC,CAAC;UACpB;UACA,IAAIF,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACC,OAAO,EAAE;YAClDD,aAAa,CAACC,OAAO,CAACC,CAAC,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC;IACD9C,SAAS,EAAEtD,IAAI,CAACyF,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACnC,SAAS,EAAED,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,SAAS,EAAElC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,QAAQ,CAAC;IACxLN;EACF,CAAC,CAAC;EACF,IAAI,CAACmD,WAAW,IAAI,CAAClD,IAAI,KAAK,CAAC4D,aAAa,IAAI3D,MAAM,CAAC,EAAE;IACvD,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,IAAI,CAACP,MAAM,EAAE;IAC/BuC,GAAG,EAAE+B,SAAS;IACdnB,SAAS,EAAEA,SAAS;IACpBO,aAAa,EAAEA,aAAa;IAC5BR,QAAQ,EAAE,aAAazC,KAAK,CAACsE,QAAQ,EAAEzF,QAAQ,CAAC,CAAC,CAAC,EAAE8F,SAAS,EAAE;MAC7DlC,QAAQ,EAAE,CAAC,CAACW,YAAY,IAAIf,iBAAiB,GAAG,aAAavC,IAAI,CAAC0E,YAAY,EAAE3F,QAAQ,CAAC,CAAC,CAAC,EAAEqG,aAAa,CAAC,CAAC,GAAG,IAAI,EAAE,aAAapF,IAAI,CAACR,SAAS,EAAE;QAChJyD,mBAAmB,EAAEA,mBAAmB;QACxCD,gBAAgB,EAAEA,gBAAgB;QAClCI,mBAAmB,EAAEA,mBAAmB;QACxCoC,SAAS,EAAExB,UAAU;QACrB3D,IAAI,EAAEA,IAAI;QACVsC,QAAQ,EAAE,aAAa1D,KAAK,CAACwG,YAAY,CAAC9C,QAAQ,EAAEwB,UAAU;MAChE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/D,KAAK,CAACgE,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtD,iBAAiB,EAAErD,SAAS,CAAC4F,WAAW;EACxC;AACF;AACA;AACA;EACEtC,aAAa,EAAEtD,SAAS,CAAC4G,MAAM;EAC/B;AACF;AACA;EACEnD,QAAQ,EAAEtD,mBAAmB,CAAC0G,UAAU;EACxC;AACF;AACA;EACExF,OAAO,EAAErB,SAAS,CAAC4G,MAAM;EACzB;AACF;AACA;EACErD,SAAS,EAAEvD,SAAS,CAAC8G,MAAM;EAC3B;AACF;AACA;AACA;EACEtD,oBAAoB,EAAExD,SAAS,CAAC+G,IAAI;EACpC;AACF;AACA;AACA;EACEpD,SAAS,EAAE3D,SAAS,CAAC4F,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEhC,UAAU,EAAE5D,SAAS,CAACgH,KAAK,CAAC;IAC1BtG,QAAQ,EAAEV,SAAS,CAAC4F,WAAW;IAC/BL,IAAI,EAAEvF,SAAS,CAAC4F;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,eAAe,EAAE7D,SAAS,CAACgH,KAAK,CAAC;IAC/BxF,QAAQ,EAAExB,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC,CAAC;IACjErF,IAAI,EAAEvB,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElD,SAAS,EAAE1D,SAAS,CAAC,sCAAsCiH,SAAS,CAAC,CAAC/G,eAAe,EAAEF,SAAS,CAACkH,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpD,gBAAgB,EAAE9D,SAAS,CAAC+G,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACEhD,mBAAmB,EAAE/D,SAAS,CAAC+G,IAAI;EACnC;AACF;AACA;AACA;EACE/C,oBAAoB,EAAEhE,SAAS,CAAC+G,IAAI;EACpC;AACF;AACA;AACA;EACE9C,aAAa,EAAEjE,SAAS,CAAC+G,IAAI;EAC7B;AACF;AACA;AACA;AACA;EACE7C,mBAAmB,EAAElE,SAAS,CAAC+G,IAAI;EACnC;AACF;AACA;AACA;EACE5C,iBAAiB,EAAEnE,SAAS,CAAC+G,IAAI;EACjC;AACF;AACA;AACA;EACE3C,YAAY,EAAEpE,SAAS,CAAC+G,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE1C,WAAW,EAAErE,SAAS,CAAC+G,IAAI;EAC3B;AACF;AACA;AACA;EACEzC,eAAe,EAAEtE,SAAS,CAACkH,IAAI;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,OAAO,EAAEnH,SAAS,CAACkH,IAAI;EACvB;AACF;AACA;EACEE,iBAAiB,EAAEpH,SAAS,CAACkH,IAAI;EACjC;AACF;AACA;EACEG,kBAAkB,EAAErH,SAAS,CAACkH,IAAI;EAClC;AACF;AACA;EACE/F,IAAI,EAAEnB,SAAS,CAAC+G,IAAI,CAACF,UAAU;EAC/B;AACF;AACA;AACA;EACEtC,SAAS,EAAEvE,SAAS,CAACgH,KAAK,CAAC;IACzBxF,QAAQ,EAAExB,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC,CAAC;IACjErF,IAAI,EAAEvB,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEtF,KAAK,EAAEtB,SAAS,CAACgH,KAAK,CAAC;IACrBxF,QAAQ,EAAExB,SAAS,CAAC4F,WAAW;IAC/BrE,IAAI,EAAEvB,SAAS,CAAC4F;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE0B,EAAE,EAAEtH,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACuH,OAAO,CAACvH,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,EAAE5G,SAAS,CAAC+G,IAAI,CAAC,CAAC,CAAC,EAAE/G,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}