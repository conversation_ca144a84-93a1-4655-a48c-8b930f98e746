{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Button, Paper, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\", \"None\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError,\n  savedGridData,\n  onDataChange\n}) => {\n  _s();\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n\n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: '',\n      _removed: false\n    };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n\n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n\n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n\n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        return row;\n      });\n\n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      return validatedData;\n    }\n\n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n\n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      // 避免不必要的更新，只有当数据真正变化时才通知父组件\n      // 使用JSON.stringify比较是否有实际变化\n      const currentDataString = JSON.stringify(gridData);\n\n      // 使用ref来存储上一次的数据字符串\n      if (!gridData._lastDataString || gridData._lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n\n        // 保存当前数据字符串，用于下次比较\n        gridData._lastDataString = currentDataString;\n\n        // 通知父组件\n        onDataChange([...gridData]); // 确保传递深拷贝\n      }\n    }\n  }, [gridData, onDataChange]);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n\n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // 显示成功消息\n      setSnackbar({\n        open: true,\n        message: '正在下载Excel文件...',\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    console.log('行数据更新:', newRow);\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n\n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback(option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        const updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return {\n                ...row,\n                REMARKS: '',\n                _selected_remarks: ''\n              };\n            } else {\n              return {\n                ...row,\n                REMARKS: option,\n                _selected_remarks: option\n              };\n            }\n          }\n          return row;\n        });\n\n        // 确保立即保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n        } catch (error) {\n          console.error('保存到localStorage失败:', error);\n        }\n        return updatedData;\n      });\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback(option => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  }, []);\n\n  // 删除行数据\n  const handleRemoveRow = id => {\n    console.log('删除行:', id);\n    const updatedData = gridData.map(row => {\n      if (row.id === id) {\n        return {\n          ...row,\n          _removed: true\n        };\n      }\n      return row;\n    });\n\n    // 重新计算总计\n    const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n\n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n\n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '行已移除并重新编号',\n      severity: 'info'\n    });\n  };\n\n  // 恢复行数据\n  const handleUndoRow = id => {\n    console.log('恢复行:', id);\n    const updatedData = gridData.map(row => {\n      if (row.id === id) {\n        return {\n          ...row,\n          _removed: false\n        };\n      }\n      return row;\n    });\n\n    // 重新计算总计\n    const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed).reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n      totalRow.COMMISSION = newTotal;\n    }\n\n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n\n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n\n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '行已恢复并重新编号',\n      severity: 'success'\n    });\n  };\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n\n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed);\n\n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n\n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks && row._selected_remarks !== 'None' ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n\n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // 显示成功消息\n        setSnackbar({\n          open: true,\n          message: '文档已生成，正在下载...',\n          severity: 'success'\n        });\n\n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({\n        open: true,\n        message: '生成文档失败，请重试',\n        severity: 'error'\n      });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false,\n    headerAlign: 'center'\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true,\n    headerAlign: 'center'\n  }, {\n    field: 'ACTION',\n    headerName: 'ACTION',\n    editable: false,\n    headerAlign: 'center'\n  } // 新添加的ACTION列\n  ];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n\n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n\n          // 如果行被删除，显示灰色Chip\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: params.row._selected_remarks || '',\n              arrow: true,\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: removedRemarkText,\n                color: \"default\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  maxWidth: '100%',\n                  opacity: 0.6,\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this);\n          }\n\n          // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n\n          // 创建普通函数而不是使用useMemo\n          const handleClick = () => {\n            // 使用setTimeout确保点击事件在UI线程之外处理，避免阻塞渲染\n            setTimeout(() => {\n              openRemarksDialog(params.row.id, params.value || '');\n            }, 0);\n          };\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasSelectedRemark ? remarkText : '',\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: remarkText,\n              color: hasSelectedRemark ? 'primary' : 'default',\n              variant: hasSelectedRemark ? 'filled' : 'outlined',\n              size: \"small\",\n              onClick: handleClick,\n              clickable: true,\n              sx: {\n                maxWidth: '100%',\n                cursor: 'pointer',\n                '& .MuiChip-label': {\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  display: 'block'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n\n    // 特殊处理ACTION列\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示ACTION按钮\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n\n          // 如果行已被删除，显示UNDO按钮\n          if (params.row._removed) {\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u6062\\u590D\",\n              color: \"success\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleUndoRow(params.row.id),\n              sx: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this);\n          }\n\n          // 否则显示REMOVE按钮\n          return /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"\\u79FB\\u9664\",\n            color: \"error\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 21\n            }, this),\n            onClick: () => handleRemoveRow(params.row.id),\n            sx: {\n              cursor: 'pointer'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: col.field === 'NO' ? 0.5 : col.field === 'DATE' ? 0.8 : col.field === 'VEHICLE NO' ? 1 : col.field === 'RO NO' ? 0.8 : col.field === 'KM' ? 0.6 : col.field === 'REMARKS' ? 2.5 : col.field === 'MAXCHECK' ? 0.6 : col.field === 'COMMISSION' ? 0.8 : col.field === 'ACTION' ? 0.6 : 1,\n      width: col.field === 'NO' ? 80 : col.field === 'DATE' ? 100 : col.field === 'VEHICLE NO' ? 120 : col.field === 'RO NO' ? 100 : col.field === 'KM' ? 80 : col.field === 'MAXCHECK' ? 80 : col.field === 'COMMISSION' ? 100 : col.field === 'ACTION' ? 90 : col.field === 'REMARKS' ? 250 : 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 如果行被删除，显示灰色文本\n        if (params.row._removed) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.disabled\",\n            sx: {\n              textDecoration: 'line-through'\n            },\n            children: params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 24\n          }, this),\n          onClick: generateDocument,\n          disabled: isGeneratingDocument,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingDocument ? '生成中...' : '生成文档'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 'auto',\n          width: '100%',\n          minHeight: 400\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          autoHeight: true,\n          headerHeight: 56,\n          columnHeaderHeight: 56,\n          getRowClassName: params => {\n            if (params.row.isTotal) return 'total-row';\n            if (params.row._removed) return 'removed-row';\n            return '';\n          },\n          isCellEditable: params => {\n            // 阻止总计行和已删除行被编辑\n            if (params.row.isTotal || params.row._removed) {\n              return false;\n            }\n            return params.colDef.editable;\n          },\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            },\n            '& .removed-row': {\n              backgroundColor: 'rgba(211, 211, 211, 0.3)',\n              color: 'text.disabled'\n            },\n            '& .MuiDataGrid-cell': {\n              whiteSpace: 'normal',\n              lineHeight: 'normal',\n              padding: '8px'\n            },\n            '& .MuiDataGrid-columnHeaders': {\n              backgroundColor: '#f5f5f5'\n            },\n            '& .MuiDataGrid-virtualScroller': {\n              overflowX: 'visible !important'\n            },\n            '& .MuiDataGrid-main': {\n              overflow: 'visible'\n            },\n            '& .MuiDataGrid-root': {\n              overflow: 'visible',\n              border: 'none'\n            },\n            '& .MuiDataGrid-columnHeader': {\n              padding: '0 8px',\n              whiteSpace: 'normal',\n              lineHeight: 'normal'\n            },\n            '& .MuiDataGrid-columnHeaderTitle': {\n              whiteSpace: 'nowrap',\n              overflow: 'visible',\n              lineHeight: '24px',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 822,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      TransitionProps: {\n        unmountOnExit: true\n      },\n      keepMounted: false,\n      disableRestoreFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 901,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 714,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"oEMxTjT6gOVu2ltgc0poQjhpbSo=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Snackbar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "UndoIcon", "PictureAsPdfIcon", "axios", "API_URL", "jsxDEV", "_jsxDEV", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "savedGridData", "onDataChange", "_s", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingDocument", "setIsGeneratingDocument", "originalData", "setOriginalData", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "_removed", "gridData", "setGridData", "console", "log", "length", "validatedData", "undefined", "processedWithIds", "index", "id", "isTotal", "NO", "currentDataString", "_lastDataString", "snackbar", "setSnackbar", "open", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "downloadUrl", "link", "document", "createElement", "href", "setAttribute", "Date", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "handleCleanup", "delete", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "prevData", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "deleteOption", "item", "handleRemoveRow", "nonRemovedRows", "sort", "a", "b", "for<PERSON>ach", "handleUndoRow", "generateDocument", "filteredRows", "docData", "DATE", "split", "Math", "floor", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "docId", "docUrl", "setTimeout", "iframe", "style", "display", "src", "Error", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "headerAlign", "columns", "col", "hasOwnProperty", "flex", "width", "renderCell", "removedRemarkText", "title", "arrow", "placement", "label", "size", "max<PERSON><PERSON><PERSON>", "opacity", "overflow", "textOverflow", "whiteSpace", "remarkText", "hasSelectedRemark", "handleClick", "value", "clickable", "cursor", "icon", "fontWeight", "textDecoration", "Boolean", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "height", "minHeight", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "autoHeight", "headerHeight", "columnHeaderHeight", "getRowClassName", "isCellEditable", "colDef", "experimentalFeatures", "newEditingApi", "backgroundColor", "lineHeight", "padding", "overflowX", "border", "onClose", "fullWidth", "TransitionProps", "unmountOnExit", "keepMounted", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { \n  Box, \n  Typography, \n  Button,\n  Paper,\n  Snackbar,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  TextField,\n  IconButton,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport UndoIcon from '@mui/icons-material/Undo';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { API_URL } from '../config';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\n  \"MAXCHECK ADVANCE\",\n  \"MAXCHECK ADVANCE PLUS\",\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\n  \"MAXCHECK BASIC\",\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\n  \"ATF REPLACEMENT\",\n  \"REPLACE BRAKE PADS\",\n  \"None\"\n];\n\nconst ResultDisplay = ({ data, fileId, onReset, onError, savedGridData, onDataChange }) => {\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);\n  \n  // 存储原始数据，用于恢复被删除的行\n  const [originalData, setOriginalData] = useState([]);\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return { ...row, REMARKS: '', _selected_remarks: '', _removed: false };\n  });\n\n  // 初始化gridData\n  const [gridData, setGridData] = useState(() => {\n    console.log('ResultDisplay初始化，检查savedGridData:', \n      savedGridData ? `有${savedGridData.length}条数据` : '无数据');\n    \n    // 如果有保存的数据，使用保存的数据\n    if (savedGridData && savedGridData.length > 0) {\n      console.log('使用savedGridData初始化');\n      \n      // 确保所有必要的字段都存在\n      const validatedData = savedGridData.map(row => {\n        // 确保_removed字段存在\n        if (row._removed === undefined) {\n          row._removed = false;\n        }\n        \n        // 确保_selected_remarks字段存在\n        if (row._selected_remarks === undefined) {\n          row._selected_remarks = '';\n        }\n        \n        return row;\n      });\n      \n      // 确保设置originalData\n      const processedWithIds = processedData.map((row, index) => ({\n        ...row,\n        id: index,\n        isTotal: row.NO === 'TOTAL'\n      }));\n      setOriginalData([...processedWithIds]);\n      \n      return validatedData;\n    }\n    \n    // 否则使用处理后的数据\n    console.log('使用processedData初始化');\n    const processedWithIds = processedData.map((row, index) => ({\n      ...row,\n      id: index,\n      isTotal: row.NO === 'TOTAL'\n    }));\n    setOriginalData([...processedWithIds]);\n    return processedWithIds;\n  });\n  \n  // 当gridData变化时，通知父组件\n  useEffect(() => {\n    if (onDataChange && gridData.length > 0) {\n      // 避免不必要的更新，只有当数据真正变化时才通知父组件\n      // 使用JSON.stringify比较是否有实际变化\n      const currentDataString = JSON.stringify(gridData);\n      \n      // 使用ref来存储上一次的数据字符串\n      if (!gridData._lastDataString || gridData._lastDataString !== currentDataString) {\n        console.log('ResultDisplay通知App组件数据变化，数据长度:', gridData.length);\n        \n        // 保存当前数据字符串，用于下次比较\n        gridData._lastDataString = currentDataString;\n        \n        // 通知父组件\n        onDataChange([...gridData]); // 确保传递深拷贝\n      }\n    }\n  }, [gridData, onDataChange]);\n  \n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n\n  // 如果没有原始数据但有gridData，设置originalData\n  useEffect(() => {\n    if (originalData.length === 0 && gridData.length > 0) {\n      console.log('设置originalData，数据长度:', gridData.length);\n      setOriginalData([...gridData]);\n    }\n  }, [gridData, originalData]);\n\n  const handleDownload = async () => {\n    try {\n      // 创建一个临时链接元素并模拟点击，确保下载开始\n      const downloadUrl = `${API_URL}/download/${fileId}`;\n      \n      // 创建隐藏的a标签并触发点击\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.setAttribute('download', `processed_excel_${new Date().getTime()}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      // 显示成功消息\n      setSnackbar({ open: true, message: '正在下载Excel文件...', severity: 'success' });\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  \n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    \n    onReset();\n  };\n\n  const handleCellEdit = (params) => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n\n  const processRowUpdate = (newRow) => {\n    console.log('行数据更新:', newRow);\n    // 更新行数据\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\n    \n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData\n          .filter(row => row.NO !== 'TOTAL' && !row._removed)\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        \n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    \n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    \n    setGridData(updatedData);\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\n    return newRow;\n  };\n\n  const onProcessRowUpdateError = (error) => {\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\n  };\n\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({ ...prev, open: false }));\n  };\n\n  // 打开REMARKS选择对话框 - 使用React.useCallback优化性能\n  const openRemarksDialog = React.useCallback((rowId, currentValue) => {\n    // 立即更新状态，不等待下一个渲染周期\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  }, []);\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = useCallback(() => {\n    setRemarksDialog(prev => ({\n      ...prev,\n      open: false\n    }));\n  }, []);\n\n  // 选择REMARKS选项\n  const selectRemarkOption = useCallback((option) => {\n    const { rowId } = remarksDialog;\n    if (rowId !== null) {\n      console.log('选择REMARKS:', option, '行ID:', rowId);\n      setGridData(prevData => {\n        const updatedData = prevData.map(row => {\n          if (row.id === rowId) {\n            // 如果选择了\"None\"，则将_selected_remarks设置为空字符串\n            if (option === \"None\") {\n              return { ...row, REMARKS: '', _selected_remarks: '' };\n            } else {\n              return { ...row, REMARKS: option, _selected_remarks: option };\n            }\n          }\n          return row;\n        });\n        \n        // 确保立即保存到localStorage\n        try {\n          localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n        } catch (error) {\n          console.error('保存到localStorage失败:', error);\n        }\n        \n        return updatedData;\n      });\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\n    }\n    closeRemarksDialog();\n  }, [remarksDialog, closeRemarksDialog]);\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(true);\n  }, []);\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = useCallback(() => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  }, []);\n\n  // 添加新选项\n  const addNewOption = useCallback(() => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      setRemarksOptions(prev => [...prev, newOption.trim()]);\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\n    }\n  }, [newOption, remarksOptions, closeAddOptionDialog]);\n\n  // 删除选项\n  const deleteOption = useCallback((option) => {\n    setRemarksOptions(prev => prev.filter(item => item !== option));\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\n  }, []);\n  \n  // 删除行数据\n  const handleRemoveRow = (id) => {\n    console.log('删除行:', id);\n    const updatedData = gridData.map(row => {\n      if (row.id === id) {\n        return { ...row, _removed: true };\n      }\n      return row;\n    });\n    \n    // 重新计算总计\n    const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = updatedData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n      \n      totalRow.COMMISSION = newTotal;\n    }\n    \n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n    \n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n    \n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    \n    setGridData(updatedData);\n    setSnackbar({ open: true, message: '行已移除并重新编号', severity: 'info' });\n  };\n  \n  // 恢复行数据\n  const handleUndoRow = (id) => {\n    console.log('恢复行:', id);\n    const updatedData = gridData.map(row => {\n      if (row.id === id) {\n        return { ...row, _removed: false };\n      }\n      return row;\n    });\n    \n    // 重新计算总计\n    const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n    if (totalRow) {\n      const newTotal = updatedData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed)\n        .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n      \n      totalRow.COMMISSION = newTotal;\n    }\n    \n    // 重新编号NO字段（只处理数字类型的NO）\n    const nonRemovedRows = updatedData.filter(row => row.NO !== 'TOTAL' && !row._removed && typeof row.NO === 'number');\n    nonRemovedRows.sort((a, b) => a.id - b.id); // 按原始顺序排序\n    \n    // 重新编号\n    nonRemovedRows.forEach((row, index) => {\n      row.NO = index + 1;\n    });\n    \n    // 确保立即保存到localStorage\n    try {\n      localStorage.setItem('savedGridData', JSON.stringify(updatedData));\n    } catch (error) {\n      console.error('保存到localStorage失败:', error);\n    }\n    \n    setGridData(updatedData);\n    setSnackbar({ open: true, message: '行已恢复并重新编号', severity: 'success' });\n  };\n\n  // 生成Word文档\n  const generateDocument = async () => {\n    try {\n      setIsGeneratingDocument(true);\n      \n      // 准备数据，排除总计行和已删除的行\n      const filteredRows = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed);\n      \n      // 按照当前显示的顺序排序（按NO字段）\n      filteredRows.sort((a, b) => {\n        if (typeof a.NO === 'number' && typeof b.NO === 'number') {\n          return a.NO - b.NO;\n        }\n        return 0; // 如果不是数字，保持原顺序\n      });\n      \n      // 映射为文档数据格式\n      const docData = filteredRows.map((row, index) => ({\n        // 使用索引+1作为NO，确保连续性\n        NO: index + 1,\n        DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: (row._selected_remarks && row._selected_remarks !== 'None') ? row._selected_remarks : '',\n        HOURS: typeof row.MAXCHECK === 'number' ? \n          (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \n          row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n      \n      // 计算总金额 - 只计算未删除的行\n      const totalAmount = gridData\n        .filter(row => row.NO !== 'TOTAL' && !row._removed && row.COMMISSION)\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n      \n      // 发送请求到后端生成Word文档\n      const response = await axios.post(`${API_URL}/generate-document`, {\n        data: docData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      \n      if (response.data && response.data.docId) {\n        // 创建一个临时链接元素并模拟点击，确保下载开始\n        const downloadUrl = `${API_URL.split('/api')[0] + response.data.docUrl}`;\n        \n        // 方法1：创建隐藏的a标签并触发点击\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.setAttribute('download', `invoice_${new Date().getTime()}.docx`);\n        link.setAttribute('target', '_blank');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 显示成功消息\n        setSnackbar({ open: true, message: '文档已生成，正在下载...', severity: 'success' });\n        \n        // 备用方法：如果上面的方法不起作用，3秒后尝试iframe方式\n        setTimeout(() => {\n          const iframe = document.createElement('iframe');\n          iframe.style.display = 'none';\n          iframe.src = downloadUrl;\n          document.body.appendChild(iframe);\n          setTimeout(() => {\n            document.body.removeChild(iframe);\n          }, 2000);\n        }, 3000);\n      } else {\n        throw new Error('生成文档失败');\n      }\n    } catch (error) {\n      console.error('生成文档出错:', error);\n      setSnackbar({ open: true, message: '生成文档失败，请重试', severity: 'error' });\n    } finally {\n      setIsGeneratingDocument(false);\n    }\n  };\n  \n  \n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          没有找到数据\n        </Typography>\n        <Button \n          variant=\"contained\" \n          onClick={onReset}\n          sx={{ mt: 2 }}\n        >\n          重新开始\n        </Button>\n      </Box>\n    );\n  }\n  \n  // 定义列的显示顺序和标题\n  const columnOrder = [\n    { field: 'NO', headerName: 'NO', editable: true, headerAlign: 'center' },\n    { field: 'DATE', headerName: 'DATE', editable: true, headerAlign: 'center' },\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true, headerAlign: 'center' },\n    { field: 'RO NO', headerName: 'RO NO', editable: true, headerAlign: 'center' },\n    { field: 'KM', headerName: 'KM', editable: true, headerAlign: 'center' },\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false, headerAlign: 'center' },  // 新添加的REMARKS列\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true, headerAlign: 'center' },\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true, headerAlign: 'center' },\n    { field: 'ACTION', headerName: 'ACTION', editable: false, headerAlign: 'center' }  // 新添加的ACTION列\n  ];\n  \n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field !== 'ACTION' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n    \n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 2.5,\n        width: 250,\n        editable: false,\n        renderCell: (params) => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          \n          // 如果行被删除，显示灰色Chip\n          if (params.row._removed) {\n            const removedRemarkText = params.row._selected_remarks || '无备注';\n            return (\n              <Tooltip title={params.row._selected_remarks || ''} arrow placement=\"top\">\n                <Chip\n                  label={removedRemarkText}\n                  color=\"default\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                  sx={{ \n                    maxWidth: '100%',\n                    opacity: 0.6,\n                    '& .MuiChip-label': {\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      whiteSpace: 'nowrap',\n                      display: 'block'\n                    }\n                  }}\n                />\n              </Tooltip>\n            );\n          }\n          \n          // 特殊处理\"None\"选项，使其显示为\"点击选择\"\n          let remarkText = '点击选择';\n          let hasSelectedRemark = false;\n          \n          if (params.row._selected_remarks && params.row._selected_remarks !== 'None') {\n            remarkText = params.row._selected_remarks;\n            hasSelectedRemark = true;\n          }\n          \n          // 创建普通函数而不是使用useMemo\n          const handleClick = () => {\n            // 使用setTimeout确保点击事件在UI线程之外处理，避免阻塞渲染\n            setTimeout(() => {\n              openRemarksDialog(params.row.id, params.value || '');\n            }, 0);\n          };\n          \n          return (\n            <Tooltip title={hasSelectedRemark ? remarkText : ''} arrow placement=\"top\">\n              <Chip\n                label={remarkText}\n                color={hasSelectedRemark ? 'primary' : 'default'}\n                variant={hasSelectedRemark ? 'filled' : 'outlined'}\n                size=\"small\"\n                onClick={handleClick}\n                clickable\n                sx={{ \n                  maxWidth: '100%',\n                  cursor: 'pointer',\n                  '& .MuiChip-label': {\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    display: 'block'\n                  }\n                }}\n              />\n            </Tooltip>\n          );\n        }\n      };\n    }\n    \n    // 特殊处理ACTION列\n    if (col.field === 'ACTION') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 0.8,\n        width: 100,\n        editable: false,\n        renderCell: (params) => {\n          // 总计行不显示ACTION按钮\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          \n          // 如果行已被删除，显示UNDO按钮\n          if (params.row._removed) {\n            return (\n              <Chip\n                label=\"恢复\"\n                color=\"success\"\n                size=\"small\"\n                icon={<UndoIcon />}\n                onClick={() => handleUndoRow(params.row.id)}\n                sx={{ cursor: 'pointer' }}\n              />\n            );\n          }\n          \n          // 否则显示REMOVE按钮\n          return (\n            <Chip\n              label=\"移除\"\n              color=\"error\"\n              size=\"small\"\n              icon={<DeleteIcon />}\n              onClick={() => handleRemoveRow(params.row.id)}\n              sx={{ cursor: 'pointer' }}\n            />\n          );\n        }\n      };\n    }\n    \n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: col.field === 'NO' ? 0.5 : \n            col.field === 'DATE' ? 0.8 :\n            col.field === 'VEHICLE NO' ? 1 : \n            col.field === 'RO NO' ? 0.8 :\n            col.field === 'KM' ? 0.6 :\n            col.field === 'REMARKS' ? 2.5 : \n            col.field === 'MAXCHECK' ? 0.6 :\n            col.field === 'COMMISSION' ? 0.8 :\n            col.field === 'ACTION' ? 0.6 : 1,\n      width: col.field === 'NO' ? 80 : \n             col.field === 'DATE' ? 100 :\n             col.field === 'VEHICLE NO' ? 120 :\n             col.field === 'RO NO' ? 100 :\n             col.field === 'KM' ? 80 : \n             col.field === 'MAXCHECK' ? 80 : \n             col.field === 'COMMISSION' ? 100 : \n             col.field === 'ACTION' ? 90 : \n             col.field === 'REMARKS' ? 250 : 120,\n      editable: col.editable,\n      renderCell: (params) => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return (\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\n            </Typography>\n          );\n        }\n        \n        // 如果行被删除，显示灰色文本\n        if (params.row._removed) {\n          return (\n            <Typography variant=\"body2\" color=\"text.disabled\" sx={{ textDecoration: 'line-through' }}>\n              {params.value}\n            </Typography>\n          );\n        }\n        \n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n        \n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n        \n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n        \n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n        \n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        \n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n  \n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          处理结果\n        </Typography>\n        \n        <Box>\n          <Button \n            variant=\"contained\"\n            color=\"success\"\n            startIcon={<DownloadIcon />}\n            onClick={handleDownload}\n            sx={{ mr: 1 }}\n          >\n            下载Excel\n          </Button>\n          \n          <Button \n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<PictureAsPdfIcon />}\n            onClick={generateDocument}\n            disabled={isGeneratingDocument}\n            sx={{ mr: 1 }}\n          >\n            {isGeneratingDocument ? '生成中...' : '生成文档'}\n          </Button>\n          \n          <Button \n            variant=\"outlined\" \n            startIcon={<RestartAltIcon />}\n            onClick={handleCleanup}\n          >\n            重新开始\n          </Button>\n        </Box>\n      </Box>\n      \n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\n        <Box sx={{ height: 'auto', width: '100%', minHeight: 400 }}>\n          <DataGrid\n            rows={gridData}\n            columns={columns}\n            pageSize={10}\n            rowsPerPageOptions={[10, 25, 50]}\n            disableSelectionOnClick\n            autoHeight\n            headerHeight={56}\n            columnHeaderHeight={56}\n            getRowClassName={(params) => {\n              if (params.row.isTotal) return 'total-row';\n              if (params.row._removed) return 'removed-row';\n              return '';\n            }}\n            isCellEditable={(params) => {\n              // 阻止总计行和已删除行被编辑\n              if (params.row.isTotal || params.row._removed) {\n                return false;\n              }\n              return params.colDef.editable;\n            }}\n            processRowUpdate={processRowUpdate}\n            onProcessRowUpdateError={onProcessRowUpdateError}\n            experimentalFeatures={{ newEditingApi: true }}\n            sx={{\n              '& .total-row': {\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                fontWeight: 'bold',\n              },\n              '& .removed-row': {\n                backgroundColor: 'rgba(211, 211, 211, 0.3)',\n                color: 'text.disabled',\n              },\n              '& .MuiDataGrid-cell': {\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n                padding: '8px',\n              },\n              '& .MuiDataGrid-columnHeaders': {\n                backgroundColor: '#f5f5f5',\n              },\n              '& .MuiDataGrid-virtualScroller': {\n                overflowX: 'visible !important',\n              },\n              '& .MuiDataGrid-main': {\n                overflow: 'visible',\n              },\n              '& .MuiDataGrid-root': {\n                overflow: 'visible',\n                border: 'none',\n              },\n              '& .MuiDataGrid-columnHeader': {\n                padding: '0 8px',\n                whiteSpace: 'normal',\n                lineHeight: 'normal',\n              },\n              '& .MuiDataGrid-columnHeaderTitle': {\n                whiteSpace: 'nowrap',\n                overflow: 'visible',\n                lineHeight: '24px',\n                fontWeight: 'bold',\n              },\n            }}\n          />\n        </Box>\n      </Paper>\n      \n      {/* REMARKS选择对话框 */}\n      <Dialog \n        open={remarksDialog.open} \n        onClose={closeRemarksDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\">选择REMARKS</Typography>\n            <Button \n              startIcon={<AddIcon />}\n              onClick={openAddOptionDialog}\n              color=\"primary\"\n            >\n              添加选项\n            </Button>\n          </Box>\n        </DialogTitle>\n        <DialogContent dividers>\n          <List>\n            {remarksOptions.map((option) => (\n              <ListItem \n                key={option} \n                disablePadding\n                secondaryAction={\n                  <IconButton \n                    edge=\"end\" \n                    aria-label=\"delete\"\n                    onClick={() => deleteOption(option)}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                }\n              >\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\n                  <ListItemText primary={option} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeRemarksDialog}>取消</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* 添加新选项对话框 */}\n      <Dialog\n        open={addOptionDialog}\n        onClose={closeAddOptionDialog}\n        fullWidth\n        maxWidth=\"xs\"\n        TransitionProps={{ unmountOnExit: true }}\n        keepMounted={false}\n        disableRestoreFocus\n      >\n        <DialogTitle>添加新选项</DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"name\"\n            label=\"选项名称\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newOption}\n            onChange={(e) => setNewOption(e.target.value)}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeAddOptionDialog}>取消</Button>\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\n        </DialogActions>\n      </Dialog>\n      \n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,MAAM;IACzD,MAAM4C,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGX,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd4C,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEV,IAAI,CAACW,SAAS,CAAChB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,aAAa,GAAGxB,IAAI,CAACyB,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAC;EACxE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,MAAM;IAC7CmE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAC7C7B,aAAa,GAAG,IAAIA,aAAa,CAAC8B,MAAM,KAAK,GAAG,KAAK,CAAC;;IAExD;IACA,IAAI9B,aAAa,IAAIA,aAAa,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAC7CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAME,aAAa,GAAG/B,aAAa,CAACqB,GAAG,CAACC,GAAG,IAAI;QAC7C;QACA,IAAIA,GAAG,CAACG,QAAQ,KAAKO,SAAS,EAAE;UAC9BV,GAAG,CAACG,QAAQ,GAAG,KAAK;QACtB;;QAEA;QACA,IAAIH,GAAG,CAACE,iBAAiB,KAAKQ,SAAS,EAAE;UACvCV,GAAG,CAACE,iBAAiB,GAAG,EAAE;QAC5B;QAEA,OAAOF,GAAG;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMW,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAC1D,GAAGZ,GAAG;QACNa,EAAE,EAAED,KAAK;QACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;MACtB,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;MAEtC,OAAOF,aAAa;IACtB;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMI,gBAAgB,GAAGb,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;MAC1D,GAAGZ,GAAG;MACNa,EAAE,EAAED,KAAK;MACTE,OAAO,EAAEd,GAAG,CAACe,EAAE,KAAK;IACtB,CAAC,CAAC,CAAC;IACHpB,eAAe,CAAC,CAAC,GAAGgB,gBAAgB,CAAC,CAAC;IACtC,OAAOA,gBAAgB;EACzB,CAAC,CAAC;;EAEF;EACAvE,SAAS,CAAC,MAAM;IACd,IAAIuC,YAAY,IAAIyB,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACvC;MACA;MACA,MAAMQ,iBAAiB,GAAG9B,IAAI,CAACW,SAAS,CAACO,QAAQ,CAAC;;MAElD;MACA,IAAI,CAACA,QAAQ,CAACa,eAAe,IAAIb,QAAQ,CAACa,eAAe,KAAKD,iBAAiB,EAAE;QAC/EV,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,QAAQ,CAACI,MAAM,CAAC;;QAE9D;QACAJ,QAAQ,CAACa,eAAe,GAAGD,iBAAiB;;QAE5C;QACArC,YAAY,CAAC,CAAC,GAAGyB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEzB,YAAY,CAAC,CAAC;EAE5B,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IAAEiF,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC;IACjDiF,IAAI,EAAE,KAAK;IACXK,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAtF,SAAS,CAAC,MAAM;IACd,IAAIsD,YAAY,CAACc,MAAM,KAAK,CAAC,IAAIJ,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;MACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACpDb,eAAe,CAAC,CAAC,GAAGS,QAAQ,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEV,YAAY,CAAC,CAAC;EAE5B,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,GAAG3D,OAAO,aAAaM,MAAM,EAAE;;MAEnD;MACA,MAAMsD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;MAC7EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;MAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;MACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;;MAE/B;MACAV,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMgE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMzE,KAAK,CAAC0E,MAAM,CAAC,GAAGzE,OAAO,YAAYM,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEAhE,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMmE,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAAC5C,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAM8B,gBAAgB,GAAIC,MAAM,IAAK;IACnCxC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuC,MAAM,CAAC;IAC7B;IACA,MAAMC,WAAW,GAAG3C,QAAQ,CAACL,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACa,EAAE,KAAKiC,MAAM,CAACjC,EAAE,GAAGiC,MAAM,GAAG9C,GAAI,CAAC;;IAE9E;IACA,IAAI8C,MAAM,CAACE,UAAU,KAAKtC,SAAS,EAAE;MACnC,MAAMuC,QAAQ,GAAGF,WAAW,CAACG,IAAI,CAAClD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAIkC,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGJ,WAAW,CACzBK,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDkD,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAItD,GAAG,CAACgD,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDC,QAAQ,CAACD,UAAU,GAAGG,QAAQ;MAChC;IACF;;IAEA;IACA,IAAI;MACFnE,YAAY,CAACY,OAAO,CAAC,eAAe,EAAEV,IAAI,CAACW,SAAS,CAACkD,WAAW,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;IAEAnC,WAAW,CAAC0C,WAAW,CAAC;IACxB5B,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOwB,MAAM;EACf,CAAC;EAED,MAAMS,uBAAuB,GAAIf,KAAK,IAAK;IACzCrB,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,SAASmB,KAAK,CAACnB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;IAChCrC,WAAW,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMsC,iBAAiB,GAAGxH,KAAK,CAACG,WAAW,CAAC,CAACoF,KAAK,EAAEC,YAAY,KAAK;IACnE;IACAF,gBAAgB,CAAC;MACfJ,IAAI,EAAE,IAAI;MACVK,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiC,kBAAkB,GAAGtH,WAAW,CAAC,MAAM;IAC3CmF,gBAAgB,CAACiC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPrC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwC,kBAAkB,GAAGvH,WAAW,CAAEwH,MAAM,IAAK;IACjD,MAAM;MAAEpC;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClBnB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsD,MAAM,EAAE,MAAM,EAAEpC,KAAK,CAAC;MAChDpB,WAAW,CAACyD,QAAQ,IAAI;QACtB,MAAMf,WAAW,GAAGe,QAAQ,CAAC/D,GAAG,CAACC,GAAG,IAAI;UACtC,IAAIA,GAAG,CAACa,EAAE,KAAKY,KAAK,EAAE;YACpB;YACA,IAAIoC,MAAM,KAAK,MAAM,EAAE;cACrB,OAAO;gBAAE,GAAG7D,GAAG;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,iBAAiB,EAAE;cAAG,CAAC;YACvD,CAAC,MAAM;cACL,OAAO;gBAAE,GAAGF,GAAG;gBAAEC,OAAO,EAAE4D,MAAM;gBAAE3D,iBAAiB,EAAE2D;cAAO,CAAC;YAC/D;UACF;UACA,OAAO7D,GAAG;QACZ,CAAC,CAAC;;QAEF;QACA,IAAI;UACFhB,YAAY,CAACY,OAAO,CAAC,eAAe,EAAEV,IAAI,CAACW,SAAS,CAACkD,WAAW,CAAC,CAAC;QACpE,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdlC,OAAO,CAACkC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC5C;QAEA,OAAOO,WAAW;MACpB,CAAC,CAAC;MACF5B,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACAqC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACpC,aAAa,EAAEoC,kBAAkB,CAAC,CAAC;;EAEvC;EACA,MAAMI,mBAAmB,GAAG1H,WAAW,CAAC,MAAM;IAC5CkD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyE,oBAAoB,GAAG3H,WAAW,CAAC,MAAM;IAC7CkD,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4E,YAAY,GAAG5H,WAAW,CAAC,MAAM;IACrC,IAAI+C,SAAS,CAAC8E,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACrF,cAAc,CAACsF,QAAQ,CAAC/E,SAAS,CAAC8E,IAAI,CAAC,CAAC,CAAC,EAAE;MACzEpF,iBAAiB,CAAC2E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAErE,SAAS,CAAC8E,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD/C,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE0C,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAInF,cAAc,CAACsF,QAAQ,CAAC/E,SAAS,CAAC8E,IAAI,CAAC,CAAC,CAAC,EAAE;MACpD/C,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAAClC,SAAS,EAAEP,cAAc,EAAEmF,oBAAoB,CAAC,CAAC;;EAErD;EACA,MAAMI,YAAY,GAAG/H,WAAW,CAAEwH,MAAM,IAAK;IAC3C/E,iBAAiB,CAAC2E,IAAI,IAAIA,IAAI,CAACL,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC,CAAC;IAC/D1C,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgD,eAAe,GAAIzD,EAAE,IAAK;IAC9BP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;IACvB,MAAMkC,WAAW,GAAG3C,QAAQ,CAACL,GAAG,CAACC,GAAG,IAAI;MACtC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;QACjB,OAAO;UAAE,GAAGb,GAAG;UAAEG,QAAQ,EAAE;QAAK,CAAC;MACnC;MACA,OAAOH,GAAG;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMiD,QAAQ,GAAGF,WAAW,CAACG,IAAI,CAAClD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IAC5D,IAAIkC,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGJ,WAAW,CACzBK,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDkD,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAItD,GAAG,CAACgD,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAEvDC,QAAQ,CAACD,UAAU,GAAGG,QAAQ;IAChC;;IAEA;IACA,MAAMoB,cAAc,GAAGxB,WAAW,CAACK,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;IACnHwD,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,EAAE,GAAG6D,CAAC,CAAC7D,EAAE,CAAC,CAAC,CAAC;;IAE5C;IACA0D,cAAc,CAACI,OAAO,CAAC,CAAC3E,GAAG,EAAEY,KAAK,KAAK;MACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI;MACF5B,YAAY,CAACY,OAAO,CAAC,eAAe,EAAEV,IAAI,CAACW,SAAS,CAACkD,WAAW,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;IAEAnC,WAAW,CAAC0C,WAAW,CAAC;IACxB5B,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMsD,aAAa,GAAI/D,EAAE,IAAK;IAC5BP,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEM,EAAE,CAAC;IACvB,MAAMkC,WAAW,GAAG3C,QAAQ,CAACL,GAAG,CAACC,GAAG,IAAI;MACtC,IAAIA,GAAG,CAACa,EAAE,KAAKA,EAAE,EAAE;QACjB,OAAO;UAAE,GAAGb,GAAG;UAAEG,QAAQ,EAAE;QAAM,CAAC;MACpC;MACA,OAAOH,GAAG;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMiD,QAAQ,GAAGF,WAAW,CAACG,IAAI,CAAClD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,CAAC;IAC5D,IAAIkC,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGJ,WAAW,CACzBK,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC,CAClDkD,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAItD,GAAG,CAACgD,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAEvDC,QAAQ,CAACD,UAAU,GAAGG,QAAQ;IAChC;;IAEA;IACA,MAAMoB,cAAc,GAAGxB,WAAW,CAACK,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAI,OAAOH,GAAG,CAACe,EAAE,KAAK,QAAQ,CAAC;IACnHwD,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,EAAE,GAAG6D,CAAC,CAAC7D,EAAE,CAAC,CAAC,CAAC;;IAE5C;IACA0D,cAAc,CAACI,OAAO,CAAC,CAAC3E,GAAG,EAAEY,KAAK,KAAK;MACrCZ,GAAG,CAACe,EAAE,GAAGH,KAAK,GAAG,CAAC;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI;MACF5B,YAAY,CAACY,OAAO,CAAC,eAAe,EAAEV,IAAI,CAACW,SAAS,CAACkD,WAAW,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;IAEAnC,WAAW,CAAC0C,WAAW,CAAC;IACxB5B,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACxE,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpF,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMqF,YAAY,GAAG1E,QAAQ,CAC1BgD,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,CAAC;;MAErD;MACA2E,YAAY,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI,OAAOD,CAAC,CAAC1D,EAAE,KAAK,QAAQ,IAAI,OAAO2D,CAAC,CAAC3D,EAAE,KAAK,QAAQ,EAAE;UACxD,OAAO0D,CAAC,CAAC1D,EAAE,GAAG2D,CAAC,CAAC3D,EAAE;QACpB;QACA,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMgE,OAAO,GAAGD,YAAY,CAAC/E,GAAG,CAAC,CAACC,GAAG,EAAEY,KAAK,MAAM;QAChD;QACAG,EAAE,EAAEH,KAAK,GAAG,CAAC;QACboE,IAAI,EAAEhF,GAAG,CAACgF,IAAI,GAAI,OAAOhF,GAAG,CAACgF,IAAI,KAAK,QAAQ,IAAIhF,GAAG,CAACgF,IAAI,CAACb,QAAQ,CAAC,GAAG,CAAC,GAAGnE,GAAG,CAACgF,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGjF,GAAG,CAACgF,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEhF,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGkF,IAAI,CAACC,KAAK,CAACnF,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFoF,EAAE,EAAE,OAAOpF,GAAG,CAACoF,EAAE,KAAK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACnF,GAAG,CAACoF,EAAE,CAAC,GAAGpF,GAAG,CAACoF,EAAE,IAAI,EAAE;QAClEnF,OAAO,EAAGD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACE,iBAAiB,KAAK,MAAM,GAAIF,GAAG,CAACE,iBAAiB,GAAG,EAAE;QACjGmF,KAAK,EAAE,OAAOrF,GAAG,CAACsF,QAAQ,KAAK,QAAQ,GACpCtF,GAAG,CAACsF,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGtF,GAAG,CAACsF,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGvF,GAAG,CAACsF,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3EvF,GAAG,CAACsF,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAOxF,GAAG,CAACgD,UAAU,KAAK,QAAQ,GAAGhD,GAAG,CAACgD,UAAU,CAACuC,OAAO,CAAC,CAAC,CAAC,GAAGvF,GAAG,CAACgD,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMyC,WAAW,GAAGrF,QAAQ,CACzBgD,MAAM,CAACpD,GAAG,IAAIA,GAAG,CAACe,EAAE,KAAK,OAAO,IAAI,CAACf,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACgD,UAAU,CAAC,CACpEK,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAI,OAAOtD,GAAG,CAACgD,UAAU,KAAK,QAAQ,GAAGhD,GAAG,CAACgD,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAM0C,QAAQ,GAAG,MAAM1H,KAAK,CAAC2H,IAAI,CAAC,GAAG1H,OAAO,oBAAoB,EAAE;QAChEK,IAAI,EAAEyG,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnChH,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAImH,QAAQ,CAACpH,IAAI,IAAIoH,QAAQ,CAACpH,IAAI,CAACsH,KAAK,EAAE;QACxC;QACA,MAAMhE,WAAW,GAAG,GAAG3D,OAAO,CAACgH,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGS,QAAQ,CAACpH,IAAI,CAACuH,MAAM,EAAE;;QAExE;QACA,MAAMhE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;QACvBC,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,WAAW,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;QACrEN,IAAI,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACrCH,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;;QAE/B;QACAV,WAAW,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,eAAe;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC;;QAE1E;QACAwE,UAAU,CAAC,MAAM;UACf,MAAMC,MAAM,GAAGjE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/CgE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7BF,MAAM,CAACG,GAAG,GAAGtE,WAAW;UACxBE,QAAQ,CAACM,IAAI,CAACC,WAAW,CAAC0D,MAAM,CAAC;UACjCD,UAAU,CAAC,MAAM;YACfhE,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACwD,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACvE,CAAC,SAAS;MACR7B,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAGD;EACA,IAAI,CAACW,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACErC,OAAA,CAAC7B,GAAG;MAAC8J,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtCpI,OAAA,CAAC5B,UAAU;QAACiK,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1I,OAAA,CAAC3B,MAAM;QACLgK,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAEtI,OAAQ;QACjB4H,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC5E;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxF;IAAEH,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EAC9E;IAAEH,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACxE;IAAEH,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC;EAAG;EACtF;IAAEH,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACjF;IAAEH,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,EACpF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAS,CAAC,CAAE;EAAA,CACpF;;EAED;EACA,MAAMC,OAAO,GAAGL,WAAW,CAACjH,GAAG,CAACuH,GAAG,IAAI;IACrC,IAAI,CAAClH,QAAQ,CAAC,CAAC,CAAC,CAACmH,cAAc,CAACD,GAAG,CAACL,KAAK,CAAC,IAAIK,GAAG,CAACL,KAAK,KAAK,SAAS,IAAIK,GAAG,CAACL,KAAK,KAAK,QAAQ,IAAIK,GAAG,CAACL,KAAK,KAAK,YAAY,EAAE;MAC7H;MACA,OAAO,IAAI;IACb;;IAEA;IACA,IAAIK,GAAG,CAACL,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;QAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;QAC1BM,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVN,QAAQ,EAAE,KAAK;QACfO,UAAU,EAAG9E,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAAC5C,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;;UAEA;UACA,IAAI6B,MAAM,CAAC5C,GAAG,CAACG,QAAQ,EAAE;YACvB,MAAMwH,iBAAiB,GAAG/E,MAAM,CAAC5C,GAAG,CAACE,iBAAiB,IAAI,KAAK;YAC/D,oBACE/B,OAAA,CAACX,OAAO;cAACoK,KAAK,EAAEhF,MAAM,CAAC5C,GAAG,CAACE,iBAAiB,IAAI,EAAG;cAAC2H,KAAK;cAACC,SAAS,EAAC,KAAK;cAAAvB,QAAA,eACvEpI,OAAA,CAACZ,IAAI;gBACHwK,KAAK,EAAEJ,iBAAkB;gBACzBlB,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBwB,IAAI,EAAC,OAAO;gBACZ5B,EAAE,EAAE;kBACF6B,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,GAAG;kBACZ,kBAAkB,EAAE;oBAClBC,QAAQ,EAAE,QAAQ;oBAClBC,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE,QAAQ;oBACpBpC,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAEd;;UAEA;UACA,IAAIyB,UAAU,GAAG,MAAM;UACvB,IAAIC,iBAAiB,GAAG,KAAK;UAE7B,IAAI3F,MAAM,CAAC5C,GAAG,CAACE,iBAAiB,IAAI0C,MAAM,CAAC5C,GAAG,CAACE,iBAAiB,KAAK,MAAM,EAAE;YAC3EoI,UAAU,GAAG1F,MAAM,CAAC5C,GAAG,CAACE,iBAAiB;YACzCqI,iBAAiB,GAAG,IAAI;UAC1B;;UAEA;UACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;YACxB;YACA1C,UAAU,CAAC,MAAM;cACfpC,iBAAiB,CAACd,MAAM,CAAC5C,GAAG,CAACa,EAAE,EAAE+B,MAAM,CAAC6F,KAAK,IAAI,EAAE,CAAC;YACtD,CAAC,EAAE,CAAC,CAAC;UACP,CAAC;UAED,oBACEtK,OAAA,CAACX,OAAO;YAACoK,KAAK,EAAEW,iBAAiB,GAAGD,UAAU,GAAG,EAAG;YAACT,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAvB,QAAA,eACxEpI,OAAA,CAACZ,IAAI;cACHwK,KAAK,EAAEO,UAAW;cAClB7B,KAAK,EAAE8B,iBAAiB,GAAG,SAAS,GAAG,SAAU;cACjD/B,OAAO,EAAE+B,iBAAiB,GAAG,QAAQ,GAAG,UAAW;cACnDP,IAAI,EAAC,OAAO;cACZlB,OAAO,EAAE0B,WAAY;cACrBE,SAAS;cACTtC,EAAE,EAAE;gBACF6B,QAAQ,EAAE,MAAM;gBAChBU,MAAM,EAAE,SAAS;gBACjB,kBAAkB,EAAE;kBAClBR,QAAQ,EAAE,QAAQ;kBAClBC,YAAY,EAAE,UAAU;kBACxBC,UAAU,EAAE,QAAQ;kBACpBpC,OAAO,EAAE;gBACX;cACF;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAEd;MACF,CAAC;IACH;;IAEA;IACA,IAAIS,GAAG,CAACL,KAAK,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,KAAK,EAAEK,GAAG,CAACL,KAAK;QAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;QAC1BM,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVN,QAAQ,EAAE,KAAK;QACfO,UAAU,EAAG9E,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAAC5C,GAAG,CAACe,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;;UAEA;UACA,IAAI6B,MAAM,CAAC5C,GAAG,CAACG,QAAQ,EAAE;YACvB,oBACEhC,OAAA,CAACZ,IAAI;cACHwK,KAAK,EAAC,cAAI;cACVtB,KAAK,EAAC,SAAS;cACfuB,IAAI,EAAC,OAAO;cACZY,IAAI,eAAEzK,OAAA,CAACL,QAAQ;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBC,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAAChC,MAAM,CAAC5C,GAAG,CAACa,EAAE,CAAE;cAC5CuF,EAAE,EAAE;gBAAEuC,MAAM,EAAE;cAAU;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAEN;;UAEA;UACA,oBACE1I,OAAA,CAACZ,IAAI;YACHwK,KAAK,EAAC,cAAI;YACVtB,KAAK,EAAC,OAAO;YACbuB,IAAI,EAAC,OAAO;YACZY,IAAI,eAAEzK,OAAA,CAACN,UAAU;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBC,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAAC1B,MAAM,CAAC5C,GAAG,CAACa,EAAE,CAAE;YAC9CuF,EAAE,EAAE;cAAEuC,MAAM,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAEN;MACF,CAAC;IACH;IAEA,OAAO;MACLI,KAAK,EAAEK,GAAG,CAACL,KAAK;MAChBC,UAAU,EAAEI,GAAG,CAACJ,UAAU;MAC1BM,IAAI,EAAEF,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,CAAC,GAC9BK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,GAAG,GACxBK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAC7BK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,GAAG,GAC9BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;MACtCQ,KAAK,EAAEH,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,MAAM,GAAG,GAAG,GAC1BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,OAAO,GAAG,GAAG,GAC3BK,GAAG,CAACL,KAAK,KAAK,IAAI,GAAG,EAAE,GACvBK,GAAG,CAACL,KAAK,KAAK,UAAU,GAAG,EAAE,GAC7BK,GAAG,CAACL,KAAK,KAAK,YAAY,GAAG,GAAG,GAChCK,GAAG,CAACL,KAAK,KAAK,QAAQ,GAAG,EAAE,GAC3BK,GAAG,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;MAC1CE,QAAQ,EAAEG,GAAG,CAACH,QAAQ;MACtBO,UAAU,EAAG9E,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAAC5C,GAAG,CAACe,EAAE,KAAK,OAAO,IAAIuG,GAAG,CAACL,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACE9I,OAAA,CAAC5B,UAAU;YAACiK,OAAO,EAAC,OAAO;YAACqC,UAAU,EAAC,MAAM;YAACpC,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAO3D,MAAM,CAAC6F,KAAK,KAAK,QAAQ,GAAG7F,MAAM,CAAC6F,KAAK,CAAClD,OAAO,CAAC,CAAC,CAAC,GAAG3C,MAAM,CAAC6F;UAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIjE,MAAM,CAAC5C,GAAG,CAACG,QAAQ,EAAE;UACvB,oBACEhC,OAAA,CAAC5B,UAAU;YAACiK,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,eAAe;YAACL,EAAE,EAAE;cAAE0C,cAAc,EAAE;YAAe,CAAE;YAAAvC,QAAA,EACtF3D,MAAM,CAAC6F;UAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEjB;;QAEA;QACA,IAAIS,GAAG,CAACL,KAAK,KAAK,MAAM,IAAIrE,MAAM,CAAC6F,KAAK,EAAE;UACxC,OAAO7F,MAAM,CAAC6F,KAAK,CAACxD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAIqC,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAOrE,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOvD,IAAI,CAACC,KAAK,CAACvC,MAAM,CAAC6F,KAAK,CAAC;QACjC;;QAEA;QACA,IAAInB,GAAG,CAACL,KAAK,KAAK,OAAO,IAAI,OAAOrE,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOvD,IAAI,CAACC,KAAK,CAACvC,MAAM,CAAC6F,KAAK,CAAC;QACjC;;QAEA;QACA,IAAInB,GAAG,CAACL,KAAK,KAAK,IAAI,IAAI,OAAOrE,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOvD,IAAI,CAACC,KAAK,CAACvC,MAAM,CAAC6F,KAAK,CAAC;QACjC;;QAEA;QACA,IAAInB,GAAG,CAACL,KAAK,KAAK,UAAU,IAAI,OAAOrE,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAO7F,MAAM,CAAC6F,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG7F,MAAM,CAAC6F,KAAK,CAAClD,OAAO,CAAC,CAAC,CAAC,GAAG3C,MAAM,CAAC6F,KAAK,CAAClD,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAI+B,GAAG,CAACL,KAAK,KAAK,YAAY,IAAI,OAAOrE,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAO7F,MAAM,CAAC6F,KAAK,CAAClD,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAO3C,MAAM,CAAC6F,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAO7F,MAAM,CAAC6F,KAAK;QACrB;QAEA,OAAO7F,MAAM,CAAC6F,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAACrF,MAAM,CAAC2F,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACE5K,OAAA,CAAC7B,GAAG;IAAAiK,QAAA,gBACFpI,OAAA,CAAC7B,GAAG;MAAC8J,EAAE,EAAE;QAAEH,OAAO,EAAE,MAAM;QAAE+C,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3C,QAAA,gBACzFpI,OAAA,CAAC5B,UAAU;QAACiK,OAAO,EAAC,IAAI;QAAC2C,YAAY;QAAA5C,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb1I,OAAA,CAAC7B,GAAG;QAAAiK,QAAA,gBACFpI,OAAA,CAAC3B,MAAM;UACLgK,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,SAAS;UACf2C,SAAS,eAAEjL,OAAA,CAACT,YAAY;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAEnF,cAAe;UACxByE,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1I,OAAA,CAAC3B,MAAM;UACLgK,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,SAAS;UACf2C,SAAS,eAAEjL,OAAA,CAACJ,gBAAgB;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCC,OAAO,EAAEjC,gBAAiB;UAC1ByE,QAAQ,EAAE9J,oBAAqB;UAC/B4G,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAEb/G,oBAAoB,GAAG,QAAQ,GAAG;QAAM;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAET1I,OAAA,CAAC3B,MAAM;UACLgK,OAAO,EAAC,UAAU;UAClB4C,SAAS,eAAEjL,OAAA,CAACR,cAAc;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAErE,aAAc;UAAA8D,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1I,OAAA,CAAC1B,KAAK;MAAC2J,EAAE,EAAE;QAAEqB,KAAK,EAAE,MAAM;QAAEU,QAAQ,EAAE;MAAS,CAAE;MAAA5B,QAAA,eAC/CpI,OAAA,CAAC7B,GAAG;QAAC8J,EAAE,EAAE;UAAEmD,MAAM,EAAE,MAAM;UAAE9B,KAAK,EAAE,MAAM;UAAE+B,SAAS,EAAE;QAAI,CAAE;QAAAjD,QAAA,eACzDpI,OAAA,CAACV,QAAQ;UACPgM,IAAI,EAAErJ,QAAS;UACfiH,OAAO,EAAEA,OAAQ;UACjBqC,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,UAAU;UACVC,YAAY,EAAE,EAAG;UACjBC,kBAAkB,EAAE,EAAG;UACvBC,eAAe,EAAGpH,MAAM,IAAK;YAC3B,IAAIA,MAAM,CAAC5C,GAAG,CAACc,OAAO,EAAE,OAAO,WAAW;YAC1C,IAAI8B,MAAM,CAAC5C,GAAG,CAACG,QAAQ,EAAE,OAAO,aAAa;YAC7C,OAAO,EAAE;UACX,CAAE;UACF8J,cAAc,EAAGrH,MAAM,IAAK;YAC1B;YACA,IAAIA,MAAM,CAAC5C,GAAG,CAACc,OAAO,IAAI8B,MAAM,CAAC5C,GAAG,CAACG,QAAQ,EAAE;cAC7C,OAAO,KAAK;YACd;YACA,OAAOyC,MAAM,CAACsH,MAAM,CAAC/C,QAAQ;UAC/B,CAAE;UACFtE,gBAAgB,EAAEA,gBAAiB;UACnCU,uBAAuB,EAAEA,uBAAwB;UACjD4G,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9ChE,EAAE,EAAE;YACF,cAAc,EAAE;cACdiE,eAAe,EAAE,0BAA0B;cAC3CxB,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBwB,eAAe,EAAE,0BAA0B;cAC3C5D,KAAK,EAAE;YACT,CAAC;YACD,qBAAqB,EAAE;cACrB4B,UAAU,EAAE,QAAQ;cACpBiC,UAAU,EAAE,QAAQ;cACpBC,OAAO,EAAE;YACX,CAAC;YACD,8BAA8B,EAAE;cAC9BF,eAAe,EAAE;YACnB,CAAC;YACD,gCAAgC,EAAE;cAChCG,SAAS,EAAE;YACb,CAAC;YACD,qBAAqB,EAAE;cACrBrC,QAAQ,EAAE;YACZ,CAAC;YACD,qBAAqB,EAAE;cACrBA,QAAQ,EAAE,SAAS;cACnBsC,MAAM,EAAE;YACV,CAAC;YACD,6BAA6B,EAAE;cAC7BF,OAAO,EAAE,OAAO;cAChBlC,UAAU,EAAE,QAAQ;cACpBiC,UAAU,EAAE;YACd,CAAC;YACD,kCAAkC,EAAE;cAClCjC,UAAU,EAAE,QAAQ;cACpBF,QAAQ,EAAE,SAAS;cACnBmC,UAAU,EAAE,MAAM;cAClBzB,UAAU,EAAE;YACd;UACF;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR1I,OAAA,CAACvB,MAAM;MACLwE,IAAI,EAAEG,aAAa,CAACH,IAAK;MACzBsJ,OAAO,EAAE/G,kBAAmB;MAC5BgH,SAAS;MACT1C,QAAQ,EAAC,IAAI;MACb2C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAxE,QAAA,gBAEnBpI,OAAA,CAACtB,WAAW;QAAA0J,QAAA,eACVpI,OAAA,CAAC7B,GAAG;UAAC8J,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAE+C,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA1C,QAAA,gBAClFpI,OAAA,CAAC5B,UAAU;YAACiK,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C1I,OAAA,CAAC3B,MAAM;YACL4M,SAAS,eAAEjL,OAAA,CAACP,OAAO;cAAA8I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBC,OAAO,EAAE/C,mBAAoB;YAC7B0C,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd1I,OAAA,CAACrB,aAAa;QAACkO,QAAQ;QAAAzE,QAAA,eACrBpI,OAAA,CAACnB,IAAI;UAAAuJ,QAAA,EACF1H,cAAc,CAACkB,GAAG,CAAE8D,MAAM,iBACzB1F,OAAA,CAAClB,QAAQ;YAEPgO,cAAc;YACdC,eAAe,eACb/M,OAAA,CAACd,UAAU;cACT8N,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnBrE,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAACP,MAAM,CAAE;cAAA0C,QAAA,eAEpCpI,OAAA,CAACN,UAAU;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAN,QAAA,eAEDpI,OAAA,CAACjB,cAAc;cAAC4J,OAAO,EAAEA,CAAA,KAAMlD,kBAAkB,CAACC,MAAM,CAAE;cAAA0C,QAAA,eACxDpI,OAAA,CAAChB,YAAY;gBAACiO,OAAO,EAAEvH;cAAO;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZhD,MAAM;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB1I,OAAA,CAACpB,aAAa;QAAAwJ,QAAA,eACZpI,OAAA,CAAC3B,MAAM;UAACsK,OAAO,EAAEnD,kBAAmB;UAAA4C,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT1I,OAAA,CAACvB,MAAM;MACLwE,IAAI,EAAE9B,eAAgB;MACtBoL,OAAO,EAAE1G,oBAAqB;MAC9B2G,SAAS;MACT1C,QAAQ,EAAC,IAAI;MACb2C,eAAe,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAE;MACzCC,WAAW,EAAE,KAAM;MACnBC,mBAAmB;MAAAxE,QAAA,gBAEnBpI,OAAA,CAACtB,WAAW;QAAA0J,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC1I,OAAA,CAACrB,aAAa;QAAAyJ,QAAA,eACZpI,OAAA,CAACf,SAAS;UACRiO,SAAS;UACTC,MAAM,EAAC,OAAO;UACdzK,EAAE,EAAC,MAAM;UACTkH,KAAK,EAAC,0BAAM;UACZwD,IAAI,EAAC,MAAM;UACXZ,SAAS;UACTnE,OAAO,EAAC,UAAU;UAClBiC,KAAK,EAAErJ,SAAU;UACjBoM,QAAQ,EAAGC,CAAC,IAAKpM,YAAY,CAACoM,CAAC,CAACC,MAAM,CAACjD,KAAK;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB1I,OAAA,CAACpB,aAAa;QAAAwJ,QAAA,gBACZpI,OAAA,CAAC3B,MAAM;UAACsK,OAAO,EAAE9C,oBAAqB;UAAAuC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD1I,OAAA,CAAC3B,MAAM;UAACsK,OAAO,EAAE7C,YAAa;UAACwC,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET1I,OAAA,CAACzB,QAAQ;MACP0E,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBuK,gBAAgB,EAAE,IAAK;MACvBjB,OAAO,EAAElH,mBAAoB;MAC7BoI,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAvF,QAAA,eAE1DpI,OAAA,CAACxB,KAAK;QAAC+N,OAAO,EAAElH,mBAAoB;QAAClC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAAiF,QAAA,EAC9DrF,QAAQ,CAACG;MAAO;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACjI,EAAA,CAp2BIP,aAAa;AAAA0N,EAAA,GAAb1N,aAAa;AAs2BnB,eAAeA,aAAa;AAAC,IAAA0N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}