{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Code\\\\My Project\\\\commision-calculation\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemButton, ListItemText, TextField, IconButton, CircularProgress } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = 'http://localhost:5000/api';\n\n// 默认的REMARKS选项\nconst DEFAULT_REMARKS_OPTIONS = [\"MAXCHECK ADVANCE\", \"MAXCHECK ADVANCE PLUS\", \"MAXCHECK COMPULSORY 1ST SERVICE\", \"MAXCHECK BASIC\", \"MAXCHECK FUEL FILTER REPLACEMENT\", \"ATF REPLACEMENT\", \"REPLACE BRAKE PADS\"];\nconst ResultDisplay = ({\n  data,\n  fileId,\n  onReset,\n  onError\n}) => {\n  _s();\n  var _gridData$find;\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\n  const [remarksOptions, setRemarksOptions] = useState(() => {\n    const savedOptions = localStorage.getItem('remarksOptions');\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\n  });\n\n  // 新选项输入框的状态\n  const [newOption, setNewOption] = useState('');\n  // 添加新选项对话框的状态\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n  const [pdfDialog, setPdfDialog] = useState({\n    open: false,\n    url: null,\n    error: null\n  });\n\n  // 当remarksOptions变化时，保存到localStorage\n  useEffect(() => {\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\n  }, [remarksOptions]);\n\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\n  const processedData = data.map(row => {\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\n    return {\n      ...row,\n      REMARKS: '',\n      _selected_remarks: ''\n    };\n  });\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\n    ...row,\n    id: index,\n    isTotal: row.NO === 'TOTAL'\n  })));\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [remarksDialog, setRemarksDialog] = useState({\n    open: false,\n    rowId: null,\n    currentValue: ''\n  });\n  const handleDownload = async () => {\n    try {\n      // 使用浏览器的下载功能\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\n    } catch (error) {\n      console.error('下载文件出错:', error);\n      onError('下载文件失败，请重试');\n    }\n  };\n  const handleCleanup = async () => {\n    try {\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\n    } catch (error) {\n      console.error('清理文件出错:', error);\n    }\n    onReset();\n  };\n  const handleCellEdit = params => {\n    // 阻止总计行被编辑\n    if (params.row.NO === 'TOTAL') {\n      return false;\n    }\n    return true;\n  };\n  const processRowUpdate = newRow => {\n    // 更新行数据\n    const updatedData = gridData.map(row => row.id === newRow.id ? newRow : row);\n\n    // 重新计算总计\n    if (newRow.COMMISSION !== undefined) {\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\n      if (totalRow) {\n        const newTotal = updatedData.filter(row => row.NO !== 'TOTAL').reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\n        totalRow.COMMISSION = newTotal;\n      }\n    }\n    setGridData(updatedData);\n    setSnackbar({\n      open: true,\n      message: '数据已更新',\n      severity: 'success'\n    });\n    return newRow;\n  };\n  const onProcessRowUpdateError = error => {\n    setSnackbar({\n      open: true,\n      message: `更新失败: ${error.message}`,\n      severity: 'error'\n    });\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // 打开REMARKS选择对话框\n  const openRemarksDialog = (rowId, currentValue) => {\n    setRemarksDialog({\n      open: true,\n      rowId,\n      currentValue\n    });\n  };\n\n  // 关闭REMARKS选择对话框\n  const closeRemarksDialog = () => {\n    setRemarksDialog({\n      ...remarksDialog,\n      open: false\n    });\n  };\n\n  // 选择REMARKS选项\n  const selectRemarkOption = option => {\n    const {\n      rowId\n    } = remarksDialog;\n    if (rowId !== null) {\n      const updatedData = gridData.map(row => {\n        if (row.id === rowId) {\n          return {\n            ...row,\n            REMARKS: option,\n            _selected_remarks: option\n          };\n        }\n        return row;\n      });\n      setGridData(updatedData);\n      setSnackbar({\n        open: true,\n        message: 'REMARKS已更新',\n        severity: 'success'\n      });\n    }\n    closeRemarksDialog();\n  };\n\n  // 打开添加新选项对话框\n  const openAddOptionDialog = () => {\n    setAddOptionDialog(true);\n  };\n\n  // 关闭添加新选项对话框\n  const closeAddOptionDialog = () => {\n    setAddOptionDialog(false);\n    setNewOption('');\n  };\n\n  // 添加新选项\n  const addNewOption = () => {\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\n      const updatedOptions = [...remarksOptions, newOption.trim()];\n      setRemarksOptions(updatedOptions);\n      setSnackbar({\n        open: true,\n        message: '新选项已添加',\n        severity: 'success'\n      });\n      closeAddOptionDialog();\n    } else if (remarksOptions.includes(newOption.trim())) {\n      setSnackbar({\n        open: true,\n        message: '该选项已存在',\n        severity: 'error'\n      });\n    }\n  };\n\n  // 删除选项\n  const deleteOption = option => {\n    const updatedOptions = remarksOptions.filter(item => item !== option);\n    setRemarksOptions(updatedOptions);\n    setSnackbar({\n      open: true,\n      message: '选项已删除',\n      severity: 'success'\n    });\n  };\n\n  // 生成PDF\n  const generatePDF = async () => {\n    try {\n      setIsGeneratingPDF(true);\n\n      // 准备数据，排除总计行\n      const pdfData = gridData.filter(row => row.NO !== 'TOTAL').map(row => ({\n        NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\n        DATE: row.DATE ? typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE : '',\n        'VEHICLE NO': row['VEHICLE NO'] || '',\n        'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\n        KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\n        REMARKS: row._selected_remarks || '',\n        HOURS: typeof row.MAXCHECK === 'number' ? row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1) : row.MAXCHECK || '',\n        AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\n      }));\n\n      // 计算总金额\n      const totalAmount = gridData.filter(row => row.NO !== 'TOTAL' && row.COMMISSION).reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\n\n      // 发送请求到后端生成PDF\n      const response = await axios.post(`${API_URL}/generate-pdf`, {\n        data: pdfData,\n        totalAmount: totalAmount.toFixed(2),\n        fileId: fileId\n      });\n      if (response.data && response.data.pdfUrl) {\n        setPdfDialog({\n          open: true,\n          url: response.data.pdfUrl,\n          error: null\n        });\n      } else {\n        throw new Error('生成PDF失败');\n      }\n    } catch (error) {\n      console.error('生成PDF出错:', error);\n      setPdfDialog({\n        open: true,\n        url: null,\n        error: '生成PDF失败，请重试'\n      });\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  // 关闭PDF对话框\n  const closePdfDialog = () => {\n    setPdfDialog({\n      ...pdfDialog,\n      open: false\n    });\n  };\n\n  // 如果数据为空\n  if (!gridData || gridData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onReset,\n        sx: {\n          mt: 2\n        },\n        children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 定义列的显示顺序和标题\n  const columnOrder = [{\n    field: 'NO',\n    headerName: 'NO',\n    editable: true\n  }, {\n    field: 'DATE',\n    headerName: 'DATE',\n    editable: true\n  }, {\n    field: 'VEHICLE NO',\n    headerName: 'VEHICLE NO',\n    editable: true\n  }, {\n    field: 'RO NO',\n    headerName: 'RO NO',\n    editable: true\n  }, {\n    field: 'KM',\n    headerName: 'KM',\n    editable: true\n  }, {\n    field: 'REMARKS',\n    headerName: 'REMARKS',\n    editable: false\n  },\n  // 新添加的REMARKS列\n  {\n    field: 'MAXCHECK',\n    headerName: 'HOURS',\n    editable: true\n  }, {\n    field: 'COMMISSION',\n    headerName: 'AMOUNT',\n    editable: true\n  }];\n\n  // 过滤和排序列\n  const columns = columnOrder.map(col => {\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\n      // 如果COMMISSION列不存在，跳过\n      return null;\n    }\n\n    // 特殊处理REMARKS列\n    if (col.field === 'REMARKS') {\n      return {\n        field: col.field,\n        headerName: col.headerName,\n        flex: 1.5,\n        minWidth: 180,\n        editable: false,\n        renderCell: params => {\n          // 总计行不显示REMARKS选项\n          if (params.row.NO === 'TOTAL') {\n            return '';\n          }\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              cursor: 'pointer',\n              '&:hover': {\n                textDecoration: 'underline',\n                color: 'primary.main'\n              }\n            },\n            onClick: () => openRemarksDialog(params.row.id, params.value || ''),\n            children: params.row._selected_remarks || '点击选择'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this);\n        }\n      };\n    }\n    return {\n      field: col.field,\n      headerName: col.headerName,\n      flex: 1,\n      minWidth: 120,\n      editable: col.editable,\n      renderCell: params => {\n        // 特殊处理总计行\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: typeof params.value === 'number' ? params.value.toFixed(2) : params.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this);\n        }\n\n        // 处理日期格式\n        if (col.field === 'DATE' && params.value) {\n          return params.value.split('T')[0]; // 只显示日期部分\n        }\n\n        // NO列不显示浮点数\n        if (col.field === 'NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // RO NO列不显示浮点数\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // KM列不显示浮点数\n        if (col.field === 'KM' && typeof params.value === 'number') {\n          return Math.floor(params.value);\n        }\n\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\n        }\n\n        // COMMISSION(AMOUNT)列保留2位小数\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\n          return params.value.toFixed(2);\n        }\n\n        // 其他数字格式\n        if (typeof params.value === 'number') {\n          return params.value;\n        }\n        return params.value;\n      }\n    };\n  }).filter(Boolean); // 过滤掉null值\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 24\n          }, this),\n          onClick: handleDownload,\n          sx: {\n            mr: 1\n          },\n          children: \"\\u4E0B\\u8F7DExcel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 24\n          }, this),\n          onClick: generatePDF,\n          disabled: isGeneratingPDF,\n          sx: {\n            mr: 1\n          },\n          children: isGeneratingPDF ? '生成中...' : '生成PDF'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RestartAltIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 24\n          }, this),\n          onClick: handleCleanup,\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: gridData,\n          columns: columns,\n          pageSize: 10,\n          rowsPerPageOptions: [10, 25, 50],\n          disableSelectionOnClick: true,\n          getRowClassName: params => params.row.isTotal ? 'total-row' : '',\n          isCellEditable: handleCellEdit,\n          processRowUpdate: processRowUpdate,\n          onProcessRowUpdateError: onProcessRowUpdateError,\n          experimentalFeatures: {\n            newEditingApi: true\n          },\n          sx: {\n            '& .total-row': {\n              backgroundColor: 'rgba(25, 118, 210, 0.08)',\n              fontWeight: 'bold'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this), gridData.some(row => 'COMMISSION' in row) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"\\u4F63\\u91D1\\u8BA1\\u7B97\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u9879\\u76EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u91D1\\u989D (RM)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\u603B\\u4F63\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: ((_gridData$find = gridData.find(row => row.NO === 'TOTAL')) === null || _gridData$find === void 0 ? void 0 : _gridData$find.COMMISSION.toFixed(2)) || '0.00',\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: remarksDialog.open,\n      onClose: closeRemarksDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\u9009\\u62E9REMARKS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 26\n            }, this),\n            onClick: openAddOptionDialog,\n            color: \"primary\",\n            children: \"\\u6DFB\\u52A0\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: remarksOptions.map(option => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              \"aria-label\": \"delete\",\n              onClick: () => deleteOption(option),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => selectRemarkOption(option),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRemarksDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: addOptionDialog,\n      onClose: closeAddOptionDialog,\n      fullWidth: true,\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u6DFB\\u52A0\\u65B0\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"\\u9009\\u9879\\u540D\\u79F0\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newOption,\n          onChange: e => setNewOption(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeAddOptionDialog,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: addNewOption,\n          color: \"primary\",\n          children: \"\\u6DFB\\u52A0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: pdfDialog.open,\n      onClose: closePdfDialog,\n      fullWidth: true,\n      maxWidth: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"PDF\\u9884\\u89C8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: pdfDialog.error ? /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"error\",\n          children: pdfDialog.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this) : pdfDialog.url ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%',\n            height: '70vh'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n            src: pdfDialog.url,\n            width: \"100%\",\n            height: \"100%\",\n            style: {\n              border: 'none'\n            },\n            title: \"PDF\\u9884\\u89C8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [pdfDialog.url && !pdfDialog.error && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => window.open(pdfDialog.url, '_blank'),\n          color: \"primary\",\n          children: \"\\u4E0B\\u8F7DPDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: closePdfDialog,\n          children: \"\\u5173\\u95ED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 401,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"hJMiG+z8fV5ZPKZQQi5IBcrSfvk=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemButton", "ListItemText", "TextField", "IconButton", "CircularProgress", "DataGrid", "DownloadIcon", "RestartAltIcon", "AddIcon", "DeleteIcon", "PictureAsPdfIcon", "axios", "jsxDEV", "_jsxDEV", "API_URL", "DEFAULT_REMARKS_OPTIONS", "ResultDisplay", "data", "fileId", "onReset", "onError", "_s", "_gridData$find", "remarksOptions", "setRemarksOptions", "savedOptions", "localStorage", "getItem", "JSON", "parse", "newOption", "setNewOption", "addOptionDialog", "setAddOptionDialog", "isGeneratingPDF", "setIsGeneratingPDF", "pdfDialog", "setPdfDialog", "open", "url", "error", "setItem", "stringify", "processedData", "map", "row", "REMARKS", "_selected_remarks", "gridData", "setGridData", "index", "id", "isTotal", "NO", "snackbar", "setSnackbar", "message", "severity", "remarksDialog", "setRemarksDialog", "rowId", "currentValue", "handleDownload", "window", "console", "handleCleanup", "delete", "handleCellEdit", "params", "processRowUpdate", "newRow", "updatedData", "COMMISSION", "undefined", "totalRow", "find", "newTotal", "filter", "reduce", "sum", "onProcessRowUpdateError", "handleCloseSnackbar", "prev", "openRemarksDialog", "closeRemarksDialog", "selectRemarkOption", "option", "openAddOptionDialog", "closeAddOptionDialog", "addNewOption", "trim", "includes", "updatedOptions", "deleteOption", "item", "generatePDF", "pdfData", "Math", "floor", "DATE", "split", "KM", "HOURS", "MAXCHECK", "toFixed", "AMOUNT", "totalAmount", "response", "post", "pdfUrl", "Error", "closePdfDialog", "length", "sx", "textAlign", "py", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mt", "columnOrder", "field", "headerName", "editable", "columns", "col", "hasOwnProperty", "flex", "min<PERSON><PERSON><PERSON>", "renderCell", "cursor", "textDecoration", "value", "fontWeight", "Boolean", "display", "justifyContent", "alignItems", "mb", "gutterBottom", "startIcon", "mr", "disabled", "width", "overflow", "height", "rows", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "getRowClassName", "isCellEditable", "experimentalFeatures", "newEditingApi", "backgroundColor", "some", "component", "align", "label", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "dividers", "disablePadding", "secondaryAction", "edge", "primary", "autoFocus", "margin", "type", "onChange", "e", "target", "src", "style", "border", "title", "p", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Code/My Project/commision-calculation/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { \r\n  Box, \r\n  Typography, \r\n  Button,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Chip,\r\n  Snackbar,\r\n  Alert,\r\n  Menu,\r\n  MenuItem,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  List,\r\n  ListItem,\r\n  ListItemButton,\r\n  ListItemText,\r\n  TextField,\r\n  IconButton,\r\n  CircularProgress\r\n} from '@mui/material';\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport DownloadIcon from '@mui/icons-material/Download';\r\nimport RestartAltIcon from '@mui/icons-material/RestartAlt';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\r\nimport axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:5000/api';\r\n\r\n// 默认的REMARKS选项\r\nconst DEFAULT_REMARKS_OPTIONS = [\r\n  \"MAXCHECK ADVANCE\",\r\n  \"MAXCHECK ADVANCE PLUS\",\r\n  \"MAXCHECK COMPULSORY 1ST SERVICE\",\r\n  \"MAXCHECK BASIC\",\r\n  \"MAXCHECK FUEL FILTER REPLACEMENT\",\r\n  \"ATF REPLACEMENT\",\r\n  \"REPLACE BRAKE PADS\"\r\n];\r\n\r\nconst ResultDisplay = ({ data, fileId, onReset, onError }) => {\r\n  // 从localStorage获取REMARKS选项，如果没有则使用默认值\r\n  const [remarksOptions, setRemarksOptions] = useState(() => {\r\n    const savedOptions = localStorage.getItem('remarksOptions');\r\n    return savedOptions ? JSON.parse(savedOptions) : DEFAULT_REMARKS_OPTIONS;\r\n  });\r\n\r\n  // 新选项输入框的状态\r\n  const [newOption, setNewOption] = useState('');\r\n  // 添加新选项对话框的状态\r\n  const [addOptionDialog, setAddOptionDialog] = useState(false);\r\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\r\n  const [pdfDialog, setPdfDialog] = useState({\r\n    open: false,\r\n    url: null,\r\n    error: null\r\n  });\r\n\r\n  // 当remarksOptions变化时，保存到localStorage\r\n  useEffect(() => {\r\n    localStorage.setItem('remarksOptions', JSON.stringify(remarksOptions));\r\n  }, [remarksOptions]);\r\n\r\n  // 处理原始数据，确保包含REMARKS列但不使用后端的值\r\n  const processedData = data.map(row => {\r\n    // 无论后端是否传来REMARKS值，都设置为空字符串，并添加_selected_remarks属性\r\n    return { ...row, REMARKS: '', _selected_remarks: '' };\r\n  });\r\n\r\n  const [gridData, setGridData] = useState(processedData.map((row, index) => ({\r\n    ...row,\r\n    id: index,\r\n    isTotal: row.NO === 'TOTAL'\r\n  })));\r\n  \r\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\r\n  const [remarksDialog, setRemarksDialog] = useState({\r\n    open: false,\r\n    rowId: null,\r\n    currentValue: ''\r\n  });\r\n\r\n  const handleDownload = async () => {\r\n    try {\r\n      // 使用浏览器的下载功能\r\n      window.open(`${API_URL}/download/${fileId}`, '_blank');\r\n    } catch (error) {\r\n      console.error('下载文件出错:', error);\r\n      onError('下载文件失败，请重试');\r\n    }\r\n  };\r\n  \r\n  const handleCleanup = async () => {\r\n    try {\r\n      await axios.delete(`${API_URL}/cleanup/${fileId}`);\r\n    } catch (error) {\r\n      console.error('清理文件出错:', error);\r\n    }\r\n    \r\n    onReset();\r\n  };\r\n\r\n  const handleCellEdit = (params) => {\r\n    // 阻止总计行被编辑\r\n    if (params.row.NO === 'TOTAL') {\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const processRowUpdate = (newRow) => {\r\n    // 更新行数据\r\n    const updatedData = gridData.map(row => (row.id === newRow.id ? newRow : row));\r\n    \r\n    // 重新计算总计\r\n    if (newRow.COMMISSION !== undefined) {\r\n      const totalRow = updatedData.find(row => row.NO === 'TOTAL');\r\n      if (totalRow) {\r\n        const newTotal = updatedData\r\n          .filter(row => row.NO !== 'TOTAL')\r\n          .reduce((sum, row) => sum + (row.COMMISSION || 0), 0);\r\n        \r\n        totalRow.COMMISSION = newTotal;\r\n      }\r\n    }\r\n    \r\n    setGridData(updatedData);\r\n    setSnackbar({ open: true, message: '数据已更新', severity: 'success' });\r\n    return newRow;\r\n  };\r\n\r\n  const onProcessRowUpdateError = (error) => {\r\n    setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });\r\n  };\r\n\r\n  const handleCloseSnackbar = () => {\r\n    setSnackbar(prev => ({ ...prev, open: false }));\r\n  };\r\n\r\n  // 打开REMARKS选择对话框\r\n  const openRemarksDialog = (rowId, currentValue) => {\r\n    setRemarksDialog({\r\n      open: true,\r\n      rowId,\r\n      currentValue\r\n    });\r\n  };\r\n\r\n  // 关闭REMARKS选择对话框\r\n  const closeRemarksDialog = () => {\r\n    setRemarksDialog({\r\n      ...remarksDialog,\r\n      open: false\r\n    });\r\n  };\r\n\r\n  // 选择REMARKS选项\r\n  const selectRemarkOption = (option) => {\r\n    const { rowId } = remarksDialog;\r\n    if (rowId !== null) {\r\n      const updatedData = gridData.map(row => {\r\n        if (row.id === rowId) {\r\n          return { ...row, REMARKS: option, _selected_remarks: option };\r\n        }\r\n        return row;\r\n      });\r\n      setGridData(updatedData);\r\n      setSnackbar({ open: true, message: 'REMARKS已更新', severity: 'success' });\r\n    }\r\n    closeRemarksDialog();\r\n  };\r\n\r\n  // 打开添加新选项对话框\r\n  const openAddOptionDialog = () => {\r\n    setAddOptionDialog(true);\r\n  };\r\n\r\n  // 关闭添加新选项对话框\r\n  const closeAddOptionDialog = () => {\r\n    setAddOptionDialog(false);\r\n    setNewOption('');\r\n  };\r\n\r\n  // 添加新选项\r\n  const addNewOption = () => {\r\n    if (newOption.trim() !== '' && !remarksOptions.includes(newOption.trim())) {\r\n      const updatedOptions = [...remarksOptions, newOption.trim()];\r\n      setRemarksOptions(updatedOptions);\r\n      setSnackbar({ open: true, message: '新选项已添加', severity: 'success' });\r\n      closeAddOptionDialog();\r\n    } else if (remarksOptions.includes(newOption.trim())) {\r\n      setSnackbar({ open: true, message: '该选项已存在', severity: 'error' });\r\n    }\r\n  };\r\n\r\n  // 删除选项\r\n  const deleteOption = (option) => {\r\n    const updatedOptions = remarksOptions.filter(item => item !== option);\r\n    setRemarksOptions(updatedOptions);\r\n    setSnackbar({ open: true, message: '选项已删除', severity: 'success' });\r\n  };\r\n\r\n  // 生成PDF\r\n  const generatePDF = async () => {\r\n    try {\r\n      setIsGeneratingPDF(true);\r\n      \r\n      // 准备数据，排除总计行\r\n      const pdfData = gridData\r\n        .filter(row => row.NO !== 'TOTAL')\r\n        .map(row => ({\r\n          NO: typeof row.NO === 'number' ? Math.floor(row.NO) : row.NO,\r\n          DATE: row.DATE ? (typeof row.DATE === 'string' && row.DATE.includes('T') ? row.DATE.split('T')[0] : row.DATE) : '',\r\n          'VEHICLE NO': row['VEHICLE NO'] || '',\r\n          'RO NO': typeof row['RO NO'] === 'number' ? Math.floor(row['RO NO']) : row['RO NO'] || '',\r\n          KM: typeof row.KM === 'number' ? Math.floor(row.KM) : row.KM || '',\r\n          REMARKS: row._selected_remarks || '',\r\n          HOURS: typeof row.MAXCHECK === 'number' ? \r\n            (row.MAXCHECK % 1 === 0 ? row.MAXCHECK.toFixed(1) : row.MAXCHECK.toFixed(1)) : \r\n            row.MAXCHECK || '',\r\n          AMOUNT: typeof row.COMMISSION === 'number' ? row.COMMISSION.toFixed(2) : row.COMMISSION || ''\r\n        }));\r\n      \r\n      // 计算总金额\r\n      const totalAmount = gridData\r\n        .filter(row => row.NO !== 'TOTAL' && row.COMMISSION)\r\n        .reduce((sum, row) => sum + (typeof row.COMMISSION === 'number' ? row.COMMISSION : 0), 0);\r\n      \r\n      // 发送请求到后端生成PDF\r\n      const response = await axios.post(`${API_URL}/generate-pdf`, {\r\n        data: pdfData,\r\n        totalAmount: totalAmount.toFixed(2),\r\n        fileId: fileId\r\n      });\r\n      \r\n      if (response.data && response.data.pdfUrl) {\r\n        setPdfDialog({\r\n          open: true,\r\n          url: response.data.pdfUrl,\r\n          error: null\r\n        });\r\n      } else {\r\n        throw new Error('生成PDF失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('生成PDF出错:', error);\r\n      setPdfDialog({\r\n        open: true,\r\n        url: null,\r\n        error: '生成PDF失败，请重试'\r\n      });\r\n    } finally {\r\n      setIsGeneratingPDF(false);\r\n    }\r\n  };\r\n  \r\n  // 关闭PDF对话框\r\n  const closePdfDialog = () => {\r\n    setPdfDialog({\r\n      ...pdfDialog,\r\n      open: false\r\n    });\r\n  };\r\n  \r\n  // 如果数据为空\r\n  if (!gridData || gridData.length === 0) {\r\n    return (\r\n      <Box sx={{ textAlign: 'center', py: 3 }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">\r\n          没有找到数据\r\n        </Typography>\r\n        <Button \r\n          variant=\"contained\" \r\n          onClick={onReset}\r\n          sx={{ mt: 2 }}\r\n        >\r\n          重新开始\r\n        </Button>\r\n      </Box>\r\n    );\r\n  }\r\n  \r\n  // 定义列的显示顺序和标题\r\n  const columnOrder = [\r\n    { field: 'NO', headerName: 'NO', editable: true },\r\n    { field: 'DATE', headerName: 'DATE', editable: true },\r\n    { field: 'VEHICLE NO', headerName: 'VEHICLE NO', editable: true },\r\n    { field: 'RO NO', headerName: 'RO NO', editable: true },\r\n    { field: 'KM', headerName: 'KM', editable: true },\r\n    { field: 'REMARKS', headerName: 'REMARKS', editable: false },  // 新添加的REMARKS列\r\n    { field: 'MAXCHECK', headerName: 'HOURS', editable: true },\r\n    { field: 'COMMISSION', headerName: 'AMOUNT', editable: true }\r\n  ];\r\n  \r\n  // 过滤和排序列\r\n  const columns = columnOrder.map(col => {\r\n    if (!gridData[0].hasOwnProperty(col.field) && col.field !== 'REMARKS' && col.field === 'COMMISSION') {\r\n      // 如果COMMISSION列不存在，跳过\r\n      return null;\r\n    }\r\n    \r\n    // 特殊处理REMARKS列\r\n    if (col.field === 'REMARKS') {\r\n      return {\r\n        field: col.field,\r\n        headerName: col.headerName,\r\n        flex: 1.5,\r\n        minWidth: 180,\r\n        editable: false,\r\n        renderCell: (params) => {\r\n          // 总计行不显示REMARKS选项\r\n          if (params.row.NO === 'TOTAL') {\r\n            return '';\r\n          }\r\n          \r\n          return (\r\n            <Box \r\n              sx={{ \r\n                cursor: 'pointer',\r\n                '&:hover': {\r\n                  textDecoration: 'underline',\r\n                  color: 'primary.main'\r\n                }\r\n              }}\r\n              onClick={() => openRemarksDialog(params.row.id, params.value || '')}\r\n            >\r\n              {params.row._selected_remarks || '点击选择'}\r\n            </Box>\r\n          );\r\n        }\r\n      };\r\n    }\r\n    \r\n    return {\r\n      field: col.field,\r\n      headerName: col.headerName,\r\n      flex: 1,\r\n      minWidth: 120,\r\n      editable: col.editable,\r\n      renderCell: (params) => {\r\n        // 特殊处理总计行\r\n        if (params.row.NO === 'TOTAL' && col.field === 'COMMISSION') {\r\n          return (\r\n            <Typography variant=\"body1\" fontWeight=\"bold\" color=\"primary\">\r\n              {typeof params.value === 'number' ? params.value.toFixed(2) : params.value}\r\n            </Typography>\r\n          );\r\n        }\r\n        \r\n        // 处理日期格式\r\n        if (col.field === 'DATE' && params.value) {\r\n          return params.value.split('T')[0]; // 只显示日期部分\r\n        }\r\n        \r\n        // NO列不显示浮点数\r\n        if (col.field === 'NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // RO NO列不显示浮点数\r\n        if (col.field === 'RO NO' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // KM列不显示浮点数\r\n        if (col.field === 'KM' && typeof params.value === 'number') {\r\n          return Math.floor(params.value);\r\n        }\r\n        \r\n        // MAXCHECK(HOURS)列保留1位小数，如果是整数显示x.0\r\n        if (col.field === 'MAXCHECK' && typeof params.value === 'number') {\r\n          return params.value % 1 === 0 ? params.value.toFixed(1) : params.value.toFixed(1);\r\n        }\r\n        \r\n        // COMMISSION(AMOUNT)列保留2位小数\r\n        if (col.field === 'COMMISSION' && typeof params.value === 'number') {\r\n          return params.value.toFixed(2);\r\n        }\r\n        \r\n        // 其他数字格式\r\n        if (typeof params.value === 'number') {\r\n          return params.value;\r\n        }\r\n        \r\n        return params.value;\r\n      }\r\n    };\r\n  }).filter(Boolean); // 过滤掉null值\r\n  \r\n  return (\r\n    <Box>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          处理结果\r\n        </Typography>\r\n        \r\n        <Box>\r\n          <Button \r\n            variant=\"contained\" \r\n            startIcon={<DownloadIcon />}\r\n            onClick={handleDownload}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            下载Excel\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"contained\"\r\n            color=\"secondary\"\r\n            startIcon={<PictureAsPdfIcon />}\r\n            onClick={generatePDF}\r\n            disabled={isGeneratingPDF}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            {isGeneratingPDF ? '生成中...' : '生成PDF'}\r\n          </Button>\r\n          \r\n          <Button \r\n            variant=\"outlined\" \r\n            startIcon={<RestartAltIcon />}\r\n            onClick={handleCleanup}\r\n          >\r\n            重新开始\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n      \r\n      <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n        <Box sx={{ height: 400, width: '100%' }}>\r\n          <DataGrid\r\n            rows={gridData}\r\n            columns={columns}\r\n            pageSize={10}\r\n            rowsPerPageOptions={[10, 25, 50]}\r\n            disableSelectionOnClick\r\n            getRowClassName={(params) => params.row.isTotal ? 'total-row' : ''}\r\n            isCellEditable={handleCellEdit}\r\n            processRowUpdate={processRowUpdate}\r\n            onProcessRowUpdateError={onProcessRowUpdateError}\r\n            experimentalFeatures={{ newEditingApi: true }}\r\n            sx={{\r\n              '& .total-row': {\r\n                backgroundColor: 'rgba(25, 118, 210, 0.08)',\r\n                fontWeight: 'bold',\r\n              },\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n      \r\n      {gridData.some(row => 'COMMISSION' in row) && (\r\n        <Box sx={{ mt: 3 }}>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            佣金计算结果\r\n          </Typography>\r\n          \r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>项目</TableCell>\r\n                  <TableCell align=\"right\">金额 (RM)</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                <TableRow>\r\n                  <TableCell>总佣金</TableCell>\r\n                  <TableCell align=\"right\">\r\n                    <Chip \r\n                      label={gridData.find(row => row.NO === 'TOTAL')?.COMMISSION.toFixed(2) || '0.00'} \r\n                      color=\"primary\" \r\n                      variant=\"outlined\"\r\n                    />\r\n                  </TableCell>\r\n                </TableRow>\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n        </Box>\r\n      )}\r\n      \r\n      {/* REMARKS选择对话框 */}\r\n      <Dialog \r\n        open={remarksDialog.open} \r\n        onClose={closeRemarksDialog}\r\n        fullWidth\r\n        maxWidth=\"xs\"\r\n      >\r\n        <DialogTitle>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n            <Typography variant=\"h6\">选择REMARKS</Typography>\r\n            <Button \r\n              startIcon={<AddIcon />}\r\n              onClick={openAddOptionDialog}\r\n              color=\"primary\"\r\n            >\r\n              添加选项\r\n            </Button>\r\n          </Box>\r\n        </DialogTitle>\r\n        <DialogContent dividers>\r\n          <List>\r\n            {remarksOptions.map((option) => (\r\n              <ListItem \r\n                key={option} \r\n                disablePadding\r\n                secondaryAction={\r\n                  <IconButton \r\n                    edge=\"end\" \r\n                    aria-label=\"delete\"\r\n                    onClick={() => deleteOption(option)}\r\n                  >\r\n                    <DeleteIcon />\r\n                  </IconButton>\r\n                }\r\n              >\r\n                <ListItemButton onClick={() => selectRemarkOption(option)}>\r\n                  <ListItemText primary={option} />\r\n                </ListItemButton>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeRemarksDialog}>取消</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* 添加新选项对话框 */}\r\n      <Dialog\r\n        open={addOptionDialog}\r\n        onClose={closeAddOptionDialog}\r\n        fullWidth\r\n        maxWidth=\"xs\"\r\n      >\r\n        <DialogTitle>添加新选项</DialogTitle>\r\n        <DialogContent>\r\n          <TextField\r\n            autoFocus\r\n            margin=\"dense\"\r\n            id=\"name\"\r\n            label=\"选项名称\"\r\n            type=\"text\"\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            value={newOption}\r\n            onChange={(e) => setNewOption(e.target.value)}\r\n          />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeAddOptionDialog}>取消</Button>\r\n          <Button onClick={addNewOption} color=\"primary\">添加</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      \r\n      {/* PDF预览对话框 */}\r\n      <Dialog\r\n        open={pdfDialog.open}\r\n        onClose={closePdfDialog}\r\n        fullWidth\r\n        maxWidth=\"md\"\r\n      >\r\n        <DialogTitle>PDF预览</DialogTitle>\r\n        <DialogContent>\r\n          {pdfDialog.error ? (\r\n            <Typography color=\"error\">{pdfDialog.error}</Typography>\r\n          ) : pdfDialog.url ? (\r\n            <Box sx={{ width: '100%', height: '70vh' }}>\r\n              <iframe \r\n                src={pdfDialog.url} \r\n                width=\"100%\" \r\n                height=\"100%\" \r\n                style={{ border: 'none' }}\r\n                title=\"PDF预览\"\r\n              />\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\r\n              <CircularProgress />\r\n            </Box>\r\n          )}\r\n        </DialogContent>\r\n        <DialogActions>\r\n          {pdfDialog.url && !pdfDialog.error && (\r\n            <Button \r\n              onClick={() => window.open(pdfDialog.url, '_blank')}\r\n              color=\"primary\"\r\n            >\r\n              下载PDF\r\n            </Button>\r\n          )}\r\n          <Button onClick={closePdfDialog}>关闭</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      \r\n      <Snackbar\r\n        open={snackbar.open}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSnackbar}\r\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n      >\r\n        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>\r\n          {snackbar.message}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,uBAAuB,GAAG,CAC9B,kBAAkB,EAClB,uBAAuB,EACvB,iCAAiC,EACjC,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,oBAAoB,CACrB;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC5D;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,MAAM;IACzD,MAAMgD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC3D,OAAOF,YAAY,GAAGG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,GAAGV,uBAAuB;EAC1E,CAAC,CAAC;;EAEF;EACA,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC;IACzC6D,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA9D,SAAS,CAAC,MAAM;IACdgD,YAAY,CAACe,OAAO,CAAC,gBAAgB,EAAEb,IAAI,CAACc,SAAS,CAACnB,cAAc,CAAC,CAAC;EACxE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoB,aAAa,GAAG1B,IAAI,CAAC2B,GAAG,CAACC,GAAG,IAAI;IACpC;IACA,OAAO;MAAE,GAAGA,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,iBAAiB,EAAE;IAAG,CAAC;EACvD,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAACkE,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEK,KAAK,MAAM;IAC1E,GAAGL,GAAG;IACNM,EAAE,EAAED,KAAK;IACTE,OAAO,EAAEP,GAAG,CAACQ,EAAE,KAAK;EACtB,CAAC,CAAC,CAAC,CAAC;EAEJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC;IAAE6D,IAAI,EAAE,KAAK;IAAEkB,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAC3F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC;IACjD6D,IAAI,EAAE,KAAK;IACXsB,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACAC,MAAM,CAACzB,IAAI,CAAC,GAAGxB,OAAO,aAAaI,MAAM,EAAE,EAAE,QAAQ,CAAC;IACxD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,OAAO,CAAC,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAM6C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMtD,KAAK,CAACuD,MAAM,CAAC,GAAGpD,OAAO,YAAYI,MAAM,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;IAEArB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMgD,cAAc,GAAIC,MAAM,IAAK;IACjC;IACA,IAAIA,MAAM,CAACvB,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMgB,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACA,MAAMC,WAAW,GAAGvB,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAKA,GAAG,CAACM,EAAE,KAAKmB,MAAM,CAACnB,EAAE,GAAGmB,MAAM,GAAGzB,GAAI,CAAC;;IAE9E;IACA,IAAIyB,MAAM,CAACE,UAAU,KAAKC,SAAS,EAAE;MACnC,MAAMC,QAAQ,GAAGH,WAAW,CAACI,IAAI,CAAC9B,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC;MAC5D,IAAIqB,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGL,WAAW,CACzBM,MAAM,CAAChC,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjCyB,MAAM,CAAC,CAACC,GAAG,EAAElC,GAAG,KAAKkC,GAAG,IAAIlC,GAAG,CAAC2B,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvDE,QAAQ,CAACF,UAAU,GAAGI,QAAQ;MAChC;IACF;IAEA3B,WAAW,CAACsB,WAAW,CAAC;IACxBhB,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;IAClE,OAAOa,MAAM;EACf,CAAC;EAED,MAAMU,uBAAuB,GAAIxC,KAAK,IAAK;IACzCe,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,SAAShB,KAAK,CAACgB,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACnF,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC1B,WAAW,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5C,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAM6C,iBAAiB,GAAGA,CAACvB,KAAK,EAAEC,YAAY,KAAK;IACjDF,gBAAgB,CAAC;MACfrB,IAAI,EAAE,IAAI;MACVsB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzB,gBAAgB,CAAC;MACf,GAAGD,aAAa;MAChBpB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+C,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAM;MAAE1B;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMW,WAAW,GAAGvB,QAAQ,CAACJ,GAAG,CAACC,GAAG,IAAI;QACtC,IAAIA,GAAG,CAACM,EAAE,KAAKS,KAAK,EAAE;UACpB,OAAO;YAAE,GAAGf,GAAG;YAAEC,OAAO,EAAEwC,MAAM;YAAEvC,iBAAiB,EAAEuC;UAAO,CAAC;QAC/D;QACA,OAAOzC,GAAG;MACZ,CAAC,CAAC;MACFI,WAAW,CAACsB,WAAW,CAAC;MACxBhB,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;IACzE;IACA2B,kBAAkB,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCtD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMuD,oBAAoB,GAAGA,CAAA,KAAM;IACjCvD,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;;EAED;EACA,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI3D,SAAS,CAAC4D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACnE,cAAc,CAACoE,QAAQ,CAAC7D,SAAS,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE;MACzE,MAAME,cAAc,GAAG,CAAC,GAAGrE,cAAc,EAAEO,SAAS,CAAC4D,IAAI,CAAC,CAAC,CAAC;MAC5DlE,iBAAiB,CAACoE,cAAc,CAAC;MACjCrC,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAU,CAAC,CAAC;MACnE+B,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIjE,cAAc,CAACoE,QAAQ,CAAC7D,SAAS,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE;MACpDnC,WAAW,CAAC;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMoC,YAAY,GAAIP,MAAM,IAAK;IAC/B,MAAMM,cAAc,GAAGrE,cAAc,CAACsD,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKR,MAAM,CAAC;IACrE9D,iBAAiB,CAACoE,cAAc,CAAC;IACjCrC,WAAW,CAAC;MAAEjB,IAAI,EAAE,IAAI;MAAEkB,OAAO,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMsC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF5D,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,MAAM6D,OAAO,GAAGhD,QAAQ,CACrB6B,MAAM,CAAChC,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,CACjCT,GAAG,CAACC,GAAG,KAAK;QACXQ,EAAE,EAAE,OAAOR,GAAG,CAACQ,EAAE,KAAK,QAAQ,GAAG4C,IAAI,CAACC,KAAK,CAACrD,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAACQ,EAAE;QAC5D8C,IAAI,EAAEtD,GAAG,CAACsD,IAAI,GAAI,OAAOtD,GAAG,CAACsD,IAAI,KAAK,QAAQ,IAAItD,GAAG,CAACsD,IAAI,CAACR,QAAQ,CAAC,GAAG,CAAC,GAAG9C,GAAG,CAACsD,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAACsD,IAAI,GAAI,EAAE;QAClH,YAAY,EAAEtD,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAOA,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAGoD,IAAI,CAACC,KAAK,CAACrD,GAAG,CAAC,OAAO,CAAC,CAAC,GAAGA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QACzFwD,EAAE,EAAE,OAAOxD,GAAG,CAACwD,EAAE,KAAK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAACrD,GAAG,CAACwD,EAAE,CAAC,GAAGxD,GAAG,CAACwD,EAAE,IAAI,EAAE;QAClEvD,OAAO,EAAED,GAAG,CAACE,iBAAiB,IAAI,EAAE;QACpCuD,KAAK,EAAE,OAAOzD,GAAG,CAAC0D,QAAQ,KAAK,QAAQ,GACpC1D,GAAG,CAAC0D,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG1D,GAAG,CAAC0D,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG3D,GAAG,CAAC0D,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAC3E3D,GAAG,CAAC0D,QAAQ,IAAI,EAAE;QACpBE,MAAM,EAAE,OAAO5D,GAAG,CAAC2B,UAAU,KAAK,QAAQ,GAAG3B,GAAG,CAAC2B,UAAU,CAACgC,OAAO,CAAC,CAAC,CAAC,GAAG3D,GAAG,CAAC2B,UAAU,IAAI;MAC7F,CAAC,CAAC,CAAC;;MAEL;MACA,MAAMkC,WAAW,GAAG1D,QAAQ,CACzB6B,MAAM,CAAChC,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAIR,GAAG,CAAC2B,UAAU,CAAC,CACnDM,MAAM,CAAC,CAACC,GAAG,EAAElC,GAAG,KAAKkC,GAAG,IAAI,OAAOlC,GAAG,CAAC2B,UAAU,KAAK,QAAQ,GAAG3B,GAAG,CAAC2B,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3F;MACA,MAAMmC,QAAQ,GAAG,MAAMhG,KAAK,CAACiG,IAAI,CAAC,GAAG9F,OAAO,eAAe,EAAE;QAC3DG,IAAI,EAAE+E,OAAO;QACbU,WAAW,EAAEA,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC;QACnCtF,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIyF,QAAQ,CAAC1F,IAAI,IAAI0F,QAAQ,CAAC1F,IAAI,CAAC4F,MAAM,EAAE;QACzCxE,YAAY,CAAC;UACXC,IAAI,EAAE,IAAI;UACVC,GAAG,EAAEoE,QAAQ,CAAC1F,IAAI,CAAC4F,MAAM;UACzBrE,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIsE,KAAK,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOtE,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCH,YAAY,CAAC;QACXC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE,IAAI;QACTC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,SAAS;MACRL,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4E,cAAc,GAAGA,CAAA,KAAM;IAC3B1E,YAAY,CAAC;MACX,GAAGD,SAAS;MACZE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAI,CAACU,QAAQ,IAAIA,QAAQ,CAACgE,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEnG,OAAA,CAAClC,GAAG;MAACsI,EAAE,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACtCvG,OAAA,CAACjC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7G,OAAA,CAAChC,MAAM;QACLwI,OAAO,EAAC,WAAW;QACnBM,OAAO,EAAExG,OAAQ;QACjB8F,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjD;IAAEF,KAAK,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAAG;EAC/D;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9D;;EAED;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAACjF,GAAG,CAACsF,GAAG,IAAI;IACrC,IAAI,CAAClF,QAAQ,CAAC,CAAC,CAAC,CAACmF,cAAc,CAACD,GAAG,CAACJ,KAAK,CAAC,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,IAAII,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;MACnG;MACA,OAAO,IAAI;IACb;;IAEA;IACA,IAAII,GAAG,CAACJ,KAAK,KAAK,SAAS,EAAE;MAC3B,OAAO;QACLA,KAAK,EAAEI,GAAG,CAACJ,KAAK;QAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;QAC1BK,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,GAAG;QACbL,QAAQ,EAAE,KAAK;QACfM,UAAU,EAAGlE,MAAM,IAAK;UACtB;UACA,IAAIA,MAAM,CAACvB,GAAG,CAACQ,EAAE,KAAK,OAAO,EAAE;YAC7B,OAAO,EAAE;UACX;UAEA,oBACExC,OAAA,CAAClC,GAAG;YACFsI,EAAE,EAAE;cACFsB,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTC,cAAc,EAAE,WAAW;gBAC3BlB,KAAK,EAAE;cACT;YACF,CAAE;YACFK,OAAO,EAAEA,CAAA,KAAMxC,iBAAiB,CAACf,MAAM,CAACvB,GAAG,CAACM,EAAE,EAAEiB,MAAM,CAACqE,KAAK,IAAI,EAAE,CAAE;YAAArB,QAAA,EAEnEhD,MAAM,CAACvB,GAAG,CAACE,iBAAiB,IAAI;UAAM;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAEV;MACF,CAAC;IACH;IAEA,OAAO;MACLI,KAAK,EAAEI,GAAG,CAACJ,KAAK;MAChBC,UAAU,EAAEG,GAAG,CAACH,UAAU;MAC1BK,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbL,QAAQ,EAAEE,GAAG,CAACF,QAAQ;MACtBM,UAAU,EAAGlE,MAAM,IAAK;QACtB;QACA,IAAIA,MAAM,CAACvB,GAAG,CAACQ,EAAE,KAAK,OAAO,IAAI6E,GAAG,CAACJ,KAAK,KAAK,YAAY,EAAE;UAC3D,oBACEjH,OAAA,CAACjC,UAAU;YAACyI,OAAO,EAAC,OAAO;YAACqB,UAAU,EAAC,MAAM;YAACpB,KAAK,EAAC,SAAS;YAAAF,QAAA,EAC1D,OAAOhD,MAAM,CAACqE,KAAK,KAAK,QAAQ,GAAGrE,MAAM,CAACqE,KAAK,CAACjC,OAAO,CAAC,CAAC,CAAC,GAAGpC,MAAM,CAACqE;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAEjB;;QAEA;QACA,IAAIQ,GAAG,CAACJ,KAAK,KAAK,MAAM,IAAI1D,MAAM,CAACqE,KAAK,EAAE;UACxC,OAAOrE,MAAM,CAACqE,KAAK,CAACrC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,IAAI8B,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO1D,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOxC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACqE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,OAAO,IAAI,OAAO1D,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAC7D,OAAOxC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACqE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,IAAI,IAAI,OAAO1D,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAC1D,OAAOxC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACqE,KAAK,CAAC;QACjC;;QAEA;QACA,IAAIP,GAAG,CAACJ,KAAK,KAAK,UAAU,IAAI,OAAO1D,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAChE,OAAOrE,MAAM,CAACqE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGrE,MAAM,CAACqE,KAAK,CAACjC,OAAO,CAAC,CAAC,CAAC,GAAGpC,MAAM,CAACqE,KAAK,CAACjC,OAAO,CAAC,CAAC,CAAC;QACnF;;QAEA;QACA,IAAI0B,GAAG,CAACJ,KAAK,KAAK,YAAY,IAAI,OAAO1D,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UAClE,OAAOrE,MAAM,CAACqE,KAAK,CAACjC,OAAO,CAAC,CAAC,CAAC;QAChC;;QAEA;QACA,IAAI,OAAOpC,MAAM,CAACqE,KAAK,KAAK,QAAQ,EAAE;UACpC,OAAOrE,MAAM,CAACqE,KAAK;QACrB;QAEA,OAAOrE,MAAM,CAACqE,KAAK;MACrB;IACF,CAAC;EACH,CAAC,CAAC,CAAC5D,MAAM,CAAC8D,OAAO,CAAC,CAAC,CAAC;;EAEpB,oBACE9H,OAAA,CAAClC,GAAG;IAAAyI,QAAA,gBACFvG,OAAA,CAAClC,GAAG;MAACsI,EAAE,EAAE;QAAE2B,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBACzFvG,OAAA,CAACjC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAAC2B,YAAY;QAAA5B,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7G,OAAA,CAAClC,GAAG;QAAAyI,QAAA,gBACFvG,OAAA,CAAChC,MAAM;UACLwI,OAAO,EAAC,WAAW;UACnB4B,SAAS,eAAEpI,OAAA,CAACP,YAAY;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAE7D,cAAe;UACxBmD,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7G,OAAA,CAAChC,MAAM;UACLwI,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,WAAW;UACjB2B,SAAS,eAAEpI,OAAA,CAACH,gBAAgB;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChCC,OAAO,EAAE5B,WAAY;UACrBoD,QAAQ,EAAEjH,eAAgB;UAC1B+E,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EAEblF,eAAe,GAAG,QAAQ,GAAG;QAAO;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAET7G,OAAA,CAAChC,MAAM;UACLwI,OAAO,EAAC,UAAU;UAClB4B,SAAS,eAAEpI,OAAA,CAACN,cAAc;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BC,OAAO,EAAE1D,aAAc;UAAAmD,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7G,OAAA,CAAC/B,KAAK;MAACmI,EAAE,EAAE;QAAEmC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAjC,QAAA,eAC/CvG,OAAA,CAAClC,GAAG;QAACsI,EAAE,EAAE;UAAEqC,MAAM,EAAE,GAAG;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAhC,QAAA,eACtCvG,OAAA,CAACR,QAAQ;UACPkJ,IAAI,EAAEvG,QAAS;UACfiF,OAAO,EAAEA,OAAQ;UACjBuB,QAAQ,EAAE,EAAG;UACbC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,uBAAuB;UACvBC,eAAe,EAAGvF,MAAM,IAAKA,MAAM,CAACvB,GAAG,CAACO,OAAO,GAAG,WAAW,GAAG,EAAG;UACnEwG,cAAc,EAAEzF,cAAe;UAC/BE,gBAAgB,EAAEA,gBAAiB;UACnCW,uBAAuB,EAAEA,uBAAwB;UACjD6E,oBAAoB,EAAE;YAAEC,aAAa,EAAE;UAAK,CAAE;UAC9C7C,EAAE,EAAE;YACF,cAAc,EAAE;cACd8C,eAAe,EAAE,0BAA0B;cAC3CrB,UAAU,EAAE;YACd;UACF;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEP1E,QAAQ,CAACgH,IAAI,CAACnH,GAAG,IAAI,YAAY,IAAIA,GAAG,CAAC,iBACxChC,OAAA,CAAClC,GAAG;MAACsI,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACjBvG,OAAA,CAACjC,UAAU;QAACyI,OAAO,EAAC,WAAW;QAAC2B,YAAY;QAAA5B,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7G,OAAA,CAAC3B,cAAc;QAAC+K,SAAS,EAAEnL,KAAM;QAAAsI,QAAA,eAC/BvG,OAAA,CAAC9B,KAAK;UAAAqI,QAAA,gBACJvG,OAAA,CAAC1B,SAAS;YAAAiI,QAAA,eACRvG,OAAA,CAACzB,QAAQ;cAAAgI,QAAA,gBACPvG,OAAA,CAAC5B,SAAS;gBAAAmI,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzB7G,OAAA,CAAC5B,SAAS;gBAACiL,KAAK,EAAC,OAAO;gBAAA9C,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ7G,OAAA,CAAC7B,SAAS;YAAAoI,QAAA,eACRvG,OAAA,CAACzB,QAAQ;cAAAgI,QAAA,gBACPvG,OAAA,CAAC5B,SAAS;gBAAAmI,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1B7G,OAAA,CAAC5B,SAAS;gBAACiL,KAAK,EAAC,OAAO;gBAAA9C,QAAA,eACtBvG,OAAA,CAACxB,IAAI;kBACH8K,KAAK,EAAE,EAAA7I,cAAA,GAAA0B,QAAQ,CAAC2B,IAAI,CAAC9B,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAK,OAAO,CAAC,cAAA/B,cAAA,uBAAxCA,cAAA,CAA0CkD,UAAU,CAACgC,OAAO,CAAC,CAAC,CAAC,KAAI,MAAO;kBACjFc,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGD7G,OAAA,CAACnB,MAAM;MACL4C,IAAI,EAAEoB,aAAa,CAACpB,IAAK;MACzB8H,OAAO,EAAEhF,kBAAmB;MAC5BiF,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAlD,QAAA,gBAEbvG,OAAA,CAAClB,WAAW;QAAAyH,QAAA,eACVvG,OAAA,CAAClC,GAAG;UAACsI,EAAE,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA1B,QAAA,gBAClFvG,OAAA,CAACjC,UAAU;YAACyI,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C7G,OAAA,CAAChC,MAAM;YACLoK,SAAS,eAAEpI,OAAA,CAACL,OAAO;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBC,OAAO,EAAEpC,mBAAoB;YAC7B+B,KAAK,EAAC,SAAS;YAAAF,QAAA,EAChB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd7G,OAAA,CAACjB,aAAa;QAAC2K,QAAQ;QAAAnD,QAAA,eACrBvG,OAAA,CAACf,IAAI;UAAAsH,QAAA,EACF7F,cAAc,CAACqB,GAAG,CAAE0C,MAAM,iBACzBzE,OAAA,CAACd,QAAQ;YAEPyK,cAAc;YACdC,eAAe,eACb5J,OAAA,CAACV,UAAU;cACTuK,IAAI,EAAC,KAAK;cACV,cAAW,QAAQ;cACnB/C,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAACP,MAAM,CAAE;cAAA8B,QAAA,eAEpCvG,OAAA,CAACJ,UAAU;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAN,QAAA,eAEDvG,OAAA,CAACb,cAAc;cAAC2H,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACC,MAAM,CAAE;cAAA8B,QAAA,eACxDvG,OAAA,CAACZ,YAAY;gBAAC0K,OAAO,EAAErF;cAAO;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAdZpC,MAAM;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB7G,OAAA,CAAChB,aAAa;QAAAuH,QAAA,eACZvG,OAAA,CAAChC,MAAM;UAAC8I,OAAO,EAAEvC,kBAAmB;UAAAgC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7G,OAAA,CAACnB,MAAM;MACL4C,IAAI,EAAEN,eAAgB;MACtBoI,OAAO,EAAE5E,oBAAqB;MAC9B6E,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAlD,QAAA,gBAEbvG,OAAA,CAAClB,WAAW;QAAAyH,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC7G,OAAA,CAACjB,aAAa;QAAAwH,QAAA,eACZvG,OAAA,CAACX,SAAS;UACR0K,SAAS;UACTC,MAAM,EAAC,OAAO;UACd1H,EAAE,EAAC,MAAM;UACTgH,KAAK,EAAC,0BAAM;UACZW,IAAI,EAAC,MAAM;UACXT,SAAS;UACThD,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAE3G,SAAU;UACjBiJ,QAAQ,EAAGC,CAAC,IAAKjJ,YAAY,CAACiJ,CAAC,CAACC,MAAM,CAACxC,KAAK;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB7G,OAAA,CAAChB,aAAa;QAAAuH,QAAA,gBACZvG,OAAA,CAAChC,MAAM;UAAC8I,OAAO,EAAEnC,oBAAqB;UAAA4B,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD7G,OAAA,CAAChC,MAAM;UAAC8I,OAAO,EAAElC,YAAa;UAAC6B,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7G,OAAA,CAACnB,MAAM;MACL4C,IAAI,EAAEF,SAAS,CAACE,IAAK;MACrB8H,OAAO,EAAErD,cAAe;MACxBsD,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAAlD,QAAA,gBAEbvG,OAAA,CAAClB,WAAW;QAAAyH,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC7G,OAAA,CAACjB,aAAa;QAAAwH,QAAA,EACXhF,SAAS,CAACI,KAAK,gBACd3B,OAAA,CAACjC,UAAU;UAAC0I,KAAK,EAAC,OAAO;UAAAF,QAAA,EAAEhF,SAAS,CAACI;QAAK;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,GACtDtF,SAAS,CAACG,GAAG,gBACf1B,OAAA,CAAClC,GAAG;UAACsI,EAAE,EAAE;YAAEmC,KAAK,EAAE,MAAM;YAAEE,MAAM,EAAE;UAAO,CAAE;UAAAlC,QAAA,eACzCvG,OAAA;YACEqK,GAAG,EAAE9I,SAAS,CAACG,GAAI;YACnB6G,KAAK,EAAC,MAAM;YACZE,MAAM,EAAC,MAAM;YACb6B,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAE;YAC1BC,KAAK,EAAC;UAAO;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN7G,OAAA,CAAClC,GAAG;UAACsI,EAAE,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEyC,CAAC,EAAE;UAAE,CAAE;UAAAlE,QAAA,eAC3DvG,OAAA,CAACT,gBAAgB;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB7G,OAAA,CAAChB,aAAa;QAAAuH,QAAA,GACXhF,SAAS,CAACG,GAAG,IAAI,CAACH,SAAS,CAACI,KAAK,iBAChC3B,OAAA,CAAChC,MAAM;UACL8I,OAAO,EAAEA,CAAA,KAAM5D,MAAM,CAACzB,IAAI,CAACF,SAAS,CAACG,GAAG,EAAE,QAAQ,CAAE;UACpD+E,KAAK,EAAC,SAAS;UAAAF,QAAA,EAChB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACD7G,OAAA,CAAChC,MAAM;UAAC8I,OAAO,EAAEZ,cAAe;UAAAK,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET7G,OAAA,CAACvB,QAAQ;MACPgD,IAAI,EAAEgB,QAAQ,CAAChB,IAAK;MACpBiJ,gBAAgB,EAAE,IAAK;MACvBnB,OAAO,EAAEnF,mBAAoB;MAC7BuG,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAtE,QAAA,eAE1DvG,OAAA,CAACtB,KAAK;QAAC6K,OAAO,EAAEnF,mBAAoB;QAACxB,QAAQ,EAAEH,QAAQ,CAACG,QAAS;QAAA2D,QAAA,EAC9D9D,QAAQ,CAACE;MAAO;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACrG,EAAA,CAvjBIL,aAAa;AAAA2K,EAAA,GAAb3K,aAAa;AAyjBnB,eAAeA,aAAa;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}